<?php
/**
 * Arra.php
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * @package    LibreNMS
 * @link       http://librenms.org
 * @copyright  2017 <PERSON>
 * <AUTHOR> <<EMAIL>>
 */

namespace LibreNMS\OS;

use LibreNMS\Device\WirelessSensor;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessClientsDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessFrequencyDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessNoiseFloorDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessRateDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessSnrDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessChannelDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessPowerDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessWidthDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessNodeinfoDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessClientsRateDiscovery;
use LibreNMS\OS;
use App\Models\ArraMeshDynamicsNodes;
use App\Models\ArraMeshDynamicsMeshs;
use App\Models\Device;
use Illuminate\Support\Facades\DB;
use App\Models\CPM;
use App\Models\GroupSettings;
use App\Models\RMQ;
use Log;

class Arra extends OS implements
    WirelessClientsDiscovery,
    WirelessFrequencyDiscovery,
    WirelessChannelDiscovery,
    WirelessPowerDiscovery,
    WirelessWidthDiscovery,
    WirelessNodeinfoDiscovery,
    WirelessClientsRateDiscovery,
    WirelessNoiseFloorDiscovery,
    WirelessRateDiscovery,
    WirelessSnrDiscovery
{
    /**
     * Retrieve (and explode to array) list of network interfaces, and desired display name in LibreNMS.
     * This information is returned from the wireless device (router / AP) - as SNMP extend, with the name "interfaces".
     *
     * @return array Interfaces
     */
    private function getInterfaces()
    {
        // Need to use PHP_EOL, found newline (\n) not near as reliable / consistent! And this is as PHP says it should be done.
        $interfaces = explode(PHP_EOL, snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutputFull."interfaces"', '-Osqnv'));
        $arrIfaces = array();
        foreach ($interfaces as $interface) {
                list($k, $v) = explode(',', $interface);
                $arrIfaces[$k] = $v;
        }
        return $arrIfaces;
    }

    private function checkArraCPHash()
    {
    	
    	$deviceId = $this->getDeviceId();
    	$device = Device::with('groups')->find($deviceId);
    	if (!empty($device)) {
    		$group = $device->groups->first();
    		if (!empty($group)) {
    			$deviceHash = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-captive-portal-hash"', '-Osqnv');
    			if (!empty($deviceHash)) {
    				$file_download = CPM::getGroupCPDetailsByGroupId($group->id);
    				if ($file_download) {
    				    $file_path =  $file_download;
    				    $existingHash = hash_file('sha256', $file_path);
    				    // echo "Current Hash File is: \n".$existingHash;
    				    // echo "\narra-aptive-portal-hash for deviceID {$deviceId}-{$group->id} is: ",$deviceHash;
    				    // die();
    				    if ($existingHash != $deviceHash) {
    				    	RMQ::setDeviceMessageRmq($deviceId,'update_captive_portal');
    				    }
    				}        
    			}
    		}
    	}
    }

    private function checkArraSettingsHash()
    {
    	$deviceId = $this->getDeviceId();
    	$device = Device::with('groups')->find($deviceId);
    	if (!empty($device)) {
    		$group = $device->groups->first();
    		if (!empty($group)) {
    			$deviceHash = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-settings-hash"', '-Osqnv');
    			if (!empty($deviceHash)) {
    				$rev_file = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-settings-rev"', '-Osqnv');
                    if (empty($rev_file)) {
                        $rev_file = 1;
                    }
    				$file_download = GroupSettings::getGroupGSDetailsByGroupId($group->id, $rev_file);
    				if ($file_download) {
    				    $file_path =  $file_download;
    				    $existingHash = hash_file('sha256', $file_path);
    				    // echo "Current Hash File is: \n".$existingHash;
    				    // echo "\narra-settings-hash for deviceID {$deviceId}-{$group->id} is: ",$deviceHash;
    				    // die();
    				    if ($existingHash != $deviceHash) {
    				    	RMQ::setDeviceMessageRmq($deviceId,'update_settings');
    				    }
    				}        
    			}
    		}
    	}
    }

    /**
     * Generic (common / shared) routine, to create new Wireless Sensors, of the sensor Type passed as the call argument.
     * type - string, matching to LibreNMS documentation => https://docs.librenms.org/Developing/os/Wireless-Sensors/
     * query - string, query to be used at client (appends to type string, e.g. -tx, -rx)
     * system - boolean, flag to indicate that a combined ("system level") sensor (and OID) is to be added
     * stats - boolean, flag denoting that statistics are to be retrieved (min, max, avg)
     * NOTE: system and stats are assumed to be mutually exclusive (at least for now!)
     *
     * @return array Sensors
     */
    private function getSensorData($type, $query = '', $system = False, $stats = False)
    {
	// Initialize needed variables, and get interfaces (actual network name, and LibreNMS name)
        $sensors = array();
        $this->checkArraCPHash();
        $this->checkArraSettingsHash();
        $interfaces = $this->getInterfaces();
        
		$count = 1;

		// Build array for stats - if desired
		$statstr = [''];
		if ($stats) {
			$statstr = ['-min', '-max', '-avg'];
		}

		// Loop over interfaces, adding sensors
		foreach ($interfaces as $index => $interface) {
			// Loop over stats, appending to sensors as needed (only a single, blank, addition if no stats)
			foreach ($statstr as $stat) {
		                $oid = "NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"$type$query-$index$stat\"";
	        	        $sensors[] = new WirelessSensor($type, $this->getDeviceId(), snmp_translate($oid), "arra$query", $count, "$interface$query$stat");
	                	$count += 1;
			}
        }
	// If system level (i.e. overall) sensor desired, add that one as well
        if ($system and (count($interfaces) > 1)) {
                $oid = "NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"$type$query-wlan\"";
                $sensors[] = new WirelessSensor($type, $this->getDeviceId(), snmp_translate($oid), "arra$query", $count, 'wlan');
        }

	// And, return all the sensors that have been created above (i.e. the array of sensors)
        return $sensors;
    }

    /**
     * Discover wireless client counts. Type is clients.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessClients()
    {
        //return $this->getSensorData('clients', '', True, False);
		
		$interfaces = $this->getInterfaces();
		
		$sensors = array();
		
		foreach ($interfaces as $interface => $interfaceName) {
		
			$wlClients = explode(PHP_EOL, snmp_get($this->getDevice(), "NET-SNMP-EXTEND-MIB::nsExtendOutputFull.\"clients-list-$interface\"", '-Osqnv'));
			$arrWlClients = array();
			$count = 0;
			foreach ($wlClients as $wlClient) {
				if (strlen($wlClient)>0) {
					$count++;
					$arrWlClients[$count] = explode("\t", $wlClient);
				}	
			}
			
			

			if (!empty($arrWlClients)) {
				
				foreach ($arrWlClients as $index => $wlClient) {
					$oid = snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-signal-$interface\".". $index);
					
					$total_oids[] = $oid;

					$sensors[] = new WirelessSensor(
						'clients',
						$this->getDeviceId(),
						$oid,
						'arra-wlclient-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.' ['.$wlClient[0].']: ' . $wlClient[2]
					);
				}
			}
			
			$sensors[] = new WirelessSensor(
				'clients',
				$this->getDeviceId(),
				snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"clients-$interface\""),
				'arra-'.$interface,
				0,
				"Clients: $interfaceName",
				$count
			);
			
        }

        return $sensors;
    }

    /**
     * Discover wireless frequency.  This is in MHz. Type is frequency.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessFrequency()
    {
        return $this->getSensorData('frequency', '', False, False);
    }

    /**
     * Discover wireless noise floor.  This is in dBm. Type is noise-floor.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessNoiseFloor()
    {
        return $this->getSensorData('noise-floor', '', False, False);
    }

    /**
     * Discover wireless rate. This is in bps. Type is rate.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array
     */
    public function discoverWirelessRate()
    {
	$txrate = $this->getSensorData('rate', '-tx', False, True);
	$rxrate = $this->getSensorData('rate', '-rx', False, True);
        return array_merge($txrate, $rxrate);
    }

    /**
     * Discover wireless snr. This is in dB. Type is snr.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array
     */
    public function discoverWirelessSNR()
    {
	return $this->getSensorData('snr', '', False, True);
    }
	
	/**
     * Discover wireless channel.  This is as number. Type is channel.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessChannel()
    {
        return $this->getSensorData('channel', '', False, False);
    }
	
	/**
     * Discover wireless power.  This is in dBm. Type is power.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessPower()
    {
        return $this->getSensorData('power', '', False, False);
    }
	
	/**
     * Discover wireless width.  This is in MHz. Type is width.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessWidth()
    {
        return $this->getSensorData('width', '', False, False);
    }
	
	/**
     * Discover wireless Nodeinfo.  This is boolean. Type is Nodeinfo.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessNodeinfo()
    {
		$sensors = array();


		$sensors[] = new WirelessSensor(
			'nodeinfo',
			$this->getDeviceId(),
			snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"nodeinfo-gateway\""),
			'nodeinfo-gateway',
			0,
			"Is Gateway (1 if is; 0 if not;)"
		);
		
		/**/
		$nextNodeSNMPData = explode(PHP_EOL, snmp_get($this->getDevice(), "NET-SNMP-EXTEND-MIB::nsExtendOutputFull.\"nodeinfo-next\"", '-Osqnv'));
		
		list($nextNodeName, $nextNodeInterface) = explode("\t", $nextNodeSNMPData[0]);
						

		if (!empty($nextNodeName) && !empty($nextNodeInterface)) {
			
						
			$sensors[] = new WirelessSensor(
				'nodeinfo',
				$this->getDeviceId(),
				snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"nodeinfo-next\""),
				'nextnodeinfo',
				'nextnode-'.$nextNodeInterface.'-'.$nextNodeName,
				"Next node: $nextNodeName ($nextNodeInterface)"
			);
		}


        return $sensors;
    }
	
	/**
     * Discover wireless client rate. Type is clients rate.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessClientsRate()
    {
        		
		$interfaces = $this->getInterfaces();
		
		$sensors = array();
		
		foreach ($interfaces as $interface => $interfaceName) {
		
			$wlClients = explode(PHP_EOL, snmp_get($this->getDevice(), "NET-SNMP-EXTEND-MIB::nsExtendOutputFull.\"clients-list-$interface\"", '-Osqnv'));
			$arrWlClients = array();
			$count = 0;
			foreach ($wlClients as $wlClient) {
				if (strlen($wlClient)>0) {
					$count++;
					$arrWlClients[$count] = explode("\t", $wlClient);
				}	
			}
			
			

			if (!empty($arrWlClients)) {
				
				foreach ($arrWlClients as $index => $wlClient) {
					
					$sensors[] = new WirelessSensor(
						'clients-rate',
						$this->getDeviceId(),
						snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-rate-rx-$interface\".". $index),
						'arra-wlclientrate-rx-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.'-rx ['.$wlClient[0].']: ' . $wlClient[2]
					);
					
					$sensors[] = new WirelessSensor(
						'clients-rate',
						$this->getDeviceId(),
						snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-rate-tx-$interface\".". $index),
						'arra-wlclientrate-tx-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.'-tx ['.$wlClient[0].']: ' . $wlClient[2]
					);
				}
			}
			
			
        }

        return $sensors;
    }

}
