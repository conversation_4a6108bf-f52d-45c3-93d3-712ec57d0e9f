<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInitDevices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('init_devices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('routerSerialNumber', 250)->index('routerSerialNumber');
            $table->text('VPNconfig');
            $table->integer('proccess')->index('proccess');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('init_devices');
    }
}
