<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInitDevices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('init_devices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('mongo_id', 250)->unique('mongo_id');
            $table->string('routerSerialNumber', 250);
            $table->string('VPN_IPv6Address', 250);
            $table->text('VPNconfig');
            $table->integer('proccess')->default(0)->index('proccess');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('init_devices');
    }
}
