@extends('layouts.librenmsv1')

@section('title', __('Arra Mesh Deploy Tool'))

@section('content')
   <div class="col-md-4">
   </div>
   <div class="col-md-4">
      @error('file')
         <p class="alert alert-danger">{{ $message }}</p>
      @enderror

      @if(\Session::has('import_success'))
         <p id="success-message" class="alert alert-success">{{ \Session::get('import_success') }}</p>
      @endif
      @if(\Session::has('import_error'))
         <p class="alert alert-danger">{{ \Session::get('import_error') }}</p>
      @endif
   </div>
   <div class="loading-screen-pin-fixed" id="loadingScreenPinFixed">
      <div class="pin"></div>
      <div class="pulse"></div>
   </div>
   <div class="modal fade bs-example-modal-lg" id="project-import" tabindex="-1" role="dialog" aria-labelledby="Create" aria-hidden="true">
      <form action="{{ route('arra-project.import', ['project' => $project->id]) }}" method="POST" enctype="multipart/form-data" id="importProjectForm">
         @csrf
         <div class="modal-dialog modal-lg">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h4 class="modal-title" id="Create">Import Project</h4>
               </div>
               <div class="modal-body">
                  <div class="row">
                     <div class="col-md-12">
                        <div class="form-group">
                           <label for="name">Choose a file to import: </label>
                           <input type="file" name="file" class="form-control input-sm">
                           <span class="hidden empty-project-file" style="color: red; font-weight: 600;">You must upload a file!</span>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" name="create-template" id="import-project">Import</button>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </form>
   </div>
   <div class="modal fade in" id="project-import-poi" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true" style="display: none; padding-right: 17px;">
      <div class="modal-dialog modal-md">
         <div class="modal-content">
            <form action="{{ route('arra-project.import-poi', ['project' => $project->id]) }}" method="POST" enctype="multipart/form-data" id="importProjectPOIForm">
               @csrf
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title" id="Delete">Import Points of Interest</h5>
               </div>
               <div class="poiUpload-content">
                  <div class="poiUpload-step1">
                     <div class="modal-body">
                        <h4>Upload CSV</h4>
                        <p> Prepare a CSV file as shown or use this <a href="{{ asset('example100.csv') }}" download>example.csv</a> file to work with</p>
                        <div class="csv-example-img">
                           <img src="{{asset('images/arra-deploy-tool-images/csv-example.png')}}" alt="">
                        </div>
                        <div class="poi-import-desc">
                           <p>Notes:</p>
                           <p>
                              You can search for your icon codes at <a href="https://fontawesome.com/v4/icons/" target="_blank">fontawesome.com/icons</a> .
                              <br />
                              Empty or non conforming entries will be ignored on import
                           </p>
                        </div>
                        <label for="poi-upload">Please upload your .csv file here: </label>
                        <input type="file" name="file_poi" id="file_poi" class="form-control" autocomplete="off">
                        <span class="hidden empty-project-file" style="color: red; font-weight: 600;">You must upload a file!</span>
                        <div style="margin-top: 20px ">
                           <button class="btn btn-primary btn-lg btn-block" type="button" id="input-poi-upload">Upload File</button>
                        </div>
                     </div>
                  </div>
                  <div class="poiUpload-step2 hidden">
                     <div class="modal-body">
                        <h4>Checkout <span class="badge badge-secondary badge-pill">141</span></h4>
                        <div>
                           <ul class="list-group mb-3 sticky-top text-muted">
                              <li class="list-group-item">
                                 <h4> <strong>141 POI</strong> locations have been detected </h4>
                              </li>
                              <li class="list-group-item text-primary">
                                 <h4><span>Total (USD)</span> <strong>$120</strong></h4>
                              </li>
                           </ul>
                        </div>
                        <div style="margin-top: 20px"><button class="btn btn-primary btn-lg btn-block" id="poi-checkout" type="button">Confirm Processing</button></div>
                     </div>
                  </div>

                  <div class="poiUpload-step3 hidden">
                     <div class="modal-body text-center">
                        <div style="padding:0px 0";><img src="loading-poi.gif" alt="" style="width: 60px;"/></div>
                        <h4 class="text-center">The import process has started! <br> You will receive an email when it has been completed.</h4>
                     </div>

                     <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                     </div>
                  </div>

                  <div class="poiUpload-step4 hidden">
                     <div class="modal-body text-center">
                        <div style="padding:0px 0";><img src="loading-poi.gif" alt="" style="width: 60px;"/></div>
                        <h4 class="text-center">The import process is completed! <br> You can toggle the POI button to see the imported points of interests</h4>
                     </div>

                     <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                     </div>
                  </div>

               <script>

                  // $("#input-poi-upload").click(function() {
                  //    $(".poiUpload-step1").hide();
                  //    $(".poiUpload-step2").fadeIn();
                  // });
                  //
                  // $("#poi-checkout").click(function() {
                  //    $(".poiUpload-step2").hide();
                  //    $(".poiUpload-step3").fadeIn();
                  // })

               </script>
            </div>
            </form>
         </div>
      </div>
   </div>


   <div class="container-fluid">
      <div class="modal fade in" id="change-project-name-modal" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true">
         <div class="modal-dialog modal-md">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title" id="Delete">Change project name</h5>
               </div>
               <div class="modal-body">
                  <div class="autocomplete" style="width:550px;">
                     <input type="text" id="project_name_bk" value="{{$project->name}}" name="project-name" class="form-control">
                  </div>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal" id="skip-change-project-name">Skip</button>
                  <button type="submit" class="btn btn-primary" id="change-project-name" data-target="alert-template-removal">Change</button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="owner-group" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true">
         <div class="modal-dialog modal-md">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title" id="Delete">Another group detected!</h5>
               </div>
               <div class="modal-body">
                  <div class="autocomplete" style="width:550px;">
                     <label for="location">This project has another group assigned than yours! You can click either Skip button - and this will not change the existing group or click Change button -  and this will
                        change the project group!
                     </label>
                  </div>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal" id="skip-change-group">Skip</button>
                  <button type="submit" class="btn btn-primary" id="change-group" data-target="alert-template-removal">Change group</button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="confirm-location" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true">
         <div class="modal-dialog modal-md">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title" id="Delete">Choose Location</h5>
               </div>
               <div class="modal-body">
                  <div class="autocomplete" style="width:550px;">
                     <label for="location">Type the desired location. You will be pointed to the choosen location after you press "Go to location" button</label>
                     <input type="location" id="location" class="form-control" autocomplete="off">
                  </div>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal" id="skip-location">Skip</button>
                  <button type="submit" class="btn btn-primary" id="go-to-location" data-target="alert-template-removal">Go to location</button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="deleteNodeModal" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true"></div>
      <div class="config-node-modal modal fade in" id="diffNodeOptionModal" tabindex="-1" role="dialog" aria-labelledby="Delete" aria-hidden="true">
         <div class="modal-dialog modal-lg">
            <div class="modal-content">
               <div class="modal-header">
                  <h5 class="modal-title text-center"> Choose node connection type </h5>
               </div>
               <div class="modal-body config-node-modal-body">
                  <div class="node-config-type-box">
                     <img src="{{asset('images/arra-deploy-tool-images/connect-all-small.png')}}" alt="">
                     <button type="button" name="button" class="btn btn-primary" id="nfConnectAllNodes"> Connect to all nodes </button>
                  </div>
                  <div class="node-config-type-box">
                     <img src="{{asset('images/arra-deploy-tool-images/connect-single-small.png')}}" alt="">
                     <button type="button" name="button" class="btn btn-primary" id="nfConnectToSelected"> Connect to selected nodes </button>
                  </div>
                  <div class="node-config-type-box">
                     <img src="{{asset('images/arra-deploy-tool-images/standalone-small.png')}}" alt="">
                     <button type="button" name="button" class="btn btn-primary" id="nfWithoutConnection"> Without connection </button>
                  </div>
               </div>
               <div class="modal-footer">
                  <div class="checkbox-inp-wrapper">
                     <input type="checkbox" id="nctChoice" name="nctChoice_name">
                     <label for="nctChoice"> Remember my choice </label>
                  </div>
               </div>
         </div>
      </div>
   </div>
   <div class="modal fade in" id="preferencesModal" tabindex="-1" role="dialog" aria-labelledby="Preferences" aria-hidden="true">
      <div class="modal-dialog modal-md">
         <div class="modal-content">
            <div class="modal-header">
               <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
               <h5 class="modal-title" id="Delete">Preferences</h5>
            </div>
            <div class="modal-body">
               <div class="preference-box" id="savedNodeConnTypeWrapper">
                  <h5> Node connection type: <span id="connectionTypeText">  </span> </h5>
                  <div class="prf-node-connection-types" id="prfNct">
                     <div class="prf-node-connection-box">
                        <input type="radio" id="nctChoice0" name="node_connection_type_choice">
                        <label for="nctChoice0"> No type selected </label>
                     </div>
                     <div class="prf-node-connection-box">
                        <input type="radio" id="nctChoice1" name="node_connection_type_choice">
                        <label for="nctChoice1"> Connect to all nodes </label>
                     </div>
                     <div class="prf-node-connection-box">
                        <input type="radio" id="nctChoice2" name="node_connection_type_choice">
                        <label for="nctChoice2"> Connect to selected node </label>
                     </div>
                     <div class="prf-node-connection-box">
                        <input type="radio" id="nctChoice3" name="node_connection_type_choice">
                        <label for="nctChoice3"> Without connection </label>
                     </div>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-default" data-dismiss="modal" id="skip-location">Close</button>
            </div>
         </div>
      </div>
   </div>
      <div class="modal fade in" id="nodeConnectionsModal" tabindex="-1" role="dialog" aria-labelledby="nodeConnectionsModalLabel" aria-hidden="true">
         <div class="modal-dialog modal-sm">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title">Available connection nodes</h5>
               </div>
               <div class="modal-body">
                  <p id="nodesCount">  </p>
                  <input class="form-control" type="text" id="nodeFilterInput" onkeyup="filterNodes()" placeholder="Search for nodes...e.g.: node1" title="Type in a name" autocomplete="off">
                  <ul id="connectionNodesList" class="connection-nodes-list"></ul>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal"> Done </button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="deletePOIModal" tabindex="-1" role="dialog" aria-labelledby="deletePOIModalLabel" aria-hidden="true">
         <div class="modal-dialog modal-sm">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title">Confirm Delete</h5>
               </div>
               <div class="modal-body">
                  <p>If you are sure you want to remove the POI, click Delete.</p>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                  <button type="button" class="btn btn-danger danger" data-dismiss="modal" onclick="removePOI()">Delete POI</button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="setHeatareaValuesModal" tabindex="-1" role="dialog" aria-labelledby="setHeatareaValuesModalLabel" aria-hidden="true">
         <div class="modal-dialog modal-md" id="newValNodesModalContent">
            <div class="modal-content">
               <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  <h5 class="modal-title">Confirm Node Values</h5>
               </div>
               <div class="modal-body">
                  {{-- <p class="text-center"> Choose the values that the node should receive </p> --}}

                  <h5 class="cst-modal-desc">We detected different heatmap parameter changes. Please confirm if you want to keep the default values or the new ones set in heatmap.</h5>
                  <table class="table">
                     <thead>
                        <tr>
                           <th>Value names</th>
                           <th>Default Values</th>
                           <th>New Values</th>
                        </tr>
                     </thead>
                     <tbody>
                        <tr id="heightRow" class="hidden">
                           <th scope="row">Height (meters)</th>
                           <td><span id="defaultNodeHeight"> </span> </td>
                           <td><span id="parentNodePairHeight"> </span> </td>
                        </tr>
                        <tr id="gainRow" class="hidden">
                           <th scope="row">Gain (dBi)</th>
                           <td><span id="defaultNodeGain"> </span> </p></td>
                           <td><span id="parentNodePairGain"> </span> </p></td>
                        </tr>
                        <tr id="cableLossRow" class="hidden">
                           <th scope="row">Cable loss (dB)</th>
                           <td><span id="defaultNodeCableLoss"> </span> </p></td>
                           <td><span id="parentNodePairCableLoss"> </span> </p></td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal"> Keep default values </button>
                  <button type="button" class="btn btn-primary" data-dismiss="modal" id="setNewValBtn"> Accept new values </button>
               </div>
            </div>
         </div>
      </div>
      <div class="modal fade in" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true"></div>
      <div class="modal fade in" id="poiCoordsChangeConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="poiCoordsChangeConfirmationModalLabel" aria-hidden="true"></div>
      <div class="row justify-content-between">
         <div class="col-md-12">
            @if(\Session::has('export_error'))
               <p class="alert alert-danger text-center">{{ \Session::get('export_error') }}</p>
            @endif
         </div>
      </div>
      <div id="manage-device-groups-panel" class="mesh-tool-panel">
         <div class="panel-body">
            <div class="project-title">
               <p id="projectName"> {{$project->name}} </p>
               <span>
                  <i class="fa fa-edit" id="trigger-change-project-name-modal"></i>
               </span>
            </div>
            <div id="meshMap">
               <div id="poiPopup" class="hidden poi-popup">   </div>
               <div id="polyLineContextMenu" class="hidden">   </div>
               <div id="markerContextMenu" class="hidden">   </div>
               <div class="leaflet-control-container">
                  <div class="leaflet-bottom leaflet-left">
                     <div class="leaflet-control-attribution leaflet-control arrameshmap"
                     style="background: none; padding-bottom: 5px;">
                  </div>
               </div>
            </div>
            <div class="mesh-planner-utils-top-right">
               <div class="save-button-wrapper mesh-utils-btn">
                  <p id="save-button"><i class="fa fa-save"></i> Save project </p>
               </div>
               <div class="mesh-settings mesh-utils-btn" >
                  <p class="dropdown-btn" id="meshSettingsBtn">
                     <i class="fa fa-cogs" aria-hidden="true" ></i>
                     Settings
                  </p>
                  <div class="settings-dropdown-container" id="meshSettingsDropdown">
                     <div class="dropdown-list">
                        <a href="{{ route('arra-project.export', ['project' => $project->id]) }}">
                           <i class="fa fa-download"></i>
                           <span> Export data </span>
                        </a>
                        <a href="#" data-toggle="modal" data-target="#project-import" data-template_id="">
                           <i class="fa fa-share" aria-hidden="true"></i>
                           <span> Import KML </span>
                        </a>
                        <a href="#" id="import_poi_modal">
                           <i class="fa fa-share" aria-hidden="true"></i>
                           <span> Import POI </span>
                        </a>
                        <a href="#" data-toggle="modal" data-target="#preferencesModal">
                           <i class="fa fa-star" aria-hidden="true"></i>
                           <span> Preferences </span>
                        </a>
                     </div>
                  </div>
               </div>
               <div class="mesh-utils-btn">
                  <p class="dropdown-btn" id="viewModeBtn">
                     <i class="fa fa-eye"></i>
                     View Mode
                  </p>
                  <span class="view-mode-tag" id="viewModeTag"> Settings </span>
                  <div class="dropdown-container" id="viewModeDropdown">
                     <ul class="dropdown-list nav nav-tabs">
                        <li class="active dropdown-tab-link view-mode-tab-controller">
                           <a href="#settingsMode" data-toggle="tab" class="nav-link">
                              <i class="fa fa-gear"></i>
                              <span> Settings </span>
                           </a>
                        </li>
                        <li class="dropdown-tab-link view-mode-tab-controller">
                           <a href="#dataMode" data-toggle="tab" class="nav-link">
                              <i class="fa fa-database"></i>
                              <span> Data </span>
                           </a>
                        </li>
                        <li class="dropdown-tab-link view-mode-tab-controller">
                           <a href="#financeMode" data-toggle="tab" class="nav-link">
                              <i class="fa fa-money"></i>
                              <span> Finance </span>
                           </a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="mesh-planner mesh-utils-btn">
                  <p class="dropdown-btn" id="meshPlansBtn">
                     <i class="fa fa-caret-down"></i>
                     Mesh Planner
                  </p>
                  <span class="mesh-planner-tag" id="meshPlannerTag"> Basic </span>
                  <div class="dropdown-container" id="meshPlansDropdown">
                     <div class="dropdown-list">
                        <a href="javascript:void(0)" id="toggleBasic">
                           <i class="fa fa-th-large"></i>
                           <span> Basic </span>
                        </a>
                        <a href="javascript:void(0)" id="toggleAdvanced">
                           <i class="fa fa-th"></i>
                           <span> Advanced </span>
                        </a>
                     </div>
                  </div>
               </div>
            </div>
            <div class="mesh-planner-poi-data"  id="poiDataDrawer">
               <div class="poi-data">
                  <p> No. of Nodes: <span id="meshNodesCount"> 0 </span> </p>
                  <p> Coverage: <span id="meshPoiCoverage"> 0km<sup>2</sup> </span>  </p>
                  {{-- <p data-toggle="modal" data-target="#TBD"> View more </p> --}}
               </div>
            </div>
            <div class="mesh-planner-utils-top-left">
               <a href="javascript:void(0)" class="btn btn-success btn-xs mesh-utils-btn" onclick="toggleMapType()">
                  <i class="fa fa-map-marker" style="color:white" aria-hidden="true"></i>
                  <span  id="mapTypeTitle"> Satelite </span>
               </a>
               <a href="javascript:void(0)" class="btn btn-success btn-xs mesh-utils-btn" onclick="toggleNodeCircle()">
                  <i class="fa fa-life-ring" style="color:white" aria-hidden="true"></i>
                  Signal Radius:
                  <span id="nodeRadiusStatus"> Off </span>
               </a>
               {{-- @if ($poi) --}}
               <p class="btn btn-success btn-xs mesh-utils-btn" onclick="togglePoiVisibility()">
                  <i class="fa fa-street-view" style="color:white"></i>
                  <span> POI: <span id="poiMeshStatus"> Off </span> </span>
               </p>
               {{-- @endif --}}
            </div>
            <div class="map-bottom" id="meshData">
               <div class="map-data-view" id="switchDataView">
                  <i class="fa fa-bars"></i>
               </div>
               <div class="signal-legend">
                  <p> Strong </p>
                  <ul class="signal-colors-list">
                     <li class="first"></li>
                     <li class="middle"></li>
                     <li class="last"></li>
                  </ul>
                  <p>Weak</p>
               </div>
               <div class="data-view">
                  <div class="tower-settings" id="nodeSettingsContainer">
                     <h3 id="emptyNodeBoxMessage" class="empty-element-message"> No node selected </h3>
                     <div class="hidden" id="nodeSettings">
                        <div class="basic-settings" id="basicSettings">
                           <div class="n-list-element">
                              <h3 id="nodeIdentifier"> Node  </h3>
                           </div>
                           <ul class="nav nav-tabs view-mode-tabs">
                              <li class="view-mode-single-tab active view-mode-tab-controller">
                                 <a data-toggle="tab" href="#settingsMode">
                                    <i class="fa fa-gear"></i>
                                    <span> Settings </span>
                                 </a>
                              </li>
                              <li class="view-mode-single-tab view-mode-tab-controller">
                                 <a data-toggle="tab" href="#dataMode">
                                    <i class="fa fa-database"></i>
                                    <span> Data </span>
                                 </a>
                              </li>
                              <li class="view-mode-single-tab view-mode-tab-controller">
                                 <a data-toggle="tab" href="#financeMode">
                                    <i class="fa fa-money"></i>
                                    <span> Finance </span>
                                 </a>
                              </li>
                           </ul>
                           <div class="tab-content">
                              <div id="settingsMode" class="tab-pane view-mode-tab settings-mode-tab fade-in active">
                                 <div class="n-list-element">
                                    <p> Node Height (m) </p>
                                    <p class="input-group">
                                       <input id="nodeHeight" type="number" placeholder="m" name="" value="">
                                    </p>
                                 </div>
                                 <div class="n-list-element">
                                    <p> Node Latitude (°)  </p>
                                    <p class="input-group">
                                       <input id="nodeLatitude" type="number" placeholder="degrees" name="" value="">
                                    </p>
                                 </div>
                                 <div class="n-list-element">
                                    <p> Node Longitude (°)  </p>
                                    <p class="input-group">
                                       <input id="nodeLongitude" type="number" placeholder="degrees" name="" value="">
                                    </p>
                                 </div>
                                 <div class="n-list-element">
                                    <p> Signal Distance (m) </p>
                                    <p class="input-group">
                                       <input id="nodeAreaDistance" type="number" placeholder="m" name="" value="">
                                    </p>
                                 </div>
                                 <div class="toogler-heatmap">
                                    <h4> Heatmap </h3>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Visibility </p>
                                       <div class="on-off-wrapper">
                                          <div class="on-off">
                                             <input type="checkbox" id="heatmapSlider"/>
                                             <label for="heatmapSlider"></label>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Distance (m) </p>
                                       <div class="input-group">
                                          <input id="heatmapAreaSector" type="number" min="0"  placeholder="radius in m" name="" value="">
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Angle (°) </p>
                                       <div class="input-group">
                                          <select class="" name="" id="heatmapAreaAngle">
                                             <option value="7"> 7 </option>
                                             <option value="15"> 15 </option>
                                             <option value="30"> 30 </option>
                                             <option value="45"> 45 </option>
                                             <option value="60" selected> 60 </option>
                                             <option value="90"> 90 </option>
                                             <option value="120"> 120 </option>
                                             <option value="360"> 360 </option>
                                             <option value="custom_value"> Custom </option>
                                          </select>
                                          <input class="hidden" id="customAngleValue" type="number" placeholder="angle (degrees)" name="" value="">
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Heading (°) </p>
                                       <div class="input-group">
                                          <input id="heatmapAreaBearing" type="number" min="0" max="360" placeholder="(0 -north, 180 - south)" name="" value="">
                                       </div>
                                    </div>
                                    <div class="heat-advanced-elements" id="heatAdvancedElements">
                                       <div class="n-list-element">
                                          <p> Precision (°)  </p>
                                          <div class="input-group">
                                             <select class="" name="" id="heatmapBearingPitch">
                                                <option value="0.2"> 0.2 </option>
                                                <option value="0.5"> 0.5 </option>
                                                <option value="1"> 1 </option>
                                                <option value="2"> 2 </option>
                                                <option value="5"> 5 </option>
                                             </select>
                                          </div>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Points Distance (m)  </p>
                                          <div class="input-group">
                                             <input id="sectorLineStep" type="number" min="0" max="360" placeholder="meters" name="" value="">
                                          </div>
                                       </div>
                                    </div>
                                    <div class="advanced-settings" id="advancedSettings">
                                       <div class="n-list-element">
                                          <p> Bandwidth (MHz)  </p>
                                          <div class="input-group">
                                             <select class="" name="" id="bandWidth">
                                                <option value="40"> 40 </option>
                                                <option value="80" selected> 80 </option>
                                             </select>
                                          </div>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Transmit power (dBm) </p>
                                          <p class="input-group">
                                             <input type="number" id="nodeTransmitPower" placeholder="dBm" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Gain (dBi) </p>
                                          <p class="input-group">
                                             <input type="number" id="nodeGain" placeholder="dBi" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Cable loss (dB)</p>
                                          <p class="input-group">
                                             <input type="number" id="nodeCableLoss" placeholder="dB" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Other loss (dB)</p>
                                          <p class="input-group">
                                             <input type="number" id="nodeOtherLoss" placeholder="dB" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Second antenna height (m)</p>
                                          <p class="input-group">
                                             <input type="number" id="secondAntennaHeight" placeholder="m" name="" value="" min="0">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Second antenna gain (dBi)</p>
                                          <p class="input-group">
                                             <input type="number" id="secondNodeGain" placeholder="dBi" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Second antenna cable loss (dB)</p>
                                          <p class="input-group">
                                             <input type="number" id="secondNodeCableLoss" placeholder="dB" name="" value="">
                                          </p>
                                       </div>
                                       <div class="n-list-element">
                                          <p> Frequency (MHz)</p>
                                          <p class="input-group">
                                             <input type="number" id="nodeFrequency" placeholder="MHz" name="" value="">
                                          </p>
                                       </div>
                                    </div>
                                 </div>
                                 <div id="dataMode" class="tab-pane view-mode-tab data-mode-tab">
                                    {{-- <h3> Data </h3> --}}
                                    <div class="n-list-element">
                                       <p> Backhaul </p>
                                       <div class="on-off-wrapper">
                                          <div class="on-off">
                                             <input type="checkbox" id="backhaulStatus"/>
                                             <label for="backhaulStatus"></label>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Backhaul supply (Mbps) </p>
                                       <p class="input-group">
                                          <input id="backhaulSupply" type="number" placeholder="300" name="" value="" disabled>
                                       </p>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Users (avg) </p>
                                       <p class="input-group">
                                          <input id="areaUsers" type="number" placeholder="10" name="" value="" disabled>
                                       </p>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Service level (Mbps) </p>
                                       <div class="input-group">
                                          <input id="areaServiceLevel" type="number" min="0"  placeholder="15" name="" value="" disabled>
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Contention </p>
                                       <div class="input-group">
                                          <input id="defaultServiceLevel" type="number" min="0"  placeholder="10" name="" value="" disabled>
                                       </div>
                                    </div>
                                    <div class="n-list-element">
                                       <p> Hop Depth </p>
                                       <div class="input-group">
                                          <input id="defaultServiceLevel" type="number" min="0"  placeholder="1" name="" value="" disabled>
                                       </div>
                                    </div>
                                 </div>
                                 <div id="financeMode" class="tab-pane view-mode-tab finance-mode-tab fade">
                                    <h3> Coming soon... </h3>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="point-of-interest-settings hidden" id="pointOfInterestSettings">
                           <div class="n-list-element">
                              <h3 id="poiIdentifier"> POI  </h3>
                              <span class="remove-element-btn" data-toggle="modal" data-target="#deletePOIModal" data-title="Delete POI">
                                 <i class="fa fa-trash"></i>
                              </span>
                           </div>
                           <div class="n-list-element">
                              <p> Label </p>
                              <p class="input-group">
                                 <input id="pointOfInterestLabel" type="text" placeholder="P.O.I Label" name="" value="">
                              </p>
                           </div>
                           <div class="n-list-element">
                              <p> Type </p>
                              <p class="input-group">
                                 <input id="pointOfInterestType" type="text" placeholder="Type" name="" value="">
                              </p>
                           </div>
                           <div class="n-list-element">
                              <p> Address </p>
                              <p class="input-group">
                                 <input id="pointOfInterestAddress" type="text" placeholder="Address" name="" value="">
                              </p>
                           </div>
                           <div class="n-list-element">
                              <p> Latitude </p>
                              <p class="input-group">
                                 <input id="pointOfInterestLat" type="number" placeholder="Latitude" name="" value="">
                              </p>
                           </div>
                           <div class="n-list-element">
                              <p> Longitude </p>
                              <p class="input-group">
                                 <input id="pointOfInterestLng" type="number" placeholder="Longitude" name="" value="">
                              </p>
                           </div>
                           <div class="n-list-element">
                              <p> Icon </p>
                              <p class="input-group">
                                 <select id="pointOfInterestIcon" class="point-of-interest-icon">
                                    <option value="fa-home"> &#xf015; Home </option>
                                    <option value="fa-user" selected> &#xf007; User </option>
                                    <option value="fa-hospital-o"> &#xf0f8; Hospital </option>
                                    <option value="fa-graduation-cap">  &#xf19d;School </option>
                                    <option value="fa-shopping-basket"> &#xf291; Shop </option>
                                    <option value="fa-plane"> &#xf072; Airport </option>
                                    <option value="fa-tree"> &#xf1bb; Forest </option>
                                    <option value="fa-futbol-o"> &#xf1e3; Stadium </option>
                                    <option value="fa-paw"> &#xf1b0; Wildlife </option>
                                    <option value="fa-industry"> &#xf275; Factory </option>
                                    <option value="fa-university"> &#xf19c; University </option>
                                    <option value="fa-gavel"> &#xf0e3; Law Court </option>
                                    <option value="fa-cutlery"> &#xf0f5; Restaurant </option>
                                    <option value="custom"> Custom Icon </option>
                                 </select>
                              </p>
                           </div>
                           <div class="n-list-element hidden" id="customPoiIconEl">
                              <p> Custom Icon </p>
                              <div class="custom-poi-icon-input-group input-group">
                                 <input type="text" name="" value="" id="customIconValue" placeholder="ex: fa-user">
                                 <i class="fa fa-info-circle" id="iconsLinkDet" data-container="body" data-toggle="popover" data-content="<span> Example: fa-user </span> </br> For all the available icons visit  <a class='popoverLink' href='https://fontawesome.com/v4/icons/' target='_blank'> https://fontawesome.com/v4/icons/ </a>"></i>
                              </div>
                           </div>
                           <div class="n-list-element">
                              <p> Icon Color </p>
                              <p class="input-group">
                                 <select id="pointOfInterestIconColor" class="point-of-interest-colorpicker">
                                    <option value="#c3c0c0" selected> Light Gray </option>
                                    <option value="#FF9E67"> Orange </option>
                                    <option value="#4B96F3"> Dark Blue </option>
                                    <option value="#909CE1"> Light Purple </option>
                                    <option value="#13B5C7"> Teal </option>
                                    <option value="#7B9EB0"> Gray </option>
                                    <option value="#4DB546"> Green </option>
                                    <option value="#b96464"> Red </option>
                                 </select>
                              </p>
                           </div>
{{--                            <div class="n-list-element">--}}
{{--                              <button type="button" name="button" onclick="savePOI()"> Save </button>--}}
{{--                           </div>--}}
                        </div>
                     </div>
                     <div class="graph-box">
                        <h3 id="emptyGraphBoxMessage" class="empty-element-message"> No line selected </h3>
                        <div class="frs-canvas-container" id="graphContainer">
                           <div id="chartContainer"></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>


@endsection

@section('scripts')
   {{--   <script src="node_modules/js-big-decimal/dist/web/js-big-decimal.min.js"></script>--}}
   <script src="https://unpkg.com/js-big-decimal@1.3.1/dist/web/js-big-decimal.min.js"></script>
   <link rel="stylesheet" href="{{ asset('css/mesh-planner/meshtool-worksheet.css?v=1.0.9') }}" />
   <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/leaflet.js" type="text/javascript"></script>
   <script src="https://unpkg.com/regenerator-runtime@0.13.1/runtime.js"></script>
   <script src="https://unpkg.com/leaflet-topography" type="text/javascript"></script>
   <script src="{{asset('js/leaflet/leaflet.circle-sector.js')}}" type="text/javascript"></script>
   <script src="{{asset('js/leaflet/leaflet-marker-cluster/leaflet.marker-cluster-src.js')}}" type="text/javascript"></script>
   <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
   <!-- Make sure you put this AFtER leaflet.js, when using with leaflet -->

   <script src="https://unpkg.com/@raruto/leaflet-elevation/dist/leaflet-elevation.js"></script>
   <script src="{{asset('js/leaflet/leaflet-providers.js')}}" type="text/javascript"></script>
   <script src="{{asset('js/leaflet.awesome-markers.min.js')}}" type="text/javascript"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.fullscreen/2.2.0/Control.FullScreen.js" integrity="sha512-e8phN7cVJjPXHY2yF552WKNry6sFKczi3DWOftXmZjZjOM04kbO1hLdGrM/SSDGOQwa7log7vX6ZBwlPvMlcQg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.fullscreen/2.2.0/Control.FullScreen.min.css" integrity="sha512-Gf0xgqc7R4+2ATKUYRXPpl2xXWAbHIgIIGlqy1ugbTcuSSSKG7Kw/IULAuQWIiRVwQAn0CcLVRtI79C6mGROQQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
   <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
   integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
   crossorigin=""/>
   <link rel="stylesheet" href="{{ asset('css/leaflet/leaflet-marker-cluster/MarkerCluster.css') }}" />
   <link rel="stylesheet" href="{{ asset('css/leaflet/leaflet-marker-cluster/MarkerCluster.Default.css') }}" />
   <link rel="stylesheet" href="https://unpkg.com/@raruto/leaflet-elevation/dist/leaflet-elevation.css" />
   <link rel="stylesheet" href="https://ppete2.github.io/Leaflet.PolylineMeasure/Leaflet.PolylineMeasure.css" />
   <link rel="stylesheet"  href="https://unpkg.com/leaflet-geosearch@3.0.0/dist/geosearch.css"/>
   <script src="https://unpkg.com/leaflet-geosearch@3.0.0/dist/geosearch.umd.js"></script>

   {{--    <script src="{{asset('js/mesh-planner/meshtool-worksheet.js')}}" type="text/javascript"></script> --}}

   <script type="text/javascript">
   var APP_URL = '{{URL::to("/")}}'
   </script>
   <script src="https://unpkg.com/leaflet-geosearch@3.0.0/dist/geosearch.umd.js"></script>
   <script type="application/javascript">

   $('#iconsLinkDet').popover({
      trigger: 'focus, click',
      placement: 'top',
      html: true
   })

   $('body').on('click', function (e) {
    //did not click a popover toggle or popover
       if ($(e.target).data('toggle') !== 'popover'
           && $(e.target).parents('.popover.in').length === 0) {
           $('[data-toggle="popover"]').popover('hide');
       }
   });

   if (document.getElementById('success-message')) {
      setTimeout(() => {
         let successMessage = document.getElementById('success-message')
         successMessage.parentNode.removeChild(successMessage);
      }, 4000)
   }

   let importModal = $('#project-import');
   let importPOIModal = $('#project-import-poi');
   let loadingFixedBlade = document.getElementById('loadingScreenPinFixed')
   let actionsObject = {
      post: {
         method: 'POST',
         url: '{{ route('arra-project-lines-points.store', ['project' => request()->route('project')]) }}',
         success: 'Successfully stored!',
         error: 'Something went wrong!'
      },
      put: {
         method: 'POST',
         url: '{{ route('arra-project-lines-points.update', ['project' => request()->route('project')]) }}',
         success: 'Successfully updated!',
         error: 'Something went wrong!'
      }
   }

   var APP_URL = '{{URL::to("/")}}'
   window.csrf_ = '{{ csrf_token() }}'
   window.points = @json($nodes);
   window.lines = @json($lines);
   window.zoom = @json($zoom);
   window.center = @json($center);
   window.advancedMenuState = @json($advancedMenuState);
   window.poi = @json($poi);
   window.poiVisibilityStatus = @json($poiVisibilityStatus);
   window.noRefresh = false;
   window.reload = true;
   window.mapType = "{{ $project->map_type }}";
   window.node_connection_type_saved = @json($node_connection_type_saved);
   window.node_connection_type = @json($node_connection_type);

   let action = 'post';
   let isAdmin = parseInt({{ $isAdmin }})
   let adminGroup = "{{ $adminGroup }}"
   let ownerGroup  = "{{ $project->group_id }}"
   let projectName = null;

   function saveData(e) {
      if (isAdmin && ownerGroup !== adminGroup) {
         // trigger pop-up
          $('#owner-group').modal('show')
      } else {
        triggerSaveProject()
      }
   }

   function triggerSaveProject(owner = null) {
      mapData.project.name = projectName ? projectName : "{{ $project->name }}";
      if (owner) {
         mapData.project.own = owner;
      } else {
         mapData.project.own = "{{ $project->group_id }}";
      }

      loadingFixedBlade.classList.add('pin-active')
      savePOI()
      setTimeout( () => {
         $.ajax({
            url: actionsObject[action].url,
            type: actionsObject[action].method,
            data: JSON.stringify(mapData),
            success: function (response) {
               loadingFixedBlade.classList.remove('pin-active')
               toastr.success(actionsObject[action].success);
               window.points = response.data.nodes;
               window.lines = response.data.lines;
               window.zoom = response.data.zoom;
               window.center = response.data.center;
               window.advancedMenuState = response.data.advancedMenuState;
               window.poiVisibilityStatus = response.data.poiVisibilityStatus;
               window.node_connection_type_saved = response.data.node_connection_type_saved;
               window.node_connection_type = response.data.node_connection_type;
               window.noRefresh = true;
               window.reload = false;
               window.poi = response.data.poi;
            },
            error: function (response) {
               toastr.error(actionsObject[action].error);
            }
         })
      }, 1000)
   }

   const getSearchAddressCords = async (address) => {
      const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${address}.json?access_token=${topographyOptions.token}&limit=10`)
         const json = await response.json();
         return json;
      }

      $('#import-project').on('click', function () {
         importModal.modal('hide')
         loadingFixedBlade.classList.add('pin-active')
         let importProjectForm = document.getElementById('importProjectForm')

         setTimeout(() => {
            importProjectForm.submit();
            loadingFixedBlade.classList.remove('pin-active')
         }, 500)
      })


   // $("#input-poi-upload").click(function() {
   //    $(".poiUpload-step1").hide();
   //    $(".poiUpload-step2").fadeIn();
   // });
   //

   $('#input-poi-upload').on('click', function () {
     // importPOIModal.modal('hide')
      $(".poiUpload-step1").hide();
      loadingFixedBlade.classList.add('pin-active')
      // let importProjectPOIForm = document.getElementById('importProjectPOIForm')
      let formData = new FormData();
      formData.append('file', $('#file_poi')[0].files[0])

         $.ajax({
            url: '{{ route('arra-project.import-poi', $project->id) }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            enctype: 'multipart/form-data',
            success : function (response) {
               setTimeout(() => {
                  $(".poiUpload-step3").removeClass('hidden').fadeIn();
                  $.ajax({
                     url: '{{ route('arra-project.begin-poi-import', $project->id) }}',
                     type: 'POST',
                     data: {terms_and_conditions: true},
                     success: function (response) {
                        window.poi = response.data.poi;
                        window.noRefresh = true;
                        window.reload = false;
                        importPOIModal.modal('hide');
                        $(".poiUpload-step3").addClass('hidden');
                        setTimeout(() => {
                           importPOIModal.modal('show');
                           $(".poiUpload-step4").removeClass('hidden');
                        },1000)

                     },
                     error: function (error) {
                        console.log(error, 'error')
                     }
                  })
               }, 3000)
            },
            error : function (error) {
               $(".poiUpload-step1").show();
               let erorrMsg =  $('.empty-project-file');

               if (error.responseJSON.errors) {
                  if (error.responseJSON.errors.file) {
                     erorrMsg.text(error.responseJSON.errors.file[0]);
                     erorrMsg.removeClass('hidden')
                  }
               } else if (error.responseJSON.error) {
                  erorrMsg.text(error.responseJSON.error);
                  erorrMsg.removeClass('hidden')
               }
            }
         });


      loadingFixedBlade.classList.remove('pin-active')
   })

   $('#trigger-change-project-name-modal').on('click', function () {
       $('#change-project-name-modal').modal('show');
   });


   $('#change-project-name').on('click', function () {
      let projectNameInput = document.getElementById('project_name_bk').value;
      if (!projectNameInput.length) {
         return;
      }

      let formData = new FormData();

      formData.set('project_name', projectNameInput)

      $.ajax({
         type: 'POST',
         data: formData,
         url: '{{ route('arra-project.change-project-name', ['project' => $project->id]) }}',
         processData: false,
         contentType: false,
         cache: false,
         success: function(response) {
            $('#change-project-name-modal').modal('hide');
            projectNameInput.text = response.name;
            projectName = response.name;
            $('#projectName').text(response.name);
            toastr.success(response.message);
         },
         error: function (error) {
            $('#change-project-name-modal').modal('hide');
            toastr.error('Something went wrong!');
         }
      })
   })

   $('#change-group').on('click', function() {
      triggerSaveProject(adminGroup)
   });

   $('#skip-change-group').on('click', function() {
      triggerSaveProject();
   })

   $('#import_poi_modal').click(function(e) {
      e.preventDefault();
      $('#file_poi').val('');
      $(".poiUpload-step1").css('display','block');
      $(".poiUpload-step3").addClass('hidden');
      $(".poiUpload-step4").addClass('hidden');
      $(".poiUpload-step2").addClass('hidden');
      $('#project-import-poi').modal('show');
   })
   {{--$("#poi-checkout").click(function() {--}}
   {{--   $(".poiUpload-step2").hide();--}}
   {{--   $(".poiUpload-step3").removeClass('hidden').fadeIn();--}}

   {{--   $.ajax({--}}
   {{--      url: '{{ route('arra-project.begin-poi-import', $project->id) }}',--}}
   {{--      type: 'POST',--}}
   {{--      data: {terms_and_conditions: true},--}}
   {{--      success: function (response) {--}}
   {{--         console.log(response, 'response1')--}}
   {{--      },--}}
   {{--      error: function (error) {--}}

   {{--         console.log(error, 'error2')--}}
   {{--      }--}}
   {{--   })--}}
   {{--})--}}



      </script>
      <script src="{{asset('js/mesh-planner/meshtool-worksheet.js?v=1.1.4')}}" type="text/javascript"></script>

   @endsection
