@extends('layouts.librenmsv1')

@section('title', __('Add/Edit Billing Details'))

@section('content')
    <div class="container">
        <div class="row">
            <form action="javascript:void(0)" method="POST" role="form"
                  class="form-horizontal gbd-form col-md-10 col-md-offset-1 col-sm-12">
                <legend>@lang('Add/Edit Billing Details to Group'): {{ $device_group->name }}</legend>
                {{ method_field('PUT') }}
                @csrf

                @include('group-billing-details.form')

                <div class="form-group">
                    <div class="col-sm-9 col-sm-offset-3 col-md-10 col-sm-offset-2">
                        <button type="submit" class="btn btn-primary" id="save_bt">@lang('Save')</button>
                        <a type="button" class="btn btn-danger"
                           href="{{ route('gbd') }}">@lang('Cancel')</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('javascript')

	<script type="text/javascript">
        var APP_URL = '{{URL::to("/")}}';
    </script>
    <script src="{{ asset('js/arra/gbd.js') }}"></script>	
    
@endsection
