@extends('layouts.librenmsv1')

@section('title', __('Group Settings'))

@section('content')
    <div class="container-fluid">
        <div id="manage-device-groups-panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-bolt fa-fw fa-lg" aria-hidden="true"></i> @lang('Group Settings')
                </h4>
            </div>
            @if($device_groups)
            <div class="panel-body">
                <div class="table-responsive">
                    <table id="manage-device-groups-table" class="table table-condensed table-hover">
                        <thead>
                        <tr>
                            <th>@lang('Group')</th>
                            <th>@lang('SSID Name')</th>
                            <th>@lang('Business Model')</th>
                            <th>@lang('Company Name')</th>
                            <th>@lang('Incentivisation')</th>
                            <th>@lang('Actions')</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($device_groups as $device_group)
                            <tr id="row_{{ $device_group->id }}">
                                <td>{{ $device_group->name }}</td>
                                <td>{{ $device_group->sssid_name }}</td>
                                <td>{{ ucfirst(str_replace("-"," ",$device_group->business_model)) }}</td>
                                <td>{{ $device_group->company_name }}</td>
                                <td>{{ $device_group->incentivisation }}</td>
                                <td>
                                    <a type="button" title="@lang('add/edit Group Settings')" class="btn btn-primary btn-sm" aria-label="@lang('Edit')"
                                       href="{{ route("gsm-e", ['id' => $device_group->id]) }}">
                                        <i class="fa fa-pencil" aria-hidden="true"></i></a>
                                    <button type="button" class="btn btn-danger btn-sm" title="@lang('delete Group Settings')" aria-label="@lang('Delete')"
                                            onclick="delete_dg('{{ $device_group->id }}', '{{ route('gsm') }}')">
                                        <i
                                            class="fa fa-trash" aria-hidden="true"></i></button>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>

    </div>
@endsection

@section('javascript')
	<script type="text/javascript">
        var APP_URL = '{{URL::to("/")}}';
    </script>
    <script src="{{ asset('js/arra/gsm.js') }}"></script>
    @if($is_edit)
    <script type="text/javascript">
        $( document ).ready(function() {
        	toastr.success('Settings saved successfully.');
        });
    </script>
    @endif
@endsection

@section('css')
    <style>
        .table-responsive {
            padding-top: 16px
        }
    </style>
@endsection
