@if (count($table_data)!=0)

    <table class="table table-condensed">
        <thead>
        <tr>
            <th>
                <?php if($mesh_type == 'mesh1'){ ?>
                    <span class="mesh-stats-title"><span class="status-circle deeppink"></span> Mesh 1 <span class="fa fa-refresh"></span></span>
                <?php }else{ ?>
                    <span class="mesh-stats-title"><span class="status-circle mdot green"></span> Mesh 2 <span class="fa fa-refresh"></span></span>
                <?php } ?>
            </th>
            <th> Signal</th>
            <th align="right" style="text-align: right;"> <a href="javascript:void(0)" onclick="changeArraMeshOrder('{{$mesh_type}}','signal')"><img src="/images/custom/ret-arrow.jpg" alt=""/></a></th>
            <th class="modulation-break"> Modulation</th>
            <th align="right" style="text-align: right;"> <a href="javascript:void(0)" onclick="changeArraMeshOrder('{{$mesh_type}}','modulation')"><img src="/images/custom/ret-arrow.jpg" alt=""/></a></th>

        </tr>
        </thead>
        <tbody>
        @foreach ($table_data as $table_item)
        <tr>
            <td valign="middle" style="min-width: 110px">

                <span class="mesh-stats-mesh-id
                @if($table_item['had_error'])
                        red
                @elseif($table_item['is_connect'])
                        white
                @endif
                    ">{{$table_item['sysname']}}

                </span>
                <span class="mesh-stats-id-sm">
                    @if($table_item['had_error'])
                        <span class="clr-red">{{$table_item['grey']['ch']}} ch</span><br>
                    @else
                              {{$table_item['grey']['ch']}} ch<br>
                    @endif
                                {{$table_item['grey']['bw']}} bw<br>
                                {{$table_item['grey']['db']}} db
                                </span>
            </td>

            <td align="right" valign="middle">
                <span class="clr-lighter">{{$table_item['white']['snr']}}</span> <br> <span class="clr-lighter">{{$table_item['white']['signal']}}/{{$table_item['white']['noise-floor']}}</span>
            </td>

            <td align="right" valign="middle">
                {{$table_item['grey']['snr']}} <br> {{$table_item['grey']['signal']}}/{{$table_item['grey']['noise-floor']}}
            </td>

            <td align="right" valign="middle" style="min-width: 50px">
                <span class="clr-lighter">{{$table_item['white']['rx']}} <span class="data-span-small">Rx</span></span> <br> <span class="clr-lighter">{{$table_item['white']['tx']}} <span class="data-span-small">Tx</span></span>
            </td>

            <td align="right" valign="middle" style="min-width: 50px">
                {{$table_item['grey']['tx']}} <span class="data-span-small">Tx</span> <br> {{$table_item['grey']['rx']}} <span class="data-span-small">Rx</span>
            </td>
            <input type="hidden" class="mesh_device_host" value="{{$table_item['hostname']}}" />
        </tr>
        @endforeach

        </tbody>
    </table>

@endif