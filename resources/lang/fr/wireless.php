<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Wireless Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of wireless sensors
    |
    */

    'ap-count' => [
        'short' => 'APs',
        'long' => "Nombre de points d'accès",
        'unit' => '',
    ],
    'clients' => [
        'short' => 'Clients',
        'long' => 'Nombre de clients',
        'unit' => '',
    ],
    'capacity' => [
        'short' => 'Capacité',
        'long' => 'Capacité',
        'unit' => '%',
    ],
    'ccq' => [
        'short' => 'CCQ',
        'long' => 'Qualité de la connexion client',
        'unit' => '%',
    ],
    'errors' => [
        'short' => 'Erreurs',
        'long' => "Nombre d'erreurs",
        'unit' => '',
    ],
    'error-ratio' => [
        'short' => "Ratio d'erreur",
        'long' => "Bit/Paquets ratio d'erreur",
        'unit' => '%',
    ],
    'error-rate' => [
        'short' => 'BER',
        'long' => "Taux d'erreur",
        'unit' => 'bps',
    ],
    'frequency' => [
        'short' => 'Fréquence',
        'long' => 'Fréquence',
        'unit' => 'MHz',
    ],
    'distance' => [
        'short' => 'Distance',
        'long' => 'Distance',
        'unit' => 'km',
    ],
    'mse' => [
        'short' => 'MSE',
        'long' => 'Erreur quadratique moyenne',
        'unit' => 'dB',
    ],
    'noise-floor' => [
        'short' => 'Bruit de fond',
        'long' => 'Bruit de fond',
        'unit' => 'dBm/Hz',
    ],
    'power' => [
        'short' => 'Puissance/Signal',
        'long' => 'TX/RX Puissance ou Signal',
        'unit' => 'dBm',
    ],
    'quality' => [
        'short' => 'Qualité',
        'long' => 'Qualité',
        'unit' => '%',
    ],
    'rate' => [
        'short' => 'Taux',
        'long' => 'TX/RX Taux',
        'unit' => 'bps',
    ],
    'rssi' => [
        'short' => 'RSSI',
        'long' => "Indicateur d'intensité du signal reçu",
        'unit' => 'dBm',
    ],
    'snr' => [
        'short' => 'SNR',
        'long' => 'Rapport signal sur bruit',
        'unit' => 'dB',
    ],
    'ssr' => [
        'short' => 'SSR',
        'long' => 'Rapport de force du signal',
        'unit' => 'dB',
    ],
    'utilization' => [
        'short' => 'Utilisation',
        'long' => 'utilisation',
        'unit' => '%',
    ],
    'xpi' => [
        'short' => 'XPI',
        'long' => 'Interférence polaire croisée',
        'unit' => 'dB',
    ],

];
