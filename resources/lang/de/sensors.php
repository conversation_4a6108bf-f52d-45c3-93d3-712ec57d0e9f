<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Sensors Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to translate names and units of sensors
    |
    */

    'airflow' => [
        'short' => 'Luftfluß',
        'long' => 'Luftfluß',
        'unit' => 'cfm',
        'unit_long' => 'Kubikfuß pro Minute',
    ],
    'ber' => [
        'short' => 'BER',
        'long' => 'Bitfehlerrate',
        'unit' => '',
        'unit_long' => '',
    ],
    'charge' => [
        'short' => 'Ladung',
        'long' => 'Ladung Prozent',
        'unit' => '%',
        'unit_long' => 'Prozent',
    ],
    'chromatic_dispersion' => [
        'short' => 'Chromatische Dispersion',
        'long' => 'Chromatische Dispersion',
        'unit' => 'ps/nm/km',
        'unit_long' => 'Picosekunden pro Nanometer per Kilometer',
    ],
    'cooling' => [
        'short' => 'Kühlung',
        'long' => '',
        'unit' => 'W',
        'unit_long' => 'Watt',
    ],
    'count' => [
        'short' => 'Zähler',
        'long' => 'Zähler',
        'unit' => '',
        'unit_long' => '',
    ],
    'current' => [
        'short' => 'Strom',
        'long' => 'Strom',
        'unit' => 'A',
        'unit_long' => 'Ampere',
    ],
    'dbm' => [
        'short' => 'dBm',
        'long' => 'dBm',
        'unit' => 'dBm',
        'unit_long' => 'Decibel-Milliwatt',
    ],
    'delay' => [
        'short' => 'Verzögerung',
        'long' => 'Verzögerung',
        'unit' => 's',
        'unit_long' => 'Sekunden',
    ],
    'eer' => [
        'short' => 'EER',
        'long' => 'Energy Effizienz Rate',
        'unit' => '',
        'unit_long' => '',
    ],
    'fanspeed' => [
        'short' => 'Lüfterdrehzahl',
        'long' => 'Lüfterdrehzahl',
        'unit' => 'RPM',
        'unit_long' => 'Umdrehungen pro Minute',
    ],
    'frequency' => [
        'short' => 'Frequenz',
        'long' => 'Frequenz',
        'unit' => 'Hz',
        'unit_long' => 'Hertz',
    ],
    'humidity' => [
        'short' => 'Luftfeuchtigkeit',
        'long' => 'Luftfeuchtigkeit Prozent',
        'unit' => '%',
        'unit_long' => 'Prozent',
    ],
    'load' => [
        'short' => 'Last',
        'long' => 'Last Prozent',
        'unit' => '%',
        'unit_long' => 'Prozent',
    ],
    'power' => [
        'short' => 'Leistung',
        'long' => 'Leistung',
        'unit' => 'W',
        'unit_long' => 'Watt',
    ],
    'power_consumed' => [
        'short' => 'Verbrauchsleistung',
        'long' => 'Verbrauchsleistung',
        'unit' => 'kWh',
        'unit_long' => 'Killowatt-Stunden',
    ],
    'power_factor' => [
        'short' => 'Leistungsfaktor',
        'long' => 'Leistungsfaktor',
        'unit' => '',
        'unit_long' => '',
    ],
    'pressure' => [
        'short' => 'Luftdruck',
        'long' => 'Luftdruck',
        'unit' => 'kPa',
        'unit_long' => 'Kilopascal',
    ],
    'quality_factor' => [
        'short' => 'Qualitätsfaktor',
        'long' => 'Qualitätsfaktor',
        'unit' => '',
        'unit_long' => '',
    ],
    'runtime' => [
        'short' => 'Laufzeit',
        'long' => 'Leufzeit',
        'unit' => 'Min',
        'unit_long' => 'Minuten',
    ],
    'signal' => [
        'short' => 'Signalstärke',
        'long' => 'Signalstärke',
        'unit' => 'dBm',
        'unit_long' => 'Decibel-Milliwatt',
    ],
    'snr' => [
        'short' => 'SNR',
        'long' => 'Signal zu Rausch Verhältnis',
        'unit' => 'dB',
        'unit_long' => 'Decibel',
    ],
    'state' => [
        'short' => 'Status',
        'long' => 'Status',
        'unit' => '',
    ],
    'temperature' => [
        'short' => 'Temperatur',
        'long' => 'Temperatur',
        'unit' => '°C',
        'unit_long' => '° Celsius',
    ],
    'voltage' => [
        'short' => 'Spannung',
        'long' => 'Spannung',
        'unit' => 'V',
        'unit_long' => 'Volt',
    ],
    'waterflow' => [
        'short' => 'Wasserdurchfluß',
        'long' => 'Wasserdurchfluß',
        'unit' => 'l/m',
        'unit_long' => 'Liter pro Minute',
    ],
];
