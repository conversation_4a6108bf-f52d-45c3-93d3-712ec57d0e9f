-- <PERSON>Boss 530 MIB

-- v 1.43 January 07 2016

-- Copyright (c) 2016 by Asentria Corporation.  All rights reserved.


SITEBOSS-530-STD-MIB DEFINITIONS ::= BEGIN

IMPORTS
 DisplayString
    FROM SNMPv2-TC
 IpAddress, TimeTicks, <PERSON>te<PERSON>32,
 M<PERSON>ULE-IDENTITY, NOTIFICATION-TYPE, OBJECT-TYPE
    FROM SNMPv2-SMI
 asentria
    FROM ASENTRIA-ROOT-MIB;

--***************************************************************************************
--MODULE IDENTITY
--***************************************************************************************

  s530 MODULE-IDENTITY
    LAST-UPDATED "201601070143Z"
    ORGANIZATION "Asentria Corporation"
    CONTACT-INFO
     "Phone:  ************
      Fax:    ************
      Email:  <EMAIL>"
    DESCRIPTION
      "MIB module for managing Asentria SiteBoss 530"

    REVISION      "201601070143Z"
    DESCRIPTION
      "-v1.43
       -Added Event Sensor Alias and Class OID for event states.
       -Added Fuel Sensor Fuel Type.
       -Applicable since unit version 2.11.140."

    REVISION      "201504030157Z"
    DESCRIPTION
      "-v1.42
       -Allow read access for serial number, product name, software version
       -Applicable since unit version 2.10.700."

    REVISION      "201412300141Z"
    DESCRIPTION
      "-v1.41
       -Removed wirelessPIN and scSecret objects (OMX-1064).
       -Applicable since unit version 2.10.610."

    REVISION      "201412110140Z"
    DESCRIPTION
      "-v1.40
       -Changed analog-input fuel sensor objects from Integer32 to DisplayString (OMX-1177).
       -Applicable since unit version 2.10.580."

    REVISION      "201410160139Z"
    DESCRIPTION
      "-v1.39
       -Added espcAIDisplayFormat object (OMX-1112).
       -Removed espcAIPolarity, espcAIConvHighSign, espcAIConvLowSign objects (OMX-1113).
       -Applicable since unit version 2.10.460."

    REVISION      "201410030138Z"
    DESCRIPTION
      "-v1.38
       -Changed analog input objects from Integer32 to DisplayString.
       -Applicable since unit version 2.10.460."

    REVISION      "201408140137Z"
    DESCRIPTION
      "-v1.37
       -Fixed description for wirelessConnIP objects (OMX-1061).
       -Applicable since unit version 2.10.390."

    REVISION      "201407030136Z"
    DESCRIPTION
      "-v1.36
       -Fixed wirelessIdleTimeout (OMX-1037).
       -Fixed CPE status objects (OMX-1036).
       -Applicable since unit version 2.10.390."

    REVISION      "201406240135Z"
    DESCRIPTION
      "-v1.35
       -Fixed CPE status objects.
       -Applicable since unit version 2.10.340."

    REVISION      "201405290134Z"
    DESCRIPTION
      "-v1.34
       -Added various missing objects (OMX-751).
       -Added CPE objects (OMX-162).
       -Applicable since unit version 2.10.320."

    REVISION      "201404110133Z"
    DESCRIPTION
      "-v1.33
       -Clarified relay and power output function and documentation (OMX-926).
       -Applicable since unit version 2.10.300."

    REVISION      "201401170132Z"
    DESCRIPTION
      "-v1.32
       -Added power distribution objects (powerDistributionStatus branch) (OMX-708).
       -Fixed ipRestrictionMask wrong type (OMX-743).
       -Added ftpPushPermissions (OMX-737).
       -Applicable since unit version 2.10.150."

    REVISION      "201312040131Z"
    DESCRIPTION
      "-v1.31
       -Added fsGenEnable (OMX-661).
       -Applicable since unit version 2.10.110."

    REVISION      "201308010130Z"
    DESCRIPTION
      "-v1.30
       -Added missing trapEventTypeNumber available values in description (OMX-437).
       -Applicable since unit version 2.09.510."

    REVISION      "201306120129Z"
    DESCRIPTION
      "-v1.29
       -Renamed EX10 to Ethernet Expansion (OMX-364)
       -Applicable since unit version 2.09.510."

    REVISION      "201305280128Z"
    DESCRIPTION
      "-v1.28
       -Fixed incorrect SYNTAX for sysCharMask (OMX-328).
       -Applicable since unit version 2.09.380."

    REVISION      "201305100127Z"
    DESCRIPTION
      "-v1.27
       -Fixed incorrect SYNTAX statements for various objects (OMX-314).
       -Applicable since unit version 2.09.380."

    REVISION      "201303150126Z"
    DESCRIPTION
      "-v1.26
       -Renamed sysLocation branch to sysLoc (OMX-198).
       -Applicable since unit version 2.09.240."

    REVISION      "201302060125Z"
    DESCRIPTION
      "-v1.25
       -Added ex10 branch.
       -Renamed telnet* objects.
       -Applicable since unit version 2.09.240."

    REVISION      "201301140124Z"
    DESCRIPTION
      "-v1.24
       -Added evReset branch and associated notification (OMX-83).
       -Applicable since unit version 2.09.220."

    REVISION      "201211260123Z"
    DESCRIPTION
      "-v1.23
       -Removed ethRouting* objects.
       -Fixed backwards pppRoutingEnable comment.  (BN 4620)"

    REVISION      "201208220122Z"
    DESCRIPTION
      "-v1.22
       -Added additional AC power monitor items."

    REVISION      "201207090121Z"
    DESCRIPTION
      "-v1.21
       -Removed wirelessBand object."

    REVISION      "201206120120Z"
    DESCRIPTION
      "-v1.20
       -Removed user credentials objects."

    REVISION      "201205020119Z"
    DESCRIPTION
      "-v1.19
       -Added object for location."

    REVISION      "201204050118Z"
    DESCRIPTION
      "-v1.18
       -Added combined eventsensor status objects."

    REVISION      "201203060117Z"
    DESCRIPTION
      "-v1.17
       -Added AC power monitor config objects."

    REVISION      "201108080900Z"
    DESCRIPTION
      "-v1.16
       -Added s530StockOutputTrap notification definition."

    REVISION      "201108010900Z"
    DESCRIPTION
      "-v1.15
       -Added evMgmtReprocess object."

    REVISION      "201107080900Z"
    DESCRIPTION
      "-v1.14
       -Added wirelessModemStatus branch."

    REVISION      "201101050900Z"
    DESCRIPTION
      "-v1.13
       -Added espcCCNormalThreshold object."

    REVISION      "201011030900Z"
    DESCRIPTION
      "-v1.12
       -Changed deadband, threshold, and current values for temperature
       sensors from integer to floating-point type.  This means the
       SNMP object type for the temperature settings (deadbands and
       thresholds) changed from Integer32 to DisplayString.
       -Fixed bug where SNMP integer range defined in the MIB for
       trapIncludedValue was 16-bit instead of 32-bit."

    REVISION      "201008240900Z"
    DESCRIPTION
      "-v1.11
       -Fixed bug where some stock traps had too few varbinds declared.
       -Removed useless trap definitions."

    REVISION      "201003160900Z"
    DESCRIPTION
      "-v1.10
       -Removed useless objects consoleInlineHsk, consoleBaud, consoleFormat, consoleHsk."

    REVISION      "201001190900Z"
    DESCRIPTION
      "-v1.09
       -Increased range for esPointValueInt"

    REVISION      "200903310900Z"
    DESCRIPTION
      "-v1.08
       -Corrected missing ActionPagerConfig definition."

    REVISION      "200811120900Z"
    DESCRIPTION
      "-v1.07
       -Corrected comments for stock trap definitions."

    REVISION      "200806090900Z"
    DESCRIPTION
      "-v1.06
       -Added sysLocation branch.
       -Updated phone numbers in contact info block."

    REVISION      "200806021100Z"
    DESCRIPTION
      "-v1.05
       -Added SitePath CPE routing authorization requested stock trap."

    REVISION      "200805151500Z"
    DESCRIPTION
      "-v1.04
       -Added pager settings"

    REVISION      "200804221652Z"
    DESCRIPTION
      "-v1.03
       -Added missing wireless objects
       -Removed wirelessBaud object which broke mib walk
       -Fixed issue where snmpNotificationTx branch was defined wrong."

    REVISION      "200804091640Z"
    DESCRIPTION
      "-v1.02
       -Removed scripting object references"

    REVISION      "200803211650Z"
    DESCRIPTION
      "-v1.01
       -Added missing CPEDown/VPNGDow notification definitions"

    REVISION      "200802131420Z"
    DESCRIPTION
      "-v1.00
       -Initial MIB"
    ::= { asentria 12 }
    --*******.4.1.3052.12


--***************************************************************************************
--Concise MIB View: Top Level Branches
--***************************************************************************************

--  status                                 .1
--    eventSensorStatus                    .1.1
--      esPointTable                       .1.1.1
--    dataEventStatus                      .1.2
--      deStatusTable                      .1.2.1
--    modemStatus                          .1.3
--    modemCLIDLogTable                    .1.4
--    powerDistributionStatus              .1.5
--    fuelSensorStatus                     .1.6
--      fsStatusTable                      .1.6.1
--    wirelessModemStatus                  .1.7
--    acPowerMonitorStatus                 .1.8
--      acpmStatusTable                    .1.8.1
--  config                                 .2
--    eventSensorBasics                    .2.1
--      esNumberEventSensors               .2.1.1
--      esTable                            .2.1.2
--        esNewSensors                     .2.1.3
--    eventSensorPointConfig               .2.2
--      esPointConfigTempTable             .2.2.1
--      esPointConfigCCTable               .2.2.2
--      esPointConfigHumidTable            .2.2.3
--      esPointConfigAITable               .2.2.5
--      esPointConfigOutputTable           .2.2.6
--    serialPorts                          .2.3
--      numberPorts                        .2.3.1
--      portConfigTable                    .2.3.2
--    network                              .2.4
--      interface                          .2.4.1
--      defaultRouter                      .2.4.2
--      dnsTable                           .2.4.3
--      hostname                           .2.4.4
--      hostTable                          .2.4.5
--      ncpDuplex                          .2.4.6
--      ncpTimeout                         .2.4.7
--      snmp                               .2.4.8
--      ftpPush                            .2.4.9
--      ppp                                .2.4.10
--      routing                            .2.4.11
--      netSecurity                        .2.4.12
--      rts                                .2.4.13
--      trap                               .2.4.14
--      routeTest                          .2.4.15
--      gprs                               .2.4.16
--      email                              .2.4.17
--      netAdvanced                        .2.4.18
--      web                                .2.4.19
--      ethExpan                           .2.4.20
--      cpe                                .2.4.22
--    modem                                .2.5
--    time                                 .2.8
--      clock                              .2.8.1
--    console                              .2.10
--    unitSecurity                         .2.11
--      secCore                            .2.11.1
--      secUserTable                       .2.11.2
--      secFactory                         .2.11.3
--      secVPN                             .2.11.5
--    event                                .2.12
--      evCore                             .2.12.1
--      evData                             .2.12.2
--      evNoData1                          .2.12.3
--      evNoData2                          .2.12.4
--      evSched1                           .2.12.5
--      evSched2                           .2.12.6
--      evShskLowTable                     .2.12.7
--      evShskHighTable                    .2.12.8
--      evNoSensor                         .2.12.9
--      fuelSensor                         .2.12.11
--        fuelSensorGeneralTable           .2.12.11.1
--        fuelSensorTankTable              .2.12.11.2
--        fuelSensorCustomTankTable        .*********
--        fuelSensorVolumeTable            .*********
--        fuelSensorDisconnectTable        .*********
--      acPowerMonitor                     .2.12.12
--      evReset                            .2.12.16
--    action                               .2.14
--      actionCallNumberTable              .2.14.1
--      actionPagerTable                   .2.14.2
--      actionSched                        .2.14.3
--      actionAsentria                     .2.14.4
--      actionHostTable                    .2.14.6
--      actionEmailTable                   .2.14.7
--      actionParseError                   .2.14.8
--    sys                                  .2.16
--      sysTime                            .2.16.1
--      sysPT                              .2.16.2
--      sysMTU                             .2.16.3
--      sysAnswerString                    .2.16.4
--      sysEventFileID                     .2.16.6
--      sysEscapeCharacter                 .2.16.7
--      sysTimeStamp                       .2.16.8
--      sysLog                             .2.16.9
--      sysCRDB                            .2.16.10
--      sysCharMask                        .2.16.11
--      sysPrompt                          .2.16.12
--      sysBootStatus                      .2.16.13
--      sysLoc                             .2.16.14
--      sysAssetMgmt                       .2.16.15
--    auditLog                             .2.17
--  productIds                             .3
--    siteName                              3.1
--    thisProduct                          .3.2
--    stockTrapString                      .3.3
--    trapEventTypeNumber                  .3.4
--    trapEventTypeName                    .3.5
--    trapIncludedValue                    .3.6
--    trapIncludedString                   .3.7
--    trapTypeString                       .3.8
--    trapEventClassNumber                 .3.9
--    trapEventClassName                   .3.10
--  keyInterface                           .4.0

--***************************************************************************************
--Complete MIB View: All Objects
--***************************************************************************************

--  status                                           .1
--    eventSensorStatus                              .1.1
--      esPointTable                                 .1.1.1
--        esPointEntry                               .*******
--          esIndexES                                .*******.1
--          esIndexPC                                .*******.2
--          esIndexPoint                             .*******.3
--          esPointName                              .*******.4
--          esPointInEventState                      .*******.5
--          esPointValueInt                          .*******.6
--          esPointValueStr                          .*******.7
--          esPointTimeLastChange                    .*******.8
--          esPointTimetickLastChange                .*******.9
--          esPointAliasValueStr                     .*******.10
--          esPointClassValueStr                     .*******.11
--    dataEventStatus                                .1.2
--      deStatusTable                                .1.2.1
--        deStatusEntry                              .1.2.1.1
--          deStatusIndex                            .1.2.1.1.1
--          deStatusName                             .1.2.1.1.2
--          deStatusCounter                          .1.2.1.1.3
--          deStatusThreshold                        .1.2.1.1.4
--    modemStatus                                    .1.3
--    modemCLIDLogTable                              .1.4
--      modemCLIDLogEntry                            .1.4.1
--        modemCLIDLogIndex                          .1.4.1.1
--        modemCLIDLogNumber                         .1.4.1.2
--    powerDistributionStatus                        .1.5
--      pdConfig                                     .1.5.1
--      pdNextGen                                    .1.5.4
--        pdnTable                                   .1.5.4.1
--          pdnEntry                                 .1.5.4.1.1
--            pdnIndexPD                             .1.5.4.1.1.1
--            pdnIndexOutput                         .1.5.4.1.1.2
--            pdnConfig                              .1.5.4.1.1.3
--            pdnMainCurrentInEventState             .1.5.4.1.1.4
--            pdnMainCurrentValue                    .1.5.4.1.1.5
--            pdnMainCurrentValueStr                 .1.5.4.1.1.6
--            pdnMainCurrentDeadband                 .1.5.4.1.1.7
--            pdnMainCurrentVHighCurrent             .1.5.4.1.1.8
--            pdnMainCurrentHighCurrent              .1.5.4.1.1.9
--            pdnMainCurrentLowCurrent               .1.5.4.1.1.10
--            pdnMainCurrentVLowCurrent              .1.5.4.1.1.11
--            pdnMainVoltageInEventState             .1.5.4.1.1.12
--            pdnMainVoltageValue                    .1.5.4.1.1.13
--            pdnMainVoltageValueStr                 .1.5.4.1.1.14
--            pdnMainVoltageDeadband                 .1.5.4.1.1.15
--            pdnMainVoltageVHighVoltage             .1.5.4.1.1.16
--            pdnMainVoltageHighVoltage              .1.5.4.1.1.17
--            pdnMainVoltageLowVoltage               .1.5.4.1.1.18
--            pdnMainVoltageVLowVoltage              .1.5.4.1.1.19
--            pdnMainPowerValue                      .1.5.4.1.1.20
--            pdnMainPowerValueStr                   .1.5.4.1.1.21
--            pdnOutputCurrentInEventState           .1.5.4.1.1.22
--            pdnOutputCurrentValue                  .1.5.4.1.1.23
--            pdnOutputCurrentValueStr               .1.5.4.1.1.24
--            pdnOutputCurrentDeadband               .1.5.4.1.1.25
--            pdnOutputCurrentVHighCurrent           .1.5.4.1.1.26
--            pdnOutputCurrentHighCurrent            .1.5.4.1.1.27
--            pdnOutputCurrentLowCurrent             .1.5.4.1.1.28
--            pdnOutputCurrentVLowCurrent            .1.5.4.1.1.29
--            pdnOutputFuseInEventState              .1.5.4.1.1.30
--            pdnOutputFuseValueStr                  .1.5.4.1.1.31
--            pdnMainCombinedStatus                  .1.5.4.1.1.32
--            pdnOutputCombinedStatusBlock1          .1.5.4.1.1.33
--            pdnOutputCombinedStatusBlock2          .1.5.4.1.1.34
--            pdnDeviceCurrentValue                  .1.5.4.1.1.35
--            pdnDeviceCurrentValueStr               .1.5.4.1.1.36
--      pdSystem                                     .1.5.5
--        pdSystemCurrent                            .1.5.5.1
--        pdSystemPower                              .1.5.5.2
--    fuelSensorStatus                               .1.6
--      fsStatusTable                                .1.6.1
--        fsStatusEntry                              .1.6.1.1
--          fsStatusIndex                            .1.6.1.1.1
--          fsStatusName                             .1.6.1.1.2
--          fsStatusDeviceState                      .1.6.1.1.3
--          fsStatusVolumeValueString                .1.6.1.1.4
--          fsStatusVolumePercentLevel               .1.6.1.1.7
--          fsStatusVolumeInEventState               .1.6.1.1.8
--          fsStatusCombined                         .1.6.1.1.9
--    wirelessModemStatus                            .1.7
--      wmsStatus                                    .1.7.1
--      wmsSignal                                    .1.7.2
--      wmsRSSI                                      .1.7.3
--      wmsBER                                       .1.7.4
--      wmsUpdated                                   .1.7.5
--      wmsRegistration                              .1.7.6
--      wmsLAC                                       .1.7.7
--      wmsCellID                                    .1.7.8
--      wmsIMSI                                      .1.7.9
--      wmsPhnum                                     .1.7.10
--      wmsLocalIP                                   .1.7.11
--      wmsMgfID                                     .1.7.12
--      wmsModelID                                   .1.7.13
--      wmsIMEI                                      .1.7.14
--      wmsRevID                                     .1.7.15
--      wmsNetName                                   .1.7.16
--      wmsGPRSStatus                                .1.7.17
--      wmsBand                                      .1.7.18
--      wmsChannel                                   .1.7.19
--      wmsCountryCode                               .1.7.20
--      wmsNetCode                                   .1.7.21
--      wmsPLMNColor                                 .1.7.22
--      wmsBScolor                                   .1.7.23
--      wmsMpRACH                                    .1.7.24
--      wmsMinRxLevel                                .1.7.25
--      wmsBaseCoeff                                 .1.7.26
--      wmsSIMStatus                                 .1.7.27
--      wmsICCID                                     .1.7.28
--      wmsModemType                                 .1.7.29
--    acPowerMonitorStatus                           .1.8
--      acpmStatusTable                              .1.8.1
--        acpmStatusEntry                            .1.8.1.1
--          acpmsIndex                               .1.8.1.1.1
--          acpmsName                                .1.8.1.1.2
--          acpmsAvgVoltageValueStr                  .1.8.1.1.3
--          acpmsAvgVoltageMinStr                    .1.8.1.1.4
--          acpmsAvgVoltageMaxStr                    .1.8.1.1.5
--          acpmsAvgVoltageAvgStr                    .1.8.1.1.6
--          acpmsAvgVoltageInEventState              .1.8.1.1.7
--          acpmsVoltagePhaseAValueStr               .1.8.1.1.8
--          acpmsVoltagePhaseBValueStr               .1.8.1.1.9
--          acpmsVoltagePhaseCValueStr               .1.8.1.1.10
--          acpmsAvgCurrentValueStr                  .1.8.1.1.11
--          acpmsAvgCurrentMinStr                    .1.8.1.1.12
--          acpmsAvgCurrentMaxStr                    .1.8.1.1.13
--          acpmsAvgCurrentAvgStr                    .1.8.1.1.14
--          acpmsAvgCurrentInEventState              .1.8.1.1.15
--          acpmsCurrentPhaseAValueStr               .1.8.1.1.16
--          acpmsCurrentPhaseBValueStr               .1.8.1.1.17
--          acpmsCurrentPhaseCValueStr               .1.8.1.1.18
--          acpmsFreqValueStr                        .1.8.1.1.19
--          acpmsFreqMinStr                          .1.8.1.1.20
--          acpmsFreqMaxStr                          .1.8.1.1.21
--          acpmsFreqAvgStr                          .1.8.1.1.22
--          acpmsFreqInEventState                    .1.8.1.1.23
--          acpmsTRPValueStr                         .1.8.1.1.24
--          acpmsTRPMinStr                           .1.8.1.1.25
--          acpmsTRPMaxStr                           .1.8.1.1.26
--          acpmsTRPAvgStr                           .1.8.1.1.27
--          acpmsTRPInEventState                     .1.8.1.1.28
--          acpmsRPPhaseAValueStr                    .1.8.1.1.29
--          acpmsRPPhaseBValueStr                    .1.8.1.1.30
--          acpmsRPPhaseCValueStr                    .1.8.1.1.31
--          acpmsCombined                            .1.8.1.1.32
--          acpmsTPFValueStr                         .1.8.1.1.33
--          acpmsTPFMinStr                           .1.8.1.1.34
--          acpmsTPFMaxStr                           .1.8.1.1.35
--          acpmsTPFAvgStr                           .1.8.1.1.36
--          acpmsTPFInEventState                     .1.8.1.1.37
--          acpmsPFPhaseAValueStr                    .1.8.1.1.38
--          acpmsPFPhaseBValueStr                    .1.8.1.1.39
--          acpmsPFPhaseCValueStr                    .1.8.1.1.40
--          acpmsTRcPValueStr                        .1.8.1.1.41
--          acpmsTRcPMinStr                          .1.8.1.1.42
--          acpmsTRcPMaxStr                          .1.8.1.1.43
--          acpmsTRcPAvgStr                          .1.8.1.1.44
--          acpmsRcPPhaseAValueStr                   .1.8.1.1.45
--          acpmsRcPPhaseBValueStr                   .1.8.1.1.46
--          acpmsRcPPhaseCValueStr                   .1.8.1.1.47
--          acpmsTAPValueStr                         .1.8.1.1.48
--          acpmsTAPMinStr                           .1.8.1.1.49
--          acpmsTAPMaxStr                           .1.8.1.1.50
--          acpmsTAPAvgStr                           .1.8.1.1.51
--          acpmsAPPhaseAValueStr                    .1.8.1.1.52
--          acpmsAPPhaseBValueStr                    .1.8.1.1.53
--          acpmsAPPhaseCValueStr                    .1.8.1.1.54
--          acpmsTotalEnergyWh                       .1.8.1.1.55
--          acpmsTotalEnergyVAR                      .1.8.1.1.56
--          acpmsTotalEnergyVA                       .1.8.1.1.57

--  config                                           .2
--    eventSensorBasics                              .2.1
--      esNumberEventSensors                         .2.1.1
--      esTable                                      .2.1.2
--        esEntry                                    .2.1.2.1
--          esIndex                                  .2.1.2.1.1
--          esName                                   .2.1.2.1.2
--          esID                                     .2.1.2.1.3
--          esNumberTempSensors                      .2.1.2.1.4
--          esTempReportingMode                      .2.1.2.1.5
--          esNumberCCs                              .2.1.2.1.6
--          esCCReportingMode                        .2.1.2.1.7
--          esNumberHumidSensors                     .2.1.2.1.8
--          esHumidReportingMode                     .2.1.2.1.9
--          esNumberNoiseSensors                     .2.1.2.1.10
--          esNoiseReportingMode                     .2.1.2.1.11
--          esNumberAirflowSensors                   .2.1.2.1.12
--          esAirflowReportingMode                   .2.1.2.1.13
--          esNumberAnalog                           .2.1.2.1.14
--          esAnalogReportingMode                    .2.1.2.1.15
--          esNumberOutputs                          .2.1.2.1.16
--          esOutputReportingMode                    .2.1.2.1.17
--          esTempCombinedStatus                     .2.1.2.1.18
--          esCCCombinedStatusBlock1                 .2.1.2.1.19
--          esCCCombinedStatusBlock2                 .2.1.2.1.20
--          esCCCombinedStatusBlock3                 .2.1.2.1.21
--          esCCCombinedStatusBlock4                 .2.1.2.1.22
--          esCCCombinedStatusBlock5                 .2.1.2.1.23
--          esCCCombinedStatusBlock6                 .2.1.2.1.24
--          esCCCombinedStatusBlock7                 .2.1.2.1.25
--          esCCCombinedStatusBlock8                 .2.1.2.1.26
--          esHumidCombinedStatus                    .2.1.2.1.27
--          esAnalogCombinedStatusBlock1             .2.1.2.1.28
--          esAnalogCombinedStatusBlock2             .2.1.2.1.29
--          esAnalogCombinedStatusBlock3             .2.1.2.1.30
--          esAnalogCombinedStatusBlock4             .2.1.2.1.31
--          esAnalogCombinedStatusBlock5             .2.1.2.1.32
--          esAnalogCombinedStatusBlock6             .2.1.2.1.33
--          esOutputCombinedStatusBlock1             .2.1.2.1.34
--          esOutputCombinedStatusBlock2             .2.1.2.1.35
--        esNewSensors                               .2.1.3
--    eventSensorPointConfig                         .2.2
--      esPointConfigTempTable                       .2.2.1
--        esPointConfigTempEntry                     .2.2.1.1
--          espcTempIndexES                          .2.2.1.1.1
--          espcTempIndexPoint                       .2.2.1.1.2
--          espcTempEnable                           .2.2.1.1.3
--          espcTempScale                            .2.2.1.1.4
--          espcTempDeadband                         .2.2.1.1.5
--          espcTempVHighTemp                        .2.2.1.1.6
--          espcTempVHighActions                     .2.2.1.1.7
--          espcTempVHighTrapnum                     .2.2.1.1.8
--          espcTempVHighClass                       .2.2.1.1.9
--          espcTempHighTemp                         .2.2.1.1.10
--          espcTempHighActions                      .2.2.1.1.11
--          espcTempHighTrapnum                      .2.2.1.1.12
--          espcTempHighClass                        .2.2.1.1.13
--          espcTempNormalActions                    .2.2.1.1.14
--          espcTempNormalTrapnum                    .2.2.1.1.15
--          espcTempNormalClass                      .2.2.1.1.16
--          espcTempLowTemp                          .2.2.1.1.17
--          espcTempLowActions                       .2.2.1.1.18
--          espcTempLowTrapnum                       .2.2.1.1.19
--          espcTempLowClass                         .2.2.1.1.20
--          espcTempVLowTemp                         .2.2.1.1.21
--          espcTempVLowActions                      .2.2.1.1.22
--          espcTempVLowTrapnum                      .2.2.1.1.23
--          espcTempVLowClass                        .2.2.1.1.24
--      esPointConfigCCTable                         .2.2.2
--        esPointConfigCCEntry                       .2.2.2.1
--          espcCCIndexES                            .2.2.2.1.1
--          espcCCIndexPoint                         .2.2.2.1.2
--          espcCCEnable                             .2.2.2.1.3
--          espcCCName                               .2.2.2.1.4
--          espcCCEventState                         .2.2.2.1.5
--          espcCCThreshold                          .2.2.2.1.6
--          espcCCEventActions                       .2.2.2.1.7
--          espcCCEventTrapnum                       .2.2.2.1.8
--          espcCCEventClass                         .2.2.2.1.9
--          espcCCNormalActions                      .2.2.2.1.10
--          espcCCNormalTrapnum                      .2.2.2.1.11
--          espcCCNormalClass                        .2.2.2.1.12
--          espcCCAlarmAlias                         .2.2.2.1.13
--          espcCCNormalAlias                        .2.2.2.1.14
--          espcCCNormalThreshold                    .2.2.2.1.15
--          espcCCOverrideGlobalReminder             .2.2.2.1.16
--          espcCCReminderInterval                   .2.2.2.1.17
--      esPointConfigHumidTable                      .2.2.3
--        esPointConfigHumidEntry                    .2.2.3.1
--          espcHumidIndexES                         .2.2.3.1.1
--          espcHumidIndexPoint                      .2.2.3.1.2
--          espcHumidEnable                          .2.2.3.1.3
--          espcHumidDeadband                        .2.2.3.1.4
--          espcHumidVHighHumid                      .2.2.3.1.5
--          espcHumidVHighActions                    .2.2.3.1.6
--          espcHumidVHighTrapnum                    .2.2.3.1.7
--          espcHumidVHighClass                      .2.2.3.1.8
--          espcHumidHighHumid                       .2.2.3.1.9
--          espcHumidHighActions                     .2.2.3.1.10
--          espcHumidHighTrapnum                     .2.2.3.1.11
--          espcHumidHighClass                       .2.2.3.1.12
--          espcHumidNormalActions                   .2.2.3.1.13
--          espcHumidNormalTrapnum                   .2.2.3.1.14
--          espcHumidNormalClass                     .2.2.3.1.15
--          espcHumidLowHumid                        .2.2.3.1.16
--          espcHumidLowActions                      .2.2.3.1.17
--          espcHumidLowTrapnum                      .2.2.3.1.18
--          espcHumidLowClass                        .2.2.3.1.19
--          espcHumidVLowHumid                       .2.2.3.1.20
--          espcHumidVLowActions                     .2.2.3.1.21
--          espcHumidVLowTrapnum                     .2.2.3.1.22
--          espcHumidVLowClass                       .2.2.3.1.23
--      esPointConfigAITable                         .2.2.5
--        esPointConfigAIEntry                       .2.2.5.1
--          espcAIIndexES                            .2.2.5.1.1
--          espcAIIndexPoint                         .2.2.5.1.2
--          espcAIEnable                             .2.2.5.1.3
--          espcAIDeadband                           .2.2.5.1.5
--          espcAIVhighValue                         .2.2.5.1.6
--          espcAIVhighActions                       .2.2.5.1.7
--          espcAIVhighTrapnum                       .2.2.5.1.8
--          espcAIVhighClass                         .2.2.5.1.9
--          espcAIHighValue                          .2.2.5.1.10
--          espcAIHighActions                        .2.2.5.1.11
--          espcAIHighTrapnum                        .2.2.5.1.12
--          espcAIHighClass                          .2.2.5.1.13
--          espcAINormalActions                      .2.2.5.1.14
--          espcAINormalTrapnum                      .2.2.5.1.15
--          espcAINormalClass                        .2.2.5.1.16
--          espcAILowValue                           .2.2.5.1.17
--          espcAILowActions                         .2.2.5.1.18
--          espcAILowTrapnum                         .2.2.5.1.19
--          espcAILowClass                           .2.2.5.1.20
--          espcAIVlowValue                          .2.2.5.1.21
--          espcAIVlowActions                        .2.2.5.1.22
--          espcAIVlowTrapnum                        .2.2.5.1.23
--          espcAIVlowClass                          .2.2.5.1.24
--          espcAIConvUnitName                       .2.2.5.1.25
--          espcAIConvHighValue                      .2.2.5.1.26
--          espcAIConvHighUnit                       .2.2.5.1.27
--          espcAIConvLowValue                       .2.2.5.1.29
--          espcAIConvLowUnit                        .2.2.5.1.30
--          espcAIDisplayFormat                      .2.2.5.1.32
--      esPointConfigOutputTable                     .2.2.6
--        esPointConfigOutputEntry                   .*******
--          espcOutputIndexES                        .*******.1
--          espcOutputIndexPoint                     .*******.2
--          espcOutputEnable                         .*******.3
--          espcOutputActiveState                    .*******.4
--          espcOutputType                           .*******.5
--          espcOutputAliasValue                     .*******.6
--          espcOutputAliasColor                     .*******.7
--          espcOutputActiveAlias                    .*******.10
--          espcOutputActiveColor                    .*******.11
--          espcOutputActiveActions                  .*******.12
--          espcOutputActiveTrapnum                  .*******.13
--          espcOutputActiveClass                    .*******.14
--          espcOutputInactiveAlias                  .*******.20
--          espcOutputInactiveColor                  .*******.21
--          espcOutputInactiveActions                .*******.22
--          espcOutputInactiveTrapnum                .*******.23
--          espcOutputInactiveClass                  .*******.24
--    serialPorts                                    .2.3
--      numberPorts                                  .2.3.1
--      portConfigTable                              .2.3.2
--        portConfigEntry                            .*******
--          portConfigIndex                          .*******.1
--          portConfigBaud                           .*******.2
--          portConfigDataFormat                     .*******.3
--          portConfigStripPtOutputLfs               .*******.4
--          portConfigStripPtInputLfs                .*******.5
--          portConfigMaskEnable                     .*******.7
--          portConfigDAEnable                       .*******.8
--          portConfigStoreAlarmsDPT                 .*******.9
--          portConfigRecordTimeout                  .*******.10
--          portConfigDataType                       .*******.11
--          portConfigEtxToCRLF                      .*******.12
--          portConfigMLREnable                      .*******.13
--          portConfigMLRStartField1Pos              .*******.14
--          portConfigMLRStartField1Text             .*******.15
--          portConfigMLRStartField2Pos              .*******.16
--          portConfigMLRStartField2Text             .*******.17
--          portConfigMLRNumLinesBefore              .*******.18
--          portConfigMLREndDetection                .*******.19
--          portConfigMLRLineCount                   .*******.20
--          portConfigMLREndField1Pos                .*******.21
--          portConfigMLREndField1Text               .*******.22
--          portConfigMLREndField2Pos                .*******.23
--          portConfigMLREndField2Text               .*******.24
--          portConfigMLRUseComplexRules             .*******.25
--          portConfigBufferPT                       .*******.26
--          portConfigId                             .*******.27
--          portConfigMode                           .*******.28
--          portConfigHsk                            .*******.29
--    network                                        .2.4
--      interface                                    .2.4.1
--        ethernet                                   .*******
--          ethernet1                                .*******.1
--            eth1Mode                               .*******.1.1
--            eth1Address                            .*******.1.2
--            eth1SubnetMask                         .*******.1.3
--            eth1Router                             .*******.1.4
--              eth1MAC                              .*******.1.6
--          ethernet2                                .*******.2
--            eth2Mode                               .*******.2.1
--            eth2Address                            .*******.2.2
--            eth2SubnetMask                         .*******.2.3
--            eth2Router                             .*******.2.4
--              eth2MAC                              .*******.2.6
--      defaultRouter                                .2.4.2
--      dnsTable                                     .2.4.3
--        dnsEntry                                   .*******
--          dnsIndex                                 .*******.1
--          dnsAddress                               .*******.2
--      hostname                                     .2.4.4
--      hostTable                                    .2.4.5
--        hostEntry                                  .*******
--          hostIndex                                .*******.1
--          hostDeclaration                          .*******.2
--      ncpDuplex                                    .2.4.6
--      ncpTimeout                                   .2.4.7
--      snmp                                         .2.4.8
--        snmpAgentEnable                            .*******
--        snmpNotificationCaptureEnable              .*******
--        snmpNotificationCaptureFile                .*******
--        snmpNotificationTx                         .*******
--          snmpNtfnAttempts                         .*******.1
--          snmpNtfnTimeout                          .*******.2
--          snmpNtfnCycles                           .*******.3
--          snmpNtfnSnooze                           .*******.4
--      ftpPush                                      .2.4.9
--        ftpPushEnable                              .*******
--        ftpPushServer                              .*******
--        ftpPushAccount                             .*******
--        ftpPushDirectory                           .2.4.9.6
--        ftpPushperiod                              .2.4.9.7
--        ftpPushPushFileTable                       .2.4.9.8
--          ftpPushPushFileEntry                     .2.4.9.8.1
--            ftpPushPushFileIndex                   .2.4.9.8.1.1
--            ftpPushPushFile                        .2.4.9.8.1.2
--        ftpPushPushAudit                           .2.4.9.9
--        ftpPushPushAlarms                          .*******0
--        ftpPushRemoteFileTable                     .*******1
--          ftpPushRemoteFileEntry                   .*******1.1
--            ftpPushRemoteFileIndex                 .*******1.1.1
--            ftpPushRemoteFileName                  .*******1.1.2
--        ftpPushRemoteAlarmName                     .*******2
--        ftpPushPassive                             .*******3
--        ftpPushIncludeDate                         .*******4
--        ftpPushIncludeTime                         .*******5
--        ftpPushIncludeSeq                          .*******6
--        ftpPushPermissions                         .*******7
--      ppp                                          .2.4.10
--        pppDial                                    .********
--          pppDialEnable                            .********.1
--          pppDialNumber                            .********.2
--          pppDialIdleTimeout                       .********.5
--          pppDialRetries                           .********.6
--          pppDialCDTimeout                         .********.7
--          pppDialLoginTimeout                      .********.8
--          pppDialMdmInit                           .********.9
--          pppDialSuggestIP                         .********.10
--          pppDialPlainLogin                        .********.11
--        pppHost                                    .********
--          pppHostEnable                            .********.1
--          pppHostIdleTimeout                       .********.2
--          pppHostLocalIP                           .********.3
--          pppHostRemoteIP                          .********.4
--      routing                                      .2.4.11
--        pppRoutingEnable                           .********
--        routingInterface                           .********
--      netSecurity                                  .2.4.12
--        ipRestriction                              .********
--          ipRestrictionTable                       .********.1
--            ipRestrictionEntry                     .2.4.********
--              ipRestrictionIndex                   .2.4.********.1
--              ipRestrictionEnable                  .2.4.********.2
--              ipRestrictionMask                    .2.4.********.3
--      rts                                          .2.4.13
--        rtsFileTable                               .********
--          rtsFileEntry                             .********.1
--            rtsFileIndex                           .********.1.1
--            rtsFileMode                            .********.1.2
--            rtsFileShowAnswer                      .********.1.3
--            rtsFileReqXON                          .********.1.4
--            rtsFileTimeout                         .********.1.5
--            rtsFileEmptyClose                      .********.1.6
--            rtsFilePushHost                        .********.1.7
--            rtsFilePushPort                        .********.1.8
--            rtsFilePushRetryTimer                  .********.1.9
--        rtsAlarms                                  .********
--          rtsAlarmsMode                            .********.1
--          rtsAlarmsShowAnswer                      .********.2
--          rtsAlarmsReqXON                          .********.3
--          rtsAlarmsTimeout                         .********.4
--          rtsAlarmsEmptyClose                      .********.5
--          rtsAlarmsPushHost                        .********.6
--          rtsAlarmsPushPort                        .********.7
--          rtsAlarmsPushRetryTimer                  .********.8
--      trap                                         .2.4.14
--        trapInclude                                .********
--          trapIncludeDateTime                      .********.1
--          trapIncludeSiteName                      .********.2
--          trapIncludeSensorID                      .********.3
--          trapIncludeUDName                        .********.4
--          trapIncludeUDState                       .********.5
--          trapIncludeSourceAddress                 .********.6
--        trapAuthFailEnable                         .********
--      routeTest                                    .2.4.15
--        routeTestEnable                            .********
--        routeTestPeriod                            .********
--        routeTestAddressTable                      .********
--          routeTestAddressEntry                    .********.1
--            routeTestAddressIndex                  .********.1.1
--            routeTestAddress                       .********.1.2
--      wireless                                     .2.4.16
--        wirelessMode                               .********
--        wirelessAPN                                .********
--        wirelessIdleTimeout                        .********
--        wirelessDRE                                .********
--        wirelessPPPUsername                        .********0
--        wirelessFirewall                           .********1
--        wirelessKeepaliveThreshold                 .********2
--        wirelessPPPDebug                           .********3
--        wirelessConnectivity                       .********0
--          wirelessConnEnable                       .********0.1
--          wirelessConnCheckInterval                .********0.2
--          wirelessConnFailThreshold                .********0.3
--          wirelessConnIP1                          .********0.10
--          wirelessConnIP2                          .********0.11
--      email                                        .2.4.17
--        emailServer                                .********
--        emailDomain                                .********
--        emailAuthEnable                            .********
--      netAdvanced                                  .2.4.18
--        arpFilter                                  .********
--      web                                          .2.4.19
--        webEnable                                  .********
--        webPort                                    .********
--        webTimeout                                 .********
--      ethExpan                                     .2.4.20
--        ethExpanIp                                 .********
--        ethExpanMask                               .********
--        ethExpanCardTable                          .********
--          ethExpanCardEntry                        .********.1
--            ethExpanCardIndex                      .********.1.1
--            ethExpanCardMAC                        .********.1.2
--        ethExpanDHCP                               .********
--          ethExpanDHCPStartIp                      .********.1
--          ethExpanDHCPLeaseTime                    .********.2
--      cpe                                          .2.4.22
--        cpeTable                                   .********
--          cpeEntry                                 .********.1
--            cpeIndex                               .********.1.1
--            cpeHost                                .********.1.2
--            cpeName                                .********.1.3
--            cpeDescription                         .********.1.4
--            cpeKeepalive                           .********.1.5
--            cpeThreshold                           .********.1.6
--            cpeEventReminderInterval               .********.1.7
--            cpeKeepaliveTicks                      .********.1.20
--            cpePingSize                            .********.1.21
--            cpeInfoReset                           .********.1.30
--            cpeInfoNumReq                          .********.1.31
--            cpeInfoNumGoodResp                     .********.1.32
--            cpeInfoNumBadResp                      .********.1.33
--            cpeInfoNumLostResp                     .********.1.34
--            cpeInfoPercentLoss                     .********.1.35
--            cpeInfoPercentBad                      .********.1.36
--    modem                                          .2.5
--      modemFormat                                  .2.5.1
--      modemInitString                              .2.5.2
--      modemCallAttempts                            .2.5.4
--      modemCallDelay                               .2.5.5
--      modemCmdTimout                               .2.5.6
--      modemGoto                                    .2.5.7
--      modemClid                                    .2.5.8
--        modemCLIDEnable                            .2.5.8.1
--        modemCLIDNumberTable                       .2.5.8.2
--          modemCLIDNumberEntry                     .2.5.8.2.1
--            modemCLIDNumberIndex                   .2.5.8.2.1.1
--            modemCLIDNumber                        .2.5.8.2.1.2
--      modemTAPInitString                           .2.5.9
--      modemTAP8N1                                  .2.5.10
--    time                                           .2.8
--      clock                                        .2.8.1
--    console                                        .2.10
--      consoleDuplex                                .2.10.1
--      consoleTimeout                               .2.10.2
--      consoleConfirm                               .2.10.7
--    unitSecurity                                   .2.11
--      secCore                                      .2.11.1
--        scShowPasswordPrompt                       .2.11.1.1
--        scConsoleLoginRequired                     .2.11.1.2
--        scModemLoginRequired                       .2.11.1.3
--        scTelnetLoginRequired                      .2.11.1.4
--        scTelnetPTLoginRequired                    .2.11.1.5
--        scRTSLoginRequired                         .2.11.1.6
--        scAuthMode                                 .2.11.1.7
--        scRightsGroup                              .2.11.1.8
--      secUserTable                                 .2.11.2
--        secUserEntry                               .2.11.2.1
--          secUserIndex                             .2.11.2.1.1
--          secUserEnable                            .2.11.2.1.2
--          secUserConnectVia                        .2.11.2.1.5
--          secUserLoginTo                           .2.11.2.1.6
--          secUserAccessFile                        .2.11.2.1.7
--          secUserPTEscapeTo                        .2.11.2.1.9
--          secUserPPPType                           .2.11.2.1.10
--          secUserRights                            .2.11.2.1.11
--          secUserEventsReadAccess                  .2.11.2.1.13
--          secUserAuditReadAccess                   .2.11.2.1.14
--          secUserEventsWriteAccess                 .2.11.2.1.16
--          secUserAuditWriteAccess                  .2.11.2.1.17
--          secUserExpiration                        .2.11.2.1.18
--          secUserCallbackNumber1                   .2.11.2.1.19
--          secUserCallbackNumber2                   .2.11.2.1.20
--          secUserCallbackNumber3                   .2.11.2.1.21
--          secUserChallengeTelnetMode               .2.11.2.1.22
--          secUserChallengeModemMode                .2.11.2.1.23
--          secUserChallengeConsoleMode              .2.11.2.1.24
--          secUserChallengeTelnetSendTo             .2.11.2.1.25
--          secUserChallengeModemSendTo              .2.11.2.1.26
--          secUserChallengeExpiration               .2.11.2.1.27
--      secFactory                                   .2.11.3
--        sfEnable                                   .2.11.3.1
--        sfSecret                                   .2.11.3.2
--      secVPN                                       .2.11.5
--    event                                          .2.12
--      evCore                                       .2.12.1
--        evClassNameTable                           .2.12.1.1
--          evClassNameEntry                         .2.********
--            evClassNameIndex                       .2.********.1
--            evClassName                            .2.********.2
--        evReminderInterval                         .2.12.1.2
--        evLog                                      .2.12.1.3
--          evLogEnable                              .2.12.1.3.1
--          evLogStoreAlarm                          .2.12.1.3.2
--          evLogMaxSize                             .2.12.1.3.3
--          evLogStoreSensor                         .2.12.1.3.4
--          evLogTimeStampAlarms                     .2.12.1.3.5
--          evLogPrependName                         .2.12.1.3.6
--        evMgmt                                     .2.12.1.4
--          evMgmtReprocess                          .2.12.1.4.3
--      evData                                       .2.12.2
--        evdCore                                    .2.12.2.1
--          evdExitOnTrue                            .2.********
--          evdFilterAction                          .2.********
--          evdWildCardChar                          .2.********
--        evdTable                                   .2.12.2.2
--          evdEntry                                 .2.********
--            evdIndex                               .2.********.1
--            evdEnable                              .2.********.2
--            evdName                                .2.********.3
--            evdEquation                            .2.********.4
--            evdThreshold                           .2.********.5
--            evdAutoclear                           .2.********.6
--            evdClearInterval                       .2.********.7
--            evdClearTime                           .2.********.8
--            evdActions                             .2.********.9
--            evdClass                               .2.********.10
--            evdTrapNum                             .2.********.11
--            evdMode                                .2.********.12
--        evdFieldTable                              .2.12.2.3
--          evdFieldEntry                            .2.********
--            evdFieldIndex                          .2.********.1
--            evdFieldStart                          .2.********.2
--            evdFieldLength                         .2.********.3
--            evdFieldName                           .2.********.4
--            evdFieldLine                           .2.********.5
--            evdFieldType                           .2.********.6
--        evdMacroTable                              .2.12.2.4
--          evdMacroEntry                            .2.********
--            evdMacroIndex                          .2.********.1
--            evdMacroName                           .2.********.2
--            evdMacroEquation                       .2.********.3
--      evNoData1                                    .2.12.3
--        evNoData1Enable                            .2.12.3.1
--        evNoData1Actions                           .2.12.3.2
--        evNoData1Message                           .2.12.3.3
--        evNoData1TrapNum                           .2.12.3.4
--        evNoData1Class                             .2.12.3.5
--        evNoData1Sched1Begin                       .2.12.3.6
--        evNoData1Sched1End                         .2.12.3.7
--        evNoData1Sched1Duration                    .2.12.3.8
--        evNoData1Sched2Begin                       .2.12.3.9
--        evNoData1Sched2End                         .2.12.3.10
--        evNoData1Sched2Duration                    .2.12.3.11
--        evNoData1PortEnableTable                   .2.12.3.12
--          evNoData1PortEnableEntry                 .2.12.3.12.1
--            evNoData1PortEnableIndex               .2.12.3.12.1.1
--            evNoData1PortEnable                    .2.12.3.12.1.2
--        evNoData1Exclusions                        .2.12.3.13
--        evNoData1Days                              .2.12.3.14
--      evNoData2                                    .2.12.4
--        evNoData2Enable                            .2.12.4.1
--        evNoData2Actions                           .2.12.4.2
--        evNoData2Message                           .2.12.4.3
--        evNoData2TrapNum                           .2.12.4.4
--        evNoData2Class                             .2.12.4.5
--        evNoData2Sched1Begin                       .2.12.4.6
--        evNoData2Sched1End                         .2.12.4.7
--        evNoData2Sched1Duration                    .2.12.4.8
--        evNoData2Sched2Begin                       .2.12.4.9
--        evNoData2Sched2End                         .2.12.4.10
--        evNoData2Sched2Duration                    .2.12.4.11
--        evNoData2PortEnableTable                   .2.12.4.12
--          evNoData2PortEnableEntry                 .2.1********
--            evNoData2PortEnableIndex               .2.1********.1
--            evNoData2PortEnable                    .2.1********.2
--        evNoData2Exclusions                        .2.12.4.13
--        evNoData2Days                              .2.12.4.14
--      evSched1                                     .2.12.5
--        evSched1Enable                             .2.12.5.1
--        evSched1Actions                            .2.12.5.2
--        evSched1Message                            .2.12.5.3
--        evSched1TrapNum                            .2.12.5.4
--        evSched1Class                              .2.12.5.5
--        evSched1Sunday                             .2.12.5.6
--        evSched1Monday                             .2.12.5.7
--        evSched1Tuesday                            .2.12.5.8
--        evSched1Wednesday                          .2.12.5.9
--        evSched1Thursday                           .2.12.5.10
--        evSched1Friday                             .2.12.5.11
--        evSched1Saturday                           .2.12.5.12
--        evSched1Exclusions                         .2.12.5.13
--      evSched2                                     .2.12.6
--        evSched2Enable                             .2.12.6.1
--        evSched2Actions                            .2.12.6.2
--        evSched2Message                            .2.12.6.3
--        evSched2TrapNum                            .2.12.6.4
--        evSched2Class                              .2.12.6.5
--        evSched2Sunday                             .2.12.6.6
--        evSched2Monday                             .2.12.6.7
--        evSched2Tuesday                            .2.12.6.8
--        evSched2Wednesday                          .2.12.6.9
--        evSched2Thursday                           .2.12.6.10
--        evSched2Friday                             .2.12.6.11
--        evSched2Saturday                           .2.12.6.12
--        evSched2Exclusions                         .2.12.6.13
--      evShskLowTable                               .2.12.7
--        evShskLowEntry                             .2.12.7.1
--          evShskLowIndex                           .2.12.7.1.1
--          evShskLowEnable                          .2.12.7.1.2
--          evShskLowActions                         .2.12.7.1.3
--          evShskLowMessage                         .2.12.7.1.4
--          evShskLowClass                           .2.12.7.1.5
--          evShskLowTrapNum                         .2.12.7.1.6
--      evShskHighTable                              .2.12.8
--        evShskHighEntry                            .2.12.8.1
--          evShskHighIndex                          .2.12.8.1.1
--          evShskHighEnable                         .2.12.8.1.2
--          evShskHighActions                        .2.12.8.1.3
--          evShskHighMessage                        .2.12.8.1.4
--          evShskHighClass                          .2.12.8.1.5
--          evShskHighTrapNum                        .2.12.8.1.6
--      evNoSensor                                   .2.12.9
--        evNoSensorTimeout                          .2.12.9.1
--        evNoSensorActions                          .2.12.9.2
--        evNoSensorTrapNum                          .2.12.9.3
--        evNoSensorClass                            .2.12.9.4
--      fuelSensor                                   .2.12.11
--        fuelSensorGeneralTable                     .2.12.11.1
--          fsGenEntry                               .2.12.11.1.1
--            fsGenIndex                             .2.12.1*******
--            fsGenName                              .2.12.1*******
--            fsGenSensorType                        .2.12.1*******
--            fsGenDistanceUnit                      .2.12.11.1.1.4
--            fsGenRawValueTop                       .2.12.11.1.1.5
--            fsGenTopOffset                         .2.12.11.1.1.6
--            fsGenRawValueBottom                    .2.12.11.1.1.7
--            fsGenBottomOffset                      .2.12.11.1.1.8
--            fsGenInputES                           .2.12.11.1.1.9
--            fsGenInputPoint                        .2.12.1*******0
--            fsGenFilterAveraging                   .2.12.1*******1
--            fsGenSysrepEnable                      .2.12.1*******2
--            fsGenSysrepThreshold                   .2.12.1*******3
--            fsGenSysrepLimit                       .2.12.1*******4
--            fsGenSysrepPackage                     .2.12.1*******5
--            fsGenSysrepType                        .2.12.1*******6
--            fsGenEnable                            .2.12.1*******7
--            fsGenFuelType                          .2.12.1*******8
--        fuelSensorTankTable                        .2.12.11.2
--          fsTankEntry                              .2.12.11.2.1
--            fsTankIndex                            .2.12.11.2.1.1
--            fsTankHeight                           .2.12.11.2.1.2
--            fsTankDimA                             .2.12.11.2.1.3
--            fsTankDimB                             .2.12.11.2.1.4
--            fsTankVolume                           .2.12.11.2.1.5
--            fsTankVolumeUnit                       .2.12.11.2.1.6
--            fsTankShape                            .2.12.11.2.1.7
--        fuelSensorCustomTankTable                  .*********
--          fsCustomTankEntry                        .*********.1
--            fsCustomTankIndexFS                    .*********.1.1
--            fsCustomTankIndexDatum                 .*********.1.2
--            fsCustomTankHeight                     .*********.1.3
--            fsCustomTankVolume                     .*********.1.4
--        fuelSensorVolumeTable                      .*********
--          fsVolumeEntry                            .*********.1
--            fsVolumeIndex                          .*********.1.1
--            fsVolumeEnable                         .*********.1.2
--            fsVolumeDeadband                       .*********.1.3
--            fsVolumeVHighValue                     .*********.1.4
--            fsVolumeVHighActions                   .*********.1.5
--            fsVolumeVHighTrapNum                   .*********.1.6
--            fsVolumeVHighClass                     .*********.1.7
--            fsVolumeHighValue                      .*********.1.8
--            fsVolumeHighActions                    .*********.1.9
--            fsVolumeHighTrapNum                    .*********.1.10
--            fsVolumeHighClass                      .*********.1.11
--            fsVolumeNormalActions                  .*********.1.12
--            fsVolumeNormalTrapNum                  .*********.1.13
--            fsVolumeNormalClass                    .*********.1.14
--            fsVolumeLowValue                       .*********.1.15
--            fsVolumeLowActions                     .*********.1.16
--            fsVolumeLowTrapNum                     .*********.1.17
--            fsVolumeLowClass                       .*********.1.18
--            fsVolumeVLowValue                      .*********.1.19
--            fsVolumeVLowActions                    .*********.1.20
--            fsVolumeVLowTrapNum                    .*********.1.21
--            fsVolumeVLowClass                      .*********.1.22
--        fuelSensorDisconnectTable                  .*********
--          fsDiscEntry                              .*********.1
--            fsDiscIndex                            .*********.1.1
--            fsDiscEnable                           .*********.1.2
--            fsDiscHighValue                        .*********.1.3
--            fsDiscLowValue                         .*********.1.4
--            fsDiscActions                          .*********.1.5
--            fsDiscTrapNum                          .*********.1.6
--            fsDiscClass                            .*********.1.7
--            fsDiscNormalActions                    .*********.1.8
--            fsDiscNormalTrapNum                    .*********.1.9
--            fsDiscNormalClass                      .*********.1.10
--      acPowerMonitor                               .2.12.12
--        acpmGeneralTable                           .*********
--          acpmGenEntry                             .*********.1
--            acpmGenIndex                           .*********.1.1
--            acpmGenDevice                          .*********.1.2
--            acpmGenName                            .*********.1.3
--            acpmGenAddress                         .*********.1.4
--            acpmGenPtRatio                         .*********.1.5
--            acpmGenCtRatio                         .*********.1.6
--            acpmGenPowerType                       .*********.1.7
--            acpmGenSysrepPackage                   .*********.1.8
--            acpmGenSysrepType                      .*********.1.9
--            acpmGenEnable                          .*********.1.10
--        acpmAvgVoltageTable                        .*********
--          acpmAvgVoltageEntry                      .*********.1
--            acpmAvgVoltageIndex                    .*********.1.1
--            acpmAvgVoltageEnable                   .*********.1.2
--            acpmAvgVoltageDeadband                 .*********.1.3
--            acpmAvgVoltageVHighValue               .*********.1.4
--            acpmAvgVoltageVHighActions             .*********.1.5
--            acpmAvgVoltageVHighTrapNum             .*********.1.6
--            acpmAvgVoltageVHighClass               .*********.1.7
--            acpmAvgVoltageHighValue                .*********.1.8
--            acpmAvgVoltageHighActions              .*********.1.9
--            acpmAvgVoltageHighTrapNum              .*********.1.10
--            acpmAvgVoltageHighClass                .*********.1.11
--            acpmAvgVoltageNormalActions            .*********.1.12
--            acpmAvgVoltageNormalTrapNum            .*********.1.13
--            acpmAvgVoltageNormalClass              .*********.1.14
--            acpmAvgVoltageLowValue                 .*********.1.15
--            acpmAvgVoltageLowActions               .*********.1.16
--            acpmAvgVoltageLowTrapNum               .*********.1.17
--            acpmAvgVoltageLowClass                 .*********.1.18
--            acpmAvgVoltageVLowValue                .*********.1.19
--            acpmAvgVoltageVLowActions              .*********.1.20
--            acpmAvgVoltageVLowTrapNum              .*********.1.21
--            acpmAvgVoltageVLowClass                .*********.1.22
--            acpmAvgVoltageSysrepEnable             .*********.1.23
--            acpmAvgVoltageSysrepThreshold          .*********.1.24
--            acpmAvgVoltageSysrepLimit              .*********.1.25
--        acpmAvgCurrentTable                        .2.12.12.3
--          acpmAvgCurrentEntry                      .2.12.12.3.1
--            acpmAvgCurrentIndex                    .2.12.12.3.1.1
--            acpmAvgCurrentEnable                   .2.12.12.3.1.2
--            acpmAvgCurrentDeadband                 .2.12.12.3.1.3
--            acpmAvgCurrentVHighValue               .2.12.12.3.1.4
--            acpmAvgCurrentVHighActions             .2.12.12.3.1.5
--            acpmAvgCurrentVHighTrapNum             .2.12.12.3.1.6
--            acpmAvgCurrentVHighClass               .2.12.12.3.1.7
--            acpmAvgCurrentHighValue                .2.12.12.3.1.8
--            acpmAvgCurrentHighActions              .2.12.12.3.1.9
--            acpmAvgCurrentHighTrapNum              .2.12.12.3.1.10
--            acpmAvgCurrentHighClass                .2.12.12.3.1.11
--            acpmAvgCurrentNormalActions            .2.12.12.3.1.12
--            acpmAvgCurrentNormalTrapNum            .2.12.12.3.1.13
--            acpmAvgCurrentNormalClass              .2.12.12.3.1.14
--            acpmAvgCurrentLowValue                 .2.12.12.3.1.15
--            acpmAvgCurrentLowActions               .2.12.12.3.1.16
--            acpmAvgCurrentLowTrapNum               .2.12.12.3.1.17
--            acpmAvgCurrentLowClass                 .2.12.12.3.1.18
--            acpmAvgCurrentVLowValue                .2.12.12.3.1.19
--            acpmAvgCurrentVLowActions              .2.12.12.3.1.20
--            acpmAvgCurrentVLowTrapNum              .2.12.12.3.1.21
--            acpmAvgCurrentVLowClass                .2.12.12.3.1.22
--            acpmAvgCurrentSysrepEnable             .2.12.12.3.1.23
--            acpmAvgCurrentSysrepThreshold          .2.12.12.3.1.24
--            acpmAvgCurrentSysrepLimit              .2.12.12.3.1.25
--        acpmFreqTable                              .2.12.12.4
--          acpmFreqEntry                            .2.12.12.4.1
--            acpmFreqIndex                          .2.12.1*******
--            acpmFreqEnable                         .2.12.12.4.1.2
--            acpmFreqDeadband                       .2.12.12.4.1.3
--            acpmFreqVHighValue                     .2.12.12.4.1.4
--            acpmFreqVHighActions                   .2.12.12.4.1.5
--            acpmFreqVHighTrapNum                   .2.12.12.4.1.6
--            acpmFreqVHighClass                     .2.12.12.4.1.7
--            acpmFreqHighValue                      .2.12.12.4.1.8
--            acpmFreqHighActions                    .2.12.12.4.1.9
--            acpmFreqHighTrapNum                    .2.12.1*******0
--            acpmFreqHighClass                      .2.12.1*******1
--            acpmFreqNormalActions                  .2.12.1*******2
--            acpmFreqNormalTrapNum                  .2.12.1*******3
--            acpmFreqNormalClass                    .2.12.1*******4
--            acpmFreqLowValue                       .2.12.1*******5
--            acpmFreqLowActions                     .2.12.1*******6
--            acpmFreqLowTrapNum                     .2.12.1*******7
--            acpmFreqLowClass                       .2.12.1*******8
--            acpmFreqVLowValue                      .2.12.1*******9
--            acpmFreqVLowActions                    .2.12.12.4.1.20
--            acpmFreqVLowTrapNum                    .2.12.12.4.1.21
--            acpmFreqVLowClass                      .2.12.12.4.1.22
--            acpmFreqSysrepEnable                   .2.12.12.4.1.23
--            acpmFreqSysrepThreshold                .2.12.12.4.1.24
--            acpmFreqSysrepLimit                    .2.12.12.4.1.25
--        acpmTotalRealPowerTable                    .2.12.12.5
--          acpmTRPEntry                             .2.12.12.5.1
--            acpmTRPIndex                           .2.12.12.5.1.1
--            acpmTRPEnable                          .2.12.12.5.1.2
--            acpmTRPDeadband                        .2.12.12.5.1.3
--            acpmTRPVHighValue                      .2.12.12.5.1.4
--            acpmTRPVHighActions                    .2.12.12.5.1.5
--            acpmTRPVHighTrapNum                    .2.12.12.5.1.6
--            acpmTRPVHighClass                      .2.12.12.5.1.7
--            acpmTRPHighValue                       .2.12.12.5.1.8
--            acpmTRPHighActions                     .2.12.12.5.1.9
--            acpmTRPHighTrapNum                     .2.12.12.5.1.10
--            acpmTRPHighClass                       .2.12.12.5.1.11
--            acpmTRPNormalActions                   .2.12.12.5.1.12
--            acpmTRPNormalTrapNum                   .2.12.12.5.1.13
--            acpmTRPNormalClass                     .2.12.12.5.1.14
--            acpmTRPLowValue                        .2.12.12.5.1.15
--            acpmTRPLowActions                      .2.12.12.5.1.16
--            acpmTRPLowTrapNum                      .2.12.12.5.1.17
--            acpmTRPLowClass                        .2.12.12.5.1.18
--            acpmTRPVLowValue                       .2.12.12.5.1.19
--            acpmTRPVLowActions                     .2.12.12.5.1.20
--            acpmTRPVLowTrapNum                     .2.12.12.5.1.21
--            acpmTRPVLowClass                       .2.12.12.5.1.22
--            acpmTRPSysrepEnable                    .2.12.12.5.1.23
--            acpmTRPSysrepThreshold                 .2.12.12.5.1.24
--            acpmTRPSysrepLimit                     .2.12.12.5.1.25
--        acpmDisconnectTable                        .2.12.12.6
--          acpmDisconnectEntry                      .2.12.12.6.1
--            acpmDisconnectIndex                    .2.12.12.6.1.1
--            acpmDisconnectEnable                   .2.12.12.6.1.2
--            acpmDisconnectActions                  .2.12.12.6.1.3
--            acpmDisconnectTrapNum                  .2.12.12.6.1.4
--            acpmDisconnectClass                    .2.12.12.6.1.5
--            acpmDisconnectNormalActions            .2.12.12.6.1.6
--            acpmDisconnectNormalTrapNum            .2.12.12.6.1.7
--            acpmDisconnectNormalClass              .2.12.12.6.1.8
--        acpmTotalPowerFactorTable                  .2.12.12.7
--          acpmTPFEntry                             .2.12.12.7.1
--            acpmTPFIndex                           .2.12.12.7.1.1
--            acpmTPFEnable                          .2.12.12.7.1.2
--            acpmTPFDeadband                        .2.12.12.7.1.3
--            acpmTPFNormalActions                   .2.12.12.7.1.4
--            acpmTPFNormalTrapNum                   .2.12.12.7.1.5
--            acpmTPFNormalClass                     .2.12.12.7.1.6
--            acpmTPFLowValue                        .2.12.12.7.1.7
--            acpmTPFLowActions                      .2.12.12.7.1.8
--            acpmTPFLowTrapNum                      .2.12.12.7.1.9
--            acpmTPFLowClass                        .2.12.12.7.1.10
--            acpmTPFVLowValue                       .2.12.12.7.1.11
--            acpmTPFVLowActions                     .2.12.12.7.1.12
--            acpmTPFVLowTrapNum                     .2.12.12.7.1.13
--            acpmTPFVLowClass                       .2.12.12.7.1.14
--            acpmTPFSysrepEnable                    .2.12.12.7.1.15
--            acpmTPFSysrepThreshold                 .2.12.12.7.1.16
--            acpmTPFSysrepLimit                     .2.12.12.7.1.17
--      evReset                                      .2.12.16
--        evResetEnable                              .2.12.16.1
--        evResetDelay                               .2.12.16.2
--        evResetActions                             .2.12.16.3
--        evResetMessage                             .2.12.16.4
--        evResetTrapnum                             .2.12.16.5
--        evResetClass                               .2.12.16.6
--    action                                         .2.14
--      actionCallNumberTable                        .2.14.1
--        actionCallNumberEntry                      .2.14.1.1
--          actionCallNumberIndex                    .2.14.1.1.1
--          actionCallNumber                         .2.14.1.1.2
--      actionPagerTable                             .2.14.2
--        actionPagerEntry                           .2.14.2.1
--          actionPagerIndex                         .2.1*******
--          actionPagerType                          .2.1*******
--          actionPagerNumber                        .2.14.2.1.3
--          actionPagerID                            .2.14.2.1.4
--          actionPagerMessage                       .2.14.2.1.5
--          actionPagerPostCalloutDelay              .2.14.2.1.6
--          actionPagerPostIDDelay                   .2.14.2.1.7
--      actionSched                                  .2.14.3
--        actionSchedEnable                          .********
--        actionSchedBegin                           .********
--        actionSchedEnd                             .********
--        actionSchedWeekdaysOnly                    .********
--      actionAsentria                               .2.14.4
--        actionAsentriaRequireAck                   .********
--        actionAsentriaVersion                      .********
--        actionAsentriaTCPPort                      .********
--      actionHostTable                              .2.14.6
--        actionHostEntry                            .********
--          actionHostIndex                          .********.1
--          actionHost                               .********.2
--      actionEmailTable                             .2.14.7
--        actionEmailEntry                           .********
--          actionEmailIndex                         .********.1
--          actionEmail                              .********.2
--      actionParseError                             .2.14.8
--    sys                                            .2.16
--      sysTime                                      .2.16.1
--        sysTimeAutoDST                             .********
--        sysTimeGMTOffset                           .********
--        sysTimeGMTDirection                        .********
--        sysTimeNet                                 .********
--          sysTimeNetEnable                         .********.1
--          sysTimeNetHostTable                      .********.2
--            sysTimeNetHostEntry                    .********.2.1
--              sysTimeNetHostIndex                  .********.2.1.1
--              sysTimeNetHost                       .********.2.1.2
--      sysPT                                        .2.16.2
--        sysPTTimeout                               .********
--        sysPTEndPause                              .********
--        sysPTJoinable                              .********
--      sysMTU                                       .2.16.3
--      sysAnswerString                              .2.16.4
--      sysEventFileID                               .2.16.6
--      sysEscapeCharacter                           .2.16.7
--      sysTimeStamp                                 .2.16.8
--        sysTimeStampTimeFormat                     .********
--        sysTimeStampDateFormat                     .********
--        sysTimeStampSpaceAfter                     .********
--      sysLog                                       .2.16.9
--        sysLogMode                                 .********
--        sysLoghost                                 .********
--        sysLogFilter                               .********
--        sysLogFileSize                             .********
--        sysLogFileCount                            .********
--        sysLogListenPort                           .********
--      sysCRDB                                      .2.16.10
--        sysCRDBCapacity                            .*********
--        sysCRDBPercentFull                         .*********
--        sysCRDBFileIDTable                         .*********
--          sysCRDBFileIDEntry                       .*********.1
--            sysCRDBFileIDIndex                     .*********.1.1
--            sysCRDBFileID                          .*********.1.2
--        sysCRDBFileEnforceMinTable                 .*********
--          sysCRDBFileEnforceMinEntry               .*********.1
--            sysCRDBFileEnforceMinIndex             .*********.1.1
--            sysCRDBFileEnforceMin                  .*********.1.2
--      sysCharMask                                  .2.16.11
--      sysPrompt                                    .2.16.12
--      sysBootStatus                                .2.16.13
--      sysLoc                                       .2.16.14
--        sysLocLatitude                             .2.16.14.1
--        sysLocLongitude                            .2.16.14.2
--        sysLocXOffset                              .2.16.14.3
--        sysLocYOffset                              .2.16.14.4
--        sysLocAngle                                .2.16.14.5
--        sysLocAltitude                             .2.16.14.6
--      sysAssetMgmt                                 .2.16.15
--        sysAMManufacturer                          .2.16.15.1
--        sysAMProduct                               .2.16.15.2
--        sysAMSerialNumber                          .2.16.15.3
--        sysAMHardwareOptions                       .2.16.15.4
--        sysAMSoftwareVersion                       .2.16.15.5
--        sysAMSiteName                              .2.16.15.6
--    auditLog                                       .2.17
--      auditLogEnable                               .2.17.1
--      auditLogStoreResets                          .2.17.2
--      auditLogStoreCommands                        .2.17.3
--      auditLogStoreOutputs                         .2.17.4
--      auditLogStoreAlarmActions                    .2.17.5
--      auditLogStorePwdFailures                     .2.17.6
--      auditLogStoreLogins                          .2.17.7
--      auditLogStoreSHSK                            .2.17.8
--      auditLogStorePassthrough                     .2.17.9
--      auditLogStoreInactivity                      .2.17.10
--      auditLogStorePolling                         .2.17.11
--      auditLogMaxSize                              .2.17.12

--  productIds                                       .3
--    siteName                                        3.1
--    thisProduct                                    .3.2
--    stockTrapString                                .3.3
--    trapEventTypeNumber                            .3.4
--    trapEventTypeName                              .3.5
--    trapIncludedValue                              .3.6
--    trapIncludedString                             .3.7
--    trapTypeString                                 .3.8
--    trapEventClassNumber                           .3.9
--    trapEventClassName                             .3.10

--  keyInterface                                     .4.0


--***************************************************************************************
--TOP-LEVEL OBJECT IDENTIFIERS
--***************************************************************************************



    status OBJECT IDENTIFIER ::= { s530 1 }
    --*******.4.1.3052.12.1

        eventSensorStatus OBJECT IDENTIFIER ::= { status 1 }
        --*******.4.1.3052.12.1.1

        dataEventStatus OBJECT IDENTIFIER ::= { status 2 }
        --*******.4.1.3052.12.1.2

        powerDistributionStatus OBJECT IDENTIFIER ::= { status 5 }
        --*******.4.1.3052.12.1.5

            pdNextGen OBJECT IDENTIFIER ::= { powerDistributionStatus 4 }
            --*******.4.1.3052.********

            pdSystem OBJECT IDENTIFIER ::= { powerDistributionStatus 5 }
            --*******.4.1.3052.********

        fuelSensorStatus OBJECT IDENTIFIER ::= { status 6 }
        --*******.4.1.3052.12.1.6

        wirelessModemStatus OBJECT IDENTIFIER ::= { status 7 }
        --*******.4.1.3052.12.1.7

        acPowerMonitorStatus OBJECT IDENTIFIER ::= { status 8 }
        --*******.4.1.3052.12.1.8

    config OBJECT IDENTIFIER ::= { s530 2 }
    --*******.4.1.3052.12.2

        eventSensorBasics OBJECT IDENTIFIER ::= { config 1 }
        --*******.4.1.3052.12.2.1

        eventSensorPointConfig OBJECT IDENTIFIER ::= { config 2 }
        --*******.4.1.3052.12.2.2

        serialPorts OBJECT IDENTIFIER ::= { config 3 }
        --*******.4.1.3052.12.2.3

        network OBJECT IDENTIFIER ::= { config 4 }
        --*******.4.1.3052.12.2.4

            interface OBJECT IDENTIFIER ::= { network 1 }
            --*******.4.1.3052.12.2.4

                ethernet OBJECT IDENTIFIER ::= { interface 1 }
                --*******.4.1.3052.********

                    ethernet1 OBJECT IDENTIFIER ::= { ethernet 1 }
                    --*******.4.1.3052.********.1

                    ethernet2 OBJECT IDENTIFIER ::= { ethernet 2 }
                    --*******.4.1.3052.********.2

            snmp OBJECT IDENTIFIER ::= { network 8 }
            --*******.4.1.3052.********

                snmpNotificationTx OBJECT IDENTIFIER ::= { snmp 7 }
                --*******.4.1.3052.********.7

            ftpPush OBJECT IDENTIFIER ::= { network 9 }
            --*******.4.1.3052.********

            ppp OBJECT IDENTIFIER ::= { network 10 }
            --*******.4.1.3052.********0

                pppDial OBJECT IDENTIFIER ::= { ppp 1 }
                --*******.4.1.3052.********0.1

                pppHost OBJECT IDENTIFIER ::= { ppp 2 }
                --*******.4.1.3052.********0.2

            routing OBJECT IDENTIFIER ::= { network 11 }
            --*******.4.1.3052.********1

            netSecurity OBJECT IDENTIFIER ::= { network 12 }
            --*******.4.1.3052.********2

                ipRestriction OBJECT IDENTIFIER ::= { netSecurity 1 }
                --*******.4.1.3052.********2.1

            rts OBJECT IDENTIFIER ::= { network 13 }
            --*******.4.1.3052.********3

                rtsAlarms OBJECT IDENTIFIER ::= { rts 2 }
                --*******.4.1.3052.********3.2

            trap OBJECT IDENTIFIER ::= { network 14 }
            --*******.4.1.3052.********4

                trapInclude OBJECT IDENTIFIER ::= { trap 1}
                --*******.4.1.3052.********4.1

            routeTest OBJECT IDENTIFIER ::= { network 15 }
            --*******.4.1.3052.********5

            wireless OBJECT IDENTIFIER ::= { network 16 }
            --*******.4.1.3052.********6

                wirelessConnectivity OBJECT IDENTIFIER ::= { wireless 20 }
                --*******.4.1.3052.********6.20

            email OBJECT IDENTIFIER ::= { network 17 }
            --*******.4.1.3052.********7

            netAdvanced OBJECT IDENTIFIER ::= { network 18 }
            --*******.4.1.3052.********8

            web OBJECT IDENTIFIER ::= { network 19 }
            --*******.4.1.3052.********9

            ethExpan OBJECT IDENTIFIER ::= { network 20 }
            --*******.4.1.3052.********0

              ethExpanDHCP OBJECT IDENTIFIER ::= { ethExpan 6 }
              --*******.4.1.3052.********0.6

            ipv6 OBJECT IDENTIFIER ::= { network 21 }
            --*******.4.1.3052.********1

            cpe OBJECT IDENTIFIER ::= { network 22 }
            --*******.4.1.3052.*********

        modem OBJECT IDENTIFIER ::= { config 5 }
        --*******.4.1.3052.12.2.5

            modemClid OBJECT IDENTIFIER ::= { modem 8 }
            --*******.4.1.3052.********

        time OBJECT IDENTIFIER ::= { config 8 }
        --*******.4.1.3052.12.2.8

        console OBJECT IDENTIFIER ::= { config 10 }
        --*******.4.1.3052.12.2.10

        unitSecurity OBJECT IDENTIFIER ::= { config 11 }
        --*******.4.1.3052.12.2.11

            secCore OBJECT IDENTIFIER ::= { unitSecurity 1 }
            --*******.4.1.3052.*********

            secFactory OBJECT IDENTIFIER ::= { unitSecurity 3 }
            --*******.4.1.3052.*********

            secVPN OBJECT IDENTIFIER ::= { unitSecurity 5 }
            --*******.4.1.3052.*********

        event OBJECT IDENTIFIER ::= { config 12 }
        --*******.4.1.3052.12.2.12

            evCore OBJECT IDENTIFIER ::= { event 1 }
            --*******.4.1.3052.*********

                evLog OBJECT IDENTIFIER ::= { evCore 3 }
                --*******.4.1.3052.*********.3

                evMgmt OBJECT IDENTIFIER ::= { evCore 4 }
                --*******.4.1.3052.*********.4

            evData OBJECT IDENTIFIER ::= { event 2 }
            --*******.4.1.3052.*********

                evdCore OBJECT IDENTIFIER ::= { evData 1 }
                --*******.4.1.3052.*********.1

            evNoData1 OBJECT IDENTIFIER ::= { event 3 }
            --*******.4.1.3052.*********

            evNoData2 OBJECT IDENTIFIER ::= { event 4 }
            --*******.4.1.3052.*********

            evSched1 OBJECT IDENTIFIER ::= { event 5 }
            --*******.4.1.3052.*********

            evSched2 OBJECT IDENTIFIER ::= { event 6 }
            --*******.4.1.3052.*********

            evNoSensor OBJECT IDENTIFIER ::= { event 9 }
            --*******.4.1.3052.*********

            fuelSensor OBJECT IDENTIFIER ::= { event 11 }
            --*******.4.1.3052.**********

            acPowerMonitor OBJECT IDENTIFIER ::= { event 12 }
            --*******.4.1.3052.**********

            evReset OBJECT IDENTIFIER ::= { event 16 }
            --*******.4.1.3052.*********6

        action OBJECT IDENTIFIER ::= { config 14 }
        --*******.4.1.3052.12.2.14

            actionSched OBJECT IDENTIFIER ::= { action 3 }
            --*******.4.1.3052.*********

            actionAsentria OBJECT IDENTIFIER ::= { action 4 }
            --*******.4.1.3052.*********

        sys OBJECT IDENTIFIER ::= { config 16 }
        --*******.4.1.3052.12.2.16

            sysTime OBJECT IDENTIFIER ::= { sys 1 }
            --*******.4.1.3052.*********

                sysTimeNet OBJECT IDENTIFIER ::= { sysTime 4 }
                --*******.4.1.3052.12.********

            sysPT OBJECT IDENTIFIER ::= { sys 2 }
            --*******.4.1.3052.*********

            sysTimeStamp OBJECT IDENTIFIER ::= { sys 8 }
            --*******.4.1.3052.*********

            sysLog OBJECT IDENTIFIER ::= { sys 9 }
            --*******.4.1.3052.*********

            sysCRDB OBJECT IDENTIFIER ::= { sys 10 }
            --*******.4.1.3052.**********

            sysLoc OBJECT IDENTIFIER ::= { sys 14 }
            --*******.4.1.3052.**********

            sysAssetMgmt OBJECT IDENTIFIER ::= { sys 15 }
            --*******.4.1.3052.**********

        auditLog OBJECT IDENTIFIER ::= { config 17 }
        --*******.4.1.3052.12.2.17

    productIds OBJECT IDENTIFIER ::= { s530 3 }
    --*******.4.1.3052.12.3

--***************************************************************************************
--OBJECT DEFINITIONS
--***************************************************************************************

--***************************************************************************************
--EventSensor Status Section    1.1
--***************************************************************************************

esPointTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPoint
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "
        This table organizes 4 basic attributes of points.  A point
        is a particular sensor on an EventSensor (e.g., temperature,
        humidity, contact closure 2, output 5, etc.).  The 4 point
        attributes are its name, whether it's in its event state,
        the point's value as a number and its value as a string.

        Points are referenced by a point index.  The point index
        is a string of 3 numbers separated by periods.
        It contains all the information necessary for getting a piece
        of data off an event sensor; namely, which EventSensor,
        point class, and which-sensor-of-that-class (a.k.a. point).
        "
    ::= { eventSensorStatus 1 }
    --*******.4.1.3052.********

esPointEntry OBJECT-TYPE
    SYNTAX ESPoint
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point table"
    INDEX { esIndexES, esIndexPC, esIndexPoint }
    ::= { esPointTable 1 }
    --*******.4.1.3052.********.1

esIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point index is a string of 3 numbers separated
        by periods.  It contains all the information
        necessary for getting a piece of data off an event
        sensor; namely, which EventSensor, point class,
        and which-sensor-of-that-class (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.

        The number corresponding to an EventSensor is
        determined by the user at the initial configuration
        time.  If there is an internal sensor in the unit,
        it will always be the first item in the Sensor
        Events setup menu.

        For example, if a new EventSensor is configured as
        the 2nd entry AFTER the internal EventSensor (if
        one exists) in the Sensor Events Setup Menu, then
        that EventSensor will be known from then on as
        EventSensor 2.  All point indeces with esIndexES=2
        will now refer to that particular EventSensor.
        "
    ::= { esPointEntry 1 }
    --*******.4.1.3052.********.1.1

esIndexPC OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to point class.

        The point index is a string of 3 numbers separated by periods.
        It contains all the information necessary for getting a piece
        of data off an event sensor; namely, which EventSensor,
        point class, and which-sensor-of-that-class (a.k.a. point).

        The values for this object are:
        1=temperature sensor
        2=contact closure
        3=humidity sensor
        4=noise sensor
        5=analog input
        6=output (relay output or power output)
        "
    ::= { esPointEntry 2 }
    --*******.4.1.3052.********.1.2

esIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with esIndexPC (Point Class or type)
        and esIndexES (which EventSensor) uniquely defines each
        point (sensor or output) attached to a unit.  For
        example, if esIndexES is 3, esIndexPC is 1 and
        esIndexPoint is 4 then this is the Fourth Temperature
        Sensor on EventSensor number 3."
    ::= { esPointEntry 3 }
    --*******.4.1.3052.********.1.3

esPointName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The name of the point on an EventSensor. For example,
        'AC temp' (in the case of the temperature sensor on an
        EventSensor).

        There can be multiple points of the same point class on an
        EventSensor (e.g., 8 contact closures) and multiple sensor
        classes on an EventSensor (e.g., temperature, contact closure,
        humidity).

        If the point referenced by a given point index is solitary
        (e.g., temperature sensor, because there can be only one on
        an EventSensor), then this object reads as '<EventSensor
        name>'.  Setting this object for solitary point indices sets
        the EventSensor name only.

        On the other hand, if a given point is among other points of
        the same point class on an EventSensor, then this object reads
        as '<Sensor name>'.  Setting this object for these NON-
        solitary point indices sets the sensor name only - not the
        EventSensor name."
    ::= { esPointEntry 4 }
    --*******.4.1.3052.********.1.4

esPointInEventState OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A number referring to the event state of a point
        on an EventSensor.  This number can have different
        meanings depending on the point class.

        If the point class is temperature, humidity, or
        analog input, then the values of this object are:
        1=very low
        2=low
        3=normal
        4=high
        5=very high

        For contact closures the values of this object are:
        1=point in event state
        2=point in normal state

        For outputs the values of this object is the numeric
        code for the logical state of the output: 1 or 2.  The
        logical state of the output is either active/inactive for
        relay outputs or on/off for power outputs.  The logical
        state of the output has different nomenclature depending
        on the type of the output (relay output or power output):

        1=active (for relay output) or on (for power output)
        2=inactive (for relay output) or off (for power output)

        This is not necessarily representative of physical state
        of the output.  Physical state is energized/de-energized,
        for both relay and power outputs.  For relay outputs,
        the active logical state corresponds to either the energized
        physical state or the de-energized physical state depending
        on how the relay output is configured.  For power outputs,
        the on logical state always corresponds to the energized
        physical state.

        For any point class and any point, if the event
        state is undefined, then the value of this object
        is 0.

        For any point class except output, this object is read-only.
        For outputs, setting this object to 1 puts the output into
        its active logical state.

        Setting this object to 2 puts the output into its inactive
        logical state.

        Setting this object to any value greater than 2 puts the
        output into its active logical state for that many seconds
        (up to 3600), after which time it puts the output into its
        inactive logical state.
        "
    ::= { esPointEntry 5 }
    --*******.4.1.3052.********.1.5

esPointValueInt OBJECT-TYPE
    SYNTAX Integer32 (-2147483648..2147483647)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "A read-only number referring to the value of a
        point on an EventSensor.

        If the point class is temperature, then this object
        is the temperature in its configured scale (default
        is Fahrenheit).

        If the point class is contact closure, then
        this object is either 0 (open) or 1 (closed).

        If the point class is humidity, then this object
        is the percent relative humidity.

        If the point class is analog input, then this
        object is the signed input value in tenths of the
        configured units.

        If the point class is an output, then this object is
        the numeric code for the physical state of the output.
        0=de-energized
        1=energized

        This object cannot be used to control the output, i.e,
        change its logical or physical state (instead use
        the esPointInEventState object).
        "
    ::= { esPointEntry 6 }
    --*******.4.1.3052.********.1.6

esPointValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A string referring to the value of a point on an
        EventSensor.

        For contact closures this object is either 'Open'
        or 'Closed'.

        For outputs this object is a concatenation of the
        logical state and physical state, separated by '/'.

        For a relay output this object can be one of the
        following 4 possible values:

        Active/Energized
        Inactive/Energized
        Active/De-energized
        Inactve/De-energized

        For a power output this object can be one of the
        following 2 possible values:

        On/Energized
        Off/De-energized

        For temperature and humidity point classes, this
        object is the string representation of the
        esPointValueInt object.  For temperature, 'C' or
        'F' is including in the string to indicate scale.

        For analog inputs, this object is the string
        representation of the plus/minus input in the
        configured units appended with the unit name.
        "
    ::= { esPointEntry 7 }
    --*******.4.1.3052.********.1.7

esPointTimeLastChange OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time of the last time an event was
        triggerred for a point on an EventSensor in
        MM/DD/YY HH:MM:SS format.

        For contact closures, the possible events are
        Active and Inactive.  For temperature, humidity,
        and analog inputs, the possible events are Normal,
        High, Very High, Low, and Very Low.

        For outputs, this object is a null string."
    ::= { esPointEntry 8 }
    --*******.4.1.3052.********.1.8

esPointTimetickLastChange OBJECT-TYPE
    SYNTAX TimeTicks
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The time of the last time an event was
        triggerred for a point on an EventSensor in
        100ths of seconds since reset.

        For contact closures, the possible events are
        Active and Inactive.  For temperature, humidity,
        and analog inputs, the possible events are Normal,
        High, Very High, Low, and Very Low.

        For outputs, this object is 0."
    ::= { esPointEntry 9 }
    --*******.4.1.3052.********.1.9

esPointAliasValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "For CC inputs, this is the CC active/inactive alias setting
         setting value that currently corresponds to the event state.
         So if the CC input is in the event state, then this object
         is the active alias setting value.  If the CC input is in the
         normal state, then this object is the inactive alias setting
         value.

         For relay/power outputs, this is the output active/inactive
         alias setting value that currently corresponds to the output
         state.  So if the output is in the active state, then this
         object is the active alias setting value.  If the output is
         in hte inactive state, then this object is the inactive alias
         setting value.

         For non-CC and non-output sensors, this is the same as the
         esPointValueStr object."
    ::= { esPointEntry 10 }
    --*******.4.1.3052.********.1.10


esPointClassValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "For CC inputs, this is the CC active/inactive class setting
         setting value that currently corresponds to the event state.
         So if the CC input is in the event state, then this object
         is the active class setting value.  If the CC input is in the
         normal state, then this object is the inactive class setting
         value.

         For relay/power, analog and temp outputs, this is the output 
         class string given the event (Very High, High, Normal, Low and
         Very Low).  The class string is based upon the chosen string in
         the class table for e.g. Info, Minor, Major, Minor, Critical etc.
         If the given event is High, the chosen Class string for this event
         is what returned.
         "
    ::= { esPointEntry 11 }
    --*******.4.1.3052.********.1.11
--***************************************************************************************
--Data Event Status Section 1.2
--***************************************************************************************

deStatusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF DEStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Data Event Status Table"
    ::= { dataEventStatus 1 }
    --*******.4.1.3052.********

deStatusEntry OBJECT-TYPE
    SYNTAX DEStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "EventSensor status entry"
    INDEX { deStatusIndex }
    ::= { deStatusTable 1 }
    --*******.4.1.3052.********.1

deStatusIndex OBJECT-TYPE
    SYNTAX Integer32 (1..100)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for Data Events"
    ::= { deStatusEntry 1 }
    --*******.4.1.3052.********.1.1

deStatusName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Data Event name"
    ::= { deStatusEntry 2 }
    --*******.4.1.3052.********.1.2

deStatusCounter OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Number of times a data record has matched this
        event's equation.
        Range: 1-*********"
    ::= { deStatusEntry 3 }
    --*******.4.1.3052.********.1.3

deStatusThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Number of times the equation is matched before
        an event is triggered.
        Range: 1-*********"
    ::= { deStatusEntry 4 }
    --*******.4.1.3052.********.1.4

modemStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "status of modem (idle, dialing, connected, etc.)"
    ::= { status 3 }
    --*******.4.1.3052.12.1.3

modemCLIDLogTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ModemCLIDLogConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of numbers of recent dialing parties"
    ::= { status 4 }
    --*******.4.1.3052.12.1.4

modemCLIDLogEntry OBJECT-TYPE
    SYNTAX ModemCLIDLogConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of CLID log entries"
    INDEX { modemCLIDLogIndex }
    ::= { modemCLIDLogTable 1 }
    --*******.4.1.3052.********

modemCLIDLogIndex OBJECT-TYPE
    SYNTAX Integer32 (1..8)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of CLID log entries"
    ::= { modemCLIDLogEntry 1 }
    --*******.4.1.3052.********.1

modemCLIDLogNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "recently logged number of dialing party"
    ::= { modemCLIDLogEntry 2 }
    --*******.4.1.3052.********.2

--***************************************************************************************
--Power Distribution Status Section 1.5
--***************************************************************************************

pdConfig OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Power Distribution output configuration"
    ::= { powerDistributionStatus 1 }
    --*******.4.1.3052.********

PowerDistribNextGen ::=
    SEQUENCE {
        pdnIndexPD
            Integer32,
        pdnIndexOutput
            Integer32,
        pdnConfig
            DisplayString,
        pdnMainCurrentInEventState
            DisplayString,
        pdnMainCurrentValue
            Integer32,
        pdnMainCurrentValueStr
            DisplayString,
        pdnMainCurrentDeadband
            Integer32,
        pdnMainCurrentVHighCurrent
            Integer32,
        pdnMainCurrentHighCurrent
            Integer32,
        pdnMainCurrentLowCurrent
            Integer32,
        pdnMainCurrentVLowCurrent
            Integer32,
        pdnMainVoltageInEventState
            DisplayString,
        pdnMainVoltageValue
            Integer32,
        pdnMainVoltageValueStr
            DisplayString,
        pdnMainVoltageDeadband
            Integer32,
        pdnMainVoltageVHighVoltage
            Integer32,
        pdnMainVoltageHighVoltage
            Integer32,
        pdnMainVoltageLowVoltage
            Integer32,
        pdnMainVoltageVLowVoltage
            Integer32,
        pdnMainPowerValue
            Integer32,
        pdnMainPowerValueStr
            DisplayString,
        pdnOutputCurrentInEventState
            DisplayString,
        pdnOutputCurrentValue
            Integer32,
        pdnOutputCurrentValueStr
            DisplayString,
        pdnOutputCurrentDeadband
            Integer32,
        pdnOutputCurrentVHighCurrent
            Integer32,
        pdnOutputCurrentHighCurrent
            Integer32,
        pdnOutputCurrentLowCurrent
            Integer32,
        pdnOutputCurrentVLowCurrent
            Integer32,
        pdnOutputFuseInEventState
            DisplayString,
        pdnOutputFuseValueStr
            DisplayString,
        pdnMainCombinedStatus
            DisplayString,
        pdnOutputCombinedStatusBlock1
            DisplayString,
        pdnOutputCombinedStatusBlock2
            DisplayString,
        pdnDeviceCurrentValue
            Integer32,
        pdnDeviceCurrentValueStr
            DisplayString
}

pdnTable OBJECT-TYPE
    SYNTAX SEQUENCE OF PowerDistribNextGen
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of power distribution objects arranged according
        to next-generation methodology (i.e., using multiple PowerBoss
        power distributors in addition to the on-board power
        distributor)."
    ::= { pdNextGen 1 }
    --*******.4.1.3052.********.1

pdnEntry OBJECT-TYPE
    SYNTAX PowerDistribNextGen
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of power distribution objects arranged according
        to next-generation methodology (i.e., using multiple PowerBoss
        power distributors in addition to the on-board power
        distributor)."
    INDEX { pdnIndexPD, pdnIndexOutput }
    ::= { pdnTable 1 }
    --*******.4.1.3052.********.1.1

pdnIndexPD OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of power distribution objects, specifically the
        index that refers to which power distributor.  A value of 200 means
        the internal power distributor.  A value from 1 to 16 refers to an
        external power distributor (PowerBoss), and is the same value as esIndexES,
        which refers to the PowerBoss as an EventSensor in the eventSensorStatus
        object table.  In other words, pdnIndexPD is the same as esIndexES:
        esIndexES is used to refer to the PowerBoss as an eventsensor (i.e., control
        outputs), while pdnIndexPD is used to refer to the PowerBoss when used for
        power distribution monitoring (i.e., to alarm on output
        current/voltage/fuse status)."
    ::= { pdnEntry 1 }
    --*******.4.1.3052.********.1.1.1

pdnIndexOutput OBJECT-TYPE
    SYNTAX Integer32 (1..12)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of power distribution objects, specifically the
        index that refers to which output on the power distributor specified
        by pdnIndexPD.  For objects where the output is irrelevant, this index
        should be 1."
    ::= { pdnEntry 2 }
    --*******.4.1.3052.********.1.1.2

pdnConfig OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of outputs on the power distributor specified by pdnIndexPD.
        pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 3 }
    --*******.4.1.3052.********.1.1.3

pdnMainCurrentInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The event state of the current (i.e. Amps) event of the power distributor
        specified by pdnIndexPD.  pdnIndexOutput should be 1 for this object.
        Possible values for this object are: 'Very Low', 'Low', 'Normal', 'High',
        'Very High'."
    ::= { pdnEntry 4 }
    --*******.4.1.3052.********.1.1.4

pdnMainCurrentValue OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current drawn, in mA, for the power distributor specified by pdnIndexPD.
        If pdnIndexPD is 1-16 then this is an external power distributor (PowerBoss)
        and refers to the current drawn by that PowerBoss and all equipment powered
        by that PowerBoss.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 5 }
    --*******.4.1.3052.********.1.1.5

pdnMainCurrentValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Essentially pdnMainCurrentValue formatted as a string in units of Amps, with
        3 decimal places.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 6 }
    --*******.4.1.3052.********.1.1.6

pdnMainCurrentDeadband OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The deadband, in mA, for the main current sensor for the power distributor
        specified by pdIndexPD.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 7 }
    --*******.4.1.3052.********.1.1.7

pdnMainCurrentVHighCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for the main current sensor's Very High event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 8 }
    --*******.4.1.3052.********.1.1.8

pdnMainCurrentHighCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for the main current sensor's High event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 9 }
    --*******.4.1.3052.********.1.1.9

pdnMainCurrentLowCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for the main current sensor's Low event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 10 }
    --*******.4.1.3052.********.1.1.10

pdnMainCurrentVLowCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for the main current sensor's Very Low event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 11 }
    --*******.4.1.3052.********.1.1.11

pdnMainVoltageInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The event state of the voltage event of the power distributor
        specified by pdnIndexPD.  pdnIndexOutput should be 1 for this object.
        Possible values for this object are: 'Very Low', 'Low', 'Normal', 'High',
        'Very High'."
    ::= { pdnEntry 12 }
    --*******.4.1.3052.********.1.1.12

pdnMainVoltageValue OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage, in mV, at the power distributor specified by pdnIndexPD.
        If pdnIndexPD is 1-16 then this is an external power distributor (PowerBoss) and
        refers to the voltage at that PowerBoss and all equipment powered by that
        PowerBoss.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 13 }
    --*******.4.1.3052.********.1.1.13

pdnMainVoltageValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Essentially pdnMainVoltageValue formatted as a string in units of Volts, with
        3 decimal places.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 14 }
    --*******.4.1.3052.********.1.1.14

pdnMainVoltageDeadband OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The deadband, in mV, for the main voltage sensor for the power distributor
        specified by pdIndexPD.  pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 15 }
    --*******.4.1.3052.********.1.1.15

pdnMainVoltageVHighVoltage OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage, in mA, for the main voltage sensor's Very High event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 16 }
    --*******.4.1.3052.********.1.1.16

pdnMainVoltageHighVoltage OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage, in mV, for the main voltage sensor's High event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 17 }
    --*******.4.1.3052.********.1.1.17

pdnMainVoltageLowVoltage OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage, in mV, for the main voltage sensor's Low event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 18 }
    --*******.4.1.3052.********.1.1.18

pdnMainVoltageVLowVoltage OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The voltage, in mV, for the main voltage sensor's Very Low event threshold,
        for the power distributor specified by pdIndexPD.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 19 }
    --*******.4.1.3052.********.1.1.19

pdnMainPowerValue OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The power, in Watts, for the power distributor specified by pdIndexPD.
        pdnIndexOutput should be 1 for this object."
    ::= { pdnEntry 20 }
    --*******.4.1.3052.********.1.1.20

pdnMainPowerValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The power, in Watts, for the power distributor specified by pdIndexPD,
        formatted as a string with the unit name 'Watts'.  pdnIndexOutput should be 1
        for this object."
    ::= { pdnEntry 21 }
    --*******.4.1.3052.********.1.1.21

pdnOutputCurrentInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The event state of the current (i.e. Amps) event of the power distributor
        output specified by pdnIndexPD and pdnIndexOutput.  E.g., for pdnIndexPD=200
        and pdnIndexOutput=2, this refers to power output #2 of the internal power
        distributor. Possible values for this object are: 'Very Low', 'Low', 'Normal',
        'High', 'Very High'."
    ::= { pdnEntry 22 }
    --*******.4.1.3052.********.1.1.22

pdnOutputCurrentValue OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current drawn, in mA, for the power distributor output specified by
        pdnIndexPD and pdIndexOutput.  E.g., if pdIndexPD is 3 then this is an external
        power distributor (PowerBoss) identified as 'power distributor 3' and refers to
        the current drawn by a specific power output on that PowerBoss.  The specific
        power output is specified by pdIndexOutput."
    ::= { pdnEntry 23 }
    --*******.4.1.3052.********.1.1.23

pdnOutputCurrentValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Essentially pdnOutputCurrentValue formatted as a string in units of Amps, with
        3 decimal places."
    ::= { pdnEntry 24 }
    --*******.4.1.3052.********.1.1.24

pdnOutputCurrentDeadband OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The deadband, in mA, for the main current sensor for the power distributor
        specified by pdIndexPD.  The power output is specified by pdnIndexOutput."
    ::= { pdnEntry 25 }
    --*******.4.1.3052.********.1.1.25

pdnOutputCurrentVHighCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for a specific power output's current sensor Very High
        event threshold, for the power distributor specified by pdIndexPD.  The power
        output is specified by pdnIndexOutput."
    ::= { pdnEntry 26 }
    --*******.4.1.3052.********.1.1.26

pdnOutputCurrentHighCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for a specific power output's current sensor High event
        threshold, for the power distributor specified by pdIndexPD.  The power output
        is specified by pdnIndexOutput for this object."
    ::= { pdnEntry 27 }
    --*******.4.1.3052.********.1.1.27

pdnOutputCurrentLowCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for a specific power output's current sensor Low event
        threshold, for the power distributor specified by pdIndexPD.  The power output
        is specified by pdnIndexOutput for this object."
    ::= { pdnEntry 28 }
    --*******.4.1.3052.********.1.1.28

pdnOutputCurrentVLowCurrent OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The current, in mA, for a specific power output's current sensor Very Low
        event threshold, for the power distributor specified by pdIndexPD.  The power
        output is specified by pdnIndexOutput for this object."
    ::= { pdnEntry 29 }
    --*******.4.1.3052.********.1.1.29

pdnOutputFuseInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The event state of the fuse event of the power distributor output specified
        by pdnIndexPD and pdnIndexOutput.  E.g., for pdnIndexPD=14 and pdnIndexOutput=2,
        this refers to power output #2 of the external power distributor (PowerBoss),
        identified as 'power distributor 14'. Possible values for this object are:
        'Very Low', 'Low', 'Normal', 'High', 'Very High'.  This object exists only for
        power distributors with fuse monitoring support."
    ::= { pdnEntry 30 }
    --*******.4.1.3052.********.1.1.30

pdnOutputFuseValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The state of the fuse of the power distributor output specified
        by pdnIndexPD and pdnIndexOutput.  Possible values for this object are:
        'BLOWN' and 'OK'.  This object exists only for power distributors with
        fuse monitoring support."
    ::= { pdnEntry 31 }
    --*******.4.1.3052.********.1.1.31

pdnMainCombinedStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        format: voltageColorCode`voltageEventState`voltageValue`mainCurrentColorCode`mainCurrentEventState`mainCurrentValue`deviceCurrentValue`mainPowerValue"
    ::= { pdnEntry 32 }
    --*******.4.1.3052.********.1.1.32

pdnOutputCombinedStatusBlock1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        format: numInBlock~outputCurrentColorCode1`outputCurrentEventState1`outputCurrentValue1`fuseColorCode1`fuseValue1~..."
    ::= { pdnEntry 33 }
    --*******.4.1.3052.********.1.1.33

pdnOutputCombinedStatusBlock2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        format: numInBlock~outputCurrentColorCode1`outputCurrentEventState1`outputCurrentValue1`fuseColorCode1`fuseValue1~..."
    ::= { pdnEntry 34 }
    --*******.4.1.3052.********.1.1.34

pdnDeviceCurrentValue OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Current drawn by device in mA.  For the internal power distributor device,
        this is the current drawn by the SiteBoss 571.  For an external power
        distributor device (PowerBoss), this is the current drawn by the
        PowerBoss.  This object exists only for power distributors with device
        current monitoring support."
    ::= { pdnEntry 35 }
    --*******.4.1.3052.********.1.1.35

pdnDeviceCurrentValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Current drawn by device as a string with units (Amps).  For the internal
        power distributor device, this is the current drawn by the SiteBoss 571.
        For an external power distributor device (PowerBoss), this is the
        current drawn by the PowerBoss.  This object exists only for power
        distributors with device current monitoring support."
    ::= { pdnEntry 36 }
    --*******.4.1.3052.********.1.1.36

pdSystemCurrent OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total current drawn by all power distributors in system; that is, the
        sum of all pdnMainCurrentValue objects for all connected power
        distributors: internal and external."
    ::= { pdSystem 1 }
    --*******.4.1.3052.********.1

pdSystemPower OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power used by all power distributors in system; that is, the
        sum of all pdnMainPowerValue objects for all connected power
        distributors: internal and external."
    ::= { pdSystem 2 }
    --*******.4.1.3052.********.2


--***************************************************************************************
--Fuel Sensor 1.6
--***************************************************************************************

fsStatusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FSStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Fuel sensor status table"
    ::= { fuelSensorStatus 1 }
    --*******.4.1.3052.********

fsStatusEntry OBJECT-TYPE
    SYNTAX FSStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Fuel sensor status table entry"
    INDEX { fsStatusIndex }
    ::= { fsStatusTable 1 }
    --*******.4.1.3052.********.1

fsStatusIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for fuel sensors"
    ::= { fsStatusEntry 1 }
    --*******.4.1.3052.********.1.1

fsStatusName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Fuel sensor name"
    ::= { fsStatusEntry 2 }
    --*******.4.1.3052.********.1.2

fsStatusDeviceState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Fuel sensor device state"
    ::= { fsStatusEntry 3 }
    --*******.4.1.3052.********.1.3

fsStatusVolumeValueString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Fuel volume. Range is -2,147,483,648 to 2,147,483,647. This value reads
         -999 when the fuel sensor is disconnected"
    ::= { fsStatusEntry 4 }
    --*******.4.1.3052.********.1.4

fsStatusVolumePercentLevel OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Height of the fluid in the tank expressed as a percentage of the tank
         height, in hundredths of a percent."
    ::= { fsStatusEntry 7 }
    --*******.4.1.3052.********.1.7

fsStatusVolumeInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Current event status of the fuel sensor."
    ::= { fsStatusEntry 8 }
    --*******.4.1.3052.********.1.8

fsStatusCombined OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        deviceState`deviceStateColorCode`percentRounded`valueAsFloat`volumeEventState`volumeEventStateColorCode`capacity`units`name"
    ::= { fsStatusEntry 9 }
    --*******.4.1.3052.********.1.9

--***************************************************************************************
--Wireless Modem Status Section  1.7
--***************************************************************************************

wmsStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem status"
    ::= { wirelessModemStatus 1 }
    --*******.4.1.3052.********

wmsSignal OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Signal strength"
    ::= { wirelessModemStatus 2 }
    --*******.4.1.3052.********

wmsRSSI OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Signal strength (RSSI)"
    ::= { wirelessModemStatus 3 }
    --*******.4.1.3052.********

wmsBER OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Bit Error Rate"
    ::= { wirelessModemStatus 4 }
    --*******.4.1.3052.********

wmsUpdated OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem info last update time"
    ::= { wirelessModemStatus 5 }
    --*******.4.1.3052.********

wmsRegistration OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem registration status"
    ::= { wirelessModemStatus 6 }
    --*******.4.1.3052.********

wmsLAC OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Location Area Code"
    ::= { wirelessModemStatus 7 }
    --*******.4.1.3052.********

wmsCellID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Cell ID"
    ::= { wirelessModemStatus 8 }
    --*******.4.1.3052.********

wmsIMSI OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem Internation Mobile Subscriber Identity number"
    ::= { wirelessModemStatus 9 }
    --*******.4.1.3052.********

wmsPhnum OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem phone number"
    ::= { wirelessModemStatus 10 }
    --*******.4.1.3052.********0

wmsLocalIP OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Local IP address of modem on wireless network"
    ::= { wirelessModemStatus 11 }
    --*******.4.1.3052.********1

wmsMgfID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem manufacturer ID"
    ::= { wirelessModemStatus 12 }
    --*******.4.1.3052.********2

wmsModelID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem model ID"
    ::= { wirelessModemStatus 13 }
    --*******.4.1.3052.********3

wmsIMEI OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem International Mobile Equipment Identity number"
    ::= { wirelessModemStatus 14 }
    --*******.4.1.3052.********4

wmsRevID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem revision ID"
    ::= { wirelessModemStatus 15 }
    --*******.4.1.3052.********5

wmsNetName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem network name"
    ::= { wirelessModemStatus 16 }
    --*******.4.1.3052.********6

wmsGPRSStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem (E)GPRS status"
    ::= { wirelessModemStatus 17 }
    --*******.4.1.3052.********7

wmsBand OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem band"
    ::= { wirelessModemStatus 18 }
    --*******.4.1.3052.********8

wmsChannel OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem channel"
    ::= { wirelessModemStatus 19 }
    --*******.4.1.3052.********9

wmsCountryCode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem country code"
    ::= { wirelessModemStatus 20 }
    --*******.4.1.3052.********0

wmsNetCode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem network code"
    ::= { wirelessModemStatus 21 }
    --*******.4.1.3052.********1

wmsPLMNColor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem PLMN color"
    ::= { wirelessModemStatus 22 }
    --*******.4.1.3052.********2

wmsBScolor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem BS color"
    ::= { wirelessModemStatus 23 }
    --*******.4.1.3052.********3

wmsMpRACH OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem max power RACH"
    ::= { wirelessModemStatus 24 }
    --*******.4.1.3052.*********

wmsMinRxLevel OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem minimum receive level"
    ::= { wirelessModemStatus 25 }
    --*******.4.1.3052.*********

wmsBaseCoeff OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem base coefficient"
    ::= { wirelessModemStatus 26 }
    --*******.4.1.3052.*********

wmsSIMStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "WWireless modem SIM status"
    ::= { wirelessModemStatus 27 }
    --*******.4.1.3052.*********

wmsICCID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem SIM ICCID"
    ::= { wirelessModemStatus 28 }
    --*******.4.1.3052.*********

wmsModemType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Wireless modem type"
    ::= { wirelessModemStatus 29 }
    --*******.4.1.3052.*********


--***************************************************************************************
--AC Power Monitor Section 1.8
--***************************************************************************************

acpmStatusTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "AC power monitor status table"
    ::= { acPowerMonitorStatus 1 }
    --*******.4.1.3052.********

acpmStatusEntry OBJECT-TYPE
    SYNTAX ACPMStatus
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "AC power monitor status table entry"
    INDEX { acpmsIndex }
    ::= { acpmStatusTable 1 }
    --*******.4.1.3052.********.1

acpmsIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for AC power monitors"
    ::= { acpmStatusEntry 1 }
    --*******.4.1.3052.********.1.1

acpmsName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Name for the AC power monitor device."
    ::= { acpmStatusEntry 2 }
    --*******.4.1.3052.********.1.2

acpmsAvgVoltageValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average voltage, in volts. This is calculated using the formula
        (register * PT ratio * 0.1). This could be either the average of
        the phase voltages, or the sum of the phase voltages, depending
        on device configuration."
    ::= { acpmStatusEntry 3 }
    --*******.4.1.3052.********.1.3

acpmsAvgVoltageMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Minimum average voltage value since last reset, in volts."
    ::= { acpmStatusEntry 4 }
    --*******.4.1.3052.********.1.4

acpmsAvgVoltageMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Maximum average voltage value since last reset, in volts."
    ::= { acpmStatusEntry 5 }
    --*******.4.1.3052.********.1.5

acpmsAvgVoltageAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average average voltage value since last reset, in volts.  This is
        the unit's average of its measurements of the average voltage
        reported by the AC power monitor device."
    ::= { acpmStatusEntry 6 }
    --*******.4.1.3052.********.1.6

acpmsAvgVoltageInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Event state of this AC power monitor voltage (Low, Normal, High, etc.)."
    ::= { acpmStatusEntry 7 }
    --*******.4.1.3052.********.1.7

acpmsVoltagePhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A voltage, in volts. This is calculated using the formula
        (register * PT ratio * 0.1)."
    ::= { acpmStatusEntry 8 }
    --*******.4.1.3052.********.1.8

acpmsVoltagePhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B voltage, in volts. This is calculated using the formula
        (register * PT ratio * 0.1)."
    ::= { acpmStatusEntry 9 }
    --*******.4.1.3052.********.1.9

acpmsVoltagePhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C voltage, in volts. This is calculated using the formula
        (register * PT ratio * 0.1)."
    ::= { acpmStatusEntry 10 }
    --*******.4.1.3052.********.1.10

acpmsAvgCurrentValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average current, in amps. This is calculated using the formula
        (register * CT ratio * 0.001). This could be either the average of
        the phase currents, or the sum of the phase currents, depending on
        device configuration."
    ::= { acpmStatusEntry 11 }
    --*******.4.1.3052.********.1.11

acpmsAvgCurrentMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Minimum average current value since last reset, in amps."
    ::= { acpmStatusEntry 12 }
    --*******.4.1.3052.********.1.12

acpmsAvgCurrentMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Maximum average current value since last reset, in amps."
    ::= { acpmStatusEntry 13 }
    --*******.4.1.3052.********.1.13

acpmsAvgCurrentAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average average current value since last reset, in amps.  This is
        the unit's average of its measurements of the average current reported
        by the AC power monitor device."
    ::= { acpmStatusEntry 14 }
    --*******.4.1.3052.********.1.14

acpmsAvgCurrentInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Event state of this AC power monitor current (Low, Normal, High, etc.)."
    ::= { acpmStatusEntry 15 }
    --*******.4.1.3052.********.1.15

acpmsCurrentPhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A current, in amps. This is calculated using the formula
        (register * CT ratio * 0.001)."
    ::= { acpmStatusEntry 16 }
    --*******.4.1.3052.********.1.16

acpmsCurrentPhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B current, in amps. This is calculated using the formula
        (register * CT ratio * 0.001)."
    ::= { acpmStatusEntry 17 }
    --*******.4.1.3052.********.1.17

acpmsCurrentPhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C current, in amps. This is calculated using the formula
        (register * CT ratio * 0.001)."
    ::= { acpmStatusEntry 18 }
    --*******.4.1.3052.********.1.18

acpmsAvgFreqValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Frequency, in hertz. On the YD2010, this is calculated using the
        formula (register * 0.00106813). On the WattsOn, this is calculated
        using the formula (register * 0.1)."
    ::= { acpmStatusEntry 19 }
    --*******.4.1.3052.********.1.19

acpmsAvgFreqMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Minimum frequency value since last reset, in hertz."
    ::= { acpmStatusEntry 20 }
    --*******.4.1.3052.********.1.20

acpmsAvgFreqMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Maximum frequency value since last reset, in hertz."
    ::= { acpmStatusEntry 21 }
    --*******.4.1.3052.********.1.21

acpmsAvgFreqAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average frequency value since last reset, in hertz.  This is the
        unit's average of its measurements of the average frequency reported
        by the AC power monitor device."
    ::= { acpmStatusEntry 22 }
    --*******.4.1.3052.********.1.22

acpmsAvgFreqInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Event state of this AC power monitor frequency (Low, Normal, High, etc.)."
    ::= { acpmStatusEntry 23 }
    --*******.4.1.3052.********.1.23

acpmsTRPValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total real power, in watts. On the YD2010, this is calculated using
        the formula (register * PT ratio * CT ratio * Urg * 0.4), where Urg is
        the voltage range taken from register 0x30A. On the WattsOn models,
        this is calculated using the formula (register * PT * CT)."
    ::= { acpmStatusEntry 24 }
    --*******.4.1.3052.********.1.24

acpmsTRPMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Minimum total real power value since last reset, in watts."
    ::= { acpmStatusEntry 25 }
    --*******.4.1.3052.********.1.25

acpmsTRPMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Maximum total real power value since last reset, in watts."
    ::= { acpmStatusEntry 26 }
    --*******.4.1.3052.********.1.26

acpmsTRPAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Average total real power value since last reset, in watts.  This is
        the unit's average of its measurements of the average total real power
        reported by the AC power monitor device."
    ::= { acpmStatusEntry 27 }
    --*******.4.1.3052.********.1.27

acpmsTRPInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Event state of this AC power monitor total real power (Low, Normal,
        High, etc.)."
    ::= { acpmStatusEntry 28 }
    --*******.4.1.3052.********.1.28

acpmsRPPhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A real power, in watts. On the YD2010, this is calculated using
        the formula (register * PT ratio * CT ratio * Urg * 0.4), where Urg is
        the voltage range taken from register 0x30A. On the WattsOn, this is
        calculated using the formula (register * PT * CT)."
    ::= { acpmStatusEntry 29 }
    --*******.4.1.3052.********.1.29

acpmsRPPhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B real power, in watts. On the YD2010, this is calculated using
        the formula (register * PT ratio * CT ratio * Urg * 0.4), where Urg is
        the voltage range taken from register 0x30A. On the WattsOn, this is
        calculated using the formula (register * PT * CT)."
    ::= { acpmStatusEntry 30 }
    --*******.4.1.3052.********.1.30

acpmsRPPhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C real power, in watts. On the YD2010, this is calculated using
        the formula (register * PT ratio * CT ratio * Urg * 0.4), where Urg is
        the voltage range taken from register 0x30A. On the WattsOn, this is
        calculated using the formula (register * PT * CT)."
    ::= { acpmStatusEntry 31 }
    --*******.4.1.3052.********.1.31

acpmsCombined OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        name`deviceState`deviceStateColorCode`voltage`current`frequency`power~voltageEventState`voltageColorCode`currentEventState`currentColorCode`frequencyEventState`freuencyColorCode`powerEventState`powerColorCode"
    ::= { acpmStatusEntry 32 }
    --*******.4.1.3052.********.1.32

acpmsTPFValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power factor."
    ::= { acpmStatusEntry 33 }
    --*******.4.1.3052.********.1.33

acpmsTPFMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power factor minimum value since last reset."
    ::= { acpmStatusEntry 34 }
    --*******.4.1.3052.********.1.34

acpmsTPFMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power factor maximum value since last reset."
    ::= { acpmStatusEntry 35 }
    --*******.4.1.3052.********.1.35

acpmsTPFAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power factor average value since last reset."
    ::= { acpmStatusEntry 36 }
    --*******.4.1.3052.********.1.36

acpmsTPFInEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total power factor event state."
    ::= { acpmStatusEntry 37 }
    --*******.4.1.3052.********.1.37

acpmsPFPhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A power factor."
    ::= { acpmStatusEntry 38 }
    --*******.4.1.3052.********.1.38

acpmsPFPhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B power factor."
    ::= { acpmStatusEntry 39 }
    --*******.4.1.3052.********.1.39

acpmsPFPhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C power factor."
    ::= { acpmStatusEntry 40 }
    --*******.4.1.3052.********.1.40

acpmsTRcPValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total reactive power, in VAR."
    ::= { acpmStatusEntry 41 }
    --*******.4.1.3052.********.1.41

acpmsTRcPMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total reactive power minimum value since last reset, in VAR."
    ::= { acpmStatusEntry 42 }
    --*******.4.1.3052.********.1.42

acpmsTRcPMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total reactive power maximum value since last reset, in VAR."
    ::= { acpmStatusEntry 43 }
    --*******.4.1.3052.********.1.43

acpmsTRcPAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total reactive power average value since last reset, in VAR."
    ::= { acpmStatusEntry 44 }
    --*******.4.1.3052.********.1.44

acpmsRcPPhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A reactive power, in VAR."
    ::= { acpmStatusEntry 45 }
    --*******.4.1.3052.********.1.45

acpmsRcPPhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B reactive power, in VAR."
    ::= { acpmStatusEntry 46 }
    --*******.4.1.3052.********.1.46

acpmsRcPPhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C reactive power, in VAR."
    ::= { acpmStatusEntry 47 }
    --*******.4.1.3052.********.1.47

acpmsTAPValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total apparent power, in VA."
    ::= { acpmStatusEntry 48 }
    --*******.4.1.3052.********.1.48

acpmsTAPMinStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total apparent power minimum value since last reset, in VA."
    ::= { acpmStatusEntry 49 }
    --*******.4.1.3052.********.1.49

acpmsTAPMaxStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total apparent power maximum value since last reset, in VA."
    ::= { acpmStatusEntry 50 }
    --*******.4.1.3052.********.1.50

acpmsTAPAvgStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total apparent power average value since last reset, in VA."
    ::= { acpmStatusEntry 51 }
    --*******.4.1.3052.********.1.51

acpmsAPPhaseAValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase A apparent power, in VA."
    ::= { acpmStatusEntry 52 }
    --*******.4.1.3052.********.1.52

acpmsAPPhaseBValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase B apparent power, in VA."
    ::= { acpmStatusEntry 53 }
    --*******.4.1.3052.********.1.53

acpmsAPPhaseCValueStr OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Phase C apparent power, in VA."
    ::= { acpmStatusEntry 54 }
    --*******.4.1.3052.********.1.54

acpmsTotalEnergyWh OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total energy, in Wh."
    ::= { acpmStatusEntry 55 }
    --*******.4.1.3052.********.1.55

acpmsTotalEnergyVAR OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total reactive energy, in VAR."
    ::= { acpmStatusEntry 56 }
    --*******.4.1.3052.********.1.56

acpmsTotalEnergyVA OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total apparent energy, in VA."
    ::= { acpmStatusEntry 57 }
    --*******.4.1.3052.********.1.57


--***************************************************************************************
--EventSensor Basics Section    2.1
--***************************************************************************************

esNumberEventSensors OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of EventSensors recognized by the unit,
        including the internal EventSensor.
        Range: 1-17"
    ::= { eventSensorBasics 1 }
    --*******.4.1.3052.********

esTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESExist
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The table of what EventSensors are attached to the unit,
        including the internal EventSensor"
    ::= { eventSensorBasics 2 }
    --*******.4.1.3052.********

esEntry OBJECT-TYPE
    SYNTAX ESExist
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table entry for EventSensor table"
    INDEX { esIndex }
    ::= { esTable 1 }
    --*******.4.1.3052.********.1

esIndex OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This number refers to an EventSensor; it has the
        same meaning as the esIndexES object (see above),
        except that this object is used only within the
        esTable branch.

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.

        The number corresponding to an EventSensor is
        determined by the user at the initial configuration
        time.  If there is an internal sensor in the unit,
        it will always be the first item in the Sensor
        Events setup menu.

        For example, if a new EventSensor is configured as
        the 2nd entry AFTER the internal EventSensor (if
        one exists) in the Sensor Events Setup Menu, then
        that EventSensor will be known from then on as
        EventSensor 2.  All point indeces with esIndex=2
        will now refer to that particular EventSensor.
        "
    ::= { esEntry 1 }
    --*******.4.1.3052.********.1.1

esName  OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The name of the EventSensor"
    ::= { esEntry 2 }
    --*******.4.1.3052.********.1.2

esID    OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The factory-assigned ID of the EventSensor"
    ::= { esEntry 3 }
    --*******.4.1.3052.********.1.3

esNumberTempSensors OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of temperature sensors on the EventSensor"
    ::= { esEntry 4 }
    --*******.4.1.3052.********.1.4

esTempReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how temperature values should
        be interpreted."
    ::= { esEntry 5 }
    --*******.4.1.3052.********.1.5

esNumberCCs OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of contact closures on the EventSensor."
    ::= { esEntry 6 }
    --*******.4.1.3052.********.1.6

esCCReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how CC values should be interpreted."
    ::= { esEntry 7 }
    --*******.4.1.3052.********.1.7

esNumberHumidSensors OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of humidity sensors on the EventSensor."
    ::= { esEntry 8 }
    --*******.4.1.3052.********.1.8

esHumidReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how humidity sensor values should be
        interpreted."
    ::= { esEntry 9 }
    --*******.4.1.3052.********.1.9

esNumberNoiseSensors OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of noise sensors on the EventSensor."
    ::= { esEntry 10 }
    --*******.4.1.3052.********.1.10

esNoiseReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how noise sensor values should be
        interpreted."
    ::= { esEntry 11 }
    --*******.4.1.3052.********.1.11

esNumberAirflowSensors OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of airflow sensors on the EventSensor."
    ::= { esEntry 12 }
    --*******.4.1.3052.********.1.12

esAirflowReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how airflow sensor values should be
        interpreted."
    ::= { esEntry 13 }
    --*******.4.1.3052.********.1.13

esNumberAnalog OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of analog inputs on the EventSensor."
    ::= { esEntry 14 }
    --*******.4.1.3052.********.1.14

esAnalogReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how analog input values should be
        interpreted."
    ::= { esEntry 15 }
    --*******.4.1.3052.********.1.15

esNumberOutputs OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of outputs (relay or power) on the EventSensor"
    ::= { esEntry 16 }
    --*******.4.1.3052.********.1.16

esOutputReportingMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description of how output values should be interpreted."
    ::= { esEntry 17 }
    --*******.4.1.3052.********.1.17

esTempCombinedStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        name`value`scale`eventState`eventColorCode"
    ::= { esEntry 18 }
    --*******.4.1.3052.********.1.18

esCCCombinedStatusBlock1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name1`value1`eventColorCode1`alias1~name2`value2`eventColorCode2`alias2~...name8`value8`eventColorCode8`alias8"
    ::= { esEntry 19 }
    --*******.4.1.3052.********.1.19

esCCCombinedStatusBlock2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name9`value9`eventColorCode9`alias9~name10`value10`eventColorCode10`alias10~...name16`value16`eventColorCode16`alias16"
    ::= { esEntry 20 }
    --*******.4.1.3052.********.1.20

esCCCombinedStatusBlock3 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name17`value17`eventColorCode17`alias17~name18`value18`eventColorCode18`alias18~...name24`value24`eventColorCode24`alias24"
    ::= { esEntry 21 }
    --*******.4.1.3052.********.1.21

esCCCombinedStatusBlock4 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name25`value25`eventColorCode25`alias25~name26`value26`eventColorCode26`alias26~...name32`value32`eventColorCode32`alias32"
    ::= { esEntry 22 }
    --*******.4.1.3052.********.1.22

esCCCombinedStatusBlock5 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name33`value33`eventColorCode33`alias33~name34`value34`eventColorCode34`alias35~...name40`value40`eventColorCode40`alias40"
    ::= { esEntry 23 }
    --*******.4.1.3052.********.1.23

esCCCombinedStatusBlock6 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name41`value41`eventColorCode41`alias41~name42`value42`eventColorCode42`alias43~...name48`value48`eventColorCode48`alias48"
    ::= { esEntry 24 }
    --*******.4.1.3052.********.1.24

esCCCombinedStatusBlock7 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name49`value49`eventColorCode49`alias49~name50`value50`eventColorCode50`alias50~...name56`value56`eventColorCode56`alias56"
    ::= { esEntry 25 }
    --*******.4.1.3052.********.1.25

esCCCombinedStatusBlock8 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name57`value57`eventColorCode57`alias57~name57`value57`eventColorCode564`alias57~...name64`value64`eventColorCode64`alias64"
    ::= { esEntry 26 }
    --*******.4.1.3052.********.1.26

esHumidCombinedStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        name`value`eventState`eventColorCode"
    ::= { esEntry 27 }
    --*******.4.1.3052.********.1.27

esAnalogCombinedStatusBlock1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name1`value1`units1`eventState`eventColorCode1~name2`value2`units2`eventState`eventColorCode2~..."
    ::= { esEntry 28 }
    --*******.4.1.3052.********.1.28

esAnalogCombinedStatusBlock2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~name9`value9`units9`eventState9`eventColorCode9~..."
    ::= { esEntry 29 }
    --*******.4.1.3052.********.1.29

esAnalogCombinedStatusBlock3 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~namex`valuex`unitsx`eventStatex`eventColorCodex~..."
    ::= { esEntry 30 }
    --*******.4.1.3052.********.1.31

esAnalogCombinedStatusBlock4 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~namex`valuex`unitsx`eventStatex`eventColorCodex~..."
    ::= { esEntry 31 }
    --*******.4.1.3052.********.1.31

esAnalogCombinedStatusBlock5 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~namex`valuex`unitsx`eventStatex`eventColorCodex~..."
    ::= { esEntry 32 }
    --*******.4.1.3052.********.1.32

esAnalogCombinedStatusBlock6 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~namex`valuex`unitsx`eventStatex`eventColorCodex~..."
    ::= { esEntry 33 }
    --*******.4.1.3052.********.1.33

esOutputCombinedStatusBlock1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock~namex`valuex`unitsx`eventStatex`eventColorCodex~..."
    ::= { esEntry 34 }
    --*******.4.1.3052.********.1.34

esOutputCombinedStatusBlock2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Combined status string intended to be parsed by NMS:
        numInBlock`namex`valuex`aliasx~namey`valuey`aliasy~...namez`valuez`aliasz"
    ::= { esEntry 35 }
    --*******.4.1.3052.********.1.35

esNewSensors OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "String representing the number of new (attached but unconfigured) EventSensors"
    ::= { eventSensorBasics 3 }
    --*******.4.1.3052.********


--***************************************************************************************
--EventSensor Point Config Section   2.2
--***************************************************************************************



esPointConfigTempTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPointConfigTemp
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes 3 basic attributes of temperature
        sensor points.  A point is a particular sensor on an
        EventSensor.  The 3 point attributes organizes in this table
        are:
        1. the configuration item (e.g., enable, name, etc.)
        2. the eventsensor on which this point resides
        3. the point number (always 1 for temp sensors)

        Points are referenced by a point config index.  The point
        config index is a string of 2 numbers separated by periods.
        It contains all the information necessary for getting a
        piece of data off an event sensor within the context of this
        emperature point config table ; namely, which EventSensor,
        and which sensor (a.k.a. point) (always 1 for temperature
        sensors).
        "
    ::= { eventSensorPointConfig 1 }
    --*******.4.1.3052.********

esPointConfigTempEntry OBJECT-TYPE
    SYNTAX ESPointConfigTemp
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point config temperature table"
    INDEX { espcTempIndexES, espcTempIndexPoint }
    ::= { esPointConfigTempTable 1 }
    --*******.4.1.3052.********.1

espcTempIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point config index is a string of 2 numbers separated
        by a period.  It contains all the information
        necessary for getting a piece of data off an event
        sensor within the the context of the eventsensor point
        config temperature table; namely, which EventSensor,
        and which sensor (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.

        The number corresponding to an EventSensor is
        determined by the user at the initial configuration
        time.  If there is an internal sensor in the unit,
        it will always be the first item in the Sensor
        Events setup menu.

        For example, if a new EventSensor is configured as
        the 2nd entry AFTER the internal EventSensor (if
        one exists) in the Sensor Events Setup Menu, then
        that EventSensor will be known from then on as
        EventSensor 2.  All point indeces with esIndexES=2
        will now refer to that particular EventSensor.
        "
    ::= { esPointConfigTempEntry 1 }
    --*******.4.1.3052.********.1.1

espcTempIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with espcTempIndexES (which EventSensor)
        uniquely defines each point attached to a unit within the
        context of temperature point configuration."
    ::= { esPointConfigTempEntry 2 }
    --*******.4.1.3052.********.1.2

espcTempEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this temperature sensor"
    ::= { esPointConfigTempEntry 3 }
    --*******.4.1.3052.********.1.3

espcTempScale OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Scale (C/F) for this temperature sensor"
    ::= { esPointConfigTempEntry 4 }
    --*******.4.1.3052.********.1.4

espcTempDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband for this temperature sensor (in floating-point format)"
    ::= { esPointConfigTempEntry 5 }
    --*******.4.1.3052.********.1.5

espcTempVHighTemp OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high temperature event threshold for this temperature
        sensor (in floating-point format)"
    ::= { esPointConfigTempEntry 6 }
    --*******.4.1.3052.********.1.6

espcTempVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high temperature event actions for this temperature
        sensor"
    ::= { esPointConfigTempEntry 7 }
    --*******.4.1.3052.********.1.7

espcTempVHighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high temperature event trap number for this
        temperature sensor"
    ::= { esPointConfigTempEntry 8 }
    --*******.4.1.3052.********.1.8

espcTempVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high temperature event class for this temperature
        sensor"
    ::= { esPointConfigTempEntry 9 }
    --*******.4.1.3052.********.1.9

espcTempHighTemp OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High temperature event threshold for this temperature
        sensor (in floating-point format)"
    ::= { esPointConfigTempEntry 10 }
    --*******.4.1.3052.********.1.10

espcTempHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High temperature event actions for this temperature sensor"
    ::= { esPointConfigTempEntry 11 }
    --*******.4.1.3052.********.1.11

espcTempHighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High temperature event trap number for this temperature
        sensor"
    ::= { esPointConfigTempEntry 12 }
    --*******.4.1.3052.********.1.12

espcTempHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High temperature event class for this temperature sensor"
    ::= { esPointConfigTempEntry 13 }
    --*******.4.1.3052.********.1.13

espcTempNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal event actions for this temperature sensor"
    ::= { esPointConfigTempEntry 14 }
    --*******.4.1.3052.********.1.14

espcTempNormalTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal event trap number for this temperature
        sensor"
    ::= { esPointConfigTempEntry 15 }
    --*******.4.1.3052.********.1.15

espcTempNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal event class for this temperature sensor"
    ::= { esPointConfigTempEntry 16 }
    --*******.4.1.3052.********.1.16

espcTempLowTemp OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low temperature event threshold for this temperature sensor
        (in floating-point format)"
    ::= { esPointConfigTempEntry 17 }
    --*******.4.1.3052.********.1.17

espcTempLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low temperature event actions for this temperature sensor"
    ::= { esPointConfigTempEntry 18 }
    --*******.4.1.3052.********.1.18

espcTempLowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low temperature event trap number for this temperature
        sensor"
    ::= { esPointConfigTempEntry 19 }
    --*******.4.1.3052.********.1.19

espcTempLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low temperature event class for this temperature sensor"
    ::= { esPointConfigTempEntry 20 }
    --*******.4.1.3052.********.1.20

espcTempVLowTemp OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low temperature event threshold for this temperature
        sensor (in floating-point format)"
    ::= { esPointConfigTempEntry 21 }
    --*******.4.1.3052.********.1.21

espcTempVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low temperature event actions for this temperature
        sensor"
    ::= { esPointConfigTempEntry 22 }
    --*******.4.1.3052.********.1.22

espcTempVLowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low temperature event trap number for this temperature
        sensor"
    ::= { esPointConfigTempEntry 23 }
    --*******.4.1.3052.********.1.23

espcTempVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low temperature event class for this temperature sensor"
    ::= { esPointConfigTempEntry 24 }
    --*******.4.1.3052.********.1.24





esPointConfigCCTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPointConfigCC
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes 3 basic attributes of contact closure
        points.  A point is a particular sensor on an EventSensor.
        The 3 point attributes organizes in this table are:
        1. the configuration item (e.g., enable, name, etc.)
        2. the eventsensor on which this point resides
        3. the point number

        Points are referenced by a point config index.  The point
        config index is a string of 2 numbers separated by a period.
        It contains all the information necessary for getting a piece
        of data off an event sensor within the context of this
        contact closure point config table ; namely, which
        EventSensor and which sensor.
        "
    ::= { eventSensorPointConfig 2 }
    --*******.4.1.3052.********

esPointConfigCCEntry OBJECT-TYPE
    SYNTAX ESPointConfigCC
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point config contact closure table"
    INDEX { espcCCIndexES, espcCCIndexPoint }
    ::= { esPointConfigCCTable 1 }
    --*******.4.1.3052.********.1

espcCCIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point config index is a string of 2 numbers separated
        by a period.  It contains all the information
        necessary for getting a piece of data off an event
        sensor within the the context of the eventsensor point
        config contact closure table; namely, which EventSensor
        and which sensor (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.
        "
    ::= { esPointConfigCCEntry 1 }
    --*******.4.1.3052.********.1.1

espcCCIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with espcCCIndexES (which EventSensor)
        uniquely defines each point attached to a unit within the
        context of contact closure point configuration."
    ::= { esPointConfigCCEntry 2 }
    --*******.4.1.3052.********.1.2

espcCCEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable for this contact closure"
    ::= { esPointConfigCCEntry 3 }
    --*******.4.1.3052.********.1.3

espcCCName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Name for this contact closure"
    ::= { esPointConfigCCEntry 4 }
    --*******.4.1.3052.********.1.4

espcCCEventState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event state for this contact closure"
    ::= { esPointConfigCCEntry 5 }
    --*******.4.1.3052.********.1.5

espcCCThreshold OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event threshold (seconds) for this contact closure"
    ::= { esPointConfigCCEntry 6 }
    --*******.4.1.3052.********.1.6

espcCCEventActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event actions for this contact closure"
    ::= { esPointConfigCCEntry 7 }
    --*******.4.1.3052.********.1.7

espcCCEventTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event trap number for this contact closure"
    ::= { esPointConfigCCEntry 8 }
    --*******.4.1.3052.********.1.8

espcCCEventClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event class for this contact closure"
    ::= { esPointConfigCCEntry 9 }
    --*******.4.1.3052.********.1.9

espcCCNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal actions for this contact closure"
    ::= { esPointConfigCCEntry 10 }
    --*******.4.1.3052.********.1.10

espcCCNormalTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal trap number for this contact closure"
    ::= { esPointConfigCCEntry 11 }
    --*******.4.1.3052.********.1.11

espcCCNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal class for this contact closure"
    ::= { esPointConfigCCEntry 12 }
    --*******.4.1.3052.********.1.12

espcCCAlarmAlias OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event alias for this contact closure"
    ::= { esPointConfigCCEntry 13 }
    --*******.4.1.3052.********.1.13

espcCCNormalAlias OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal alias for this contact closure"
    ::= { esPointConfigCCEntry 14 }
    --*******.4.1.3052.********.1.14

espcCCNormalThreshold OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Event threshold (seconds) for this contact closure to return to normal"
    ::= { esPointConfigCCEntry 15 }
    --*******.4.1.3052.********.1.15

espcCCOverrideGlobalReminder OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Override global reminder interval (evReminderInterval) for this CC."
    ::= { esPointConfigCCEntry 16 }
    --*******.4.1.3052.********.1.16

espcCCReminderInterval OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Individual reminder interval for this CC, applicable when the global
        reminder is overriden."
    ::= { esPointConfigCCEntry 17 }
    --*******.4.1.3052.********.1.17



esPointConfigHumidTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPointConfigHumid
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes 3 basic attributes of humidity points.
        A point is a particular sensor on an EventSensor.  The 3
        point attributes organizes in this table are:
        1. the configuration item (e.g., enable, name, etc.)
        2. the eventsensor on which this point resides
        3. the point number (always 1 for humidity sensors)

        Points are referenced by a point config index.  The point
        config index is a string of 2 numbers separated by a period.
        It contains all the information necessary for getting a piece
        of data off an event sensor within the context of this
        humidity point config table ; namely, which EventSensor and
        which sensor.
        "
    ::= { eventSensorPointConfig 3 }
    --*******.4.1.3052.1*******

esPointConfigHumidEntry OBJECT-TYPE
    SYNTAX ESPointConfigHumid
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point config humidity table"
    INDEX { espcHumidIndexES, espcHumidIndexPoint }
    ::= { esPointConfigHumidTable 1 }
    --*******.4.1.3052.1*******.1

espcHumidIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point config index is a string of 2 numbers separated
        by a period.  It contains all the information
        necessary for getting a piece of data off an event
        sensor within the the context of the eventsensor point
        config contact closure table; namely, which EventSensor
        and which sensor (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.
        "
    ::= { esPointConfigHumidEntry 1 }
    --*******.4.1.3052.1*******.1.1

espcHumidIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with espcHumidIndexES (which EventSensor)
        uniquely defines each point attached to a unit within the
        context of contact closure point configuration."
    ::= { esPointConfigHumidEntry 2 }
    --*******.4.1.3052.1*******.1.2

espcHumidEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable for this humidity sensor"
    ::= { esPointConfigHumidEntry 3 }
    --*******.4.1.3052.1*******.1.3

espcHumidDeadband OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband for this humidity sensor"
    ::= { esPointConfigHumidEntry 4 }
    --*******.4.1.3052.1*******.1.4

espcHumidVHighHumid OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold for this humidity sensor"
    ::= { esPointConfigHumidEntry 5 }
    --*******.4.1.3052.1*******.1.5

espcHumidVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions for this humidity sensor"
    ::= { esPointConfigHumidEntry 6 }
    --*******.4.1.3052.1*******.1.6

espcHumidVHighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number for this humidity sensor"
    ::= { esPointConfigHumidEntry 7 }
    --*******.4.1.3052.1*******.1.7

espcHumidVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class for this humidity sensor"
    ::= { esPointConfigHumidEntry 8 }
    --*******.4.1.3052.1*******.1.8

espcHumidHighHumid OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold for this humidity sensor"
    ::= { esPointConfigHumidEntry 9 }
    --*******.4.1.3052.1*******.1.9

espcHumidHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions for this humidity sensor"
    ::= { esPointConfigHumidEntry 10 }
    --*******.4.1.3052.1*******.1.10

espcHumidHighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number for this humidity sensor"
    ::= { esPointConfigHumidEntry 11 }
    --*******.4.1.3052.1*******.1.11

espcHumidHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class for this humidity sensor"
    ::= { esPointConfigHumidEntry 12 }
    --*******.4.1.3052.1*******.1.12

espcHumidNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal actions for this humidity sensor"
    ::= { esPointConfigHumidEntry 13 }
    --*******.4.1.3052.1*******.1.13

espcHumidNormalTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal trap number for this humidity sensor"
    ::= { esPointConfigHumidEntry 14 }
    --*******.4.1.3052.1*******.1.14

espcHumidNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal class for this humidity sensor"
    ::= { esPointConfigHumidEntry 15 }
    --*******.4.1.3052.1*******.1.15

espcHumidLowHumid OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold for this humidity sensor"
    ::= { esPointConfigHumidEntry 16 }
    --*******.4.1.3052.1*******.1.16

espcHumidLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions for this humidity sensor"
    ::= { esPointConfigHumidEntry 17 }
    --*******.4.1.3052.1*******.1.17

espcHumidLowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number for this humidity sensor"
    ::= { esPointConfigHumidEntry 18 }
    --*******.4.1.3052.1*******.1.18

espcHumidLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class for this humidity sensor"
    ::= { esPointConfigHumidEntry 19 }
    --*******.4.1.3052.1*******.1.19

espcHumidVLowHumid OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold for this humidity sensor"
    ::= { esPointConfigHumidEntry 20 }
    --*******.4.1.3052.1*******.1.20

espcHumidVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions for this humidity sensor"
    ::= { esPointConfigHumidEntry 21 }
    --*******.4.1.3052.1*******.1.21

espcHumidVLowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event trap number for this humidity sensor"
    ::= { esPointConfigHumidEntry 22 }
    --*******.4.1.3052.1*******.1.22

espcHumidVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class for this humidity sensor"
    ::= { esPointConfigHumidEntry 23 }
    --*******.4.1.3052.1*******.1.23


esPointConfigAITable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPointConfigAnalogInput
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes 3 basic attributes of analog input
        points.  A point is a particular sensor on an EventSensor.
        The 3 point attributes organizes in this table are:
        1. the configuration item (e.g., enable, name, etc.)
        2. the eventsensor on which this point resides
        3. the point number

        Points are referenced by a point config index.  The point
        config index is a string of 2 numbers separated by a period.
        It contains all the information necessary for getting a
        piece of data off an event sensor within the context of this
        analog input point config table ; namely, which EventSensor
        and which sensor.
        "
    ::= { eventSensorPointConfig 5 }
    --*******.4.1.3052.********

esPointConfigAIEntry OBJECT-TYPE
    SYNTAX ESPointConfigAnalogInput
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point config analog input table"
    INDEX { espcHumidIndexES, espcHumidIndexPoint }
    ::= { esPointConfigAITable 1 }
    --*******.4.1.3052.********.1

espcAIIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point config index is a string of 2 numbers separated
        by a period.  It contains all the information
        necessary for getting a piece of data off an event
        sensor within the the context of the eventsensor point
        config contact closure table; namely, which EventSensor
        and which sensor (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.
        "
    ::= { esPointConfigAIEntry 1 }
    --*******.4.1.3052.********.1.1

espcAIIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with espcAIIndexES (which EventSensor)
        uniquely defines each point attached to a unit within the
        context of contact closure point configuration."
    ::= { esPointConfigAIEntry 2 }
    --*******.4.1.3052.********.1.2

espcAIEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF for this analog input sensor"
    ::= { esPointConfigAIEntry 3 }
    --*******.4.1.3052.********.1.3

espcAIDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband for this analog input sensor"
    ::= { esPointConfigAIEntry 5 }
    --*******.4.1.3052.********.1.5

espcAIVhighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold (decivolts or tenths of
        milliamps) for this analog input sensor"
    ::= { esPointConfigAIEntry 6 }
    --*******.4.1.3052.********.1.6

espcAIVhighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions for this analog input sensor"
    ::= { esPointConfigAIEntry 7 }
    --*******.4.1.3052.********.1.7

espcAIVhighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number for this analog input sensor"
    ::= { esPointConfigAIEntry 8 }
    --*******.4.1.3052.********.1.8

espcAIVhighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class for this analog input sensor"
    ::= { esPointConfigAIEntry 9 }
    --*******.4.1.3052.********.1.9

espcAIHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold (decivolts or tenths of milliamps) for
        this analog input sensor"
    ::= { esPointConfigAIEntry 10 }
    --*******.4.1.3052.********.1.10

espcAIHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions for this analog input sensor"
    ::= { esPointConfigAIEntry 11 }
    --*******.4.1.3052.********.1.11

espcAIHighTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number for this analog input sensor"
    ::= { esPointConfigAIEntry 12 }
    --*******.4.1.3052.********.1.12

espcAIHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class for this analog input sensor"
    ::= { esPointConfigAIEntry 13 }
    --*******.4.1.3052.********.1.13

espcAINormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal actions for this analog input sensor"
    ::= { esPointConfigAIEntry 14 }
    --*******.4.1.3052.********.1.14

espcAINormalTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal trap number for this analog input sensor"
    ::= { esPointConfigAIEntry 15 }
    --*******.4.1.3052.********.1.15

espcAINormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return-to-normal class for this analog input sensor"
    ::= { esPointConfigAIEntry 16 }
    --*******.4.1.3052.********.1.16

espcAILowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold (decivolts or tenths of milliamps) for
        this analog input sensor"
    ::= { esPointConfigAIEntry 17 }
    --*******.4.1.3052.********.1.17

espcAILowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions for this analog input sensor"
    ::= { esPointConfigAIEntry 18 }
    --*******.4.1.3052.********.1.18

espcAILowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number for this analog input sensor"
    ::= { esPointConfigAIEntry 19 }
    --*******.4.1.3052.********.1.19

espcAILowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class for this analog input sensor"
    ::= { esPointConfigAIEntry 20 }
    --*******.4.1.3052.********.1.20

espcAIVlowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold (decivolts or tenths of milliamps)
        for this analog input sensor"
    ::= { esPointConfigAIEntry 21 }
    --*******.4.1.3052.********.1.21

espcAIVlowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions for this analog input sensor"
    ::= { esPointConfigAIEntry 22 }
    --*******.4.1.3052.********.1.22

espcAIVlowTrapnum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions for this analog input sensor"
    ::= { esPointConfigAIEntry 23 }
    --*******.4.1.3052.********.1.23

espcAIVlowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class for this analog input sensor"
    ::= { esPointConfigAIEntry 24 }
    --*******.4.1.3052.********.1.24

espcAIConvUnitName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Converstion unit name for this analog input sensor"
    ::= { esPointConfigAIEntry 25 }
    --*******.4.1.3052.********.1.25

espcAIConvHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Conversion high value for this analog input sensor"
    ::= { esPointConfigAIEntry 26 }
    --*******.4.1.3052.********.1.26

espcAIConvHighUnit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Conversion high unit value for this analog input sensor"
    ::= { esPointConfigAIEntry 27 }
    --*******.4.1.3052.********.1.27

espcAIConvLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Conversion low value for this analog input sensor"
    ::= { esPointConfigAIEntry 29 }
    --*******.4.1.3052.********.1.29

espcAIConvLowUnit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Conversion unit low unit value for this analog input sensor"
    ::= { esPointConfigAIEntry 30 }
    --*******.4.1.3052.********.1.30

espcAIDisplayFormat OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Analog input display format"
    ::= { esPointConfigAIEntry 32 }
    --*******.4.1.3052.********.1.32

esPointConfigOutputTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ESPointConfigOutput
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes configuration items for output
        points.  A point is a particular sensor on an EventSensor.
        The 3 point attributes organized in this table are:
        1. the configuration item (e.g., enable, name, etc.)
        2. the eventsensor on which this point resides
        3. the point number

        Points are referenced by a point config index.  The point
        config index is a string of 2 numbers separated by a period.
        It contains all the information necessary for getting a
        piece of data off an event sensor within the context of this
        output config table ; namely, which EventSensor
        and which sensor.
        "
    ::= { eventSensorPointConfig 6 }
    --*******.4.1.3052.********

esPointConfigOutputEntry OBJECT-TYPE
    SYNTAX ESPointConfigOutput
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for EventSensor point config output table"
    INDEX { espcOutputIndexES, espcOutputIndexPoint }
    ::= { esPointConfigOutputTable 1 }
    --*******.4.1.3052.********.1

espcOutputIndexES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that refers to an
        EventSensor.

        The point config index is a string of 2 numbers separated
        by a period.  It contains all the information
        necessary for getting a piece of data off an event
        sensor within the the context of the eventsensor point
        config contact closure table; namely, which EventSensor
        and which sensor (a.k.a. point).

        This object's allowable values are 1 through 16,
        and 200.  200 always refers to the EventSensor
        inside the unit.  1 through 16 refer to additional
        attached EventSensors.
        "
    ::= { esPointConfigOutputEntry 1 }
    --*******.4.1.3052.********.1.1

espcOutputIndexPoint OBJECT-TYPE
    SYNTAX Integer32 (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The part of the point index that delineates which
        sensor on the EventSensor.

        So this combined with espcOutputIndexES (which EventSensor)
        uniquely defines each point attached to a unit within the
        context of output point configuration."
    ::= { esPointConfigOutputEntry 2 }
    --*******.4.1.3052.********.1.2

espcOutputEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enables the output event for this output.  This has no effect
        on whether the relay changes state, only on whether a state change
        triggers an event."
    ::= { esPointConfigOutputEntry 3 }
    --*******.4.1.3052.********.1.3

espcOutputActiveState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Which state is considered the active state: energized/de-energized."
    ::= { esPointConfigOutputEntry 4 }
    --*******.4.1.3052.********.1.4

espcOutputType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output type: RELAY, POWER, SSR, TTL."
    ::= { esPointConfigOutputEntry 5 }
    --*******.4.1.3052.********.1.5

espcOutputAliasValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output alias corresponding to its event state."
    ::= { esPointConfigOutputEntry 6 }
    --*******.4.1.3052.********.1.6

espcOutputAliasColor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Output color corresponding to its event state."
    ::= { esPointConfigOutputEntry 7 }
    --*******.4.1.3052.********.1.7

espcOutputActiveAlias OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Alias for the output active state."
    ::= { esPointConfigOutputEntry 10 }
    --*******.4.1.3052.********.1.10

espcOutputActiveColor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Output color (as rendered in the web UI, in RGB hex format) corresponding
        to the output active state."
    ::= { esPointConfigOutputEntry 11 }
    --*******.4.1.3052.********.1.11

espcOutputActiveActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the output active event."
    ::= { esPointConfigOutputEntry 12 }
    --*******.4.1.3052.********.1.12

espcOutputActiveTrapnum OBJECT-TYPE
    SYNTAX Integer32 (150..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the output active event."
    ::= { esPointConfigOutputEntry 13 }
    --*******.4.1.3052.********.1.13

espcOutputActiveClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class (severity) for the output active event."
    ::= { esPointConfigOutputEntry 14 }
    --*******.4.1.3052.********.1.14

espcOutputInactiveAlias OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Alias for the output inactive state."
    ::= { esPointConfigOutputEntry 20 }
    --*******.4.1.3052.********.1.20

espcOutputInactiveColor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Output color (as rendered in the web UI, in RGB hex format) corresponding
        to the output inactive state."
    ::= { esPointConfigOutputEntry 21 }
    --*******.4.1.3052.********.1.21

espcOutputInactiveActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the output inactive event."
    ::= { esPointConfigOutputEntry 22 }
    --*******.4.1.3052.********.1.22

espcOutputInactiveTrapnum OBJECT-TYPE
    SYNTAX Integer32 (150..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the output inactive event."
    ::= { esPointConfigOutputEntry 23 }
    --*******.4.1.3052.********.1.23

espcOutputInactiveClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class (severity) for the output inactive event."
    ::= { esPointConfigOutputEntry 24 }
    --*******.4.1.3052.********.1.24


--***************************************************************************************
--Serial Port Section       2.3
--***************************************************************************************

numberPorts OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Number of RS-232 ports found.
        Range: 2 or 4"
    ::= { serialPorts 1 }
    --*******.4.1.3052.********

portConfigTable OBJECT-TYPE
    SYNTAX SEQUENCE OF PortConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Port config table"
    ::= { serialPorts 2 }
    --*******.4.1.3052.********

portConfigEntry OBJECT-TYPE
    SYNTAX PortConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Port config entry"
    INDEX { portConfigIndex }
    ::= { portConfigTable 1 }
    --*******.4.1.3052.12.*******

portConfigIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for ports"
    ::= { portConfigEntry 1 }
    --*******.4.1.3052.12.*******.1

portConfigBaud OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Baud rate, 19200, 9600, etc."
    ::= { portConfigEntry 2 }
    --*******.4.1.3052.12.*******.2

portConfigDataFormat OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Value representing word length, parity, and
        stop bits: 0=8N1, 1=7E1, 2=7O1, 3=7N1, 4=8O2"
    ::= { portConfigEntry 3 }
    --*******.4.1.3052.12.*******.3

portConfigStripPtOutputLfs OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Strip passthrough LFs sent to device on this port:
        1=yes, 0=no."
    ::= { portConfigEntry 4 }
    --*******.4.1.3052.12.*******.4

portConfigStripPtInputLfs OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Strip passthrough LFs received from device on this
        port: 1=yes, 0=no."
    ::= { portConfigEntry 5 }
    --*******.4.1.3052.12.*******.5

portConfigMaskEnable OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "1=Use the character mask on this port.  0=Do
        not use the mask.  When the mask is enabled,
        the following ASCII characters are ignored
        on the port:
        0, 1, 4-9, 11, 12, 14-31, 128-255.
        When the mask is disabled, all characters are
        accepted on the port."
    ::= { portConfigEntry 7 }
    --*******.4.1.3052.12.*******.7

portConfigDAEnable OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "1=Enable the Data Alarm Evaluator for data
        on this port.  0=Do not evaluate data on this
        port."
    ::= { portConfigEntry 8 }
    --*******.4.1.3052.12.*******.8

portConfigStoreAlarmsDPT OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: store alarms received during passthrough"
    ::= { portConfigEntry 9 }
    --*******.4.1.3052.12.*******.9

portConfigRecordTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Timeout after last unterminated data received upon
        which data collected so far is stored as a record."
    ::= { portConfigEntry 10 }
    --*******.4.1.3052.12.*******.10

portConfigDataType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "How the port interprets incoming data: ASCII/BINARY"
    ::= { portConfigEntry 11 }
    --*******.4.1.3052.12.*******.11

portConfigEtxToCRLF OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF whether the port translates incoming ETX to CRLF"
    ::= { portConfigEntry 12 }
    --*******.4.1.3052.12.*******.12

portConfigMLREnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Whether Multiline Records are collected on this port."
    ::= { portConfigEntry 13 }
    --*******.4.1.3052.12.*******.13

portConfigMLRStartField1Pos OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Position of MLR start field 1"
    ::= { portConfigEntry 14 }
    --*******.4.1.3052.12.*******.14

portConfigMLRStartField1Text OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Text to match MLR start field 1"
    ::= { portConfigEntry 15 }
    --*******.4.1.3052.12.*******.15

portConfigMLRStartField2Pos OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Position of MLR start field 2"
    ::= { portConfigEntry 16 }
    --*******.4.1.3052.12.*******.16

portConfigMLRStartField2Text OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Text to match MLR start field 2"
    ::= { portConfigEntry 17 }
    --*******.4.1.3052.12.*******.17

portConfigMLRNumLinesBefore OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of lines before the starting line which should
        be included in the MLR"
    ::= { portConfigEntry 18 }
    --*******.4.1.3052.12.*******.18

portConfigMLREndDetection OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "How the MLR parser determines what ends an MLR: by FORMULA,
        number of lines received so far (COUNT), or number of blank
        lines received so far (BLANKS)"
    ::= { portConfigEntry 19 }
    --*******.4.1.3052.12.*******.19

portConfigMLRLineCount OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of lines in the MLR (used by the end detection mode)"
    ::= { portConfigEntry 20 }
    --*******.4.1.3052.12.*******.20

portConfigMLREndField1Pos OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Position of MLR end field 1"
    ::= { portConfigEntry 21 }
    --*******.4.1.3052.12.*******.21

portConfigMLREndField1Text OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Text to match MLR end field 1"
    ::= { portConfigEntry 22 }
    --*******.4.1.3052.12.*******.22

portConfigMLREndField2Pos OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Position of MLR end field 2"
    ::= { portConfigEntry 23 }
    --*******.4.1.3052.12.*******.23

portConfigMLREndField2Text OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Text to match MLR end field 2"
    ::= { portConfigEntry 24 }
    --*******.4.1.3052.12.*******.24

portConfigMLRUseComplexRules OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF MLR parser uses complex rules (field possitions/
        formulas, etc.)"
    ::= { portConfigEntry 25 }
    --*******.4.1.3052.12.*******.25

portConfigBufferPT OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF records received during passthrough mode are
        buffered/stored in the database"
    ::= { portConfigEntry 26 }
    --*******.4.1.3052.12.*******.26

portConfigId OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The text identifer for the port"
    ::= { portConfigEntry 27 }
    --*******.4.1.3052.12.*******.27

portConfigMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Mode of the serial port."
    ::= { portConfigEntry 28 }
    --*******.4.1.3052.12.*******.28

portConfigHsk OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Handshaking mode of the serial port."
    ::= { portConfigEntry 29 }
    --*******.4.1.3052.12.*******.29


--***************************************************************************************
--Network Section   2.4
--***************************************************************************************

eth1Mode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
         "Mode of the Ethernet1 interface: STATIC,DHCP"
    ::= { ethernet1 1 }
    --*******.4.1.3052.********.1.1.1

eth1Address OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet1 IP address"
    ::= { ethernet1 2 }
    --*******.4.1.3052.********.1.1.2

eth1SubnetMask OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet1 subnet mask"
    ::= { ethernet1 3 }
    --*******.4.1.3052.********.1.1.3

eth1Router OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet1 router"
    ::= { ethernet1 4 }
    --*******.4.1.3052.********.1.1.4

eth1MAC OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Ethernet1 MAC"
    ::= { ethernet1 6 }
    --*******.4.1.3052.********.1.1.6


eth2Mode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
         "Mode of the Ethernet2 interface: STATIC,DHCP"
    ::= { ethernet2 1 }
    --*******.4.1.3052.********.1.2.1

eth2Address OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet2 IP address"
    ::= { ethernet2 2 }
    --*******.4.1.3052.********.1.2.2

eth2SubnetMask OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet2 subnet mask"
    ::= { ethernet2 3 }
    --*******.4.1.3052.********.1.2.3

eth2Router OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ethernet2 router"
    ::= { ethernet2 4 }
    --*******.4.1.3052.********.1.2.4

eth2MAC OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Ethernet2 MAC"
    ::= { ethernet2 6 }
    --*******.4.1.3052.********.1.2.6


defaultRouter OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The default router/gateway"
    ::= { network 2 }
    --*******.4.1.3052.********

-- DNS

dnsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF DNSConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "DNS server address table"
    ::= { network 3 }
    --*******.4.1.3052.********

dnsEntry OBJECT-TYPE
    SYNTAX DNSConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "DNS server address entry"
    INDEX { dnsIndex }
    ::= { dnsTable 1 }
    --*******.4.1.3052.********.1

dnsIndex OBJECT-TYPE
    SYNTAX Integer32 (1..2)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for DNS server address"
    ::= { dnsEntry 1 }
    --*******.4.1.3052.********.1.1

dnsAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "DNS server address"
    ::= { dnsEntry 2 }
    --*******.4.1.3052.********.1.2

hostname OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The hostname of the unit"
    ::= { network 4 }
    --*******.4.1.3052.********

hostTable OBJECT-TYPE
    SYNTAX SEQUENCE OF HostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Static host table"
    ::= { network 5 }
    --*******.4.1.3052.********

hostEntry OBJECT-TYPE
    SYNTAX HostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Static host entry"
    INDEX { hostIndex }
    ::= { hostTable 1 }
    --*******.4.1.3052.********.1

hostIndex OBJECT-TYPE
    SYNTAX Integer32 (1..2)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for static host"
    ::= { hostEntry 1 }
    --*******.4.1.3052.********.1.1

hostDeclaration OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Static host declaration string (i.e. 'address hostname')"
    ::= { hostEntry 2 }
    --*******.4.1.3052.********.1.2

-- Network Command Processor

ncpDuplex OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "For network command processor: 1=Input characters are echoed; 0=no echo."
    ::= { network 6 }
    --*******.4.1.3052.********

ncpTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Network command processor inactivity timeout"
    ::= { network 7 }
    --*******.4.1.3052.********

-- SNMP

snmpAgentEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF for snmp agent"
    ::= { snmp 1 }
    --*******.4.1.3052.********.1

snmpNotificationCaptureEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF unit receives and stores SNMP traps/informs"
    ::= { snmp 5 }
    --*******.4.1.3052.********.5

snmpNotificationCaptureFile OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "File to which unit stores received SNMP traps/informs"
    ::= { snmp 6 }
    --*******.4.1.3052.********.6

snmpNtfnAttempts OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum of times the unit attempts to send a notification
        (trap/inform) in 1 cycle"
    ::= { snmpNotificationTx 1 }
    --*******.4.1.3052.********.7.1

snmpNtfnTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of seconds between retries of sending a notification"
    ::= { snmpNotificationTx 2 }
    --*******.4.1.3052.********.7.2

snmpNtfnCycles OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of times the unit attempts a group of attempts to send
        a notification (a cycle)"
    ::= { snmpNotificationTx 3 }
    --*******.4.1.3052.********.7.3

snmpNtfnSnooze OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minutes between cycles"
    ::= { snmpNotificationTx 4 }
    --*******.4.1.3052.********.7.4


-- FTP

ftpPushEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable FTP push for REGULAR (protocol: FTP) or SECURE (protocol:
        SFTP) operation, or disable it (OFF)"
    ::= { ftpPush 1 }
    --*******.4.1.3052.********.1

ftpPushServer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "FTP server to which data should be pushed"
    ::= { ftpPush 2 }
    --*******.4.1.3052.********.2

ftpPushAccount OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "FTP push account"
    ::= { ftpPush 5 }
    --*******.4.1.3052.********.5

ftpPushDirectory OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "FTP push directory"
    ::= { ftpPush 6 }
    --*******.4.1.3052.********.6

ftpPushperiod OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minutes between pushes"
    ::= { ftpPush 7 }
    --*******.4.1.3052.********.7

ftpPushPushFileTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FTPPushPushFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of files to push"
    ::= { ftpPush 8 }
    --*******.4.1.3052.********.8

ftpPushPushFileEntry OBJECT-TYPE
    SYNTAX FTPPushPushFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of files to push"
    INDEX { ftpPushPushFileIndex }
    ::= { ftpPushPushFileTable 1 }
    --*******.4.1.3052.********.8.1

ftpPushPushFileIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of files to push"
    ::= { ftpPushPushFileEntry 1 }
    --*******.4.1.3052.********.8.1.1

ftpPushPushFile OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF push this local FILEx"
    ::= { ftpPushPushFileEntry 2 }
    --*******.4.1.3052.********.8.1.2

ftpPushPushAudit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF push the audit log"
    ::= { ftpPush 9 }
    --*******.4.1.3052.********.9

ftpPushPushAlarms OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF push the alarms/events file"
    ::= { ftpPush 10 }
    --*******.4.1.3052.********.10

ftpPushRemoteFileTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FTPPushRemoteFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of remote names of files to push"
    ::= { ftpPush 11 }
    --*******.4.1.3052.********.11

ftpPushRemoteFileEntry OBJECT-TYPE
    SYNTAX FTPPushRemoteFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of remote names of files to push"
    INDEX { ftpPushRemoteFileIndex }
    ::= { ftpPushRemoteFileTable 1 }
    --*******.4.1.3052.********.11.1

ftpPushRemoteFileIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of remote names of files to push"
    ::= { ftpPushRemoteFileEntry 1 }
    --*******.4.1.3052.********.11.1

ftpPushRemoteFileName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "remote name of pushed FILEx (name on server)"
    ::= { ftpPushRemoteFileEntry 2 }
    --*******.4.1.3052.********.11.2

ftpPushRemoteAlarmName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Filename on server for alarms"
    ::= { ftpPush 12 }
    --*******.4.1.3052.********.12

ftpPushPassive OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF use ftp passive mode"
    ::= { ftpPush 13 }
    --*******.4.1.3052.********.13

ftpPushIncludeDate OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include the date in the name of the pushed file"
    ::= { ftpPush 14 }
    --*******.4.1.3052.********.14

ftpPushIncludeTime OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include the time in the name of the pushed file"
    ::= { ftpPush 15 }
    --*******.4.1.3052.********.15

ftpPushIncludeSeq OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include a sequence number in the name of the pushed file"
    ::= { ftpPush 16 }
    --*******.4.1.3052.********.16

ftpPushPermissions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Unix permissions to apply to pushed files (octal notation)."
    ::= { ftpPush 17 }
    --*******.4.1.3052.********.17


pppDialEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF PPP dialout enabled"
    ::= { pppDial 1 }
    --*******.4.1.3052.********0.1.1

pppDialNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "phone number to dial"
    ::= { pppDial 2 }
    --*******.4.1.3052.********0.1.2

pppDialIdleTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Seconds of inactivity after which the unit disconnects"
    ::= { pppDial 5 }
    --*******.4.1.3052.********0.1.5

pppDialRetries OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of dialing retries for PPP"
    ::= { pppDial 6 }
    --*******.4.1.3052.********0.1.6

pppDialCDTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of seconds to wait after dialing for CD before
        giving up"
    ::= { pppDial 7 }
    --*******.4.1.3052.********0.1.7

pppDialLoginTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Seconds to wait to negotiate PPP after connecting"
    ::= { pppDial 8 }
    --*******.4.1.3052.********0.1.8

pppDialMdmInit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Modem init string to use when dialing PPP"
    ::= { pppDial 9 }
    --*******.4.1.3052.********0.1.9

pppDialSuggestIP OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "IP address to suggest as the unit's end (local end) of the
        PPP link"
    ::= { pppDial 10 }
    --*******.4.1.3052.********0.1.10

pppDialPlainLogin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF look for login/password prompt before negotiating
        PPP"
    ::= { pppDial 11 }
    --*******.4.1.3052.********0.1.11

pppHostEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Unit accepts PPP negotiation"
    ::= { pppHost 1 }
    --*******.4.1.3052.********0.2.1

pppHostIdleTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Seconds of inactivity after which PPP-hosted link will
        terminate"
    ::= { pppHost 2 }
    --*******.4.1.3052.********0.2.2

pppHostLocalIP OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Local IP address to negotiate"
    ::= { pppHost 3 }
    --*******.4.1.3052.********0.2.3

pppHostRemoteIP OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Remote IP address to hand out to PPP peer"
    ::= { pppHost 4 }
    --*******.4.1.3052.********0.2.4

-- Routing

pppRoutingEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF unit routes IP traffic from PPP/Wireless to Ethernet"
    ::= { routing 1 }
    --*******.4.1.3052.********1.1

routingInterface OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When pppRoutingEnable is enabled, this is the
        Ethernet interface to which IP frames received on PPP are
        routed"
    ::= { routing 4 }
    --*******.4.1.3052.********1.4

-- IP Restrictions

ipRestrictionTable OBJECT-TYPE
    SYNTAX SEQUENCE OF IPRestrictionConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of IP restrictions"
    ::= { ipRestriction 1 }
    --*******.4.1.3052.********2.1.1

ipRestrictionEntry OBJECT-TYPE
    SYNTAX IPRestrictionConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of IP restrictions"
    INDEX { ipRestrictionIndex }
    ::= { ipRestrictionTable 1 }
    --*******.4.1.3052.********2.1.1

ipRestrictionIndex OBJECT-TYPE
    SYNTAX Integer32 (1..8)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of IP restrictions"
    ::= { ipRestrictionEntry 1 }
    --*******.4.1.3052.12.2.4.********.1

ipRestrictionEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this IP restriction"
    ::= { ipRestrictionEntry 2 }
    --*******.4.1.3052.12.2.4.********.2

ipRestrictionMask OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "IP restriction mask"
    ::= { ipRestrictionEntry 3 }
    --*******.4.1.3052.12.2.4.********.3


-- RTS


rtsFileTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RTSFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of RTS files"
    ::= { rts 1 }
    --*******.4.1.3052.********3.1

rtsFileEntry OBJECT-TYPE
    SYNTAX RTSFileConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of RTS files"
    INDEX { rtsFileIndex }
    ::= { rtsFileTable 1 }
    --*******.4.1.3052.********3.1

rtsFileIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of RTS files"
    ::= { rtsFileEntry 1 }
    --*******.4.1.3052.********3.1.1.1

rtsFileMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "RTS mode (OFF,LISTEN,PUSH) for FILEx"
    ::= { rtsFileEntry 2 }
    --*******.4.1.3052.********3.1.1.2

rtsFileShowAnswer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Show answer string upon RTS connect for FILEx"
    ::= { rtsFileEntry 3 }
    --*******.4.1.3052.********3.1.1.3

rtsFileReqXON OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF require XON to start flow for FILEx"
    ::= { rtsFileEntry 4 }
    --*******.4.1.3052.********3.1.1.4

rtsFileTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Idle connection close timer (0 means no timeout) for FILEx"
    ::= { rtsFileEntry 5 }
    --*******.4.1.3052.********3.1.1.5

rtsFileEmptyClose OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF close the socket when FILEx is empty"
    ::= { rtsFileEntry 6 }
    --*******.4.1.3052.********3.1.1.6

rtsFilePushHost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Host to which FILEx should be pushed via RTS"
    ::= { rtsFileEntry 7 }
    --*******.4.1.3052.********3.1.1.7

rtsFilePushPort OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TCP port to which FILEx should be pushed via RTS"
    ::= { rtsFileEntry 8 }
    --*******.4.1.3052.********3.1.1.8

rtsFilePushRetryTimer OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Retry timer (seconds) for retrying failed RTS push connect
        attempts"
    ::= { rtsFileEntry 9 }
    --*******.4.1.3052.********3.1.1.9

-- RTS Alarms

rtsAlarmsMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "RTS mode (OFF,LISTEN,PUSH) for alarms file"
    ::= { rtsAlarms 1 }
    --*******.4.1.3052.********3.2.1

rtsAlarmsShowAnswer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Show answer string upon RTS connect for alarms file"
    ::= { rtsAlarms 2 }
    --*******.4.1.3052.********3.2.2

rtsAlarmsReqXON OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF require XON to start flow for alarms file"
    ::= { rtsAlarms 3 }
    --*******.4.1.3052.********3.2.3

rtsAlarmsTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Idle connection close timer (0 means no timeout) for alarms
        file"
    ::= { rtsAlarms 4 }
    --*******.4.1.3052.********3.2.4

rtsAlarmsEmptyClose OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF close the socket when this file is empty for alarms
        file"
    ::= { rtsAlarms 5 }
    --*******.4.1.3052.********3.2.5

rtsAlarmsPushHost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Host to which alarms file should be pushed via RTS"
    ::= { rtsAlarms 6 }
    --*******.4.1.3052.********3.2.6

rtsAlarmsPushPort OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TCP port to which alarms file should be pushed via RTS"
    ::= { rtsAlarms 7 }
    --*******.4.1.3052.********3.2.7

rtsAlarmsPushRetryTimer OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Retry timer (seconds) for retrying failed RTS push connect
        attempts"
    ::= { rtsAlarms 8 }
    --*******.4.1.3052.********3.2.8

-- Traps

trapIncludeDateTime OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include date and time in stock trap message"
    ::= { trapInclude 1 }
    --*******.4.1.3052.********4.1.1

trapIncludeSiteName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include site name in stock trap message"
    ::= { trapInclude 2 }
    --*******.4.1.3052.********4.1.2

trapIncludeSensorID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include sensor ID in stock trap message"
    ::= { trapInclude 3 }
    --*******.4.1.3052.********4.1.3

trapIncludeUDName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include user defined name in stock trap message"
    ::= { trapInclude 4 }
    --*******.4.1.3052.********4.1.4

trapIncludeUDState OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include user defined state in stock trap message"
    ::= { trapInclude 5 }
    --*******.4.1.3052.********4.1.5

trapIncludeSourceAddress OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF include source IP address in stock trap message"
    ::= { trapInclude 6 }
    --*******.4.1.3052.********4.1.6

trapAuthFailEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable trap sent upon SNMP authentication failure"
    ::= { trap 2 }
    --*******.4.1.3052.********4.2

-- Route testing

routeTestEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable route testing"
    ::= { routeTest 1 }
    --*******.4.1.3052.********5.1


routeTestPeriod OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The period which a route is considered valid"
    ::= { routeTest 2 }
    --*******.4.1.3052.********5.2

routeTestAddressTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RouteTestConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of route testing addresses"
    ::= { routeTest 3 }
    --*******.4.1.3052.********5.3

routeTestAddressEntry OBJECT-TYPE
    SYNTAX RouteTestConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of RTS files"
    INDEX { routeTestAddressIndex }
    ::= { routeTestAddressTable 1 }
    --*******.4.1.3052.********5.3.1

routeTestAddressIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of RTS files"
    ::= { routeTestAddressEntry 1 }
    --*******.4.1.3052.********5.3.1.1

routeTestAddress OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Address to ping to see if the route to it is valid"
    ::= { routeTestAddressEntry 2 }
    --*******.4.1.3052.********5.3.1.2


-- Wireless


wirelessMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless mode (OFF,PERMANENT-EDGE)"
    ::= { wireless 1 }
    --*******.4.1.3052.********6.1

wirelessAPN OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless access point name"
    ::= { wireless 2 }
    --*******.4.1.3052.********6.2

wirelessIdleTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless idle timeout"
    ::= { wireless 3 }
    --*******.4.1.3052.********6.3

wirelessDRE OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Default route enable for wireless network interface.
         When this is ON, the wireless link, if up, becomes
         the default route, overriding the default route setting
         (defaultRouter object).  When OFF, the default route
         setting controls the default route and the wireless link
         does not become the default route."
    ::= { wireless 9 }
    --*******.4.1.3052.********6.9

wirelessPPPUsername OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Username for PPP-based wireless modem connections."
    ::= { wireless 10 }
    --*******.4.1.3052.********6.10

wirelessFirewall OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable wireless modem firewall."
    ::= { wireless 11 }
    --*******.4.1.3052.********6.11

wirelessKeepaliveThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minutes of no RX activity before sending keep-alive."
    ::= { wireless 12 }
    --*******.4.1.3052.********6.12

wirelessPPPDebug OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable wireless modem PPP debug."
    ::= { wireless 13 }
    --*******.4.1.3052.********6.13

wirelessConnEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable wireless modem connectivity check."
    ::= { wirelessConnectivity 1 }
    --*******.4.1.3052.********6.20.1

wirelessConnCheckInterval OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless connectivity check interval in minutes."
    ::= { wirelessConnectivity 2 }
    --*******.4.1.3052.********6.20.2

wirelessConnFailThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless connectivity fail threshold."
    ::= { wirelessConnectivity 3 }
    --*******.4.1.3052.********6.20.3

wirelessConnIP1 OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless connectivity check IP address 1."
    ::= { wirelessConnectivity 10 }
    --*******.4.1.3052.********6.20.10

wirelessConnIP2 OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Wireless connectivity check IP address 2."
    ::= { wirelessConnectivity 11 }
    --*******.4.1.3052.********6.20.11


-- Email


emailServer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "SMTP server to which the unit sends email"
    ::= { email 1 }
    --*******.4.1.3052.********7.1

emailDomain OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Domain to use in recipient address"
    ::= { email 2 }
    --*******.4.1.3052.********7.2

emailAuthEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable SMTP authentication"
    ::= { email 3 }
    --*******.4.1.3052.********7.3

-- Advanced network config

arpFilter OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ARP filter code"
    ::= { netAdvanced 1 }
    --*******.4.1.3052.********8.1


-- Web interface

webEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF for the web interface"
    ::= { web 1 }
    --*******.4.1.3052.********9.1

webPort OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TCP port on which web interface listens"
    ::= { web 2 }
    --*******.4.1.3052.********9.2

webTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Web interface session timeout (seconds)"
    ::= { web 3 }
    --*******.4.1.3052.********9.3


-- Ethernet Expansion

ethExpanIp OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "IPv4 address of the unit reachable by nodes on the network interface
        consisting of the Ethernet Expansion Card(s). If this is default
        (0.0.0.0), then each card will still convey Ethernet traffic
        (acting as Ethernet switch) among its nodes but those nodes will not
        be able to communicate with the unit."
    ::= { ethExpan 1 }
    --*******.4.1.3052.********0.1

ethExpanMask OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "IPv4 subnet mask for the Ethernet Expansion network interface."
    ::= { ethExpan 2 }
    --*******.4.1.3052.********0.2

ethExpanCardTable OBJECT-TYPE
    SYNTAX SEQUENCE OF EthernetExpansionCardConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of Ethernet Expansion cards"
    ::= { ethExpan 3 }
    --*******.4.1.3052.********0.3

ethExpanCardEntry OBJECT-TYPE
    SYNTAX EthernetExpansionCardConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of Ethernet Expansion cards"
    INDEX { ethExpanCardIndex }
    ::= { ethExpanCardTable 1 }
    --*******.4.1.3052.********0.3.1

ethExpanCardIndex OBJECT-TYPE
    SYNTAX Integer32 (1..2)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of Ethernet Expansion cards"
    ::= { ethExpanCardEntry 1 }
    --*******.4.1.3052.********0.3.1.1

ethExpanCardMAC OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "MAC address for each Ethernet Expansion Card that is bridged
        together to form the Ethernet Expansion network interface."
    ::= { ethExpanCardEntry 2 }
    --*******.4.1.3052.********0.3.1.2

ethExpanDHCPStartIp OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Starting IP address for serving DHCP addresses on the Ethernet
        Expansion network interface. If this is default (0.0.0.0) then
        the unit will not run DHCP to serve addresses."
    ::= { ethExpanDHCP 1 }
    --*******.4.1.3052.********0.6.1

ethExpanDHCPLeaseTime OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Lease time for Ethernet Expansion DHCP clients in minutes."
    ::= { ethExpanDHCP 2 }
    --*******.4.1.3052.********0.6.2



-- IPv6

ipv6DefaultRouter OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IPv6 default router in use."
    ::= { ipv6 1 }
    --*******.4.1.3052.********1.1



-- CPE table

cpeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF CPEConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of CPEs"
    ::= { cpe 1 }
    --*******.4.1.3052.*********.1

cpeEntry OBJECT-TYPE
    SYNTAX CPEConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of CPEs"
    INDEX { cpeIndex }
    ::= { cpeTable 1 }
    --*******.4.1.3052.*********.1.1

cpeIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of CPEs"
    ::= { cpeEntry 1 }
    --*******.4.1.3052.*********.1.1.1

cpeHost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "CPE host (IP address or DNS name)"
    ::= { cpeEntry 2 }
    --*******.4.1.3052.*********.1.1.2

cpeName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "CPE name, informational only"
    ::= { cpeEntry 3 }
    --*******.4.1.3052.*********.1.1.3

cpeDescription OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "CPE description, informational only"
    ::= { cpeEntry 4 }
    --*******.4.1.3052.*********.1.1.4

cpeKeepalive OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of seconds between each successful ping request."
    ::= { cpeEntry 5 }
    --*******.4.1.3052.*********.1.1.5

cpeThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of unsuccessful pings that triggers the CPE event."
    ::= { cpeEntry 6 }
    --*******.4.1.3052.*********.1.1.6

cpeEventReminderInterval OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "CPE event reminder interval (minutes)."
    ::= { cpeEntry 7 }
    --*******.4.1.3052.*********.1.1.7

cpeKeepaliveTicks OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "CPE ping period in 16ths of a second (nonzero overrides keepalive)."
    ::= { cpeEntry 20 }
    --*******.4.1.3052.*********.1.1.20

cpePingSize OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of bytes in CPE ping payload"
    ::= { cpeEntry 21 }
    --*******.4.1.3052.*********.1.11

cpeInfoReset OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Set any number to reset CPE ping stats."
    ::= { cpeEntry 30 }
    --*******.4.1.3052.*********.1.1.30

cpeInfoNumReq OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total number of requests transmitted."
    ::= { cpeEntry 31 }
    --*******.4.1.3052.*********.1.1.31

cpeInfoNumGoodResp OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total number of non-corrupt responses."
    ::= { cpeEntry 32 }
    --*******.4.1.3052.*********.1.1.32

cpeInfoNumBadResp OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total number of corrupt responses."
    ::= { cpeEntry 33 }
    --*******.4.1.3052.*********.1.1.33

cpeInfoNumLostResp OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Total number of requests with no response."
    ::= { cpeEntry 34 }
    --*******.4.1.3052.*********.1.1.34

cpeInfoPercentLoss OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Percent loss (missing responses / total requests)."
    ::= { cpeEntry 35 }
    --*******.4.1.3052.*********.1.1.35

cpeInfoPercentBad OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Percent corrupt (corrupt responses / total requests)."
    ::= { cpeEntry 36 }
    --*******.4.1.3052.*********.1.1.36

--***************************************************************************************
--Modem Section  2.5
--***************************************************************************************





modemFormat OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Modem data format (8N1,7E1,7O1,7N1)"
    ::= { modem 1 }
    --*******.4.1.3052.********

modemInitString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Init string for modem"
    ::= { modem 2 }
    --*******.4.1.3052.********


modemCallAttempts OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Max mumber of times the modem should try to call out until
        successful connect"
    ::= { modem 4 }
    --*******.4.1.3052.********

modemCallDelay OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minimum delay (seconds) between callouts"
    ::= { modem 5 }
    --*******.4.1.3052.********


modemCmdTimout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of minutes after which an idle command session via
        modem is terminated"
    ::= { modem 6 }
    --*******.4.1.3052.********

modemGoto OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "When a user connects, where that user is routed (command
        session, passthrough to port 1, passthrough to port 2, etc.)"
    ::= { modem 7 }
    --*******.4.1.3052.********


modemCLIDEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Calling Line Identification security"
    ::= { modemClid 1 }
    --*******.4.1.3052.********.1


modemCLIDNumberTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ModemCLIDNumberConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of authorized callers identified by phone number"
    ::= { modemClid 2 }
    --*******.4.1.3052.********.2

modemCLIDNumberEntry OBJECT-TYPE
    SYNTAX ModemCLIDNumberConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of CLID numbers"
    INDEX { modemCLIDNumberIndex }
    ::= { modemCLIDNumberTable 1 }
    --*******.4.1.3052.********.2.1

modemCLIDNumberIndex OBJECT-TYPE
    SYNTAX Integer32 (1..20)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of CLID numbers"
    ::= { modemCLIDNumberEntry 1 }
    --*******.4.1.3052.********.2.1.1

modemCLIDNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "auhorized CLID number"
    ::= { modemCLIDNumberEntry 2 }
    --*******.4.1.3052.********.2.1.2

modemTAPInitString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Init string to use for TAP (pager) connections"
    ::= { modem 9 }
    --*******.4.1.3052.********

modemTAP8N1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: use 8N1 data/parity/stop for TAP connections"
    ::= { modem 10 }
    --*******.4.1.3052.********0

--***************************************************************************************
--Time Section  2.8
--***************************************************************************************

clock OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Text string for date and time;
        e.g. 'SUN 01/02/98 12:34:27'.  When setting
        this object, only the date and time are
        required; the unit will ignore a specified
        day of week because it is calculated from
        the date and time.  When this object is included
        in a notification as a varbind, it represents
        the time that the triggering event occurred."
    ::= { time 1 }
    --*******.4.1.3052.********

--***************************************************************************************
--Console Section  2.10
--***************************************************************************************


consoleDuplex OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF characters echoed on telnet command session"
    ::= { console 1 }
    --*******.4.1.3052.*********

consoleTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Minutes of inactivity after which telnet command session is terminated"
    ::= { console 2 }
    --*******.4.1.3052.*********

consoleConfirm OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Prompt to confirm certain actions"
    ::= { console 7 }
    --*******.4.1.3052.*********

--***************************************************************************************
--Core Security Section 2.11.1
--***************************************************************************************

scShowPasswordPrompt OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF show password prompt upon login"
    ::= { secCore 1 }
    --*******.4.1.3052.*********.1

scConsoleLoginRequired  OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF login required upon connecting via console"
    ::= { secCore 2 }
    --*******.4.1.3052.*********.2

scModemLoginRequired OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF login required upon connecting via modem"
    ::= { secCore 3 }
    --*******.4.1.3052.*********.3

scTelnetLoginRequired OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF login required upon connecting via Telnet"
    ::= { secCore 4 }
    --*******.4.1.3052.*********.4

scTelnetPTLoginRequired OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF login required upon connecting via Telnet to pass-
        through ports (TCP ports 210x)"
    ::= { secCore 5 }
    --*******.4.1.3052.*********.5

scRTSLoginRequired OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF login required upon connecting to RTS (TCP ports 220x)"
    ::= { secCore 6 }
    --*******.4.1.3052.*********.6

scAuthMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Authentication mode (USERNAME/PW,PW/USERNAME,PASSWORD ONLY)"
    ::= { secCore 7 }
    --*******.4.1.3052.*********.7

scRightsGroup OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The set of aliases used for the 8 available rights levels"
    ::= { secCore 8 }
    --*******.4.1.3052.*********.8


--***************************************************************************************
--User Security Section 2.11.2
--***************************************************************************************


secUserTable OBJECT-TYPE
    SYNTAX SEQUENCE OF SecUserConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of users"
    ::= { unitSecurity 2 }
    --*******.4.1.3052.*********

secUserEntry OBJECT-TYPE
    SYNTAX SecUserConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of users"
    INDEX { secUserIndex }
    ::= { secUserTable 1 }
    --*******.4.1.3052.*********.1

secUserIndex OBJECT-TYPE
    SYNTAX Integer32 (1..12)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of users"
    ::= { secUserEntry 1 }
    --*******.4.1.3052.*********.1.1

secUserEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable user"
    ::= { secUserEntry 2 }
    --*******.4.1.3052.*********.1.2

secUserConnectVia OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Methods by which user is allowed to connect:
        L=local (console)
        T=Telnet
        M=modem
        F=FTP
        R=RTS
        S=SSH
        "
    ::= { secUserEntry 5 }
    --*******.4.1.3052.*********.1.5

secUserLoginTo OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The function presented to the user upon login (COMMAND,
        PASSTHROUGH,MENU)"
    ::= { secUserEntry 6 }
    --*******.4.1.3052.*********.1.6

secUserAccessFile OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The default access file associated with the user"
    ::= { secUserEntry 7 }
    --*******.4.1.3052.*********.1.7

secUserPTEscapeTo OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The function presented to the user upon escaping pass-
        through: currently only MENU"
    ::= { secUserEntry 9 }
    --*******.4.1.3052.*********.1.9

secUserPPPType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The kind of PPP access allowed to this user cannot connect
        via PPP (NONE), can connect but only to the unit (LOCAL), or
        can connect and route to Ethernet (ROUTING)"
    ::= { secUserEntry 10 }
    --*******.4.1.3052.*********.1.10

secUserRights OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The rights level granted to this user"
    ::= { secUserEntry 11 }
    --*******.4.1.3052.*********.1.11

secUserEventsReadAccess OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "DENY/ALLOW user to read the events/alarms file"
    ::= { secUserEntry 13 }
    --*******.4.1.3052.*********.1.13

secUserAuditReadAccess OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "DENY/ALLOW user to read the audit log"
    ::= { secUserEntry 14 }
    --*******.4.1.3052.*********.1.14

secUserEventsWriteAccess OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "DENY/ALLOW user to delete the events file"
    ::= { secUserEntry 16 }
    --*******.4.1.3052.*********.1.16

secUserAuditWriteAccess OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "DENY/ALLOW user to write to the audit log"
    ::= { secUserEntry 17 }
    --*******.4.1.3052.*********.1.17

secUserExpiration OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Expiration date/time of this user"
    ::= { secUserEntry 18 }
    --*******.4.1.3052.*********.1.18

secUserCallbackNumber1 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number slot 1 the unit calls for dialback"
    ::= { secUserEntry 19 }
    --*******.4.1.3052.*********.1.19

secUserCallbackNumber2 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number slot 2 the unit calls for dialback"
    ::= { secUserEntry 20 }
    --*******.4.1.3052.*********.1.20

secUserCallbackNumber3 OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number slot 3 the unit calls for dialback"
    ::= { secUserEntry 21 }
    --*******.4.1.3052.*********.1.21

secUserChallengeTelnetMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Telnet challenge mode: when user logs in via telnet, unit
        allows access (OFF), presents a challenge (CHALLENGE), or
        unit sends a one-time-password (SEND PASSWORD) to a
        destination configured by secUserChallengeTelnetSendTo"
    ::= { secUserEntry 22 }
    --*******.4.1.3052.*********.1.22

secUserChallengeModemMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Modem challenge mode: when user logs in via modem, unit
        allows access (OFF), presents a challenge (CHALLENGE), sends
        a one-time-password (SEND PASSWORD) to a destination
        configured by secUserChallengeTelnetSendTo, or dials a
        number (CALLBACK) configured with secUserCallbackNumberx"
    ::= { secUserEntry 23 }
    --*******.4.1.3052.*********.1.23

secUserChallengeConsoleMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Console challenge mode: when user logs in via console, unit
        allows access (OFF), or presents a challenge (CHALLENGE)"
    ::= { secUserEntry 24 }
    --*******.4.1.3052.*********.1.24

secUserChallengeTelnetSendTo OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Email action used as the destination address for one-time-
        password when a user logs in with a challenge mode of
        SEND PASSWORD"
    ::= { secUserEntry 25 }
    --*******.4.1.3052.*********.1.25

secUserChallengeModemSendTo OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Modem action used as the destination phone number for one-
        time-password when a user logs in with a challenge mode of
        SEND PASSWORD"
    ::= { secUserEntry 26 }
    --*******.4.1.3052.*********.1.26

secUserChallengeExpiration OBJECT-TYPE
    SYNTAX Integer32 (0..180)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time (in minutes) after which a one-time-password is sent
        that it can be used to log in"
    ::= { secUserEntry 27 }
    --*******.4.1.3052.*********.1.27


--***************************************************************************************
--Factory Security Section 2.11.3
--***************************************************************************************

sfEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable factory access"
    ::= { secFactory 1 }
    --*******.4.1.3052.*********.1

sfSecret OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Secret used for factory access"
    ::= { secFactory 2 }
    --*******.4.1.3052.*********.2



--***************************************************************************************
--Event Section   2.12
--***************************************************************************************

--***************************************************************************************
--Event Core Section   2.12.1
--***************************************************************************************


evClassNameTable OBJECT-TYPE
    SYNTAX SEQUENCE OF EventClassConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of classes"
    ::= { evCore 1 }
    --*******.4.1.3052.*********.1

evClassNameEntry OBJECT-TYPE
    SYNTAX EventClassConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of classes"
    INDEX { evClassNameIndex }
    ::= { evClassNameTable 1 }
    --*******.4.1.3052.12.2.********

evClassNameIndex OBJECT-TYPE
    SYNTAX Integer32 (1..12)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of classes"
    ::= { evClassNameEntry 1 }
    --*******.4.1.3052.12.2.********.1

evClassName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class name for this slot"
    ::= { evClassNameEntry 2 }
    --*******.4.1.3052.12.2.********.2

evReminderInterval OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Interval (minutes) between EventSensor reminder events"
    ::= { evCore 2 }
    --*******.4.1.3052.*********.2

evLogEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable event log"
    ::= { evLog 1 }
    --*******.4.1.3052.*********.3.1

evLogStoreAlarm OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF store data alarms in event log"
    ::= { evLog 2 }
    --*******.4.1.3052.*********.3.2

evLogMaxSize OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "max size (in KB) of event log"
    ::= { evLog 3 }
    --*******.4.1.3052.*********.3.3

evLogStoreSensor OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF store EventSensor events in event log"
    ::= { evLog 4 }
    --*******.4.1.3052.*********.3.4

evLogTimeStampAlarms OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF put timestamps on event log entries"
    ::= { evLog 5 }
    --*******.4.1.3052.*********.3.5

evLogPrependName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF prepend data event name to data alarm records in event
        log"
    ::= { evLog 6 }
    --*******.4.1.3052.*********.3.6


evMgmtReprocess OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of sensors that have generated events since reset.
        Write this to reprocess each sensor's last generated event
        since reset.  The value to write determines what kind of
        events should be reprocessed:
                'all': all sensor events
                 'cc': cc events
        'temperature': temperature events
           'humidity': humidity events
             'analog': analog input events"
    ::= { evMgmt 3 }
    --*******.4.1.3052.*********.4.3






--***************************************************************************************
--Data event section 2.12.2
--***************************************************************************************

evdExitOnTrue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: Data alarm evaluator stops checking record against
        alarm equations when it finds an equation that matches"
    ::= { evdCore 1 }
    --*******.4.1.3052.12.2.********.1

evdFilterAction OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "What happens to a record that matches an equation:
        REJECT/ACCEPT"
    ::= { evdCore 2 }
    --*******.4.1.3052.12.2.********.2

evdWildCardChar OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Character (in decimal ASCII code format) used to indicate
        wildcards in data alarm equation"
    ::= { evdCore 3 }
    --*******.4.1.3052.12.2.********.3


evdTable OBJECT-TYPE
    SYNTAX SEQUENCE OF DataEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of data events"
    ::= { evData 2 }
    --*******.4.1.3052.*********.2

evdEntry OBJECT-TYPE
    SYNTAX DataEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of data events"
    INDEX { evdIndex }
    ::= { evdTable 1 }
    --*******.4.1.3052.*********.2.1

evdIndex OBJECT-TYPE
    SYNTAX Integer32 (1..100)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of data events"
    ::= { evdEntry 1 }
    --*******.4.1.3052.*********.2.1.1

evdEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this data event"
    ::= { evdEntry 2 }
    --*******.4.1.3052.*********.2.1.2

evdName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Name for this data event"
    ::= { evdEntry 3 }
    --*******.4.1.3052.*********.2.1.3

evdEquation OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Equation for this data event"
    ::= { evdEntry 4 }
    --*******.4.1.3052.*********.2.1.4

evdThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of equation matches that must happen to trigger this
        data event"
    ::= { evdEntry 5 }
    --*******.4.1.3052.*********.2.1.5

evdAutoClear OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF this data alarm is automatically cleared (re-armed)
        upon reaching the threshold number of matches"
    ::= { evdEntry 6 }
    --*******.4.1.3052.*********.2.1.6

evdClearInterval OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Interval at which this alarm is automatically cleared (no
        bearing with evdAutoClear)"
    ::= { evdEntry 7 }
    --*******.4.1.3052.*********.2.1.7

evdClearTime OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Tie of day (HH:MM) at which this data alarm is cleared (no
        bearing with evdAutoClear)"
    ::= { evdEntry 8 }
    --*******.4.1.3052.*********.2.1.8

evdActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for this data event"
    ::= { evdEntry 9 }
    --*******.4.1.3052.*********.2.1.9

evdClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for this data event"
    ::= { evdEntry 10 }
    --*******.4.1.3052.*********.2.1.10

evdTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for this event"
    ::= { evdEntry 11 }
    --*******.4.1.3052.*********.2.1.11

evdMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ALARM/FILTER: what is happens when this data event
        triggers: either generate actions (mode=ALARM) )or
        ACCEPT/REJECT (via evdFilterAction) the record
        (mode=FILTER)"
    ::= { evdEntry 12 }
    --*******.4.1.3052.*********.2.1.12

evdFieldTable OBJECT-TYPE
    SYNTAX SEQUENCE OF DataEventFieldConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of data event fields"
    ::= { evData 3 }
    --*******.4.1.3052.*********.3

evdFieldEntry OBJECT-TYPE
    SYNTAX DataEventFieldConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of data event fields"
    INDEX { evdFieldIndex }
    ::= { evdFieldTable 1 }
    --*******.4.1.3052.12.2.********

evdFieldIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of data event fields"
    ::= { evdFieldEntry 1 }
    --*******.4.1.3052.12.2.********.1

evdFieldStart OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Start position (1-based) for this data event field"
    ::= { evdFieldEntry 2 }
    --*******.4.1.3052.12.2.********.2

evdFieldLength OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of characters this data event field occupies"
    ::= { evdFieldEntry 3 }
    --*******.4.1.3052.12.2.********.3

evdFieldName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Start position (1-based) for this data event field"
    ::= { evdFieldEntry 4 }
    --*******.4.1.3052.12.2.********.4

evdFieldLine OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Line number (1-based, for multiline records) for this data
        event field"
    ::= { evdFieldEntry 5 }
    --*******.4.1.3052.12.2.********.5

evdFieldType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Alpha/Numeric type for this data event field"
    ::= { evdFieldEntry 6 }
    --*******.4.1.3052.12.2.********.6

evdMacroTable OBJECT-TYPE
    SYNTAX SEQUENCE OF DataEventMacroConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of data event macros"
    ::= { evData 4 }
    --*******.4.1.3052.*********.4

evdMacroEntry OBJECT-TYPE
    SYNTAX DataEventMacroConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of data event macros"
    INDEX { evdMacroIndex }
    ::= { evdMacroTable 1 }
    --*******.4.1.3052.12.2.********

evdMacroIndex OBJECT-TYPE
    SYNTAX Integer32 (1..100)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of data event macros"
    ::= { evdMacroEntry 1 }
    --*******.4.1.3052.12.2.********.1

evdMacroName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Name of this macro for use in data event equations"
    ::= { evdMacroEntry 2 }
    --*******.4.1.3052.12.2.********.2

evdMacroEquation OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Equation for this data event macro"
    ::= { evdMacroEntry 3 }
    --*******.4.1.3052.12.2.********.3

--***************************************************************************************
--No data event 1 Section 2.12.3
--***************************************************************************************

evNoData1Enable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this no data event"
    ::= { evNoData1 1 }
    --*******.4.1.3052.*********.1

evNoData1Actions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for this no data event"
    ::= { evNoData1 2 }
    --*******.4.1.3052.*********.2

evNoData1Message OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for this no data event"
    ::= { evNoData1 3 }
    --*******.4.1.3052.*********.3

evNoData1TrapNum OBJECT-TYPE
    SYNTAX Integer32 (505..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for this no data event"
    ::= { evNoData1 4 }
    --*******.4.1.3052.*********.4

evNoData1Class OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for this no data event"
    ::= { evNoData1 5 }
    --*******.4.1.3052.*********.5

evNoData1Sched1Begin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 1 begin time (HH:MM) for this no data event"
    ::= { evNoData1 6 }
    --*******.4.1.3052.*********.6

evNoData1Sched1End OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 1 end time (HH:MM) for this no data event"
    ::= { evNoData1 7 }
    --*******.4.1.3052.*********.7

evNoData1Sched1Duration OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Duration (minutes) for schedule 1 of this no data event"
    ::= { evNoData1 8 }
    --*******.4.1.3052.*********.8

evNoData1Sched2Begin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 2 begin time (HH:MM) for this no data event"
    ::= { evNoData1 9 }
    --*******.4.1.3052.*********.9

evNoData1Sched2End OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 2 end time (HH:MM) for this no data event"
    ::= { evNoData1 10 }
    --*******.4.1.3052.*********.10

evNoData1Sched2Duration OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Duration (minutes) for schedule 2 of this no data event"
    ::= { evNoData1 11 }
    --*******.4.1.3052.*********.11

evNoData1PortEnableTable OBJECT-TYPE
    SYNTAX SEQUENCE OF NoDataEvent1PortEnableConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of no data event 1 port enables"
    ::= { evNoData1 12 }
    --*******.4.1.3052.*********.12

evNoData1PortEnableEntry OBJECT-TYPE
    SYNTAX NoDataEvent1PortEnableConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of no data event 1 port enables"
    INDEX { evNoData1PortEnableIndex }
    ::= { evNoData1PortEnableTable 1 }
    --*******.4.1.3052.*********.12.1

evNoData1PortEnableIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of no data event 1 port enables"
    ::= { evNoData1PortEnableEntry 1 }
    --*******.4.1.3052.*********.12.1.1

evNoData1PortEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: no data event 1 observes this port"
    ::= { evNoData1PortEnableEntry 2 }
    --*******.4.1.3052.*********.12.1.2

evNoData1Exclusions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Exclusions (space-separated MM/DD-formatted dates) for this
        no data event"
    ::= { evNoData1 13 }
    --*******.4.1.3052.*********.13

evNoData1Days OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Days of the week on which this no event is active"
    ::= { evNoData1 14 }
    --*******.4.1.3052.*********.14

--***************************************************************************************
--No data event 2 Section 2.12.4
--***************************************************************************************

evNoData2Enable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this no data event"
    ::= { evNoData2 1 }
    --*******.4.1.3052.*********.1

evNoData2Actions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for this no data event"
    ::= { evNoData2 2 }
    --*******.4.1.3052.*********.2

evNoData2Message OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for this no data event"
    ::= { evNoData2 3 }
    --*******.4.1.3052.*********.3

evNoData2TrapNum OBJECT-TYPE
    SYNTAX Integer32 (505..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for this no data event"
    ::= { evNoData2 4 }
    --*******.4.1.3052.*********.4

evNoData2Class OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for this no data event"
    ::= { evNoData2 5 }
    --*******.4.1.3052.*********.5

evNoData2Sched1Begin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 1 begin time (HH:MM) for this no data event"
    ::= { evNoData2 6 }
    --*******.4.1.3052.*********.6

evNoData2Sched1End OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 1 end time (HH:MM) for this no data event"
    ::= { evNoData2 7 }
    --*******.4.1.3052.*********.7

evNoData2Sched1Duration OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Duration (minutes) for schedule 1 of this no data event"
    ::= { evNoData2 8 }
    --*******.4.1.3052.*********.8

evNoData2Sched2Begin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 2 begin time (HH:MM) for this no data event"
    ::= { evNoData2 9 }
    --*******.4.1.3052.*********.9

evNoData2Sched2End OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Schedule 2 end time (HH:MM) for this no data event"
    ::= { evNoData2 10 }
    --*******.4.1.3052.*********.10

evNoData2Sched2Duration OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Duration (minutes) for schedule 2 of this no data event"
    ::= { evNoData2 11 }
    --*******.4.1.3052.*********.11

evNoData2PortEnableTable OBJECT-TYPE
    SYNTAX SEQUENCE OF NoDataEvent2PortEnableConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of no data event 2 port enables"
    ::= { evNoData2 12 }
    --*******.4.1.3052.*********.12

evNoData2PortEnableEntry OBJECT-TYPE
    SYNTAX NoDataEvent2PortEnableConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of no data event 2 port enables"
    INDEX { evNoData2PortEnableIndex }
    ::= { evNoData2PortEnableTable 1 }
    --*******.4.1.3052.12.2.1********

evNoData2PortEnableIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of no data event 2 port enables"
    ::= { evNoData2PortEnableEntry 1 }
    --*******.4.1.3052.12.2.1********.1

evNoData2PortEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: no data event 2 observes this port"
    ::= { evNoData2PortEnableEntry 2 }
    --*******.4.1.3052.12.2.1********.2

evNoData2Exclusions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Exclusions (space-separated MM/DD-formatted dates) for this
        no data event"
    ::= { evNoData2 13 }
    --*******.4.1.3052.*********.13

evNoData2Days OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Days of the week on which this no event is active"
    ::= { evNoData2 14 }
    --*******.4.1.3052.*********.14

--***************************************************************************************
--Scheduled Event 1 Section 2.12.5
--***************************************************************************************

evSched1Enable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this scheduled event"
    ::= { evSched1 1 }
    --*******.4.1.3052.*********.1

evSched1Actions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for this scheduled event"
    ::= { evSched1 2 }
    --*******.4.1.3052.*********.2

evSched1Message OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for this scheduled event"
    ::= { evSched1 3 }
    --*******.4.1.3052.*********.3

evSched1TrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for this scheduled event"
    ::= { evSched1 4 }
    --*******.4.1.3052.*********.4

evSched1Class OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for this scheduled event"
    ::= { evSched1 5 }
    --*******.4.1.3052.*********.5

evSched1Sunday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Sunday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 6 }
    --*******.4.1.3052.*********.6

evSched1Monday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Monday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 7 }
    --*******.4.1.3052.*********.7

evSched1Tuesday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Tuesday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 8 }
    --*******.4.1.3052.*********.8

evSched1Wednesday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Wednesday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 9 }
    --*******.4.1.3052.*********.9

evSched1Thursday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Thursday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 10 }
    --*******.4.1.3052.*********.10

evSched1Friday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Friday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 11 }
    --*******.4.1.3052.*********.11

evSched1Saturday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Saturday (HH:MM) when this scheduled event triggers"
    ::= { evSched1 12 }
    --*******.4.1.3052.*********.12

evSched1Exclusions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Exclusions (space-separated MM/DD-formatted dates) for this
        scheduled event"
    ::= { evSched1 13 }
    --*******.4.1.3052.*********.13


--***************************************************************************************
--Scheduled Event 2 Section 2.12.6
--***************************************************************************************

evSched2Enable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable this scheduled event"
    ::= { evSched2 1 }
    --*******.4.1.3052.*********.1

evSched2Actions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for this scheduled event"
    ::= { evSched2 2 }
    --*******.4.1.3052.*********.2

evSched2Message OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for this scheduled event"
    ::= { evSched2 3 }
    --*******.4.1.3052.*********.3

evSched2TrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for this scheduled event"
    ::= { evSched2 4 }
    --*******.4.1.3052.*********.4

evSched2Class OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for this scheduled event"
    ::= { evSched2 5 }
    --*******.4.1.3052.*********.5

evSched2Sunday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Sunday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 6 }
    --*******.4.1.3052.*********.6

evSched2Monday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Monday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 7 }
    --*******.4.1.3052.*********.7

evSched2Tuesday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Tuesday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 8 }
    --*******.4.1.3052.*********.8

evSched2Wednesday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Wednesday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 9 }
    --*******.4.1.3052.*********.9

evSched2Thursday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Thursday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 10 }
    --*******.4.1.3052.*********.10

evSched2Friday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Friday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 11 }
    --*******.4.1.3052.*********.11

evSched2Saturday OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Time on Saturday (HH:MM) when this scheduled event triggers"
    ::= { evSched2 12 }
    --*******.4.1.3052.*********.12

evSched2Exclusions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Exclusions (space-separated MM/DD-formatted dates) for this
        scheduled event"
    ::= { evSched2 13 }
    --*******.4.1.3052.*********.13

--***************************************************************************************
--Serial Handshaking Low Section 2.12.7
--***************************************************************************************

evShskLowTable OBJECT-TYPE
    SYNTAX SEQUENCE OF SerialHandshakingLowEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of serial handshaking low events"
    ::= { event 7 }
    --*******.4.1.3052.*********

evShskLowEntry OBJECT-TYPE
    SYNTAX SerialHandshakingLowEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of serial handshaking low events"
    INDEX { evShskLowIndex }
    ::= { evShskLowTable 1 }
    --*******.4.1.3052.*********.1

evShskLowIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of serial handshaking low events"
    ::= { evShskLowEntry 1 }
    --*******.4.1.3052.*********.1.1

evShskLowEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: enable the serial handshaking low event for this port"
    ::= { evShskLowEntry 2 }
    --*******.4.1.3052.*********.1.2

evShskLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the serial handshaking low event for this port"
    ::= { evShskLowEntry 3 }
    --*******.4.1.3052.*********.1.3

evShskLowMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for the serial handshaking low event for this port"
    ::= { evShskLowEntry 4 }
    --*******.4.1.3052.*********.1.4

evShskLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for the serial handshaking low event for this port"
    ::= { evShskLowEntry 5 }
    --*******.4.1.3052.*********.1.5

evShskLowTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for the serial handshaking low event for this port"
    ::= { evShskLowEntry 6 }
    --*******.4.1.3052.*********.1.6


--***************************************************************************************
--Serial Handshaking High Section 2.12.8
--***************************************************************************************

evShskHighTable OBJECT-TYPE
    SYNTAX SEQUENCE OF SerialHandshakingHighEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of serial handshaking high events"
    ::= { event 8 }
    --*******.4.1.3052.*********

evShskHighEntry OBJECT-TYPE
    SYNTAX SerialHandshakingHighEventConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of serial handshaking high events"
    INDEX { evShskHighIndex }
    ::= { evShskHighTable 1 }
    --*******.4.1.3052.*********.1

evShskHighIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of serial handshaking high events"
    ::= { evShskHighEntry 1 }
    --*******.4.1.3052.*********.1.1

evShskHighEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: enable the serial handshaking high event for this
        port"
    ::= { evShskHighEntry 2 }
    --*******.4.1.3052.*********.1.2

evShskHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for the serial handshaking high event for this port"
    ::= { evShskHighEntry 3 }
    --*******.4.1.3052.*********.1.3

evShskHighMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for the serial handshaking high event for this port"
    ::= { evShskHighEntry 4 }
    --*******.4.1.3052.*********.1.4

evShskHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for the serial handshaking high event for this port"
    ::= { evShskHighEntry 5 }
    --*******.4.1.3052.*********.1.5

evShskHighTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for the serial handshaking high event for this
        port"
    ::= { evShskHighEntry 6 }
    --*******.4.1.3052.*********.1.6



--***************************************************************************************
--No Sensor Event Section 2.12.9
--***************************************************************************************

evNoSensorTimeout OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Timeout (seconds) after which an EventSensor becomes
        unresponsive that this event is triggered"
    ::= { evNoSensor 1 }
    --*******.4.1.3052.*********.1

evNoSensorActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for no sensor event"
    ::= { evNoSensor 2 }
    --*******.4.1.3052.*********.2

evNoSensorTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for no sensor event"
    ::= { evNoSensor 3 }
    --*******.4.1.3052.*********.3

evNoSensorClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for no sensor event"
    ::= { evNoSensor 4 }
    --*******.4.1.3052.*********.4



--***************************************************************************************
--Fuel Sensor Config Section       2.12.11
--***************************************************************************************


fuelSensorGeneralTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FuelSensorConfigGeneral
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes general fuel sensor attributes."
    ::= { fuelSensor 1 }
    --*******.4.1.3052.**********.1

fsGenEntry OBJECT-TYPE
    SYNTAX FuelSensorConfigGeneral
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for fuel sensor general config table."
    INDEX { fsGenIndex }
    ::= { fuelSensorGeneralTable 1 }
    --*******.4.1.3052.**********.1.1

fsGenIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which fuel sensor in the fuel sensor general config table."
    ::= { fsGenEntry 1 }
    --*******.4.1.3052.**********.1.1.1

fsGenName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor name."
    ::= { fsGenEntry 2 }
    --*******.4.1.3052.**********.1.1.2

fsGenSensorType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Type of fuel sensor in use; NONE disables the fuel sensor."
    ::= { fsGenEntry 3 }
    --*******.4.1.3052.**********.1.1.3

fsGenDistanceUnit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Distance units in use when setting and indicating tank dimensions."
    ::= { fsGenEntry 4 }
    --*******.4.1.3052.**********.1.1.4

fsGenRawValueTop OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Analog input reading corresponding to the SENSOR FULL point."
    ::= { fsGenEntry 5 }
    --*******.4.1.3052.**********.1.1.5

fsGenTopOffset OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Distance, in the specified distance unit, between the SENSOR FULL
        point and the TANK FULL point.  The value is positive if the
        SENSOR FULL point is above the TANK FULL point, and negative if it is
        below."
    ::= { fsGenEntry 6 }
    --*******.4.1.3052.**********.1.1.6

fsGenRawValueBottom OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Analog input reading corresponding to the minimum fluid height
        measurable by the fuel sensor."
    ::= { fsGenEntry 7 }
    --*******.4.1.3052.**********.1.1.7

fsGenBottomOffset OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Distance, in the specified distance unit, between the SENSOR EMPTY
        point and the TANK EMPTY point.  The value is positive if the
        SENSOR EMPTY point is above the TANK EMPTY point, and negative if it
        is below."
    ::= { fsGenEntry 8 }
    --*******.4.1.3052.**********.1.1.8

fsGenInputES OBJECT-TYPE
    SYNTAX Integer32 (1..200)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Which eventsensor contains the analog input associated with this fuel
        sensor. Allowed values are 200 (for internal ES), or 1-16."
    ::= { fsGenEntry 9 }
    --*******.4.1.3052.**********.1.1.9

fsGenInputPoint OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Which analog input point, on the specified eventsensor, that is
        associated with the fuel sensor."
    ::= { fsGenEntry 10 }
    --*******.4.1.3052.**********.1.1.10

fsGenFilterAveraging OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor filter averaging factor."
    ::= { fsGenEntry 11 }
    --*******.4.1.3052.**********.1.1.11

fsGenSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor System Reporting enable."
    ::= { fsGenEntry 12 }
    --*******.4.1.3052.**********.1.1.12

fsGenSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor System Reporting threshold (floating point value)."
    ::= { fsGenEntry 13 }
    --*******.4.1.3052.**********.1.1.13

fsGenSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor System Reporting limit."
    ::= { fsGenEntry 14 }
    --*******.4.1.3052.**********.1.1.14

fsGenSysrepPackage OBJECT-TYPE
    SYNTAX Integer32 (0..10)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor System Reporting package number."
    ::= { fsGenEntry 15 }
    --*******.4.1.3052.**********.1.1.15

fsGenSysrepType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor ASM profile type."
    ::= { fsGenEntry 16 }
    --*******.4.1.3052.**********.1.1.16

fsGenEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor enable."
    ::= { fsGenEntry 17 }
    --*******.4.1.3052.**********.1.1.17

fsGenFuelType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Type of fuel being measured: DIESEL, PROPANE,
        NATURAL GAS, HYDROGEN, GASOLINE, OTHER."
    ::= { fsGenEntry 18 }
    --*******.4.1.3052.**********.1.1.18

fuelSensorTankTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FuelSensorConfigTank
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes tank-specific fuel sensor attributes."
    ::= { fuelSensor 2 }
    --*******.4.1.3052.**********.2

fsTankEntry OBJECT-TYPE
    SYNTAX FuelSensorConfigTank
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for fuel sensor tank config table."
    INDEX { fsTankIndex }
    ::= { fuelSensorTankTable 1 }
    --*******.4.1.3052.**********.2.1

fsTankIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which fuel sensor in the fuel sensor tank config table."
    ::= { fsTankEntry 1 }
    --*******.4.1.3052.**********.2.1.1

fsTankHeight OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Height of the tank, from the TANK EMPTY point to the TANK FULL point,
        in the specified distance unit (i.e. TANK HEIGHT).  This is a
        floating point value."
    ::= { fsTankEntry 2 }
    --*******.4.1.3052.**********.2.1.2

fsTankDimA OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Tank dimension measurement A for certain tank profiles. For
        HORIZ OVAL, this is the length of the straight section.  This is a
        floating point value."
    ::= { fsTankEntry 3 }
    --*******.4.1.3052.**********.2.1.3

fsTankDimB OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Tank dimension measurement B for certain tank profiles. For
        HORIZ OVAL, this is the radius of the curved section.  This is a
        floating point value."
    ::= { fsTankEntry 4 }
    --*******.4.1.3052.**********.2.1.4

fsTankVolume OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "How many volume units the tank contains, when full.  This is a
        floating point value."
    ::= { fsTankEntry 5 }
    --*******.4.1.3052.**********.2.1.5

fsTankVolumeUnit OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Name of the unit representing fluid volume in the tank. This is only
        used for display purposes; it does not affect the volume calculation."
    ::= { fsTankEntry 6 }
    --*******.4.1.3052.**********.2.1.6

fsTankShape OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The shape of the fuel tank.  A value of LINEAR produces a fluid
        volume reading that is directly proportional to fluid height.  The
        HORIZ CYL and HORIZ OVAL settings use a hard-coded formula to
        calculate the volume.  The CUSTOM setting uses a strapping table that
        must be configured as well (see fuelSensorCustomTankTable)."
    ::= { fsTankEntry 7 }
    --*******.4.1.3052.**********.2.1.7


fuelSensorCustomTankTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FuelSensorConfigCustomTankStrapping
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes the custom tank strapping table.  Each fuel
        sensor has a table of height/volume pairs in service only when the tank
        shape (fsTankShape) in CUSTOM.  This allows the unit to calculate
        volumes for nonstandard tank shapes, to the degree that accurate
        height/volume measurements can be configured.  This SNMP table has two
        indexes: the first for which fuel sensor and the second for which
        height/volume pair."
    ::= { fuelSensor 3 }
    --*******.4.1.3052.12.*********

fsCustomTankEntry OBJECT-TYPE
    SYNTAX FuelSensorConfigCustomTankStrapping
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for fuel sensor custom tank config table."
    INDEX { fsCustomTankIndexFS, fsCustomTankIndexDatum }
    ::= { fuelSensorCustomTankTable 1 }
    --*******.4.1.3052.12.*********.1

fsCustomTankIndexFS OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which fuel sensor in the fuel sensor tank config table.  This is the
        first of two indexes for this SNMP table."
    ::= { fsCustomTankEntry 1 }
    --*******.4.1.3052.12.*********.1.1

fsCustomTankIndexDatum OBJECT-TYPE
    SYNTAX Integer32 (1..32)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which hight/volume pair: there are 32 pairs per fuel sensor.  This is
        the second of two indexes for this SNMP table."
    ::= { fsCustomTankEntry 2 }
    --*******.4.1.3052.12.*********.1.2

fsCustomTankHeight OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Height of custom tank height/volume pair.  This is a floating point
        value."
    ::= { fsCustomTankEntry 3 }
    --*******.4.1.3052.12.*********.1.3

fsCustomTankVolume OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Volume of custom tank height/volume pair.  This is a floating point
        value."
    ::= { fsCustomTankEntry 4 }
    --*******.4.1.3052.12.*********.1.4


fuelSensorVolumeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FuelSensorConfigVolume
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes volume-event-specific fuel sensor attributes."
    ::= { fuelSensor 4 }
    --*******.4.1.3052.12.*********

fsVolumeEntry OBJECT-TYPE
    SYNTAX FuelSensorConfigVolume
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for fuel sensor volume event config table."
    INDEX { fsVolumeIndex }
    ::= { fuelSensorVolumeTable 1 }
    --*******.4.1.3052.12.*********.1

fsVolumeIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which fuel sensor in the fuel sensor volume event config table."
    ::= { fsVolumeEntry 1 }
    --*******.4.1.3052.12.*********.1.1

fsVolumeEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable fuel sensor volume events."
    ::= { fsVolumeEntry 2 }
    --*******.4.1.3052.12.*********.1.2

fsVolumeDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor deadband (hysteresis).  This is a floating point value."
    ::= { fsVolumeEntry 3 }
    --*******.4.1.3052.12.*********.1.3

fsVolumeVHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very high event threshold value.  This is a
        floating point value."
    ::= { fsVolumeEntry 4 }
    --*******.4.1.3052.12.*********.1.4

fsVolumeVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very high event threshold actions."
    ::= { fsVolumeEntry 5 }
    --*******.4.1.3052.12.*********.1.5

fsVolumeVHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (519..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very high event threshold trap number."
    ::= { fsVolumeEntry 6 }
    --*******.4.1.3052.12.*********.1.6

fsVolumeVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very high event threshold class (severity)."
    ::= { fsVolumeEntry 7 }
    --*******.4.1.3052.12.*********.1.7

fsVolumeHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume high event threshold value.  This is a floating
        point value."
    ::= { fsVolumeEntry 8 }
    --*******.4.1.3052.12.*********.1.8

fsVolumeHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume high event threshold actions."
    ::= { fsVolumeEntry 9 }
    --*******.4.1.3052.12.*********.1.9

fsVolumeHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (519..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume high event threshold trap number."
    ::= { fsVolumeEntry 10 }
    --*******.4.1.3052.12.*********.1.10

fsVolumeHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume high event threshold class (severity)."
    ::= { fsVolumeEntry 11 }
    --*******.4.1.3052.12.*********.1.11

fsVolumeNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume return to normal actions."
    ::= { fsVolumeEntry 12 }
    --*******.4.1.3052.12.*********.1.12

fsVolumeNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (519..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume return to normal trap number."
    ::= { fsVolumeEntry 13 }
    --*******.4.1.3052.12.*********.1.13

fsVolumeNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume return to normal class (severity)."
    ::= { fsVolumeEntry 14 }
    --*******.4.1.3052.12.*********.1.14

fsVolumeLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume low event threshold value.  This is a floating
        point value."
    ::= { fsVolumeEntry 15 }
    --*******.4.1.3052.12.*********.1.15

fsVolumeLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume low event threshold actions."
    ::= { fsVolumeEntry 16 }
    --*******.4.1.3052.12.*********.1.16

fsVolumeLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (519..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume low event threshold trap number."
    ::= { fsVolumeEntry 17 }
    --*******.4.1.3052.12.*********.1.17

fsVolumeLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume low event threshold class (severity)."
    ::= { fsVolumeEntry 18 }
    --*******.4.1.3052.12.*********.1.18

fsVolumeVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very low event threshold value.  This is a
        floating point value."
    ::= { fsVolumeEntry 19 }
    --*******.4.1.3052.12.*********.1.19

fsVolumeVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very low event threshold actions."
    ::= { fsVolumeEntry 20 }
    --*******.4.1.3052.12.*********.1.20

fsVolumeVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (519..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very low event threshold trap number."
    ::= { fsVolumeEntry 21 }
    --*******.4.1.3052.12.*********.1.21

fsVolumeVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor volume very low event threshold class (severity)."
    ::= { fsVolumeEntry 22 }
    --*******.4.1.3052.12.*********.1.22



fuelSensorDisconnectTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FuelSensorConfigDisconnect
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes disconnect-event-specific fuel sensor attributes."
    ::= { fuelSensor 5 }
    --*******.4.1.3052.12.*********

fsDiscEntry OBJECT-TYPE
    SYNTAX FuelSensorConfigDisconnect
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for fuel sensor disconnect event config table."
    INDEX { fsDiscIndex }
    ::= { fuelSensorDisconnectTable 1 }
    --*******.4.1.3052.12.*********.1

fsDiscIndex OBJECT-TYPE
    SYNTAX Integer32 (1..3)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which fuel sensor in the fuel sensor disconnect event config table."
    ::= { fsDiscEntry 1 }
    --*******.4.1.3052.12.*********.1.1

fsDiscEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect event enable."
    ::= { fsDiscEntry 2 }
    --*******.4.1.3052.12.*********.1.2

fsDiscHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High end of input value range when fuel sensor is disconnected.  This
        is in the units of the analog input value."
    ::= { fsDiscEntry 3 }
    --*******.4.1.3052.12.*********.1.3

fsDiscLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low end of input value range when fuel sensor is disconnected.  This
        is in the units of the analog input value."
    ::= { fsDiscEntry 4 }
    --*******.4.1.3052.12.*********.1.4

fsDiscActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect event actions."
    ::= { fsDiscEntry 5 }
    --*******.4.1.3052.12.*********.1.5

fsDiscTrapNum OBJECT-TYPE
    SYNTAX Integer32 (515..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect event trap number."
    ::= { fsDiscEntry 6 }
    --*******.4.1.3052.12.*********.1.6

fsDiscClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect event class (severity)."
    ::= { fsDiscEntry 7 }
    --*******.4.1.3052.12.*********.1.7

fsDiscNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect return to normal actions."
    ::= { fsDiscEntry 8 }
    --*******.4.1.3052.12.*********.1.8

fsDiscNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (515..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect return to normal trap number."
    ::= { fsDiscEntry 9 }
    --*******.4.1.3052.12.*********.1.9

fsDiscNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Fuel sensor disconnect return to normal class (severity)."
    ::= { fsDiscEntry 10 }
    --*******.4.1.3052.12.*********.1.10


--***************************************************************************************
--AC Power Monitor Config Section       2.12.12
--***************************************************************************************

acpmGeneralTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigGeneral
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes general AC power monitor attributes."
    ::= { acPowerMonitor 1 }
    --*******.4.1.3052.12.*********

acpmGenEntry OBJECT-TYPE
    SYNTAX ACPMConfigGeneral
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor general config table."
    INDEX { acpmGenIndex }
    ::= { acpmGeneralTable 1 }
    --*******.4.1.3052.12.*********.1

acpmGenIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor general config table."
    ::= { acpmGenEntry 1 }
    --*******.4.1.3052.12.*********.1.1

acpmGenDevice OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Model of the AC power monitor device. The unit needs this setting to
        know which registers to poll for the various data items, among other
        things."
    ::= { acpmGenEntry 2 }
    --*******.4.1.3052.12.*********.1.2

acpmGenName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Name for the AC power monitor device."
    ::= { acpmGenEntry 3 }
    --*******.4.1.3052.12.*********.1.3

acpmGenAddress OBJECT-TYPE
    SYNTAX Integer32 (1..247)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Modbus address of the AC power monitor device."
    ::= { acpmGenEntry 4 }
    --*******.4.1.3052.12.*********.1.4

acpmGenPtRatio OBJECT-TYPE
    SYNTAX Integer32 (1..10000)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ratio of secondary turns to primary turns on potential transformers
        (PT)."
    ::= { acpmGenEntry 5 }
    --*******.4.1.3052.12.*********.1.5

acpmGenCtRatio OBJECT-TYPE
    SYNTAX Integer32 (1..10000)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Ratio of secondary turns to primary turns on current transformers
        (CT)."
    ::= { acpmGenEntry 6 }
    --*******.4.1.3052.12.*********.1.6

acpmGenPowerType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Type of power being monitored."
    ::= { acpmGenEntry 7 }
    --*******.4.1.3052.12.*********.1.7

acpmGenSysrepPackage OBJECT-TYPE
    SYNTAX Integer32 (0..10)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting package number."
    ::= { acpmGenEntry 8 }
    --*******.4.1.3052.12.*********.1.8

acpmGenSysrepType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ASM profile type."
    ::= { acpmGenEntry 9 }
    --*******.4.1.3052.12.*********.1.9

acpmGenEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "AC power monitor enable."
    ::= { acpmGenEntry 10 }
    --*******.4.1.3052.12.*********.1.10


acpmAvgVoltageTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigAvgVoltage
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor average voltage attributes."
    ::= { acPowerMonitor 2 }
    --*******.4.1.3052.12.*********

acpmAvgVoltageEntry OBJECT-TYPE
    SYNTAX ACPMConfigAvgVoltage
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor average voltage config table."
    INDEX { acpmAvgVoltageIndex }
    ::= { acpmAvgVoltageTable 1 }
    --*******.4.1.3052.12.*********.1

acpmAvgVoltageIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor average voltage config
        table."
    ::= { acpmAvgVoltageEntry 1 }
    --*******.4.1.3052.12.*********.1.1

acpmAvgVoltageEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor average voltage events."
    ::= { acpmAvgVoltageEntry 2 }
    --*******.4.1.3052.12.*********.1.2

acpmAvgVoltageDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband (hysteresis) for average voltage event.  This is a floating
        point value."
    ::= { acpmAvgVoltageEntry 3 }
    --*******.4.1.3052.12.*********.1.3

acpmAvgVoltageVHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold.  This is a floating point value."
    ::= { acpmAvgVoltageEntry 4 }
    --*******.4.1.3052.12.*********.1.4

acpmAvgVoltageVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions."
    ::= { acpmAvgVoltageEntry 5 }
    --*******.4.1.3052.12.*********.1.5

acpmAvgVoltageVHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (520..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number."
    ::= { acpmAvgVoltageEntry 6 }
    --*******.4.1.3052.12.*********.1.6

acpmAvgVoltageVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class."
    ::= { acpmAvgVoltageEntry 7 }
    --*******.4.1.3052.12.*********.1.7

acpmAvgVoltageHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold.  This is a floating point value."
    ::= { acpmAvgVoltageEntry 8 }
    --*******.4.1.3052.12.*********.1.8

acpmAvgVoltageHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions."
    ::= { acpmAvgVoltageEntry 9 }
    --*******.4.1.3052.12.*********.1.9

acpmAvgVoltageHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (520..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number."
    ::= { acpmAvgVoltageEntry 10 }
    --*******.4.1.3052.12.*********.1.10

acpmAvgVoltageHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class."
    ::= { acpmAvgVoltageEntry 11 }
    --*******.4.1.3052.12.*********.1.11

acpmAvgVoltageNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal actions."
    ::= { acpmAvgVoltageEntry 12 }
    --*******.4.1.3052.12.*********.1.12

acpmAvgVoltageNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (520..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal trap number."
    ::= { acpmAvgVoltageEntry 13 }
    --*******.4.1.3052.12.*********.1.13

acpmAvgVoltageNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal class."
    ::= { acpmAvgVoltageEntry 14 }
    --*******.4.1.3052.12.*********.1.14

acpmAvgVoltageLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold.  This is a floating point value."
    ::= { acpmAvgVoltageEntry 15 }
    --*******.4.1.3052.12.*********.1.15

acpmAvgVoltageLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions."
    ::= { acpmAvgVoltageEntry 16 }
    --*******.4.1.3052.12.*********.1.16

acpmAvgVoltageLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (520..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number."
    ::= { acpmAvgVoltageEntry 17 }
    --*******.4.1.3052.12.*********.1.17

acpmAvgVoltageLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class."
    ::= { acpmAvgVoltageEntry 18 }
    --*******.4.1.3052.12.*********.1.18

acpmAvgVoltageVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold.  This is a floating point value."
    ::= { acpmAvgVoltageEntry 19 }
    --*******.4.1.3052.12.*********.1.19

acpmAvgVoltageVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions."
    ::= { acpmAvgVoltageEntry 20 }
    --*******.4.1.3052.12.*********.1.20

acpmAvgVoltageVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (520..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event trap number."
    ::= { acpmAvgVoltageEntry 21 }
    --*******.4.1.3052.12.*********.1.21

acpmAvgVoltageVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class."
    ::= { acpmAvgVoltageEntry 22 }
    --*******.4.1.3052.12.*********.1.22

acpmAvgVoltageSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting enable for average voltage telemetry."
    ::= { acpmAvgVoltageEntry 23 }
    --*******.4.1.3052.12.*********.1.23

acpmAvgVoltageSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting threshold for average voltage telemetry.  This is a
        floating point value."
    ::= { acpmAvgVoltageEntry 24 }
    --*******.4.1.3052.12.*********.1.24

acpmAvgVoltageSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting limit for average voltage telemetry."
    ::= { acpmAvgVoltageEntry 25 }
    --*******.4.1.3052.12.*********.1.25


acpmAvgCurrentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigAvgCurrent
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor average current attributes."
    ::= { acPowerMonitor 3 }
    --*******.4.1.3052.**********.3

acpmAvgCurrentEntry OBJECT-TYPE
    SYNTAX ACPMConfigAvgCurrent
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor average current config table."
    INDEX { acpmAvgCurrentIndex }
    ::= { acpmAvgCurrentTable 1 }
    --*******.4.1.3052.**********.3.1

acpmAvgCurrentIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor average current config
        table."
    ::= { acpmAvgCurrentEntry 1 }
    --*******.4.1.3052.**********.3.1.1

acpmAvgCurrentEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor average current events."
    ::= { acpmAvgCurrentEntry 2 }
    --*******.4.1.3052.**********.3.1.2

acpmAvgCurrentDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband (hysteresis) for average current event.  This is a floating
        point value."
    ::= { acpmAvgCurrentEntry 3 }
    --*******.4.1.3052.**********.3.1.3

acpmAvgCurrentVHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold.  This is a floating point value."
    ::= { acpmAvgCurrentEntry 4 }
    --*******.4.1.3052.**********.3.1.4

acpmAvgCurrentVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions."
    ::= { acpmAvgCurrentEntry 5 }
    --*******.4.1.3052.**********.3.1.5

acpmAvgCurrentVHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (521..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number."
    ::= { acpmAvgCurrentEntry 6 }
    --*******.4.1.3052.**********.3.1.6

acpmAvgCurrentVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class."
    ::= { acpmAvgCurrentEntry 7 }
    --*******.4.1.3052.**********.3.1.7

acpmAvgCurrentHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold.  This is a floating point value."
    ::= { acpmAvgCurrentEntry 8 }
    --*******.4.1.3052.**********.3.1.8

acpmAvgCurrentHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions."
    ::= { acpmAvgCurrentEntry 9 }
    --*******.4.1.3052.**********.3.1.9

acpmAvgCurrentHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (521..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number."
    ::= { acpmAvgCurrentEntry 10 }
    --*******.4.1.3052.**********.3.1.10

acpmAvgCurrentHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class."
    ::= { acpmAvgCurrentEntry 11 }
    --*******.4.1.3052.**********.3.1.11

acpmAvgCurrentNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal actions."
    ::= { acpmAvgCurrentEntry 12 }
    --*******.4.1.3052.**********.3.1.12

acpmAvgCurrentNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (521..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal trap number."
    ::= { acpmAvgCurrentEntry 13 }
    --*******.4.1.3052.**********.3.1.13

acpmAvgCurrentNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal class."
    ::= { acpmAvgCurrentEntry 14 }
    --*******.4.1.3052.**********.3.1.14

acpmAvgCurrentLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold.  This is a floating point value."
    ::= { acpmAvgCurrentEntry 15 }
    --*******.4.1.3052.**********.3.1.15

acpmAvgCurrentLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions."
    ::= { acpmAvgCurrentEntry 16 }
    --*******.4.1.3052.**********.3.1.16

acpmAvgCurrentLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (521..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number."
    ::= { acpmAvgCurrentEntry 17 }
    --*******.4.1.3052.**********.3.1.17

acpmAvgCurrentLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class."
    ::= { acpmAvgCurrentEntry 18 }
    --*******.4.1.3052.**********.3.1.18

acpmAvgCurrentVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold.  This is a floating point value."
    ::= { acpmAvgCurrentEntry 19 }
    --*******.4.1.3052.**********.3.1.19

acpmAvgCurrentVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions."
    ::= { acpmAvgCurrentEntry 20 }
    --*******.4.1.3052.**********.3.1.20

acpmAvgCurrentVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (521..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event trap number."
    ::= { acpmAvgCurrentEntry 21 }
    --*******.4.1.3052.**********.3.1.21

acpmAvgCurrentVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class."
    ::= { acpmAvgCurrentEntry 22 }
    --*******.4.1.3052.**********.3.1.22

acpmAvgCurrentSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting enable for average current telemetry."
    ::= { acpmAvgCurrentEntry 23 }
    --*******.4.1.3052.**********.3.1.23

acpmAvgCurrentSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting threshold for average current telemetry.  This is
        a floating point value."
    ::= { acpmAvgCurrentEntry 24 }
    --*******.4.1.3052.**********.3.1.24

acpmAvgCurrentSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting limit for average current telemetry."
    ::= { acpmAvgCurrentEntry 25 }
    --*******.4.1.3052.**********.3.1.25


acpmFreqTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigFreq
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor frequency attributes."
    ::= { acPowerMonitor 4 }
    --*******.4.1.3052.**********.4

acpmFreqEntry OBJECT-TYPE
    SYNTAX ACPMConfigFreq
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor frequency config table."
    INDEX { acpmFreqIndex }
    ::= { acpmFreqTable 1 }
    --*******.4.1.3052.**********.4.1

acpmFreqIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor frequency config table."
    ::= { acpmFreqEntry 1 }
    --*******.4.1.3052.****************

acpmFreqEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor frequency events."
    ::= { acpmFreqEntry 2 }
    --*******.4.1.3052.**********.4.1.2

acpmFreqDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband (hysteresis) for frequency event.  This is a floating point
        value."
    ::= { acpmFreqEntry 3 }
    --*******.4.1.3052.**********.4.1.3

acpmFreqVHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold.  This is a floating point value."
    ::= { acpmFreqEntry 4 }
    --*******.4.1.3052.**********.4.1.4

acpmFreqVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions."
    ::= { acpmFreqEntry 5 }
    --*******.4.1.3052.**********.4.1.5

acpmFreqVHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (522..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number."
    ::= { acpmFreqEntry 6 }
    --*******.4.1.3052.**********.4.1.6

acpmFreqVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class."
    ::= { acpmFreqEntry 7 }
    --*******.4.1.3052.**********.4.1.7

acpmFreqHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold.  This is a floating point value."
    ::= { acpmFreqEntry 8 }
    --*******.4.1.3052.**********.4.1.8

acpmFreqHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions."
    ::= { acpmFreqEntry 9 }
    --*******.4.1.3052.**********.4.1.9

acpmFreqHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (522..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number."
    ::= { acpmFreqEntry 10 }
    --*******.4.1.3052.****************0

acpmFreqHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class."
    ::= { acpmFreqEntry 11 }
    --*******.4.1.3052.****************1

acpmFreqNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal actions."
    ::= { acpmFreqEntry 12 }
    --*******.4.1.3052.****************2

acpmFreqNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (522..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal trap number."
    ::= { acpmFreqEntry 13 }
    --*******.4.1.3052.****************3

acpmFreqNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal class."
    ::= { acpmFreqEntry 14 }
    --*******.4.1.3052.****************4

acpmFreqLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold.  This is a floating point value."
    ::= { acpmFreqEntry 15 }
    --*******.4.1.3052.****************5

acpmFreqLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions."
    ::= { acpmFreqEntry 16 }
    --*******.4.1.3052.****************6

acpmFreqLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (522..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number."
    ::= { acpmFreqEntry 17 }
    --*******.4.1.3052.****************7

acpmFreqLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class."
    ::= { acpmFreqEntry 18 }
    --*******.4.1.3052.****************8

acpmFreqVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold.  This is a floating point value."
    ::= { acpmFreqEntry 19 }
    --*******.4.1.3052.****************9

acpmFreqVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions."
    ::= { acpmFreqEntry 20 }
    --*******.4.1.3052.**********.4.1.20

acpmFreqVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (522..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event trap number."
    ::= { acpmFreqEntry 21 }
    --*******.4.1.3052.**********.4.1.21

acpmFreqVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class."
    ::= { acpmFreqEntry 22 }
    --*******.4.1.3052.**********.4.1.22

acpmFreqSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting enable for frequency telemetry."
    ::= { acpmFreqEntry 23 }
    --*******.4.1.3052.**********.4.1.23

acpmFreqSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting threshold for frequency telemetry.  This is a
        floating point value."
    ::= { acpmFreqEntry 24 }
    --*******.4.1.3052.**********.4.1.24

acpmFreqSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting limit for frequency telemetry."
    ::= { acpmFreqEntry 25 }
    --*******.4.1.3052.**********.4.1.25


acpmTotalRealPowerTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigTRP
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor total real power attributes."
    ::= { acPowerMonitor 5 }
    --*******.4.1.3052.**********.5

acpmTRPEntry OBJECT-TYPE
    SYNTAX ACPMConfigTRP
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor total real power config table."
    INDEX { acpmTRPIndex }
    ::= { acpmTotalRealPowerTable 1 }
    --*******.4.1.3052.**********.5.1

acpmTRPIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor total real power
        config table."
    ::= { acpmTRPEntry 1 }
    --*******.4.1.3052.**********.5.1.1

acpmTRPEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor total real power events."
    ::= { acpmTRPEntry 2 }
    --*******.4.1.3052.**********.5.1.2

acpmTRPDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband (hysteresis) for total real power event.  This is a
        floating point value."
    ::= { acpmTRPEntry 3 }
    --*******.4.1.3052.**********.5.1.3

acpmTRPVHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event threshold.  This is a floating point value."
    ::= { acpmTRPEntry 4 }
    --*******.4.1.3052.**********.5.1.4

acpmTRPVHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event actions."
    ::= { acpmTRPEntry 5 }
    --*******.4.1.3052.**********.5.1.5

acpmTRPVHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (523..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event trap number."
    ::= { acpmTRPEntry 6 }
    --*******.4.1.3052.**********.5.1.6

acpmTRPVHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very high event class."
    ::= { acpmTRPEntry 7 }
    --*******.4.1.3052.**********.5.1.7

acpmTRPHighValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event threshold.  This is a floating point value."
    ::= { acpmTRPEntry 8 }
    --*******.4.1.3052.**********.5.1.8

acpmTRPHighActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event actions."
    ::= { acpmTRPEntry 9 }
    --*******.4.1.3052.**********.5.1.9

acpmTRPHighTrapNum OBJECT-TYPE
    SYNTAX Integer32 (523..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event trap number."
    ::= { acpmTRPEntry 10 }
    --*******.4.1.3052.**********.5.1.10

acpmTRPHighClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "High event class."
    ::= { acpmTRPEntry 11 }
    --*******.4.1.3052.**********.5.1.11

acpmTRPNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal actions."
    ::= { acpmTRPEntry 12 }
    --*******.4.1.3052.**********.5.1.12

acpmTRPNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (523..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal trap number."
    ::= { acpmTRPEntry 13 }
    --*******.4.1.3052.**********.5.1.13

acpmTRPNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Return to normal class."
    ::= { acpmTRPEntry 14 }
    --*******.4.1.3052.**********.5.1.14

acpmTRPLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event threshold.  This is a floating point value."
    ::= { acpmTRPEntry 15 }
    --*******.4.1.3052.**********.5.1.15

acpmTRPLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event actions."
    ::= { acpmTRPEntry 16 }
    --*******.4.1.3052.**********.5.1.16

acpmTRPLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (523..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event trap number."
    ::= { acpmTRPEntry 17 }
    --*******.4.1.3052.**********.5.1.17

acpmTRPLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low event class."
    ::= { acpmTRPEntry 18 }
    --*******.4.1.3052.**********.5.1.18

acpmTRPVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event threshold.  This is a floating point value."
    ::= { acpmTRPEntry 19 }
    --*******.4.1.3052.**********.5.1.19

acpmTRPVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event actions."
    ::= { acpmTRPEntry 20 }
    --*******.4.1.3052.**********.5.1.20

acpmTRPVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32 (523..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event trap number."
    ::= { acpmTRPEntry 21 }
    --*******.4.1.3052.**********.5.1.21

acpmTRPVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very low event class."
    ::= { acpmTRPEntry 22 }
    --*******.4.1.3052.**********.5.1.22

acpmTRPSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting enable for total real power telemetry."
    ::= { acpmTRPEntry 23 }
    --*******.4.1.3052.**********.5.1.23

acpmTRPSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting threshold for total real power telemetry.  This is a
        floating point value."
    ::= { acpmTRPEntry 24 }
    --*******.4.1.3052.**********.5.1.24

acpmTRPSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting limit for total real power telemetry."
    ::= { acpmTRPEntry 25 }
    --*******.4.1.3052.**********.5.1.25


acpmDisconnectTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigDisconnect
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor device disconnect event
        attributes."
    ::= { acPowerMonitor 6 }
    --*******.4.1.3052.**********.6

acpmDisconnectEntry OBJECT-TYPE
    SYNTAX ACPMConfigDisconnect
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor device disconnect event config table."
    INDEX { acpmDisconnectIndex }
    ::= { acpmDisconnectTable 1 }
    --*******.4.1.3052.**********.6.1

acpmDisconnectIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor device disconnect event
        config table."
    ::= { acpmDisconnectEntry 1 }
    --*******.4.1.3052.**********.6.1.1

acpmDisconnectEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor device disconnect event."
    ::= { acpmDisconnectEntry 2 }
    --*******.4.1.3052.**********.6.1.2

acpmDisconnectActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for disconnect event."
    ::= { acpmDisconnectEntry 3 }
    --*******.4.1.3052.**********.6.1.3

acpmDisconnectTrapNum OBJECT-TYPE
    SYNTAX Integer32 (524..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for disconnect event."
    ::= { acpmDisconnectEntry 4 }
    --*******.4.1.3052.**********.6.1.4

acpmDisconnectClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for disconnect event."
    ::= { acpmDisconnectEntry 5 }
    --*******.4.1.3052.**********.6.1.5

acpmDisconnectNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for disconnect return to normal event."
    ::= { acpmDisconnectEntry 6 }
    --*******.4.1.3052.**********.6.1.6

acpmDisconnectNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32 (524..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for disconnect return to normal event."
    ::= { acpmDisconnectEntry 7 }
    --*******.4.1.3052.**********.6.1.7

acpmDisconnectNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class for disconnect return to normal event."
    ::= { acpmDisconnectEntry 8 }
    --*******.4.1.3052.**********.6.1.8

acpmTotalPowerFactorTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ACPMConfigTotalPowerFactor
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table organizes AC power monitor device total power factor
        event attributes."
    ::= { acPowerMonitor 7 }
    --*******.4.1.3052.**********.7

acpmTPFEntry OBJECT-TYPE
    SYNTAX ACPMConfigTotalPowerFactor
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for AC power monitor device total power factor event config
        table."
    INDEX { acpmTPFIndex }
    ::= { acpmTotalPowerFactorTable 1 }
    --*******.4.1.3052.**********.7.1

acpmTPFIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Which AC power monitor in the AC power monitor device disconnect
        event config table."
    ::= { acpmTPFEntry 1 }
    --*******.4.1.3052.**********.7.1.1

acpmTPFEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable AC power monitor device disconnect event."
    ::= { acpmTPFEntry 2 }
    --*******.4.1.3052.**********.7.1.2

acpmTPFDeadband OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Deadband (hysteresis)."
    ::= { acpmTPFEntry 3 }
    --*******.4.1.3052.**********.7.1.3

acpmTPFNormalActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Normal actions."
    ::= { acpmTPFEntry 4 }
    --*******.4.1.3052.**********.7.1.4

acpmTPFNormalTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Normal trap number."
    ::= { acpmTPFEntry 5 }
    --*******.4.1.3052.**********.7.1.5

acpmTPFNormalClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Normal class."
    ::= { acpmTPFEntry 6 }
    --*******.4.1.3052.**********.7.1.6

acpmTPFLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low threshold."
    ::= { acpmTPFEntry 7 }
    --*******.4.1.3052.**********.7.1.7

acpmTPFLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low actions."
    ::= { acpmTPFEntry 8 }
    --*******.4.1.3052.**********.7.1.8

acpmTPFLowTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low trap number."
    ::= { acpmTPFEntry 9 }
    --*******.4.1.3052.**********.7.1.9

acpmTPFLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Low class."
    ::= { acpmTPFEntry 10 }
    --*******.4.1.3052.**********.7.1.10

acpmTPFVLowValue OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low threshold."
    ::= { acpmTPFEntry 11 }
    --*******.4.1.3052.**********.7.1.11

acpmTPFVLowActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low actions."
    ::= { acpmTPFEntry 12 }
    --*******.4.1.3052.**********.7.1.12

acpmTPFVLowTrapNum OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low trap number."
    ::= { acpmTPFEntry 13 }
    --*******.4.1.3052.**********.7.1.13

acpmTPFVLowClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Very Low class."
    ::= { acpmTPFEntry 14 }
    --*******.4.1.3052.**********.7.1.14

acpmTPFSysrepEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting enable."
    ::= { acpmTPFEntry 15 }
    --*******.4.1.3052.**********.7.1.15

acpmTPFSysrepThreshold OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting threshold."
    ::= { acpmTPFEntry 16 }
    --*******.4.1.3052.**********.7.1.16

acpmTPFSysrepLimit OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "System reporting limit."
    ::= { acpmTPFEntry 17 }
    --*******.4.1.3052.**********.7.1.17



--***************************************************************************************
--Reset Event Section   2.12.16
--***************************************************************************************


evResetEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable/disable reset event.  This event is triggered when the unit
        finishes booting."
    ::= { evReset 1 }
    --*******.4.1.3052.*********6.1

evResetDelay OBJECT-TYPE
    SYNTAX Integer32 (0..3600)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of seconds to wait after unit finishes booting before triggering
        the reset event."
    ::= { evReset 2 }
    --*******.4.1.3052.*********6.2

evResetActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Actions for reset event."
    ::= { evReset 3 }
    --*******.4.1.3052.*********6.3

evResetMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Message for reset event."
    ::= { evReset 4 }
    --*******.4.1.3052.*********6.4

evResetTrapnum OBJECT-TYPE
    SYNTAX Integer32 (543..1199)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Trap number for reset event."
    ::= { evReset 5 }
    --*******.4.1.3052.*********6.5

evResetClass OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Class (severity) for reset event."
    ::= { evReset 6 }
    --*******.4.1.3052.*********6.6


--***************************************************************************************
--Action Section   2.14
--***************************************************************************************



actionCallNumberTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ActionCallNumberConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of phone numbers to dial for modem actions"
    ::= { action 1 }
    --*******.4.1.3052.*********

actionCallNumberEntry OBJECT-TYPE
    SYNTAX ActionCallNumberConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of phone numbers to dial for modem actions"
    INDEX { actionCallNumberIndex }
    ::= { actionCallNumberTable 1 }
    --*******.4.1.3052.*********.1

actionCallNumberIndex OBJECT-TYPE
    SYNTAX Integer32 (1..4)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of phone numbers to dial for modem actions"
    ::= { actionCallNumberEntry 1 }
    --*******.4.1.3052.*********.1.1

actionCallNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The number to dial for this action slot"
    ::= { actionCallNumberEntry 2 }
    --*******.4.1.3052.*********.1.2

actionPagerTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ActionPagerConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of pager configurations"
    ::= { action 2 }
    --*******.4.1.3052.*********

actionPagerEntry OBJECT-TYPE
    SYNTAX ActionPagerConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of pager configurations"
    INDEX { actionPagerIndex }
    ::= { actionPagerTable 1 }
    --*******.4.1.3052.*********.1

actionPagerIndex OBJECT-TYPE
    SYNTAX Integer32 (1..4)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "index for table of pager configurations"
    ::= { actionPagerEntry 1 }
    --*******.4.1.3052.*********.1.1

actionPagerType OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "type of this pager configuration"
    ::= { actionPagerEntry 2 }
    --*******.4.1.3052.*********.1.2

actionPagerNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "phone number to dial for this pager configuration"
    ::= { actionPagerEntry 3 }
    --*******.4.1.3052.*********.1.3

actionPagerID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ID to use for this pager configuration"
    ::= { actionPagerEntry 4 }
    --*******.4.1.3052.*********.1.4

actionPagerMessage OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "message to use for this pager configuration"
    ::= { actionPagerEntry 5 }
    --*******.4.1.3052.*********.1.5

actionPagerPostCalloutDelay OBJECT-TYPE
    SYNTAX Integer32 (0..255)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Seconds to wait after dialing before sending ID"
    ::= { actionPagerEntry 6 }
    --*******.4.1.3052.*********.1.6

actionPagerPostIDDelay OBJECT-TYPE
    SYNTAX Integer32 (0..255)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Seconds to wait after sending ID to send message"
    ::= { actionPagerEntry 7 }
    --*******.4.1.3052.*********.1.7

actionSchedEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable action schedule"
    ::= { actionSched 1 }
    --*******.4.1.3052.12.********

actionSchedBegin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "action schedule begin time (HH:MM)"
    ::= { actionSched 2 }
    --*******.4.1.3052.12.********

actionSchedEnd OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "action schedule end time (HH:MM)"
    ::= { actionSched 3 }
    --*******.4.1.3052.12.********

actionSchedWeekdaysOnly OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF whether the action schedule applies to weekdays only
        (ON) or all days (OFF)"
    ::= { actionSched 4 }
    --*******.4.1.3052.12.********

actionAsentriaRequireAck OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Asentria action requires an ack from the receiver"
    ::= { actionAsentria 1 }
    --*******.4.1.3052.12.********

actionAsentriaVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "version 1.0 or 1.1"
    ::= { actionAsentria 2 }
    --*******.4.1.3052.12.********

actionAsentriaTCPPort OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TCP port used for TCP-based Asentria alarms"
    ::= { actionAsentria 3 }
    --*******.4.1.3052.12.********

actionHostTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ActionHostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of network hosts to use for network-based actions"
    ::= { action 6 }
    --*******.4.1.3052.*********

actionHostEntry OBJECT-TYPE
    SYNTAX ActionHostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of network hosts to use for network-based
        actions"
    INDEX { actionHostIndex }
    ::= { actionHostTable 1 }
    --*******.4.1.3052.12.********

actionHostIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of network hosts to use for network-based
        actions"
    ::= { actionHostEntry 1 }
    --*******.4.1.3052.12.********.1

actionHost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Hostname/IP address used as destination for network-based
        action (e.g., trap)"
    ::= { actionHostEntry 2 }
    --*******.4.1.3052.12.********.2

actionEmailTable OBJECT-TYPE
    SYNTAX SEQUENCE OF ActionEmailConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of email addresses to use for email actions"
    ::= { action 7 }
    --*******.4.1.3052.*********

actionEmailEntry OBJECT-TYPE
    SYNTAX ActionEmailConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of email addresses to use for email actions"
    INDEX { actionEmailIndex }
    ::= { actionEmailTable 1 }
    --*******.4.1.3052.12.********

actionEmailIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of email addresses to use for email actions"
    ::= { actionEmailEntry 1 }
    --*******.4.1.3052.12.********.1

actionEmail OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "email address"
    ::= { actionEmailEntry 2 }
    --*******.4.1.3052.12.********.2

actionParseError OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "When a user attempts to configure an invalid action setting/
        object, this object says why it was invalid."
    ::= { action 8 }
    --*******.4.1.3052.*********

--***************************************************************************************
--System Section   2.16
--***************************************************************************************


sysTimeAutoDST OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF automatically adjust for Daylight Savings Time"
    ::= { sysTime 1 }
    --*******.4.1.3052.12.********

sysTimeGMTOffset OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Absolute value of offset (hours) from GMT of the timezone"
    ::= { sysTime 2 }
    --*******.4.1.3052.12.********

sysTimeGMTDirection OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Direction (AHEAD/BEHIND) of timezone relative to GMT"
    ::= { sysTime 3 }
    --*******.4.1.3052.12.********


sysTimeNetEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Network time mode"
    ::= { sysTimeNet 1 }
    --*******.4.1.3052.12.********.1

sysTimeNetHostTable OBJECT-TYPE
    SYNTAX SEQUENCE OF NetworkTimeHostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of hosts to use for network time services"
    ::= { sysTimeNet 2 }
    --*******.4.1.3052.12.********.2

sysTimeNetHostEntry OBJECT-TYPE
    SYNTAX NetworkTimeHostConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of hosts to use for network time services"
    INDEX { sysTimeNetHostIndex }
    ::= { sysTimeNetHostTable 1 }
    --*******.4.1.3052.12.********.2.1

sysTimeNetHostIndex OBJECT-TYPE
    SYNTAX Integer32 (1..6)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of hosts to use for network time services"
    ::= { sysTimeNetHostEntry 1 }
    --*******.4.1.3052.12.********.2.1.1

sysTimeNetHost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "host to use for network time services"
    ::= { sysTimeNetHostEntry 2 }
    --*******.4.1.3052.12.********.2.1.2

sysPTTimeout  OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Timeout (minutes) of inactivity after which a pass-through
        session is terminated (0 means it never terminates)"
    ::= { sysPT 1 }
    --*******.4.1.3052.12.********

sysPTEndPause OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "number of 16ths of a second between escape characters
        received on pass-through origin IO handle that are reuired
        for the escape characters to register as pass-through escape
        characters in the pass-through escape sequence.  Set to 0 to
        make the escape character register as part of the pass-
        through escape sequence without requiring a pause."
    ::= { sysPT 2 }
    --*******.4.1.3052.12.********

sysPTJoinable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Enable joinable pass-through sessions."
    ::= { sysPT 3 }
    --*******.4.1.3052.12.********

sysMTU OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum transfer unit for ethernet interfaces"
    ::= { sys 3 }
    --*******.4.1.3052.*********

sysAnswerString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "String displayed upon login"
    ::= { sys 4 }
    --*******.4.1.3052.*********

sysEventFileID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ID string for events file"
    ::= { sys 6 }
    --*******.4.1.3052.*********

sysEscapeCharacter OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Decimal ASCII code of the system escape character"
    ::= { sys 7 }
    --*******.4.1.3052.*********

sysTimeStampTimeFormat OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Format of time stamp: HH:MM,HH:MM:SS,BLANK"
    ::= { sysTimeStamp 1 }
    --*******.4.1.3052.12.********

sysTimeStampDateFormat OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Format of date stamp: MM/DD,MM/DD/YY,MM/DD/YYYY,BLANK"
    ::= { sysTimeStamp 2 }
    --*******.4.1.3052.12.********

sysTimeStampSpaceAfter OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF Inlude a space after timestamp"
    ::= { sysTimeStamp 3 }
    --*******.4.1.3052.12.********

sysLogMode OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Syslog mode: disabled, emit UDP (REMOTE), save in a
        rotating file set (FILE), or emit on a listening socket
        (SERVER)"
    ::= { sysLog 1 }
    --*******.4.1.3052.12.********

sysLoghost OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Syslog host: destination machine when syslog mode is REMOTE"
    ::= { sysLog 2 }
    --*******.4.1.3052.12.********

sysLogFilter OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "String describing what functions place data in the syslog"
    ::= { sysLog 3 }
    --*******.4.1.3052.12.********

sysLogFileSize OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Size (in KB) of syslog files (when syslog mode is SERVER or
        FILE)"
    ::= { sysLog 4 }
    --*******.4.1.3052.12.********

sysLogFileCount OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Number of files in syslog file set (when syslog mode is
        SERVER or FILE)"
    ::= { sysLog 5 }
    --*******.4.1.3052.12.********

sysLogListenPort OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TCP port on which the unit listens for a connection on
        which syslog data is dumped (when syslog mode is SERVER)"
    ::= { sysLog 6 }
    --*******.4.1.3052.12.********

sysCRDBCapacity OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Capacity of Call Record Database (in KB)"
    ::= { sysCRDB 1 }
    --*******.4.1.3052.12.*********

sysCRDBPercentFull OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Percent of the CRDB used"
    ::= { sysCRDB 2 }
    --*******.4.1.3052.12.*********


sysCRDBFileIDTable OBJECT-TYPE
    SYNTAX SEQUENCE OF CRDBFileIDConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of CRDB file IDs"
    ::= { sysCRDB 3 }
    --*******.4.1.3052.12.*********

sysCRDBFileIDEntry OBJECT-TYPE
    SYNTAX CRDBFileIDConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of CRDB file IDs"
    INDEX { sysCRDBFileIDIndex }
    ::= { sysCRDBFileIDTable 1 }
    --*******.4.1.3052.12.*********.1

sysCRDBFileIDIndex OBJECT-TYPE
    SYNTAX Integer32 (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of CRDB file IDs"
    ::= { sysCRDBFileIDEntry 1 }
    --*******.4.1.3052.12.*********.1.1

sysCRDBFileID OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ID of CRDB file for this port-associated file x"
    ::= { sysCRDBFileIDEntry 2 }
    --*******.4.1.3052.12.*********.1.2

sysCRDBFileEnforceMinTable OBJECT-TYPE
    SYNTAX SEQUENCE OF CRDBFileEnforceMinConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Table of CRDB file minimum size enforcement settings"
    ::= { sysCRDB 4 }
    --*******.4.1.3052.12.*********

sysCRDBFileEnforceMinEntry OBJECT-TYPE
    SYNTAX CRDBFileEnforceMinConfig
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Entry for table of CRDB file minimum size enforcement
        settings"
    INDEX { sysCRDBFileEnforceMinIndex }
    ::= { sysCRDBFileEnforceMinTable 1 }
    --*******.4.1.3052.12.*********.1

sysCRDBFileEnforceMinIndex OBJECT-TYPE
    SYNTAX Integer32 (1..24)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Index for table of CRDB file minimum size enforcement
        settings"
    ::= { sysCRDBFileEnforceMinEntry 1 }
    --*******.4.1.3052.12.*********.1.1

sysCRDBFileEnforceMin OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF: enforce minimum size for this CRDB file x"
    ::= { sysCRDBFileEnforceMinEntry 2 }
    --*******.4.1.3052.12.*********.1.2

sysCharMask OBJECT-TYPE
    SYNTAX OCTET STRING
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Character mask used for ignoring certain characters on
        serial ports when assembling data records (when port mode is
        ASCII)"
    ::= { sys 11 }
    --*******.4.1.3052.**********

sysPrompt OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Prompt to use in a commnand session"
    ::= { sys 12 }
    --*******.4.1.3052.**********

sysBootStatus OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "String describing the boot status of the unit (Booted OK/
        still booting"
    ::= { sys 13 }
    --*******.4.1.3052.**********

sysLocLatitude OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Latitude portion of geographic coordinates as string"
    ::= { sysLoc 1 }
    --*******.4.1.3052.**********.1

sysLocLongitude OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Longitude portion of geographic coordinates as string"
    ::= { sysLoc 2 }
    --*******.4.1.3052.**********.2

sysLocXOffset OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "X-offset portion of geographic coordinates as string"
    ::= { sysLoc 3 }
    --*******.4.1.3052.**********.3

sysLocYOffset OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Y offset portion of geographic coordinates as string"
    ::= { sysLoc 4 }
    --*******.4.1.3052.**********.4

sysLocAngle OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Angle portion of geographic coordinates as string"
    ::= { sysLoc 5 }
    --*******.4.1.3052.**********.5

sysLocAltitude OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Altitude as string"
    ::= { sysLoc 6 }
    --*******.4.1.3052.**********.6

--***************************************************************************************
--Sys Asset Managment 2.16.15
--***************************************************************************************
sysAMManufacturer OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "Device manufacturer"
    ::= { sysAssetMgmt 1 }
    --*******.4.1.3052.**********.1

sysAMProduct OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "Device product description (i.e., model or model number)"
    ::= { sysAssetMgmt 2 }
    --*******.4.1.3052.**********.2

sysAMSerialNumber OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "Manufacturer serial number for device"
    ::= { sysAssetMgmt 3 }
    --*******.4.1.3052.**********.3

sysAMHardwareOptions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "Hardware options currently installed on device"
    ::= { sysAssetMgmt 4 }
    --*******.4.1.3052.**********.4

sysAMSoftwareVersion OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
    "Currently-running software version on device"
    ::= { sysAssetMgmt 5 }
    --*******.4.1.3052.**********.5

sysAMSiteName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
    "Site Name of device (user definable, also known as the
    siteName object under the productIds branch."
    ::= { sysAssetMgmt 6 }
    --*******.4.1.3052.**********.6

--***************************************************************************************
--Audit Log Section   2.17
--***************************************************************************************

auditLogEnable OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF enable audit log"
    ::= { auditLog 1 }
    --*******.4.1.3052.*********

auditLogStoreResets OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores when the unit resets"
    ::= { auditLog 2 }
    --*******.4.1.3052.*********

auditLogStoreCommands OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores commands entered in a command
        session"
    ::= { auditLog 3 }
    --*******.4.1.3052.*********

auditLogStoreOutputs OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores output (relay output and power output)
        activity"
    ::= { auditLog 4 }
    --*******.4.1.3052.*********

auditLogStoreAlarmActions OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores action delivery activity"
    ::= { auditLog 5 }
    --*******.4.1.3052.*********

auditLogStorePwdFailures OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores when user was denied login"
    ::= { auditLog 6 }
    --*******.4.1.3052.*********

auditLogStoreLogins OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores when a user was allowed login"
    ::= { auditLog 7 }
    --*******.4.1.3052.*********

auditLogStoreSHSK OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores serial handshaking status
        (regardless of serial handshaking event)"
    ::= { auditLog 8 }
    --*******.4.1.3052.*********

auditLogStorePassthrough OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores pass-through activity"
    ::= { auditLog 9 }
    --*******.4.1.3052.*********

auditLogStoreInactivity OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores when sessions expire due to
        inactivity"
    ::= { auditLog 10 }
    --*******.4.1.3052.*********

auditLogStorePolling OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "ON/OFF audit log stores CRDB polling activity"
    ::= { auditLog 11 }
    --*******.4.1.3052.*********

auditLogMaxSize OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Maximum size (in KB) of audit log"
    ::= { auditLog 12 }
    --*******.4.1.3052.*********

--***************************************************************************************
--Product IDs Section   3
--***************************************************************************************

siteName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Site Name string; Max 40 characters."
    ::= { productIds 1 }
    --*******.4.1.3052.12.3.1

thisProduct OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This is a factory configured string for the
        product name."
    ::= { productIds 2 }
    --*******.4.1.3052.12.3.2

stockTrapString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The message defined for the event that triggers a trap."
    ::= { productIds 3 }
    --*******.4.1.3052.12.3.3

trapEventTypeNumber OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the type of
        event that happened.  Definitions:
           4    Scheduled
           7    Serial Handshaking Low
           8    Serial Handshaking High
           10   Contact Closure Active
           11   Contact Closure Inactive
           12   Temperature High
           13   Temperature Very High
           14   Temperature Low
           15   Temperature Very Low
           16   Temperature Return to Normal
           17   Humidity High
           18   Humidity Very High
           19   Humidity Low
           20   Humidity Very Low
           21   Humidity Return to Normal
           26   Analog High
           27   Analog Very High
           28   Analog Low
           29   Analog Very Low
           30   Analog Normal
           40   EventSensor Unresponsive
           41   Custom
           42   CPE Down
           44   CPE Normal
           83   CPE Down Reminder
           100  Contact Closure Active Reminder
           101  Temperature High Reminder
           102  Temperature High Very Reminder
           103  Temperature Low Reminder
           104  Temperature Very Low Reminder
           105  Humidity High Reminder
           106  Humidity Very High Reminder
           107  Humidity Low Reminder
           108  Humidity Very Low Reminder
           109  Analog High Reminder
           110  Analog Very High Reminder
           111  Analog Low Reminder
           112  Analog Very Low Reminder
           130  Relay Output Active
           131  Relay Output Inactive
           132  Power Output On
           133  Power Output Off
           164  Reset
           "
    ::= { productIds 4 }
    --*******.4.1.3052.12.3.4

trapEventTypeName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the string
        corresponding to the event type."
    ::= { productIds 5 }
    --*******.4.1.3052.12.3.5

trapIncludedValue OBJECT-TYPE
    SYNTAX Integer32 (-2147483648..2147483647)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the generic
        included value."
    ::= { productIds 6 }
    --*******.4.1.3052.12.3.6

trapIncludedString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the generic
        included string."
    ::= { productIds 7 }
    --*******.4.1.3052.12.3.7

trapTypeString OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The cause of the trap, added for DL880 compatibility."
    ::= { productIds 8 }
    --*******.4.1.3052.12.3.8

trapEventClassNumber OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the configurable
        class number associated with the triggering event."
    ::= { productIds 9 }
    --*******.4.1.3052.12.3.9

trapEventClassName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The user-defined trap variable for the configurable
         class name associated with the class number."
    ::= { productIds 10 }
    --*******.4.1.3052.12.3.10


--***************************************************************************************
--Product IDs Section   3
--***************************************************************************************

keyInterface OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This object represents a setting key interface.  When
        setting this object, the value to set is the 'sk' command
        you would enter on the command line (without the 'sk').  To
        get the result of the operation, read this object after you
        write it.

        For example, to configure EventSensor 1 contact closure 34
        event actions to be an SNMP InformRequest to host foo, set
        this object to:
        'event.sensor[1].cc[34].eventactions=inform(foo)'.  The SNMP
        response is the result of the operation: if setting that key
        to that value was a success then the SNMP response will be a
        success and the response value will be what you had set.  If
        you subsequently read this object then you will see a
        message 'OK: ...'.

        If setting that key to that value failed then the SNMP
        response will be a failure (a badValue response).  If you
        subsequently read this object then the value returned is the
        reason that setting that key to that value failed.

        For another example, to read user 3's pass-through access
        for port 7, set this object to:
        'sec.user[3].port[7].ptaccess'.  The SNMP response is the
        result of the operation: if reading that key is a success
        then the SNMP response will be a success and the response
        value will be what you had SET (i.e., the opertion of
        reading a key, which is just the key itself with no '='
        operator, and this is not the key value you want to read).
        If you subsequently read this object then the value
        returned is the value of the key you want to read.

        If reading that key failed (e.g., if the key is invalid)
        then the SNMP response to your SNMP set operation (the
        value you set being the invalid key to read) will be a
        failure (a badValue response).  If you subsequently read
        this object then the value returned is the reason that
        reading that key failed (i.e., 'invalid key')."
    ::= { s530 4 }
    --*******.4.1.3052.12.4


--***************************************************************************************
--TABLE DEFINITIONS
--***************************************************************************************

ESPointConfigTemp ::=
    SEQUENCE {
    espcTempIndexES
            Integer32,
    espcTempIndexPoint
            Integer32,
    espcTempEnable
            DisplayString,
    espcTempScale
            DisplayString,
    espcTempDeadband
            DisplayString,
    espcTempVHighTemp
            DisplayString,
    espcTempVHighActions
            DisplayString,
    espcTempVHighTrapnum
            Integer32,
    espcTempVHighClass
            DisplayString,
    espcTempHighTemp
            DisplayString,
    espcTempHighActions
            DisplayString,
    espcTempHighTrapnum
            Integer32,
    espcTempHighClass
            DisplayString,
    espcTempNormalActions
            DisplayString,
    espcTempNormalTrapnum
            Integer32,
    espcTempNormalClass
            DisplayString,
    espcTempLowTemp
            DisplayString,
    espcTempLowActions
            DisplayString,
    espcTempLowTrapnum
            Integer32,
    espcTempLowClass
            DisplayString,
    espcTempVLowTemp
            DisplayString,
    espcTempVLowActions
            DisplayString,
    espcTempVLowTrapnum
            Integer32,
    espcTempVLowClass
            DisplayString
}


ESPointConfigCC ::=
    SEQUENCE {
        espcCCIndexES
            Integer32,
        espcCCIndexPoint
            Integer32,
        espcCCEnable
            DisplayString,
        espcCCName
            DisplayString,
        espcCCEventState
            DisplayString,
        espcCCThreshold
            Integer32,
        espcCCEventActions
            DisplayString,
        espcCCEventTrapnum
            Integer32,
        espcCCEventClass
            DisplayString,
        espcCCNormalActions
            DisplayString,
        espcCCNormalTrapnum
            Integer32,
        espcCCNormalClass
            DisplayString,
        espcCCAlarmAlias
            DisplayString,
        espcCCNormalAlias
            DisplayString,
        espcCCNormalThreshold
            Integer32,
        espcCCOverrideGlobalReminder
            DisplayString,
        espcCCReminderInterval
            Integer32
}

ESPointConfigHumid ::=
    SEQUENCE {
    espcHumidIndexES
            Integer32,
    espcHumidIndexPoint
            Integer32,
    espcHumidEnable
            DisplayString,
    espcHumidDeadband
            Integer32,
    espcHumidVHighHumid
            Integer32,
    espcHumidVHighActions
            DisplayString,
    espcHumidVHighTrapnum
            Integer32,
    espcHumidVHighClass
            DisplayString,
    espcHumidHighHumid
            Integer32,
    espcHumidHighActions
            DisplayString,
    espcHumidHighTrapnum
            Integer32,
    espcHumidHighClass
            DisplayString,
    espcHumidNormalActions
            DisplayString,
    espcHumidNormalTrapnum
            Integer32,
    espcHumidNormalClass
            DisplayString,
    espcHumidLowHumid
            Integer32,
    espcHumidLowActions
            DisplayString,
    espcHumidLowTrapnum
            Integer32,
    espcHumidLowClass
            DisplayString,
    espcHumidVLowHumid
            Integer32,
    espcHumidVLowActions
            DisplayString,
    espcHumidVLowTrapnum
            Integer32,
    espcHumidVLowClass
            DisplayString
}

ESPointConfigAnalogInput ::=
    SEQUENCE {
    espcAIIndexES
            Integer32,
    espcAIIndexPoint
            Integer32,
    espcAIEnable
            DisplayString,
    espcAIDeadband
            DisplayString,
    espcAIVhighValue
            DisplayString,
    espcAIVhighActions
            DisplayString,
    espcAIVhighTrapnum
            Integer32,
    espcAIVhighClass
            DisplayString,
    espcAIHighValue
            DisplayString,
    espcAIHighActions
            DisplayString,
    espcAIHighTrapnum
            Integer32,
    espcAIHighClass
            DisplayString,
    espcAINormalActions
            DisplayString,
    espcAINormalTrapnum
            Integer32,
    espcAINormalClass
            DisplayString,
    espcAILowValue
            DisplayString,
    espcAILowActions
            DisplayString,
    espcAILowTrapnum
            Integer32,
    espcAILowClass
            DisplayString,
    espcAIVlowValue
            DisplayString,
    espcAIVlowActions
            DisplayString,
    espcAIVlowTrapnum
            Integer32,
    espcAIVlowClass
            DisplayString,
    espcAIConvUnitName
            DisplayString,
    espcAIConvHighValue
            DisplayString,
    espcAIConvHighUnit
            DisplayString,
    espcAIConvLowValue
            DisplayString,
    espcAIConvLowUnit
            DisplayString,
    espcAIDisplayFormat
            DisplayString
}


ActionCallNumberConfig ::=
    SEQUENCE {
        actionCallNumberIndex
            Integer32,
        actionCallNumber
            DisplayString

}

ActionPagerConfig ::=
    SEQUENCE {
        actionPagerIndex
            Integer32,
        actionPagerType
            DisplayString,
        actionPagerNumber
            DisplayString,
        actionPagerID
            DisplayString,
        actionPagerMessage
            DisplayString,
        actionPagerPostCalloutDelay
            Integer32,
        actionPagerPostIDDelay
            Integer32
}

ESPointConfigOutput ::=
    SEQUENCE {
        espcOutputIndexES
            Integer32,
        espcOutputIndexPoint
            Integer32,
        espcOutputEnable
            DisplayString,
        espcOutputActiveState
            DisplayString,
        espcOutputType
            DisplayString,
        espcOutputAliasValue
            DisplayString,
        espcOutputAliasColor
            DisplayString,
        espcOutputActiveAlias
            DisplayString,
        espcOutputActiveColor
            DisplayString,
        espcOutputActiveActions
            DisplayString,
        espcOutputActiveTrapnum
            Integer32,
        espcOutputActiveClass
            DisplayString,
        espcOutputInactiveAlias
            DisplayString,
        espcOutputInactiveColor
            DisplayString,
        espcOutputInactiveActions
            DisplayString,
        espcOutputInactiveTrapnum
            Integer32,
        espcOutputInactiveClass
            DisplayString
}

CPEConfig ::=
    SEQUENCE {
        cpeIndex
            Integer32,
        cpeHost
            DisplayString,
        cpeName
            DisplayString,
        cpeDescription
            DisplayString,
        cpeKeepalive
            Integer32,
        cpeThreshold
            Integer32,
        cpeEventReminderInterval
            Integer32,
        cpeKeepaliveTicks
            Integer32,
        cpePingSize
            Integer32,
        cpeInfoReset
            Integer32,
        cpeInfoNumReq
            Integer32,
        cpeInfoNumGoodResp
            Integer32,
        cpeInfoNumBadResp
            Integer32,
        cpeInfoNumLostResp
            Integer32,
        cpeInfoPercentLoss
            DisplayString,
        cpeInfoPercentBad
            DisplayString
}

EventClassConfig ::=
    SEQUENCE {
        evClassNameIndex
            Integer32,
        evClassName
            DisplayString
}

RTSFileConfig ::=
    SEQUENCE {
        rtsFileIndex
            Integer32,
        rtsFileMode
            DisplayString,
        rtsFileShowAnswer
            DisplayString,
        rtsFileReqXON
            DisplayString,
        rtsFileTimeout
            Integer32,
        rtsFileEmptyClose
            DisplayString,
        rtsFilePushHost
            DisplayString,
        rtsFilePushPort
            Integer32,
        rtsFilePushRetryTimer
            Integer32
}


ModemCLIDLogConfig ::=
    SEQUENCE {
        modemCLIDLogIndex
            Integer32,
        modemCLIDLogNumber
            DisplayString
}

CRDBFileEnforceMinConfig ::=
    SEQUENCE {
        sysCRDBFileEnforceMinIndex
            Integer32,
        sysCRDBFileEnforceMin
            DisplayString
}

CRDBFileIDConfig ::=
    SEQUENCE {
        sysCRDBFileIDIndex
            Integer32,
        sysCRDBFileID
            DisplayString
}

NetworkTimeHostConfig ::=
    SEQUENCE {
        sysTimeNetHostIndex
            Integer32,
        sysTimeNetHost
            DisplayString
}

ActionHostConfig ::=
    SEQUENCE {
        actionHostIndex
            Integer32,
        actionHost
            DisplayString
}

ActionEmailConfig ::=
    SEQUENCE {
        actionEmailIndex
            Integer32,
        actionEmail
            DisplayString
}

FileEnforceMinimumConfig ::=
    SEQUENCE {
        sysCRDBFileEnforceMinIndex
            Integer32,
        sysCRDBFileEnforceMin
            DisplayString
}

SerialHandshakingLowEventConfig ::=
    SEQUENCE {
        evShskLowIndex
            Integer32,
        evShskLowEnable
            DisplayString,
        evShskLowActions
            DisplayString,
        evShskLowMessage
            DisplayString,
        evShskLowClass
            DisplayString,
        evShskLowTrapNum
            Integer32
}

SerialHandshakingHighEventConfig ::=
    SEQUENCE {
        evShskHighIndex
            Integer32,
        evShskHighEnable
            DisplayString,
        evShskHighActions
            DisplayString,
        evShskHighMessage
            DisplayString,
        evShskHighClass
            DisplayString,
        evShskHighTrapNum
            Integer32
}

NoDataEvent1PortEnableConfig ::=
    SEQUENCE {
        evNoData1PortEnableIndex
            Integer32,
        evNoData1PortEnable
            DisplayString
}

NoDataEvent2PortEnableConfig ::=
    SEQUENCE {
        evNoData2PortEnableIndex
            Integer32,
        evNoData2PortEnable
            DisplayString
}

DataEventMacroConfig ::=
    SEQUENCE {
        evdMacroIndex
            Integer32,
        evdMacroName
            DisplayString,
        evdMacroEquation
            DisplayString
}

DataEventFieldConfig ::=
    SEQUENCE {
        evdFieldIndex
            Integer32,
        evdFieldStart
            Integer32,
        evdFieldLength
            Integer32,
        evdFieldName
            DisplayString,
        evdFieldLine
            Integer32,
        evdFieldType
            DisplayString
}

DataEventConfig ::=
    SEQUENCE {
        evdIndex
            Integer32,
        evdEnable
            DisplayString,
        evdName
            DisplayString,
        evdEquation
            DisplayString,
        evdThreshold
            Integer32,
        evdAutoClear
            DisplayString,
        evdClearInterval
            DisplayString,
        evdClearTime
            DisplayString,
        evdActions
            DisplayString,
        evdClass
            DisplayString,
        evdTrapNum
            Integer32,
        evdMode
            DisplayString
}

SecUserConfig ::=
    SEQUENCE {
        secUserIndex
            Integer32,
        secUserEnable
            DisplayString,
        secUserConnectVia
            DisplayString,
        secUserLoginTo
            DisplayString,
        secUserAccessFile
            DisplayString,
        secUserPTEscapeTo
            DisplayString,
        secUserPPPType
            DisplayString,
        secUserRights
            DisplayString,
        secUserEventsReadAccess
            DisplayString,
        secUserAuditReadAccess
            DisplayString,
        secUserEventsWriteAccess
            DisplayString,
        secUserAuditWriteAccess
            DisplayString,
        secUserExpiration
            DisplayString,
        secUserCallbackNumber1
            DisplayString,
        secUserCallbackNumber2
            DisplayString,
        secUserCallbackNumber3
            DisplayString,
        secUserChallengeTelnetMode
            DisplayString,
        secUserChallengeModemMode
            DisplayString,
        secUserChallengeConsoleMode
            DisplayString,
        secUserChallengeTelnetSendTo
            DisplayString,
        secUserChallengeModemSendTo
            DisplayString,
        secUserChallengeExpiration
            Integer32
}




ModemCLIDNumberConfig ::=
    SEQUENCE {
        modemCLIDNumberIndex
            Integer32,
        modemCLIDNumber
            DisplayString
}


RouteTestConfig ::=
    SEQUENCE {
        routeTestAddressIndex
            Integer32,
        routeTestAddress
            DisplayString
}

IPRestrictionConfig ::=
    SEQUENCE {
        ipRestrictionIndex
            Integer32,
        ipRestrictionEnable
            DisplayString,
        ipRestrictionMask
            DisplayString
}

DNSConfig ::=
    SEQUENCE {
        dnsIndex
            Integer32,
        dnsAddress
            IpAddress
}

HostConfig ::=
    SEQUENCE {
        hostIndex
            Integer32,
        hostDeclaration
            DisplayString
}

FTPPushPushFileConfig ::=
    SEQUENCE {
        ftpPushPushFileIndex
            Integer32,
        ftpPushPushFile
            DisplayString
}

FTPPushRemoteFileConfig ::=
    SEQUENCE {
        ftpPushRemoteFileIndex
            Integer32,
        ftpPushRemoteFileName
            DisplayString
}

PortConfig ::=
    SEQUENCE {
        portConfigIndex
            Integer32,
        portConfigBaud
            Integer32,
        portConfigDataFormat
            Integer32,
        portConfigStripPtOutputLfs
            Integer32,
        portConfigStripPtInputLfs
            Integer32,
        portConfigMaskEnable
            Integer32,
        portConfigDAEnable
            Integer32,
        portConfigStoreAlarmsDPT
            DisplayString,
        portConfigRecordTimeout
            Integer32,
        portConfigDataType
            DisplayString,
        portConfigEtxToCRLF
            DisplayString,
        portConfigMLREnable
            DisplayString,
        portConfigMLRStartField1Pos
            Integer32,
        portConfigMLRStartField1Text
            DisplayString,
        portConfigMLRStartField2Pos
            Integer32,
        portConfigMLRStartField2Text
            DisplayString,
        portConfigMLRNumLinesBefore
            Integer32,
        portConfigMLREndDetection
            DisplayString,
        portConfigMLRLineCount
            Integer32,
        portConfigMLREndField1Pos
            Integer32,
        portConfigMLREndField1Text
            DisplayString,
        portConfigMLREndField2Pos
            Integer32,
        portConfigMLREndField2Text
            DisplayString,
        portConfigMLRUseComplexRules
            DisplayString,
        portConfigBufferPT
            DisplayString,
        portConfigId
            DisplayString,
        portConfigMode
            DisplayString,
        portConfigHsk
            DisplayString

}

ESPoint ::=
    SEQUENCE {
        esIndexES
            Integer32,
        esIndexPC
            Integer32,
        esIndexPoint
            Integer32,
        esPointName
            DisplayString,
        esPointInEventState
            Integer32,
        esPointValueInt
            Integer32,
        esPointValueStr
            DisplayString,
        esPointTimeLastChange
            DisplayString,
        esPointTimetickLastChange
            TimeTicks,
        esPointAliasValueStr
            DisplayString,
        esPointClassValueStr
            DisplayString
}

ESExist ::=
    SEQUENCE {
        esIndex
            Integer32,
        esName
            DisplayString,
        esID
            DisplayString,
        esNumberTempSensors
            Integer32,
        esTempReportingMode
            DisplayString,
        esNumberCCs
            Integer32,
        esCCReportingMode
            DisplayString,
        esNumberHumidSensors
            Integer32,
        esHumidReportingMode
            DisplayString,
        esNumberNoiseSensors
            Integer32,
        esNoiseReportingMode
            DisplayString,
        esNumberAirflowSensors
            Integer32,
        esAirflowReportingMode
            DisplayString,
        esNumberAnalog
            Integer32,
        esAnalogReportingMode
            DisplayString,
        esNumberOutputs
            Integer32,
        esOutputReportingMode
            DisplayString,
        esTempCombinedStatus
            DisplayString,
        esCCCombinedStatusBlock1
            DisplayString,
        esCCCombinedStatusBlock2
            DisplayString,
        esCCCombinedStatusBlock3
            DisplayString,
        esCCCombinedStatusBlock4
            DisplayString,
        esCCCombinedStatusBlock5
            DisplayString,
        esCCCombinedStatusBlock6
            DisplayString,
        esCCCombinedStatusBlock7
            DisplayString,
        esCCCombinedStatusBlock8
            DisplayString,
        esHumidCombinedStatus
            DisplayString,
        esAnalogCombinedStatusBlock1
            DisplayString,
        esAnalogCombinedStatusBlock2
            DisplayString,
        esAnalogCombinedStatusBlock3
            DisplayString,
        esAnalogCombinedStatusBlock4
            DisplayString,
        esAnalogCombinedStatusBlock5
            DisplayString,
        esAnalogCombinedStatusBlock6
            DisplayString,
        esOutputCombinedStatusBlock1
            DisplayString,
        esOutputCombinedStatusBlock2
            DisplayString
}

DEStatus ::=
    SEQUENCE {
        deStatusIndex
            Integer32,
        deStatusName
            DisplayString,
        deStatusCounter
            Integer32,
        deStatusThreshold
            Integer32
}


FSStatus ::=
    SEQUENCE {
        fsStatusIndex
            Integer32,
        fsStatusName
            DisplayString,
        fsStatusDeviceState
            DisplayString,
        fsStatusVolumeValueString
            DisplayString,
        fsStatusVolumePercentLevel
            DisplayString,
        fsStatusVolumeInEventState
            DisplayString,
        fsStatusCombined
            DisplayString
}

ACPMStatus ::=
    SEQUENCE {
        acpmsIndex
            Integer32,
        acpmsName
            DisplayString,
        acpmsAvgVoltageValueStr
            DisplayString,
        acpmsAvgVoltageMinStr
            DisplayString,
        acpmsAvgVoltageMaxStr
            DisplayString,
        acpmsAvgVoltageAvgStr
            DisplayString,
        acpmsAvgVoltageInEventState
            DisplayString,
        acpmsVoltagePhaseAValueStr
            DisplayString,
        acpmsVoltagePhaseBValueStr
            DisplayString,
        acpmsVoltagePhaseCValueStr
            DisplayString,
        acpmsAvgCurrentValueStr
            DisplayString,
        acpmsAvgCurrentMinStr
            DisplayString,
        acpmsAvgCurrentMaxStr
            DisplayString,
        acpmsAvgCurrentAvgStr
            DisplayString,
        acpmsAvgCurrentInEventState
            DisplayString,
        acpmsCurrentPhaseAValueStr
            DisplayString,
        acpmsCurrentPhaseBValueStr
            DisplayString,
        acpmsCurrentPhaseCValueStr
            DisplayString,
        acpmsAvgFreqValueStr
            DisplayString,
        acpmsAvgFreqMinStr
            DisplayString,
        acpmsAvgFreqMaxStr
            DisplayString,
        acpmsAvgFreqAvgStr
            DisplayString,
        acpmsAvgFreqInEventState
            DisplayString,
        acpmsTRPValueStr
            DisplayString,
        acpmsTRPMinStr
            DisplayString,
        acpmsTRPMaxStr
            DisplayString,
        acpmsTRPAvgStr
            DisplayString,
        acpmsTRPInEventState
            DisplayString,
        acpmsRPPhaseAValueStr
            DisplayString,
        acpmsRPPhaseBValueStr
            DisplayString,
        acpmsRPPhaseCValueStr
            DisplayString,
        acpmsCombined
            DisplayString,
        acpmsTPFValueStr
            DisplayString,
        acpmsTPFMinStr
            DisplayString,
        acpmsTPFMaxStr
            DisplayString,
        acpmsTPFAvgStr
            DisplayString,
        acpmsTPFInEventState
            DisplayString,
        acpmsPFPhaseAValueStr
            DisplayString,
        acpmsPFPhaseBValueStr
            DisplayString,
        acpmsPFPhaseCValueStr
            DisplayString,
        acpmsTRcPValueStr
            DisplayString,
        acpmsTRcPMinStr
            DisplayString,
        acpmsTRcPMaxStr
            DisplayString,
        acpmsTRcPAvgStr
            DisplayString,
        acpmsRcPPhaseAValueStr
            DisplayString,
        acpmsRcPPhaseBValueStr
            DisplayString,
        acpmsRcPPhaseCValueStr
            DisplayString,
        acpmsTAPValueStr
            DisplayString,
        acpmsTAPMinStr
            DisplayString,
        acpmsTAPMaxStr
            DisplayString,
        acpmsTAPAvgStr
            DisplayString,
        acpmsAPPhaseAValueStr
            DisplayString,
        acpmsAPPhaseBValueStr
            DisplayString,
        acpmsAPPhaseCValueStr
            DisplayString,
        acpmsTotalEnergyWh
            Integer32,
        acpmsTotalEnergyVAR
            Integer32,
        acpmsTotalEnergyVA
            Integer32
}

FuelSensorConfigGeneral ::=
    SEQUENCE {
        fsGenIndex
            Integer32,
        fsGenName
            DisplayString,
        fsGenSensorType
            DisplayString,
        fsGenDistanceUnit
            DisplayString,
        fsGenRawValueTop
            DisplayString,
        fsGenTopOffset
            DisplayString,
        fsGenRawValueBottom
            DisplayString,
        fsGenBottomOffset
            DisplayString,
        fsGenInputES
            Integer32,
        fsGenInputPoint
            Integer32,
        fsGenFilterAveraging
            Integer32,
        fsGenSysrepEnable
            DisplayString,
        fsGenSysrepThreshold
            DisplayString,
        fsGenSysrepLimit
            Integer32,
        fsGenSysrepPackage
            Integer32,
        fsGenSysrepType
            DisplayString,
        fsGenEnable
            DisplayString,
        fsGenFuelType
            DisplayString
}

FuelSensorConfigTank ::=
    SEQUENCE {
        fsTankIndex
            Integer32,
        fsTankHeight
            DisplayString,
        fsTankDimA
            DisplayString,
        fsTankDimB
            DisplayString,
        fsTankVolume
            DisplayString,
        fsTankVolumeUnit
            DisplayString,
        fsTankShape
            DisplayString
}

FuelSensorConfigCustomTankStrapping ::=
    SEQUENCE {
        fsCustomTankIndexFS
            Integer32,
        fsCustomTankIndexDatum
            Integer32,
        fsCustomTankHeight
            DisplayString,
        fsCustomTankVolume
            DisplayString
}

FuelSensorConfigVolume ::=
    SEQUENCE {
        fsVolumeIndex
            Integer32,
        fsVolumeEnable
            DisplayString,
        fsVolumeDeadband
            DisplayString,
        fsVolumeVHighValue
            DisplayString,
        fsVolumeVHighActions
            DisplayString,
        fsVolumeVHighTrapNum
            Integer32,
        fsVolumeVHighClass
            DisplayString,
        fsVolumeHighValue
            DisplayString,
        fsVolumeHighActions
            DisplayString,
        fsVolumeHighTrapNum
            Integer32,
        fsVolumeHighClass
            DisplayString,
        fsVolumeNormalActions
            DisplayString,
        fsVolumeNormalTrapNum
            Integer32,
        fsVolumeNormalClass
            DisplayString,
        fsVolumeLowValue
            DisplayString,
        fsVolumeLowActions
            DisplayString,
        fsVolumeLowTrapNum
            Integer32,
        fsVolumeLowClass
            DisplayString,
        fsVolumeVLowValue
            DisplayString,
        fsVolumeVLowActions
            DisplayString,
        fsVolumeVLowTrapNum
            Integer32,
        fsVolumeVLowClass
            DisplayString
}

FuelSensorConfigDisconnect ::=
    SEQUENCE {
        fsDiscIndex
            Integer32,
        fsDiscEnable
            DisplayString,
        fsDiscHighValue
            DisplayString,
        fsDiscLowValue
            DisplayString,
        fsDiscActions
            DisplayString,
        fsDiscTrapNum
            Integer32,
        fsDiscClass
            DisplayString,
        fsDiscNormalActions
            DisplayString,
        fsDiscNormalTrapNum
            Integer32,
        fsDiscNormalClass
            DisplayString
}

ACPMConfigGeneral ::=
    SEQUENCE {
        acpmGenIndex
            Integer32,
        acpmGenDevice
            DisplayString,
        acpmGenName
            DisplayString,
        acpmGenAddress
            Integer32,
        acpmGenPtRatio
            Integer32,
        acpmGenCtRatio
            Integer32,
        acpmGenPowerType
            DisplayString,
        acpmGenSysrepPackage
            Integer32,
        acpmGenSysrepType
            DisplayString,
        acpmGenEnable
            DisplayString
}

ACPMConfigAvgVoltage ::=
    SEQUENCE {
        acpmAvgVoltageIndex
            Integer32,
        acpmAvgVoltageEnable
            DisplayString,
        acpmAvgVoltageDeadband
            DisplayString,
        acpmAvgVoltageVHighValue
            DisplayString,
        acpmAvgVoltageVHighActions
            DisplayString,
        acpmAvgVoltageVHighTrapNum
            Integer32,
        acpmAvgVoltageVHighClass
            DisplayString,
        acpmAvgVoltageHighValue
            DisplayString,
        acpmAvgVoltageHighActions
            DisplayString,
        acpmAvgVoltageHighTrapNum
            Integer32,
        acpmAvgVoltageHighClass
            DisplayString,
        acpmAvgVoltageNormalActions
            DisplayString,
        acpmAvgVoltageNormalTrapNum
            Integer32,
        acpmAvgVoltageNormalClass
            DisplayString,
        acpmAvgVoltageLowValue
            DisplayString,
        acpmAvgVoltageLowActions
            DisplayString,
        acpmAvgVoltageLowTrapNum
            Integer32,
        acpmAvgVoltageLowClass
            DisplayString,
        acpmAvgVoltageVLowValue
            DisplayString,
        acpmAvgVoltageVLowActions
            DisplayString,
        acpmAvgVoltageVLowTrapNum
            Integer32,
        acpmAvgVoltageVLowClass
            DisplayString,
        acpmAvgVoltageSysrepEnable
            DisplayString,
        acpmAvgVoltageSysrepThreshold
            DisplayString,
        acpmAvgVoltageSysrepLimit
            Integer32
}

ACPMConfigAvgCurrent ::=
    SEQUENCE {
        acpmAvgCurrentIndex
            Integer32,
        acpmAvgCurrentEnable
            DisplayString,
        acpmAvgCurrentDeadband
            DisplayString,
        acpmAvgCurrentVHighValue
            DisplayString,
        acpmAvgCurrentVHighActions
            DisplayString,
        acpmAvgCurrentVHighTrapNum
            Integer32,
        acpmAvgCurrentVHighClass
            DisplayString,
        acpmAvgCurrentHighValue
            DisplayString,
        acpmAvgCurrentHighActions
            DisplayString,
        acpmAvgCurrentHighTrapNum
            Integer32,
        acpmAvgCurrentHighClass
            DisplayString,
        acpmAvgCurrentNormalActions
            DisplayString,
        acpmAvgCurrentNormalTrapNum
            Integer32,
        acpmAvgCurrentNormalClass
            DisplayString,
        acpmAvgCurrentLowValue
            DisplayString,
        acpmAvgCurrentLowActions
            DisplayString,
        acpmAvgCurrentLowTrapNum
            Integer32,
        acpmAvgCurrentLowClass
            DisplayString,
        acpmAvgCurrentVLowValue
            DisplayString,
        acpmAvgCurrentVLowActions
            DisplayString,
        acpmAvgCurrentVLowTrapNum
            Integer32,
        acpmAvgCurrentVLowClass
            DisplayString,
        acpmAvgCurrentSysrepEnable
            DisplayString,
        acpmAvgCurrentSysrepThreshold
            DisplayString,
        acpmAvgCurrentSysrepLimit
            Integer32
}

ACPMConfigFreq ::=
    SEQUENCE {
        acpmFreqIndex
            Integer32,
        acpmFreqEnable
            DisplayString,
        acpmFreqDeadband
            DisplayString,
        acpmFreqVHighValue
            DisplayString,
        acpmFreqVHighActions
            DisplayString,
        acpmFreqVHighTrapNum
            Integer32,
        acpmFreqVHighClass
            DisplayString,
        acpmFreqHighValue
            DisplayString,
        acpmFreqHighActions
            DisplayString,
        acpmFreqHighTrapNum
            Integer32,
        acpmFreqHighClass
            DisplayString,
        acpmFreqNormalActions
            DisplayString,
        acpmFreqNormalTrapNum
            Integer32,
        acpmFreqNormalClass
            DisplayString,
        acpmFreqLowValue
            DisplayString,
        acpmFreqLowActions
            DisplayString,
        acpmFreqLowTrapNum
            Integer32,
        acpmFreqLowClass
            DisplayString,
        acpmFreqVLowValue
            DisplayString,
        acpmFreqVLowActions
            DisplayString,
        acpmFreqVLowTrapNum
            Integer32,
        acpmFreqVLowClass
            DisplayString,
        acpmFreqSysrepEnable
            DisplayString,
        acpmFreqSysrepThreshold
            DisplayString,
        acpmFreqSysrepLimit
            Integer32
}

ACPMConfigTRP ::=
    SEQUENCE {
        acpmTRPIndex
            Integer32,
        acpmTRPEnable
            DisplayString,
        acpmTRPDeadband
            DisplayString,
        acpmTRPVHighValue
            DisplayString,
        acpmTRPVHighActions
            DisplayString,
        acpmTRPVHighTrapNum
            Integer32,
        acpmTRPVHighClass
            DisplayString,
        acpmTRPHighValue
            DisplayString,
        acpmTRPHighActions
            DisplayString,
        acpmTRPHighTrapNum
            Integer32,
        acpmTRPHighClass
            DisplayString,
        acpmTRPNormalActions
            DisplayString,
        acpmTRPNormalTrapNum
            Integer32,
        acpmTRPNormalClass
            DisplayString,
        acpmTRPLowValue
            DisplayString,
        acpmTRPLowActions
            DisplayString,
        acpmTRPLowTrapNum
            Integer32,
        acpmTRPLowClass
            DisplayString,
        acpmTRPVLowValue
            DisplayString,
        acpmTRPVLowActions
            DisplayString,
        acpmTRPVLowTrapNum
            Integer32,
        acpmTRPVLowClass
            DisplayString,
        acpmTRPSysrepEnable
            DisplayString,
        acpmTRPSysrepThreshold
            DisplayString,
        acpmTRPSysrepLimit
            Integer32
}

ACPMConfigDisconnect ::=
    SEQUENCE {
        acpmDisconnectIndex
            Integer32,
        acpmDisconnectEnable
            DisplayString,
        acpmDisconnectActions
            DisplayString,
        acpmDisconnectTrapNum
            Integer32,
        acpmDisconnectClass
            DisplayString,
        acpmDisconnectNormalActions
            DisplayString,
        acpmDisconnectNormalTrapNum
            Integer32,
        acpmDisconnectNormalClass
            DisplayString
}

ACPMConfigTotalPowerFactor ::=
    SEQUENCE {
        acpmTPFIndex
            Integer32,
        acpmTPFEnable
            DisplayString,
        acpmTPFDeadband
            DisplayString,
        acpmTPFNormalActions
            DisplayString,
        acpmTPFNormalTrapNum
            Integer32,
        acpmTPFNormalClass
            DisplayString,
        acpmTPFLowValue
            DisplayString,
        acpmTPFLowActions
            DisplayString,
        acpmTPFLowTrapNum
            Integer32,
        acpmTPFLowClass
            DisplayString,
        acpmTPFVLowValue
            DisplayString,
        acpmTPFVLowActions
            DisplayString,
        acpmTPFVLowTrapNum
            Integer32,
        acpmTPFVLowClass
            DisplayString,
        acpmTPFSysrepEnable
            DisplayString,
        acpmTPFSysrepThreshold
            DisplayString,
        acpmTPFSysrepLimit
            Integer32
}

EthernetExpansionCardConfig ::=
    SEQUENCE {
        ethExpanCardIndex
            Integer32,
        ethExpanCardMAC
            DisplayString
}

--***************************************************************************************
--TRAP DEFINITIONS
--***************************************************************************************

s530StockContactClosureTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock contact closure trap is issued when a contact closure
        event happens."
    ::= { s530 110 }

s530StockTempTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock temperature trap is issued when a temperature event
        happens."
    ::= { s530 120 }

s530StockHumidityTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock humidity trap is issued when a humidity event
        happens."
    ::= { s530 130 }

s530StockAnalogTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock analog trap is issued when an analog sensor event
        happens."
    ::= { s530 140 }

s530StockOutputTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock output trap is issued when an output event
        happens.  That is, depending on the configuration,
        when an output changes state for any reason, this
        trap will be sent."
    ::= { s530 150 }

s530StockPDCurrentTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock analog trap is issued when a power distribution
        current event happens."
    ::= { s530 180 }

s530StockPDVoltageTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock analog trap is issued when a power distribution
        voltage event happens."
    ::= { s530 190 }

s530StockPDFuseTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock analog trap is issued when a power distribution
        fuse event happens."
    ::= { s530 210 }

s530StockDbasePfullTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "The stock database percent full trap is issued
        when the database size surpasses the threshold."
    ::= { s530 501 }

s530StockDataAlarmTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "The stock data alarm trap is issued when a
        data alarm happens."
    ::= { s530 503 }

s530StockNoDataAlarmTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "The stock no-data alarm trap is issued when
        the unit has received no data for a configured
        interval within a configured schedule."
    ::= { s530 505 }

s530StockSchedTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "The stock scheduled alarm trap is issued
        when the current time reaches the scheduled
        alarm time."
    ::= { s530 506 }

s530StockImmediateTrap NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "The stock immediate alarm trap is issued
        when the user enters the DOTRAP command."
    ::= { s530 507 }

s530StockCTSTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock CTS trap is issued when an RS-232 CTS event
        happens."
    ::= { s530 510 }

s530CPEDownTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock CPE Down trap is issued when a CPE Down event
        happens."
    ::= { s530 511 }

s530FuelSensorDisconnectTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock Fuel Sensor Disconnect trap is issued when a Fuel
        Sensor Disconnect or Connect (Return to Normal) event happens."
    ::= { s530 515 }

s530FuelSensorVolumeTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock Fuel Sensor Volume trap is issued when a Fuel
        Sensor Volume event happens"
    ::= { s530 519 }

s530ACPowerMonitorAvgVoltageTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor average voltage trap is issued when an
        AC power monitor average voltage event happens."
    ::= { s530 520 }

s530ACPowerMonitorAvgCurrentTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor average current trap is issued when an
        AC power monitor average current event happens."
    ::= { s530 521 }

s530ACPowerMonitorFrequencyTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor frequency trap is issued when an
        AC power monitor frequency event happens."
    ::= { s530 522 }

s530ACPowerMonitorTRPTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor TRP (total real power) trap is issued
        when an AC power monitor TRP event happens."
    ::= { s530 523 }

s530ACPowerMonitorDisconnectTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor disconnect trap is issued
        when an AC power monitor disconnect event happens."
    ::= { s530 524 }

s530ACTotalPowerFactorTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock AC power monitor total power factor trap is issued
        when an AC power monitor total power factor event happens."
    ::= { s530 540 }

s530ResetTrap  NOTIFICATION-TYPE
    OBJECTS { siteName, stockTrapString, trapTypeString }
    STATUS current
    DESCRIPTION
        "A stock reset trap is issued when a reset event happens."
    ::= { s530 543 }

s530UserTrap1000 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1000."
    ::= { s530 1000 }

s530UserTrap1001 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1001."
    ::= { s530 1001 }

s530UserTrap1002 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1002."
    ::= { s530 1002 }

s530UserTrap1003 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1003."
    ::= { s530 1003 }

s530UserTrap1004 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1004."
    ::= { s530 1004 }

s530UserTrap1005 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1005."
    ::= { s530 1005 }

s530UserTrap1006 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1006."
    ::= { s530 1006 }

s530UserTrap1007 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1007."
    ::= { s530 1007 }

s530UserTrap1008 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1008."
    ::= { s530 1008 }

s530UserTrap1009 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1009."
    ::= { s530 1009 }

s530UserTrap1010 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1010."
    ::= { s530 1010 }

s530UserTrap1011 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1011."
    ::= { s530 1011 }

s530UserTrap1012 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1012."
    ::= { s530 1012 }

s530UserTrap1013 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1013."
    ::= { s530 1013 }

s530UserTrap1014 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1014."
    ::= { s530 1014 }

s530UserTrap1015 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1015."
    ::= { s530 1015 }

s530UserTrap1016 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1016."
    ::= { s530 1016 }

s530UserTrap1017 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1017."
    ::= { s530 1017 }

s530UserTrap1018 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1018."
    ::= { s530 1018 }

s530UserTrap1019 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1019."
    ::= { s530 1019 }

s530UserTrap1020 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1020."
    ::= { s530 1020 }

s530UserTrap1021 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1021."
    ::= { s530 1021 }

s530UserTrap1022 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1022."
    ::= { s530 1022 }

s530UserTrap1023 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1023."
    ::= { s530 1023 }

s530UserTrap1024 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1024."
    ::= { s530 1024 }

s530UserTrap1025 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1025."
    ::= { s530 1025 }

s530UserTrap1026 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1026."
    ::= { s530 1026 }

s530UserTrap1027 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1027."
    ::= { s530 1027 }

s530UserTrap1028 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1028."
    ::= { s530 1028 }

s530UserTrap1029 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1029."
    ::= { s530 1029 }

s530UserTrap1030 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1030."
    ::= { s530 1030 }

s530UserTrap1031 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1031."
    ::= { s530 1031 }

s530UserTrap1032 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1032."
    ::= { s530 1032 }

s530UserTrap1033 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1033."
    ::= { s530 1033 }

s530UserTrap1034 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1034."
    ::= { s530 1034 }

s530UserTrap1035 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1035."
    ::= { s530 1035 }

s530UserTrap1036 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1036."
    ::= { s530 1036 }

s530UserTrap1037 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1037."
    ::= { s530 1037 }

s530UserTrap1038 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1038."
    ::= { s530 1038 }

s530UserTrap1039 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1039."
    ::= { s530 1039 }

s530UserTrap1040 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1040."
    ::= { s530 1040 }

s530UserTrap1041 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1041."
    ::= { s530 1041 }

s530UserTrap1042 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1042."
    ::= { s530 1042 }

s530UserTrap1043 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1043."
    ::= { s530 1043 }

s530UserTrap1044 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1044."
    ::= { s530 1044 }

s530UserTrap1045 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1045."
    ::= { s530 1045 }

s530UserTrap1046 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1046."
    ::= { s530 1046 }

s530UserTrap1047 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1047."
    ::= { s530 1047 }

s530UserTrap1048 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1048."
    ::= { s530 1048 }

s530UserTrap1049 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1049."
    ::= { s530 1049 }

s530UserTrap1050 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1050."
    ::= { s530 1050 }

s530UserTrap1051 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1051."
    ::= { s530 1051 }

s530UserTrap1052 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1052."
    ::= { s530 1052 }

s530UserTrap1053 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1053."
    ::= { s530 1053 }

s530UserTrap1054 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1054."
    ::= { s530 1054 }

s530UserTrap1055 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1055."
    ::= { s530 1055 }

s530UserTrap1056 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1056."
    ::= { s530 1056 }

s530UserTrap1057 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1057."
    ::= { s530 1057 }

s530UserTrap1058 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1058."
    ::= { s530 1058 }

s530UserTrap1059 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1059."
    ::= { s530 1059 }

s530UserTrap1060 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1060."
    ::= { s530 1060 }

s530UserTrap1061 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1061."
    ::= { s530 1061 }

s530UserTrap1062 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1062."
    ::= { s530 1062 }

s530UserTrap1063 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1063."
    ::= { s530 1063 }

s530UserTrap1064 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1064."
    ::= { s530 1064 }

s530UserTrap1065 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1065."
    ::= { s530 1065 }

s530UserTrap1066 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1066."
    ::= { s530 1066 }

s530UserTrap1067 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1067."
    ::= { s530 1067 }

s530UserTrap1068 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1068."
    ::= { s530 1068 }

s530UserTrap1069 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1069."
    ::= { s530 1069 }

s530UserTrap1070 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1070."
    ::= { s530 1070 }

s530UserTrap1071 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1071."
    ::= { s530 1071 }

s530UserTrap1072 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1072."
    ::= { s530 1072 }

s530UserTrap1073 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1073."
    ::= { s530 1073 }

s530UserTrap1074 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1074."
    ::= { s530 1074 }

s530UserTrap1075 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1075."
    ::= { s530 1075 }

s530UserTrap1076 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1076."
    ::= { s530 1076 }

s530UserTrap1077 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1077."
    ::= { s530 1077 }

s530UserTrap1078 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1078."
    ::= { s530 1078 }

s530UserTrap1079 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1079."
    ::= { s530 1079 }

s530UserTrap1080 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1080."
    ::= { s530 1080 }

s530UserTrap1081 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1081."
    ::= { s530 1081 }

s530UserTrap1082 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1082."
    ::= { s530 1082 }

s530UserTrap1083 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1083."
    ::= { s530 1083 }

s530UserTrap1084 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1084."
    ::= { s530 1084 }

s530UserTrap1085 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1085."
    ::= { s530 1085 }

s530UserTrap1086 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1086."
    ::= { s530 1086 }

s530UserTrap1087 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1087."
    ::= { s530 1087 }

s530UserTrap1088 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1088."
    ::= { s530 1088 }

s530UserTrap1089 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1089."
    ::= { s530 1089 }

s530UserTrap1090 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1090."
    ::= { s530 1090 }

s530UserTrap1091 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1091."
    ::= { s530 1091 }

s530UserTrap1092 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1092."
    ::= { s530 1092 }

s530UserTrap1093 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1093."
    ::= { s530 1093 }

s530UserTrap1094 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1094."
    ::= { s530 1094 }

s530UserTrap1095 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1095."
    ::= { s530 1095 }

s530UserTrap1096 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1096."
    ::= { s530 1096 }

s530UserTrap1097 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1097."
    ::= { s530 1097 }

s530UserTrap1098 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1098."
    ::= { s530 1098 }

s530UserTrap1099 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1099."
    ::= { s530 1099 }

s530UserTrap1100 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1100."
    ::= { s530 1100 }

s530UserTrap1101 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1101."
    ::= { s530 1101 }

s530UserTrap1102 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1102."
    ::= { s530 1102 }

s530UserTrap1103 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1103."
    ::= { s530 1103 }

s530UserTrap1104 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1104."
    ::= { s530 1104 }

s530UserTrap1105 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1105."
    ::= { s530 1105 }

s530UserTrap1106 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1106."
    ::= { s530 1106 }

s530UserTrap1107 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1107."
    ::= { s530 1107 }

s530UserTrap1108 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1108."
    ::= { s530 1108 }

s530UserTrap1109 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1109."
    ::= { s530 1109 }

s530UserTrap1110 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1110."
    ::= { s530 1110 }

s530UserTrap1111 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1111."
    ::= { s530 1111 }

s530UserTrap1112 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1112."
    ::= { s530 1112 }

s530UserTrap1113 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1113."
    ::= { s530 1113 }

s530UserTrap1114 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1114."
    ::= { s530 1114 }

s530UserTrap1115 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1115."
    ::= { s530 1115 }

s530UserTrap1116 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1116."
    ::= { s530 1116 }

s530UserTrap1117 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1117."
    ::= { s530 1117 }

s530UserTrap1118 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1118."
    ::= { s530 1118 }

s530UserTrap1119 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1119."
    ::= { s530 1119 }

s530UserTrap1120 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1120."
    ::= { s530 1120 }

s530UserTrap1121 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1121."
    ::= { s530 1121 }

s530UserTrap1122 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1122."
    ::= { s530 1122 }

s530UserTrap1123 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1123."
    ::= { s530 1123 }

s530UserTrap1124 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1124."
    ::= { s530 1124 }

s530UserTrap1125 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1125."
    ::= { s530 1125 }

s530UserTrap1126 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1126."
    ::= { s530 1126 }

s530UserTrap1127 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1127."
    ::= { s530 1127 }

s530UserTrap1128 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1128."
    ::= { s530 1128 }

s530UserTrap1129 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1129."
    ::= { s530 1129 }

s530UserTrap1130 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1130."
    ::= { s530 1130 }

s530UserTrap1131 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1131."
    ::= { s530 1131 }

s530UserTrap1132 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1132."
    ::= { s530 1132 }

s530UserTrap1133 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1133."
    ::= { s530 1133 }

s530UserTrap1134 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1134."
    ::= { s530 1134 }

s530UserTrap1135 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1135."
    ::= { s530 1135 }

s530UserTrap1136 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1136."
    ::= { s530 1136 }

s530UserTrap1137 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1137."
    ::= { s530 1137 }

s530UserTrap1138 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1138."
    ::= { s530 1138 }

s530UserTrap1139 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1139."
    ::= { s530 1139 }

s530UserTrap1140 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1140."
    ::= { s530 1140 }

s530UserTrap1141 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1141."
    ::= { s530 1141 }

s530UserTrap1142 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1142."
    ::= { s530 1142 }

s530UserTrap1143 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1143."
    ::= { s530 1143 }

s530UserTrap1144 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1144."
    ::= { s530 1144 }

s530UserTrap1145 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1145."
    ::= { s530 1145 }

s530UserTrap1146 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1146."
    ::= { s530 1146 }

s530UserTrap1147 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1147."
    ::= { s530 1147 }

s530UserTrap1148 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1148."
    ::= { s530 1148 }

s530UserTrap1149 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1149."
    ::= { s530 1149 }

s530UserTrap1150 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1150."
    ::= { s530 1150 }

s530UserTrap1151 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1151."
    ::= { s530 1151 }

s530UserTrap1152 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1152."
    ::= { s530 1152 }

s530UserTrap1153 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1153."
    ::= { s530 1153 }

s530UserTrap1154 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1154."
    ::= { s530 1154 }

s530UserTrap1155 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1155."
    ::= { s530 1155 }

s530UserTrap1156 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1156."
    ::= { s530 1156 }

s530UserTrap1157 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1157."
    ::= { s530 1157 }

s530UserTrap1158 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1158."
    ::= { s530 1158 }

s530UserTrap1159 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1159."
    ::= { s530 1159 }

s530UserTrap1160 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1160."
    ::= { s530 1160 }

s530UserTrap1161 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1161."
    ::= { s530 1161 }

s530UserTrap1162 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1162."
    ::= { s530 1162 }

s530UserTrap1163 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1163."
    ::= { s530 1163 }

s530UserTrap1164 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1164."
    ::= { s530 1164 }

s530UserTrap1165 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1165."
    ::= { s530 1165 }

s530UserTrap1166 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1166."
    ::= { s530 1166 }

s530UserTrap1167 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1167."
    ::= { s530 1167 }

s530UserTrap1168 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1168."
    ::= { s530 1168 }

s530UserTrap1169 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1169."
    ::= { s530 1169 }

s530UserTrap1170 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1170."
    ::= { s530 1170 }

s530UserTrap1171 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1171."
    ::= { s530 1171 }

s530UserTrap1172 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1172."
    ::= { s530 1172 }

s530UserTrap1173 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1173."
    ::= { s530 1173 }

s530UserTrap1174 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1174."
    ::= { s530 1174 }

s530UserTrap1175 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1175."
    ::= { s530 1175 }

s530UserTrap1176 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1176."
    ::= { s530 1176 }

s530UserTrap1177 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1177."
    ::= { s530 1177 }

s530UserTrap1178 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1178."
    ::= { s530 1178 }

s530UserTrap1179 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1179."
    ::= { s530 1179 }

s530UserTrap1180 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1180."
    ::= { s530 1180 }

s530UserTrap1181 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1181."
    ::= { s530 1181 }

s530UserTrap1182 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1182."
    ::= { s530 1182 }

s530UserTrap1183 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1183."
    ::= { s530 1183 }

s530UserTrap1184 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1184."
    ::= { s530 1184 }

s530UserTrap1185 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1185."
    ::= { s530 1185 }

s530UserTrap1186 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1186."
    ::= { s530 1186 }

s530UserTrap1187 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1187."
    ::= { s530 1187 }

s530UserTrap1188 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1188."
    ::= { s530 1188 }

s530UserTrap1189 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1189."
    ::= { s530 1189 }

s530UserTrap1190 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1190."
    ::= { s530 1190 }

s530UserTrap1191 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1191."
    ::= { s530 1191 }

s530UserTrap1192 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1192."
    ::= { s530 1192 }

s530UserTrap1193 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1193."
    ::= { s530 1193 }

s530UserTrap1194 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1194."
    ::= { s530 1194 }

s530UserTrap1195 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1195."
    ::= { s530 1195 }

s530UserTrap1196 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1196."
    ::= { s530 1196 }

s530UserTrap1197 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1197."
    ::= { s530 1197 }

s530UserTrap1198 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1198."
    ::= { s530 1198 }

s530UserTrap1199 NOTIFICATION-TYPE
    OBJECTS { siteName, esIndex, esName, trapEventTypeNumber,
        trapEventTypeName, esIndexPoint, esPointName, esID,
        clock, trapIncludedValue, trapIncludedString,
        trapEventClassNumber, trapEventClassName }
    STATUS current
    DESCRIPTION
        "This user-defined trap is issued when an event happens that causes a
        trap with specific trap type 1199."
    ::= { s530 1199 }

END
