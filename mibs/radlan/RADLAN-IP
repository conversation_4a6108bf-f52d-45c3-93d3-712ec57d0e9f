RADLAN-IP DEFINITIONS ::= BEGIN

-- Title:      RADLAN IP Private Extension
-- Version:    **********
-- Date:       29 Sep 2005

IMPORTS
    rnd                                                     FROM RADLAN-MIB
    ipAddrEntry                                             FROM IP-MIB
    rip2IfConfEntry                                         FROM RFC1389-MIB
    ipCidrRouteEntry,ipCidrRouteDest,
    ipCidrRouteMask, ipCidrRouteTos, ipCidrRouteNextHop     FROM IP-FORWARD-MIB
    DisplayString                                           FROM SNMPv2-TC-v1
    Unsigned32, Integer32, Counter32, Ip<PERSON>ddress,
    MODULE-IDENTITY, OBJECT-TYPE                            FROM SNMPv2-SM<PERSON>
    RowStatus, TruthValue, TEXTUAL-CONVENTION               FROM SNMPv2-TC;


ipSpec MODULE-IDENTITY
       LAST-UPDATED "200509290000Z"
       ORGANIZATION "Radlan Computer Communications Ltd."
       CONTACT-INFO
            "radlan.com"
       DESCRIPTION
            "The private MIB module definition for IP MIB."
       REVISION "200509290000Z"
       DESCRIPTION
            "Initial version of this MIB."
       ::= { rnd 26 }


rsIpAddrTable OBJECT-TYPE
    SYNTAX      SEQUENCE  OF RsIpAddrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "This table is parralel to MIB II IpAddrTable, and is used to
        add/delete entries to/from that table. In addition it contains
        private objects."
    ::=  { ipSpec 1 }

rsIpAddrEntry   OBJECT-TYPE
    SYNTAX      RsIpAddrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The addressing information for one of this
            entity's IP addresses."
    INDEX  {rsIpAdEntAddr}
    ::=  { rsIpAddrTable 1 }

RsIpAddrEntry ::= SEQUENCE {
    rsIpAdEntAddr                IpAddress,
    rsIpAdEntIfIndex             INTEGER,
    rsIpAdEntNetMask             IpAddress,
    rsIpAdEntForwardIpBroadcast  INTEGER,
    rsIpAdEntBackupAddr          IpAddress, -- obsolete
    rsIpAdEntStatus              INTEGER,
    rsIpAdEntBcastAddr           INTEGER,
    rsIpAdEntArpServer           INTEGER,
    rsIpAdEntName                DisplayString,
    rsIpAdEntOwner               INTEGER,
    rsIpAdEntAdminStatus         INTEGER,
    rsIpAdEntOperStatus                 INTEGER
}

rsIpAdEntAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The IP address to which this entry's addressing
            information pertains."
    ::= { rsIpAddrEntry 1 }

rsIpAdEntIfIndex OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The index value which uniquely identifies the
            interface to which this entry is applicable.  The
            interface identified by a particular value of this
            index is the same interface as identified by the
            same value of ifIndex."
    ::= { rsIpAddrEntry 2 }

rsIpAdEntNetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The subnet mask associated with the IP address of
            this entry.  The value of the mask is an IP
            address with all the network bits set to 1 and all
            the hosts bits set to 0."
    ::= { rsIpAddrEntry 3 }

rsIpAdEntForwardIpBroadcast OBJECT-TYPE
    SYNTAX INTEGER {
       enable(1),
       disable(2)
       }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " This variable controls forwarding of IP (sub)net-directed
          broadcasts destined for an attached sub(net). "
    DEFVAL  { enable }
    ::=   { rsIpAddrEntry 4 }

rsIpAdEntBackupAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "In case there are two IP routers in the domain,
             the address of the second IP router."
    ::= { rsIpAddrEntry 5 }

rsIpAdEntStatus OBJECT-TYPE
    SYNTAX INTEGER{
       valid(1),
       invalid(2)
       }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " The validity of this entry. Invalid indicates that this entry is
          invalid in IpAddrTable (MIB II)."
    DEFVAL  { valid }
    ::=   { rsIpAddrEntry 6 }

rsIpAdEntBcastAddr OBJECT-TYPE
    SYNTAX      INTEGER (0..1)
    MAX-ACCESS read-write
    STATUS      current
    DESCRIPTION
        " Indicates how the host part of ip subnet broadcast messages will be
          filled:
           0 - host part will be filled by 0
           1 - host part will be filled by 1."
    ::=   { rsIpAddrEntry 7 }

rsIpAdEntArpServer OBJECT-TYPE
    SYNTAX INTEGER{
       enable(1),
       disable(2)
       }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether the router will reply to incoming ARP requests on
         this interface, providing the physical address corresponding to this
         IP interface."
    DEFVAL  { disable }
    ::=   { rsIpAddrEntry 8 }

    rsIpAdEntName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE (0..30))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
           "The IP Interface name"
    ::=   { rsIpAddrEntry 9 }

rsIpAdEntOwner OBJECT-TYPE
        SYNTAX  INTEGER{
               static(1),
               dhcp(2),
               internal(3),
               default(4)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
           "The IP Interface owner. Static if interface defined by user, dhcp
            if received by boot protocol like DHCP and internal
            for internal usage."
    DEFVAL  { static }
    ::=   { rsIpAddrEntry 10 }

rsIpAdEntAdminStatus     OBJECT-TYPE
        SYNTAX  INTEGER{
               up(1),
               down(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
           "The IP Interface admin status."
    DEFVAL  { up }
    ::=   { rsIpAddrEntry 11 }

rsIpAdEntOperStatus   OBJECT-TYPE
            SYNTAX INTEGER {
                       active(1),
                       inactive(2)
            }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "If active the interface can be used to send and receive frames."
            ::= { rsIpAddrEntry 12 }


icmpSpec               OBJECT IDENTIFIER ::= { ipSpec 2 }

rsIcmpGenErrMsgEnable   OBJECT-TYPE
    SYNTAX INTEGER {
       enable(1),
       disable(2)
   }
    MAX-ACCESS  read-write
    STATUS  current
        DESCRIPTION
           "This variable controlls the ability to generate ICMP error messages"
    DEFVAL  { enable }
    ::= { icmpSpec 1 }

rsIcmpRdTable OBJECT-TYPE
    SYNTAX      SEQUENCE  OF RsIcmpRdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "This table contains ICMP Router Discovery parameters
        configurated per IP interface."
    ::=  {icmpSpec 2}

rsIcmpRdEntry   OBJECT-TYPE
    SYNTAX      RsIcmpRdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
           "The ICMP parameters configurated for IP interface."
    INDEX  {rsIcmpRdIpAddr}
    ::=  {rsIcmpRdTable 1}

RsIcmpRdEntry ::= SEQUENCE {
    rsIcmpRdIpAddr             IpAddress,
    rsIcmpRdIpAdvertAddr       IpAddress,
    rsIcmpRdMaxAdvertInterval  INTEGER,
    rsIcmpRdMinAdvertInterval  INTEGER,
    rsIcmpRdAdvertLifetime     INTEGER,
    rsIcmpRdAdvertise          INTEGER,
    rsIcmpRdPreferenceLevel    INTEGER,
    rsIcmpRdEntStatus          INTEGER
    }

rsIcmpRdIpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The IP address to which this entry's information pertains."
    ::= {rsIcmpRdEntry 1}

rsIcmpRdIpAdvertAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            " The IP destination address to be used for multicast
              Router Advertisements sent from the interface. The
              only permissible values are the all-systems multicast
              address, *********, or the limited-broadcast address,
              ***************."
    DEFVAL  {'E0000001'H}
    ::= {rsIcmpRdEntry 2}

rsIcmpRdMaxAdvertInterval OBJECT-TYPE
    SYNTAX      INTEGER (4..1800)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The maximum time allowed between sending multicast
             Router Advertisements from the interface, in seconds.
             Must be no less than 4 seconds and no greater than 1800
             seconds."
    DEFVAL  {600}
    ::= {rsIcmpRdEntry 3}

rsIcmpRdMinAdvertInterval OBJECT-TYPE
    SYNTAX      INTEGER (3..1800)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The minimum time allowed between sending unsolicited
             multicast Router Advertisements from the interface, in
             seconds.  Must be no less than 3 seconds and no greater
             than rsIcmpRdMaxAdvertInterval.
             Default: 0.75 * rsIcmpRdMaxAdvertInterval."
    ::= {rsIcmpRdEntry 4}

rsIcmpRdAdvertLifetime OBJECT-TYPE
    SYNTAX      INTEGER (4..9000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "The maximum length of time that the advertised addresses
             are to be considered as valid. Must be no less than
             rsIcmpRdMaxAdvertInterval and no greater than 9000 seconds.
             Default: 3 * rsIcmpRdMaxAdvertInterval."
    ::= {rsIcmpRdEntry 5}

rsIcmpRdAdvertise OBJECT-TYPE
    SYNTAX  INTEGER {
       enable  (1),
       disable (2)
       }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "A flag indicating whether or not the address is to be
              advertised."
    DEFVAL  {enable}
    ::= {rsIcmpRdEntry 6}

rsIcmpRdPreferenceLevel OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "The preferability of the address as a default router
             address, relative to other router addresses on the same
             subnet."
    DEFVAL  {0}
    ::= {rsIcmpRdEntry 7}

rsIcmpRdEntStatus  OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Setting of any value to this object set values of
             all fields to the default values."
    ::=   {rsIcmpRdEntry 8}


rip2Spec              OBJECT IDENTIFIER ::= { ipSpec  3 }
-- see rlIpRouters

arpSpec                OBJECT IDENTIFIER ::= { ipSpec 4 }

rsArpDeleteTable OBJECT-TYPE
    SYNTAX  INTEGER {
       noAction(0),  -- for get only
       deleteArpTab(1),
       deleteIpArpDynamicEntries(2),
       deleteIpArpStaticEntries(3),
       deleteIpArpDelDynamicRefreshStatic(4)
     }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
--  old description
--         "Setting this object to any not-null value has the effect of deleting
--          all entries of the ARP table."
-- new description
           "Setting to value deleteArpTab(1): deletes the arp table -
                                              static and dynamic entries
            deleteIpArpDynamicEntries(2):     delete all dynamic entries
            deleteIpArpStaticEntries(3):      delete all static entries
            deleteIpArpDelDynamicRefreshStatic(4) - delete all dynamic -
                                                    refresh static, thus
                                                    refrashing FFT.
            on get returns the last action"
    ::= { arpSpec 1  }

-- range 1 to 40,000,000 to allow system convesion to timer
rsArpInactiveTimeOut OBJECT-TYPE
    SYNTAX      Unsigned32 (1..40000000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This variable defines the maximum time period (in second) that can
          pass between ARP requests concerning an entry in the ARP table.
          After this time period, the entry is deleted from the table."
    DEFVAL  { 60000 }
    ::= { arpSpec 2  }

rsArpProxy OBJECT-TYPE
    SYNTAX INTEGER {
       enable(1),
       disable(2)
    }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "When ARP Proxy is enabled, the router can respond to
          ARP requests for nodes located on a different sub-net,
          provided they are it its network table. The router responds
          with its own MAC address.
          When ARP Proxy is disabled, the router responds only
          to ARP requests for its own IP addresses."
    DEFVAL  { disable }
    ::= { arpSpec 3  }

rsArpRequestsSent OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Displays how many ARP requests have been sent out to an ARP server
          for address resolution."
    ::= { arpSpec 4  }

rsArpRepliesSent OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Displays how many ARP replies have been sent out to an ARP client
          in response to request packets."
    ::= { arpSpec 5  }

rsArpProxyRepliesSent OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Displays how many proxy ARP replies have been sent out in response
          to request packets. A proxy router serving as a gateway to a subnet
          would respond with a proxy reply."
    ::= { arpSpec 6  }

rsArpUnresolveTimer OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "Specifies the frequency in seconds in which to send out ARP
          requests to resolve the Next Hop MAC address."
    ::= { arpSpec 7  }

rsArpMibVersion OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MIB's version, the current version is 2.
          Version 1:
            rsArpDeleteTable
                Setting this object to any not-null value has the effect
                of deleting all entries of the ARP table.
          Version 2:
            rsArpDeleteTable
                Setting to value deleteArpTab(1): deletes the arp table -
                                                  static and dynamic entries
                deleteIpArpDynamicEntries(2):     delete all dynamic entries
                deleteIpArpStaticEntries(3):      delete all static entries
                deleteIpArpDelDynamicRefreshStatic(4):
                                                    delete all dynamic -
                                                    refresh static, thus
                                                    refrashing FFT.
                on get returns the last action.
            New MIB variables support:
                rsArpRequestsSent
                rsArpRepliesSent
                rsArpProxyRepliesSent
                rsArpUnresolveTimer
                rsArpMibVersion"
    ::= { arpSpec 8  }

tftp     OBJECT IDENTIFIER ::= { ipSpec 5 }

rsTftpRetryTimeOut OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " General Retransmission time-out value (seconds) "
    DEFVAL  { 15 }
    ::= { tftp 1 }

rsTftpTotalTimeOut OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Total Retransmission time-out value (seconds) "
    DEFVAL  { 60 }
    ::= { tftp 2 }

rsSendConfigFile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The file name include path where the Router Server will put the full
         configuration. The default destination address will be the sender
         address."
    ::= { tftp 3 }

rsGetConfigFile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The file name include path where the Router Server will get the full
         configuration from. The default destination address will be the sender
         address."
    ::= { tftp 4 }

rsLoadSoftware OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The file name include path where the Router Server will get the
         software. The default source address will be the sender address."
    ::= { tftp 5 }

rsFileServerAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The IP address of the configuration / sw server."
        ::= { tftp 6 }

rsSoftwareDeviceName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..8))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Software Device Name specifies a device name, using this Software"
    ::= { tftp 7 }

rsSoftwareFileAction OBJECT-TYPE
   SYNTAX INTEGER {
        download(1),
        upload  (2)
   }
   MAX-ACCESS read-write
   STATUS     current
   DESCRIPTION
    "Holds the current action done on the software file "
   DEFVAL {download }
     ::= {tftp 8 }

ipRedundancy    OBJECT IDENTIFIER ::= { ipSpec 6 }
-- see rlIpRouter.mib

ipRouteLeaking    OBJECT IDENTIFIER ::= { ipSpec 7 }
-- see rlIpRouter.mib

ipRipFilter    OBJECT IDENTIFIER ::= { ipSpec 8 }
-- see rlIpRouter.mib

rsRipEnable OBJECT-TYPE
    SYNTAX  INTEGER {
        enable(1),
        disable(2)
    }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables or disables RIP."
::=  { ipSpec 9 }

rsTelnetPassword OBJECT-TYPE
    SYNTAX      OCTET STRING
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
           ""
    ::=  { ipSpec 11 }

rlTranslationNameToIpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF RlTranslationNameToIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table translates IP interfaces's name to
        IP interface's address"
    ::= { ipSpec 12 }

rlTranslationNameToIpEntry OBJECT-TYPE
    SYNTAX      RlTranslationNameToIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The row definition for this table."
    INDEX { IMPLIED rlTranslationNameToIpName }
    ::= { rlTranslationNameToIpTable 1 }

RlTranslationNameToIpEntry ::= SEQUENCE {
    rlTranslationNameToIpName   DisplayString,
    rlTranslationNameToIpIpAddr IpAddress
}

rlTranslationNameToIpName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..30))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP Interface name"
    ::=  { rlTranslationNameToIpEntry 1 }

rlTranslationNameToIpIpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP Interface address"
    ::=  { rlTranslationNameToIpEntry 2 }

-- Mib for Preferance among routing protocols:
-- Range value 0..255 .  O is most preferred, 255 never used for forwarding.
-- only exception is direct which range 0..254 we prevent direct from becoming unreachable
-- (according to RFC1812  section 5.2.4)

rlIpRoutingProtPreference OBJECT IDENTIFIER  ::=  { ipSpec 13 }
-- see rlIpRouter.mib

rlOspf OBJECT IDENTIFIER ::=  { ipSpec 14 }
-- see rlIpRouter.mib

--IP address table mib ver

rlIpAddrTableMibVersion OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IpAddrTable MIB's version."
    ::= {ipSpec 15 }

rlIpCidrRouteExtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF RlIpCidrRouteExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Augmenting ipCidrRouteTable (ip forwarfing information table)
             for added info as read only"
    ::= {ipSpec 16 }

rlIpCidrRouteExtEntry OBJECT-TYPE
    SYNTAX      RlIpCidrRouteExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A row of the table ipCidrRouteTable Extended
         by this definition."
    AUGMENTS { ipCidrRouteEntry }
    ::= {rlIpCidrRouteExtTable  1 }

RlIpCidrRouteExtEntry  ::= SEQUENCE {
    rlIpCidrRouteProto      INTEGER
}

rlIpCidrRouteProto OBJECT-TYPE
    SYNTAX   INTEGER {
        local(1),                 -- local interface
        netmgmt(2),               -- static route
        rip(3),                   -- Berkeley RIP or RIP-II
        ospfInternal(4),          -- Open Shortest Path First Internal Route
        ospfExternal(5),          -- Open Shortest Path First External Route
        ospfAggregateNetRange(6), -- Open Shortest Path First
        bgp4Internal(7),          -- Border Gateway Protocol Internal Route
        bgp4External(8),          -- Border Gateway Protocol External Route
        aggregateRoute(9),        --
        other(10)                 -- not specified
    }

    MAX-ACCESS  read-only
    STATUS      current
        DESCRIPTION
       "Added infor for ipCidrRouteTable.
        extends the info of ipCidrRouteProto to show the route inner protocol.
        Allowes the user to see which type of route in the protocol
        e.g. ospf internal, ospf external."
    ::= { rlIpCidrRouteExtEntry  1 }


rlIpStaticRoute OBJECT IDENTIFIER  ::=  { ipSpec 17 }

rlIpStaticRouteTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF RlIpStaticRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
      "This entity's    static (user configured) IP Routing table.
       entries are MAX-ACCESSible even if not used for forwarding "
    ::=  { rlIpStaticRoute      1 }

rlIpStaticRouteEntry OBJECT-TYPE
    SYNTAX      RlIpStaticRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "A particular Static(user configured) route to  a  particular  destina-
         tion, under a particular policy."
    INDEX { rlIpStaticRouteDest,
            rlIpStaticRouteMask,
            rlIpStaticRouteTos,
            rlIpStaticRouteNextHop }
    ::=  { rlIpStaticRouteTable 1 }


RlIpStaticRouteEntry ::= SEQUENCE {
        rlIpStaticRouteDest                  IpAddress,
        rlIpStaticRouteMask                  IpAddress,
        rlIpStaticRouteTos                   INTEGER,
        rlIpStaticRouteNextHop               IpAddress,
        rlIpStaticRouteMetric                INTEGER,
        rlIpStaticRouteType                  INTEGER,
        rlIpStaticRouteNextHopAS             INTEGER,
        rlIpStaticRouteForwardingStatus      INTEGER,
        rlIpStaticRouteRowStatus             RowStatus,
        rlIpStaticRouteOwner                 INTEGER
      }

rlIpStaticRouteDest OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
      "The destination IP address of this route.
       This object may not take a Multicast (Class  D)
       address value.
       Any assignment (implicit or  otherwise)  of  an
       instance  of  this  object to a value x must be
       rejected if the bitwise logical-AND of  x  with
       the  value of the corresponding instance of the
       rlIpStaticRouteMask object is not equal to x."
    ::= { rlIpStaticRouteEntry 1 }

rlIpStaticRouteMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
      "Indicate the mask to be logical-ANDed with the
       destination address before being compared to
       the value in the rlIpStaticRouteDest  field.   For
       those  systems that do not support arbitrary
       subnet masks, an agent constructs the value  of
       the  rlIpStaticRouteMask  by  reference to the IP Ad-
       dress Class.
       Any assignment (implicit or  otherwise)  of  an
       instance of this object to a value x must be
       rejected if the bitwise logical-AND of  x  with
       the  value of the corresponding instance of the
       rlIpStaticRouteDest object is not equal to ipCidrRoute-
       Dest."
    ::= { rlIpStaticRouteEntry 2 }

rlIpStaticRouteTos OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "See ipCidrRouteTos definition
        For now only value 0 is valid"
    ::= { rlIpStaticRouteEntry 3 }

rlIpStaticRouteNextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "On remote routes, the address of the next sys-
       tem en route; Otherwise, 0.0.0.0."
    ::= { rlIpStaticRouteEntry 4 }

rlIpStaticRouteMetric OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
       "The routing  metric  for  this  route.
       The semantics of this metric are determined by the user.
       normal semantic will be next hop count or some administarative distance
       to create routing policy."
    ::= { rlIpStaticRouteEntry 5 }

rlIpStaticRouteType OBJECT-TYPE
    SYNTAX   INTEGER {
                reject   (1), -- route which discards traffic
                local    (2), -- local interface
                remote   (3)  -- remote destination
             }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
       "The type of route.  Note that local(3)  refers
       to  a route for which the next hop is the final
       destination this is the case when user overides the a local interface
       entry to change it parameters;
       remote(4) refers to  a  route  for
       which  the  next  hop is not the final destina-
       tion.
       reject (2) refers to a route which, if matched, discards
       the message as unreachable. This is may be used as a means of
       correctly aggregating routes, When static routes are distributed (leaked)
       to other protocols."
    ::= { rlIpStaticRouteEntry 6 }

rlIpStaticRouteNextHopAS OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
       "The Autonomous System Number of the Next  Hop.
       The  semantics of this object are determined by
       the routing-protocol specified in  the  route's
       ipCidrRouteProto  value. When  this object is
       unknown or not relevant its value should be set
       to zero."
    DEFVAL { 0 }
    ::= { rlIpStaticRouteEntry 7 }

rlIpStaticRouteForwardingStatus OBJECT-TYPE
    SYNTAX   INTEGER {
                active (1),
                inactive  (2)
             }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "active - An indication that the route has implication on routing
       inactive - the route is a backup route or it is down. It is not used
                  in forwarding decision.
       Down means that the Ip interface on which it is configured is down.
       (Note: ip interface down may be for two reason - its admin status or
       the L2 interface , on which the ip interface is configured, status"
    ::= { rlIpStaticRouteEntry 8 }

rlIpStaticRouteRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
       "The row status variable, used according to
       row installation and removal conventions."
    ::= { rlIpStaticRouteEntry 9 }

rlIpStaticRouteOwner OBJECT-TYPE
    SYNTAX   INTEGER {
                static  (1),
                dhcp    (2),
                default (3)
             }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "Static - The route is configured over Static IP.
                 This route is written to configuration files.
        Dhcp -   The route is Configured by DHCP
                 (received as part of DHCP configuration)
                 This route IS NOT written to configuration files
        Dhcp -   The route is Configured default system config
                 exist till any other configuration
                 is applied"
    ::= { rlIpStaticRouteEntry 10 }

rlIpRouter OBJECT IDENTIFIER  ::=  { ipSpec 18 }


END

