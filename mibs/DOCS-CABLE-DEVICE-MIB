DOCS-CABLE-DEVICE-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY,
        OBJECT-TYPE,
-- do not import        <PERSON><PERSON><PERSON>,
        <PERSON>p<PERSON>dd<PERSON>,
        Unsigned<PERSON>,
        <PERSON>32,
        Integer32,
        zeroDot<PERSON><PERSON>,
        mib-2
                FROM SNMPv2-<PERSON><PERSON>
        RowStatus,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>ndT<PERSON>,
        TruthValue
                FROM SNMPv2-TC
        OBJECT-GROUP,
        MODULE-COMPLIANCE
                FROM SNMPv2-CONF
        SnmpAdminString
                FROM SNMP-FRAMEWORK-MIB
        InterfaceIndexOrZero
                FROM IF-MIB;  -- RFC2233

docsDev MODULE-IDENTITY
        LAST-UPDATED    "9908190000Z" -- August 19, 1999
        ORGANIZATION    "IETF IPCDN Working Group"
        CONTACT-INFO
            "        Michael <PERSON>: @Home Network
                     425 Broadway
                     Redwood City, CA 94063
                     U.S.A.
             Phone:  ****** 569 5368
             E-mail: <EMAIL>"





        DESCRIPTION
            "This is the MIB Module for MCNS-compliant cable modems and
             cable-modem termination systems."
        REVISION "9908190000Z"
        DESCRIPTION
            "Initial Version, published as RFC 2669.
             Modified by <PERSON> <PERSON>Johns to add/revise filtering, TOS
             support, software version information objects."
        ::= { mib-2 69 }

docsDevMIBObjects  OBJECT IDENTIFIER ::= { docsDev 1 }
docsDevBase OBJECT IDENTIFIER ::= { docsDevMIBObjects 1 }

--
-- For the following object, there is no concept in the
-- RFI specification corresponding to a backup CMTS. The
-- enumeration is provided here in case someone is able
-- to define such a role or device.
--

docsDevRole OBJECT-TYPE
        SYNTAX INTEGER {
            cm(1),
            cmtsActive(2),
            cmtsBackup(3)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
             "Defines the current role of this device.  cm (1) is
             a Cable Modem, cmtsActive(2) is a Cable Modem Termination
             System which is controlling the system of cable modems,
             and cmtsBackup(3) is a CMTS which is currently connected,
             but not controlling the system (not currently used).

             In general, if this device is a 'cm', its role will not
             change during operation or between reboots.  If the
             device is a 'cmts' it may change between cmtsActive and
             cmtsBackup and back again during normal operation.  NB:
             At this time, the DOCSIS standards do not support the
             concept of a backup CMTS, cmtsBackup is included for
             completeness."
        ::= { docsDevBase 1 }

docsDevDateTime OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  read-write
        STATUS      current





        DESCRIPTION
            "The date and time, with optional timezone
             information."
        ::= { docsDevBase 2 }

docsDevResetNow OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Setting this object to true(1) causes the device to reset.
             Reading this object always returns false(2)."
        ::= { docsDevBase 3 }

docsDevSerialNumber OBJECT-TYPE
        SYNTAX      SnmpAdminString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The manufacturer's serial number for this device."
        ::= { docsDevBase 4 }

docsDevSTPControl OBJECT-TYPE
        SYNTAX INTEGER {
            stEnabled(1),
            noStFilterBpdu(2),
            noStPassBpdu(3)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This object controls operation of the spanning tree
             protocol (as distinguished from transparent bridging).
             If set to stEnabled(1) then the spanning tree protocol
             is enabled, subject to bridging constraints. If
             noStFilterBpdu(2), then spanning tree is not active,
             and Bridge PDUs received are discarded.
             If noStPassBpdu(3) then spanning tree is not active
             and Bridge PDUs are transparently forwarded. Note that
             a device need not implement all of these options,
             but that noStFilterBpdu(2) is required."
        ::= { docsDevBase 5 }

--
-- The following table provides one level of security for access
-- to the device by network management stations.
-- Note that access is also constrained by the
-- community strings and any vendor-specific security.





--

docsDevNmAccessTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevNmAccessEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This table controls access to SNMP objects by network
             management stations. If the table is empty, access
             to SNMP objects is unrestricted.  This table exists only
             on SNMPv1 or v2c agents and does not exist on SNMPv3
             agents. See the conformance section for details.
             Specifically, for v3 agents, the appropriate MIBs and
             security models apply in lieu of this table."
        ::= { docsDevMIBObjects 2 }

docsDevNmAccessEntry OBJECT-TYPE
        SYNTAX      DocsDevNmAccessEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry describing  access to SNMP objects by a
             particular network management station. An entry in
             this table is not readable unless the management station
             has read-write permission (either implicit if the table
             is empty, or explicit through an entry in this table.
             Entries are ordered by docsDevNmAccessIndex.  The first
             matching entry (e.g. matching IP address and community
             string) is used to derive access."
        INDEX { docsDevNmAccessIndex  }
        ::= {  docsDevNmAccessTable 1 }

DocsDevNmAccessEntry ::= SEQUENCE {
            docsDevNmAccessIndex         Integer32,
            docsDevNmAccessIp            IpAddress,
            docsDevNmAccessIpMask        IpAddress,
            docsDevNmAccessCommunity     OCTET STRING,
            docsDevNmAccessControl       INTEGER,
            docsDevNmAccessInterfaces    OCTET STRING,
            docsDevNmAccessStatus        RowStatus
        }

docsDevNmAccessIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Index used to order the application of access





             entries."
        ::= { docsDevNmAccessEntry 1 }

docsDevNmAccessIp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The IP address (or subnet) of the network management
             station. The address *************** is defined to mean
             any NMS. If traps are enabled for this entry, then the
             value must be the address of a specific device."
        DEFVAL { 'ffffffff'h }
        ::= { docsDevNmAccessEntry 2 }

docsDevNmAccessIpMask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The IP subnet mask of the network management stations.
             If traps are enabled for this entry, then the value must
             be ***************."
        DEFVAL { 'ffffffff'h }
        ::= { docsDevNmAccessEntry 3 }

docsDevNmAccessCommunity OBJECT-TYPE
        SYNTAX      OCTET STRING
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The community string to be matched for access by this
             entry. If set to a zero length string then any community
             string will match.  When read, this object SHOULD return
             a zero length string."
        DEFVAL { "public" }
        ::= { docsDevNmAccessEntry 4 }

docsDevNmAccessControl OBJECT-TYPE
        SYNTAX         INTEGER {
            none(1),
            read(2),
            readWrite(3),
            roWithTraps(4),
            rwWithTraps(5),
            trapsOnly(6)
        }
        MAX-ACCESS  read-create





        STATUS      current
        DESCRIPTION
            "Specifies the type of access allowed to this NMS. Setting
             this object to none(1) causes the table entry to be
             destroyed. Read(2) allows access by 'get' and 'get-next'
             PDUs. ReadWrite(3) allows access by 'set' as well.
             RoWithtraps(4), rwWithTraps(5), and trapsOnly(6)
             control distribution of Trap PDUs transmitted by this
             device."
        DEFVAL { read }
        ::= { docsDevNmAccessEntry 5 }

-- The syntax of the following object was copied from RFC1493,
-- dot1dStaticAllowedToGoTo.

docsDevNmAccessInterfaces OBJECT-TYPE
        SYNTAX      OCTET STRING
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Specifies the set of interfaces from which requests from
             this NMS will be accepted.
             Each octet within the value of this object specifies a set
             of eight interfaces, with the first octet specifying ports
             1 through 8, the second octet specifying interfaces 9
             through 16, etc.  Within each octet, the most significant
             bit represents the lowest numbered interface, and the least
             significant bit represents the highest numbered interface.
             Thus, each interface is represented by a single bit within
             the value of this object. If that bit has a value of '1'
             then that interface is included in the set.

             Note that entries in this table apply only to link-layer
             interfaces (e.g., Ethernet and CATV MAC). Upstream and
             downstream channel interfaces must not be specified."
--         DEFVAL is the bitmask corresponding to all interfaces
        ::= { docsDevNmAccessEntry 6 }

docsDevNmAccessStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Controls and reflects the status of rows in this
             table. Rows in this table may be created by either the
             create-and-go or create-and-wait paradigms.  There is no
             restriction on changing values in a row of this table while
             the row is active."





        ::= { docsDevNmAccessEntry 7 }

--
--  Procedures for using the following group are described in section
--  3.2.1 of the DOCSIS Radio Frequence Interface Specification
--

docsDevSoftware OBJECT IDENTIFIER ::= { docsDevMIBObjects 3 }

docsDevSwServer OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The address of the TFTP server used for software upgrades.
             If the TFTP server is unknown, return 0.0.0.0."
        ::= { docsDevSoftware 1 }

docsDevSwFilename OBJECT-TYPE
        SYNTAX      SnmpAdminString (SIZE (0..64))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The file name of the software image to be loaded into this
             device. Unless set via SNMP, this is the file name
             specified by the provisioning server that corresponds to
             the software version that is desired for this device.
             If unknown, the string '(unknown)' is returned."
        ::= { docsDevSoftware 2 }

docsDevSwAdminStatus OBJECT-TYPE
        SYNTAX INTEGER {
            upgradeFromMgt(1),
            allowProvisioningUpgrade(2),
            ignoreProvisioningUpgrade(3)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "If set to upgradeFromMgt(1), the device will initiate a
             TFTP software image download using docsDevSwFilename.
             After successfully receiving an image, the device will
             set its state to ignoreProvisioningUpgrade(3) and reboot.
             If the download process is interrupted by a reset or
             power failure, the device will load the previous image
             and, after re-initialization, continue to attempt loading
             the image specified in docsDevSwFilename.






             If set to allowProvisioningUpgrade(2), the device will
             use the software version information supplied by the
             provisioning server when next rebooting (this does not
             cause a reboot).

             When set to ignoreProvisioningUpgrade(3), the device
             will disregard software image upgrade information from the
             provisioning server.

             Note that reading this object can return upgradeFromMgt(1).
             This indicates that a software download is currently in
             progress, and that the device will reboot after
             successfully receiving an image.

             At initial startup, this object has the default value of
             allowProvisioningUpgrade(2)."
        ::= { docsDevSoftware 3 }

docsDevSwOperStatus OBJECT-TYPE
        SYNTAX INTEGER {
            inProgress(1),
            completeFromProvisioning(2),
            completeFromMgt(3),
            failed(4),
            other(5)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "InProgress(1) indicates that a TFTP download is underway,
             either as a result of a version mismatch at provisioning
             or as a result of a upgradeFromMgt request.
             CompleteFromProvisioning(2) indicates that the last
             software upgrade was a result of version mismatch at
             provisioning. CompleteFromMgt(3) indicates that the last
             software upgrade was a result of setting
             docsDevSwAdminStatus to upgradeFromMgt.
             Failed(4) indicates that the last attempted download
             failed, ordinarily due to TFTP timeout."
        REFERENCE
             "DOCSIS Radio Frequency Interface Specification, Section
             8.2, Downloading Cable Modem Operating Software."
        ::= { docsDevSoftware 4 }

docsDevSwCurrentVers OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current





    DESCRIPTION
            "The software version currently operating in this device.
             This object should be in the syntax used by the individual
             vendor to identify software versions.  Any CM MUST return a
             string descriptive of the current software load.  For a
             CMTS, this object SHOULD contain either a human readable
             representation of the vendor specific designation of the
             software for the chassis, or of the software for the
             control processor. If neither of these is  applicable,
             this MUST contain an empty string."
    ::= { docsDevSoftware 5 }


--
-- The following group describes server access and parameters used for
-- initial provisioning and bootstrapping.
--

docsDevServer OBJECT IDENTIFIER ::= { docsDevMIBObjects 4 }

docsDevServerBootState OBJECT-TYPE
        SYNTAX INTEGER {
            operational(1),
            disabled(2),
            waitingForDhcpOffer(3),
            waitingForDhcpResponse(4),
            waitingForTimeServer(5),
            waitingForTftp(6),
            refusedByCmts(7),
            forwardingDenied(8),
            other(9),
            unknown(10)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "If operational(1), the device has completed loading and
             processing of configuration parameters and the CMTS has
             completed the Registration exchange.
             If disabled(2) then the device was administratively
             disabled, possibly by being refused network access in the
             configuration file.
             If waitingForDhcpOffer(3) then a DHCP Discover has been
             transmitted and no offer has yet been received.
             If waitingForDhcpResponse(4) then a DHCP Request has been
             transmitted and no response has yet been received.
             If waitingForTimeServer(5) then a Time Request has been
             transmitted and no response has yet been received.





             If waitingForTftp(6) then a request to the TFTP parameter
             server has been made and no response received.
             If refusedByCmts(7) then the Registration Request/Response
             exchange with the CMTS failed.
             If forwardingDenied(8) then the registration process
             completed, but the network access option in the received
             configuration file prohibits forwarding. "
        REFERENCE
             "DOCSIS Radio Frequency Interface Specification, Figure
             7-1, CM Initialization Overview."
        ::= { docsDevServer 1 }

docsDevServerDhcp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The IP address of the DHCP server that assigned an IP
             address to this device. Returns 0.0.0.0 if DHCP was not
             used for IP address assignment."
        ::= { docsDevServer 2 }

docsDevServerTime OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The IP address of the Time server (RFC-868). Returns
             0.0.0.0 if the time server IP address is unknown."
        ::= { docsDevServer 3 }

docsDevServerTftp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The IP address of the TFTP server responsible for
             downloading provisioning and configuration parameters
             to this device. Returns 0.0.0.0 if the TFTP server
             address is unknown."
        ::= { docsDevServer 4 }

docsDevServerConfigFile OBJECT-TYPE
        SYNTAX      SnmpAdminString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The name of the device configuration file read from the





             TFTP server. Returns an empty string if the configuration
             file name is unknown."
        ::= { docsDevServer 5 }

--
--  Event Reporting
--

docsDevEvent OBJECT IDENTIFIER ::= { docsDevMIBObjects 5 }

docsDevEvControl OBJECT-TYPE
        SYNTAX INTEGER {
            resetLog(1),
            useDefaultReporting(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Setting this object to resetLog(1) empties the event log.
             All data is deleted. Setting it to useDefaultReporting(2)
             returns all event priorities to their factory-default
             reporting. Reading this object always returns
             useDefaultReporting(2)."
        ::= { docsDevEvent 1 }

docsDevEvSyslog OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The IP address of the Syslog server. If 0.0.0.0, syslog
             transmission is inhibited."
        ::= { docsDevEvent 2 }

docsDevEvThrottleAdminStatus OBJECT-TYPE
        SYNTAX INTEGER {
            unconstrained(1),
            maintainBelowThreshold(2),
            stopAtThreshold(3),
            inhibited(4)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Controls the transmission of traps and syslog messages
             with respect to the trap pacing threshold.
             unconstrained(1) causes traps and syslog messages to be
             transmitted without regard to the threshold settings.





             maintainBelowThreshold(2) causes trap transmission and
             syslog messages to be suppressed if the number of traps
             would otherwise exceed the threshold.
             stopAtThreshold(3) causes trap transmission to cease
             at the threshold, and not resume until directed to do so.
             inhibited(4) causes all trap transmission and syslog
             messages to be suppressed.

             A single event is always treated as a single event for
             threshold counting. That is, an event causing both a trap
             and a syslog message is still treated as a single event.

             Writing to this object resets the thresholding state.

             At initial startup, this object has a default value of
             unconstrained(1)."
        ::= { docsDevEvent 3 }

docsDevEvThrottleInhibited OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "If true(1), trap and syslog transmission is currently
             inhibited due to thresholds and/or the current setting of
             docsDevEvThrottleAdminStatus. In addition, this is set to
             true(1) if transmission is inhibited due to no
             syslog (docsDevEvSyslog) or trap (docsDevNmAccessEntry)
             destinations having been set."
        ::= { docsDevEvent 4 }

docsDevEvThrottleThreshold OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Number of trap/syslog events per docsDevEvThrottleInterval
             to be transmitted before throttling.

             A single event is always treated as a single event for
             threshold counting. That is, an event causing both a trap
             and a syslog message is still treated as a single event.

             At initial startup, this object returns 0."
        ::= { docsDevEvent 5 }

docsDevEvThrottleInterval OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)





        UNITS       "seconds"
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The interval over which the trap threshold applies.
             At initial startup, this object has a value of 1."

        ::= { docsDevEvent 6 }

--
-- The following table controls the reporting of the various classes of
-- events.
--

docsDevEvControlTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevEvControlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This table allows control of the reporting of event classes.
        For each event priority, a combination of logging and
        reporting mechanisms may be chosen. The mapping of event types
        to priorities is vendor-dependent. Vendors may also choose to
        allow the user to control that mapping through proprietary
        means."
        ::= {  docsDevEvent 7 }


docsDevEvControlEntry OBJECT-TYPE
        SYNTAX      DocsDevEvControlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Allows configuration of the reporting mechanisms for a
             particular event priority."
        INDEX { docsDevEvPriority }
        ::= { docsDevEvControlTable 1 }

DocsDevEvControlEntry ::= SEQUENCE {
            docsDevEvPriority        INTEGER,
            docsDevEvReporting       BITS
        }

docsDevEvPriority OBJECT-TYPE
        SYNTAX INTEGER {
            emergency(1),
            alert(2),
            critical(3),





            error(4),
            warning(5),
            notice(6),
            information(7),
            debug(8)
        }
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The priority level that is controlled by this
             entry. These are ordered from most (emergency) to least
             (debug) critical.  Each event with a CM or CMTS has a
             particular priority level associated with it (as defined
             by the vendor). During normal operation no event more
             critical than notice(6) should be generated. Events between
             warning and emergency should be generated at appropriate
             levels of problems (e.g. emergency when the box is about to
             crash)."
        ::= { docsDevEvControlEntry 1 }

docsDevEvReporting OBJECT-TYPE
        SYNTAX BITS {
            local(0),
            traps(1),
            syslog(2),
            local-vol(3)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Defines the action to be taken on occurrence of this
             event class. Implementations may not necessarily support
             all options for all event classes, but at minimum must
             allow traps and syslogging to be disabled. If the
             local(0) bit is set, then log to the internal log, if the
             traps(1) bit is set, then generate a trap, if the
             syslog(2) bit is set, then send a syslog message
             (assuming the syslog address is set)."
        ::= { docsDevEvControlEntry 2 }

docsDevEventTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevEventEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Contains a log of network and device events that may be
             of interest in fault isolation and troubleshooting."
        ::= {  docsDevEvent 8 }






docsDevEventEntry OBJECT-TYPE
        SYNTAX      DocsDevEventEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Describes a network or device event that may be of
             interest in fault isolation and troubleshooting. Multiple
             sequential identical events are represented by
             incrementing docsDevEvCounts and setting
             docsDevEvLastTime to the current time rather than creating
             multiple rows.

             Entries are created with the first occurrance of an event.
             docsDevEvControl can be used to clear the table.
             Individual events can not be deleted."
        INDEX { docsDevEvIndex }

        ::= { docsDevEventTable 1 }

DocsDevEventEntry ::= SEQUENCE {
            docsDevEvIndex           Integer32,
            docsDevEvFirstTime       DateAndTime,
            docsDevEvLastTime        DateAndTime,
            docsDevEvCounts          Counter32,
            docsDevEvLevel           INTEGER,
            docsDevEvId              Unsigned32,
            docsDevEvText            SnmpAdminString
        }

docsDevEvIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Provides relative ordering of the objects in the event
             log. This object will always increase except when
             (a) the log is reset via docsDevEvControl,
             (b) the device reboots and does not implement non-volatile
             storage for this log, or (c) it reaches the value 2^31.
             The next entry for all the above cases is 1."
        ::= { docsDevEventEntry 1 }

docsDevEvFirstTime OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The time that this entry was created."





        ::= { docsDevEventEntry 2 }

docsDevEvLastTime OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "If multiple events are reported via the same entry, the
             time that the last event for this entry occurred,
             otherwise this should have the same value as
             docsDevEvFirstTime. "
        ::= { docsDevEventEntry 3 }

-- This object was renamed from docsDevEvCount to meet naming
-- requirements for Counter32
docsDevEvCounts OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of consecutive event instances reported by
             this entry.  This starts at 1 with the creation of this
             row and increments by 1 for each subsequent duplicate
             event."
        ::= { docsDevEventEntry 4 }

docsDevEvLevel OBJECT-TYPE
        SYNTAX INTEGER {
            emergency(1),
            alert(2),
            critical(3),
            error(4),
            warning(5),
            notice(6),
            information(7),
            debug(8)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The priority level of this event as defined by the
             vendor.  These are ordered from most serious (emergency)
             to least serious (debug)."
        ::= { docsDevEventEntry 5 }

--
-- Vendors will provide their own enumerations for the following.
-- The interpretation of the enumeration is unambiguous for a





-- particular value of the vendor's enterprise number in sysObjectID.
--

docsDevEvId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "For this product, uniquely identifies the type of event
             that is reported by this entry."
        ::= { docsDevEventEntry 6 }

docsDevEvText OBJECT-TYPE
        SYNTAX      SnmpAdminString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Provides a human-readable description of the event,
             including all relevant context (interface numbers,
             etc.)."
        ::= { docsDevEventEntry 7 }

docsDevFilter OBJECT IDENTIFIER ::= { docsDevMIBObjects 6 }


--
-- Link Level Control Filtering
--

-- docsDevFilterLLCDefault renamed to docsDevFilterLLCUnmatchedAction

docsDevFilterLLCUnmatchedAction OBJECT-TYPE
        SYNTAX INTEGER {
            discard(1),
            accept(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "LLC (Link Level Control) filters can be defined on an
             inclusive or exclusive basis: CMs can be configured to
             forward only packets matching a set of layer three
             protocols, or to drop packets matching a set of layer
             three protocols.  Typical use of these filters is to
             filter out possibly harmful (given the context of a large
             metropolitan LAN) protocols.

             If set to discard(1), any L2 packet which does not match at





             least one filter in the docsDevFilterLLCTable will be
             discarded. If set to accept(2), any L2 packet which does not
             match at least one filter in the docsDevFilterLLCTable
             will be accepted for further processing (e.g., bridging).
             At initial system startup, this object returns accept(2)."
        ::= { docsDevFilter 1 }

docsDevFilterLLCTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevFilterLLCEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of filters to apply to (bridged) LLC
             traffic. The filters in this table are applied to
             incoming traffic on the appropriate interface(s)  prior
             to any further processing (e.g. before handing the packet
             off for level 3 processing, or for bridging).  The
             specific action taken when no filter is matched is
             controlled by docsDevFilterLLCUnmatchedAction."
        ::= { docsDevFilter 2 }

docsDevFilterLLCEntry OBJECT-TYPE
        SYNTAX      DocsDevFilterLLCEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Describes a single filter to apply to (bridged) LLC traffic
             received on a specified interface. "
        INDEX { docsDevFilterLLCIndex }
        ::= { docsDevFilterLLCTable 1 }

DocsDevFilterLLCEntry ::= SEQUENCE {
            docsDevFilterLLCIndex               Integer32,
            docsDevFilterLLCStatus              RowStatus,
            docsDevFilterLLCIfIndex             InterfaceIndexOrZero,
            docsDevFilterLLCProtocolType        INTEGER,
            docsDevFilterLLCProtocol            Integer32,
            docsDevFilterLLCMatches             Counter32
        }

docsDevFilterLLCIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Index used for the identification of filters (note that LLC
             filter order is irrelevant)."
        ::= { docsDevFilterLLCEntry 1 }





docsDevFilterLLCStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Controls and reflects the status of rows in this
             table. There is no restriction on changing any of the
             associated columns for this row while this object is set
             to active."

        ::= { docsDevFilterLLCEntry 2}

docsDevFilterLLCIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The entry interface to which this filter applies.
             The value corresponds to ifIndex for either a CATV MAC
             or another network interface. If the value is zero, the
             filter applies to all interfaces. In Cable Modems, the
             default value is the customer side interface. In Cable
             Modem Termination Systems, this object has to be
             specified to create a row in this table."
        ::= { docsDevFilterLLCEntry 3 }

docsDevFilterLLCProtocolType OBJECT-TYPE
        SYNTAX INTEGER {
            ethertype(1),
            dsap(2)
        }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The format of the value in docsDevFilterLLCProtocol:
             either a two-byte Ethernet Ethertype, or a one-byte
             802.2 SAP value. EtherType(1) also applies to SNAP-
             encapsulated frames."
        DEFVAL { ethertype }
        ::= { docsDevFilterLLCEntry 4 }

docsDevFilterLLCProtocol OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The layer three protocol for which this filter applies.
             The protocol value format depends on





             docsDevFilterLLCProtocolType. Note that for SNAP frames,
             etherType filtering is performed rather than DSAP=0xAA."
        DEFVAL { 0 }
        ::= { docsDevFilterLLCEntry 5 }

docsDevFilterLLCMatches OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Counts the number of times this filter was matched."
        ::= { docsDevFilterLLCEntry 6 }

-- The default behavior for (bridged) packets that do not match IP
-- filters is defined by
-- docsDevFilterIpDefault.

docsDevFilterIpDefault OBJECT-TYPE
        SYNTAX INTEGER {
            discard(1),
            accept(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "If set to discard(1), all packets not matching an IP filter
             will be discarded. If set to accept(2), all packets not
             matching an IP filter will be accepted for further
             processing (e.g., bridging).
             At initial system startup, this object returns accept(2)."
        ::= { docsDevFilter 3 }

docsDevFilterIpTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevFilterIpEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An ordered list of filters or classifiers to apply to
             IP traffic. Filter application is ordered by the filter
             index, rather than by a best match algorithm (Note that
             this implies that the filter table may have gaps in the
             index values). Packets which match no filters will have
             policy 0 in the docsDevFilterPolicyTable applied to them if
             it exists. Otherwise, Packets which match no filters
             are discarded or forwarded according to the setting of
             docsDevFilterIpDefault.

             Any IP packet can theoretically match multiple rows of





             this table.  When considering a packet, the table is
             scanned in row index order (e.g. filter 10 is checked
             before filter 20).  If the packet matches that filter
             (which means that it matches ALL criteria for that row),
             actions appropriate to docsDevFilterIpControl and
             docsDevFilterPolicyId are taken.  If the packet was
             discarded processing is complete.  If
             docsDevFilterIpContinue is set to true, the filter
             comparison continues with the next row in the table
             looking for additional matches.

             If the packet matches no filter in the table, the packet
             is accepted or dropped for further processing based on
             the setting of docsDevFilterIpDefault. If the packet is
             accepted, the actions specified by policy group 0
             (e.g. the rows in docsDevFilterPolicyTable which have a
             value of 0 for docsDevFilterPolicyId) are taken if that
             policy group exists.

             Logically, this table is consulted twice during the
             processing of any IP packet - once upon its acceptance
             from the L2 entity, and once upon its transmission to the
             L2 entity.  In actuality, for cable modems, IP filtering
             is generally the only IP processing done for transit
             traffic.  This means that inbound and outbound filtering
             can generally be done at the same time with one pass
             through the filter table."
        ::= { docsDevFilter 4 }

docsDevFilterIpEntry OBJECT-TYPE
        SYNTAX      DocsDevFilterIpEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Describes a filter to apply to IP traffic received on a
             specified interface.  All identity objects in this table
             (e.g. source and destination address/mask, protocol,
             source/dest port, TOS/mask, interface and direction) must
             match their respective fields in the packet for any given
             filter to match.

             To create an entry in this table, docsDevFilterIpIfIndex
             must be specified."
        INDEX { docsDevFilterIpIndex }
        ::= { docsDevFilterIpTable 1 }

DocsDevFilterIpEntry ::= SEQUENCE {
            docsDevFilterIpIndex             Integer32,





            docsDevFilterIpStatus            RowStatus,
            docsDevFilterIpControl           INTEGER,
            docsDevFilterIpIfIndex           InterfaceIndexOrZero,
            docsDevFilterIpDirection         INTEGER,
            docsDevFilterIpBroadcast         TruthValue,
            docsDevFilterIpSaddr             IpAddress,
            docsDevFilterIpSmask             IpAddress,
            docsDevFilterIpDaddr             IpAddress,
            docsDevFilterIpDmask             IpAddress,
            docsDevFilterIpProtocol          Integer32,
            docsDevFilterIpSourcePortLow     Integer32,
            docsDevFilterIpSourcePortHigh    Integer32,
            docsDevFilterIpDestPortLow       Integer32,
            docsDevFilterIpDestPortHigh      Integer32,
            docsDevFilterIpMatches           Counter32,
            docsDevFilterIpTos               OCTET STRING,
            docsDevFilterIpTosMask           OCTET STRING,
            docsDevFilterIpContinue          TruthValue,
            docsDevFilterIpPolicyId          Integer32
        }

docsDevFilterIpIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Index used to order the application of filters.
             The filter with the lowest index is always applied
             first."
        ::= { docsDevFilterIpEntry 1 }

docsDevFilterIpStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Controls and reflects the status of rows in this
             table. Specifying only this object (with the appropriate
             index) on a CM is sufficient to create a filter row which
             matches all inbound packets on the ethernet interface,
             and results in the packets being
             discarded. docsDevFilterIpIfIndex (at least) must be
             specified on a CMTS to create a row.  Creation of the
             rows may be done via either create-and-wait or
             create-and-go, but the filter is not applied until this
             object is set to (or changes to) active. There is no
             restriction in changing any object in a row while this
             object is set to active."





        ::= { docsDevFilterIpEntry 2 }

docsDevFilterIpControl OBJECT-TYPE
        SYNTAX INTEGER {
            discard(1),
            accept(2),
            policy(3)
        }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If set to discard(1), all packets matching this filter
             will be discarded and scanning of the remainder of the
             filter list will be aborted. If set to accept(2), all
             packets matching this filter will be accepted for further
             processing (e.g., bridging). If docsDevFilterIpContinue
             is set to true, see if there are other matches, otherwise
             done. If set to policy (3), execute the policy entries
             matched by docsDevIpFilterPolicyId in
             docsDevIpFilterPolicyTable.

             If is docsDevFilterIpContinue is set to true, continue
             scanning the table for other matches, otherwise done."
        DEFVAL { discard }
        ::= { docsDevFilterIpEntry 3 }

docsDevFilterIpIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The entry interface to which this filter applies. The
             value corresponds to ifIndex for either a CATV MAC or
             another network interface. If the value is zero, the
             filter applies to all interfaces. Default value in Cable
             Modems is the index of the customer-side (e.g. ethernet)
             interface. In Cable Modem Termination Systems, this
             object MUST be specified to create a row in this table."
        ::= { docsDevFilterIpEntry 4 }

docsDevFilterIpDirection OBJECT-TYPE
        SYNTAX INTEGER {
            inbound(1),
            outbound(2),
            both(3)
        }
        MAX-ACCESS  read-create
        STATUS      current





        DESCRIPTION
            "Determines whether the filter is applied to inbound(1)
             traffic, outbound(2) traffic, or traffic in both(3)
             directions."
        DEFVAL { inbound }
        ::= { docsDevFilterIpEntry 5 }

docsDevFilterIpBroadcast OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If set to true(1), the filter only applies to multicast
             and broadcast traffic. If set to false(2), the filter
             applies to all traffic."
        DEFVAL { false }
        ::= { docsDevFilterIpEntry 6 }

docsDevFilterIpSaddr OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The source IP address, or portion thereof, that is to be
             matched for this filter.  The source address is first
             masked (and'ed) against docsDevFilterIpSmask before being
             compared  to this value.  A value of 0 for this object
             and 0 for the mask matches all IP addresses."
        DEFVAL { '00000000'h }
        ::= { docsDevFilterIpEntry 7 }

docsDevFilterIpSmask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "A bit mask that is to be applied to the source address
             prior to matching. This mask is not necessarily the same
             as a subnet mask, but 1's bits must be leftmost and
             contiguous."
        DEFVAL { '00000000'h }
        ::= { docsDevFilterIpEntry 8 }

docsDevFilterIpDaddr OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION





            "The destination IP address, or portion thereof, that is
             to be matched for this filter. The destination address is
             first masked (and'ed) against docsDevFilterIpDmask before
             being compared  to this value.  A value of 0 for this
             object and 0 for the mask matches all IP addresses."
        DEFVAL { '00000000'h }
        ::= { docsDevFilterIpEntry 9 }

docsDevFilterIpDmask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "A bit mask that is to be applied to the destination
             address prior to matching. This mask is not necessarily
             the same as a subnet mask, but 1's bits must be leftmost
             and contiguous."
        DEFVAL { '00000000'h }
        ::= { docsDevFilterIpEntry 10 }

docsDevFilterIpProtocol OBJECT-TYPE
        SYNTAX Integer32 (0..256)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The IP protocol value that is to be matched. For example:
             icmp is 1, tcp is 6, udp is 17. A value of 256 matches
             ANY protocol."
        DEFVAL { 256 }
        ::= { docsDevFilterIpEntry 11 }

docsDevFilterIpSourcePortLow OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If docsDevFilterIpProtocol is udp or tcp, this is the
             inclusive lower bound of the transport-layer source port
             range that is to be matched, otherwise it is ignored
             during matching."
        DEFVAL { 0 }
        ::= { docsDevFilterIpEntry 12 }

docsDevFilterIpSourcePortHigh OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION





            "If docsDevFilterIpProtocol is udp or tcp, this is the
             inclusive upper bound of the transport-layer source port
             range that is to be matched, otherwise it is ignored
             during matching."
        DEFVAL { 65535 }
        ::= { docsDevFilterIpEntry 13 }

docsDevFilterIpDestPortLow OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If docsDevFilterIpProtocol is udp or tcp, this is the
             inclusive lower bound of the transport-layer destination
             port range that is to be matched, otherwise it is ignored
             during matching."
        DEFVAL { 0 }
        ::= { docsDevFilterIpEntry 14 }

docsDevFilterIpDestPortHigh OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If docsDevFilterIpProtocol is udp or tcp, this is the
             inclusive upper bound of the transport-layer destination
             port range that is to be matched, otherwise it is ignored
             during matching."
        DEFVAL { 65535 }
        ::= { docsDevFilterIpEntry 15 }

docsDevFilterIpMatches OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Counts the number of times this filter was matched.
             This object is initialized to 0 at boot, or at row
             creation, and is reset only upon reboot."
        ::= { docsDevFilterIpEntry 16 }

docsDevFilterIpTos  OBJECT-TYPE
        SYNTAX      OCTET STRING ( SIZE (1))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This is the value to be matched to the packet's
             TOS (Type of Service) value (after the TOS value





             is AND'd with docsDevFilterIpTosMask).  A value for this
             object of 0 and a mask of 0 matches all TOS values."
        DEFVAL { '00'h }
        ::= { docsDevFilterIpEntry 17 }

docsDevFilterIpTosMask OBJECT-TYPE
        SYNTAX      OCTET STRING ( SIZE (1) )
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The mask to be applied to the packet's TOS value before
             matching."
        DEFVAL { '00'h }
        ::= { docsDevFilterIpEntry 18 }

docsDevFilterIpContinue OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "If this value is set to true, and docsDevFilterIpControl
             is anything but discard (1), continue scanning and
             applying policies."
        DEFVAL { false }
        ::= { docsDevFilterIpEntry 19 }

docsDevFilterIpPolicyId OBJECT-TYPE
        SYNTAX      Integer32 (0..2147483647)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object points to an entry in docsDevFilterPolicyTable.
             If docsDevFilterIpControl is set to policy (3), execute
             all matching policies in docsDevFilterPolicyTable.
             If no matching policy exists, treat as if
             docsDevFilterIpControl were set to accept (1).
             If this object is set to the value of 0, there is no
             matching policy, and docsDevFilterPolicyTable MUST NOT be
             consulted."
        DEFVAL { 0 }
        ::= { docsDevFilterIpEntry 20 }

--
--

docsDevFilterPolicyTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevFilterPolicyEntry
        MAX-ACCESS  not-accessible





        STATUS      current
        DESCRIPTION
            "A Table which maps between a policy group ID and a set of
             policies to be applied.  All rows with the same
             docsDevFilterPolicyId are part of the same policy group
             and are applied in the order in which they are in this
             table.

             docsDevFilterPolicyTable exists to allow multiple policy
             actions to be applied to any given classified packet. The
             policy actions are applied in index order For example:

             Index   ID    Type    Action
              1      1      TOS     1
              9      5      TOS     1
              12     1      IPSEC   3

             This says that a packet which matches a filter with
             policy id 1, first has TOS policy 1 applied (which might
             set the TOS bits to enable a higher priority), and next
             has the IPSEC policy 3 applied (which may result in the
             packet being dumped into a secure VPN to a remote
             encryptor).

             Policy ID 0 is reserved for default actions and is
             applied only to packets which match no filters in
             docsDevIpFilterTable."
        ::= { docsDevFilter 5 }

docsDevFilterPolicyEntry OBJECT-TYPE
        SYNTAX      DocsDevFilterPolicyEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in the docsDevFilterPolicyTable. Entries are
             created by Network Management. To create an entry,
             docsDevFilterPolicyId and docsDevFilterPolicyAction
             must be specified."
        INDEX { docsDevFilterPolicyIndex }
        ::= { docsDevFilterPolicyTable 1 }

DocsDevFilterPolicyEntry ::= SEQUENCE {
            docsDevFilterPolicyIndex   Integer32,
            docsDevFilterPolicyId      Integer32,
--            docsDevFilterPolicyType    INTEGER,
--            docsDevFilterPolicyAction  Integer32,
            docsDevFilterPolicyStatus  RowStatus,
            docsDevFilterPolicyPtr     RowPointer





        }

docsDevFilterPolicyIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index value for the table."
        ::= { docsDevFilterPolicyEntry 1 }

docsDevFilterPolicyId OBJECT-TYPE
        SYNTAX      Integer32 (0..2147483647)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
             "Policy ID for this entry. A policy ID can apply to
              multiple rows of this table, all relevant policies are
              executed. Policy 0 (if populated) is applied to all
              packets which do not match any of the filters. N.B. If
              docsDevFilterIpPolicyId is set to 0, it DOES NOT match
              policy 0 of this table. "
        ::= { docsDevFilterPolicyEntry 2 }

-- docsDevFilterPolicyType ::= { docsDevFilterPolicyEntry 3} Removed
-- docsDevFilterPolicyAction ::= { docsDevFilterPolicyEntry 4 } removed

docsDevFilterPolicyStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Object used to create an entry in this table."
        ::= { docsDevFilterPolicyEntry 5 }


docsDevFilterPolicyPtr OBJECT-TYPE
        SYNTAX      RowPointer
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object points to a row in an applicable filter policy
             table.  Currently, the only standard policy table is
             docsDevFilterTosTable. Per the textual convention, this
             object points to the first accessible object in the row.
             E.g. to point to a row in docsDevFilterTosTable with an
             index of 21, the value of this object would be the object
             identifier docsDevTosStatus.21.

             Vendors must adhere to the same convention when adding





             vendor specific policy table extensions.

             The default upon row creation is a null pointer which
             results in no policy action being taken."
        DEFVAL { zeroDotZero }
        ::= { docsDevFilterPolicyEntry 6 }

--
-- TOS Policy action table
--

docsDevFilterTosTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevFilterTosEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
             "Table used to describe Type of Service (TOS) bits
             processing.

             This table is an adjunct to the docsDevFilterIpTable, and
             the docsDevFilterPolicy table.  Entries in the latter
             table can point to specific rows in this (and other)
             tables and cause specific actions to be taken.  This table
             permits the manipulation of the value of the Type of
             Service bits in the IP header of the matched packet as
             follows:
             Set the tosBits of the packet to
                (tosBits & docsDevFilterTosAndMask) |
                                                 docsDevFilterTosOrMask

             This construct allows you to do a clear and set of all
             the TOS bits in a flexible manner."
        ::= { docsDevFilter 6 }

docsDevFilterTosEntry OBJECT-TYPE
        SYNTAX      DocsDevFilterTosEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
             "A TOS policy entry."
        INDEX { docsDevFilterTosIndex }
        ::= { docsDevFilterTosTable 1 }

DocsDevFilterTosEntry ::= SEQUENCE {
            docsDevFilterTosIndex   Integer32,
            docsDevFilterTosStatus  RowStatus,
            docsDevFilterTosAndMask OCTET STRING (SIZE (1)),
            docsDevFilterTosOrMask  OCTET STRING (SIZE (1))





        }

docsDevFilterTosIndex OBJECT-TYPE
        SYNTAX      Integer32 (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The unique index for this row.  There are no ordering
             requirements for this table and any valid index may be
             specified."
        ::= { docsDevFilterTosEntry 1 }

docsDevFilterTosStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The object used to create and delete entries in this
             table. A row created by specifying just this object
             results in a row which specifies no change to the TOS
             bits.   A row may be created using either the create-and-go
             or create-and-wait paradigms. There is no restriction on
             the ability to change values in this row while the row is
             active."
        ::= { docsDevFilterTosEntry 2 }

docsDevFilterTosAndMask OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (1))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION

            "This value is bitwise AND'd with the matched  packet's
        TOS bits."
        DEFVAL { 'ff'h }
        ::= { docsDevFilterTosEntry 3 }

docsDevFilterTosOrMask OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (1))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "After bitwise AND'ing with the above bits, the packet's
             TOS bits are bitwise OR'd with these bits."
        DEFVAL { '00'h }
        ::= { docsDevFilterTosEntry 4 }







--
-- CPE IP Management and anti spoofing group.  Only implemented on
-- Cable Modems.
--

docsDevCpe OBJECT IDENTIFIER ::= { docsDevMIBObjects 7}

docsDevCpeEnroll OBJECT-TYPE
        SYNTAX      INTEGER {
            none(1),
            any(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This object controls the population of docsDevFilterCpeTable.
             If set to none, the filters must be set manually.
             If set to any, the CM wiretaps the packets originating
             from the ethernet and enrolls up to docsDevCpeIpMax
             addresses based on the source IP addresses of those
             packets. At initial system startup, default value for this
             object is any(2)."
        ::= { docsDevCpe 1 }

docsDevCpeIpMax OBJECT-TYPE
        SYNTAX      Integer32 (-1..2147483647)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This object controls the maximum number of CPEs allowed to
             connect behind this device. If set to zero, any number of
             CPEs may connect up to the maximum permitted for the device.
             If set to -1, no filtering is done on CPE source addresses,
             and no entries are made in the docsDevFilterCpeTable. If an
             attempt is made to set this to a number greater than that
             permitted for the device, it is set to that maximum.
             At iniitial system startup, default value for this object
             is 1."
        ::= { docsDevCpe 2 }

docsDevCpeTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DocsDevCpeEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This table lists the IP addresses seen (or permitted)  as
             source addresses in packets originating from the customer
             interface on this device. In addition, this table can be





             provisioned with the specific addresses permitted for the
             CPEs via the normal row creation mechanisms."
        ::= { docsDevCpe 3 }

docsDevCpeEntry OBJECT-TYPE
        SYNTAX      DocsDevCpeEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An entry in the docsDevFilterCpeTable. There is one entry
             for each IP CPE seen or provisioned. If docsDevCpeIpMax
             is set to -1, this table is ignored, otherwise: Upon receipt
             of an IP  packet from the customer interface of the CM, the
             source IP address is checked against this table. If the
             address is in the table, packet processing continues.
             If the address is not in the table, but docsDevCpeEnroll
             is set to any and the table size is less than
             docsDevCpeIpMax, the address is added to the table and
             packet processing continues. Otherwise, the packet is
             dropped.

             The filtering actions specified by this table occur after
             any LLC filtering (docsDevFilterLLCTable), but prior
             to any IP filtering (docsDevFilterIpTable,
             docsDevNmAccessTable)."
        INDEX   { docsDevCpeIp }
        ::= {docsDevCpeTable 1 }

DocsDevCpeEntry ::= SEQUENCE {
            docsDevCpeIp      IpAddress,
            docsDevCpeSource  INTEGER,
            docsDevCpeStatus  RowStatus
        }

docsDevCpeIp OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The IP address to which this entry applies."
        ::= { docsDevCpeEntry 1 }

docsDevCpeSource OBJECT-TYPE
        SYNTAX      INTEGER {
            other(1),
            manual(2),
            learned(3)
        }





        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object describes how this entry was created. If the
             value is manual(2), this row was created by a network
             management action (either configuration, or SNMP set).
             If set to learned(3), then it was found via
             looking at the source IP address of a received packet."
        ::= { docsDevCpeEntry 2 }

docsDevCpeStatus OBJECT-TYPE
        SYNTAX  RowStatus
        MAX-ACCESS  read-create
        STATUS  current
        DESCRIPTION
            "Standard object to manipulate rows. To create a row in this
             table, you only need to specify this object. Management
             stations SHOULD use the create-and-go mechanism for
             creating rows in this table."
        ::= { docsDevCpeEntry 3 }

--
-- Placeholder for notifications/traps.
--
docsDevNotification OBJECT IDENTIFIER   ::= { docsDev 2 }


--
-- Conformance definitions
--
docsDevConformance  OBJECT IDENTIFIER   ::= { docsDev 3 }
docsDevGroups       OBJECT IDENTIFIER   ::= { docsDevConformance 1 }
docsDevCompliances  OBJECT IDENTIFIER   ::= { docsDevConformance 2 }

docsDevBasicCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "The compliance statement for MCNS Cable Modems and
             Cable Modem Termination Systems."

MODULE  -- docsDev

-- conditionally mandatory groups

GROUP docsDevBaseGroup
        DESCRIPTION
            "Mandatory in Cable Modems, optional in Cable Modem
             Termination Systems."





GROUP docsDevEventGroup
        DESCRIPTION
            "Mandatory in Cable Modems, optional in Cable Modem
             Termination Systems."

GROUP docsDevFilterGroup
        DESCRIPTION
            "Mandatory in Cable Modems, optional in Cable Modem
             Termination Systems."

GROUP docsDevNmAccessGroup
        DESCRIPTION
            "This group is only implemented in devices which do not
             implement SNMPv3 User Security Model.  It SHOULD NOT be
             implemented by SNMPv3 conformant devices.

             For devices which do not implement SNMPv3 or later, this
             group is Mandatory in Cable Modems and is optional
             in Cable Modem Termination Systems."

GROUP docsDevServerGroup
        DESCRIPTION
            "This group is implemented only in Cable Modems and is
             not implemented in Cable Modem Termination Systems."

GROUP docsDevSoftwareGroup
        DESCRIPTION
            "This group is Mandatory in Cable Modems and  optional in
             Cable Modem Termination Systems."

GROUP docsDevCpeGroup
        DESCRIPTION
            "This group is Mandatory in Cable Modems, and is
             not implemented in Cable Modem Termination Systems.  A
             similar capability for CMTS devices may be proposed later
             after study."

OBJECT docsDevSTPControl
        MIN-ACCESS read-only
        DESCRIPTION
            "It is compliant to implement this object as read-only.
             Devices need only support noStFilterBpdu(2)."

OBJECT docsDevEvReporting
         MIN-ACCESS read-only
         DESCRIPTION
             "It is compliant to implement this object as read-only.
              Devices need only support local(0)."





         ::= { docsDevCompliances 1 }

docsDevBaseGroup OBJECT-GROUP
        OBJECTS {
             docsDevRole,
             docsDevDateTime,
             docsDevResetNow,
             docsDevSerialNumber,
             docsDevSTPControl
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects providing device status and
             control."
        ::= { docsDevGroups 1 }

docsDevNmAccessGroup OBJECT-GROUP
        OBJECTS {
             docsDevNmAccessIp,
             docsDevNmAccessIpMask,
             docsDevNmAccessCommunity,
             docsDevNmAccessControl,
             docsDevNmAccessInterfaces,
             docsDevNmAccessStatus
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects for controlling access to SNMP
             objects."
        ::= { docsDevGroups 2 }

docsDevSoftwareGroup OBJECT-GROUP
        OBJECTS {
            docsDevSwServer,
            docsDevSwFilename,
            docsDevSwAdminStatus,
            docsDevSwOperStatus,
            docsDevSwCurrentVers
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects for controlling software
             downloads."
        ::= { docsDevGroups 3 }

docsDevServerGroup OBJECT-GROUP
        OBJECTS {
            docsDevServerBootState,





            docsDevServerDhcp,
            docsDevServerTime,
            docsDevServerTftp,
            docsDevServerConfigFile
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects providing status about server
             provisioning."
        ::= { docsDevGroups 4 }

docsDevEventGroup OBJECT-GROUP
        OBJECTS {
            docsDevEvControl,
            docsDevEvSyslog,
            docsDevEvThrottleAdminStatus,
            docsDevEvThrottleInhibited,
            docsDevEvThrottleThreshold,
            docsDevEvThrottleInterval,
            docsDevEvReporting,
            docsDevEvFirstTime,
            docsDevEvLastTime,
            docsDevEvCounts,
            docsDevEvLevel,
            docsDevEvId,
            docsDevEvText
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects used to control and monitor
             events."
        ::= { docsDevGroups 5 }

docsDevFilterGroup OBJECT-GROUP
        OBJECTS {
            docsDevFilterLLCUnmatchedAction,
            docsDevFilterIpDefault,
            docsDevFilterLLCStatus,
            docsDevFilterLLCIfIndex,
            docsDevFilterLLCProtocolType,
            docsDevFilterLLCProtocol,
            docsDevFilterLLCMatches,
            docsDevFilterIpControl,
            docsDevFilterIpIfIndex,
            docsDevFilterIpStatus,
            docsDevFilterIpDirection,
            docsDevFilterIpBroadcast,
            docsDevFilterIpSaddr,





            docsDevFilterIpSmask,
            docsDevFilterIpDaddr,
            docsDevFilterIpDmask,
            docsDevFilterIpProtocol,
            docsDevFilterIpSourcePortLow,
            docsDevFilterIpSourcePortHigh,
            docsDevFilterIpDestPortLow,
            docsDevFilterIpDestPortHigh,
            docsDevFilterIpMatches,
            docsDevFilterIpTos,
            docsDevFilterIpTosMask,
            docsDevFilterIpContinue,
            docsDevFilterIpPolicyId,
            docsDevFilterPolicyId,
            docsDevFilterPolicyStatus,
            docsDevFilterPolicyPtr,
            docsDevFilterTosStatus,
            docsDevFilterTosAndMask,
            docsDevFilterTosOrMask
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects to specify filters at link layer
             and IP layer."
        ::= { docsDevGroups 6 }

docsDevCpeGroup OBJECT-GROUP
        OBJECTS {
           docsDevCpeEnroll,
           docsDevCpeIpMax,
           docsDevCpeSource,
           docsDevCpeStatus
        }
        STATUS      current
        DESCRIPTION
            "A collection of objects used to control the number
             and specific values of IP addresses allowed for
             associated Customer Premises Equipment (CPE)."
        ::= { docsDevGroups 7 }

END
