-- *****************************************************************
-- NMS-QOS-PIB-MIB.my: MIB for QOS Policy
--
-- October 2003
--
-- Copyright (c) 2003 by NMS, Inc.
-- All rights reserved.
-- *****************************************************************
--

NMS-QOS-PIB-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    Counter64,
    IpAddress
        FROM SNMPv2-SMI

    MODULE-COMPLIANCE,
    OBJECT-GROUP
        FROM SNMPv2-CONF

    TEXTUAL-CONVENTION,
    DisplayString,
    Mac<PERSON>ddress,
    TruthValue
        FROM SNMPv2-TC

    Unsigned32
        FROM NMS-TC


    nmsPibToMib
        FROM NMS-SMI
    ;

nmsQos<PERSON>BMIB MODULE-IDENTITY
	LAST-UPDATED "200310160000Z"
	ORGANIZATION ""
	CONTACT-INFO
		""
    	DESCRIPTION
            	"The NMS QOS Policy PIB for provisioning QOS policy."
	REVISION        "200310160000Z"
	DESCRIPTION
		"Initial version of this MIB."	
    ::= { nmsPibToMib 1 }

-- New textual conventions
--

-- DiffServ Codepoint
--
Dscp ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer that is in the range of the DiffServ codepoint
        values."
    SYNTAX INTEGER (0..63)

-- Layer 2 CoS
--
QosLayer2Cos ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer that is in the range of the layer 2 CoS values.
        This corresponds to the 802.1p and ISL CoS values."
    SYNTAX INTEGER (0..7)

-- Supported Queues
--
QueueRange ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer that is limited to the number of queues per
        interface supported by the PIB.  Limited to 64 which is the
        number of codepoints."
    SYNTAX INTEGER {
        oneQ(1), twoQ(2), threeQ(3), fourQ(4),
        eightQ(8), sixteenQ(16), thirtyTwoQ(32), sixtyFourQ(64)
        }

-- Supported Thresholds
--
ThresholdSetRange ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer that is limited to the number of threshold sets
        per queue supported by the PIB. A threshold set is a
        collection of parameters describing queue threshold.  The
        parameters of a threshold set depend on the drop mechanism the
        queue implements.  For example, the threshold set for
        tail-drop  comprises a single parameter, the percentage of
        queue size at which dropping occurs.  The threshold set for
        WRED comprises two parameters; within the range of the two
        parameters packets are randomly dropped."
    SYNTAX INTEGER {
        zeroT(0), oneT(1), twoT(2), fourT(4), eightT(8)
        }

-- Percentage for thresholds, etc.
--
Percent ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An integer that is in the range of a percent value."
    SYNTAX INTEGER (0..100)

-- Interface types
--
QosInterfaceQueueType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An enumerated type for all the known interface types.  The
        interface types are currently limited to a predefined
        combination of queues and thresholds such that the product of
        queues and thresholds does not exceed 64 (i.e., the total
        number of DSCPs."
    SYNTAX INTEGER {
        oneQ1t(1),      oneQ2t(2),      oneQ4t(3),       oneQ8t(4),
        twoQ1t(5),      twoQ2t(6),      twoQ4t(7),       twoQ8t(8),
        threeQ1t(9),    threeQ2t(10),   threeQ4t(11),    threeQ8t(12),
        fourQ1t(13),    fourQ2t(14),    fourQ4t(15),     fourQ8t(16),
        eightQ1t(17),   eightQ2t(18),   eightQ4t(19),    eightQ8t(20),
        sixteenQ1t(21), sixteenQ2t(22), sixteenQ4t(23),
        sixtyfourQ1t(24), sixtyfourQ2t(25), sixtyfourQ4t(26),
        oneP1Q0t(27),   oneP1Q4t(28),   oneP1Q8t(29),    oneP2Q1t(30),
        oneP2Q2t(31),   oneP3Q1t(32),   oneP7Q8t(33)
    }

QosInterfaceTypeCapabilities ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An enumeration of interface capabilities.  Used by the PDP to
        select policies and configuration to push to the PEP."
    SYNTAX BITS {
        unspecified (0),

        -- Classification support
        inputL2Classification (1), inputIpClassification (2),
        outputL2Classification (3), outputIpClassification (4),
        inputPortClassification (19), outputPortClassification (20),

        -- Policing support
        inputUflowPolicing (5), inputAggregatePolicing (6),
        outputUflowPolicing (7), outputAggregatePolicing (8),
        policeByMarkingDown (9), policeByDropping (10),
        inputUflowShaping (21), inputAggregateShaping (22),
        outputUflowShaping (23), outputAggregateShaping (24),

        -- Supported scheduling mechanisms
        fifo (11), wrr (12), wfq (13), cq (14), pq (15), cbwfq (16),
        pqWrr (25), pqCbwfq (26),

        -- Supported congestion control mechanisms
        tailDrop (17), wred (18)
  }

-- Role
--
-- This TC is commented out since it is not actually used in this
-- PIB.  Nevertheless, the size and character restrictions must still
-- be enforced
--
-- Role ::= TEXTUAL-CONVENTION
--     STATUS current
--     DESCRIPTION
--         "A display string where valid letters are a-z, A-Z, 0-9,
--         ., - and _.  Name can not start with an '_'.
--     SYNTAX OCTET STRING (SIZE (1..31))

-- Role Combination
--
RoleCombination ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "A Display string consisting of a set of roles concatenated
        with a '+' character where the roles are in lexicographic
        order from minimum to maximum."
    SYNTAX OCTET STRING (SIZE (0..255))

-- Policy Instance Index
--
PolicyInstanceId ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "A textual convention for an attribute that is an an
        unsigned integer index attribute of class.  It is used for
        attributes that exist for the purpose of providing an integer
        index of an instance.

        For any integer index that refers to another policy instance,
        that other policy instance must exist. Furthermore, it is an
        error to try to delete a policy instance that is referred to by
        another instance without first deleting the referring
        instance."
    SYNTAX Unsigned32

-- Unsigned 64 bit integer
--
Unsigned64 ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "An unsigned 64 bit integer. We use SYNTAX Counter64 for the
        enconding rules."
    SYNTAX Counter64

--
-- Object identifier for conformance statements
--

qosPIBConformance  OBJECT IDENTIFIER ::= { nmsQosPIBMIB 1 }

--
-- Device Config.
--

-- This group contains device configuration information.  This
-- configuration is either set by management or reflects the physical
-- configuration of the device.  This configuration is generally
-- reported to the PDP (i.e., the policy server so that the PDP can
-- determine what policies to download to the PEP (i.e., the device).

qosDeviceConfig OBJECT IDENTIFIER ::= { nmsQosPIBMIB 2 }

qosDevicePibIncarnationTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosDevicePibIncarnationEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This class contains a single policy instance that identifies
        the current incarnation of the PIB and the PDP that installed
        this incarnation.  The instance of this class is reported to
        the PDP at client connect time so that the PDP can (attempt
        to) ascertain the current state of the PIB."
    ::= { qosDeviceConfig 1 }

qosDevicePibIncarnationEntry OBJECT-TYPE
    SYNTAX QosDevicePibIncarnationEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The single policy instance of this class identifies the
        current incarnation of the PIB and the PDP that installed
        this incarnation."
    INDEX { qosDeviceIncarnationId }
    ::= { qosDevicePibIncarnationTable 1 }

QosDevicePibIncarnationEntry ::= SEQUENCE {
        qosDeviceIncarnationId   PolicyInstanceId,
        qosDevicePdpName         DisplayString,
        qosDevicePibIncarnation  OCTET STRING,
        qosDevicePibTtl          Unsigned32
    }

qosDeviceIncarnationId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosDevicePibIncarnationEntry 1 }

qosDevicePdpName OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The name of the PDP that installed the current incarnation of
        the PIB into the device.  By default it is the zero length
        string."
        ::= { qosDevicePibIncarnationEntry 2 }

qosDevicePibIncarnation OBJECT-TYPE
    SYNTAX OCTET STRING (SIZE (128))
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An octet string to identify the current incarnation.  It has
        meaning to the PDP that installed the PIB and perhaps its
        standby PDPs. By default the empty string."
        ::= { qosDevicePibIncarnationEntry 3 }

qosDevicePibTtl OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of seconds after a client close or TCP timeout for
        which the PEP continues to enforce the policy in the PIB.
        After this interval, the PIB is consired expired and the
        device no longer enforces the policy installed in the PIB."
        ::= { qosDevicePibIncarnationEntry 4 }

qosDeviceAttributeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosDeviceAttributeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The single instance of this class indicates specific
        attributes of the device.  These include configuration values
        such as the configured PDP addresses, the maximum message
        size, and specific device capabilities.  The latter include
        input port-based and output port-based classification and/or
        policing, support for flow based policing, aggregate based
        policing, traffic shaping capabilities, etc."
    ::= { qosDeviceConfig 2 }

qosDeviceAttributeEntry OBJECT-TYPE
    SYNTAX QosDeviceAttributeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The single instance of this class indicates specific
        attributes of the device."
    INDEX { qosDeviceAttributeId }
    ::= { qosDeviceAttributeTable 1 }

QosDeviceAttributeEntry ::= SEQUENCE {
        qosDeviceAttributeId     PolicyInstanceId,
        qosDevicePepDomain       DisplayString,
        qosDevicePrimaryPdp      IpAddress,
        qosDeviceSecondaryPdp    IpAddress,
        qosDeviceMaxMessageSize  Unsigned32,
        qosDeviceCapabilities    BITS
    }

qosDeviceAttributeId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosDeviceAttributeEntry 1 }

qosDevicePepDomain OBJECT-TYPE
    SYNTAX DisplayString
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The QoS domain that this device belongs to.  This is
        configured locally on the device (perhaps by some management
        protocol such as SNMP).  By default, it is the zero-length
        string."
        ::= { qosDeviceAttributeEntry 2 }

qosDevicePrimaryPdp OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The address of the PDP configured to be the primary PDP for
        the device."
    ::= { qosDeviceAttributeEntry 3 }

qosDeviceSecondaryPdp OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The address of the PDP configured to be the secondary PDP for
        the device.  An address of zero indicates no secondary is
        configured."
    ::= { qosDeviceAttributeEntry 4 }

qosDeviceMaxMessageSize OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum size message that this PEP is capable of
        receiving in bytes.  A value of zero means that the maximum
        message size is unspecified (but does not mean it is
        unlimited).  A message greater than this maximum results in a
        MessageTooBig error on a 'no commit' REP."
    ::= { qosDeviceAttributeEntry 5 }

qosDeviceCapabilities OBJECT-TYPE
    SYNTAX BITS {
        unspecified (0),

        -- QoS labels supported
        layer2Cos (1), ipPrecedence (2), dscp (3)
        }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An enumeration of device capabilities.  Used by the PDP to
        select policies and configuration to push to the PEP."
    ::= { qosDeviceAttributeEntry 6 }

qosInterfaceTypeTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosInterfaceTypeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This class describes the interface types of the interfaces
        that exist on the device.  It includes the queue type, role
        combination and capabilities of interfaces.  The PEP does not
        report which specific interfaces have which characteristics."
    ::= { qosDeviceConfig 3 }

qosInterfaceTypeEntry OBJECT-TYPE
    SYNTAX QosInterfaceTypeEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class describes a role combination for
        an interface type of an interface that exists on the device."
    INDEX { qosInterfaceTypeId }
    ::= { qosInterfaceTypeTable 1 }

QosInterfaceTypeEntry ::= SEQUENCE {
        qosInterfaceTypeId           PolicyInstanceId,
        qosInterfaceQueueType        QosInterfaceQueueType,
        qosInterfaceTypeRoles        RoleCombination,
        qosInterfaceTypeCapabilities QosInterfaceTypeCapabilities
    }

qosInterfaceTypeId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosInterfaceTypeEntry 1 }

qosInterfaceQueueType OBJECT-TYPE
    SYNTAX QosInterfaceQueueType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The interface type in terms of number of queues and
        thresholds."
    ::= { qosInterfaceTypeEntry 2 }

qosInterfaceTypeRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A combination of roles on at least one interface of type
        qosInterfaceType."
    ::= { qosInterfaceTypeEntry 3 }

qosInterfaceTypeCapabilities OBJECT-TYPE
    SYNTAX QosInterfaceTypeCapabilities
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An enumeration of interface capabilities.  Used by the PDP to
        select policies and configuration to push to the PEP."
    ::= { qosInterfaceTypeEntry 4 }

--
-- General Config for the entire domain.
--

-- Table of DiffServ codepoint mappings
-- Maps DSCP to marked-down DSCP, IP precedence and CoS

qosDomainConfig OBJECT IDENTIFIER ::= { nmsQosPIBMIB 3 }

qosDiffServMappingTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosDiffServMappingEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Maps each DSCP to a marked-down DSCP.  Also maps each DSCP to
        an IP precedence and QosLayer2Cos.  When configured for the
        first time, all 64 entries of the table must be
        specified. Thereafter, instances may be modified (with a
        delete and install in a single decision) but not deleted
        unless all instances are deleted."
    ::= { qosDomainConfig 1 }

qosDiffServMappingEntry OBJECT-TYPE
    SYNTAX QosDiffServMappingEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class represents mappings from a DSCP."
    INDEX { qosDscp }
    ::= { qosDiffServMappingTable 1 }

QosDiffServMappingEntry ::= SEQUENCE {
        qosDscp       Dscp,
        qosMarkedDscp Dscp,
        qosL2Cos      QosLayer2Cos
        }

qosDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A DSCP for which this entry contains mappings."
    ::= { qosDiffServMappingEntry 1 }

qosMarkedDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The DSCP to use instead of the qosDscp when the packet is out
        of profile and hence marked as such."
    ::= { qosDiffServMappingEntry 2 }

qosL2Cos OBJECT-TYPE
    SYNTAX QosLayer2Cos
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The L2 CoS value to use when mapping this DSCP to layer 2
        CoS."
    ::= { qosDiffServMappingEntry 3 }

-- Table of Layer 2 CoS to DSCP mappings
--

qosCosToDscpTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosCosToDscpEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Maps each of eight CoS values to a DSCP.  When configured for
        the first time, all 8 entries of the table must be
        specified. Thereafter, instances may be modified (with a
        delete and install in a single decision) but not deleted
        unless all instances are deleted."
    ::= { qosDomainConfig 2 }

qosCosToDscpEntry OBJECT-TYPE
    SYNTAX QosCosToDscpEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class maps a CoS value to a DSCP."
    INDEX { qosCosToDscpCos }
    ::= { qosCosToDscpTable 1 }

QosCosToDscpEntry ::= SEQUENCE {
        qosCosToDscpCos  QosLayer2Cos,
        qosCosToDscpDscp Dscp
        }

qosCosToDscpCos OBJECT-TYPE
    SYNTAX QosLayer2Cos
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The L2 CoS value that is being mapped."
    ::= { qosCosToDscpEntry 1 }

qosCosToDscpDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The DSCP value to use when mapping the L2 CoS to a DSCP."
    ::= { qosCosToDscpEntry 2 }

--
-- The Unmatched Policy Table
--

-- This group specifies the policy to apply to an interface for a
-- given role combination where no other policy matches.  More
-- specifically, the unmatched policy is what is applied to non-IP
-- packets for which there is no MAC classification, or what is
-- applied to IP packets that do not match any ACE in any ACL applied
-- to the interface.

qosUnmatchedPolicy OBJECT IDENTIFIER ::= { nmsQosPIBMIB 4 }

qosUnmatchedPolicyTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosUnmatchedPolicyEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A policy class that specifies what QoS to apply to a packet
        that does not match any other policy configured for this role
        combination for a particular direction of traffic."
    ::= { qosUnmatchedPolicy 1 }

qosUnmatchedPolicyEntry OBJECT-TYPE
    SYNTAX QosUnmatchedPolicyEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the unmatched policy
        for a particular role combination for incoming or outgoing
        traffic."
    INDEX { qosUnmatchedPolicyId }
    ::= { qosUnmatchedPolicyTable 1 }

QosUnmatchedPolicyEntry ::= SEQUENCE {
        qosUnmatchedPolicyId            PolicyInstanceId,
        qosUnmatchedPolicyRole          RoleCombination,
        qosUnmatchedPolicyDirection     INTEGER,
        qosUnmatchedPolicyDscp          Dscp,
        qosUnmatchedPolicyDscpTrusted   TruthValue,
        qosUnmatchPolMicroFlowPolicerId PolicyInstanceId,
        qosUnmatchedPolicyAggregateId   PolicyInstanceId
    }

qosUnmatchedPolicyId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosUnmatchedPolicyEntry 1 }

qosUnmatchedPolicyRole OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Role combination for which this instance applies."
    ::= { qosUnmatchedPolicyEntry 2 }

qosUnmatchedPolicyDirection OBJECT-TYPE
    SYNTAX INTEGER { in(0), out(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The direction of packet flow at the interface in question to
        which this instance applies."
    ::= { qosUnmatchedPolicyEntry 3 }

qosUnmatchedPolicyDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The DSCP to classify the unmatched packet with.  This must be
        specified even if qosUnmatchedPolicyDscpTrusted is true."
    ::= { qosUnmatchedPolicyEntry 4 }

qosUnmatchedPolicyDscpTrusted OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If this attribute is true, then the Dscp associated with the
        packet is trusted, i.e., it is assumed to have already been
        set.  In this case, the Dscp is not rewritten with
        qosUnmatchedPolicyDscp (qosUnmatchedPolicyDscp is ignored)
        unless this is a non-IP packet and arrives untagged.  The
        packet is still policed as part of its micro flow and its
        aggregate flow.

        When a trusted action is applied to an input interface, the
        Dscp (for an IP packet) or CoS (for a non-IP packet)
        associated with the packet is the one contained in the packet.
        When a trusted action is applied to an output interface, the
        Dscp associated with the packet is the one that is the result
        of the input classification and policing."
    ::= { qosUnmatchedPolicyEntry 5 }

qosUnmatchPolMicroFlowPolicerId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index identifying the instance of policer to apply to
        unmatched packets.  It must correspond to the integer index of
        an instance of class qosPolicerTable or be zero.  If zero, the
        microflow is not policed."
    ::= { qosUnmatchedPolicyEntry 6 }

qosUnmatchedPolicyAggregateId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index identifying the aggregate that the packet belongs
        to.  It must correspond to the integer index of an instance of
        class qosAggregateTable or be zero.  If zero, the microflow
        does not belong to any aggregate and is not policed as part of
        any aggregate."
    ::= { qosUnmatchedPolicyEntry 7 }

--
-- The Policer Group
--

-- This group specifies policer parameters that can then be used by
-- other groups such as the IP ACL Actions, or the unmatched policy.
-- This group also defines aggregates that flows can then be assigned
-- to.

qosPolicer OBJECT IDENTIFIER ::= { nmsQosPIBMIB 5 }

-- The Policer Definition Table
--

qosPolicerTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosPolicerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class specifying policing parameters for both microflows
        and aggregate flows.  This table is designed for policing
        according to a token bucket scheme where an average rate and
        burst size is specified."
    ::= { qosPolicer 1 }

qosPolicerEntry OBJECT-TYPE
    SYNTAX QosPolicerEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies a set of policing
        parameters."
    INDEX { qosPolicerId }
    ::= { qosPolicerTable 1 }

QosPolicerEntry ::= SEQUENCE {
        qosPolicerId          PolicyInstanceId,
        qosPolicerRate        Unsigned64,
        qosPolicerNormalBurst Unsigned32,
        qosPolicerExcessBurst Unsigned32,
        qosPolicerAction      INTEGER
    }

qosPolicerId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosPolicerEntry 1 }

qosPolicerRate OBJECT-TYPE
    SYNTAX Unsigned64
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The token rate.  It is specified in units of bit/s. A rate of
        zero means that all packets will be out of profile.  If the
        qosPolicerAction is set to drop then this effectively
        denies any service to packets policed by this policer."
    ::= { qosPolicerEntry 2 }

qosPolicerNormalBurst OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The normal size of a burst in terms of bits."
    ::= { qosPolicerEntry 3 }

qosPolicerExcessBurst OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The excess size of a burst in terms of bits."
    ::= { qosPolicerEntry 4 }

qosPolicerAction OBJECT-TYPE
    SYNTAX INTEGER { drop(0), mark(1), shape(2) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An indication of how to handle out of profile packets.  When
        the shape action is chosen then traffic is shaped to the rate
        specified by qosPolicerRate."
    ::= { qosPolicerEntry 5 }

-- The Aggregate Table
--

qosAggregateTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosAggregateEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Instances of this class identify aggregate flows and the
        policer to apply to each."
    ::= { qosPolicer 2 }

qosAggregateEntry OBJECT-TYPE
    SYNTAX QosAggregateEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the policer to apply to
        an aggregate flow."
    INDEX { qosAggregateId }
    ::= { qosAggregateTable 1 }

QosAggregateEntry ::= SEQUENCE {
        qosAggregateId        PolicyInstanceId,
        qosAggregatePolicerId PolicyInstanceId
    }

qosAggregateId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosAggregateEntry 1 }

qosAggregatePolicerId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index identifying the instance of policer to apply to the
        aggregate.  It must correspond to the integer index of an
        instance of class qosPolicerTable."
    ::= { qosAggregateEntry 2 }

--
-- MAC DA Classification Group
--

-- This group determines the CoS to assign to a MAC frame on the
-- basis of the destination MAC address.  There is no provision for
-- policing or rate limiting at layer 2.

qosMacQos OBJECT IDENTIFIER ::= { nmsQosPIBMIB 6 }

qosMacClassificationTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosMacClassificationEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class of MAC/Vlan tuples and their associated CoS values."
    ::= { qosMacQos 1 }

qosMacClassificationEntry OBJECT-TYPE
    SYNTAX QosMacClassificationEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the mapping of a VLAN
        and a MAC address to a CoS value."
    INDEX { qosMacClassificationId }
    ::= { qosMacClassificationTable 1 }

QosMacClassificationEntry ::= SEQUENCE {
        qosMacClassificationId PolicyInstanceId,
        qosDstMacVlan          INTEGER,
        qosDstMacAddress       MacAddress,
        qosDstMacCos           QosLayer2Cos
    }

qosMacClassificationId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosMacClassificationEntry 1 }

qosDstMacVlan OBJECT-TYPE
    SYNTAX INTEGER (1..4095)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The VLAN of the destination MAC address of the L2 frame."
    ::= { qosMacClassificationEntry 2 }

qosDstMacAddress OBJECT-TYPE
    SYNTAX MacAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The destination MAC address of the L2 frame."
    ::= { qosMacClassificationEntry 3 }

qosDstMacCos OBJECT-TYPE
    SYNTAX QosLayer2Cos
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The CoS to assign the packet with the associated MAC/VLAN
        tuple.  Note that this CoS is overridden by the policies to
        classify the frame at layer 3 if there are any."
    ::= { qosMacClassificationEntry 4 }

--
-- The IP Classification and Policing Group
--

qosIpQos OBJECT IDENTIFIER ::= { nmsQosPIBMIB 7 }

-- The ACE Table
--

qosIpAceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIpAceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "ACE definitions."
    ::= { qosIpQos 1 }

qosIpAceEntry OBJECT-TYPE
    SYNTAX QosIpAceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies an ACE."
    INDEX { qosIpAceId }
    ::= { qosIpAceTable 1 }

QosIpAceEntry ::= SEQUENCE {
        qosIpAceId              PolicyInstanceId,
        qosIpAceDstAddr         IpAddress,
        qosIpAceDstAddrMask     IpAddress,
        qosIpAceSrcAddr         IpAddress,
        qosIpAceSrcAddrMask     IpAddress,
        qosIpAceDscpMin         Dscp,
        qosIpAceDscpMax         Dscp,
        qosIpAceProtocol        INTEGER,
        qosIpAceDstL4PortMin    INTEGER,
        qosIpAceDstL4PortMax    INTEGER,
        qosIpAceSrcL4PortMin    INTEGER,
        qosIpAceSrcL4PortMax    INTEGER,
        qosIpAcePermit          TruthValue
    }

qosIpAceId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIpAceEntry 1 }

qosIpAceDstAddr OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP address to match against the packet's destination IP
        address."
    ::= { qosIpAceEntry 2 }

qosIpAceDstAddrMask OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A mask for the matching of the destination IP address."
    ::= { qosIpAceEntry 3 }

qosIpAceSrcAddr OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP address to match against the packet's source IP
        address."
    ::= { qosIpAceEntry 4 }

qosIpAceSrcAddrMask OBJECT-TYPE
    SYNTAX IpAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "A mask for the matching of the source IP address."
    ::= { qosIpAceEntry 5 }

qosIpAceDscpMin OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum value that the DSCP in the packet can have and
        match this ACE."
    ::= { qosIpAceEntry 6 }

qosIpAceDscpMax OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum value that the DSCP in the packet can have and
        match this ACE."
    ::= { qosIpAceEntry 7 }

qosIpAceProtocol OBJECT-TYPE
    SYNTAX INTEGER (0..255)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The IP protocol to match against the packet's protocol.
        A value of zero means match all."
    ::= { qosIpAceEntry 8 }

qosIpAceDstL4PortMin OBJECT-TYPE
    SYNTAX INTEGER (0..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum value that the packet's layer 4 dest port number
        can have and match this ACE."
    ::= { qosIpAceEntry 9 }

qosIpAceDstL4PortMax OBJECT-TYPE
    SYNTAX INTEGER (0..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum value that the packet's layer 4 dest port number
        can have and match this ACE."
    ::= { qosIpAceEntry 10 }

qosIpAceSrcL4PortMin OBJECT-TYPE
    SYNTAX INTEGER (0..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The minimum value that the packet's layer 4 source port
        number can have and match this ACE."
    ::= { qosIpAceEntry 11 }

qosIpAceSrcL4PortMax OBJECT-TYPE
    SYNTAX INTEGER (0..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum value that the packet's layer 4 source port
        number can have and match this ACE."
    ::= { qosIpAceEntry 12 }

qosIpAcePermit OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If the packet matches this ACE and the value of this attribute
        is true, then the matching process terminates and the QoS
        associated with this ACE (indirectly through the ACL) is
        applied to the packet.  If the value of this attribute is false,
        then no more ACEs in this ACL are compared to this packet and
        matching continues with the first ACE of the next ACL."
    ::= { qosIpAceEntry 13 }

-- The ACL Definition Table
--

qosIpAclDefinitionTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIpAclDefinitionEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class that defines a set of ACLs each being an ordered list
         of ACEs."
    ::= { qosIpQos 2 }

qosIpAclDefinitionEntry OBJECT-TYPE
    SYNTAX QosIpAclDefinitionEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies an ACE in an ACL and its
        order with respect to other ACEs in the same ACL."
    INDEX { qosIpAclDefinitionId }
    ::= { qosIpAclDefinitionTable 1 }

QosIpAclDefinitionEntry ::= SEQUENCE {
        qosIpAclDefinitionId PolicyInstanceId,
        qosIpAclId PolicyInstanceId,
        qosIpAceOrder Unsigned32,
        qosIpAclDefAceId PolicyInstanceId
    }

qosIpAclDefinitionId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIpAclDefinitionEntry 1 }

qosIpAclId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index for this ACL.  There will be one instance of
        policy class qosIpAclDefinition with this integer index for
        each ACE in the ACL per role combination."
    ::= { qosIpAclDefinitionEntry 2 }

qosIpAceOrder OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An integer that determines the position of this ACE in the ACL.
        An ACE with a given order is positioned in the access contol
        list before one with a higher order."
    ::= { qosIpAclDefinitionEntry 3 }

qosIpAclDefAceId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This attribute specifies the ACE in the qosIpAceTable that is
        in the ACL specified by qosIpAclId at the position specified
        by qosIpAceOrder."
    ::= { qosIpAclDefinitionEntry 4 }

-- The ACL Action Table
--

qosIpAclActionTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIpAclActionEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class that applies a set of ACLs to interfaces specifying,
        for each interface the order of the ACL with respect to other
        ACLs applied to the same interface and, for each ACL the
        action to take for a packet that matches a permit ACE in that
        ACL.  Interfaces are specified abstractly in terms of
        interface role combinations."
    ::= { qosIpQos 3 }

qosIpAclActionEntry OBJECT-TYPE
    SYNTAX QosIpAclActionEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class applies an ACL to traffic in a
        particular direction on an interface with a particular role
        combination, and specifies the action for packets which match
        the ACL."
    INDEX { qosIpAclActionId }
    ::= { qosIpAclActionTable 1 }

QosIpAclActionEntry ::= SEQUENCE {
        qosIpAclActionId PolicyInstanceId,
        qosIpAclActAclId PolicyInstanceId,
        qosIpAclInterfaceRoles RoleCombination,
        qosIpAclInterfaceDirection INTEGER,
        qosIpAclOrder Unsigned32,
        qosIpAclDscp Dscp,
        qosIpAclDscpTrusted TruthValue,
        qosIpAclMicroFlowPolicerId PolicyInstanceId,
        qosIpAclAggregateId PolicyInstanceId
    }

qosIpAclActionId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIpAclActionEntry 1 }

qosIpAclActAclId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The ACL associated with this action."
    ::= { qosIpAclActionEntry 2 }

qosIpAclInterfaceRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The interfaces to which this ACL applies specified in terms
        of a set of roles."
    ::= { qosIpAclActionEntry 3 }

qosIpAclInterfaceDirection OBJECT-TYPE
    SYNTAX INTEGER { in(0), out(1) }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The direction of packet flow at the interface in question to
        which this ACL applies."
    ::= { qosIpAclActionEntry 4 }

qosIpAclOrder OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An integer that determines the order of this ACL in the list
        of ACLs applied to interfaces of the specified role
        combination. An ACL with a given order is positioned in the
        list before one with a higher order."
    ::= { qosIpAclActionEntry 5 }

qosIpAclDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The DSCP to classify the packet with in the event that the
        packet matches an ACE in this ACL and the ACE is a permit."
    ::= { qosIpAclActionEntry 6 }

qosIpAclDscpTrusted OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "If this attribute is true, then the Dscp associated with
        the packet is trusted, i.e., it is assumed to have already
        been set.  In this case, the Dscp is not rewritten with
        qosIpAclDscp (qosIpAclDscp is ignored).  The packet is still
        policed as part of its micro flow and its aggregate flow.

        When a trusted action is applied to an input interface, the
        Dscp associated with the packet is the one contained in the
        packet.  When a trusted action is applied to an output
        interface, the Dscp associated with the packet is the one that
        is the result of the input classification and policing."
    ::= { qosIpAclActionEntry 7 }

qosIpAclMicroFlowPolicerId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index identifying the instance of policer to apply to the
        microflow.  It must correspond to the integer index of an
        instance of class qosPolicerTableor be zero.  If zero, the
        microflow is not policed."
    ::= { qosIpAclActionEntry 8 }

qosIpAclAggregateId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An index identifying the aggregate that the packet belongs
        to.  It must correspond to the integer index of an instance of
        class qosAggregateTable or be zero.  If zero, the microflow
        does not belong to any aggregate and is not policed as part of
        any aggregate."
    ::= { qosIpAclActionEntry 9 }

--
-- QoS Interface Group
--

-- This group specifies the configuration of the various interface
-- types including the setting of thresholds, queueing parameters,
-- mapping of DSCPs to queues and thresholds, etc.

qosIfParameters OBJECT IDENTIFIER ::= { nmsQosPIBMIB 8 }

-- Table of scheduling discipline preferences
--

qosIfSchedulingPreferencesTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfSchedulingPreferenceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This class specifies the scheduling preference an interface
        chooses if it supports multiple scheduling types.  Higher
        values are preferred over lower values."
    ::= { qosIfParameters 1 }

qosIfSchedulingPreferenceEntry OBJECT-TYPE
    SYNTAX QosIfSchedulingPreferenceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies a scheduling preference
        for a queue-type on an interface with a particular role
        combination."
    INDEX { qosIfSchedulingPreferenceId }
    ::= { qosIfSchedulingPreferencesTable 1 }

QosIfSchedulingPreferenceEntry ::=  SEQUENCE {
        qosIfSchedulingPreferenceId PolicyInstanceId,
        qosIfSchedulingRoles RoleCombination,
        qosIfSchedulingPreference INTEGER,
        qosIfSchedulingDiscipline INTEGER,
        qosIfSchedulingQueueType QosInterfaceQueueType
    }

qosIfSchedulingPreferenceId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfSchedulingPreferenceEntry 1 }

qosIfSchedulingRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The combination of roles the interface must have for this
        policy instance to apply to that interface."
    ::= { qosIfSchedulingPreferenceEntry 2 }

qosIfSchedulingPreference OBJECT-TYPE
    SYNTAX INTEGER (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The preference to use this scheduling discipline and queue
        type.  A higher value means a higher preference.  If two
        disciplines have the same preference the choice is a local
        decision."
    ::= { qosIfSchedulingPreferenceEntry 3 }

qosIfSchedulingDiscipline OBJECT-TYPE
    SYNTAX INTEGER {
        weightedFairQueueing (1),
        weightedRoundRobin (2),
        customQueueing (3),
        priorityQueueing (4),
        classBasedWFQ (5),
        fifo (6),
        pqWrr (7),
        pqCbwfq (8)
        }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An enumerate type for all the known scheduling disciplines."
    ::= { qosIfSchedulingPreferenceEntry 4 }

qosIfSchedulingQueueType OBJECT-TYPE
    SYNTAX QosInterfaceQueueType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The queue type of this preference."
    ::= { qosIfSchedulingPreferenceEntry 5 }

-- Table of drop mechanism preferences
--

qosIfDropPreferenceTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfDropPreferenceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This class specifies the preference of the drop mechanism an
        interface chooses if it supports multiple drop mechanisms.
        Higher values are preferred over lower values."
    ::= { qosIfParameters 2 }

qosIfDropPreferenceEntry OBJECT-TYPE
    SYNTAX QosIfDropPreferenceEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies a drop preference for
        a drop mechanism on an interface with a particular role
        combination."
    INDEX { qosIfDropPreferenceId }
    ::= { qosIfDropPreferenceTable 1 }

QosIfDropPreferenceEntry ::= SEQUENCE {
        qosIfDropPreferenceId PolicyInstanceId,
        qosIfDropRoles RoleCombination,
        qosIfDropPreference INTEGER,
        qosIfDropDiscipline INTEGER
    }

qosIfDropPreferenceId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfDropPreferenceEntry 1 }

qosIfDropRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The combination of roles the interface must have for this
        policy instance to apply to that interface."
    ::= { qosIfDropPreferenceEntry 2 }

qosIfDropPreference OBJECT-TYPE
    SYNTAX INTEGER (1..16)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The preference to use this drop mechanism.  A higher value
        means a higher preference.  If two mechanisms have the same
        preference the choice is a local decision."
    ::= { qosIfDropPreferenceEntry 3 }

qosIfDropDiscipline OBJECT-TYPE
    SYNTAX INTEGER {
        qosIfDropWRED (1),
        qosIfDropTailDrop (2)
        }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An enumerate type for all the known drop mechanisms."
    ::= { qosIfDropPreferenceEntry 4 }

-- The Assignment of DSCPs to queues and thresholds for each interface
-- type.
--

qosIfDscpAssignmentTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfDscpAssignmentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The assignment of each DSCP to a queue and threshold for each
        interface queue type."
    ::= { qosIfParameters 3 }

qosIfDscpAssignmentEntry OBJECT-TYPE
    SYNTAX QosIfDscpAssignmentEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the queue and threshold
        set for a packet with a particular DSCP on an interface of
        a particular type with a particular role combination."
    INDEX { qosIfDscpAssignmentId }
    ::= { qosIfDscpAssignmentTable 1 }

QosIfDscpAssignmentEntry ::= SEQUENCE {
        qosIfDscpAssignmentId PolicyInstanceId,
        qosIfDscpRoles RoleCombination,
        qosIfQueueType QosInterfaceQueueType,
        qosIfDscp Dscp,
        qosIfQueue INTEGER,
        qosIfThresholdSet INTEGER
        }

qosIfDscpAssignmentId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfDscpAssignmentEntry 1 }

qosIfDscpRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The role combination the interface must be configured with."
    ::= { qosIfDscpAssignmentEntry 2 }

qosIfQueueType OBJECT-TYPE
    SYNTAX QosInterfaceQueueType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The interface queue type to which this row applies."
    ::= { qosIfDscpAssignmentEntry 3 }

qosIfDscp OBJECT-TYPE
    SYNTAX Dscp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The DSCP to which this row applies."
    ::= { qosIfDscpAssignmentEntry 4 }

qosIfQueue OBJECT-TYPE
    SYNTAX INTEGER (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The queue to which the DSCP applies for the given interface
        type."
    ::= { qosIfDscpAssignmentEntry 5 }

qosIfThresholdSet OBJECT-TYPE
    SYNTAX INTEGER (1..8)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold set of the specified queue to which the DSCP
        applies for the given interface type."
    ::= { qosIfDscpAssignmentEntry 6 }

-- The configuration of RED thresholds
--

qosIfRedTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfRedEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class of lower and upper values for each threshold set in a
        queue supporting WRED.  If the size of the queue for a given
        threshold is below the lower value then packets assigned to
        that threshold are always accepted into the queue.  If the
        size of the queue is above upper value then packets are always
        dropped.  If the size of the queue is between the lower and
        the upper then packets are randomly dropped."
    ::= { qosIfParameters 4 }

qosIfRedEntry OBJECT-TYPE
    SYNTAX QosIfRedEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies threshold limits for a
        particular RED threshold of a given threshold set on an
        interface and with a particular role combination."
    INDEX { qosIfRedId }
    ::= { qosIfRedTable 1 }

QosIfRedEntry ::= SEQUENCE {
        qosIfRedId PolicyInstanceId,
        qosIfRedRoles RoleCombination,
        qosIfRedNumThresholdSets ThresholdSetRange,
        qosIfRedThresholdSet INTEGER,
        qosIfRedThresholdSetLower Percent,
        qosIfRedThresholdSetUpper Percent
        }

qosIfRedId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfRedEntry 1 }

qosIfRedRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The role combination the interface must be configured with."
    ::= { qosIfRedEntry 2 }

qosIfRedNumThresholdSets OBJECT-TYPE
    SYNTAX ThresholdSetRange
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The values in this entry apply only to queues with the number
        of thresholds specified by this attribute."
    ::= { qosIfRedEntry 3 }

qosIfRedThresholdSet OBJECT-TYPE
    SYNTAX INTEGER (1..8)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold set to which the lower and upper values apply.
        It must be in the range 1 through qosIfRedNumThresholdSets.
        There must be exactly one PRI for each value in this range."
    ::= { qosIfRedEntry 4 }

qosIfRedThresholdSetLower OBJECT-TYPE
    SYNTAX Percent
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold value below which no packets are dropped."
    ::= { qosIfRedEntry 5 }

qosIfRedThresholdSetUpper OBJECT-TYPE
    SYNTAX Percent
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold value above which all packets are dropped."
    ::= { qosIfRedEntry 6 }

-- The configuration of tail drop thresholds
--

qosIfTailDropTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfTailDropEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class for threshold sets in a queue supporting tail drop.
        If the size of the queue for a given threshold set is at or
        below the specified value then packets assigned to that
        threshold set are always accepted into the queue.  If the size
        of the queue is above the specified value then packets are
        always dropped."
    ::= { qosIfParameters 5 }

qosIfTailDropEntry OBJECT-TYPE
    SYNTAX QosIfTailDropEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the queue depth for a
        particular tail-drop threshold set on an interface with a
        particular role combination."
    INDEX { qosIfTailDropId }
    ::= { qosIfTailDropTable 1 }

QosIfTailDropEntry ::= SEQUENCE {
        qosIfTailDropId PolicyInstanceId,
        qosIfTailDropRoles RoleCombination,
        qosIfTailDropNumThresholdSets ThresholdSetRange,
        qosIfTailDropThresholdSet INTEGER,
        qosIfTailDropThresholdSetValue Percent
        }

qosIfTailDropId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfTailDropEntry 1 }

qosIfTailDropRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The role combination the interface must be configured with."
    ::= { qosIfTailDropEntry 2 }

qosIfTailDropNumThresholdSets OBJECT-TYPE
    SYNTAX ThresholdSetRange
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The value in this entry applies only to queues with the
        number of thresholds specified by this attribute."
    ::= { qosIfTailDropEntry 3 }

qosIfTailDropThresholdSet OBJECT-TYPE
    SYNTAX INTEGER (1..8)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold set to which the threshold value applies"
    ::= { qosIfTailDropEntry 4 }

qosIfTailDropThresholdSetValue OBJECT-TYPE
    SYNTAX Percent
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The threshold value above which packets are dropped."
    ::= { qosIfTailDropEntry 5 }

-- Weights for interfaces that support WRR, WFQ, CBWFQ, etc.
--

qosIfWeightsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF QosIfWeightsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A class of scheduling weights for each queue of an interface
        that supports weighted round robin scheduling or a mix of
        priority queueing and weighted round robin.  For a queue with
        N priority queues, the N highest queue numbers are the
        priority queues with the highest queue number having the
        highest priority.  WRR is applied to the non-priority queues."
    ::= { qosIfParameters 6 }

qosIfWeightsEntry OBJECT-TYPE
    SYNTAX QosIfWeightsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An instance of this class specifies the scheduling weight for
        a particular queue of an interface with a particular number
        of queues and with a particular role combination."
    INDEX { qosIfWeightsId }
    ::= { qosIfWeightsTable 1 }

QosIfWeightsEntry ::= SEQUENCE {
        qosIfWeightsId PolicyInstanceId,
        qosIfWeightsRoles RoleCombination,
        qosIfWeightsNumQueues QueueRange,
        qosIfWeightsQueue INTEGER,
        qosIfWeightsDrainSize Unsigned32,
        qosIfWeightsQueueSize Unsigned32
    }

qosIfWeightsId OBJECT-TYPE
    SYNTAX PolicyInstanceId
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An integer index to identify the instance of the policy class."
    ::= { qosIfWeightsEntry 1 }

qosIfWeightsRoles OBJECT-TYPE
    SYNTAX RoleCombination
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The role combination the interface must be configured with."
    ::= { qosIfWeightsEntry 2 }

qosIfWeightsNumQueues OBJECT-TYPE
    SYNTAX QueueRange
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The value of the weight in this instance applies only to
        interfaces with the number of queues specified by this
        attribute."
    ::= { qosIfWeightsEntry 3 }

qosIfWeightsQueue OBJECT-TYPE
    SYNTAX INTEGER (1..64)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The queue to which the weight applies."
    ::= { qosIfWeightsEntry 4 }

qosIfWeightsDrainSize OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The maximum number of bytes that may be drained from the
        queue in one cycle.  The percentage of the bandwith allocated
        to this queue can be calculated from this attribute and the
        sum of the drain sizes of all the non-priority queues of the
        interface."
    ::= { qosIfWeightsEntry 5 }

qosIfWeightsQueueSize OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The size of the queue in bytes.  Some devices set queue size
        in terms of packets.  These devices must calculate the queue
        size in packets by assuming an average packet size suitable
        for the particular interface.

        Some devices have a fixed size buffer to be shared among all
        queues.  These devices must allocate a fraction of the
        total buffer space to this queue calculated as the the ratio
        of the queue size to the sum of the queue sizes for the
        interface."
    ::= { qosIfWeightsEntry 6 }

qosPIBCompliances OBJECT IDENTIFIER ::= { qosPIBConformance 1 }
qosPIBGroups OBJECT IDENTIFIER ::= { qosPIBConformance 2 }

-- Compliance

qosPIBCompliance MODULE-COMPLIANCE
    STATUS current
    DESCRIPTION
        "The compliance statement for the QOS Policy Derived MIB."
    MODULE        
    MANDATORY-GROUPS {
        qosDevicePibIncarnationTableGroup,
        qosDeviceAttributeTableGroup,
        qosInterfaceTypeTableGroup
    }
    ::= { qosPIBCompliances 1 }

qosDevicePibIncarnationTableGroup OBJECT-GROUP
    OBJECTS {
        qosDevicePdpName,
        qosDevicePibIncarnation,
        qosDevicePibTtl
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 1 }

qosDeviceAttributeTableGroup OBJECT-GROUP
    OBJECTS {
        qosDevicePepDomain,
        qosDevicePrimaryPdp,
        qosDeviceSecondaryPdp,
        qosDeviceMaxMessageSize,
        qosDeviceCapabilities
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 2 }

qosInterfaceTypeTableGroup OBJECT-GROUP
    OBJECTS {
        qosInterfaceQueueType,
        qosInterfaceTypeRoles,
        qosInterfaceTypeCapabilities
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 3 }

qosDiffServMappingTableGroup OBJECT-GROUP
    OBJECTS {
        qosMarkedDscp,
        qosL2Cos
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 4 }

qosCosToDscpTableGroup OBJECT-GROUP
    OBJECTS {
        qosCosToDscpDscp
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 5 }

qosUnmatchedPolicyTableGroup OBJECT-GROUP
    OBJECTS {
        qosUnmatchedPolicyRole,
        qosUnmatchedPolicyDirection,
        qosUnmatchedPolicyDscp,
        qosUnmatchedPolicyDscpTrusted,
        qosUnmatchPolMicroFlowPolicerId,
        qosUnmatchedPolicyAggregateId
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 6 }

qosPolicerTableGroup OBJECT-GROUP
    OBJECTS {
        qosPolicerRate,
        qosPolicerNormalBurst,
        qosPolicerExcessBurst,
        qosPolicerAction
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 7 }

qosAggregateTableGroup OBJECT-GROUP
    OBJECTS {
        qosAggregatePolicerId
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 8 }

qosMacClassificationTableGroup OBJECT-GROUP
    OBJECTS {
        qosDstMacVlan,
        qosDstMacAddress,
        qosDstMacCos
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 9 }

qosIpAceTableGroup OBJECT-GROUP
    OBJECTS {
        qosIpAceDstAddr,
        qosIpAceDstAddrMask,
        qosIpAceSrcAddr,
        qosIpAceSrcAddrMask,
        qosIpAceDscpMin,
        qosIpAceDscpMax,
        qosIpAceProtocol,
        qosIpAceDstL4PortMin,
        qosIpAceDstL4PortMax,
        qosIpAceSrcL4PortMin,
        qosIpAceSrcL4PortMax,
        qosIpAcePermit
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 10 }

qosIpAclDefinitionTableGroup OBJECT-GROUP
    OBJECTS {
        qosIpAclId,
        qosIpAceOrder,
        qosIpAclDefAceId
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 11 }

qosIpAclActionTableGroup OBJECT-GROUP
    OBJECTS {
        qosIpAclActAclId,
        qosIpAclInterfaceRoles,
        qosIpAclInterfaceDirection,
        qosIpAclOrder,
        qosIpAclDscp,
        qosIpAclDscpTrusted,
        qosIpAclMicroFlowPolicerId,
        qosIpAclAggregateId
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 12 }

qosIfSchedulingPreferencesTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfSchedulingRoles,
        qosIfSchedulingPreference,
        qosIfSchedulingDiscipline,
        qosIfSchedulingQueueType
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 13 }

qosIfDropPreferenceTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfDropRoles,
        qosIfDropPreference,
        qosIfDropDiscipline
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 14 }

qosIfDscpAssignmentTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfDscpRoles,
        qosIfQueueType,
        qosIfDscp,
        qosIfQueue,
        qosIfThresholdSet
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 15 }

qosIfRedTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfRedRoles,
        qosIfRedNumThresholdSets,
        qosIfRedThresholdSet,
        qosIfRedThresholdSetLower,
        qosIfRedThresholdSetUpper
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 16 }

qosIfTailDropTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfTailDropRoles,
        qosIfTailDropNumThresholdSets,
        qosIfTailDropThresholdSet,
        qosIfTailDropThresholdSetValue
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 17 }

qosIfWeightsTableGroup OBJECT-GROUP
    OBJECTS {
        qosIfWeightsRoles,
        qosIfWeightsNumQueues,
        qosIfWeightsQueue,
        qosIfWeightsDrainSize,
        qosIfWeightsQueueSize
    }
    STATUS current
    DESCRIPTION
        ""
    ::= { qosPIBGroups 18 }

END
