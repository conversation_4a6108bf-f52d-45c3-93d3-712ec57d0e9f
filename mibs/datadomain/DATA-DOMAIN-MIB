-- **********************************************************************
--
-- Copyright 2004-2009 Data Domain, Inc.
-- All Rights Reserved.
--
-- Management Information Base for Data Domain Products
-- 
-- Data Domain enterprise number is 19746
--
-- The ASN.1 prefix to, and including the Data Domain, Inc.  Enterprise is
--      *******.4.1.19746
--
-- OBJECT IDENTIFIER's 19746.1-99 reserved for DD SNMP modules
--
-- **********************************************************************

DATA-DOMAIN-MIB DEFINITIONS ::=	BEGIN

    IMPORTS
	MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, enterprises, Counter32, Integer32, Counter64
	    FROM SNMPv2-SMI
	MODULE-COMPLIANCE, OB<PERSON>ECT-G<PERSON>UP, NOTIFICATION-GROUP
	    FROM SNMPv2-CONF
        TEXTUAL-CONVENTION, DisplayString
	    FROM SNMPv2-TC
        ifIndex, ifDescr
            FROM IF-MIB;
        
    dataDomainMib MODULE-IDENTITY
        LAST-UPDATED "201705160000Z"  -- 16 may, 2017 
	ORGANIZATION "Data Domain, Inc"
	CONTACT-INFO
	    "Phone: ******-980-4800 
	     Fax:   ******-980-8620"
	DESCRIPTION
	    "This MIB is used for managing the suite of Data Domain Products."
        REVISION "201705160000Z"  -- 16 may, 2017 
        DESCRIPTION
           "Updated mib on 16 may, 2017. Please refer to Release Guide for list of changes."
	::= { enterprises 19746 }

    dataDomainMibConformance	OBJECT IDENTIFIER ::= { dataDomainMib 0 }

    dataDomainMibObjects	OBJECT IDENTIFIER ::= { dataDomainMib 1 }

    dataDomainMibNotifications	OBJECT IDENTIFIER ::= { dataDomainMib 2 }
    
    dataDomainMibProducts	OBJECT IDENTIFIER ::= { dataDomainMib 3 }
    
    dataDomainMibGroups		OBJECT IDENTIFIER ::= { dataDomainMibConformance 2 }
-- temporary placeholder for autogen header file

    environmentals		OBJECT IDENTIFIER ::= { dataDomainMibObjects 1 }

    nvram			OBJECT IDENTIFIER ::= { dataDomainMibObjects 2 }

    fileSystem			OBJECT IDENTIFIER ::= { dataDomainMibObjects 3 }

    alerts			OBJECT IDENTIFIER ::= { dataDomainMibObjects 4 }

    statistics			OBJECT IDENTIFIER ::= { dataDomainMibObjects 5 }

    diskStorage			OBJECT IDENTIFIER ::= { dataDomainMibObjects 6 }

    replication			OBJECT IDENTIFIER ::= { dataDomainMibObjects 8 }

    nfs			OBJECT IDENTIFIER ::= { dataDomainMibObjects 9 }

    cifs		OBJECT IDENTIFIER ::= { dataDomainMibObjects 10 }

    vtl			OBJECT IDENTIFIER ::= { dataDomainMibObjects 11 }

    ddboost			OBJECT IDENTIFIER ::= { dataDomainMibObjects 12 }

    dataDomainSystem		OBJECT IDENTIFIER ::= { dataDomainMibObjects 13 }

    art                         OBJECT IDENTIFIER ::= { dataDomainMibObjects 14 }

    mtree                       OBJECT IDENTIFIER ::= { dataDomainMibObjects 15 }
    
    storage                     OBJECT IDENTIFIER ::= { dataDomainMibObjects 16 }
    
    enclosure                   OBJECT IDENTIFIER ::= { dataDomainMibObjects 17 }

    network                     OBJECT IDENTIFIER ::= { dataDomainMibObjects 18 }

    ddms                OBJECT IDENTIFIER ::= { dataDomainMibObjects 19 }
    
    smt                         OBJECT IDENTIFIER ::= { dataDomainMibObjects 20 }

    quota                       OBJECT IDENTIFIER ::= { dataDomainMibObjects 21 }
    
    highAvailability            OBJECT IDENTIFIER ::= { dataDomainMibObjects 22 }
    
    scsitarget                  OBJECT IDENTIFIER ::= { dataDomainMibObjects 23 }
    -- Generic Type definitions.

    EnclosureID         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
                "unique Enclosure ID, used as index in several tables"
    SYNTAX      Integer32(1..2147483647)
                                        

    Temperature         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
                "Temperature value in Celsius"
    SYNTAX      Integer32

    Minutes             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
                "Minutes"
    SYNTAX      Integer32

    Percentage          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
            "Percentage"
    SYNTAX      Integer32(0..100)

    PercentageStr       ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "20a"
    STATUS current
    DESCRIPTION
                "Percentage string"
        SYNTAX  OCTET STRING  (SIZE (0..20))


    KBytesPerSecond             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of KBytes transferred per second"
    SYNTAX  Counter32

    OpsPerSecond                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of Operations performed per second"
    SYNTAX  Counter32

    ErrorCount          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of Errors Encountered"
    SYNTAX  Counter32

    DDMibTableIndexTC      ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index for Any Data Domain MIB table"
    SYNTAX  Integer32(0..2147483647)

    DDMibTableString32TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 32"
    SYNTAX  OCTET STRING  (SIZE (0..32))

    DDMibTableString64TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 64"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    DDMibTableString128TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "128a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 128"
    SYNTAX  OCTET STRING  (SIZE (0..128))

    DDMibTableString256TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "256a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 256"
    SYNTAX  OCTET STRING  (SIZE (0..256))

    DDMibTableString512TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "512a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 512"
    SYNTAX  OCTET STRING  (SIZE (0..512))

    DDMibTableString1024TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "DD Mib Table String 1024"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    DDMibString96TC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "96a"
    STATUS current
    DESCRIPTION
                "DD Mib String 96"
    SYNTAX  OCTET STRING  (SIZE (0..96))

    DDMibTableSizeGibTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "20a"
    STATUS current
    DESCRIPTION
            "Data Domain Compression size in GiB"
    SYNTAX      OCTET STRING  (SIZE (0..20))

    DDMibTableSizeMiBTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "32a"
    STATUS current
    DESCRIPTION
            "Data Domain size in MiB"
    SYNTAX      OCTET STRING  (SIZE (0..32))


    DDMibDateTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "16a"
    STATUS current
    DESCRIPTION
                "Data Domain Mib Date in the yyyy-mm-dd hh:mm format"
    SYNTAX  OCTET STRING  (SIZE (0..16))

    DDMibMemorySizeTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Data Domain Mib Memory Size of  in bytes."
    SYNTAX  Integer32

    DDMibTimeStampTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "DD Mib Timestamp"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        
    DDMibVersionTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Data Domain Mib version"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        
    DDMibTableEnabledTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "DD Mib Table  Enabled"
    SYNTAX      INTEGER { 
                          no(0),
                          yes(1)
                        }


    DDMibInteger32TC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Data Domain Mib Integer 32."
    SYNTAX  Integer32

    DDMibCompressionFactorTC        ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "20a"
    STATUS current
    DESCRIPTION
                "Data Domain compression factor"
    SYNTAX  OCTET STRING  (SIZE (0..20))

    DDMibAlertSeverityTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Data Domain Alert Severity"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    DDMibTrafficBytesTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "measurement of bytes being transferred"
    SYNTAX  Counter64

    DDMibStatusTC                       ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "DD Mib Status"
    SYNTAX      INTEGER     { 
                                  enabled(1),
                                  disabled(2)
                            }

    -- Specific Type definitions.

    PowerModuleIndexTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of Power Module present in the DDR"
    SYNTAX  Integer32(0..2147483647)

    PowerModuleDescriptionTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Power Module Description"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    PowerModuleStatusTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Power Module Status"
    SYNTAX  INTEGER { 
                        absent (0),
                        ok              (1),
                        failed          (2),
                        faulty          (3),
                        acnone (4),
                        unknown (99)
                     }

    TempSensorIndexTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of Temperature Sensor present in the DDR"
    SYNTAX  Integer32(0..2147483647)

    TempSensorDescriptionTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Temperature Sensor Description"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        
    TempSensorStatusTC          ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Temperature Sensor Status"
    SYNTAX              INTEGER { 
                                        failed          (0),
                                        ok                      (1),
                                        notfound        (2),
                                        overheatWarning (3),
                                        overheatCritical (4)
                                    }

    FanIndexTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Fan Number of fan that is present in DDR"
    SYNTAX  Integer32(0..2147483647)
        
    FanDescriptionTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Fan Description"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    FanLevelTC          ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Fan level"
    SYNTAX      INTEGER { 
                                        unknown (0), 
                                        low             (1),
                                        medium          (2),
                                        high            (3)
                                    }

    FanStatusTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Fan Status"
    SYNTAX      INTEGER { 
                                        notfound        (0), 
                                        ok              (1),
                                        fail            (2)
                                    }

    NvramIndexTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of NVRAM card present in the DDR"
    SYNTAX  Integer32(0..2147483647)

    NvramMemorySizeTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Memory Size of NVRAM in bytes."
    SYNTAX  Integer32

    NvramHCPropertyBytesTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Value of an NVRAM high capacity property in bytes."
    SYNTAX  Counter64

    NvramWindowSizeTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Window Size of NVRAM in bytes."
    SYNTAX  Integer32

    NvramBatteryIndexTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of NVRAM Battery present in the DDR"
    SYNTAX  Integer32(0..2147483647)

    NvramBatteryStatusTC                ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current NVRAM Battery Status
                Status      Description
                --------------------------------
                ok          charged and enabled
                discharged  charging and enabled"
    SYNTAX      INTEGER { 
                           ok              (0),
                           disabled        (1), 
                           discharged      (2),
                           softdisabled (3)
                         }

    DiskIndexTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Disk Number of disk that is present in the DDR"
    SYNTAX  Integer32(0..2147483647)

    DiskModelTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Manufacture & Model Number of disk"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        

    DiskFirmwareVersionTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Firmware version of disk"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        

    DiskSerialNumberTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Serial Number of disk"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        

    DiskCapacityTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "20a"
    STATUS current
    DESCRIPTION
                "Capacity of disk"
    SYNTAX  OCTET STRING  (SIZE (0..20))
                                                        
    DiskStateTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Disk State"
    SYNTAX      INTEGER { 
                                        ok              (1),
                                        unknown         (2),
                                        absent          (3), 
                                        failed          (4),
                                        spare           (5),                                  
                                        available       (6)

                                    }
                                    
    DiskPackTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Pack number for disks of ES60."
    SYNTAX      INTEGER { 
                                        notapplicable	(0),
                                        pack1           (1),
                                        pack2           (2),
                                        pack3           (3),
                                        pack4           (4)

                                    }
                                    
    DiskSectorsPerSecondTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of disk sectors being transferred per second"
    SYNTAX  Counter32

    FileSystemStatusTC          ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current File System Status"
    SYNTAX      INTEGER { 
                                        enabled         (1),
                                        disabled        (2),
                                        running         (3),
                                        unknown         (4),
                                        error           (5),
                                        cleaning       (6)
                                    }

    FileSystemResourceIndexTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Resource Number of file system resource"
    SYNTAX  Integer32(0..2147483647)

    FileSystemResourceNameTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "File System resource name"
    SYNTAX  OCTET STRING  (SIZE (0..64))
        

    FileSystemSpaceUnitTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "20a"
    STATUS current
    DESCRIPTION
                "Units of space in the file System represented in Gigabytes"
    SYNTAX      OCTET STRING  (SIZE (0..20))

    
    FileSystemCompressionSizeTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "20a"
    STATUS current
    DESCRIPTION
            "File Systems Compression size in GiB"
    SYNTAX      OCTET STRING  (SIZE (0..20))


    FileSystemCompressionFactorTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "20a"
    STATUS current
    DESCRIPTION
                "File System compression factor"
    SYNTAX  OCTET STRING  (SIZE (0..20))

    FileSystemCompressionPeriodTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "20a"
    STATUS current
    DESCRIPTION
                "File System compression period: last 24 hours, last 7 days"
    SYNTAX  OCTET STRING  (SIZE (0..20))

    DateTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "16a"
    STATUS current
    DESCRIPTION
                "Date in the yyyy-mm-dd hh:mm format"
    SYNTAX  OCTET STRING  (SIZE (0..16))

    FileSystemOptionsIndexTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
        "Index of File System Option Table Index"
    SYNTAX      Integer32(0..2147483647)

    FileSystemOptionsNameTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "Index of File Systems Option Name "
    SYNTAX      OCTET STRING  (SIZE (0..128))

    FileSystemOptionsValueTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "Index of File Systems Option Value "
    SYNTAX      OCTET STRING  (SIZE (0..128))

    FileSystemCleanIndexTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "File System Clean Index"
    SYNTAX  Integer32(0..2147483647)
        
    FileSystemCleanStatusTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "File System Clean Status "
    SYNTAX      OCTET STRING  (SIZE (0..128))

    FileSystemCleanScheduleTC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "File System Clean Schedule "
    SYNTAX      OCTET STRING  (SIZE (0..128))

    FileSystemCleanThrottleTC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "File System Clean throttle"
    SYNTAX      OCTET STRING  (SIZE (0..128))

    AlertIndexTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Alert Row Index"
    SYNTAX  Integer32(0..2147483647)

    AlertTimestampTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Alert Timestamp"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    AlertDescriptionTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS current
    DESCRIPTION
                "Alert Description"
    SYNTAX  OCTET STRING  (SIZE (0..255))
        
    
    SystemStatsIndexTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Statistics Row Index"
    SYNTAX  Integer32(0..2147483647)

    RaidDiskStateTC             ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Raid Disk State"
    SYNTAX      INTEGER { 
                                        inuse           (1),
                                        notinuse        (2),
                                        spare           (3),
                                        absent          (4),
                                        failed          (5),
                                        invalid (6),
                                        foreign (7),
                                        known   (8),
                                        available (9),
                                        unknown         (99)
                                    }

    ReplicationStateTC          ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Replication status"
    SYNTAX      INTEGER { 
                                        initializing            (1),
                                        normal                  (2),
                                        recovering              (3),
                                        uninitialized           (4)
                                    }

    ReplicationStatusTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Replication connection status"
    SYNTAX      INTEGER { 
                                        connected               (1),
                                        disconnected    (2), 
                                        migrating               (3), 
                                        suspended               (4), 
                                        neverConnected  (5),
                                        idle            (6)
                                    }

    ReplicationConnectTimeTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "timestamp when connection was established, or 0 if there is no Replication connection"
        SYNTAX  Integer32

    ReplicationPathTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "254a"
    STATUS current
    DESCRIPTION
                "source or destination path for replication"
        SYNTAX  OCTET STRING  (SIZE (0..254))
        
    ReplicationTrafficTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "measurement of bytes being replicated"
        SYNTAX  Counter64

    ReplicationThrottleTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "measurement of replication throttle in bps"
        SYNTAX  Integer32

    ReplicationSyncedTimeTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "timestamp when replication source and destination were in sync, or 0 if unknown"
        SYNTAX  Integer32
                                        
    ReplicationContextTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "0 for collection replication contexts, > 0 for directory replication contexts"
        SYNTAX  Integer32(0..2147483647)


    ReplicationConfigIndexTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Replication Config Row Index"
    SYNTAX  Integer32(0..2147483647)


    ReplicationConfigContextIdTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "Replicaton Configuration Context Id"
    SYNTAX  OCTET STRING  (SIZE (0..32))

    ReplicationConfigSourceTC   ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "256a"
    STATUS current
    DESCRIPTION
                "Replicaton Configuration Connection Source"
    SYNTAX  OCTET STRING  (SIZE (0..256))

    ReplicationConfigDestTC     ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "256a"
    STATUS current
    DESCRIPTION
                "Replicaton Configuration Destination"
    SYNTAX  OCTET STRING  (SIZE (0..256))

    ReplicationConfigConnHostTC     ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "256a"
    STATUS current
    DESCRIPTION
                "Replicaton Configuration Connection Host"
    SYNTAX  OCTET STRING  (SIZE (0..256))

    ReplicationConfigConnPortTC                 ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "256a"
    STATUS current
    DESCRIPTION
                "Replicaton Configuration Connection Port"
    SYNTAX  OCTET STRING  (SIZE (0..256))

    ReplicationConfigLowBWOptimTC               ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Replication Low BW Optimization Status"
    SYNTAX      INTEGER { 
                                        disabled        (0),
                                        enabled         (1)
                                    }


    ReplicationConfigEnabledTC          ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Replication Configuration Enabled"
    SYNTAX      INTEGER { 
                                        no              (0),
                                        yes             (1)
                                    }



                                        
    NfsStatusTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current Network File System Status"
    SYNTAX      INTEGER { 
                                        enabled         (1),
                                        disabled        (2)
                                    }

    NfsClientIndexTC            ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of NFS clients"
    SYNTAX  Integer32(0..2147483647)

    NfsClientPathTC             ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "NFS path"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    NfsClientClientsTC          ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "NFS Clients"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    NfsClientOptionsTC          ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "254a"
    STATUS current
    DESCRIPTION
                "NFS options"
    SYNTAX  OCTET STRING  (SIZE (0..254))

    NfsStatsIndexTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of NFS stats"
    SYNTAX  Integer32(0..2147483647)

    NfsStatsExportPointTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "254a"
    STATUS current
    DESCRIPTION
                "NFS export point"
    SYNTAX  OCTET STRING  (SIZE (0..254))

    NfsStatsFilesystemTypeTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "NFS file system type"
    SYNTAX  OCTET STRING  (SIZE (0..32))

    NfsStatsCacheEntryTC        ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of cache entries"
    SYNTAX  Counter32       

    NfsStatsFileHandleLookupTC  ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of file handle lookup"
    SYNTAX  Counter32       

    NfsStatsMaxCacheSizeTC      ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Max cache size"
    SYNTAX  Counter32       

    NfsStatsCurrentOpenStreamsTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of current open streams"
    SYNTAX  Counter32       

    VtlAdminStateTC             ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current VTL administration state"
    SYNTAX      INTEGER { 
                          unknown     (0),
                          enabled         (1),
                          disabled        (2),
                          failed      (3)
                         }

    VtlProcessStateTC           ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current VTL process state"
    SYNTAX      INTEGER { 
                           unknown         (0),
                           stopped         (1),
                           starting        (2),
                           running     (3),
                           timingout   (4),
                           stopping    (5),
                           stuck       (6)
                         }

    VtlLibraryIndexTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of VTL library"
        SYNTAX  Integer32(0..64)        

    VtlLibraryNameTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL library name"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlLibraryVendorTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL library vendor"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlLibraryModelTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL library model"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlLibraryRevisionTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL library revision"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlLibrarySerialTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL library serial"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlLibraryTotalDrivesTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of VTL drives"
    SYNTAX  Counter32       

    VtlLibraryTotalSlotsTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of VTL slots"
    SYNTAX  Counter32       

    VtlLibraryTotalCapsTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Number of VTL caps"
    SYNTAX  Counter32       

    VtlLibraryStatusTC    ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current VTL library status"
        SYNTAX  INTEGER { 
                        unknown (0), 
                        online  (1),
                        offline (2)
                        }

    VtlDriveIndexTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of VTL drive"
    SYNTAX  Integer32(0..512) 

    VtlDriveNameTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive name"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlDriveVendorTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive vendor"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlDriveModelTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive model"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlDriveRevisionTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive revision"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlDriveSerialTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive serial"
        SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlDriveStatusTC    ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current VTL drive status"
        SYNTAX  INTEGER { 
                        unknown (0), 
                        online  (1),
                        offline (2)
                        }

    VtlDriveTapeVolumeTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL drive tape volume"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlPortIndexTC                      ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Port Table Index"
    SYNTAX Integer32(0..10)         

    VtlPortNameTC                       ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Port Name "
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlPortIDTC                         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Port Id"
    SYNTAX  Counter32

    VtlPortModelTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Port MODEL"
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlPortFirmwareTC           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Port Firmware"
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlPortWWNNTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL WWNN Address"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlPortWWPNTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL WWPN Address"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlPortConnectionTypeTC     ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "VTL Port Connection Type"
        SYNTAX  INTEGER { 
                    nPORT           (0), 
                    loop            (1),
                    pointToPoint  (2),
                    fabricLoop     (3),
                    unknown         (4)
                }


    VtlPortSpeedTC              ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "VTL Port Speed "
        SYNTAX  INTEGER { 
                    zeroGBPS       (0), 
                    oneGBPS         (1),
                    twoGBPS        (2),
                    fourGBPS       (3),
                    eightGBPS      (4),
                    unknown         (6)
                }

    VtlPortEnabledTC            ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "VTL Port Enabled"
        SYNTAX  INTEGER {
                    disabled        (0),
                    enabled         (1),
                    unknown         (2)
                }

    
    VtlPortStatusTC             ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "VTL Port State"
        SYNTAX  INTEGER {
                    offline          (0),
                    online           (1),
                    unknown          (2)
                }

        
    VtlTapeIndexTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Tape Table Index"
    SYNTAX  Integer32(0..250000)    

    VtlTapeBarCodeTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "VTL Tape Bar Code "
    SYNTAX  OCTET STRING  (SIZE (0..64))

    VtlTapePoolTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Pool "
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlTapeLocationTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Location "
    SYNTAX  OCTET STRING  (SIZE (0..32))
    
    VtlTapeStateTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape State "
    SYNTAX  OCTET STRING  (SIZE (0..32))
    
    VtlTapeSizeTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Size "
    SYNTAX  OCTET STRING  (SIZE (0..32))
    
    VtlTapeUsedTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Used "
    SYNTAX  OCTET STRING  (SIZE (0..32))
    
    VtlTapeCompTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Compression "
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlTapeModTimeTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Mod Time "
    SYNTAX  OCTET STRING  (SIZE (0..32))

    VtlStatsIndexTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Index"
    SYNTAX  Integer32(0..2147483647)        

    VtlStatsPortTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "32a"
    STATUS current
    DESCRIPTION
                "VTL Tape Bar Code "
        SYNTAX  OCTET STRING  (SIZE (0..32))
    
    VtlStatsConrolCommandsTC        ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Control Commands"
    SYNTAX  Counter64

    VtlStatsWriteCommandsTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table write commands"
    SYNTAX  Counter64

    VtlStatsReadCommandsTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Read Commands"
    SYNTAX  Counter64

    VtlStatsInTC                            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Stats In"
    SYNTAX  Counter64

    VtlStatsOutTC                           ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Stats Out"
    SYNTAX  Counter64

    VtlStatsLinkFailuresTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Link failures"
    SYNTAX  Counter64

    VtlStatsLIPCountTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table LIP count"
    SYNTAX  Counter64

    VtlStatsSyncLossesTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Sync Losses"
    SYNTAX  Counter64

    VtlStatsSignalLossesTC                      ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Signal Losses"
    SYNTAX  Counter64

    VtlStatsPrimSeqProtoErrorsTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Prim Seq Protocol Errors"
    SYNTAX  Counter64

    VtlStatsInvalidTxWordsTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Invalid Tx Words"
    SYNTAX  Counter64

    VtlStatsInvalidCRCsTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "VTL Stats Table Invalid CRCs"
    SYNTAX  Counter64

    CifsStatusTC                ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current CIFS status"
    SYNTAX      INTEGER { 
                                        enabled                      (1),
                    enabledRunning          (2),
                    enabledNotRunning      (3),
                    enabledWindbindNotRun (4),
                                        disabled                     (5)
                                    }

    CifsConfigModeTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "Current CIFS mode"
    SYNTAX  OCTET STRING  (SIZE (0..64))

    CifsConfigWINSServerTC              ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS WINS servers"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsConfigNetBIOSHostnameTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS current
    DESCRIPTION
                "CIFS NetBIOS hostname"
    SYNTAX  OCTET STRING  (SIZE (0..255))

    CifsConfigDomainControllerTC                ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS Domain Controller"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsConfigDNSTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS DNS server"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsConfigGroupNameTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS current
    DESCRIPTION
                "CIFS group name"
    SYNTAX  OCTET STRING  (SIZE (0..255))

    CifsConfigMaxConnectionTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "CIFS configuration maximum connection"
    SYNTAX  Counter32

    CifsConfigMaxOpenFilesPerConnectionTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "CIFS configuration maximum open files per connection"
    SYNTAX  Counter32

    CifsShareIndexTC            ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "Index of CIFS share"
    SYNTAX  Integer32(0..2147483647)

    CifsShareNameTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS current
    DESCRIPTION
                "CIFS share name"
    SYNTAX  OCTET STRING  (SIZE (0..255))

    CifsSharePathTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS share path"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsShareMaxConnectionTC    ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "d"
    STATUS current
    DESCRIPTION
                "CIFS maximum connection"
    SYNTAX  Counter32

    CifsShareClientsTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS share clients"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsShareBrowsingTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current CIFS browsing"
    SYNTAX      INTEGER { 
                                        enabled                      (1),
                                        disabled                     (2)
                                    }

    CifsShareWriteableTC                ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "Current CIFS writeable"
    SYNTAX      INTEGER { 
                                        enabled                      (1),
                                        disabled                     (2)
                                    }

    CifsShareUserTC             ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "CIFS share user"
    SYNTAX  OCTET STRING  (SIZE (0..1024))

    CifsShareCommentTC          ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS current
    DESCRIPTION
                "CIFS share comment"
    SYNTAX  OCTET STRING  (SIZE (0..255))

    CifsStatsSummaryIndexTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
        "Index of Cifs Stats Summary Table"
    SYNTAX      Integer32(0..2147483647)

    CifsStatsDetailsIndexTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
        "Index of CIFS Stats Details Table"
    SYNTAX      Integer32(0..2147483647)

    CifsOptionsIndexTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
        "Index of CIFS Option Table Index"
    SYNTAX Integer32(0..100)
    
    CifsOptionsNameTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "Index of CIFS Option Name "
    SYNTAX      OCTET STRING  (SIZE (0..128))
    
    CifsOptionsValueTC ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "128a"
    STATUS current
    DESCRIPTION
        "Index of CIFS Option Value "
    SYNTAX      OCTET STRING  (SIZE (0..128))
    

    DDboostStatsIndexTC         ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "d"
    STATUS current
    DESCRIPTION
        "Index of ddboost Stats Table"
    SYNTAX      Integer32(0..2147483647)

         
    DDboostStatusTC                     ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "DDboost Status"
    SYNTAX      INTEGER     { 
                                  enabled       (1),
                                  disabled      (2)
                            }

   
    DDboostUserTC               ::= TEXTUAL-CONVENTION
    DISPLAY-HINT        "1024a"
    STATUS current
    DESCRIPTION
            "DDboost user"
    SYNTAX      OCTET STRING  (SIZE (0..1024))

    SystemSerialNumberTC                                ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "128a"
    STATUS current
    DESCRIPTION
                "System Serial Number"
        SYNTAX  OCTET STRING  (SIZE (0..128))

    SystemTimeZoneNameTC                                ::= TEXTUAL-CONVENTION
        DISPLAY-HINT    "64a"
    STATUS current
    DESCRIPTION
                "DDR system's time zone name"
        SYNTAX  OCTET STRING  (SIZE (0..64))

    SystemNotesTC                                       ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "1024a"
    STATUS current
    DESCRIPTION
                "System notes"
    SYNTAX  OCTET STRING  (SIZE (0..1024))


    FileSystemArchiveUnitStateTC                        ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "File System Archive Unit State"
    SYNTAX      INTEGER     { 
                                  new   (1),
                                  target        (2),
                                  sealed   (3)
                            }

    FileSystemArchiveUnitStatusTC                       ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "File System Archive Unit Status"
    SYNTAX      INTEGER     { 
                                  ready (1),
                                  disabled      (2)
                            }

    MtreeListStatusTC                   ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "Mtree List  Status TC"
    SYNTAX      INTEGER     { 
                                  deleted       (1),
                                  readOnly     (2),
                                  readWrite    (3),
                                  replicationDestination  (4),
                                  retentionLockEnabled   (5),
                                  retentionLockDisabled  (6)
                            }
    MtreeRetentionLockStatusTC        ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "Mtree Retention Lock"
    SYNTAX      INTEGER     { 
                                  enabled       (1),
                                  disabled      (2)
                            }

    TenantUnitMgmtUserListUserRoleTC             ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "The role of a Management user of a tenant-unit. It can be 
                tenant-admin (1) or tenant-user (2)."
    SYNTAX      INTEGER     {
                                  tenantAdmin    (1),
                                  tenantUser     (2)
                            }

    TenantUnitMgmtGroupTypeTC             ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "The type of a Management group of a tenant-unit."
    SYNTAX      INTEGER     {
                                  all           (0),
                                  unknown       (1),
				  local         (2),
				  ad            (3),
				  nis           (4),
				  ldap          (5)
                            }

    SmtStatusTC                       ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "Status of the SMT feature components. They may be enabled or 
                disabled."
    SYNTAX      INTEGER     {
                                  disabled(0),
                                  enabled(1)
                            }

    TenantUnitSecurityModeTC            ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "The security mode of a tenant-unit. It can be 
                strict (1) or default (2)."
    SYNTAX      INTEGER     {
                                  strict       	(1),
                                  default       (2)
                            }

    DDStatusTC                       ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "DD feature Status"
    SYNTAX      INTEGER     {
                                  disabled(0),
                                  enabled(1)
                            }

    DdboostAccessClientsEncryStrengthTC         ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "The types of Ddboost access clients encryption strength level"
    SYNTAX      INTEGER     {
                                none            (0),
                                medium          (2),
                                high            (3)
                            }
   
    DdboostAccessClientsAuthModeTC              ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
                "The types of Ddboost access clients authentication mode"
    SYNTAX      INTEGER     {
                                none            (0),
                                oneWay          (1),
                                twoWay          (2),
                                anonymous       (3)
                            }

    HaSystemStatusTC                 ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "The status of the high availabiilty system."
    SYNTAX      INTEGER     {
                                  unknown(0),
                                  highlyAvailable(1),
                                  degraded(2)
                            }

    HaStatusTC                       ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
               "The status of a high availabiilty component."
    SYNTAX      INTEGER     {
                                  unknown(0),
                                  ok(1),
                                  notOk(2),
                                  notApplicable(3)
                            }
  
-- **********************************************************************
--
-- Environmentals 
-- ==============
--
-- dataDomainMib		(*******.4.1.19746)
--   dataDomainMibObjects	(*******.4.1.19746.1)
--	environmentals		(*******.4.1.19746.1.1)
--
-- **********************************************************************

-- **********************************************************************
--
-- Power 
-- ============
--
-- dataDomainMib		(*******.4.1.19746)
--   dataDomainMibObjects	(*******.4.1.19746.1)
--	environmentals		(*******.4.1.19746.1.1)
--	    power			(*******.4.1.19746.1.1.1)
--
-- **********************************************************************

    power	    OBJECT IDENTIFIER ::= { environmentals 1 }

-- **********************************************************************
--
-- powerModules 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	environmentals			(*******.4.1.19746.1.1)
--	    power				(*******.4.1.19746.1.1.1)
--	       powerModules		(*******.4.1.19746.*******)
--
-- **********************************************************************

    powerModules	    OBJECT IDENTIFIER ::= { power 1 }

    powerModuleTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF PowerModuleEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of PowerModuleEntry."
        ::= { powerModules 1 }

    powerModuleEntry OBJECT-TYPE
        SYNTAX  PowerModuleEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "powerModuleTable Row Description" 
        INDEX { powerEnclosureID, powerModuleIndex }
        ::= { powerModuleTable 1 }

    PowerModuleEntry ::= SEQUENCE {
    powerEnclosureID		EnclosureID,
    powerModuleIndex		PowerModuleIndexTC,
    powerModuleDescription  PowerModuleDescriptionTC,
	powerModuleStatus		PowerModuleStatusTC
    }

    powerEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Power Module Enclosure ID"
        ::= { powerModuleEntry 1 }

    powerModuleIndex OBJECT-TYPE
        SYNTAX  PowerModuleIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Power Module index"
        ::= { powerModuleEntry 2 }

    powerModuleDescription OBJECT-TYPE
        SYNTAX  PowerModuleDescriptionTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Power Module Description"
        ::= { powerModuleEntry 3 }

    powerModuleStatus OBJECT-TYPE
        SYNTAX  PowerModuleStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "current enclosure Power Module status"
        ::= { powerModuleEntry 4 }

-- **********************************************************************
--
-- Temperatures 
-- ============
--
-- dataDomainMib		(*******.4.1.19746)
--   dataDomainMibObjects	(*******.4.1.19746.1)
--	environmentals		(*******.4.1.19746.1.1)
--	    temperatures	(*******.4.1.19746.1.1.2)
--
-- **********************************************************************

    temperatures	    OBJECT IDENTIFIER ::= { environmentals 2 }

-- **********************************************************************
--
-- TemperatureSensors 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	environmentals			(*******.4.1.19746.1.1)
--	    temperatures		(*******.4.1.19746.1.1.2)
--	       temperatureSensors	(*******.4.1.19746.*******)
--
-- **********************************************************************

    temperatureSensors	    OBJECT IDENTIFIER ::= { temperatures 1 }

    temperatureSensorTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TemperatureSensorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of TemperatureSensorEntry."
        ::= { temperatureSensors 1 }

    temperatureSensorEntry OBJECT-TYPE
        SYNTAX  TemperatureSensorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "temperatureSensorTable Row Description" 
        INDEX { tempEnclosureID, tempSensorIndex }
        ::= { temperatureSensorTable 1 }

    TemperatureSensorEntry ::= SEQUENCE {
    tempEnclosureID		EnclosureID,
	tempSensorIndex		TempSensorIndexTC,
        tempSensorTrapIndex     TempSensorIndexTC,
	tempSensorDescription	TempSensorDescriptionTC,
	tempSensorCurrentValue	Temperature,
	tempSensorStatus		TempSensorStatusTC
    }

    tempEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Temperature Sensor Enclosure ID"
        ::= { temperatureSensorEntry 1 }

    tempSensorIndex OBJECT-TYPE
        SYNTAX  TempSensorIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Temperature Sensor Number"
        ::= { temperatureSensorEntry 2 }

    tempSensorTrapIndex OBJECT-TYPE
        SYNTAX  TempSensorIndexTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Temperature Sensor Number Used for Traps"
        ::= { temperatureSensorEntry 3 }

    tempSensorDescription OBJECT-TYPE
        SYNTAX  TempSensorDescriptionTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Temperature Sensor Description"
        ::= { temperatureSensorEntry 4 }

    tempSensorCurrentValue OBJECT-TYPE
        SYNTAX  Temperature 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Current Temperature Value of the sensor"
        ::= { temperatureSensorEntry 5 }

    tempSensorStatus OBJECT-TYPE
        SYNTAX  TempSensorStatusTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Current status of the sensor"
        ::= { temperatureSensorEntry 6 }

-- **********************************************************************
--
-- Fans 
-- ====
--
-- dataDomainMib		(*******.4.1.19746)
--   dataDomainMibObjects	(*******.4.1.19746.1)
--	environmentals		(*******.4.1.19746.1.1)
--	    fans		(*******.4.1.19746.1.1.3)
--
-- **********************************************************************

    fans		    OBJECT IDENTIFIER ::= { environmentals 3 }

-- **********************************************************************
--
-- FanProperties
-- =============
--
-- dataDomainMib        	(*******.4.1.19746)
--   dataDomainMibObjects	(*******.4.1.19746.1)
--	environmentals		(*******.4.1.19746.1.1)
--	    fans		(*******.4.1.19746.1.1.3)
--		fanProperties	(*******.4.1.19746.*******)
--
-- **********************************************************************

    fanProperties	    OBJECT IDENTIFIER ::= { fans 1 }

    fanPropertiesTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FanPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of FanPropertiesEntry."
        ::= { fanProperties 1 }

    fanPropertiesEntry OBJECT-TYPE
        SYNTAX  FanPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "fanPropertiesTable Row Description" 
        INDEX { fanEnclosureID, fanIndex }
        ::= { fanPropertiesTable 1 }

    FanPropertiesEntry ::= SEQUENCE {
	fanEnclosureID	EnclosureID,
	fanIndex	FanIndexTC,
        fanTrapIndex	FanIndexTC,
	fanDescription  FanDescriptionTC,
	fanLevel	FanLevelTC,
	fanStatus	FanStatusTC
    }

    fanEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Enclosure ID of fan"
        ::= { fanPropertiesEntry 1 }

    fanIndex OBJECT-TYPE
        SYNTAX  FanIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Fan Number Index of the fan"
        ::= { fanPropertiesEntry 2 }

    fanTrapIndex OBJECT-TYPE
        SYNTAX  FanIndexTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Fan Number trap index, used for trap notifications"
        ::= { fanPropertiesEntry 3 }

    fanDescription OBJECT-TYPE
        SYNTAX  FanDescriptionTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Vendor supplied description of the fan"
        ::= { fanPropertiesEntry 4 }

    fanLevel OBJECT-TYPE
        SYNTAX  FanLevelTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "current run level of fan"
        ::= { fanPropertiesEntry 5 }

    fanStatus OBJECT-TYPE
        SYNTAX  FanStatusTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Current status of the fan"
        ::= { fanPropertiesEntry 6 }

-- **********************************************************************
--
-- NvramBatteries
-- ==============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	nvram				(*******.4.1.19746.1.2)
--	    nvramBatteries		(*******.4.1.19746.1.2.3)
--
-- **********************************************************************

    nvramBatteries	    OBJECT IDENTIFIER ::= { nvram 3 }

    nvramBatteryTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NvramBatteryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NvramBatteryEntry."
        ::= { nvramBatteries 1 }

    nvramBatteryEntry OBJECT-TYPE
        SYNTAX  NvramBatteryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "nvramBatteryTable Row Description" 
        INDEX { nvramBatteriesIndex, nvramBatteryIndex }
        ::= { nvramBatteryTable 1 }

    NvramBatteryEntry ::= SEQUENCE {
	nvramBatteriesIndex   NvramIndexTC,
	nvramBatteryIndex   NvramBatteryIndexTC,
	nvramBatteryStatus  NvramBatteryStatusTC,
	nvramBatteryCharge  Percentage 
    }

    nvramBatteriesIndex OBJECT-TYPE
        SYNTAX  NvramIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NVRAM card index"
        ::= { nvramBatteryEntry 1 }

    nvramBatteryIndex OBJECT-TYPE
        SYNTAX  NvramBatteryIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NVRAM Battery Number"
        ::= { nvramBatteryEntry 2 }

    nvramBatteryStatus OBJECT-TYPE
        SYNTAX  NvramBatteryStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NVRAM Battery Status"
        ::= { nvramBatteryEntry 3 }

    nvramBatteryCharge OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NVRAM Battery charge percentage"
        ::= { nvramBatteryEntry 4 }

-- **********************************************************************
--
-- NvramProperties
-- ===============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	nvram				(*******.4.1.19746.1.2)
--	    nvramProperties		(*******.4.1.19746.1.2.1)
--
-- **********************************************************************

    nvramProperties	    OBJECT IDENTIFIER ::= { nvram 1 }

    nvramPropertiesTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NvramPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NvramPropertiesEntry."
        ::= { nvramProperties 1 }

    nvramPropertiesEntry OBJECT-TYPE
        SYNTAX  NvramPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "nvramPropertiesTable Row Description" 
        INDEX { nvramPropertiesIndex }
        ::= { nvramPropertiesTable 1 }

    NvramPropertiesEntry ::= SEQUENCE {
	nvramPropertiesIndex   NvramIndexTC,
	nvramMemorySize        NvramMemorySizeTC,
	nvramWindowSize        NvramWindowSizeTC,
	nvramHCMemorySize      NvramHCPropertyBytesTC
    }

    nvramPropertiesIndex OBJECT-TYPE
        SYNTAX  NvramIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NVRAM card index"
        ::= { nvramPropertiesEntry 1 }

    nvramMemorySize OBJECT-TYPE
        SYNTAX  NvramMemorySizeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Size of NVRAM Memory in bytes"
        ::= { nvramPropertiesEntry 2 }

    nvramWindowSize OBJECT-TYPE
        SYNTAX  NvramWindowSizeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Window size of NVRAM in bytes"
        ::= { nvramPropertiesEntry 3 }

    nvramHCMemorySize OBJECT-TYPE
        SYNTAX  NvramHCPropertyBytesTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
            "The size of an NVRAM entity's memory in bytes, with support for
             high capacity modules. This entry may be used in place of 
             nvramMemorySize."
        ::= { nvramPropertiesEntry 4 }


-- **********************************************************************
--
-- NvramStats
-- ==========
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	nvram				(*******.4.1.19746.1.2)
--	    nvramStats			(*******.4.1.19746.1.2.2)
--
-- **********************************************************************

    nvramStats		    OBJECT IDENTIFIER ::= { nvram 2 }

    nvramStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NvramStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NvramStatsEntry."
        ::= { nvramStats 1 }

    nvramStatsEntry OBJECT-TYPE
        SYNTAX  NvramStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "nvramStatsTable Row Description" 
        INDEX { nvramStatsIndex }
        ::= { nvramStatsTable 1 }

    NvramStatsEntry ::= SEQUENCE {
	nvramStatsIndex   NvramIndexTC,
	nvramPCIErrorCount  ErrorCount,
	nvramMemoryErrorCount  ErrorCount 
    }

    nvramStatsIndex OBJECT-TYPE
        SYNTAX  NvramIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NVRAM card index"
        ::= { nvramStatsEntry 1 }

    nvramPCIErrorCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Number of PCI Errors Encountered on NVRAM Card"
        ::= { nvramStatsEntry 2 }

    nvramMemoryErrorCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Number of Memory Errors  Encountered on NVRAM Card"
        ::= { nvramStatsEntry 3 }

-- **********************************************************************
--
-- fileSystemArchiveUnit
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     fileSystem                     (*******.4.1.19746.1.3)
--       fileSystemArchiveUnit           (*******.4.1.19746.1.3.6)
--
-- **********************************************************************

    fileSystemArchiveUnit		    OBJECT IDENTIFIER ::= { fileSystem 6 }

    fileSystemArchiveUnitTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FileSystemArchiveUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of fileSystemArchiveUnitEntry."
        ::= { fileSystemArchiveUnit 1 }

    fileSystemArchiveUnitEntry OBJECT-TYPE
        SYNTAX  FileSystemArchiveUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "fileSystemArchiveUnitTable Row Description"
        INDEX { fileSystemArchiveUnitIndex }
        ::= { fileSystemArchiveUnitTable 1 }

    FileSystemArchiveUnitEntry ::= SEQUENCE {
        fileSystemArchiveUnitIndex             DDMibTableIndexTC,
        fileSystemArchiveUnitName              DDMibTableString256TC,
        fileSystemArchiveUnitState             FileSystemArchiveUnitStateTC,
        fileSystemArchiveUnitStatus            FileSystemArchiveUnitStatusTC,
        fileSystemArchiveUnitStartTime         DDMibTimeStampTC,
        fileSystemArchiveUnitEndTime           DDMibTimeStampTC,
        fileSystemArchiveUnitSize              DDMibTableSizeGibTC,
        fileSystemArchiveUnitDiskGroups        DDMibTableString1024TC
    }

    fileSystemArchiveUnitIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit index"
        ::= { fileSystemArchiveUnitEntry 1 }

    fileSystemArchiveUnitName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit Name"
        ::= { fileSystemArchiveUnitEntry 2 }

    fileSystemArchiveUnitState OBJECT-TYPE
        SYNTAX FileSystemArchiveUnitStateTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit State"
        ::= { fileSystemArchiveUnitEntry 3 }

    fileSystemArchiveUnitStatus OBJECT-TYPE
        SYNTAX  FileSystemArchiveUnitStatusTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit Status"
        ::= { fileSystemArchiveUnitEntry 4 }

    fileSystemArchiveUnitStartTime OBJECT-TYPE
        SYNTAX  DDMibTimeStampTC 
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit start Time"
        ::= { fileSystemArchiveUnitEntry 5 }

    fileSystemArchiveUnitEndTime OBJECT-TYPE
        SYNTAX  DDMibTimeStampTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit End Time"
        ::= { fileSystemArchiveUnitEntry 6 }

    fileSystemArchiveUnitSize OBJECT-TYPE
        SYNTAX  DDMibTableSizeGibTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit Size in Gib"
        ::= { fileSystemArchiveUnitEntry 7 }

    fileSystemArchiveUnitDiskGroups OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem ArchiveUnit DiskGroups"
        ::= { fileSystemArchiveUnitEntry 8 }

-- **********************************************************************
--
-- fileSystemClean
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     fileSystem                     (*******.4.1.19746.1.3)
--       fileSystemClean              (*******.4.1.19746.1.3.5)
--
-- **********************************************************************

    fileSystemClean		    OBJECT IDENTIFIER ::= { fileSystem 5 }

    fileSystemCleanTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FileSystemCleanEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of fileSystemCleanEntry."
        ::= { fileSystemClean 1 }

    fileSystemCleanEntry OBJECT-TYPE
        SYNTAX  FileSystemCleanEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "fileSystemCleanTable Row Description"
        INDEX { fileSystemCleanIndex }
        ::= { fileSystemCleanTable 1 }

    FileSystemCleanEntry ::= SEQUENCE {
        fileSystemCleanIndex          FileSystemCleanIndexTC,
        fileSystemCleanStatus           FileSystemCleanStatusTC,
        fileSystemCleanSchedule          FileSystemCleanScheduleTC,
        fileSystemCleanThrottle          FileSystemCleanThrottleTC
    }

    fileSystemCleanIndex OBJECT-TYPE
        SYNTAX  FileSystemCleanIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "fileSystem Clean index"
        ::= { fileSystemCleanEntry 1 }

    fileSystemCleanStatus OBJECT-TYPE
        SYNTAX  FileSystemCleanStatusTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem Clean Status"
        ::= { fileSystemCleanEntry 2 }

    fileSystemCleanSchedule OBJECT-TYPE
        SYNTAX  FileSystemCleanScheduleTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem Clean Schedule"
        ::= { fileSystemCleanEntry 3 }

    fileSystemCleanThrottle OBJECT-TYPE
        SYNTAX  FileSystemCleanThrottleTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem Clean Throttle"
        ::= { fileSystemCleanEntry 4 }

-- **********************************************************************
--
-- FileSystemCompression 
-- ===============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	fileSystem			        (*******.4.1.19746.1.3)
--	    fileSystemCompression   (*******.4.1.19746.1.3.3)
--
-- **********************************************************************

    fileSystemCompression	    OBJECT IDENTIFIER ::= { fileSystem 3 }

    fileSystemCompressionTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FileSystemCompressionEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of FileSystemCompressionEntry."
        ::= { fileSystemCompression 1 }

    fileSystemCompressionEntry OBJECT-TYPE
        SYNTAX  FileSystemCompressionEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "fileSystemCompressionTable Row Description" 
        INDEX { fileSystemCompressionIndex }
        ::= { fileSystemCompressionTable 1 }

    FileSystemCompressionEntry ::= SEQUENCE {
	fileSystemCompressionIndex			FileSystemResourceIndexTC,
	fileSystemCompressionPeriod			FileSystemCompressionPeriodTC,
	fileSystemCompressionStartTime		DateTC,
	fileSystemCompressionEndTime		DateTC,
	fileSystemPreCompressionSize		FileSystemCompressionSizeTC,
	fileSystemPostCompressionSize		FileSystemCompressionSizeTC,
	fileSystemGlobalCompressionFactor	FileSystemCompressionFactorTC,
	fileSystemLocalCompressionFactor	FileSystemCompressionFactorTC,
        fileSystemTotalCompressionFactor        FileSystemCompressionFactorTC,
        fileSystemReductionPercent              Percentage,
        fileSystemReductionPercent1             PercentageStr 
    }

    fileSystemCompressionIndex OBJECT-TYPE
        SYNTAX  FileSystemResourceIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "File system compression index"
        ::= { fileSystemCompressionEntry 1 }

    fileSystemCompressionPeriod OBJECT-TYPE
        SYNTAX  FileSystemCompressionPeriodTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system compression period"
        ::= { fileSystemCompressionEntry 2 }

    fileSystemCompressionStartTime OBJECT-TYPE
        SYNTAX  DateTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system compression start time"
        ::= { fileSystemCompressionEntry 3 }

    fileSystemCompressionEndTime OBJECT-TYPE
        SYNTAX  DateTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system compression end time"
        ::= { fileSystemCompressionEntry 4 }

    fileSystemPreCompressionSize OBJECT-TYPE
        SYNTAX  FileSystemCompressionSizeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Size of the file system pre compression in gigabytes"
        ::= { fileSystemCompressionEntry 5 }

    fileSystemPostCompressionSize OBJECT-TYPE
        SYNTAX  FileSystemCompressionSizeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Size of the file system post compression in gigabytes"
        ::= { fileSystemCompressionEntry 6 }

    fileSystemGlobalCompressionFactor OBJECT-TYPE
        SYNTAX  FileSystemCompressionFactorTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system global compression factor"
        ::= { fileSystemCompressionEntry 7 }

    fileSystemLocalCompressionFactor OBJECT-TYPE
        SYNTAX  FileSystemCompressionFactorTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system local compression factor"
        ::= { fileSystemCompressionEntry 8 }

    fileSystemTotalCompressionFactor OBJECT-TYPE
        SYNTAX  FileSystemCompressionFactorTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system Total compression factor"
        ::= { fileSystemCompressionEntry 9 }

    fileSystemReductionPercent OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  deprecated
        DESCRIPTION
            "File system Reduction Percent"
        ::= { fileSystemCompressionEntry 10 }

    fileSystemReductionPercent1 OBJECT-TYPE
        SYNTAX  PercentageStr 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system Reduction Percent"
        ::= { fileSystemCompressionEntry 11 }

-- **********************************************************************
--
-- fileSystemOptions
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     fileSystem                     (*******.4.1.19746.1.3)
--       fileSystemOptions              (*******.4.1.19746.1.3.4)
--
-- **********************************************************************

    fileSystemOptions		    OBJECT IDENTIFIER ::= { fileSystem 4 }

    fileSystemOptionsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FileSystemOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of fileSystemOptionsEntry."
        ::= { fileSystemOptions 1 }

    fileSystemOptionsEntry OBJECT-TYPE
        SYNTAX  FileSystemOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "fileSystemOptionsTable Row Description"
        INDEX { fileSystemOptionsIndex }
        ::= { fileSystemOptionsTable 1 }

    FileSystemOptionsEntry ::= SEQUENCE {
        fileSystemOptionsIndex          FileSystemOptionsIndexTC,
        fileSystemOptionsName           FileSystemOptionsNameTC,
        fileSystemOptionsValue          FileSystemOptionsValueTC
    }

    fileSystemOptionsIndex OBJECT-TYPE
        SYNTAX  FileSystemOptionsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "fileSystem Options index"
        ::= { fileSystemOptionsEntry 1 }

    fileSystemOptionsName OBJECT-TYPE
        SYNTAX  FileSystemOptionsNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "This object represents the fileSystem Options type in text format.
             Below lists the possible options and their representations.
             --------------------------------   --------------------------
             Option Name                        Representation in text
             --------------------------------   --------------------------
	     Local compression type             gz/gzfast/lz
	     Marker-type                        auto/cv1
	     Report-replica-as-writable         enabled/disabled
	     Low-bw-optim replication support   enabled/disabled
	     Current global compression type    1/9
	     Staging reserve                    % of the total space/disabled
	     Staging clean                      % of staging reserve space
	     Staging delete suspend             % of staging reserve space 
	     --------------------------------   ---------------------------
            "
        ::= { fileSystemOptionsEntry 2 }

    fileSystemOptionsValue OBJECT-TYPE
        SYNTAX  FileSystemOptionsValueTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "fileSystem Options Value"
        ::= { fileSystemOptionsEntry 3 }


-- **********************************************************************
--
-- FileSystemProperties 
-- ====================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	fileSystem			(*******.4.1.19746.1.3)
--	    fileSystemProperties	(*******.4.1.19746.1.3.1)
--
-- **********************************************************************

    fileSystemProperties    OBJECT IDENTIFIER ::= { fileSystem 1 }

    fileSystemStatus OBJECT-TYPE
        SYNTAX  FileSystemStatusTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Status of the file system"
        ::= { fileSystemProperties 1 }

    fileSystemVirtualSpace OBJECT-TYPE
        SYNTAX  FileSystemSpaceUnitTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Amount of Uncompressed data that has been backed up by the system"
        ::= { fileSystemProperties 2 }

   fileSystemUpTime OBJECT-TYPE
        SYNTAX  DDMibTimeStampTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "File System Up time"
        ::= { fileSystemProperties 3 }

   fileSystemStatusMessage OBJECT-TYPE
        SYNTAX   DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "file system status message"
        ::= { fileSystemProperties 4 }



-- **********************************************************************
--
-- FileSystemSpace 
-- ===============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	fileSystem			(*******.4.1.19746.1.3)
--	    fileSystemSpace		(*******.4.1.19746.1.3.2)
--
-- **********************************************************************

    fileSystemSpace	    OBJECT IDENTIFIER ::= { fileSystem 2 }

    fileSystemSpaceTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF FileSystemSpaceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of FileSystemSpaceEntry."
        ::= { fileSystemSpace 1 }

    fileSystemSpaceEntry OBJECT-TYPE
        SYNTAX  FileSystemSpaceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "fileSystemSpaceTable Row Description" 
        INDEX { fileSystemResourceIndex }
        ::= { fileSystemSpaceTable 1 }

    FileSystemSpaceEntry ::= SEQUENCE {
	fileSystemResourceIndex	FileSystemResourceIndexTC,
        fileSystemResourceTrapIndex	FileSystemResourceIndexTC,
	fileSystemResourceName	FileSystemResourceNameTC,
	fileSystemSpaceSize	FileSystemSpaceUnitTC,
	fileSystemSpaceUsed	FileSystemSpaceUnitTC,
	fileSystemSpaceAvail	FileSystemSpaceUnitTC,
	fileSystemPercentUsed		Percentage,
        fileSystemSpaceCleanable    FileSystemSpaceUnitTC,
        fileSystemResourceTier  DDMibTableString128TC

    }

    fileSystemResourceIndex OBJECT-TYPE
        SYNTAX  FileSystemResourceIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "File system resource index"
        ::= { fileSystemSpaceEntry 1 }

    fileSystemResourceTrapIndex OBJECT-TYPE
        SYNTAX  FileSystemResourceIndexTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system resource trap index"
        ::= { fileSystemSpaceEntry 2 }

    fileSystemResourceName OBJECT-TYPE
        SYNTAX  FileSystemResourceNameTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system resource name"
        ::= { fileSystemSpaceEntry 3 }

    fileSystemSpaceSize OBJECT-TYPE
        SYNTAX  FileSystemSpaceUnitTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Size of the file system resource in gigabytes"
        ::= { fileSystemSpaceEntry 4 }

    fileSystemSpaceUsed OBJECT-TYPE
        SYNTAX  FileSystemSpaceUnitTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Amount of used space within the file system resource in gigabytes"
        ::= { fileSystemSpaceEntry 5 }

    fileSystemSpaceAvail OBJECT-TYPE
        SYNTAX  FileSystemSpaceUnitTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Amount of available space within the file system resource in gigabytes"
        ::= { fileSystemSpaceEntry 6 }

    fileSystemPercentUsed OBJECT-TYPE
        SYNTAX  Percentage
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of used space within the file system resource"
        ::= { fileSystemSpaceEntry 7 }

    fileSystemSpaceCleanable OBJECT-TYPE
        SYNTAX  FileSystemSpaceUnitTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Amount of cleanable space within the file system resource"
        ::= { fileSystemSpaceEntry 8 }

    fileSystemResourceTier OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The tier that a resource belongs to, such as active or archive."
        ::= { fileSystemSpaceEntry 9 }

-- **********************************************************************
--
-- CurrentAlerts 
-- =============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	alerts				(*******.4.1.19746.1.4)
--	    currentAlerts		(*******.4.1.19746.1.4.1)
--
-- **********************************************************************

    currentAlerts	    OBJECT IDENTIFIER ::= { alerts 1 }

    currentAlertTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF CurrentAlertEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of CurrentAlertEntry."
        ::= { currentAlerts 1 }

    currentAlertEntry OBJECT-TYPE
        SYNTAX  CurrentAlertEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "currentAlertTable Row Description" 
        INDEX { currentAlertIndex }
        ::= { currentAlertTable 1 }

    CurrentAlertEntry ::= SEQUENCE {
	currentAlertIndex	AlertIndexTC,
	currentAlertTimestamp	AlertTimestampTC,
	currentAlertDescription	AlertDescriptionTC,
        currentAlertSeverity    DDMibAlertSeverityTC,
        currentAlertID          DDMibTableString32TC
    }

    currentAlertIndex OBJECT-TYPE
        SYNTAX  AlertIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Current Alert Row index"
        ::= { currentAlertEntry 1 }

    currentAlertTimestamp OBJECT-TYPE
        SYNTAX  AlertTimestampTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Timestamp of current alert"
        ::= { currentAlertEntry 2 }

    currentAlertDescription OBJECT-TYPE
        SYNTAX  AlertDescriptionTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert Description"
        ::= { currentAlertEntry 3 }

    currentAlertSeverity OBJECT-TYPE
        SYNTAX  DDMibAlertSeverityTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert Severity"
        ::= { currentAlertEntry 4 }

    currentAlertID OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert ID"
        ::= { currentAlertEntry 5 }
-- **********************************************************************
--
-- alertHistory
-- =============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	alerts				(*******.4.1.19746.1.4)
--	    alertHistory		(*******.4.1.19746.1.4.2)
--
-- **********************************************************************
    alertHistory             OBJECT IDENTIFIER ::= { alerts 2 }


    alertHistoryTable    OBJECT-TYPE
        SYNTAX  SEQUENCE OF AlertHistoryEntry 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of Alert History Entries."
        ::= { alertHistory 1 }
    
    alertHistoryEntry OBJECT-TYPE
        SYNTAX  AlertHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "alertHistoryTable Row Description" 
        INDEX { alertHistoryIndex }
        ::= { alertHistoryTable 1 }
    
    AlertHistoryEntry ::= SEQUENCE {
      alertHistoryIndex                    DDMibTableIndexTC,
      alertHistoryTimestamp                DDMibTimeStampTC,
      alertHistoryDescription              DDMibTableString256TC,
      alertHistorySeverity                 DDMibAlertSeverityTC,
      alertHistoryStatus                   DDMibTableString64TC
    }
 
    alertHistoryIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Alert History Row index"
        ::= { alertHistoryEntry 1 }
 
    alertHistoryTimestamp OBJECT-TYPE
        SYNTAX  DDMibTimeStampTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Timestamp of  alert"
        ::= { alertHistoryEntry 2 }
 
    alertHistoryDescription OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert history Description"
        ::= { alertHistoryEntry 3 }

   alertHistorySeverity OBJECT-TYPE
        SYNTAX  DDMibAlertSeverityTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert history Severity"
        ::= { alertHistoryEntry 4 }
    
    alertHistoryStatus OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert history Status (Post or Cleared)"
        ::= { alertHistoryEntry 5 }

-- **********************************************************************
--
-- alertInfo
-- =============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	alerts				(*******.4.1.19746.1.4)
--	    alertInfo   		(*******.4.1.19746.1.4.3)
--
-- **********************************************************************
    alertInfo             OBJECT IDENTIFIER ::= { alerts 3 }


    alertInfoTable    OBJECT-TYPE
        SYNTAX  SEQUENCE OF AlertInfoEntry 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of Alert Information."
        ::= { alertInfo 1 }
    
    alertInfoEntry OBJECT-TYPE
        SYNTAX  AlertInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "alertInfoTable Row Description" 
        INDEX { alertInfoIndex }
        ::= { alertInfoTable 1 }
    
    AlertInfoEntry ::= SEQUENCE {
      alertInfoIndex                    DDMibTableIndexTC,
      alertInfoDescription              DDMibTableString256TC
    }
 
    alertInfoIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Alert Info Row index"
        ::= { alertInfoEntry 1 }
 
    alertInfoDescription OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Alert Info Description"
        ::= { alertInfoEntry 2 }

   
-- **********************************************************************
--
-- SystemStats 
-- ===========
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	statistics			(*******.4.1.19746.1.5)
--	    systemStats			(*******.4.1.19746.1.5.1)
--
-- **********************************************************************

    systemStats		    OBJECT IDENTIFIER ::= { statistics 1 }

    systemStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemStatsEntry."
        ::= { systemStats 1 }

    systemStatsEntry OBJECT-TYPE
        SYNTAX  SystemStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "systemStatsTable Row Description" 
        INDEX { systemStatsIndex }
        ::= { systemStatsTable 1 }

    SystemStatsEntry ::= SEQUENCE {
	systemStatsIndex	    SystemStatsIndexTC,
	cpuAvgPercentageBusy	    Percentage,
	cpuMaxPercentageBusy	    Percentage,
	nfsOpsPerSecond		    OpsPerSecond,
	nfsIdlePercentage	    Percentage,
	nfsProcPercentage	    Percentage,
	nfsSendPercentage	    Percentage,
	nfsReceivePercentage	    Percentage,
	cifsOpsPerSecond	    OpsPerSecond,
	diskReadKBytesPerSecond	    KBytesPerSecond,
	diskWriteKBytesPerSecond    KBytesPerSecond,
	diskBusyPercentage	    Percentage,
	nvramReadKBytesPerSecond    KBytesPerSecond,
	nvramWriteKBytesPerSecond   KBytesPerSecond,
	replInKBytesPerSecond	    KBytesPerSecond,
	replOutKBytesPerSecond	    KBytesPerSecond 
    }

    systemStatsIndex OBJECT-TYPE
        SYNTAX  SystemStatsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "System Stats Row index"
        ::= { systemStatsEntry 1 }

    cpuAvgPercentageBusy OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "CPU Average Percentage Busy"
        ::= { systemStatsEntry 2 }

    cpuMaxPercentageBusy OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "CPU Max Percentage Busy"
        ::= { systemStatsEntry 3 }

    nfsOpsPerSecond OBJECT-TYPE
        SYNTAX  OpsPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of NFS Operations performed per second"
        ::= { systemStatsEntry 4 }

    nfsIdlePercentage OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of time NFS was Idle"
        ::= { systemStatsEntry 5 }

    nfsProcPercentage OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of time NFS was processing"
        ::= { systemStatsEntry 6 }

    nfsSendPercentage OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of time NFS was sending requests"
        ::= { systemStatsEntry 7 }

    nfsReceivePercentage OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of time NFS was receiving requests"
        ::= { systemStatsEntry 8 }

    cifsOpsPerSecond OBJECT-TYPE
        SYNTAX  OpsPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of CIFS Operations performed per second"
        ::= { systemStatsEntry 9 }

    diskReadKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes per second read from disk"
        ::= { systemStatsEntry 10 }

    diskWriteKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes per second written on disk"
        ::= { systemStatsEntry 11 }

    diskBusyPercentage OBJECT-TYPE
        SYNTAX  Percentage 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of Time Disks were busy"
        ::= { systemStatsEntry 12 }

    nvramReadKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes read per second from NVRAM"
        ::= { systemStatsEntry 13 }

    nvramWriteKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes written per second on NVRAM"
        ::= { systemStatsEntry 14 }

    replInKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes per second received for Replication"
        ::= { systemStatsEntry 15 }

    replOutKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of KBytes per second sent for Replication"
        ::= { systemStatsEntry 16 }

-- **********************************************************************
--
-- DiskPerformance
-- ===============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	diskStorage			(*******.4.1.19746.1.6)
--	    diskPerformance		(*******.4.1.19746.1.6.2)
--
-- **********************************************************************

    diskPerformance	    OBJECT IDENTIFIER ::= { diskStorage 2 }

    diskPerformanceTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DiskPerformanceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of DiskPerformanceEntry."
        ::= { diskPerformance 1 }

    diskPerformanceEntry OBJECT-TYPE
        SYNTAX  DiskPerformanceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "diskPerformanceTable Row Description" 
        INDEX { diskPerfEnclosureID, diskPerfIndex }
        ::= { diskPerformanceTable 1 }

    DiskPerformanceEntry ::= SEQUENCE {
    diskPerfEnclosureID		EnclosureID,
	diskPerfIndex	    DiskIndexTC,
	diskSectorsRead	    DiskSectorsPerSecondTC,
	diskSectorsWritten  DiskSectorsPerSecondTC,
	diskTotalKBytes	    KBytesPerSecond,
	diskBusy	    Percentage,
        diskPerfState	    DiskStateTC
    }

    diskPerfEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Enclosure ID of disk"
        ::= { diskPerformanceEntry 1 }

    diskPerfIndex OBJECT-TYPE
        SYNTAX  DiskIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Disk Number"
        ::= { diskPerformanceEntry 2 }

    diskSectorsRead OBJECT-TYPE
        SYNTAX  DiskSectorsPerSecondTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of disk sectors read per second"
        ::= { diskPerformanceEntry 3 }

    diskSectorsWritten OBJECT-TYPE
        SYNTAX  DiskSectorsPerSecondTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of disk sectors written per second"
        ::= { diskPerformanceEntry 4 }

    diskTotalKBytes OBJECT-TYPE
        SYNTAX  KBytesPerSecond
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Total Number of Kilo-bytes read/written per second"
        ::= { diskPerformanceEntry 5 }

    diskBusy OBJECT-TYPE
        SYNTAX  Percentage
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Percentage of time disk is busy"
        ::= { diskPerformanceEntry 6 }

    diskPerfState OBJECT-TYPE
        SYNTAX  DiskStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Current State of the disk"
        ::= { diskPerformanceEntry 7 }

-- **********************************************************************
--
-- DiskProperties
-- ==============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	diskStorage			(*******.4.1.19746.1.6)
--	    diskProperties		(*******.4.1.19746.1.6.1)
--
-- **********************************************************************

    diskProperties	    OBJECT IDENTIFIER ::= { diskStorage 1 }

    diskPropertiesTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DiskPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of DiskPropertiesEntry."
        ::= { diskProperties 1 }

    diskPropertiesEntry OBJECT-TYPE
        SYNTAX  DiskPropertiesEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION "diskPropertiesTable Row Description" 
        INDEX { diskPropEnclosureID, diskPropIndex }
        ::= { diskPropertiesTable 1 }

    DiskPropertiesEntry ::= SEQUENCE {
    diskPropEnclosureID     EnclosureID,
	diskPropIndex	    DiskIndexTC,
        diskPropTrapIndex   DiskIndexTC,
	diskModel	    DiskModelTC,
	diskFirmwareVersion DiskFirmwareVersionTC,
	diskSerialNumber    DiskSerialNumberTC,
	diskCapacity	    DiskCapacityTC,
	diskPropState	    DiskStateTC,
	diskPack            DiskPackTC
    }

    diskPropEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Enclosure ID of disk"
        ::= { diskPropertiesEntry 1 }

    diskPropIndex OBJECT-TYPE
        SYNTAX  DiskIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Disk Number"
        ::= { diskPropertiesEntry 2 }

    diskPropTrapIndex OBJECT-TYPE
        SYNTAX  DiskIndexTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Disk Number for Traps"
        ::= { diskPropertiesEntry 3 }

    diskModel OBJECT-TYPE
        SYNTAX	DiskModelTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
	    "Manufacture and model of the disk"
        ::= { diskPropertiesEntry 4 }

    diskFirmwareVersion OBJECT-TYPE
        SYNTAX	DiskFirmwareVersionTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
	    "Firmware version of the disk"
        ::= { diskPropertiesEntry 5 }

    diskSerialNumber OBJECT-TYPE
        SYNTAX	DiskSerialNumberTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
	    "Serial Number of the disk"
        ::= { diskPropertiesEntry 6 }

    diskCapacity OBJECT-TYPE
        SYNTAX	DiskCapacityTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
	    "Capacity of the disk"
        ::= { diskPropertiesEntry 7 }

    diskPropState OBJECT-TYPE
        SYNTAX	DiskStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION 
	    "Current State of the disk"
        ::= { diskPropertiesEntry 8 }

    diskPack OBJECT-TYPE
        SYNTAX  DiskPackTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Pack information of the disk.
	     Applicable to enclosures with packs such as ES60, and not for ES20 or ES30."
        ::= { diskPropertiesEntry 9 }

-- **********************************************************************
--
-- DiskReliability
-- ===============
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	diskStorage			(*******.4.1.19746.1.6)
--	    diskReliability		(*******.4.1.19746.1.6.3)
--
-- **********************************************************************

    diskReliability	    OBJECT IDENTIFIER ::= { diskStorage 3 }

    diskReliabilityTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DiskReliabilityEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of DiskReliabilityEntry."
        ::= { diskReliability 1 }

    diskReliabilityEntry OBJECT-TYPE
        SYNTAX  DiskReliabilityEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "diskReliabilityTable Row Description" 
        INDEX { diskErrEnclosureID, diskErrIndex }
        ::= { diskReliabilityTable 1 }

    DiskReliabilityEntry ::= SEQUENCE {
    diskErrEnclosureID	EnclosureID,
	diskErrIndex		DiskIndexTC,
        diskErrTrapIndex        DiskIndexTC,
	diskTemperature		Temperature,
	diskTimeoutCount	ErrorCount,
	diskReadFailCount	ErrorCount,
	diskWriteFailCount	ErrorCount,
	diskMiscFailCount	ErrorCount,
	diskOffTrackErrCount	ErrorCount,
	diskSoftEccCount	ErrorCount,
	diskCrcErrCount		ErrorCount,
	diskProbationalCount	ErrorCount,
	diskReallocCount	ErrorCount,
	diskErrState		DiskStateTC 
    }

    diskErrEnclosureID OBJECT-TYPE
        SYNTAX  EnclosureID
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Enclosure ID of disk"
        ::= { diskReliabilityEntry 1 }

    diskErrIndex OBJECT-TYPE
        SYNTAX  DiskIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Disk Number"
        ::= { diskReliabilityEntry 2 }

    diskErrTrapIndex OBJECT-TYPE
        SYNTAX  DiskIndexTC
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "Disk Number for Traps"
        ::= { diskReliabilityEntry 3 }

    diskTemperature OBJECT-TYPE
        SYNTAX  Temperature 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Current Disk Temperature in Celsius"
        ::= { diskReliabilityEntry 4 }

    diskTimeoutCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of command timeouts"
        ::= { diskReliabilityEntry 5 }

    diskReadFailCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of read failures"
        ::= { diskReliabilityEntry 6 }

    diskWriteFailCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of write failures"
        ::= { diskReliabilityEntry 7 }

    diskMiscFailCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of miscellaneous failures"
        ::= { diskReliabilityEntry 8 }

    diskOffTrackErrCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of offtrack errors"
        ::= { diskReliabilityEntry 9 }

    diskSoftEccCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of soft ecc errors"
        ::= { diskReliabilityEntry 10 }

    diskCrcErrCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of crc errors"
        ::= { diskReliabilityEntry 11 }

    diskProbationalCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of probational errors"
        ::= { diskReliabilityEntry 12 }

    diskReallocCount OBJECT-TYPE
        SYNTAX  ErrorCount 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of reallocation errors"
        ::= { diskReliabilityEntry 13 }

    diskErrState OBJECT-TYPE
        SYNTAX  DiskStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Current State of the disk"
        ::= { diskReliabilityEntry 14 }

-- **********************************************************************
--
-- ReplicationConfig 
-- ========
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	replication				(*******.4.1.19746.1.8)
--	    replicationConfig			(*******.4.1.19746.1.8.2)
--
-- **********************************************************************

    replicationConfig		    OBJECT IDENTIFIER ::= { replication 2 }

    replicationConfigTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ReplicationConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of ReplicationConfigEntry."
        ::= { replicationConfig 1 }

    replicationConfigEntry OBJECT-TYPE
        SYNTAX  ReplicationConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "replicationConfigTable Row Description" 
        INDEX { replConfigIndex }
        ::= { replicationConfigTable 1 }

    ReplicationConfigEntry ::= SEQUENCE {
	replConfigIndex		     		ReplicationConfigIndexTC,
	replConfigContextId	  	  	ReplicationConfigContextIdTC,
	replConfigSource			ReplicationConfigSourceTC,
	replConfigDest			        ReplicationConfigDestTC,
	replConfigConnHost		    	ReplicationConfigConnHostTC,
	replConfigConnPort		    	ReplicationConfigConnPortTC, 
	replConfigLowBWOptim 			ReplicationConfigLowBWOptimTC,
	replConfigEnabled			ReplicationConfigEnabledTC,
	replConfigTenantUnit			DDMibString96TC
    }

    replConfigIndex OBJECT-TYPE
        SYNTAX  ReplicationConfigIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ConfigIndex ID of replication source/dest pair"
        ::= { replicationConfigEntry 1 }

    replConfigContextId OBJECT-TYPE
        SYNTAX  ReplicationConfigContextIdTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "ConfigContextId of replication source/dest pair"
        ::= { replicationConfigEntry 2 }

    replConfigSource OBJECT-TYPE
        SYNTAX  ReplicationConfigSourceTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Source for replication source/dest pair connection"
        ::= { replicationConfigEntry 3 }

    replConfigDest OBJECT-TYPE
        SYNTAX  ReplicationConfigDestTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Destination for replication connection"
        ::= { replicationConfigEntry 4 }

    replConfigConnHost OBJECT-TYPE
        SYNTAX  ReplicationConfigConnHostTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Host for a replication connection"
        ::= { replicationConfigEntry 5 }

    replConfigConnPort OBJECT-TYPE
        SYNTAX  ReplicationConfigConnPortTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication Configuration port for the connection"
        ::= { replicationConfigEntry 6 }

    replConfigLowBWOptim OBJECT-TYPE
        SYNTAX  ReplicationConfigLowBWOptimTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication configuration Low BW optim"
        ::= { replicationConfigEntry 7 }

    replConfigEnabled OBJECT-TYPE
        SYNTAX  ReplicationConfigEnabledTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication config Enabled"
        ::= { replicationConfigEntry 8 }

    replConfigTenantUnit OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of the tenant-unit of a particular replication context.
             "
        ::= { replicationConfigEntry 9 }


-- **********************************************************************
--
-- replicationHistory 
-- ========
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	replication				(*******.4.1.19746.1.8)
--	    replicationHistory			(*******.4.1.19746.1.8.3)
--
-- **********************************************************************

    replicationHistory		    OBJECT IDENTIFIER ::= { replication 3 }

    replicationHistoryTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ReplicationHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of replicationHistoryEntry."
        ::= { replicationHistory 1 }

    replicationHistoryEntry OBJECT-TYPE
        SYNTAX  ReplicationHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "replicationHistoryTable Row Description" 
        INDEX { replHistoryContext }
        ::= { replicationHistoryTable 1 }

    ReplicationHistoryEntry ::= SEQUENCE {
	replHistoryContext		     		DDMibTableIndexTC,
	replHistoryDate			  	        DDMibDateTC,
	replHistoryTime			  	        DDMibTimeStampTC,
	replHistoryPreCompWritten		    	DDMibTrafficBytesTC,
	replHistoryPreCompRemaining		        DDMibTrafficBytesTC, 
	replHistoryPreCompressed 			DDMibTrafficBytesTC,
	replHistoryPostFiltered	                        DDMibTrafficBytesTC,
	replHistoryPostLowBwOptim                       DDMibTrafficBytesTC,
	replHistoryPostLocalComp                        DDMibTrafficBytesTC,
        replHistoryBytesNetwork                         DDMibTrafficBytesTC,
	replHistorySyncedAsOfTime			DDMibInteger32TC
    }

    replHistoryContext OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "context ID of replication History source/dest pair"
        ::= { replicationHistoryEntry 1 }

    replHistoryDate OBJECT-TYPE
        SYNTAX  DDMibDateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "replication History Date"
        ::= { replicationHistoryEntry 2 }

    replHistoryTime OBJECT-TYPE
        SYNTAX DDMibTimeStampTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "replication History Time"
        ::= { replicationHistoryEntry 3 }

    replHistoryPreCompWritten OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication History Pre comp bytes written"
        ::= { replicationHistoryEntry 4 }

    replHistoryPreCompRemaining OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication History Byte remaining"
        ::= { replicationHistoryEntry 5 }

    replHistoryPreCompressed OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication History Precompressed bytes"
        ::= { replicationHistoryEntry 6 }

    replHistoryPostFiltered OBJECT-TYPE
        SYNTAX DDMibTrafficBytesTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "replication history post filtered bytes"
        ::= { replicationHistoryEntry 7 }

    replHistoryPostLowBwOptim OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication history Post Low BW Optimum"
        ::= { replicationHistoryEntry 8 }

    replHistoryPostLocalComp OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication History Post Local Comp"
        ::= { replicationHistoryEntry 9 }

    replHistoryBytesNetwork OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Replication History network bytes"
        ::= { replicationHistoryEntry 10 }

    replHistorySyncedAsOfTime OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Time when the source and destination were in sync,
             or 0 if the time is unknown"
        ::= { replicationHistoryEntry 11 }

-- **********************************************************************
--
-- ReplicationInfo 
-- ========
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	replication				(*******.4.1.19746.1.8)
--	    replicationInfo			(*******.4.1.19746.1.8.1)
--
-- Physical replication is represented with a replContext of 0
-- and has its bytes sent and received put in replPostCompBytesSent
-- and replPostCompBytesReceived respectively
-- **********************************************************************

    replicationInfo		    OBJECT IDENTIFIER ::= { replication 1 }

    replicationInfoTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ReplicationInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of ReplicationInfoEntry."
        ::= { replicationInfo 1 }

    replicationInfoEntry OBJECT-TYPE
        SYNTAX  ReplicationInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "replicationInfoTable Row Description" 
        INDEX { replContext }
        ::= { replicationInfoTable 1 }

    ReplicationInfoEntry ::= SEQUENCE {
	replContext		     		ReplicationContextTC,
        replTrapContext		     		ReplicationContextTC,
	replState			  	  	ReplicationStateTC,
	replStatus			  		ReplicationStatusTC,
	replFileSysStatus			FileSystemStatusTC,
	replConnTime		    	ReplicationConnectTimeTC,
	replSource			    	ReplicationPathTC, 
	replDestination 			ReplicationPathTC,
	replPreCompBytesSent	    ReplicationTrafficTC,
	replPostCompBytesSent       ReplicationTrafficTC,
	replPreCompBytesRemaining   ReplicationTrafficTC,
	replPostCompBytesReceived   ReplicationTrafficTC,
	replThrottle				ReplicationThrottleTC,
	replSyncedAsOfTime			ReplicationSyncedTimeTC
    }

    replContext OBJECT-TYPE
        SYNTAX  ReplicationContextTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "context ID of replication source/dest pair"
        ::= { replicationInfoEntry 1 }

    replTrapContext OBJECT-TYPE
        SYNTAX  ReplicationContextTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "context ID of replication source/dest pair for traps"
        ::= { replicationInfoEntry 2 }
        
    replState OBJECT-TYPE
        SYNTAX  ReplicationStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "state of replication source/dest pair"
        ::= { replicationInfoEntry 3 }

    replStatus OBJECT-TYPE
        SYNTAX  ReplicationStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "status of replication source/dest pair connection"
        ::= { replicationInfoEntry 4 }

    replFileSysStatus OBJECT-TYPE
        SYNTAX  FileSystemStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "status of filesystem"
        ::= { replicationInfoEntry 5 }

    replConnTime OBJECT-TYPE
        SYNTAX  ReplicationConnectTimeTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "time of connection established between source and dest,
             or time since disconnect if status is 'disconnected'"
        ::= { replicationInfoEntry 6 }

    replSource OBJECT-TYPE
        SYNTAX  ReplicationPathTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "network path to replication source directory"
        ::= { replicationInfoEntry 7 }

    replDestination OBJECT-TYPE
        SYNTAX  ReplicationPathTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "network path to replication destination directory"
        ::= { replicationInfoEntry 8 }

    replPreCompBytesSent OBJECT-TYPE
        SYNTAX  ReplicationTrafficTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "pre compression bytes sent"
        ::= { replicationInfoEntry 9 }

    replPostCompBytesSent OBJECT-TYPE
        SYNTAX  ReplicationTrafficTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "post compression bytes sent"
        ::= { replicationInfoEntry 10 }

    replPreCompBytesRemaining OBJECT-TYPE
        SYNTAX  ReplicationTrafficTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "pre compression bytes remaining"
        ::= { replicationInfoEntry 11 }

    replPostCompBytesReceived OBJECT-TYPE
        SYNTAX  ReplicationTrafficTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "post compression bytes received"
        ::= { replicationInfoEntry 12 }

    replThrottle OBJECT-TYPE
        SYNTAX  ReplicationThrottleTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "replication throttle in bps -- -1 is disabled, 0 unlimited"
        ::= { replicationInfoEntry 13 }

    replSyncedAsOfTime OBJECT-TYPE
        SYNTAX  ReplicationSyncedTimeTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "time when the source and destination were in sync,
             or 0 if the time is unknown"
        ::= { replicationInfoEntry 14 }

-- **********************************************************************
--
-- replicationPerformance 
-- ========
--
-- dataDomainMib                    (*******.4.1.19746)
--   dataDomainMibObjects           (*******.4.1.19746.1)
--     replication                  (*******.4.1.19746.1.8)
--       replicationPerformance     (*******.4.1.19746.1.8.4)
--
-- **********************************************************************

    replicationPerformance            OBJECT IDENTIFIER ::= { replication 4 }

    replicationPerformanceTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ReplicationPerformanceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of replicationPerformanceEntry."
        ::= { replicationPerformance 1 }

    replicationPerformanceEntry OBJECT-TYPE
        SYNTAX  ReplicationPerformanceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
            "replicationPerformanceTable Row Description" 
        INDEX { replContext }
        ::= { replicationPerformanceTable 1 }

    ReplicationPerformanceEntry ::= SEQUENCE {
        replPerformancePreCompKBPerSec      DDMibInteger32TC,
        replPerformanceNetworkKBPerSec      DDMibInteger32TC,
        replPerformanceStreams              DDMibInteger32TC,
        replPerformanceBusyReading          DDMibInteger32TC, 
        replPerformanceBusyMeta             DDMibInteger32TC,
        replPerformanceWaitingDest          DDMibInteger32TC,
        replPerformanceWaitingNetwork       DDMibInteger32TC
    }

    replPerformancePreCompKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Network (KB/s): The size value before compression is applied. Sometimes referred to as 'logical size.'"
        ::= { replicationPerformanceEntry 1 }

    replPerformanceNetworkKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Network (KB/s): The amount of compressed data transferred over the network per second."
        ::= { replicationPerformanceEntry 2 }

    replPerformanceStreams OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "An internal system resource associated with reads and writes.
             One replication context can use multiple streams for better performance."
        ::= { replicationPerformanceEntry 3 }

    replPerformanceBusyReading OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The time spent reading filesystem data from the local filesystem.
             Typically this number is the second highest number after Network.
             On a deployment with high network bandwidth, Reading may be the largest column."
        ::= { replicationPerformanceEntry 4 }

    replPerformanceBusyMeta OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The time spent on miscellaneous bookkeeping activities and replicating filesystem
            namespace operations. Typically this value is under 50. If this value exceeds 50 on a
            sustained basis, it may indicate an unusual workload (a large number of file attribute
            updates, for example)."
        ::= { replicationPerformanceEntry 5 }

    replPerformanceWaitingDest OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The time spent waiting due to the receiver not providing the sender enough information
            on what data to send. Typically this value is low. Exceptions include systems on
            high-speed networks where the sender is a more powerful Data Domain system than the
            replica, or where the replica has a higher workload than the sender because the replica
            is the destination for a multiple replication contexts."
        ::= { replicationPerformanceEntry 6 }

    replPerformanceWaitingNetwork OBJECT-TYPE
        SYNTAX DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The time spent waiting due to the receiver not providing the sender enough information
            on what data to send. Typically this value is low. Exceptions include systems on
            high-speed networks where the sender is a more powerful Data Domain system than the
            replica, or where the replica has a higher workload than the sender because the replica
            is the destination for a multiple replication contexts."
        ::= { replicationPerformanceEntry 7 }

-- **********************************************************************
--
-- nfsActive
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     nfs                      (*******.4.1.19746.1.9)
--      nfsActive               (*******.4.1.19746.1.9.2)
--
-- **********************************************************************

    nfsActive		    OBJECT IDENTIFIER ::= { nfs 4 }

    nfsActiveTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NfsActiveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of Nfs Active Clients."
        ::= { nfsActive 1 }

    nfsActiveEntry OBJECT-TYPE
        SYNTAX  NfsActiveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "nfsActiveTable Row Description"
        INDEX { nfsActiveIndex }
        ::= { nfsActiveTable 1 }

    NfsActiveEntry ::= SEQUENCE {
        nfsActiveIndex          DDMibTableIndexTC,
        nfsActivePath           DDMibTableString1024TC,
        nfsActiveClients        DDMibTableString1024TC
    }

    nfsActiveIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NFS Active index"
        ::= { nfsActiveEntry 1 }

    nfsActivePath OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS Active client path"
        ::= { nfsActiveEntry 2 }

    nfsActiveClients OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS Active Client"
        ::= { nfsActiveEntry 3 }


-- **********************************************************************
--
-- nfsClient
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     nfs                      (*******.4.1.19746.1.9)
--      nfsClient               (*******.4.1.19746.1.9.2)
--
-- **********************************************************************

    nfsClient		    OBJECT IDENTIFIER ::= { nfs 2 }

    nfsClientTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NfsClientEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NfsClientEntry."
        ::= { nfsClient 1 }

    nfsClientEntry OBJECT-TYPE
        SYNTAX  NfsClientEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "nfsClientTable Row Description"
        INDEX { nfsClientIndex }
        ::= { nfsClientTable 1 }

    NfsClientEntry ::= SEQUENCE {
        nfsClientIndex NfsClientIndexTC,
        nfsClientPath  NfsClientPathTC,
        nfsClientClients NfsClientClientsTC,
        nfsClientOptions NfsClientOptionsTC
    }

    nfsClientIndex OBJECT-TYPE
        SYNTAX  NfsClientIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NFS client index"
        ::= { nfsClientEntry 1 }

    nfsClientPath OBJECT-TYPE
        SYNTAX  NfsClientPathTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS client path"
        ::= { nfsClientEntry 2 }

    nfsClientClients OBJECT-TYPE
        SYNTAX  NfsClientClientsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "List of NFS clients"
        ::= { nfsClientEntry 3 }

    nfsClientOptions OBJECT-TYPE
        SYNTAX  NfsClientOptionsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS client's options"
        ::= { nfsClientEntry 4 }

-- **********************************************************************
--
-- nfsPort
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     nfs                      (*******.4.1.19746.1.9)
--      nfsPort                 (*******.4.1.19746.1.9.5)
--
-- **********************************************************************

    nfsPort		    OBJECT IDENTIFIER ::= { nfs 5 }

    nfsPortTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NfsPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NfsPortEntry."
        ::= { nfsPort 1 }

    nfsPortEntry OBJECT-TYPE
        SYNTAX  NfsPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "nfsPortTable Row Description"
        INDEX { nfsPortIndex }
        ::= { nfsPortTable 1 }

    NfsPortEntry ::= SEQUENCE {
        nfsPortIndex            DDMibTableIndexTC,
        nfsPortService          DDMibTableString32TC,
        nfsPortPort             DDMibTableString32TC
    }

    nfsPortIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NFS Port index"
        ::= { nfsPortEntry 1 }

    nfsPortService OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS Port Service Name"
        ::= { nfsPortEntry 2 }

    nfsPortPort OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "NFS Service Port"
        ::= { nfsPortEntry 3 }


-- **********************************************************************
--
-- nfsProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     nfs                      (*******.4.1.19746.1.9)
--      nfsProperties           (*******.4.1.19746.1.9.1)
--
-- **********************************************************************

    nfsProperties		    OBJECT IDENTIFIER ::= { nfs 1 }

    nfsStatus OBJECT-TYPE
        SYNTAX  NfsStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Status of the network file system"
        ::= { nfsProperties 1 }

-- **********************************************************************
--
-- nfsStats 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     nfs                      (*******.4.1.19746.1.9)
--      nfsStats                (*******.4.1.19746.1.9.3)
--
-- **********************************************************************

    nfsStats		    OBJECT IDENTIFIER ::= { nfs 3 }

    nfsStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF NfsStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of NfsStatsEntry."
        ::= { nfsStats 1 }

    nfsStatsEntry OBJECT-TYPE
        SYNTAX  NfsStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "nfsStatsTable Row Description"
        INDEX { nfsStatsIndex }
        ::= { nfsStatsTable 1 }

    NfsStatsEntry ::= SEQUENCE {
        nfsStatsIndex NfsStatsIndexTC,
	    nfsStatsExportPoint NfsStatsExportPointTC,
	    nfsStatsFilesystemType NfsStatsFilesystemTypeTC,
	    nfsStatsCacheEntry NfsStatsCacheEntryTC,
	    nfsStatsFileHandleLookup NfsStatsFileHandleLookupTC,
	    nfsStatsMaxCacheSize NfsStatsMaxCacheSizeTC,
	    nfsStatsCurrentOpenStreams NfsStatsCurrentOpenStreamsTC
    }

    nfsStatsIndex OBJECT-TYPE
        SYNTAX  NfsStatsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "NFS resource index"
        ::= { nfsStatsEntry 1 }

    nfsStatsExportPoint OBJECT-TYPE
        SYNTAX  NfsStatsExportPointTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NFS export point"
        ::= { nfsStatsEntry 2 }

    nfsStatsFilesystemType OBJECT-TYPE
        SYNTAX  NfsStatsFilesystemTypeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File system type"
        ::= { nfsStatsEntry 3 }

    nfsStatsCacheEntry OBJECT-TYPE
        SYNTAX  NfsStatsCacheEntryTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of cache entries"
        ::= { nfsStatsEntry 4 }

    nfsStatsFileHandleLookup OBJECT-TYPE
        SYNTAX  NfsStatsFileHandleLookupTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "File handle lookup count"
        ::= { nfsStatsEntry 5 }

    nfsStatsMaxCacheSize OBJECT-TYPE
        SYNTAX  NfsStatsMaxCacheSizeTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Max cache size"
        ::= { nfsStatsEntry 6 }

    nfsStatsCurrentOpenStreams OBJECT-TYPE
        SYNTAX  NfsStatsCurrentOpenStreamsTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Current open stream count"
        ::= { nfsStatsEntry 7 }

-- **********************************************************************
--
-- cifsConfig 
-- ====================
--
-- dataDomainMib                       (*******.4.1.19746)
--   dataDomainMibObjects              (*******.4.1.19746.1)
--     cifs                            (*******.4.1.19746.1.10)
--       cifsConfig                    (*******.4.1.19746.1.10.2)
--         cifsConfigMode              (*******.4.1.19746.********)
--         cifsConfigWINSServer        (*******.4.1.19746.********)
--         cifsConfigNetBIOSHostname   (*******.4.1.19746.********)
--         cifsConfigDomainController  (*******.4.1.19746.********)
--         cifsConfigDNS               (*******.4.1.19746.********)
--         cifsConfigGroupname         (*******.4.1.19746.********)
--
-- **********************************************************************

    cifsConfig		    OBJECT IDENTIFIER ::= { cifs 2 }

    cifsConfigMode OBJECT-TYPE
        SYNTAX  CifsConfigModeTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS configuration mode"
        ::= { cifsConfig 1 }

    cifsConfigWINSServer OBJECT-TYPE
        SYNTAX  CifsConfigWINSServerTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS WINS server"
        ::= { cifsConfig 2 }

    cifsConfigNetBIOSHostname OBJECT-TYPE
        SYNTAX  CifsConfigNetBIOSHostnameTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS Net BIOS hostname"
        ::= { cifsConfig 3 }

    cifsConfigDomainController OBJECT-TYPE
        SYNTAX  CifsConfigDomainControllerTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS domain controller"
        ::= { cifsConfig 4 }

    cifsConfigDNS OBJECT-TYPE
        SYNTAX  CifsConfigDNSTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS DNS server"
        ::= { cifsConfig 5 }

    cifsConfigGroupName OBJECT-TYPE
        SYNTAX  CifsConfigGroupNameTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS configuration group name"
        ::= { cifsConfig 6 }

    cifsConfigMaxConnection OBJECT-TYPE
        SYNTAX  CifsConfigMaxConnectionTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "CIFS configuration maximum connection"
        ::= { cifsConfig 7 }

    cifsConfigMaxOpenFilesPerConnection OBJECT-TYPE
        SYNTAX  CifsConfigMaxOpenFilesPerConnectionTC
        MAX-ACCESS  read-only
        STATUS  deprecated
        DESCRIPTION
            "CIFS configuration maximum open files per connection"
        ::= { cifsConfig 8 }

    cifsConfigMaxOpenFiles OBJECT-TYPE
        SYNTAX  Counter32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "CIFS configuration maximum open files"
        ::= { cifsConfig 9 }

-- **********************************************************************
--
-- cifsOptions
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     cifs                     (*******.4.1.19746.1.10)
--       cifsOptions              (*******.4.1.19746.1.10.5)
--
-- **********************************************************************

    cifsOptions		    OBJECT IDENTIFIER ::= { cifs 5 }

    cifsOptionsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF CifsOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of CifsOptionsEntry."
        ::= { cifsOptions 1 }

    cifsOptionsEntry OBJECT-TYPE
        SYNTAX  CifsOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "cifsOptionsTable Row Description"
        INDEX { cifsOptionsIndex }
        ::= { cifsOptionsTable 1 }

    CifsOptionsEntry ::= SEQUENCE {
        cifsOptionsIndex          CifsOptionsIndexTC,
        cifsOptionsName           CifsOptionsNameTC,
        cifsOptionsValue           CifsOptionsValueTC
    }

    cifsOptionsIndex OBJECT-TYPE
        SYNTAX  CifsOptionsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "CIFS Options index"
        ::= { cifsOptionsEntry 1 }

    cifsOptionsName OBJECT-TYPE
        SYNTAX  CifsOptionsNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS Options name"
        ::= { cifsOptionsEntry 2 }

    cifsOptionsValue OBJECT-TYPE
        SYNTAX  CifsOptionsValueTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS Options Value"
        ::= { cifsOptionsEntry 3 }


-- **********************************************************************
--
-- cifsProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     cifs                     (*******.4.1.19746.1.10)
--       cifsProperties         (*******.4.1.19746.1.10.1)
--         cifsStatus           (*******.4.1.19746.********)
--
-- **********************************************************************

    cifsProperties		    OBJECT IDENTIFIER ::= { cifs 1 }

    cifsStatus OBJECT-TYPE
        SYNTAX  CifsStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "CIFS status"
        ::= { cifsProperties 1 }

-- **********************************************************************
--
-- cifsShare
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     cifs                     (*******.4.1.19746.1.10)
--       cifsShare              (*******.4.1.19746.1.10.3)
--
-- **********************************************************************

    cifsShare		    OBJECT IDENTIFIER ::= { cifs 3 }

    cifsShareTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF CifsShareEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of CifsShareEntry."
        ::= { cifsShare 1 }

    cifsShareEntry OBJECT-TYPE
        SYNTAX  CifsShareEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "cifsShareTable Row Description"
        INDEX { cifsShareIndex }
        ::= { cifsShareTable 1 }

    CifsShareEntry ::= SEQUENCE {
        cifsShareIndex          CifsShareIndexTC,
        cifsShareName           CifsShareNameTC,
        cifsSharePath           CifsSharePathTC,
        cifsShareClients        CifsShareClientsTC,
        cifsShareUser           CifsShareUserTC,
        cifsShareComment        CifsShareCommentTC,
        cifsShareBrowsing       CifsShareBrowsingTC,
        cifsShareWriteable      CifsShareWriteableTC,
        cifsShareMaxConnection  CifsShareMaxConnectionTC
    }

    cifsShareIndex OBJECT-TYPE
        SYNTAX  CifsShareIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "CIFS share index"
        ::= { cifsShareEntry 1 }

    cifsShareName OBJECT-TYPE
        SYNTAX  CifsShareNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share name"
        ::= { cifsShareEntry 2 }

    cifsSharePath OBJECT-TYPE
        SYNTAX  CifsSharePathTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share path"
        ::= { cifsShareEntry 3 }

    cifsShareClients OBJECT-TYPE
        SYNTAX  CifsShareClientsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share clients"
        ::= { cifsShareEntry 4 }

    cifsShareUser OBJECT-TYPE
        SYNTAX  CifsShareUserTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share user"
        ::= { cifsShareEntry 5 }

    cifsShareComment OBJECT-TYPE
        SYNTAX  CifsShareCommentTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share comment"
        ::= { cifsShareEntry 6 }

    cifsShareBrowsing OBJECT-TYPE
        SYNTAX  CifsShareBrowsingTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share browsing"
        ::= { cifsShareEntry 7 }

    cifsShareWriteable OBJECT-TYPE
        SYNTAX  CifsShareWriteableTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share writeable"
        ::= { cifsShareEntry 8 }

    cifsShareMaxConnection OBJECT-TYPE
        SYNTAX  CifsShareMaxConnectionTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "CIFS share maximum connection"
        ::= { cifsShareEntry 9 }

-- **********************************************************************
--
-- vtlLibrary
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlLibrary           (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlConfiguration		    OBJECT IDENTIFIER ::= { vtl 2 }
    vtlLibrary		            OBJECT IDENTIFIER ::= { vtlConfiguration 1 }


    vtlLibraryTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlLibraryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlLibraryEntry."
        ::= { vtlLibrary 1 }

    vtlLibraryEntry OBJECT-TYPE
        SYNTAX  VtlLibraryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlLibraryTable Row Description"
        INDEX { vtlLibraryIndex }
        ::= { vtlLibraryTable 1 }

    VtlLibraryEntry ::= SEQUENCE {
        vtlLibraryIndex        VtlLibraryIndexTC,
        vtlLibraryName         VtlLibraryNameTC,
        vtlLibraryVendor       VtlLibraryVendorTC,
        vtlLibraryModel        VtlLibraryModelTC,
        vtlLibraryRevision     VtlLibraryRevisionTC,
        vtlLibrarySerial       VtlLibrarySerialTC,
        vtlLibraryTotalDrives  VtlLibraryTotalDrivesTC,
        vtlLibraryTotalSlots   VtlLibraryTotalSlotsTC,
        vtlLibraryTotalCaps    VtlLibraryTotalCapsTC,
        vtlLibraryStatus       VtlLibraryStatusTC
    }

    vtlLibraryIndex OBJECT-TYPE
        SYNTAX  VtlLibraryIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Library index"
        ::= { vtlLibraryEntry 1 }

    vtlLibraryName OBJECT-TYPE
        SYNTAX  VtlLibraryNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library name"
        ::= { vtlLibraryEntry 2 }

    vtlLibraryVendor OBJECT-TYPE
        SYNTAX  VtlLibraryVendorTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library vendor"
        ::= { vtlLibraryEntry 3 }

    vtlLibraryModel OBJECT-TYPE
        SYNTAX  VtlLibraryModelTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library model"
        ::= { vtlLibraryEntry 4 }

    vtlLibraryRevision OBJECT-TYPE
        SYNTAX  VtlLibraryRevisionTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library revision"
        ::= { vtlLibraryEntry 5 }

    vtlLibrarySerial OBJECT-TYPE
        SYNTAX  VtlLibrarySerialTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library serial"
        ::= { vtlLibraryEntry 6 }

    vtlLibraryTotalDrives OBJECT-TYPE
        SYNTAX  VtlLibraryTotalDrivesTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library total drives"
        ::= { vtlLibraryEntry 7 }

    vtlLibraryTotalSlots OBJECT-TYPE
        SYNTAX  VtlLibraryTotalSlotsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library total slots"
        ::= { vtlLibraryEntry 8 }

    vtlLibraryTotalCaps OBJECT-TYPE
        SYNTAX  VtlLibraryTotalCapsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library total caps"
        ::= { vtlLibraryEntry 9 }

    vtlLibraryStatus OBJECT-TYPE
        SYNTAX  VtlLibraryStatusTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL library status"
        ::= { vtlLibraryEntry 10 }

-- **********************************************************************
--
-- vtlDrive
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlDrive             (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlDrive		            OBJECT IDENTIFIER ::= { vtlConfiguration 2 }

    vtlDriveTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlDriveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlDriveEntry."
        ::= { vtlDrive 1 }

    vtlDriveEntry OBJECT-TYPE
        SYNTAX  VtlDriveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlDriveTable Row Description"
        INDEX { vtlDriveIndex }
        ::= { vtlDriveTable 1 }

    VtlDriveEntry ::= SEQUENCE {
        vtlDriveIndex        VtlDriveIndexTC,
        vtlDriveName         VtlDriveNameTC,
        vtlDriveVendor       VtlDriveVendorTC,
        vtlDriveModel        VtlDriveModelTC,
        vtlDriveRevision     VtlDriveRevisionTC,
        vtlDriveSerial       VtlDriveSerialTC,
        vtlDriveLibraryName  VtlLibraryNameTC,
        vtlDriveStatus       VtlDriveStatusTC,
        vtlDriveTapeVolume   VtlDriveTapeVolumeTC
    }

    vtlDriveIndex OBJECT-TYPE
        SYNTAX  VtlDriveIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL drive index"
        ::= { vtlDriveEntry 1 }

    vtlDriveName OBJECT-TYPE
        SYNTAX  VtlDriveNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive name"
        ::= { vtlDriveEntry 2 }

    vtlDriveVendor OBJECT-TYPE
        SYNTAX  VtlDriveVendorTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive vendor"
        ::= { vtlDriveEntry 3 }

    vtlDriveModel OBJECT-TYPE
        SYNTAX  VtlDriveModelTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive model"
        ::= { vtlDriveEntry 4 }

    vtlDriveRevision OBJECT-TYPE
        SYNTAX  VtlDriveRevisionTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive revision"
        ::= { vtlDriveEntry 5 }

    vtlDriveSerial OBJECT-TYPE
        SYNTAX  VtlDriveSerialTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive serial"
        ::= { vtlDriveEntry 6 }

    vtlDriveLibraryName OBJECT-TYPE
        SYNTAX  VtlLibraryNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive library name"
        ::= { vtlDriveEntry 7 }

    vtlDriveStatus OBJECT-TYPE
        SYNTAX  VtlDriveStatusTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive status"
        ::= { vtlDriveEntry 8 }

    vtlDriveTapeVolume OBJECT-TYPE
        SYNTAX  VtlDriveTapeVolumeTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL drive tape volume"
        ::= { vtlDriveEntry 9 }


-- **********************************************************************
--
-- vtlGroups
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlGroups              (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlGroups		            OBJECT IDENTIFIER ::= { vtlConfiguration 7 }

    vtlGroupTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlGroupEntry."
        ::= { vtlGroups 1 }

    vtlGroupEntry OBJECT-TYPE
        SYNTAX  VtlGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlGroupTable Row Description"
        INDEX { vtlGroupIndex }
        ::= { vtlGroupTable 1 }

    VtlGroupEntry ::= SEQUENCE {
        vtlGroupIndex                   DDMibTableIndexTC,
        vtlGroupName                    DDMibTableString32TC,
        vtlGroupInitiaterCount          DDMibInteger32TC,
        vtlGroupDeviceCount             DDMibInteger32TC
    }

    vtlGroupIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Group index"
        ::= { vtlGroupEntry 1 }

    vtlGroupName OBJECT-TYPE
        SYNTAX DDMibTableString32TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Group Name"
        ::= { vtlGroupEntry 2 }
    
    vtlGroupInitiaterCount OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Group Initiater Count"
        ::= { vtlGroupEntry 3 }


    vtlGroupDeviceCount OBJECT-TYPE
        SYNTAX DDMibInteger32TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Group Device Count"
        ::= { vtlGroupEntry 4 }


    vtlGroupDeviceTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlGroupDeviceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the devices in VTL groups.
            It provides information such as the Device Name, the Device LUN,
            the Primary Ports, Secondary Ports, In-Use Ports, and Group Name.
            It is comprised of entries of VtlGroupDeviceEntry."
        ::= { vtlGroups 2 }

    vtlGroupDeviceEntry OBJECT-TYPE
        SYNTAX  VtlGroupDeviceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "Information about the devices in a VTL group."
        INDEX { vtlGroupIndex, vtlGroupDeviceIndex }
        ::= { vtlGroupDeviceTable 1 }

    VtlGroupDeviceEntry ::= SEQUENCE {
        vtlGroupDeviceIndex             DDMibTableIndexTC,
        vtlGroupDeviceGroupName         DDMibTableString256TC,
        vtlGroupDeviceDeviceName        DDMibTableString256TC,
        vtlGroupDeviceLun               DDMibInteger32TC,
        vtlGroupDevicePrimaryPorts      DDMibTableString64TC,
        vtlGroupDeviceSecondaryPorts    DDMibTableString64TC,
        vtlGroupDeviceInUsePorts        DDMibTableString64TC
    }

    vtlGroupDeviceIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index of a VTL Device."
        ::= { vtlGroupDeviceEntry 2 }

    vtlGroupDeviceGroupName OBJECT-TYPE
        SYNTAX DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of a VTL Group."
        ::= { vtlGroupDeviceEntry 3 }

    vtlGroupDeviceDeviceName OBJECT-TYPE
        SYNTAX DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of a VTL Device."
        ::= { vtlGroupDeviceEntry 4 }

    vtlGroupDeviceLun OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The LUN (Logical Unit Number) of a VTL Device."
        ::= { vtlGroupDeviceEntry 5 }

    vtlGroupDevicePrimaryPorts OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Primary Ports of a VTL Device. This entry specifies a set of
            ports that the device will be visible on. If 'all' is provided the
            device is visible on all ports. If 'none' is provided the device 
            is visible on none of the ports."
        ::= { vtlGroupDeviceEntry 6 }

    vtlGroupDeviceSecondaryPorts OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Secondary Ports of a VTL Device. This entry specifies a second
            set of ports that the device will be visible on. The administrator
            can switch the ports in use in a group to the primary or secondary
            port list."
        ::= { vtlGroupDeviceEntry 7 }

    vtlGroupDeviceInUsePorts OBJECT-TYPE
        SYNTAX DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The In-Use Ports of a VTL Device. The port list that the device is
            visible on is the in-use port list, which can be the primary or
            secondary port list."
        ::= { vtlGroupDeviceEntry 8 }

-- **********************************************************************
--
-- vtlInitiator
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlInitiator              (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlInitiator		OBJECT IDENTIFIER ::= { vtlConfiguration 8 }

    vtlInitiatorTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlInitiatorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlInitiatorEntry."
        ::= { vtlInitiator 1 }

    vtlInitiatorEntry OBJECT-TYPE
        SYNTAX  VtlInitiatorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlInitiatorTable Row Description"
        INDEX { vtlInitiatorIndex }
        ::= { vtlInitiatorTable 1 }

    VtlInitiatorEntry ::= SEQUENCE {
        vtlInitiatorIndex          DDMibTableIndexTC,
        vtlInitiatorName           DDMibTableString32TC,
        vtlInitiatorStatus         DDMibTableString32TC,
        vtlInitiatorGroup          DDMibTableString32TC,
        vtlInitiatorWWNN           DDMibTableString64TC,
        vtlInitiatorWWPN           DDMibTableString64TC,
        vtlInitiatorPort           DDMibTableString32TC
    }

    vtlInitiatorIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Initiator index"
        ::= { vtlInitiatorEntry 1 }

    vtlInitiatorName OBJECT-TYPE
        SYNTAX DDMibTableString32TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator Name"
        ::= { vtlInitiatorEntry 2 }
    
    vtlInitiatorStatus OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator Status"
        ::= { vtlInitiatorEntry 3 }


    vtlInitiatorGroup OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator Group"
        ::= { vtlInitiatorEntry 4 }

    vtlInitiatorWWNN OBJECT-TYPE
        SYNTAX  DDMibTableString64TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator WWNN"
        ::= { vtlInitiatorEntry 5 }

    vtlInitiatorWWPN OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator WWPN"
        ::= { vtlInitiatorEntry 6 }

    vtlInitiatorPort OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Initiator Port"
        ::= { vtlInitiatorEntry 7 }


-- **********************************************************************
--
-- vtlPool
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlPool              (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlPool		            OBJECT IDENTIFIER ::= { vtlConfiguration 6 }

    vtlPoolTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlPoolEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlPoolEntry."
        ::= { vtlPool 1 }

    vtlPoolEntry OBJECT-TYPE
        SYNTAX  VtlPoolEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlPoolTable Row Description"
        INDEX { vtlPoolIndex }
        ::= { vtlPoolTable 1 }

    VtlPoolEntry ::= SEQUENCE {
        vtlPoolIndex          DDMibTableIndexTC,
        vtlPoolPool           DDMibTableString64TC,
        vtlPoolStatus         DDMibTableString64TC,
        vtlPoolTapes          DDMibTableString64TC,
        vtlPoolSize           DDMibTableString64TC,
        vtlPoolUsed           DDMibTableString64TC,
        vtlPoolComp           DDMibTableString64TC
    }

    vtlPoolIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Pool index"
        ::= { vtlPoolEntry 1 }

    vtlPoolPool OBJECT-TYPE
        SYNTAX DDMibTableString64TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Pool"
        ::= { vtlPoolEntry 2 }
    
    vtlPoolStatus OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Status"
        ::= { vtlPoolEntry 3 }


    vtlPoolTapes OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Tapes"
        ::= { vtlPoolEntry 4 }

    vtlPoolSize OBJECT-TYPE
        SYNTAX  DDMibTableString64TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Size"
        ::= { vtlPoolEntry 5 }

    vtlPoolUsed OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Used"
        ::= { vtlPoolEntry 6 }

    vtlPoolComp OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Pool Compression"
        ::= { vtlPoolEntry 7 }


-- **********************************************************************
--
-- vtlPort
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlPort             (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlPort		            OBJECT IDENTIFIER ::= { vtlConfiguration 3 }

    vtlPortTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlPortEntry."
        ::= { vtlPort 1 }

    vtlPortEntry OBJECT-TYPE
        SYNTAX  VtlPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlPortTable Row Description"
        INDEX { vtlPortIndex }
        ::= { vtlPortTable 1 }

    VtlPortEntry ::= SEQUENCE {
        vtlPortIndex            VtlPortIndexTC,
        vtlPortName             VtlPortNameTC,
        vtlPortID               VtlPortIDTC,
        vtlPortModel            VtlPortModelTC,
        vtlPortFirmware         VtlPortFirmwareTC,
        vtlPortWWNN             VtlPortWWNNTC,
        vtlPortWWPN             VtlPortWWPNTC,
        vtlPortConnectionType   VtlPortConnectionTypeTC,
        vtlPortSpeed            VtlPortSpeedTC,
        vtlPortEnabled          VtlPortEnabledTC,
        vtlPortStatus           VtlPortStatusTC,
        vtlPortTrapIndex        VtlPortIndexTC
    }

    vtlPortIndex OBJECT-TYPE
        SYNTAX  VtlPortIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Port index"
        ::= { vtlPortEntry 1 }

    vtlPortName OBJECT-TYPE
        SYNTAX  VtlPortNameTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port name"
        ::= { vtlPortEntry 2 }

    vtlPortID OBJECT-TYPE
        SYNTAX  VtlPortIDTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port ID"
        ::= { vtlPortEntry 3 }

    vtlPortModel OBJECT-TYPE
        SYNTAX  VtlPortModelTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port model"
        ::= { vtlPortEntry 4 }
    
    vtlPortFirmware OBJECT-TYPE
        SYNTAX  VtlPortFirmwareTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port Firmware Version"
        ::= { vtlPortEntry 5 }

    vtlPortWWNN OBJECT-TYPE
        SYNTAX  VtlPortWWNNTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port WWNN address"
        ::= { vtlPortEntry 6 }

    vtlPortWWPN OBJECT-TYPE
        SYNTAX  VtlPortWWPNTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port WWPN Address"
        ::= { vtlPortEntry 7 }

    vtlPortConnectionType OBJECT-TYPE
        SYNTAX  VtlPortConnectionTypeTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port Connection Type"
        ::= { vtlPortEntry 8 }

    vtlPortSpeed OBJECT-TYPE
        SYNTAX  VtlPortSpeedTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port Speed in Gibs"
        ::= { vtlPortEntry 9 }

    vtlPortEnabled OBJECT-TYPE
        SYNTAX  VtlPortEnabledTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port Enabled"
        ::= { vtlPortEntry 10 }

    vtlPortStatus OBJECT-TYPE
        SYNTAX  VtlPortStatusTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Port Status"
        ::= { vtlPortEntry 11 }
    
    vtlPortTrapIndex OBJECT-TYPE
        SYNTAX  VtlPortIndexTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "VTL Port index"
        ::= { vtlPortEntry 12 }

-- **********************************************************************
--
-- vtlProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlProperties          (*******.4.1.19746.1.11.1)
--         vtlAdminState        (*******.4.1.19746.********)
--         vtlProcessState      (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlProperties		    OBJECT IDENTIFIER ::= { vtl 1 }

    vtlAdminState OBJECT-TYPE
        SYNTAX  VtlAdminStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "State of VTL administration"
        ::= { vtlProperties 1 }

    vtlProcessState OBJECT-TYPE
        SYNTAX  VtlProcessStateTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "State of VTL process"
        ::= { vtlProperties 2 }

-- **********************************************************************
--
-- vtlStats
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlStats                  (*******.4.1.19746.1.11.3)
--
-- **********************************************************************

    vtlStats		      OBJECT IDENTIFIER ::= { vtl 3 }

    vtlStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains detailed statistical information about the 
            individual VTL Ports on a Data Domain System. It includes the 
            count of Control Commands, Write Commands, and Read Commands. The
            amount of data In, and Out. The count of Link Failures, LIP count,
            Sync Losses, Signal Losses, Prim Seq Proto Errors, Invalid Tx 
            Words, and Invalid CRCs. It is comprised of entries of 
            VtlStatsEntry."

        ::= { vtlStats 1 }

    vtlStatsEntry OBJECT-TYPE
        SYNTAX  VtlStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlStatsTable Row Description"
        INDEX { vtlStatsIndex }
        ::= { vtlStatsTable 1 }

    VtlStatsEntry ::= SEQUENCE {
        vtlStatsIndex                   VtlStatsIndexTC,
        vtlStatsPort                    VtlStatsPortTC,
        vtlStatsConrolCommands          VtlStatsConrolCommandsTC,
        vtlStatsWriteCommands           VtlStatsWriteCommandsTC,
        vtlStatsReadCommands            VtlStatsReadCommandsTC,
        vtlStatsIn                      VtlStatsInTC,
        vtlStatsOut                     VtlStatsOutTC,
        vtlStatsLinkFailures            VtlStatsLinkFailuresTC,
        vtlStatsLIPCount                VtlStatsLIPCountTC,
        vtlStatsSyncLosses              VtlStatsSyncLossesTC,
        vtlStatsSignalLosses            VtlStatsSignalLossesTC,
        vtlStatsPrimSeqProtoErrors      VtlStatsPrimSeqProtoErrorsTC,
        vtlStatsInvalidTxWords          VtlStatsInvalidTxWordsTC,
        vtlStatsInvalidCRCs             VtlStatsInvalidCRCsTC

    }

    vtlStatsIndex OBJECT-TYPE
        SYNTAX  VtlStatsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Stats Index. The index of the VTL Per-Port Statistical entry."
        ::= { vtlStatsEntry 1 }

    vtlStatsPort OBJECT-TYPE
        SYNTAX  VtlStatsPortTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Port. The physical VTL port number."
        ::= { vtlStatsEntry 2 }

    vtlStatsConrolCommands OBJECT-TYPE
        SYNTAX  VtlStatsConrolCommandsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Control Commands. The number of non read/write 
            commands."
        ::= { vtlStatsEntry 3 }

    vtlStatsWriteCommands OBJECT-TYPE
        SYNTAX  VtlStatsWriteCommandsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Write Commands. The number of WRITE commands."
        ::= { vtlStatsEntry 4 }

    vtlStatsReadCommands OBJECT-TYPE
        SYNTAX  VtlStatsReadCommandsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Read Commands. The number of READ commands."
        ::= { vtlStatsEntry 5 }

    vtlStatsIn OBJECT-TYPE
        SYNTAX  VtlStatsInTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats In. The number of megabytes written to the specific 
            port."
        ::= { vtlStatsEntry 6 }

    vtlStatsOut OBJECT-TYPE
        SYNTAX  VtlStatsOutTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Out. The number of megabytes read from the specific
            port."
        ::= { vtlStatsEntry 7 }

    vtlStatsLinkFailures OBJECT-TYPE
        SYNTAX  VtlStatsLinkFailuresTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Link Failures. The count of Link Failures."
        ::= { vtlStatsEntry 8 }

    vtlStatsLIPCount OBJECT-TYPE
        SYNTAX  VtlStatsLIPCountTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats LIP Count. The number of LIPs."
        ::= { vtlStatsEntry 9 }

    vtlStatsSyncLosses OBJECT-TYPE
        SYNTAX  VtlStatsSyncLossesTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Sync Losses. The number of times sync loss was 
            detected."
        ::= { vtlStatsEntry 10 }

    vtlStatsSignalLosses OBJECT-TYPE
        SYNTAX  VtlStatsSignalLossesTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Signal Losses. The number of times loss of signal was
            detected."
        ::= { vtlStatsEntry 11 }

    vtlStatsPrimSeqProtoErrors OBJECT-TYPE
        SYNTAX  VtlStatsPrimSeqProtoErrorsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Prim Seq Proto Errors. The count of errors in the 
            Primitive Sequence Protocol."
        ::= { vtlStatsEntry 12 }

    vtlStatsInvalidTxWords OBJECT-TYPE
        SYNTAX  VtlStatsInvalidTxWordsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Invalid Tx Words. The number of invalid TX words."
        ::= { vtlStatsEntry 13 }

    vtlStatsInvalidCRCs OBJECT-TYPE
        SYNTAX  VtlStatsInvalidCRCsTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Stats Invalid CRCs. The number of frames received with bad 
            CRC."
        ::= { vtlStatsEntry 14 }


-- **********************************************************************
--
-- vtlTape
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     vtl                      (*******.4.1.19746.1.11)
--       vtlConfiguration       (*******.4.1.19746.1.11.2)
--         vtlTape              (*******.4.1.19746.********)
--
-- **********************************************************************

    vtlTape		            OBJECT IDENTIFIER ::= { vtlConfiguration 4 }

    vtlTapeTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF VtlTapeEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of VtlTapeEntry."
        ::= { vtlTape 1 }

    vtlTapeEntry OBJECT-TYPE
        SYNTAX  VtlTapeEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "vtlTapeTable Row Description"
        INDEX { vtlTapeIndex }
        ::= { vtlTapeTable 1 }

    VtlTapeEntry ::= SEQUENCE {
        vtlTapeIndex          VtlTapeIndexTC,
        vtlTapeBarCode        VtlTapeBarCodeTC,
        vtlTapePool           VtlTapePoolTC,
        vtlTapeLocation       VtlTapeLocationTC,
        vtlTapeState          VtlTapeStateTC,
        vtlTapeSize           VtlTapeSizeTC,
        vtlTapeUsed           VtlTapeUsedTC,
        vtlTapeComp           VtlTapeCompTC,
        vtlTapeModTime        VtlTapeModTimeTC
    }

    vtlTapeIndex OBJECT-TYPE
        SYNTAX  VtlTapeIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "VTL Tape index"
        ::= { vtlTapeEntry 1 }

    vtlTapeBarCode OBJECT-TYPE
        SYNTAX  VtlTapeBarCodeTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape BarCode"
        ::= { vtlTapeEntry 2 }

    vtlTapePool OBJECT-TYPE
        SYNTAX  VtlTapePoolTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape Pool"
        ::= { vtlTapeEntry 3 }
    
    vtlTapeLocation OBJECT-TYPE
        SYNTAX  VtlTapeLocationTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape Location"
        ::= { vtlTapeEntry 4 }


    vtlTapeState OBJECT-TYPE
        SYNTAX  VtlTapeStateTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape State"
        ::= { vtlTapeEntry 5 }

    vtlTapeSize OBJECT-TYPE
        SYNTAX  VtlTapeSizeTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape Size"
        ::= { vtlTapeEntry 6 }

    vtlTapeUsed OBJECT-TYPE
        SYNTAX  VtlTapeUsedTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape Used"
        ::= { vtlTapeEntry 7 }

    vtlTapeComp OBJECT-TYPE
        SYNTAX  VtlTapeCompTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape Compression"
        ::= { vtlTapeEntry 8 }

    vtlTapeModTime OBJECT-TYPE
        SYNTAX  VtlTapeModTimeTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "VTL Tape status"
        ::= { vtlTapeEntry 9 }


-- **********************************************************************
--
-- ddboostAccessClients 
-- ====================
--
-- dataDomainMib                    (*******.4.1.19746)
--   dataDomainMibObjects           (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostAccessClients       (*******.4.1.19746.1.12.8)
--
-- **********************************************************************

    ddboostAccessClients		    OBJECT IDENTIFIER ::= { ddboost 8 }

     ddboostAccessClientsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostAccessClientsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST AccessClients table information for DDBOOST Access Client Configuration"
        ::= { ddboostAccessClients 1 }

    ddboostAccessClientsEntry OBJECT-TYPE
        SYNTAX  DdboostAccessClientsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostAccessClientsTable Row Entry"
        INDEX { ddboostAccessClientsIndex }
        ::= { ddboostAccessClientsTable 1 }

     DdboostAccessClientsEntry ::= SEQUENCE {
        ddboostAccessClientsIndex                       DDMibTableIndexTC,
        ddboostAccessClientsName                        DDMibTableString64TC,
        ddboostAccessClientsEncryStrength               DdboostAccessClientsEncryStrengthTC,
        ddboostAccessClientsAuthMode                    DdboostAccessClientsAuthModeTC
    }

     ddboostAccessClientsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost AccessClients Table index"
        ::= { ddboostAccessClientsEntry 1 }

    ddboostAccessClientsName OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The DDboost Access clients Name"
        ::= { ddboostAccessClientsEntry 2 }

    ddboostAccessClientsEncryStrength OBJECT-TYPE
        SYNTAX   DdboostAccessClientsEncryStrengthTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The encryption strength that the DDboost Access client is using, possible values are
             none, medium and high"
        ::= { ddboostAccessClientsEntry 3 }

    ddboostAccessClientsAuthMode OBJECT-TYPE
        SYNTAX   DdboostAccessClientsAuthModeTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Authentication mode that the DDboost Access client is using, possible values are
             none, one-way, two-way and anonymous"
        ::= { ddboostAccessClientsEntry 4 }

-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostConnections               (*******.4.1.19746.1.12.3)
--
-- **********************************************************************

    ddboostConnections		    OBJECT IDENTIFIER ::= { ddboost 3 }

     ddboostConnectionsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostConnectionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST connections table information for DDBOOST Client Connections"
        ::= { ddboostConnections 1 }

    ddboostConnectionsEntry OBJECT-TYPE
        SYNTAX  DdboostConnectionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostConnectionsTable Row Entry"
        INDEX { ddboostConnectionsIndex }
        ::= { ddboostConnectionsTable 1 }

     DdboostConnectionsEntry ::= SEQUENCE {
        ddboostConnectionsIndex                       DDMibTableIndexTC,
        ddboostInterface	                      DDMibTableString64TC,
	ddboostifGroupMember	                      DDMibTableEnabledTC,
	ddboostBackupConnections	              DDMibInteger32TC,
	ddboostRestoreConnections	              DDMibInteger32TC,
        ddboostControlConnections	              DDMibInteger32TC,
        ddboostTotalConnections	                      DDMibInteger32TC
    }

     ddboostConnectionsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost connections Table index"
        ::= { ddboostConnectionsEntry 1 }

    ddboostInterface OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Interface Name"
        ::= { ddboostConnectionsEntry 2 }

    ddboostifGroupMember OBJECT-TYPE
        SYNTAX  DDMibTableEnabledTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "If Member of ifGroup"
        ::= { ddboostConnectionsEntry 3 }

    ddboostBackupConnections OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost backup connections"
        ::= { ddboostConnectionsEntry 4 }

    ddboostRestoreConnections OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Restore connections"
        ::= { ddboostConnectionsEntry 5 }

    ddboostControlConnections OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost control connections"
        ::= { ddboostConnectionsEntry 6 }

    ddboostTotalConnections OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Total connections"
        ::= { ddboostConnectionsEntry 7 }




-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostFileReplicationHistory               (*******.4.1.19746.1.12.6)
--
-- **********************************************************************

    ddboostFileReplicationHistory		    OBJECT IDENTIFIER ::= { ddboost 6 }

     ddboostFileReplicationHistoryTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostFileReplicationHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST FileReplicationHistory table contains information for File Replication History"
        ::= { ddboostFileReplicationHistory 1 }

    ddboostFileReplicationHistoryEntry OBJECT-TYPE
        SYNTAX  DdboostFileReplicationHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostFileReplicationHistoryTable Row Entry"
        INDEX { ddboostFileReplHistoryIndex }
        ::= { ddboostFileReplicationHistoryTable 1 }

     DdboostFileReplicationHistoryEntry ::= SEQUENCE {
        ddboostFileReplHistoryIndex                            DDMibTableIndexTC,
        ddboostFileReplHistoryDirection	                       DDMibTableString32TC,
	ddboostFileReplHistoryNetwork	                       DDMibTrafficBytesTC,
	ddboostFileReplHistoryPreComp   	               DDMibTrafficBytesTC,
        ddboostFileReplHistoryPostComp                         DDMibTrafficBytesTC,
	ddboostFileReplHistoryLowBWOpt	                       DDMibTableString32TC,
        ddboostFileReplHistoryErrors	                       DDMibTrafficBytesTC,
        ddboostFileReplHistoryDate	                       DDMibDateTC,
        ddboostFileReplHistoryTime                             DDMibDateTC
    }

     ddboostFileReplHistoryIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost FileReplicationHistory Table index"
        ::= { ddboostFileReplicationHistoryEntry 1 }

    ddboostFileReplHistoryDirection OBJECT-TYPE
        SYNTAX   DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost File ReplHistory History Direction"
        ::= { ddboostFileReplicationHistoryEntry 2 }

    ddboostFileReplHistoryNetwork OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication History Network KBytes Sent"
        ::= { ddboostFileReplicationHistoryEntry 3 }

    ddboostFileReplHistoryPreComp OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File ReplHistory Pre Compressed Kbytes sent"
        ::= { ddboostFileReplicationHistoryEntry 4 }
    
    ddboostFileReplHistoryPostComp OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File ReplHistory Post Compressed KBytes"
        ::= { ddboostFileReplicationHistoryEntry 5 }


    ddboostFileReplHistoryLowBWOpt OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File ReplHistory KBytes after Low Bandwidth optimization"
        ::= { ddboostFileReplicationHistoryEntry 6 }

    ddboostFileReplHistoryErrors OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File ReplHistory Errors"
        ::= { ddboostFileReplicationHistoryEntry 7 }

    ddboostFileReplHistoryDate OBJECT-TYPE
        SYNTAX DDMibDateTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost File ReplHistory Date"
        ::= { ddboostFileReplicationHistoryEntry 8 }

    ddboostFileReplHistoryTime OBJECT-TYPE
        SYNTAX DDMibDateTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost File ReplHistory Time"
        ::= { ddboostFileReplicationHistoryEntry 9 }



-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostFileReplicationStats               (*******.4.1.19746.1.12.5)
--
-- **********************************************************************

    ddboostFileReplicationStats		    OBJECT IDENTIFIER ::= { ddboost 5 }

     ddboostFileReplicationStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostFileReplicationStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST FileReplicationStats table contains information for File Replication Stats"
        ::= { ddboostFileReplicationStats 1 }

    ddboostFileReplicationStatsEntry OBJECT-TYPE
        SYNTAX  DdboostFileReplicationStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostFileReplicationStatsTable Row Entry"
        INDEX { ddboostFileReplStatsIndex }
        ::= { ddboostFileReplicationStatsTable 1 }

     DdboostFileReplicationStatsEntry ::= SEQUENCE {
        ddboostFileReplStatsIndex                       DDMibTableIndexTC,
        ddboostFileReplStatsDirection	                       DDMibTableString32TC,
	ddboostFileReplStatsNetworkSent	               DDMibTrafficBytesTC,
	ddboostFileReplStatsPreCompSent   	               DDMibTrafficBytesTC,
        ddboostFileReplStatsFiltered                         DDMibTrafficBytesTC,
	ddboostFileReplStatsLowBWOpt	                       DDMibTrafficBytesTC,
        ddboostFileReplStatsLocalComp	                       DDMibTrafficBytesTC,
        ddboostFileReplStatsCompRatio	                       DDMibTableString32TC
    }

     ddboostFileReplStatsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost FileReplicationStats Table index"
        ::= { ddboostFileReplicationStatsEntry 1 }

    ddboostFileReplStatsDirection OBJECT-TYPE
        SYNTAX   DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost File Replication Direction"
        ::= { ddboostFileReplicationStatsEntry 2 }

    ddboostFileReplStatsNetworkSent OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication Network Bytes Sent"
        ::= { ddboostFileReplicationStatsEntry 3 }

    ddboostFileReplStatsPreCompSent OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication Pre Compressed bytes sent"
        ::= { ddboostFileReplicationStatsEntry 4 }
    
    ddboostFileReplStatsFiltered OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication Bytes after Filtering"
        ::= { ddboostFileReplicationStatsEntry 5 }


    ddboostFileReplStatsLowBWOpt OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication Bytes after Low Bandwidth optimization"
        ::= { ddboostFileReplicationStatsEntry 6 }

    ddboostFileReplStatsLocalComp OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBoost File Replication Bytes after local compression"
        ::= { ddboostFileReplicationStatsEntry 7 }

    ddboostFileReplStatsCompRatio OBJECT-TYPE
        SYNTAX  DDMibTableString32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost File Replication Compressed Ratio"
        ::= { ddboostFileReplicationStatsEntry 8 }




-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                            (*******.4.1.19746)
--   dataDomainMibObjects                   (*******.4.1.19746.1)
--     ddboost                              (*******.4.1.19746.1.12)
--       ddboostFileReplicationPerformance  (*******.4.1.19746.1.12.10)
--
-- **********************************************************************

    ddboostFileReplicationPerformance       OBJECT IDENTIFIER ::= { ddboost 10 }


    ddboostFileRepliPerfInPreCompKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBOOST File Replication Performance Inbound pre-comp kilobytes per second"
        ::= { ddboostFileReplicationPerformance 1 }

    ddboostFileRepliPerfInNetworkKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBOOST File Replication Performance Inbound network kilobytes per second"
        ::= { ddboostFileReplicationPerformance 2 }

    ddboostFileRepliPerfOutPreCompKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBOOST File Replication Performance Outbound pre-comp kilobytes per second"
        ::= { ddboostFileReplicationPerformance 3 }

    ddboostFileRepliPerfOutNetworkKBPerSec OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDBOOST File Replication Performance Outbound network kilobytes per second"
        ::= { ddboostFileReplicationPerformance 4 }


-- **********************************************************************
--
-- ddboostIfGroupConfig 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostIfGroupConfig               (*******.4.1.19746.1.12.7)
--
-- **********************************************************************

    ddboostIfGroupConfig		    OBJECT IDENTIFIER ::= { ddboost 7 }

     ddboostIfGroupConfigTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostIfGroupConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST IfGroupConfig table information for DDBOOST ifGroup Configuration"
        ::= { ddboostIfGroupConfig 1 }

    ddboostIfGroupConfigEntry OBJECT-TYPE
        SYNTAX  DdboostIfGroupConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostIfGroupConfigTable Row Entry"
        INDEX { ddboostIfGroupConfigIndex }
        ::= { ddboostIfGroupConfigTable 1 }

     DdboostIfGroupConfigEntry ::= SEQUENCE {
        ddboostIfGroupConfigIndex                       DDMibTableIndexTC,
        ddboostIfGroupInterface	                        DDMibTableString64TC
    }

     ddboostIfGroupConfigIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost IfGroupConfig Table index"
        ::= { ddboostIfGroupConfigEntry 1 }

    ddboostIfGroupInterface OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost ifGroup Interface Name"
        ::= { ddboostIfGroupConfigEntry 2 }




-- **********************************************************************
--
-- ddboostOptions
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostOptions               (*******.4.1.19746.1.12.9)
--
-- **********************************************************************

    ddboostOptions                  OBJECT IDENTIFIER ::= { ddboost 9 }

     ddboostOptionsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST Options table"
        ::= { ddboostOptions 1 }

    ddboostOptionsEntry OBJECT-TYPE
        SYNTAX  DdboostOptionsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostOptionsTable Row Entry"
        INDEX { ddboostOptionsIndex }
        ::= { ddboostOptionsTable 1 }

     DdboostOptionsEntry ::= SEQUENCE {
        ddboostOptionsIndex                       DDMibTableIndexTC,
        ddboostOptionsName                        DDMibTableString64TC,
        ddboostOptionsStatus                      DDMibStatusTC
    }

     ddboostOptionsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost Option Table index"
        ::= { ddboostOptionsEntry 1 }

    ddboostOptionsName OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Option Value Name "
        ::= { ddboostOptionsEntry 2 }


    ddboostOptionsStatus OBJECT-TYPE
        SYNTAX  DDMibStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Option Status Enabled/Disabled "
        ::= { ddboostOptionsEntry 3 }





-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostProperties          (*******.4.1.19746.1.12.1)
--
-- **********************************************************************

    ddboostProperties		    OBJECT IDENTIFIER ::= { ddboost 1 }

    ddboostStatus OBJECT-TYPE
        SYNTAX  DDboostStatusTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "The status of the DDBoost interface group on a DD system."
        ::= { ddboostProperties 1 }

    ddboostUser OBJECT-TYPE
        SYNTAX  DDboostUserTC
        MAX-ACCESS  read-only
        STATUS  deprecated
        DESCRIPTION "DDBoost User."
        ::= { ddboostProperties 2 }

    ddboostIfGroupStatus OBJECT-TYPE
        SYNTAX  DDMibStatusTC
        MAX-ACCESS  read-only
        STATUS  deprecated
        DESCRIPTION "DDBoost IfGroup Status."
        ::= { ddboostProperties 3 }

    ddboostUserTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about DDBoost users."
        ::= { ddboostProperties 4 }

    ddboostUserEntry OBJECT-TYPE
        SYNTAX  DdboostUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostUserTable Row Entry."
        INDEX { ddboostUserIdx }
        ::= { ddboostUserTable 1 }

    DdboostUserEntry ::= SEQUENCE {
        ddboostUserIdx                DDboostStatsIndexTC,
        ddboostUserName               DDMibTableString256TC,
        ddboostUserDefaultTenantUnit  DDMibTableString256TC
    }

    ddboostUserIdx OBJECT-TYPE
        SYNTAX  DDboostStatsIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBoost users table index."
        ::= { ddboostUserEntry 1 }

    ddboostUserName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of a Ddboost user."
        ::= { ddboostUserEntry 2 }

    ddboostUserDefaultTenantUnit OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The default tenant unit that is associated with a specific DDBoost user."
        ::= { ddboostUserEntry 3 }

    ddboostIfGroupTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostIfGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBoost IfGroup table containing interface group information."
        ::= { ddboostProperties 5 }

    ddboostIfGroupEntry OBJECT-TYPE
        SYNTAX  DdboostIfGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostIfGroupTable Row Entry."
        INDEX { ddboostIfGroupIdx }
        ::= { ddboostIfGroupTable 1 }

    DdboostIfGroupEntry ::= SEQUENCE {
        ddboostIfGroupIdx                DDboostStatsIndexTC,
        ddboostIfGroupName               DDMibTableString256TC,
        ddboostIfGroupCurrentStatus      DDMibStatusTC
    }

    ddboostIfGroupIdx OBJECT-TYPE
        SYNTAX  DDboostStatsIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBoost IfGroup table index."
        ::= { ddboostIfGroupEntry 1 }

    ddboostIfGroupName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of DDBoost IfGroup."
        ::= { ddboostIfGroupEntry 2 }

    ddboostIfGroupCurrentStatus OBJECT-TYPE
        SYNTAX  DDMibStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The status of a DDBoost IfGroup. Possible values are: 'enabled' or 'disabled'."
        ::= { ddboostIfGroupEntry 3 }

-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostStats               (*******.4.1.19746.1.12.2)
--
-- **********************************************************************

    ddboostStats		    OBJECT IDENTIFIER ::= { ddboost 2 }

     ddboostStatsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST stats table containing byte statistics information for DDBOOST"
        ::= { ddboostStats 1 }

    ddboostStatsEntry OBJECT-TYPE
        SYNTAX  DdboostStatsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostStatsTable Row Entry"
        INDEX { ddboostStatsIndex }
        ::= { ddboostStatsTable 1 }

     DdboostStatsEntry ::= SEQUENCE {
        ddboostStatsIndex                   DDboostStatsIndexTC,
        ddboostPreCompKBytesPerSecond	    KBytesPerSecond,
	ddboostPostCompKBytesPerSecond	    KBytesPerSecond,
	ddboostNetworkKBytesPerSecond	    KBytesPerSecond,
	ddboostReadKBytesPerSecond	    KBytesPerSecond,
        ddboostStatsBackupConn              Counter64,
        ddboostStatsRestoreConn             Counter64,
        ddboostStatsImageCreatesCount       Counter64,
        ddboostStatsImageCreatesErrors      Counter64,
        ddboostStatsImageDeletesCount       Counter64,
        ddboostStatsImageDeletesErrors      Counter64,
        ddboostStatsPrecompBytesReceived    DDMibTrafficBytesTC,
        ddboostStatsBytesAfterFiltering     DDMibTrafficBytesTC,
        ddboostStatsBytesAfterLc            DDMibTrafficBytesTC,
        ddboostStatsNetworkBytesReceived    DDMibTrafficBytesTC,
        ddboostStatsCompressionRatio        DDMibTableString32TC,
        ddboostStatsTotalBytesReadCount     DDMibTrafficBytesTC,
        ddboostStatsTotalBytesReadErrors    DDMibTrafficBytesTC
    }

     ddboostStatsIndex OBJECT-TYPE
        SYNTAX  DDboostStatsIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost Stats Table index"
        ::= { ddboostStatsEntry 1 }

    ddboostPreCompKBytesPerSecond OBJECT-TYPE
        SYNTAX   KBytesPerSecond
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of pre-compressed (logical) bytes received per second"
        ::= { ddboostStatsEntry 2 }

    ddboostPostCompKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "number of pddboost local compression bytes received per second"
        ::= { ddboostStatsEntry 3 }

    ddboostNetworkKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of physical network bytes received per second"
        ::= { ddboostStatsEntry 4 }

    ddboostReadKBytesPerSecond OBJECT-TYPE
        SYNTAX  KBytesPerSecond 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of bytes read per second"
        ::= { ddboostStatsEntry 5 }

    ddboostStatsBackupConn OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of backup connections"
        ::= { ddboostStatsEntry 6 }

    ddboostStatsRestoreConn OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of restore connections"
        ::= { ddboostStatsEntry 7 }

    ddboostStatsImageCreatesCount OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of image creates"
        ::= { ddboostStatsEntry 8 }

    ddboostStatsImageCreatesErrors OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of image creates errors"
        ::= { ddboostStatsEntry 9 }

    ddboostStatsImageDeletesCount OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of image deletes"
        ::= { ddboostStatsEntry 10 }

    ddboostStatsImageDeletesErrors OBJECT-TYPE
        SYNTAX  Counter64
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of image deletes errors"
        ::= { ddboostStatsEntry 11 }

    ddboostStatsPrecompBytesReceived OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of pre-compressed (logical) bytes received"
        ::= { ddboostStatsEntry 12 }

    ddboostStatsBytesAfterFiltering OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of bytes after filtering"
        ::= { ddboostStatsEntry 13 }

    ddboostStatsBytesAfterLc OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of bytes after local compression"
        ::= { ddboostStatsEntry 14 }

    ddboostStatsNetworkBytesReceived OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Number of network bytes received"
        ::= { ddboostStatsEntry 15 }

    ddboostStatsCompressionRatio OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Compression ratio"
        ::= { ddboostStatsEntry 16 }

    ddboostStatsTotalBytesReadCount OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Total bytes read count"
        ::= { ddboostStatsEntry 17 }

    ddboostStatsTotalBytesReadErrors OBJECT-TYPE
        SYNTAX  DDMibTrafficBytesTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Total bytes read errors"
        ::= { ddboostStatsEntry 18 }

-- **********************************************************************
--
-- ddboostProperties 
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     ddboost                      (*******.4.1.19746.1.12)
--       ddboostStorageUnit               (*******.4.1.19746.1.12.4)
--
-- **********************************************************************

    ddboostStorageUnit		    OBJECT IDENTIFIER ::= { ddboost 4 }

     ddboostStorageUnitTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DdboostStorageUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DDBOOST StorageUnit table information for DDBOOST Storage Units"
        ::= { ddboostStorageUnit 1 }

    ddboostStorageUnitEntry OBJECT-TYPE
        SYNTAX  DdboostStorageUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "ddboostStorageUnitTable Row Entry"
        INDEX { ddboostStorageUnitIndex }
        ::= { ddboostStorageUnitTable 1 }

     DdboostStorageUnitEntry ::= SEQUENCE {
        ddboostStorageUnitIndex                       DDMibTableIndexTC,
        ddboostStorageUnitName	                      DDMibTableString64TC,
	ddboostStorageUnitBytes 	              DDMibInteger32TC,
	ddboostStorageUnitGlobalComp                  DDMibInteger32TC,
        ddboostStorageUnitLocalComp	              DDMibInteger32TC,
        ddboostStorageUnitMetaData                    DDMibInteger32TC,
        ddboostStorageUnitStatus                      DDMibTableString64TC,
        ddboostStorageUnitPreComp                     DDMibTableString64TC,
        ddboostStorageUnitUser                        DDMibTableString64TC,
        ddboostStorageUnitReportPhysicalSize          DDMibInteger32TC
    }

     ddboostStorageUnitIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "ddboost StorageUnit Table index"
        ::= { ddboostStorageUnitEntry 1 }

    ddboostStorageUnitName OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Name"
        ::= { ddboostStorageUnitEntry 2 }

    ddboostStorageUnitBytes OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Original MiB."
        ::= { ddboostStorageUnitEntry 3 }

    ddboostStorageUnitGlobalComp OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Globally Compressed MiB."
        ::= { ddboostStorageUnitEntry 4 }

    ddboostStorageUnitLocalComp OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Locally Compressed MiB."
        ::= { ddboostStorageUnitEntry 5 }

    ddboostStorageUnitMetaData OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Meta Data MiB."
        ::= { ddboostStorageUnitEntry 6 }

    ddboostStorageUnitStatus OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Status."
        ::= { ddboostStorageUnitEntry 7 }

    ddboostStorageUnitPreComp OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Pre-Compression Rate."
        ::= { ddboostStorageUnitEntry 8 }

    ddboostStorageUnitUser OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The User of the DDboost Storage Unit."
        ::= { ddboostStorageUnitEntry 9 }

    ddboostStorageUnitReportPhysicalSize OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DDboost Storage Unit Report Physical Size."
        ::= { ddboostStorageUnitEntry 10 }

-- **********************************************************************
--
-- systemLicense
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--      dataDomainSystem                (*******.4.1.19746.1.13)
--             systemLicense              (*******.4.1.19746.1.13.4)
--
-- **********************************************************************


    systemLicense      OBJECT IDENTIFIER ::= { dataDomainSystem 4 }

    systemLicenseTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemLicenseEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemLicenseEntry."
        ::= { systemLicense 1 }

    systemLicenseEntry OBJECT-TYPE
        SYNTAX  SystemLicenseEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "systemLicenseTable Row Description"
        INDEX { systemLicenseIndex }
        ::= { systemLicenseTable 1 }

    SystemLicenseEntry ::= SEQUENCE {
        systemLicenseIndex          DDMibTableIndexTC,
        systemLicenseKey            DDMibTableString256TC,
        systemLicenseFeature        DDMibTableString64TC
    }

    systemLicenseIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "System License Row index"
        ::= { systemLicenseEntry 1 }

    systemLicenseKey OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "System License Key"
        ::= { systemLicenseEntry 2 }

    systemLicenseFeature OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Feature for the license"
        ::= { systemLicenseEntry 3 }



-- **********************************************************************
--
-- systemLicense
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--      dataDomainSystem                (*******.4.1.19746.1.13)
--          systemLicense               (*******.4.1.19746.1.13.4)
--             systemCapacityLicense    (*******.4.1.19746.********)
--
-- **********************************************************************
     systemCapacityLicense OBJECT IDENTIFIER ::= { systemLicense 2 }


    systemCapacityLicenseTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemCapacityLicenseEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of systemCapacityLicenseEntry."
        ::= { systemCapacityLicense 1 }

    systemCapacityLicenseEntry OBJECT-TYPE
        SYNTAX  SystemCapacityLicenseEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "systemCapacityLicenseTable Row Description"
        INDEX { systemCapacityLicenseIndex }
        ::= { systemCapacityLicenseTable 1 }

    SystemCapacityLicenseEntry ::= SEQUENCE {
        systemCapacityLicenseIndex          DDMibTableIndexTC,
        systemCapacityLicenseKey            DDMibTableString256TC,
        systemCapacityLicenseFeature        DDMibTableString64TC,
        systemCapacityLicenseModel          DDMibTableString32TC,
        systemCapacityLicenseCapacity       DDMibTableString32TC
    }

    systemCapacityLicenseIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "systemCapacity License Row index"
        ::= { systemCapacityLicenseEntry 1 }

    systemCapacityLicenseKey OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "systemCapacity License Key"
        ::= { systemCapacityLicenseEntry 2 }

    systemCapacityLicenseFeature OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Capacity Feature for the license"
        ::= { systemCapacityLicenseEntry 3 }

    systemCapacityLicenseModel OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Model for Feature of the license"
        ::= { systemCapacityLicenseEntry 4 }


     systemCapacityLicenseCapacity OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Capacity of the model"
        ::= { systemCapacityLicenseEntry 5 }


-- **********************************************************************
--
-- systemHardware 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	dataDomainSystem		(*******.4.1.19746.1.13)
--	       systemHardware           (*******.4.1.19746.********)
--
-- **********************************************************************


    systemHardware		OBJECT IDENTIFIER ::= { dataDomainSystem 2 }

    systemHardwareTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemHardwareEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemHardwareEntry."
        ::= { systemHardware 1 }

    systemHardwareEntry OBJECT-TYPE
        SYNTAX  SystemHardwareEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "systemHardwareTable Row Description" 
        INDEX { systemHardwareIndex }
        ::= { systemHardwareTable 1 }

    SystemHardwareEntry ::= SEQUENCE {
	systemHardwareIndex	    DDMibTableIndexTC,
	systemHardwareSlot	    DDMibInteger32TC,
	systemHardwareVendor        DDMibTableString64TC,
	systemHardwareDevice        DDMibTableString128TC,
	systemHardwarePorts	    DDMibTableString128TC,
        systemHardwareSlotName      DisplayString
    }

    systemHardwareIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "System Hardware Row index"
        ::= { systemHardwareEntry 1 }

    systemHardwareSlot OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  deprecated
        DESCRIPTION
            "System Hardware Slot"
        ::= { systemHardwareEntry 2 }

    systemHardwareVendor OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Slot Device Vendor"
        ::= { systemHardwareEntry 3 }

    systemHardwareDevice OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Slot Device"
        ::= { systemHardwareEntry 4 }
    
    systemHardwarePorts OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Slot Ports"
        ::= { systemHardwareEntry 5 }


    systemHardwareSlotName OBJECT-TYPE
        SYNTAX  DisplayString (SIZE (0..3))
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "System Hardware Slot Name"
        ::= { systemHardwareEntry 6 }


-- **********************************************************************
--
-- systemPorts 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	dataDomainSystem		(*******.4.1.19746.1.13)
--	       systemPorts              (*******.4.1.19746.********)
--
-- **********************************************************************


    systemPorts		OBJECT IDENTIFIER ::= { dataDomainSystem 3 }

    systemPortsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemPortsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemPortsEntry."
        ::= { systemPorts 1 }

    systemPortsEntry OBJECT-TYPE
        SYNTAX  SystemPortsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "systemPortsTable Row Description" 
        INDEX { systemPortsIndex }
        ::= { systemPortsTable 1 }

    SystemPortsEntry ::= SEQUENCE {
	systemPortsIndex	    DDMibTableIndexTC,
	systemPortsPort	            DDMibTableString32TC,
	systemPortsConnectionType   DDMibTableString64TC,
	systemPortsLinkSpeed        DDMibTableString128TC,
	systemPortsFirmware	    DDMibTableString128TC,
        systemPortsHardwareAddress  DDMibTableString64TC
    }

    systemPortsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "System Ports Row index"
        ::= { systemPortsEntry 1 }

    systemPortsPort OBJECT-TYPE
        SYNTAX  DDMibTableString32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "System Ports Port name"
        ::= { systemPortsEntry 2 }

    systemPortsConnectionType OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Port LinkSpeed ConnectionType"
        ::= { systemPortsEntry 3 }

    systemPortsLinkSpeed OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "system Port LinkSpeed"
        ::= { systemPortsEntry 4 }
    
    systemPortsFirmware OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "system ports firmware version"
        ::= { systemPortsEntry 5 }

    systemPortsHardwareAddress OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "System Port Hardware Address."
        ::= { systemPortsEntry 6 }



    
-- **********************************************************************
--
-- systemProperties 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	dataDomainSystem		(*******.4.1.19746.1.13)
--	       systemProperties         (*******.4.1.19746.********)
--
-- **********************************************************************

    systemProperties	    OBJECT IDENTIFIER ::= { dataDomainSystem 1 }

    systemSerialNumber OBJECT-TYPE
        SYNTAX  SystemSerialNumberTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Serial Number of the System"
        ::= { systemProperties 1 }
   
   
    systemCurrentTime OBJECT-TYPE
        SYNTAX   DDMibTimeStampTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "DDR system's current time"
        ::= { systemProperties 2 }
   
   
    systemVersion OBJECT-TYPE
        SYNTAX  DDMibVersionTC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "DD Version of the System"
        ::= { systemProperties 3 }
   
   
    systemModelNumber OBJECT-TYPE
        SYNTAX   DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Model Number of the System"
        ::= { systemProperties 4 }
   
    systemTimeZoneName OBJECT-TYPE
        SYNTAX   SystemTimeZoneNameTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "DDR system's time zone name"
        ::= { systemProperties 5 }   

    sysNotes OBJECT-TYPE
        SYNTAX  SystemNotesTC 
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION "Customer defined notes associated with this DD System."
        ::= { systemProperties 6 }

-- **********************************************************************
--
-- SystemUser
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     dataDomainSystem                 (*******.4.1.19746.1.13)
--       SystemUser                     (*******.4.1.19746.1.13.5)
--
-- **********************************************************************


    systemUser          OBJECT IDENTIFIER ::= { dataDomainSystem 5 }

    systemUserTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemUsers."
        ::= { systemUser 1 }

    systemUserEntry OBJECT-TYPE
        SYNTAX  SystemUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "SystemUserTable Row Description"
        INDEX { systemUserIndex }
        ::= { systemUserTable 1 }

    SystemUserEntry ::= SEQUENCE {
        systemUserIndex      DDMibTableIndexTC,
        systemUserName       DDMibTableString128TC,
        systemUserUID              DDMibInteger32TC,
        systemUserRole             DDMibTableString32TC,
        systemUserStatus           DDMibTableString32TC
    }

    systemUserIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "SystemUser Row index"
        ::= { systemUserEntry 1 }

    systemUserName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemUser Name "
        ::= { systemUserEntry 2 }

    systemUserUID OBJECT-TYPE
        SYNTAX DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemUser UID"
        ::= { systemUserEntry 3 }

    systemUserRole OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemUser Role"
        ::= { systemUserEntry 4 }

    systemUserStatus OBJECT-TYPE
        SYNTAX DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemUsers Status "
        ::= { systemUserEntry 5 }


    systemActiveUserTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SystemActiveUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of SystemActiveUsers."
        ::= { systemUser 2 }

    systemActiveUserEntry OBJECT-TYPE
        SYNTAX  SystemActiveUserEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "SystemActiveUserTable Row Description"
        INDEX { systemActiveUserIndex }
        ::= { systemActiveUserTable 1 }

    SystemActiveUserEntry ::= SEQUENCE {
        systemActiveUserIndex       DDMibTableIndexTC,
        systemActiveUserName        DDMibTableString128TC,
        systemActiveUserIdleTime    DDMibTableString32TC,
        systemActiveUserLoginTime   DDMibTableString32TC,
        systemActiveUserLoginFrom   DDMibTableString32TC,
        systemActiveUserTty         DDMibTableString32TC
    }

    systemActiveUserIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "SystemActiveUser Row index"
        ::= { systemActiveUserEntry 1 }

    systemActiveUserName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemActiveUser Name "
        ::= { systemActiveUserEntry 2 }

    systemActiveUserIdleTime OBJECT-TYPE
        SYNTAX DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemActiveUser idle time"
        ::= { systemActiveUserEntry 3 }

    systemActiveUserLoginTime OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemActiveUser login time"
        ::= { systemActiveUserEntry 4 }
        
    systemActiveUserLoginFrom OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemActiveUser login from"
        ::= { systemActiveUserEntry 5 }

    systemActiveUserTty OBJECT-TYPE
        SYNTAX DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "SystemActiveUsers Tty"
        ::= { systemActiveUserEntry 6 }


-- *****:*****************************************************************
--
-- managedSystem
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--          ddms                        (*******.4.1.19746.1.19)
--             taskHistory              (*******.4.1.19746.1.19.2)
--
-- **********************************************************************


    taskHistory                 OBJECT IDENTIFIER ::= { ddms 2 }

    taskHistoryTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TaskHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of TaskHistoryEntry."
        ::= { taskHistory 1 }

    taskHistoryEntry OBJECT-TYPE
        SYNTAX  TaskHistoryEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "taskHistoryEntry Row Description" 
        INDEX { taskHistoryIndex }
        ::= { taskHistoryTable 1 }

    TaskHistoryEntry ::= SEQUENCE {
	taskHistoryIndex        DDMibTableIndexTC,
	taskHistoryUser         DDMibTableString64TC,
	taskHistoryID           DDMibTableString64TC,
        taskHistoryParent       DDMibTableString64TC,
	taskHistoryName         DDMibTableString64TC,
	taskHistoryState        DDMibTableString64TC,
	taskHistoryStartTime    DDMibTableString64TC,
	taskHistoryDuration     DDMibTableString64TC
    }

    taskHistoryIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Task History Row index"
        ::= { taskHistoryEntry 1 }

    taskHistoryUser OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History User"
        ::= { taskHistoryEntry 2 }

    taskHistoryID OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History ID"
        ::= { taskHistoryEntry 3 }

    taskHistoryParent OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History Parent"
        ::= { taskHistoryEntry 4 }

    taskHistoryName OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History Name"
        ::= { taskHistoryEntry 5 }

    taskHistoryState OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History State"
        ::= { taskHistoryEntry 6 }

    taskHistoryStartTime OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History Start Time"
        ::= { taskHistoryEntry 7 }

    taskHistoryDuration OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task History Duration"
        ::= { taskHistoryEntry 8 }

-- *****:*****************************************************************
--
-- managedSystem
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--          ddms                        (*******.4.1.19746.1.19)
--             taskActive               (*******.4.1.19746.1.19.3)
--
-- **********************************************************************


    taskActive                 OBJECT IDENTIFIER ::= { ddms 3 }

    taskActiveTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TaskActiveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of TaskActiveEntry."
        ::= { taskActive 1 }

    taskActiveEntry OBJECT-TYPE
        SYNTAX  TaskActiveEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "taskActiveEntry Row Description" 
        INDEX { taskActiveIndex }
        ::= { taskActiveTable 1 }

    TaskActiveEntry ::= SEQUENCE {
	taskActiveIndex        DDMibTableIndexTC,
	taskActiveUser         DDMibTableString64TC,
	taskActiveID           DDMibTableString64TC,
        taskActiveParent       DDMibTableString64TC,
	taskActiveName         DDMibTableString64TC,
	taskActiveState        DDMibTableString64TC,
	taskActiveStartTime    DDMibTableString64TC,
	taskActiveDuration     DDMibTableString64TC
    }

    taskActiveIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Task Active Row index"
        ::= { taskActiveEntry 1 }

    taskActiveUser OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active User"
        ::= { taskActiveEntry 2 }

    taskActiveID OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active ID"
        ::= { taskActiveEntry 3 }

    taskActiveParent OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active Parent"
        ::= { taskActiveEntry 4 }

    taskActiveName OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active Name"
        ::= { taskActiveEntry 5 }

    taskActiveState OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active State"
        ::= { taskActiveEntry 6 }

    taskActiveStartTime OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active Start Time"
        ::= { taskActiveEntry 7 }

    taskActiveDuration OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Task Active Duration"
        ::= { taskActiveEntry 8 }

-- **********************************************************************
--
-- artConfig
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     art                     (*******.4.1.19746.1.14)
--       artConfig              (*******.4.1.19746.1.14.1)
--
-- **********************************************************************

    artConfig		    OBJECT IDENTIFIER ::= { art 1 }

    artConfigTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ArtConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of artConfigEntry."
        ::= { artConfig 1 }

    artConfigEntry OBJECT-TYPE
        SYNTAX  ArtConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "artConfigTable Row Description"
        INDEX { artConfigIndex }
        ::= { artConfigTable 1 }

    ArtConfigEntry ::= SEQUENCE {
        artConfigIndex                  DDMibTableIndexTC,
        artConfigStatus                 DDMibTableEnabledTC,
        artConfigMigrationSchedule      DDMibTableString128TC,
        artConfigDefaultAge             DDMibInteger32TC,
        artConfigFileSystemClean           DDMibTableEnabledTC,
        artConfigCompression            DDMibTableString32TC 
        
    }

    artConfigIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "art Config index"
        ::= { artConfigEntry 1 }

    artConfigStatus OBJECT-TYPE
        SYNTAX  DDMibTableEnabledTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art Config Status"
        ::= { artConfigEntry 2 }

    artConfigMigrationSchedule OBJECT-TYPE
        SYNTAX  DDMibTableString128TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art Config Migration Schedule"
        ::= { artConfigEntry 3 }

    artConfigDefaultAge OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art Config Default Age threshold migration policy"
        ::= { artConfigEntry 4 }

    artConfigFileSystemClean OBJECT-TYPE
        SYNTAX  DDMibTableEnabledTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art Config file System Clean Required"
        ::= { artConfigEntry 5 }

    
    artConfigCompression OBJECT-TYPE
        SYNTAX  DDMibTableString32TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art Config local compression"
        ::= { artConfigEntry 6 }

    


-- **********************************************************************
--
-- artMigrationPolicy
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     art                     (*******.4.1.19746.1.14)
--       artMigrationPolicy              (*******.4.1.19746.1.14.3)
--
-- **********************************************************************

    artMigrationPolicy		    OBJECT IDENTIFIER ::= { art 3 }

    artMigrationPolicyTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ArtMigrationPolicyEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of artMigrationPolicyEntry."
        ::= { artMigrationPolicy 1 }

    artMigrationPolicyEntry OBJECT-TYPE
        SYNTAX  ArtMigrationPolicyEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "artMigrationPolicyTable Row Description"
        INDEX { artMigrationPolicyIndex }
        ::= { artMigrationPolicyTable 1 }

    ArtMigrationPolicyEntry ::= SEQUENCE {
        artMigrationPolicyIndex                  DDMibTableIndexTC,
        artMigrationPolicyMtreeName              DDMibTableString256TC,
        artMigrationPolicyDefaultAge             DDMibInteger32TC 
    }

    artMigrationPolicyIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "art MigrationPolicy index"
        ::= { artMigrationPolicyEntry 1 }

    artMigrationPolicyMtreeName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art MigrationPolicy Mtree Name"
        ::= { artMigrationPolicyEntry 2 }

    artMigrationPolicyDefaultAge OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art MigrationPolicy Default Age"
        ::= { artMigrationPolicyEntry 3 }

-- **********************************************************************
--
-- artMigrationSchedule
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     art                     (*******.4.1.19746.1.14)
--       artMigrationSchedule             (*******.4.1.19746.1.14.2)
--
-- **********************************************************************

    artMigrationSchedule		    OBJECT IDENTIFIER ::= { art 2 }

    artMigrationScheduleTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ArtMigrationScheduleEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of artMigrationScheduleEntry."
        ::= { artMigrationSchedule 1 }

    artMigrationScheduleEntry OBJECT-TYPE
        SYNTAX  ArtMigrationScheduleEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "artMigrationScheduleTable Row Description"
        INDEX { artMigrationScheduleIndex }
        ::= { artMigrationScheduleTable 1 }

    ArtMigrationScheduleEntry ::= SEQUENCE {
        artMigrationScheduleIndex                  DDMibTableIndexTC,
        artMigrationScheduleSchedule               DDMibTableString512TC,
        artMigrationScheduleStatus                 DDMibStatusTC
    }

    artMigrationScheduleIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "art MigrationScheduleindex"
        ::= { artMigrationScheduleEntry 1 }

    artMigrationScheduleSchedule OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art MigrationSchedule Schedule"
        ::= { artMigrationScheduleEntry 2 }

    artMigrationScheduleStatus OBJECT-TYPE
        SYNTAX  DDMibStatusTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "art MigrationSchedule Status"
        ::= { artMigrationScheduleEntry 3 }

-- **********************************************************************
--
-- mtreeCompression
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     mtree                     (*******.4.1.19746.1.15)
--       mtreeCompression              (*******.4.1.19746.1.15.1)
--
-- **********************************************************************

    mtreeCompression		    OBJECT IDENTIFIER ::= { mtree 1 }

    mtreeCompressionTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF MtreeCompressionEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of mtreeCompressionEntry."
        ::= { mtreeCompression 1 }

    mtreeCompressionEntry OBJECT-TYPE
        SYNTAX  MtreeCompressionEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "mtreeCompressionTable Row Description"
        INDEX { mtreeCompressionIndex }
        ::= { mtreeCompressionTable 1 }

    MtreeCompressionEntry ::= SEQUENCE {
        mtreeCompressionIndex                  DDMibTableIndexTC,
        mtreeCompressionMtreePath              DDMibTableString512TC,
        mtreeCompressionPreCompGib             DDMibTableSizeGibTC,
        mtreeCompressionPostCompGib            DDMibTableSizeGibTC,
        mtreeCompressionGlobalCompFactor       DDMibCompressionFactorTC,
        mtreeCompressionLocalCompFactor        DDMibCompressionFactorTC,
        mtreeCompressionPostTotalCompFactor    DDMibCompressionFactorTC,
        mtreeCompressionTimePeriod             DDMibTableString128TC
    }

    mtreeCompressionIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "mtree Compression index"
        ::= { mtreeCompressionEntry 1 }

    mtreeCompressionMtreePath OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Mtree Path"
        ::= { mtreeCompressionEntry 2 }

    mtreeCompressionPreCompGib OBJECT-TYPE
        SYNTAX  DDMibTableSizeGibTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Pre comp giga bytes"
        ::= { mtreeCompressionEntry 3 }

     mtreeCompressionPostCompGib OBJECT-TYPE
        SYNTAX  DDMibTableSizeGibTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "mtree Compression Post Comp Giga Bytes"
        ::= { mtreeCompressionEntry 4 }

    mtreeCompressionGlobalCompFactor OBJECT-TYPE
        SYNTAX  DDMibCompressionFactorTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Global Comp Factor"
        ::= { mtreeCompressionEntry 5 }

    mtreeCompressionLocalCompFactor OBJECT-TYPE
        SYNTAX  DDMibCompressionFactorTC
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Local Comp Factor"
        ::= { mtreeCompressionEntry 6 }

    mtreeCompressionPostTotalCompFactor OBJECT-TYPE
        SYNTAX  DDMibCompressionFactorTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Total Comp Factor"
        ::= { mtreeCompressionEntry 7 }

    
    mtreeCompressionTimePeriod    OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree Compression Time Period"
        ::= { mtreeCompressionEntry 8 }

    
-- **********************************************************************
--
-- mtreeList
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     mtree                     (*******.4.1.19746.1.15)
--       mtreeList              (*******.4.1.19746.1.15.2)
--
-- **********************************************************************

    mtreeList		    OBJECT IDENTIFIER ::= { mtree 2 }

    mtreeListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF MtreeListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of mtreeListEntry."
        ::= { mtreeList 1 }

    mtreeListEntry OBJECT-TYPE
        SYNTAX  MtreeListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "mtreeListTable Row Description"
        INDEX { mtreeListIndex }
        ::= { mtreeListTable 1 }

    MtreeListEntry ::= SEQUENCE {
        mtreeListIndex                  DDMibTableIndexTC,
        mtreeListMtreeName              DDMibTableString512TC,
        mtreeListPreCompGib             DDMibTableSizeGibTC,
        mtreeListStatus                 MtreeListStatusTC
    }

    mtreeListIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "mtree List index"
        ::= { mtreeListEntry 1 }

    mtreeListMtreeName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree List Mtree Name"
        ::= { mtreeListEntry 2 }

    mtreeListPreCompGib OBJECT-TYPE
        SYNTAX  DDMibTableSizeGibTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree List Pre comp giga bytes"
        ::= { mtreeListEntry 3 }

     mtreeListStatus OBJECT-TYPE
        SYNTAX  MtreeListStatusTC
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "mtree List Status"
        ::= { mtreeListEntry 4 }


    
-- **********************************************************************
--
-- mtreeRetentionLock
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     mtree                     (*******.4.1.19746.1.15)
--       mtreeRetentionLock              (*******.4.1.19746.1.15.4)
--
-- **********************************************************************

    mtreeRetentionLock		    OBJECT IDENTIFIER ::= { mtree 4 }

    mtreeRetentionLockTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF MtreeRetentionLockEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of mtreeRetentionLockEntry."
        ::= { mtreeRetentionLock 1 }

    mtreeRetentionLockEntry OBJECT-TYPE
        SYNTAX  MtreeRetentionLockEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "mtreeRetentionLockTable Row Description"
        INDEX { mtreeRetentionLockIndex }
        ::= { mtreeRetentionLockTable 1 }

    MtreeRetentionLockEntry ::= SEQUENCE {
        mtreeRetentionLockIndex                  DDMibTableIndexTC,
        mtreeRetentionLockMtreeName              DDMibTableString512TC,
        mtreeRetentionLockStatus                 MtreeRetentionLockStatusTC,
        mtreeRetentionLockUUID                   DDMibTableString32TC,
        mtreeRetentionLockMinRetentionPeriod     DDMibTableString32TC,
        mtreeRetentionLockMaxRetentionPeriod     DDMibTableString32TC
    }

    mtreeRetentionLockIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock index"
        ::= { mtreeRetentionLockEntry 1 }

    mtreeRetentionLockMtreeName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock Mtree Name"
        ::= { mtreeRetentionLockEntry 2 }

    mtreeRetentionLockStatus OBJECT-TYPE
        SYNTAX  MtreeRetentionLockStatusTC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock Status"
        ::= { mtreeRetentionLockEntry 3 }

     mtreeRetentionLockUUID OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock UUID"
        ::= { mtreeRetentionLockEntry 4 }

    mtreeRetentionLockMinRetentionPeriod OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock Minimum Retention Period"
        ::= { mtreeRetentionLockEntry 5 }

    mtreeRetentionLockMaxRetentionPeriod OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "mtree RetentionLock Maximum Retention Period"
        ::= { mtreeRetentionLockEntry 6 }

    
-- **********************************************************************
--
-- systemHardware 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	enclosure       		(*******.4.1.19746.1.17)
--	       enclosureList            (*******.4.1.19746.********)
--
-- **********************************************************************


    enclosureList       		OBJECT IDENTIFIER ::= { enclosure 1 }

    enclosureListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF EnclosureListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of EnclosureListEntry."
        ::= { enclosureList 1 }

    enclosureListEntry OBJECT-TYPE
        SYNTAX  EnclosureListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "enclosure List Row Description" 
        INDEX { enclosureListIndex }
        ::= { enclosureListTable 1 }

    EnclosureListEntry ::= SEQUENCE {
	enclosureListIndex	    DDMibTableIndexTC,
	enclosureListNum	    DDMibInteger32TC,
	enclosureListModel          DDMibTableString64TC,
	enclosureListSerialNum      DDMibTableString128TC,
	enclosureListOemName	    DDMibTableString128TC,
        enclosureListOemValue       DDMibTableString128TC,
        enclosureListCapacity       DDMibTableString64TC
    }

    enclosureListIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "enclosure List Row index"
        ::= { enclosureListEntry 1 }

    enclosureListNum OBJECT-TYPE
        SYNTAX  DDMibInteger32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "enclosure Number"
        ::= { enclosureListEntry 2 }

    enclosureListModel OBJECT-TYPE
        SYNTAX  DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "enclosure model"
        ::= { enclosureListEntry 3 }

    enclosureListSerialNum OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "enclosure serial number"
        ::= { enclosureListEntry 4 }
    
    enclosureListOemName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "enclosure oem name"
        ::= { enclosureListEntry 5 }

    enclosureListOemValue OBJECT-TYPE
       SYNTAX  DDMibTableString128TC
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
       "enclosure oem value"
       ::= { enclosureListEntry 6 }

    enclosureListCapacity OBJECT-TYPE
       SYNTAX  DDMibTableString64TC
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
       "enclosure Capacity"
       ::= { enclosureListEntry 7 }
                                             
    enclosurePack       		OBJECT IDENTIFIER ::= { enclosure 2 }

    enclosurePackTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF EnclosurePackEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of EnclosurePackEntry."
        ::= { enclosurePack 1 }

    enclosurePackEntry OBJECT-TYPE
        SYNTAX  EnclosurePackEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "enclosure Pack Row Description" 
        INDEX { enclosureListIndex, enclosurePackID }
        ::= { enclosurePackTable 1 }

    EnclosurePackEntry ::= SEQUENCE {
	enclosurePackID             DDMibTableIndexTC
    }

    enclosurePackID OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Pack information for the enclosure.
	    Applicable to enclosures with packs such as ES60, and not for ES20 or ES30."
        ::= { enclosurePackEntry 1 }

    restorer            	OBJECT IDENTIFIER ::= { dataDomainMibProducts 1 }
-- generated platform section for Data Domain MIB
  unknown    OBJECT IDENTIFIER ::= { restorer 0 }
  dd200    OBJECT IDENTIFIER ::= { restorer 1 }
  dd200Proto    OBJECT IDENTIFIER ::= { restorer 2 }
  dd410    OBJECT IDENTIFIER ::= { restorer 3 }
  dd430    OBJECT IDENTIFIER ::= { restorer 4 }
  dd460    OBJECT IDENTIFIER ::= { restorer 5 }
  dd400g    OBJECT IDENTIFIER ::= { restorer 6 }
  dd460g    OBJECT IDENTIFIER ::= { restorer 7 }
  dd560    OBJECT IDENTIFIER ::= { restorer 8 }
  dd560g    OBJECT IDENTIFIER ::= { restorer 9 }
  dd580    OBJECT IDENTIFIER ::= { restorer 10 }
  dd580g    OBJECT IDENTIFIER ::= { restorer 11 }
  dd565    OBJECT IDENTIFIER ::= { restorer 12 }
  dd530    OBJECT IDENTIFIER ::= { restorer 13 }
  dd510    OBJECT IDENTIFIER ::= { restorer 14 }
  dd120    OBJECT IDENTIFIER ::= { restorer 15 }
  dd690    OBJECT IDENTIFIER ::= { restorer 16 }
  dd690g    OBJECT IDENTIFIER ::= { restorer 17 }
  dd660    OBJECT IDENTIFIER ::= { restorer 18 }
  dd880    OBJECT IDENTIFIER ::= { restorer 19 }
  dd880g    OBJECT IDENTIFIER ::= { restorer 20 }
  dd610    OBJECT IDENTIFIER ::= { restorer 21 }
  dd630    OBJECT IDENTIFIER ::= { restorer 22 }
  dd140    OBJECT IDENTIFIER ::= { restorer 23 }
  dd670    OBJECT IDENTIFIER ::= { restorer 24 }
  dd860    OBJECT IDENTIFIER ::= { restorer 25 }
  dd860g    OBJECT IDENTIFIER ::= { restorer 26 }
  dd890    OBJECT IDENTIFIER ::= { restorer 27 }
  dd640    OBJECT IDENTIFIER ::= { restorer 28 }
  dd620    OBJECT IDENTIFIER ::= { restorer 29 }
  dd160    OBJECT IDENTIFIER ::= { restorer 30 }
  ddintrepid    OBJECT IDENTIFIER ::= { restorer 31 }
  dd4500    OBJECT IDENTIFIER ::= { restorer 32 }
  dd7200    OBJECT IDENTIFIER ::= { restorer 33 }
  ddve    OBJECT IDENTIFIER ::= { restorer 34 }
  dd990    OBJECT IDENTIFIER ::= { restorer 35 }
  dd2500    OBJECT IDENTIFIER ::= { restorer 36 }
  dd4200    OBJECT IDENTIFIER ::= { restorer 37 }
  ddkoalam1    OBJECT IDENTIFIER ::= { restorer 38 }
  apollo    OBJECT IDENTIFIER ::= { restorer 39 }
  unset    OBJECT IDENTIFIER ::= { restorer 9999 }
-- **********************************************************************
--
-- systemHardware 
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	network       		(*******.4.1.19746.1.18)
--	       dns            (*******.4.1.19746.********)
--
-- **********************************************************************


    dns       		OBJECT IDENTIFIER ::= { network 1 }

    dnsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF DNSEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of dnsEntry."
        ::= { dns 1 }

    dnsEntry OBJECT-TYPE
        SYNTAX  DNSEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "DNS Table Row Description" 
        INDEX { dnsIndex }
        ::= { dnsTable 1 }

    DNSEntry ::= SEQUENCE {
	dnsIndex	    DDMibTableIndexTC,
	dnsServer	    DDMibTableString32TC
    }

    dnsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "DNS Table Row index"
        ::= { dnsEntry 1 }

    dnsServer OBJECT-TYPE
        SYNTAX  DDMibTableString32TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "DNS Server"
        ::= { dnsEntry 2 }

                                                         
-- **********************************************************************
--
--  searchDomains
-- ==================
--
-- dataDomainMib        		(*******.4.1.19746)
--   dataDomainMibObjects		(*******.4.1.19746.1)
--	network       		(*******.4.1.19746.1.18)
--	       searchDomains            (*******.4.1.19746.********)
--
-- **********************************************************************


    searchDomains       		OBJECT IDENTIFIER ::= { network 2 }

    searchDomainsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SearchDomainsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of searchDomainsEntry."
        ::= { searchDomains 1 }

    searchDomainsEntry OBJECT-TYPE
        SYNTAX  SearchDomainsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION 
	    "searchDomains Table Row Description" 
        INDEX { searchDomainsIndex }
        ::= { searchDomainsTable 1 }

    SearchDomainsEntry ::= SEQUENCE {
	searchDomainsIndex	    DDMibTableIndexTC,
	searchDomainsName	    DDMibTableString128TC
    }

    searchDomainsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "searchDomains Table Row index"
        ::= { searchDomainsEntry 1 }

    searchDomainsName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC 
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "searchDomains Name"
        ::= { searchDomainsEntry 2 }

                                                         
-- **********************************************************************
--
--  snmpTrapHosts
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--      network                         (*******.4.1.19746.1.18)
--             snmpTrapHosts            (*******.4.1.19746.********)
--
-- **********************************************************************


    snmpTrapHosts                       OBJECT IDENTIFIER ::= { network 3 }

    snmpTrapHostsTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF SnmpTrapHostsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of snmp Trap Hosts."
        ::= { snmpTrapHosts 1 }

    snmpTrapHostsEntry OBJECT-TYPE
        SYNTAX  SnmpTrapHostsEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "snmpTrapHosts Table Row Description"
        INDEX { snmpTrapHostsIndex }
        ::= { snmpTrapHostsTable 1 }

    SnmpTrapHostsEntry ::= SEQUENCE {
        snmpTrapHostsIndex          DDMibTableIndexTC,
        snmpTrapHostsName           DDMibTableString256TC,
        snmpTrapHostsVersion        DDMibTableString32TC
    }

    snmpTrapHostsIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "snmpTrapHosts Table Row index"
        ::= { snmpTrapHostsEntry 1 }

    snmpTrapHostsName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "snmp Trap Hosts Name"
        ::= { snmpTrapHostsEntry 2 }

    snmpTrapHostsVersion OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "snmp Trap Hosts Version"
        ::= { snmpTrapHostsEntry 3 }

-- **********************************************************************
--
-- nis
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     network                  (*******.4.1.19746.1.18)
--       nis                    (*******.4.1.19746.********)
--
-- **********************************************************************


    nis		    OBJECT IDENTIFIER ::= { network 4 }


    nisDomain OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS domain"
        ::= { nis 1 }

    nisServers OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS servers"
        ::= { nis 2 }

    nisAdminGroups OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS admin groups"
        ::= { nis 3 }

    nisUserGroups OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS user groups"
        ::= { nis 4 }

    nisBackupOperatorGroups OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS backup operator groups"
        ::= { nis 5 }

    nisEnabled OBJECT-TYPE
        SYNTAX  DDMibTableEnabledTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS enabled or not"
        ::= { nis 6 }

    nisStatus OBJECT-TYPE
        SYNTAX  DDMibTableString1024TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "NIS status"
        ::= { nis 7 }


-- **********************************************************************
--
-- managedSystem
-- ==================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--          ddms                        (*******.4.1.19746.1.19)
--             managedSystem            (*******.4.1.19746.1.19.1)
--
-- **********************************************************************


     managedSystem          OBJECT IDENTIFIER ::= { ddms 1 }

    managedSystemTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ManagedSystemEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "A table containing entries of managed systems."
        ::= { managedSystem 1 }

    managedSystemEntry OBJECT-TYPE
        SYNTAX  ManagedSystemEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "managedSystemTable Row Description"
        INDEX { managedSystemIndex }
        ::= { managedSystemTable 1 }

    ManagedSystemEntry ::= SEQUENCE {
        managedSystemIndex      DDMibTableIndexTC,
        managedSystemHostname   DDMibTableString256TC,
        managedSystemSerial     DDMibTableString32TC, 
        managedSystemState      DDMibTableString32TC,
        managedSystemStatus     DDMibTableString32TC,
        managedSystemDDOSVersion  DDMibTableString32TC,
        managedSystemHDSyncTime  DDMibTableString64TC,
        managedSystemCDSyncTime  DDMibTableString64TC
    }

    managedSystemIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "managed system Row index"
        ::= { managedSystemEntry 1 }

    managedSystemHostname OBJECT-TYPE
        SYNTAX  DDMibTableString256TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system hostname"
        ::= { managedSystemEntry 2 }

    managedSystemSerial OBJECT-TYPE
        SYNTAX   DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system serial number"
        ::= { managedSystemEntry 3 }

    managedSystemState OBJECT-TYPE
        SYNTAX  DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system state"
        ::= { managedSystemEntry 4 }

    managedSystemStatus OBJECT-TYPE
        SYNTAX DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system status"
        ::= { managedSystemEntry 5 }

    managedSystemDDOSVersion OBJECT-TYPE
        SYNTAX DDMibTableString32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system DDOS version"
        ::= { managedSystemEntry 6 }

    managedSystemHDSyncTime OBJECT-TYPE
        SYNTAX DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system historial data last sync time"
        ::= { managedSystemEntry 7 }

    managedSystemCDSyncTime OBJECT-TYPE
        SYNTAX DDMibTableString64TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "managed system system current data last sync time"
        ::= { managedSystemEntry 8 }

-- **********************************************************************
--
-- smt
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     smt                      (*******.4.1.19746.1.20)
--       smtProperties          (*******.4.1.19746.1.20.1)
--
-- **********************************************************************

    smtProperties       OBJECT IDENTIFIER ::= { smt 1 }

    smtStatus OBJECT-TYPE
        SYNTAX  SmtStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The status of the Secure Multi-Tenancy (SMT) feature on a DD 
            System, which enables storage consolidation for multiple tenants on
            the same appliance. Through this feature, data and control paths on
            the DD System are logically secured, isolated and confined, 
            creating secure and isolated logical partitions for each tenant-
            unit within the same appliance."
        ::= { smtProperties 1 }


-- **********************************************************************
--
-- tenantUnitList
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     smt                      (*******.4.1.19746.1.20)
--       tenantUnitList         (*******.4.1.19746.1.20.2)
--
-- **********************************************************************

    tenantUnitList                OBJECT IDENTIFIER ::= { smt 2 }

    tenantUnitListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the tenant-units within a 
            DD System that has the Secure Multi-Tenancy (SMT) feature enabled.
            It includes the tenant-unit name, the number of tenant specific 
            Management users that have been added, the number of Mtree(s)/
            DDBoost Storage Units assigned, and whether Tenant-self-service 
            mode is enabled."
        ::= { tenantUnitList 1 }

    tenantUnitListEntry OBJECT-TYPE
        SYNTAX  TenantUnitListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "Information about a tenant-unit within a DD System."
        INDEX { tenantUnitListIdx }
        ::= { tenantUnitListTable 1 }

    TenantUnitListEntry ::= SEQUENCE {
        tenantUnitListIdx                             DDMibTableIndexTC,
        tenantUnitListName                            DDMibTableString256TC,
        tenantUnitListNumberOfMgmtUsers               DDMibInteger32TC,
        tenantUnitListNumberOfMtrees                  DDMibInteger32TC,
        tenantUnitListNumberOfDdboostStus             DDMibInteger32TC,
        tenantUnitListTenantSelfServiceMode           SmtStatusTC,
        tenantUnitListParentTenantName                DDMibTableString256TC,
        tenantUnitListType                            DDMibTableString256TC,
        tenantUnitListSecurityMode                    TenantUnitSecurityModeTC,
        tenantUnitListNumberOfMgmtGroups              DDMibInteger32TC
    }

    tenantUnitListIdx OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index of a tenant-unit (SMT management object). This index 
            uniquely identifies the tenant-unit at a given time."
        ::= { tenantUnitListEntry 1 }

    tenantUnitListName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The name of a tenant-unit. A tenant-unit name consists of letters,
            numbers, no special characters and can have a length of 254
            characters."
        ::= { tenantUnitListEntry 2 }

    tenantUnitListNumberOfMgmtUsers OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The number of Tenant specific Management users which have been 
            added to a tenant-unit."
        ::= { tenantUnitListEntry 3 }

    tenantUnitListNumberOfMtrees OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The number of Mtree(s) which have been assigned to a tenant-unit."
        ::= { tenantUnitListEntry 4 }

    tenantUnitListNumberOfDdboostStus OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The number of DDBoost Storage Units which have been assigned to a
            tenant-unit."
        ::= { tenantUnitListEntry 5 }

     tenantUnitListTenantSelfServiceMode OBJECT-TYPE
        SYNTAX  SmtStatusTC
        MAX-ACCESS read-only 
        STATUS  current
        DESCRIPTION
            "Status of Tenant Self Service Mode. If enabled, administrative
            access to each tenant is allowed, so that they can monitor and
            configure their data protection implementation, within the bounds
            of their allocated objects and features."
        ::= { tenantUnitListEntry 6 }

    tenantUnitListParentTenantName OBJECT-TYPE
        SYNTAX  DDMibTableString256TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The tenant name that a tenant-unit is associated with.
	    "
        ::= { tenantUnitListEntry 7 }

    tenantUnitListType OBJECT-TYPE
        SYNTAX  DDMibTableString256TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The Mtree type of a tenant-unit.
	     "
        ::= { tenantUnitListEntry 8 }

    tenantUnitListSecurityMode OBJECT-TYPE
        SYNTAX  TenantUnitSecurityModeTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The security mode of a tenant-unit.
	     "
        ::= { tenantUnitListEntry 9 }

    tenantUnitListNumberOfMgmtGroups OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The number of Tenant specific Management groups which have been 
            added to a tenant-unit."
        ::= { tenantUnitListEntry 10 }

    
-- **********************************************************************
--
-- tenantUnitMgmtUserList
-- ====================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     smt                              (*******.4.1.19746.1.20)
--       tenantUnitMgmtUserList         (*******.4.1.19746.1.20.3)
--
-- **********************************************************************

    tenantUnitMgmtUserList          OBJECT IDENTIFIER ::= { smt 3 }

    tenantUnitMgmtUserListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitMgmtUserListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the tenant specific
            management users that have been added to a tenant-unit. Such users
            may be limited by their role and scope to operate a limited set of
            operations only on the objects which are assigned-to/associated
            with their tenant-units. Tenant specific management users may have 
            limited administrative and management capabilities."
        ::= { tenantUnitMgmtUserList 1 }

    tenantUnitMgmtUserListEntry OBJECT-TYPE
        SYNTAX  TenantUnitMgmtUserListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Information about the management users that have been added to a
            tenant-unit."
        INDEX { tenantUnitListIdx, tenantUnitMgmtUserListUserName }
        ::= { tenantUnitMgmtUserListTable 1 }

    TenantUnitMgmtUserListEntry ::= SEQUENCE {
        tenantUnitMgmtUserListUserName                   DDMibString96TC,
        tenantUnitMgmtUserListUserRole                   TenantUnitMgmtUserListUserRoleTC
    }

    tenantUnitMgmtUserListUserName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The name of a management user which has been added to a tenant-
            unit. Users may be limited by their roles and may have limited
            administrative and management capabilities."
        ::= { tenantUnitMgmtUserListEntry 2 }

    tenantUnitMgmtUserListUserRole OBJECT-TYPE
        SYNTAX  TenantUnitMgmtUserListUserRoleTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The role of a management user. Roles include tenant-admin and
            tenant-user."
        ::= { tenantUnitMgmtUserListEntry 3 }


-- **********************************************************************
--
-- tenantUnitMtreeList
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     smt                      (*******.4.1.19746.1.20)
--       tenantUnitMtreeList    (*******.4.1.19746.1.20.4)
--
-- **********************************************************************

    tenantUnitMtreeList                OBJECT IDENTIFIER ::= { smt 4 }

    tenantUnitMtreeListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitMtreeListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the mtree(s) which have been
            assigned to a tenant-unit. Mtree logical quotas for soft-limit and
            hard-limit may be imposed on a tenant-unit and will allow 
            provisioning space for each tenant on a DD System. Data access may
            be contained within the mtrees belonging to an tenant-unit."
        ::= { tenantUnitMtreeList 1 }

    tenantUnitMtreeListEntry OBJECT-TYPE
        SYNTAX  TenantUnitMtreeListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Information about an mtree associated with a tenant-unit within a 
            DD System."
        INDEX { tenantUnitListIdx, tenantUnitMtreeListMtreeName }
        ::= { tenantUnitMtreeListTable 1 }

    TenantUnitMtreeListEntry ::= SEQUENCE {
        tenantUnitMtreeListMtreeName                  DDMibString96TC
    }

    tenantUnitMtreeListMtreeName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of an mtree that has been assigned to a particular 
            tenant-unit. One tenant-unit (SMT management object) may have 
            multiple mtrees associated with it."
        ::= { tenantUnitMtreeListEntry 2 }


-- **********************************************************************
--
-- tenantUnitDdboostStuList
-- ====================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     smt                              (*******.4.1.19746.1.20)
--       tenantUnitDdboostStuList       (*******.4.1.19746.1.20.5)
--
-- **********************************************************************

    tenantUnitDdboostStuList          OBJECT IDENTIFIER ::= { smt 5 }

    tenantUnitDdboostStuListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitDdboostStuListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the DDBoost Storage Units
            that have been assigned to a tenant-unit. Logical quotas for soft-
            limit and hard-limit may be imposed on a tenant-unit and will allow
            provisioning space for each tenant on a DD System. Data access
            may be contained within the DDBoost Storage Units belonging to a
            tenant-unit."
        ::= { tenantUnitDdboostStuList 1 }

    tenantUnitDdboostStuListEntry OBJECT-TYPE
        SYNTAX  TenantUnitDdboostStuListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Information about a DDBoost Storage Unit associated with a tenant-
            unit."
        INDEX { tenantUnitListIdx, tenantUnitDdboostStuListStuName }
        ::= { tenantUnitDdboostStuListTable 1 }

    TenantUnitDdboostStuListEntry ::= SEQUENCE {
        tenantUnitDdboostStuListStuName                   DDMibString96TC
    }

    tenantUnitDdboostStuListStuName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of a DDBoost Storage Unit associated with a tenant-unit.
            One tenant-unit (SMT management object) may have multiple DDBoost 
            Storage Units associated with it."
        ::= { tenantUnitDdboostStuListEntry 2 }


-- **********************************************************************
--
-- tenantUnitAdminIpInfo
-- ====================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     smt                              (*******.4.1.19746.1.20)
--       tenantUnitAdminIpInfo          (*******.4.1.19746.1.20.6)
--
-- **********************************************************************

    tenantUnitAdminIpInfo             OBJECT IDENTIFIER ::= { smt 6 }

    tenantUnitAdminIpInfoTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitAdminIpInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table provides IP address related information
	    about a tenant-unit administrator."
        ::= { tenantUnitAdminIpInfo 1 }

    tenantUnitAdminIpInfoEntry OBJECT-TYPE
        SYNTAX  TenantUnitAdminIpInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "An entry of this table represents one set of 
	    IP information about a tenant-unit administrator."
        INDEX { tenantUnitListIdx, tenantUnitAdminIpInfoAddr }
        ::= { tenantUnitAdminIpInfoTable 1 }

    TenantUnitAdminIpInfoEntry ::= SEQUENCE {
        tenantUnitAdminIpInfoAddr               DDMibString96TC,
        tenantUnitAdminIpInfoType               DDMibString96TC
    }

    tenantUnitAdminIpInfoAddr OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The IP address (IPv4/IPv6) of a tenant-unit administrator."
        ::= { tenantUnitAdminIpInfoEntry 2 }

    tenantUnitAdminIpInfoType OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The IP addresss type of a tenant-unit administrator.
	    Possible values: local or remote."
        ::= { tenantUnitAdminIpInfoEntry 3 }


-- **********************************************************************
--
-- tenantInfo
-- ====================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     smt                              (*******.4.1.19746.1.20)
--       tenantInfo                     (*******.4.1.19746.1.20.7)
--
-- **********************************************************************

    tenantInfo                        OBJECT IDENTIFIER ::= { smt 7 }

    tenantInfoTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table provides tenant information
	    for the Secure Multi-Tenancy (SMT) feature.
            "
        ::= { tenantInfo 1 }

    tenantInfoEntry OBJECT-TYPE
        SYNTAX  TenantInfoEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "An entry represents one instance about a tenant
	     that is configured on DD system."
        INDEX { tenantInfoIdx }
        ::= { tenantInfoTable 1 }

    TenantInfoEntry ::= SEQUENCE {
        tenantInfoIdx                          DDMibTableIndexTC,
        tenantInfoTenantName                   DDMibString96TC
    }

    tenantInfoIdx OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index into this table indentifying a particular tenant object."
        ::= { tenantInfoEntry 1 }

    tenantInfoTenantName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of this tenant."
        ::= { tenantInfoEntry 2 }

    tenantInfoTenantUnitTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantInfoTenantUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table provides information about the tenant-unit(s) that
	    are associated with this tenant."
        ::= { tenantInfo 2 }

    tenantInfoTenantUnitEntry OBJECT-TYPE
        SYNTAX  TenantInfoTenantUnitEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "An entry represents information about one tenant-unit
	    that is associated with a particular tenant."
        INDEX { tenantInfoIdx, tenantInfoTenantUnitName }
        ::= { tenantInfoTenantUnitTable 1 }

    TenantInfoTenantUnitEntry ::= SEQUENCE {
        tenantInfoTenantUnitName               DDMibString96TC
    }

    tenantInfoTenantUnitName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of tenant-unit that is associated with this tenant."
        ::= { tenantInfoTenantUnitEntry 2 }

-- **********************************************************************
--
-- tenantUnitMgmtGroupList
-- ====================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibObjects               (*******.4.1.19746.1)
--     smt                              (*******.4.1.19746.1.20)
--       tenantUnitMgmtGroupList        (*******.4.1.19746.1.20.8)
--
-- **********************************************************************

    tenantUnitMgmtGroupList          OBJECT IDENTIFIER ::= { smt 8 }

    tenantUnitMgmtGroupListTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF TenantUnitMgmtGroupListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table contains information about the tenant specific
            management groups that have been added to a tenant-unit. Such groups
            may be limited by their role and scope to operate a limited set of
            operations only on the objects which are assigned-to/associated
            with their tenant-units. Tenant specific management groups may have 
            limited administrative and management capabilities."
        ::= { tenantUnitMgmtGroupList 1 }

    tenantUnitMgmtGroupListEntry OBJECT-TYPE
        SYNTAX  TenantUnitMgmtGroupListEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Information about the management groups that have been added to a
            tenant-unit."
        INDEX { tenantUnitListIdx, tenantUnitMgmtGroupListGroupName }
        ::= { tenantUnitMgmtGroupListTable 1 }

    TenantUnitMgmtGroupListEntry ::= SEQUENCE {
        tenantUnitMgmtGroupListGroupName                    DDMibString96TC,
        tenantUnitMgmtGroupListGroupRole                    TenantUnitMgmtUserListUserRoleTC,
        tenantUnitMgmtGroupListGroupType                    TenantUnitMgmtGroupTypeTC
    }

    tenantUnitMgmtGroupListGroupName OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The name of a management group which has been added to a tenant-
            unit. Groups may be limited by their roles and may have limited
            administrative and management capabilities."
        ::= { tenantUnitMgmtGroupListEntry 2 }

    tenantUnitMgmtGroupListGroupRole OBJECT-TYPE
        SYNTAX  TenantUnitMgmtUserListUserRoleTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The role of a management group. Roles include tenant-admin and
            tenant-group."
        ::= { tenantUnitMgmtGroupListEntry 3 }

    tenantUnitMgmtGroupListGroupType OBJECT-TYPE
        SYNTAX  TenantUnitMgmtGroupTypeTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The type of a management group.  Types include all, local, ad,
	     nis and ldap."
        ::= { tenantUnitMgmtGroupListEntry 4 }


-- **********************************************************************
--
-- quotaProperties
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     quota                    (*******.4.1.19746.1.21)
--       quotaProperties        (*******.4.1.19746.1.21.1)
--
-- **********************************************************************

    quotaProperties       OBJECT IDENTIFIER ::= { quota 1 }

    quotaCapacityStatus OBJECT-TYPE
        SYNTAX  DDStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The status of the quota capacity property on a Data Domain System.
            If enabled quota limits are enforced and administrators can control
            the logical space consumed on a per-Mtree basis."
        ::= { quotaProperties 1 }


-- **********************************************************************
--
-- quotaCapacity
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     quota                    (*******.4.1.19746.1.21)
--       quotaCapacity          (*******.4.1.19746.1.21.2)
--
-- **********************************************************************

    quotaCapacity                OBJECT IDENTIFIER ::= { quota 2 }

    quotaCapacityTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF QuotaCapacityEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table lists quota capacities for Mtrees and Storage-units. It 
            provides information such as the Mtree name, the Pre-Comp (MiB) 
            size, Soft-Limit (MiB), and Hard-Limit (MiB). It is comprised of 
            entries of quotaCapacityEntry."
        ::= { quotaCapacity 1 }

    quotaCapacityEntry OBJECT-TYPE
        SYNTAX  QuotaCapacityEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
        "An entry containing quota information for Mtrees and Storage-units on
        the DD System."
        INDEX { quotaCapacityIndex }
        ::= { quotaCapacityTable 1 }

    QuotaCapacityEntry ::= SEQUENCE {
        quotaCapacityIndex                  DDMibTableIndexTC,
        quotaCapacityMtreeName              DDMibTableString512TC,
        quotaCapacityPreCompMiB             DDMibTableSizeMiBTC,
        quotaCapacitySoftLimitMiB           DDMibTableSizeMiBTC,
        quotaCapacityHardLimitMiB           DDMibTableSizeMiBTC,
        quotaCapacityTenantUnit             DDMibTableString512TC
    }

    quotaCapacityIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index of an Mtree or Storage-unit. This index uniquely 
            identifies the entry at a given time."
        ::= { quotaCapacityEntry 1 }

    quotaCapacityMtreeName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The name of the Mtree or Storage-unit for which quota capacity
            information is provided."
        ::= { quotaCapacityEntry 2 }

    quotaCapacityPreCompMiB OBJECT-TYPE
        SYNTAX  DDMibTableSizeMiBTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "The Pre-Comp (MiB) size of the Mtree or Storage-unit. This is the
            amount of data written before compression."
        ::= { quotaCapacityEntry 3 }

    quotaCapacitySoftLimitMiB OBJECT-TYPE
        SYNTAX  DDMibTableSizeMiBTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Soft-Limit (MiB) quota for the Mtree or Storage-unit. Soft
            quotas provide only an alert when quota limits are reached, and the
            backup job is allowed to complete so that the administrator can
            take corrective actions that are convenient to the organization."
        ::= { quotaCapacityEntry 4 }

    quotaCapacityHardLimitMiB OBJECT-TYPE
        SYNTAX  DDMibTableSizeMiBTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Hard-Limit (MiB) quota for the Mtree or Storage-unit. Hard
            quotas provide a stricter enforcement model than soft quotas by
            failing the backup jobs or I/O when quota limits are reached." 
        ::= { quotaCapacityEntry 5 }

    quotaCapacityTenantUnit OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The Tenant-unit to which this Mtree or Storage-unit is assigned.
            Tenant-units allow for secure and isolated logical partitions 
            within an appliance and are part of the Secure Multi-Tenancy (SMT)
            feature."
        ::= { quotaCapacityEntry 6 }



    
-- **********************************************************************
--
-- highAvailability
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     highAvailability         (*******.4.1.19746.1.22)
--       highAvailabilityStatus (*******.4.1.19746.1.22.1)
--
-- **********************************************************************

    highAvailabilityStatus       OBJECT IDENTIFIER ::= { highAvailability 1 }

    haSystemName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability System Name"
        ::= { highAvailabilityStatus 1 }

    haSystemStatus OBJECT-TYPE
        SYNTAX  HaSystemStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability System Status"
        ::= { highAvailabilityStatus 2 }

    haInterconnectStatus OBJECT-TYPE
        SYNTAX   HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability Interconnect Status"
        ::= { highAvailabilityStatus 3 }

    haPrimaryHeartbeatStatus OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability Primary Heartbeat Status"
        ::= { highAvailabilityStatus 4 }

    haExternalLanHeartbeatStatus OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability External LAN Heartbeat Status"
        ::= { highAvailabilityStatus 5 }

    haHardwareCompatibilityCheck OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability Hardware Compatibility Check"
        ::= { highAvailabilityStatus 6 }

    haSoftwareVersionCheck OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "High Availability Software Version Check"
        ::= { highAvailabilityStatus 7 }


-- **********************************************************************
--
-- highAvailabilityNode
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     highAvailability         (*******.4.1.19746.1.22)
--       highAvailabilityNode   (*******.4.1.19746.1.22.2)
--
-- **********************************************************************

    highAvailabilityNode       OBJECT IDENTIFIER ::= { highAvailability 2 }

    highAvailabilityNodeTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF HighAvailabilityNodeEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table provides high availability node information.
            "
        ::= { highAvailabilityNode 1 }

    highAvailabilityNodeEntry OBJECT-TYPE
        SYNTAX  HighAvailabilityNodeEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "An entry represents one instance about a high availability node.
	    "
        INDEX { highAvailabilityNodeIdx }
        ::= { highAvailabilityNodeTable 1 }

    HighAvailabilityNodeEntry ::= SEQUENCE {
        highAvailabilityNodeIdx                DDMibTableIndexTC,
        highAvailabilityNodeName               DDMibTableString128TC,
        highAvailabilityNodeId                 DDMibTableString128TC,
        highAvailabilityNodeRole               DDMibTableString128TC,
        highAvailabilityNodeState              DDMibTableString128TC,
        highAvailabilityNodeHealth             HaStatusTC
    }

    highAvailabilityNodeIdx OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index into this table indentifying a particular node."
        ::= { highAvailabilityNodeEntry 1 }

    highAvailabilityNodeName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of a high availability node."
        ::= { highAvailabilityNodeEntry 2 }

    highAvailabilityNodeId OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The ID of a high availability node."
        ::= { highAvailabilityNodeEntry 3 }

    highAvailabilityNodeRole OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The role of a high availability node."
        ::= { highAvailabilityNodeEntry 4 }

    highAvailabilityNodeState OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The state of a high availability node."
        ::= { highAvailabilityNodeEntry 5 }

    highAvailabilityNodeHealth OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The health of the high availability node's resources."
        ::= { highAvailabilityNodeEntry 6 }


-- **********************************************************************
--
-- highAvailabilityComponent
-- =========================
--
-- dataDomainMib                    (*******.4.1.19746)
--   dataDomainMibObjects           (*******.4.1.19746.1)
--     highAvailability             (*******.4.1.19746.1.22)
--       highAvailabilityComponent  (*******.4.1.19746.1.22.3)
--
-- **********************************************************************

    highAvailabilityComponent       OBJECT IDENTIFIER ::= { highAvailability 3 }

    highAvailabilityComponentTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF HighAvailabilityComponentEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "This table provides high availability component information.
            "
        ::= { highAvailabilityComponent 1 }

    highAvailabilityComponentEntry OBJECT-TYPE
        SYNTAX  HighAvailabilityComponentEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "An entry represents one instance about a high availability component.
	    "
        INDEX { highAvailabilityComponentIdx }
        ::= { highAvailabilityComponentTable 1 }

    HighAvailabilityComponentEntry ::= SEQUENCE {
        highAvailabilityComponentIdx                DDMibTableIndexTC,
        highAvailabilityComponentName               DDMibTableString128TC,
        highAvailabilityComponentState              HaStatusTC
    }

    highAvailabilityComponentIdx OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "The index into this table indentifying a particular component."
        ::= { highAvailabilityComponentEntry 1 }

    highAvailabilityComponentName OBJECT-TYPE
        SYNTAX  DDMibTableString128TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The name of the component."
        ::= { highAvailabilityComponentEntry 2 }

    highAvailabilityComponentState OBJECT-TYPE
        SYNTAX  HaStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "The status of the component."
        ::= { highAvailabilityComponentEntry 3 }


-- ************************************************************ **********
--
-- scsitargetProperties
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetProperties   (*******.4.1.19746.1.23.1)
--
-- **********************************************************************

    scsitargetProperties       OBJECT IDENTIFIER ::= { scsitarget 1 }

    scsitargetAdminState OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Scsitarget Admin State"
        ::= { scsitargetProperties 1 }

    scsitargetProcessState OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Scsitarget Process State"
        ::= { scsitargetProperties 2 }


-- **********************************************************************
--
-- scsitargetGroup
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetGroup        (*******.4.1.19746.1.23.2)
--
-- **********************************************************************

    scsitargetGroup                OBJECT IDENTIFIER ::= { scsitarget 2 }

    scsitargetGroupTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Table"
        ::= { scsitargetGroup 1 }

    scsitargetGroupEntry OBJECT-TYPE
        SYNTAX  ScsitargetGroupEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetGroupEntry"
        INDEX { scsitargetGroupIndex }
        ::= { scsitargetGroupTable 1 }

    ScsitargetGroupEntry ::= SEQUENCE {
        scsitargetGroupIndex                DDMibTableIndexTC,
        scsitargetGroupName                 DDMibTableString512TC,
        scsitargetGroupService              DDMibTableString512TC,
        scsitargetGroupActiveState          DDMibString96TC,
        scsitargetGroupNumInitiators        DDMibInteger32TC,
        scsitargetGroupNumDevices           DDMibInteger32TC
    }

    scsitargetGroupIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Index"
        ::= { scsitargetGroupEntry 1 }

    scsitargetGroupName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Name"
        ::= { scsitargetGroupEntry 2 }

    scsitargetGroupService OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Service"
        ::= { scsitargetGroupEntry 3 }

    scsitargetGroupActiveState OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Active State"
        ::= { scsitargetGroupEntry 4 }

    scsitargetGroupNumInitiators OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Number of Initiators"
        ::= { scsitargetGroupEntry 5 }

    scsitargetGroupNumDevices OBJECT-TYPE
        SYNTAX  DDMibInteger32TC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Scsitarget Group Number of Devices"
        ::= { scsitargetGroupEntry 6 }


-- **********************************************************************
--
-- scsitargetInitiator
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetInitiator    (*******.4.1.19746.1.23.3)
--
-- **********************************************************************

    scsitargetInitiator            OBJECT IDENTIFIER ::= { scsitarget 3 }

    scsitargetInitiatorTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetInitiatorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Table"
        ::= { scsitargetInitiator 1 }

    scsitargetInitiatorEntry OBJECT-TYPE
        SYNTAX  ScsitargetInitiatorEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetInitiatorEntry"
        INDEX { scsitargetInitiatorIndex }
        ::= { scsitargetInitiatorTable 1 }

    ScsitargetInitiatorEntry ::= SEQUENCE {
        scsitargetInitiatorIndex                DDMibTableIndexTC,
        scsitargetInitiatorName                 DDMibTableString512TC,
        scsitargetInitiatorSystemAddress        DDMibTableString512TC,
        scsitargetInitiatorGroup                DDMibTableString512TC,
        scsitargetInitiatorService              DDMibTableString512TC,
        scsitargetInitiatorAddressMethod        DDMibTableString512TC,
        scsitargetInitiatorTransport            DDMibTableString512TC,
        scsitargetInitiatorFcWwpn               DDMibTableString512TC,
        scsitargetInitiatorFcWwnn               DDMibTableString512TC,
        scsitargetInitiatorFcSymbolicPortName   DDMibTableString512TC
    }

    scsitargetInitiatorIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Index"
        ::= { scsitargetInitiatorEntry 1 }

    scsitargetInitiatorName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Name"
        ::= { scsitargetInitiatorEntry 2 }

    scsitargetInitiatorSystemAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator System Address"
        ::= { scsitargetInitiatorEntry 3 }

    scsitargetInitiatorGroup OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Group"
        ::= { scsitargetInitiatorEntry 4 }

    scsitargetInitiatorService OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Service"
        ::= { scsitargetInitiatorEntry 5 }

    scsitargetInitiatorAddressMethod OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Address Method"
        ::= { scsitargetInitiatorEntry 6 }

    scsitargetInitiatorTransport OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Transport"
        ::= { scsitargetInitiatorEntry 7 }

    scsitargetInitiatorFcWwpn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Fc Wwpn"
        ::= { scsitargetInitiatorEntry 8 }

    scsitargetInitiatorFcWwnn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Fc Wwnn"
        ::= { scsitargetInitiatorEntry 9 }

    scsitargetInitiatorFcSymbolicPortName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Fc Symbolic Port Name"
        ::= { scsitargetInitiatorEntry 10 }


    scsitargetInitiatorEndpTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetInitiatorEndpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Endpoint Table"
        ::= { scsitargetInitiator 2 }

    scsitargetInitiatorEndpEntry OBJECT-TYPE
        SYNTAX  ScsitargetInitiatorEndpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetInitiatorEndpEntry"
        INDEX { scsitargetInitiatorEndpIndex }
        ::= { scsitargetInitiatorEndpTable 1 }

    ScsitargetInitiatorEndpEntry ::= SEQUENCE {
        scsitargetInitiatorEndpIndex                DDMibTableIndexTC,
        scsitargetInitiatorEndpInitiator            DDMibTableString512TC,
        scsitargetInitiatorEndpEndpoint             DDMibTableString512TC,
        scsitargetInitiatorEndpStatus               DDMibString96TC
    }

    scsitargetInitiatorEndpIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Endpoint Index"
        ::= { scsitargetInitiatorEndpEntry 1 }

    scsitargetInitiatorEndpInitiator OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator"
        ::= { scsitargetInitiatorEndpEntry 2 }

    scsitargetInitiatorEndpEndpoint OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Endpoint"
        ::= { scsitargetInitiatorEndpEntry 3 }

    scsitargetInitiatorEndpStatus OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Initiator Endpoint Status"
        ::= { scsitargetInitiatorEndpEntry 4 }


-- **********************************************************************
--
-- scsitargetEndpoint
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetEndpoint     (*******.4.1.19746.1.23.4)
--
-- **********************************************************************

    scsitargetEndpoint             OBJECT IDENTIFIER ::= { scsitarget 4 }

    scsitargetEndpointTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetEndpointEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Table"
        ::= { scsitargetEndpoint 1 }

    scsitargetEndpointEntry OBJECT-TYPE
        SYNTAX  ScsitargetEndpointEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetEndpointEntry"
        INDEX { scsitargetEndpointIndex }
        ::= { scsitargetEndpointTable 1 }

    ScsitargetEndpointEntry ::= SEQUENCE {
        scsitargetEndpointIndex                     DDMibTableIndexTC,
        scsitargetEndpointName                      DDMibTableString512TC,
        scsitargetEndpointCurrentSystemAddress      DDMibTableString512TC,
        scsitargetEndpointPrimarySystemAddress      DDMibTableString512TC,
        scsitargetEndpointSecondarySystemAddress    DDMibTableString512TC,
        scsitargetEndpointEnabled                   DDStatusTC,
        scsitargetEndpointStatus                    DDMibTableString512TC,
        scsitargetEndpointTransport                 DDMibTableString512TC,
        scsitargetEndpointFcWwnn                    DDMibTableString512TC,
        scsitargetEndpointFcWwpn                    DDMibTableString512TC
    }

    scsitargetEndpointIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Index"
        ::= { scsitargetEndpointEntry 1 }

    scsitargetEndpointName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Name"
        ::= { scsitargetEndpointEntry 2 }

    scsitargetEndpointCurrentSystemAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Current System Address"
        ::= { scsitargetEndpointEntry 3 }

    scsitargetEndpointPrimarySystemAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Primary System Address"
        ::= { scsitargetEndpointEntry 4 }

    scsitargetEndpointSecondarySystemAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Secondary System Address"
        ::= { scsitargetEndpointEntry 5 }

    scsitargetEndpointEnabled OBJECT-TYPE
        SYNTAX  DDStatusTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Enabled"
        ::= { scsitargetEndpointEntry 6 }

    scsitargetEndpointStatus OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Status"
        ::= { scsitargetEndpointEntry 7 }

    scsitargetEndpointTransport OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Transport"
        ::= { scsitargetEndpointEntry 8 }

    scsitargetEndpointFcWwnn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Fc Wwnn"
        ::= { scsitargetEndpointEntry 9 }

    scsitargetEndpointFcWwpn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Endpoint Fc Wwpn"
        ::= { scsitargetEndpointEntry 10 }


-- **********************************************************************
--
-- scsitargetPort
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetPort         (*******.4.1.19746.1.23.5)
--
-- **********************************************************************

    scsitargetPort                 OBJECT IDENTIFIER ::= { scsitarget 5 }

    scsitargetPortTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Table"
        ::= { scsitargetPort 1 }

    scsitargetPortEntry OBJECT-TYPE
        SYNTAX  ScsitargetPortEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetPortEntry"
        INDEX { scsitargetPortIndex }
        ::= { scsitargetPortTable 1 }

    ScsitargetPortEntry ::= SEQUENCE {
        scsitargetPortIndex                 DDMibTableIndexTC,
        scsitargetPortSystemAddress         DDMibTableString512TC,
        scsitargetPortEnabled               DDStatusTC,
        scsitargetPortStatus                DDMibTableString512TC,
        scsitargetPortTransport             DDMibTableString512TC,
        scsitargetPortOperationalStatus     DDMibTableString512TC,
        scsitargetPortFcNpiv                DDMibTableString512TC,
        scsitargetPortPortId                DDMibTableString512TC,
        scsitargetPortModel                 DDMibTableString512TC,
        scsitargetPortFirmware              DDMibTableString512TC,
        scsitargetPortFcBaseWwnn            DDMibTableString512TC,
        scsitargetPortFcBaseWwpn            DDMibTableString512TC,
        scsitargetPortFcCurrentWwnn         DDMibTableString512TC,
        scsitargetPortFcCurrentWwpn         DDMibTableString512TC,
        scsitargetPortFcp2Retry             DDMibTableString512TC,
        scsitargetPortConnectionType        DDMibTableString512TC,
        scsitargetPortLinkSpeed             DDMibTableString512TC,
        scsitargetPortFcTopology            DDMibTableString512TC
    }

    scsitargetPortIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Index"
        ::= { scsitargetPortEntry 1 }

    scsitargetPortSystemAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port System Address"
        ::= { scsitargetPortEntry 2 }

    scsitargetPortEnabled OBJECT-TYPE
        SYNTAX  DDStatusTC
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Enabled"
        ::= { scsitargetPortEntry 3 }

    scsitargetPortStatus OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Status"
        ::= { scsitargetPortEntry 4 }

    scsitargetPortTransport OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Transport"
        ::= { scsitargetPortEntry 5 }

    scsitargetPortOperationalStatus OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Operational Status"
        ::= { scsitargetPortEntry 6 }

    scsitargetPortFcNpiv OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Npiv"
        ::= { scsitargetPortEntry 7 }

    scsitargetPortPortId OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Port Id"
        ::= { scsitargetPortEntry 8 }

    scsitargetPortModel OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Model"
        ::= { scsitargetPortEntry 9 }

    scsitargetPortFirmware OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Firmware"
        ::= { scsitargetPortEntry 10 }

    scsitargetPortFcBaseWwnn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Base Wwnn"
        ::= { scsitargetPortEntry 11 }

    scsitargetPortFcBaseWwpn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Base Wwpn"
        ::= { scsitargetPortEntry 12 }

    scsitargetPortFcCurrentWwnn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Current Wwnn"
        ::= { scsitargetPortEntry 13 }

    scsitargetPortFcCurrentWwpn OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Current Wwpn"
        ::= { scsitargetPortEntry 14 }

    scsitargetPortFcp2Retry OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fcp2 Retry"
        ::= { scsitargetPortEntry 15 }

    scsitargetPortConnectionType OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "scsitarget Port Connection Type"
        ::= { scsitargetPortEntry 16 }

    scsitargetPortLinkSpeed OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Link Speed"
        ::= { scsitargetPortEntry 17 }

    scsitargetPortFcTopology OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Fc Topology"
        ::= { scsitargetPortEntry 18 }


    scsitargetPortEndpTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetPortEndpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint Table"
        ::= { scsitargetPort 2 }

    scsitargetPortEndpEntry OBJECT-TYPE
        SYNTAX  ScsitargetPortEndpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetPortEndpEntry"
        INDEX { scsitargetPortEndpIndex }
        ::= { scsitargetPortEndpTable 1 }

    ScsitargetPortEndpEntry ::= SEQUENCE {
        scsitargetPortEndpIndex                DDMibTableIndexTC,
        scsitargetPortEndpPort                 DDMibTableString512TC,
        scsitargetPortEndpEndpoint             DDMibTableString512TC,
        scsitargetPortEndpEnabled              DDStatusTC,
        scsitargetPortEndpStatus               DDMibString96TC,
        scsitargetPortEndpCurrentInstance      DDMibTableString512TC
    }

    scsitargetPortEndpIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint Index"
        ::= { scsitargetPortEndpEntry 1 }

    scsitargetPortEndpPort OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port"
        ::= { scsitargetPortEndpEntry 2 }

    scsitargetPortEndpEndpoint OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint"
        ::= { scsitargetPortEndpEntry 3 }

    scsitargetPortEndpEnabled OBJECT-TYPE
        SYNTAX  DDStatusTC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint Enabled"
        ::= { scsitargetPortEndpEntry 4 }

    scsitargetPortEndpStatus OBJECT-TYPE
        SYNTAX  DDMibString96TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint Status"
        ::= { scsitargetPortEndpEntry 5 }

    scsitargetPortEndpCurrentInstance OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Port Endpoint Current Instance"
        ::= { scsitargetPortEndpEntry 6 }


-- **********************************************************************
--
-- scsitargetDevice
-- ====================
--
-- dataDomainMib                (*******.4.1.19746)
--   dataDomainMibObjects       (*******.4.1.19746.1)
--     scsitarget               (*******.4.1.19746.1.23)
--       scsitargetDevice       (*******.4.1.19746.1.23.6)
--
-- **********************************************************************

    scsitargetDevice               OBJECT IDENTIFIER ::= { scsitarget 6 }

    scsitargetDeviceTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetDeviceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Table"
        ::= { scsitargetDevice 1 }

    scsitargetDeviceEntry OBJECT-TYPE
        SYNTAX  ScsitargetDeviceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetDeviceEntry"
        INDEX { scsitargetDeviceIndex }
        ::= { scsitargetDeviceTable 1 }

    ScsitargetDeviceEntry ::= SEQUENCE {
        scsitargetDeviceIndex           DDMibTableIndexTC,
        scsitargetDeviceName            DDMibTableString512TC,
        scsitargetDeviceService         DDMibTableString512TC,
        scsitargetDeviceActiveState     DDMibString96TC,
        scsitargetDeviceAddress         DDMibTableString512TC
    }

    scsitargetDeviceIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Index"
        ::= { scsitargetDeviceEntry 1 }

    scsitargetDeviceName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Name"
        ::= { scsitargetDeviceEntry 2 }

    scsitargetDeviceService OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Service"
        ::= { scsitargetDeviceEntry 3 }

    scsitargetDeviceActiveState OBJECT-TYPE
        SYNTAX  DDMibString96TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Active State"
        ::= { scsitargetDeviceEntry 4 }

    scsitargetDeviceAddress OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Address"
        ::= { scsitargetDeviceEntry 5 }


    scsitargetDeviceGrpTable OBJECT-TYPE
        SYNTAX  SEQUENCE OF ScsitargetDeviceGrpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Table"
        ::= { scsitargetDevice 2 }

    scsitargetDeviceGrpEntry OBJECT-TYPE
        SYNTAX  ScsitargetDeviceGrpEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "scsitargetDeviceGrpEntry"
        INDEX { scsitargetDeviceGrpIndex }
        ::= { scsitargetDeviceGrpTable 1 }

    ScsitargetDeviceGrpEntry ::= SEQUENCE {
        scsitargetDeviceGrpIndex                DDMibTableIndexTC,
        scsitargetDeviceGrpDevice               DDMibTableString512TC,
        scsitargetDeviceGrpGroupName            DDMibTableString512TC,
        scsitargetDeviceGrpLun                  DDMibTableString512TC,
        scsitargetDeviceGrpPrimaryEndpoints     DDMibTableString512TC,
        scsitargetDeviceGrpSecondaryEndpoints   DDMibTableString512TC,
        scsitargetDeviceGrpInUseEndpoints       DDMibTableString512TC
    }

    scsitargetDeviceGrpIndex OBJECT-TYPE
        SYNTAX  DDMibTableIndexTC 
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Index"
        ::= { scsitargetDeviceGrpEntry 1 }

    scsitargetDeviceGrpDevice OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device"
        ::= { scsitargetDeviceGrpEntry 2 }

    scsitargetDeviceGrpGroupName OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Name"
        ::= { scsitargetDeviceGrpEntry 3 }

    scsitargetDeviceGrpLun OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Lun"
        ::= { scsitargetDeviceGrpEntry 4 }

    scsitargetDeviceGrpPrimaryEndpoints OBJECT-TYPE
        SYNTAX  DDMibTableString512TC
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Primary Endpoints"
        ::= { scsitargetDeviceGrpEntry 5 }

    scsitargetDeviceGrpSecondaryEndpoints OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group Secondary Endpoints"
        ::= { scsitargetDeviceGrpEntry 6 }

    scsitargetDeviceGrpInUseEndpoints OBJECT-TYPE
        SYNTAX  DDMibTableString512TC 
        MAX-ACCESS  read-only 
        STATUS  current
        DESCRIPTION
            "Scsitarget Device Group In Use Endpoints"
        ::= { scsitargetDeviceGrpEntry 7 }


--
-- Common Notifications 
-- ====================
--
-- dataDomainMib        	(*******.4.1.19746)
--  dataDomainMibNotifications	(*******.4.1.19746.2)
--
-- **********************************************************************

dataDomainMibTraps OBJECT IDENTIFIER ::= { dataDomainMibNotifications 0 }

  powerSupplyFailedAlarm NOTIFICATION-TYPE
	STATUS deprecated
        DESCRIPTION
            "Meaning: Power Supply failed
             What to do: replace the power supply"
        ::= { dataDomainMibTraps 1 } 

  systemOverheatWarningAlarm NOTIFICATION-TYPE
        OBJECTS { tempSensorDescription } 
	STATUS deprecated
	DESCRIPTION
	    "Meaning: the temperature reading of one of the thermometers in the Chassis  has exceeded 
	     the 'warning' temperature level.  If it continues to rise, it may eventually trigger a 
	     shutdown of the DDR.  The index value of the alarm indicates the thermometer index that
	     may be looked up in the environmentals table 'temperatures' for more information about
	     the actual thermometer reading the high value.
	     What to do: check the Fan status, temperatures of the environment in which the DDR is,
	     and other factors which may increase the temperature."
	::= { dataDomainMibTraps 2 }

  systemOverheatAlertAlarm NOTIFICATION-TYPE
        OBJECTS { tempSensorDescription } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the temperature reading of one of the thermometers in the Chassis is more than
	     halfway between the 'warning' and 'shutdown' temperature levels.  If it continues to rise, 
	     it may eventually trigger a shutdown of the DDR.  The index value of the alarm indicates 
	     the thermometer index that may be looked up in the environmentals table 'temperatures' 
	     for more information about the actual thermometer reading the high value.
	     What to do: check the Fan status, temperatures of the environment in which the DDR is,
	     and other factors which may increase the system temperature."
	::= { dataDomainMibTraps 3 }

  systemOverheatShutdownAlarm NOTIFICATION-TYPE
        OBJECTS { tempSensorDescription } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the temperature reading of one of the thermometers in the Chassis has reached
	     or exceeded the 'shutdown' temperature level. The DDR will be shutdown to prevent damage
	     to the system.  The index value of the alarm indicates the thermometer index that may be
	     looked up in the environmentals table 'temperatures' for more information about the actual 
	     thermometer reading the high value.
	     What to do: Once the system has been brought back up, after checking for high environment
	     temperatures or other factors which may increase the system temperature, check other 
	     environmental values, such as Fan Status, Disk Temperatures, etc..."
	::= { dataDomainMibTraps 4 }

  fanModuleFailedAlarm NOTIFICATION-TYPE
        OBJECTS { fanDescription } 
	STATUS deprecated	
        DESCRIPTION
            "Meaning: a Fan Module in the enclosure has failed.  The index of the fan is given
             as the index of the alarm.  This same index can be looked up in the environmentals
             table 'fanProperies' for more information about which fan has failed.
             What to do: replace the fan"
        ::= { dataDomainMibTraps 5 } 

  nvramFailingAlarm NOTIFICATION-TYPE
	STATUS deprecated	
        DESCRIPTION
            "Meaning: The system has detected that the NVRAM is potentially failing.  There has
            been an excessive amount of PCI or Memory errors. The nvram tables 'nvramProperties'
            and 'nvramStats' may provide for information on why the NVRAM is failing.
            What to do: check the status of the NVRAM after reboot, and replace if the
            errors continue."
        ::= { dataDomainMibTraps 6 } 

  fileSystemFailedAlarm NOTIFICATION-TYPE
	STATUS deprecated	
        DESCRIPTION
            "Meaning: The File System process on the DDR has had a serious problem and has had
             to restart.
             What to do: check the system logs for conditions that may be triggering the failure.
             Other alarms may also indicate why the File System is having problems."
        ::= { dataDomainMibTraps 7 } 

  fileSpaceMaintenanceAlarm NOTIFICATION-TYPE
        OBJECTS { fileSystemResourceName } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: DDVAR File System Resource Space is running low for system maintenance activities.  The
	     system may not have enough space for the routine system activities to run without error.
	     What to do: Delete unneeded files, such as old log files, support bundles, core files,
	     upgrade rpm files stored in the /ddvar file system."
	::= { dataDomainMibTraps 8 }

  fileSpacePreWarningAlarm NOTIFICATION-TYPE
        OBJECTS { fileSystemResourceName } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: A File System Resource space is 80-85% utilized.  While not critical, the space usage
	     should be monitored.  The index value of the alarm indicates the file system index that may be 
	     looked up in the fileSystem table 'fileSystemSpace' for more information about the actual FS 
	     that is getting full. 
	     What to do: no action is necessary, but the file system should be monitored more closely as it
	     grows more full.  Further alarms will be sent when and if the file system space is approaching very full."
	::= { dataDomainMibTraps 9 }

  fileSpaceWarningAlarm NOTIFICATION-TYPE
        OBJECTS { fileSystemResourceName } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: A File System Resource space is 90% utilized.  The index value of the alarm indicates 
	     the file system index that may be looked up in the fileSystem table 'fileSystemSpace' 
	     for more information about the actual FS that is getting full. 
	     What to do: Delete unneeded files, such as old log files, support bundles, core files,
	     upgrade rpm files stored in the /ddvar file system.  Consider upgrading the hardware or adding
	     shelves to high-end units.  Reducing the retention times for backup data can also help. When 
	     files are deleted from outside of the /ddvar space, filesys clean will have to be done before 
	     the space is recovered."
	::= { dataDomainMibTraps 10 }

  fileSpaceSevereAlarm NOTIFICATION-TYPE
        OBJECTS { fileSystemResourceName } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: A File System Resource space is 95% utilized.  The index value of the alarm indicates 
	     the file system index that may be looked up in the fileSystem table 'fileSystemSpace' 
	     for more information about the actual FS that is getting full. 
	     What to do: Delete unneeded files, such as old log files, support bundles, core files,
	     upgrade rpm files stored in the /ddvar file system.  Consider upgrading the hardware or adding
	     shelves to high-end units.  Reducing the retention times for backup data can also help. When 
	     files are deleted from outside of the /ddvar space, filesys clean will have to be done before 
	     the space is recovered."
	::= { dataDomainMibTraps 11 }

  fileSpaceCriticalAlarm NOTIFICATION-TYPE
        OBJECTS { fileSystemResourceName } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: A File System Resource space is 100% utilized.  The index value of the alarm indicates 
	     the file system index that may be looked up in the fileSystem table 'fileSystemSpace' 
	     for more information about the actual FS that is full. 
	     What to do: Delete unneeded files, such as old log files, support bundles, core files,
	     upgrade rpm files stored in the /ddvar file system.  Consider upgrading the hardware or adding
	     shelves to high-end units.  Reducing the retention times for backup data can also help. When 
	     files are deleted from outside of the /ddvar space, filesys clean will have to be done before 
	     the space is recovered."
	::= { dataDomainMibTraps 12 }

  diskFailedAlarm NOTIFICATION-TYPE
        OBJECTS { diskSerialNumber } 
	STATUS deprecated	
        DESCRIPTION
            "Meaning: some problem has been detected about the indicated disk.  The index value 
             of the alarm indicates the disk index that may be looked up in the disk tables 
             'diskProperties', 'diskPerformance', and 'diskReliability' for more information 
             about the actual disk that has failed.
             What to do: replace the disk."
        ::= { dataDomainMibTraps 14 } 

  diskOverheatWarningAlarm NOTIFICATION-TYPE
        OBJECTS { diskTemperature } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the temperature reading of the indicated disk has exceeded the 'warning' 
	     temperature level.  If it continues to rise, it may eventually trigger a 
	     shutdown of the DDR.  The index value of the alarm indicates the disk index that
	     may be looked up in the disk tables 'diskProperties', 'diskPerformance', and 
	     'diskReliability' for more information about the actual disk reading the high value.
	     What to do: check the disk status, temperatures of the environment in which the DDR is,
	     and other factors which may increase the temperature."
	::= { dataDomainMibTraps 15 }

  diskOverheatAlertAlarm NOTIFICATION-TYPE
        OBJECTS { diskTemperature } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the temperature reading of the indicated disk is more than halfway between
	     the 'warning' and 'shutdown' temperature levels. If it continues to rise, it will 
	     trigger a shutdown of the DDR.  The index value of the alarm indicates the disk index that
	     may be looked up in the disk tables 'diskProperties', 'diskPerformance', and 
	     'diskReliability' for more information about the actual disk reading the high value.
	     What to do: check the disk status, temperatures of the environment in which the DDR is,
	     and other factors which may increase the temperature.  If the temperature continues stays
	     at this level or rises, and no other disks are reading this trouble, consider 'failing'
	     the disk, and get a replacement."
	::= { dataDomainMibTraps 16 }

  diskOverheatShutdownAlarm NOTIFICATION-TYPE
        OBJECTS { diskTemperature } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the temperature reading of the indicated disk has surpassed the 'shutdown' 
	     temperature level. The DDR will be shutdown.  The index value of the alarm indicates 
	     the disk index that may be looked up in the disk tables 'diskProperties', 'diskPerformance', 
	     and 'diskReliability' for more information about the actual disk reading the high value.
	     What to do: Boot the DDR and monitor the status and temperatures.  If the same disk has
	     continued problems, consider 'failing' it and get a replacement disk."
	::= { dataDomainMibTraps 17 }

  raidReconSevereAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: A raid group is MAX degraded with no reconstruction after less than 71 hours or 
             A disk group is degraded with none max degraded with no reconstruction. This alarm is sent 
             every 1 hour until 71 hours after which raidReconCriticalAlarm or raidReconCriticalShutdown
             alarm is generated. This can happen due to a disk failing at run-time or boot-time.
	     What to do: while it is still possible that the reconstruction could succeed, the disk
	     should be replaced to ensure data safety."
	::= { dataDomainMibTraps 18 }

  raidReconCriticalAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: Raid group is MAX degraded with no reconstruction after 71 hours. This can
	     happen due to a disk failing at run-time or boot-up.
	     What to do: the disk should be replaced to ensure data safety."
	::= { dataDomainMibTraps 19 }

  raidReconCriticalShutdownAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: Raid group is MAX degraded with no reconstruction after 71 hours. This can
             happen due to a disk failing at run-time or boot-up.
	     What to do: the disk must be replaced."
	::= { dataDomainMibTraps 20 }

  raidGroupMissingAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: one or more raid groups are missing.
	     What to do: disk may need replacement, or raid administration may be necessary."
	::= { dataDomainMibTraps 21 }

  diskNoSpareAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: there is no spare available for the raid group.
	     What to do: disk may need replacement, or raid administration may be necessary."
	::= { dataDomainMibTraps 22 }

  diskPathAlarm NOTIFICATION-TYPE
        OBJECTS { diskSerialNumber } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: multipath configuration is experiencing a problem.  The number of paths set up is less than 
	    the required number of paths.  Disk index is the first disk in the enclosure with multipath.
	     What to do: multipath disk administration may be necessary."
	::= { dataDomainMibTraps 23 }

  diskSASAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: SAS configuration error.  Maximum enclosures has been reached, or there
	    is a topology problem.
	     What to do: SAS configuration documentation should be consulted.  administration may be necessary."
	::= { dataDomainMibTraps 24 }

  diskSASHBAAlarm NOTIFICATION-TYPE
        OBJECTS { diskSerialNumber } 
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: Unsupported multi-path setting is enabled in the hba firmware.
	     What to do: contact Data Domain support."
	::= { dataDomainMibTraps 25 }

  snapshotFullAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: maximum number of snapshots has been reached.
	     What to do: expire some old snapshots to make room."
	::= { dataDomainMibTraps 26 }

  snapshotHWMAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: the number of snapshots has exceeded a predefined percentage (current 90%) of the maximum.
	     What to do: begin expiring snapshots so that the maximum is not reached."
	::= { dataDomainMibTraps 27 }

  clusterNodeAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: node is not reachable on any network interface.
	     What to do: check status of network and wiring.  if that fails, try 
	     rebooting the node."
	::= { dataDomainMibTraps 28 }

  clusterInterfaceAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: one interface of the cluster node is down.
	     What to do: check status of network and wiring.  if that fails, try 
	     rebooting the node."
	::= { dataDomainMibTraps 29 }

  replSyncAlarm NOTIFICATION-TYPE
  		OBJECTS { replStatus }
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: a replication context is disabled due to nvram loss.
	     What to do: break replication on source and destination, then reconfigure
	     them and run replication sync."
	::= { dataDomainMibTraps 30 }

  systemStartupAlarm NOTIFICATION-TYPE
	STATUS deprecated
	DESCRIPTION
	    "Meaning: system has rebooted or started.  this does not indicate any abnormal activity.
	     What to do: nothing, unless reboot was triggered by other abnormal conditions, such as
	     temperature, fan or power problems."
	::= { dataDomainMibTraps 31 }

  filesysRelaunchAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: file system has undergone too many relaunches.  it is probably unstable.
	     What to do: consult system logs.  software or hardware restart may fix the malfunction."
	::= { dataDomainMibTraps 32 }

  filesysDDGCFailedAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: DDGC cleaning process has failed.
	     What to do: consult system logs.  software or hardware restart may fix the malfunction."
	::= { dataDomainMibTraps 33 }

  filesysGeneralProblemAlarm NOTIFICATION-TYPE
	STATUS deprecated 
	DESCRIPTION
	    "Meaning: a general problem has occurred with the file system.
	     What to do: consult system logs.  software or hardware restart may fix the malfunction."
	::= { dataDomainMibTraps 34 }
	
  diskUnsupportedAlarm NOTIFICATION-TYPE
        OBJECTS { diskSerialNumber } 
	STATUS deprecated	
        DESCRIPTION
            "Meaning: the model of the disk is unsupported by current DD platform.  The index value 
             of the alarm indicates the disk index that may be looked up in the disk tables 
             'diskProperties', 'diskPerformance', and 'diskReliability' for more information 
             about the actual disk.
             What to do: replace the disk."
        ::= { dataDomainMibTraps 35 }
 
  eventIPMIUnmanageAlarm NOTIFICATION-TYPE
	STATUS deprecated	
        DESCRIPTION
            "Meaning: IPMI unmanaged alert detected.
             What to do: check alert message"
        ::= { dataDomainMibTraps 36 } 

-- autogenerated notifications here
-- generated notifications
generatedNotificationsGroup NOTIFICATION-GROUP
NOTIFICATIONS {
   cpismissing,
   controllerUnreachableAlert,
   controllerIfaceUnreachableAlert,
   containerMarkedInvalid,
   cMTaskEnded,
   correctableECCLimitReached,
   uncorrectableECCerror,
   dIMMFailure,
   compromisedEncryptionKeys,
   newEncryptionKey,
   encryptionKeyTableFull,
   encryptionKeyExportFailed,
   insufficientSpaceForEncryption,
   corruptEncryptionKeys,
   legacyChassisTempWarning,
   legacyChassisTempCritical,
   legacyPowerSupplyWarning,
   legacyFanWarning,
   powerSupplyWarning,
   fanWarning,
   voltageWarning,
   powerWarning,
   correctECCWarning,
   processorWarning,
   powerUnitWarning,
   unCorrectECCWarning,
   chassisSensorCritical,
   chassisTempWarning,
   chassisTempCritical,
   cPUFailureWarning,
   legacyBMCHangCritical,
   bMCHangCritical,
   abnormalShutdown,
   smiMrc,
   bMCPartialHang,
   fanFault,
   powerSupplyInputFault,
   powerSupplyFailure,
   powerSupplyAbsent,
   unsupportedACVoltage,
   iOModuleFault,
   iOModuleInserted,
   mgmtModuleFault,
   sPFault,
   chassisFailure,
   forcedControllerShutdown,
   systemReset,
   enclosureHighTemp,
   unsupportedSystemType,
   bMCHangShutdown,
   bMCFailure,
   unsupportedHardwareConfig,
   unsupportedVirtualCPU,
   unsupportedPowerSupply,
   openFanDrawer,
   memoryRiserFault,
   bMCFailureSysBBU,
   unsupportedEnclosurePSU,
   pCILinkDegraded,
   invalidHardwareCritical,
   invalidHardwareWarning,
   correctableErrorWarning,
   generalHardwareFailure,
   targetDriverPortOffline,
   targetDriverPortOnline,
   targetDriverPortCore,
   targetDriverPortMultipleCore,
   targetDriverPortFWLoadFailed,
   targetDriverPortUnreadable,
   targetDriverPortTooManyOsc,
   tooManyRelaunches,
   filesystemProblem,
   dDFSFailedInShutdown,
   dDFSNoHeartbeat,
   dDFSDiedAfterReboot,
   dDFSDied,
   dDFSRebooted,
   dDFSRebootedDisabled,
   indexRebuildComplete,
   filesystemNVRAMDataLoss,
   recoverFromNVRAMFailed,
   dDFSRequiresReboot,
   metadataWarningThreshold,
   filesystemCorruption,
   physicalCapacityMeasurementTasksLost,
   physicalCapacityMeasurementTasksLostMTree,
   physicalCapacityMeasurementScheduleFailed,
   uncertifiedFirmware,
   fileMigrationError,
   cleaningError,
   hAdegraded,
   hAofflineErrors,
   hATimeOutOfSync,
   historicalDatabaseRecoverError,
   historicalDatabaseBackupError,
   historicalDatabaseUpgradeError,
   historicalDatabasePruneError,
   noHistoricalDatabaseError,
   historicalDatabaseFailoverError,
   hDTFileTransferFailed,
   hDTSystemError,
   spuriousInterruptDisabled,
   licenseExpiring,
   licenseExpired,
   dIMMFailureAlert,
   memoryAlert,
   portPathDisabled,
   diskPathRedundancy,
   missingPortConnection,
   missingLunPath,
   missingDiskPath,
   missingEnclosurePath,
   interfaceConnectivityDown,
   interfaceConnectivityIntermittent,
   interfaceMisconfiguration,
   interfaceConnectivityUpAndRunning,
   duplicateAddressDetection,
   invalidNICSlot,
   unsupportedNIC,
   tcpZeroWindowAlert,
   dNSUnresponsive,
   nISCommFailure,
   iOModuleMacFault,
   missingSlaveInterface,
   nTPDFailed,
   nvramWarning,
   nvramBatteryAlert,
   nvramErrorAlert,
   nvramBatteryLowChargeAlert,
   ext3NvlogDisabled,
   nvramHWAlert,
   nvramBattAlert,
   nvramEnvAlert,
   nvramCondAlert,
   nvramEventHWAlert,
   nvramBattEndOfLife,
   phyalert,
   mtreeQuotaSoftLimit,
   mtreeQuotaHardLimit,
   storageUnitStreamSoftLimit,
   replProgressThreshholdReached,
   replNeedResync,
   replLogFull,
   replIncompatibleWorm,
   replDestNotConfigured,
   replLagThreshholdReached,
   replPathTooLong,
   missingCreplUnits,
   mtreeCascadeNeedResync,
   insecureEncryptedReplication,
   suspendedMReplMissingUnits,
   sASEnclosureCheck,
   sASTopologyCheck,
   sASPortDisabled,
   sASHBAFailure,
   sASHBAErrors,
   unsupportedSASDevice,
   invalidEnclosureTopology,
   diskPathSpeedDegraded,
   enclosureMixType,
   enclosureMixDriveType,
   sCSITGTInvalidRegistry,
   sSLCertificateCorrupted,
   unusableHostCertificate,
   missingHostCertificate,
   expiredHostCertificate,
   sMSUnresponsive,
   mailserverError,
   snapshotOver90Percent,
   snapshotLimitReached,
   sNTZMultipleIterations,
   coredumpWarning,
   coredumpDisabled,
   spaceOver80Percent,
   spaceOver90Percent,
   spaceReclRestartFailed,
   spaceReclMissingUnit,
   spaceReclUnitReclaimed,
   spaceReclError,
   spaceReclSuspended,
   spaceReclUnitError,
   diskAccessError,
   diskFailure,
   diskTemperatureWarning,
   diskTemperatureShutdown,
   unsupportedHardwareSpareSize,
   missingDiskGroup,
   diskGroupReconstructionNoProgress,
   diskGroupReconstruction,
   diskGroupReconstructionShutdown,
   diskGroupReconstructionCritical,
   diskUnknown,
   lowSpares,
   unsupportedConfigurationROL,
   foreignEnclosure,
   sSDEndOfLife,
   multipleDiskReadErrors,
   unsupportedDriveModel,
   driveMixType,
   missingTierStorage,
   storageMigrationCopyComplete,
   storageMigrationCannotResume,
   storageMigrationUserSuspend,
   foreignPack,
   upgradeFailure,
   upgradeCompleted,
   upgradeInProgress,
   vDiskSCSITargetMismatch,
   tapeReposition,
   duplicateVTLPoolNames,
   vTLEnabled,
   vTLDisabled
}
STATUS  current
DESCRIPTION
   "A collection of objects providing  notifications, automatically generated by build."
::= { dataDomainMibGroups 5000 }

cpismissing NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An archive unit is missing which could result in failure to access files in that unit.
    
   Action: 
        - check power connection of each shelf.
        - check data cables of each shelf.
        - if a hardware problem is found and fixed, restart the files system and verify that the archive unit is online.
        - If the problem persists,  contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5500 }

controllerUnreachableAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        This is a GDA event raised by the Master controller when it fails to reach a Worker
        controller for more than 5 minutes. An unreachable Worker controller implies none of the Worker
        controller interfaces are pingable from the Master controller.
    
   Action: 
        Check the status of network interfaces on both controllers.
        Check network connectivity between the Master and Worker controller.
        Make sure all cables are securely connected.
    "
::= { dataDomainMibTraps 5002 }

controllerIfaceUnreachableAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        This is a GDA event raised by the Master controller when it fails to reach a particular
        Worker controller interface for more than 5 minutes. An unreachable interface implies that
        it is not pingable from its Master controller equivalent interface.
    
   Action: 
        Check network interface status on both controllers.
        Check network connectivity between the Master and Worker controller.
        Make sure all cables are securely connected.
    "
::= { dataDomainMibTraps 5003 }

containerMarkedInvalid NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The restorer filesystem has detected a data integrity check failure
        due to hardware/software problems. There is potential for data loss or
        corruption and needs immediate attention.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5501 }

cMTaskEnded NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A long running task has ended.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10503 }

correctableECCLimitReached NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
      Single bit correctable ECC limit reached.
    
   Action: 
      Replace the failed DIMM(s).
      Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5004 }

uncorrectableECCerror NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
      Multi bit uncorrectable ECC error.
    
   Action: 
      Replace the failed DIMM(s).
      Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5005 }

dIMMFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fault has been detected on a memory card. The memory may no longer be usable by the system and the system may not function with the reduced memory capacity.
    
   Action: 
        The failing memory card must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6525 }

compromisedEncryptionKeys NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Data on disk may be at risk of disclosure. Data encrypted with
    the compromised keys should be re-encrypted before shipping the disks.
    One can get the list of compromised keys either from CLI or Enterprise Manager.
    
   Action: 
    Start file system cleaning to re-encrypt the affected data. 
    For extended retention systems, run space reclamation to re-encrypt affected data in the archive tier.
    Note, these operations will take a long time to complete.
    "
::= { dataDomainMibTraps 6001 }

newEncryptionKey NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Old key is still used for encrypting new data.
    The file system needs to be restarted to activate the new key.
    
   Action: 
    Restart the filesystem.
    "
::= { dataDomainMibTraps 6002 }

encryptionKeyTableFull NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Encryption key table is full. No new keys can be added until some keys are
        deleted.
    
   Action: 
        Free up key table space by deleting an unused key and then add the new key.
    "
::= { dataDomainMibTraps 6003 }

encryptionKeyExportFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The automatic export of the encryption keys failed.
        This could result in encrypted data being inaccessible.
    
   Action: 
        Manually export the encryption keys.
    "
::= { dataDomainMibTraps 6540 }

insufficientSpaceForEncryption NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Additional space is needed on the archive tier to encrypt the existing data.
    Free space up to 1% of data size may be required.
    Un-encrypted data will not get encrypted until sufficient space is available.
    
   Action: 
    Make free space available in the archive tier by doing one or all of the following:
    1. Delete any unwanted data in archive tier and run space-reclamation to reclaim free space.
    2. Expand the archive unit by adding some storage, and run space-reclamation. 
    "
::= { dataDomainMibTraps 7515 }

corruptEncryptionKeys NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    File system was locked as the encryption keys cannot not be validated. One or more 
    encryption keys could be corrupt.
  
   Action: 
    Contact your contracted support provider or visit us online at https://support.emc.com
  "
::= { dataDomainMibTraps 10009 }

legacyChassisTempWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current temperature sensor reading exceeds warning threshold.
    
   Action: 
        Monitor temperature readings. If alert persists, check ambient room temperature and for blocked air flow.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5006 }

legacyChassisTempCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current temperature sensor reading exceeds critical threshold.
    
   Action: 
        Check ambient room temperature and for blocked air flow.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5007 }

legacyPowerSupplyWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, powerModuleDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A power supply has failed, is unplugged, or absent.
    
   Action: 
        Check power supply cables and LED. Replace with other working power supply.
        If there are outstanding alerts for all power supplies in an enclosure, the
        system must be shutdown before replacing any power supply. 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5008 }

legacyFanWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fan has failed.
    
   Action: 
        Check for any failed fan. If failed, replace with new fan.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5009 }

powerSupplyWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A power supply has failed, is unplugged, or absent.
    
   Action: 
        Check power supply cables and LED. Replace with other working power supply.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5010 }

fanWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fan has failed or is missing.
    
   Action: 
        Check for any failed or missing fan. Reseat the fan.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5011 }

voltageWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Voltage sensor reading exceeds warning threshold.
    
   Action: 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5012 }

powerWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current power consumption reading exceeds warning threshold.
    
   Action: 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5013 }

correctECCWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Memory correctable ECC errors exceed warning threshold.
    
   Action: 
        These memory errors are automatically corrected by the system. 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5014 }

processorWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Processor status sensor alert.
    
   Action: 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5016 }

powerUnitWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        This event is expected after power off, power cycle, or AC power loss event.
    
   Action: 
        If this alert is not expected, check power cords and AC power.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5017 }

unCorrectECCWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Memory uncorrectable ECC error alert.
    
   Action: 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5018 }

chassisSensorCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current sensor reading or state exceeds critical condition.
    
   Action: 
        Check the specific alert message for information.
        Monitor temperature readings. If alert persists, check ambient room temperature 
        and for blocked air flow. 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5020 }

chassisTempWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current temperature sensor reading exceeds warning threshold.
    
   Action: 
        Monitor temperature readings. If alert persists, check ambient room temperature 
        and for blocked air flow. 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5021 }

chassisTempCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current temperature sensor reading exceeds critical threshold.
    
   Action: 
        Check ambient room temperature and for blocked air flow. 
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5022 }

cPUFailureWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        DDOS has detected a fault with the indicated CPU.
    
   Action: 
         Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5023 }

legacyBMCHangCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        While BMC is unresponsive, environmental monitoring and remote access via IPMI is not operational.
        This could mask serious problems such as overheating.
    
   Action: 
        Use CLI or Enterprise Manager to check fan, voltage, and power supply readings.
        If the system fails to fetch the readings, gracefully shut down the system and pull out all power cables to reset BMC.
        Wait until all LEDS are off. Reinsert the power cables and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5024 }

bMCHangCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        While BMC is unresponsive, environmental monitoring and remote access via IPMI is not operational.
        This could mask serious problems such as overheating.
    
   Action: 
        Use CLI or Enterprise Manager to check fan, voltage, and power supply readings.
        If the system fails to fetch the readings, gracefully shut down the system and pull out all power cables to reset BMC.
        Wait until all LEDS are off. Reinsert the power cables and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5025 }

abnormalShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The system has been shutdown by abnormal method, i.e. not by one of the following: 
        1) Via IPMI chassis control command 
        2) Via power button
        3) Via OS shutdown
    
   Action: 
        This alert is expected after loss of AC (main power) event.
        If this shutdown is not expected and persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5026 }

smiMrc NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        BIOS SMI MRC interrupt
    
   Action: 
        Check if system memory size has been decreased due to a DIMM has been disabled.
        If yes, replace the bad DIMM.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5502 }

bMCPartialHang NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        BMC firmware is not responsive.
    
   Action: 
        Use CLI or Enterprise Manager to check fan, voltage, and power supply readings.
        If the system fails to fetch the readings, gracefully shut down the system and pull out all power cables to reset BMC.
        Wait until all LEDS are off. Reinsert the power cables and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6015 }

fanFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A hardware fault has been detected in an enclosure cooling fan. This may allow the enclosure to overheat.
    
   Action: 
        The fan must be replaced. Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6517 }

powerSupplyInputFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A power loss has been detected in an enclosure power supply.  This can be caused by a loss of AC power to one power supply.
    
   Action: 
        Check if AC main power is available to the system. 
        Check if power supply cables are properly connected.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com to replace the power supply. 
    "
::= { dataDomainMibTraps 6518 }

powerSupplyFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A power loss has been detected in an enclosure power supply.  This can be caused by a failure in the power supply.
    
   Action: 
        Power supply needs to be replaced. Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6519 }

powerSupplyAbsent NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An enclosure power supply is absent.
    
   Action: 
        Power supply needs to be replaced. Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6520 }

unsupportedACVoltage NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The power supply for this enclosure is connected to an unsupported AC voltage. 
        The system may operate at a reduced capacity or unexpectedly shut down.
    
   Action: 
         Replace the input power with the supported voltage.
    "
::= { dataDomainMibTraps 6521 }

iOModuleFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fault has been detected with an I/O module and it will not function correctly.
    
   Action: 
        The I/O module must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6522 }

iOModuleInserted NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An I/O module has been inserted during system runtime. The I/O module will not be powered on until the system is restarted.
    
   Action: 
        Restart the system to activate this module.
    "
::= { dataDomainMibTraps 6523 }

mgmtModuleFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fault has been detected in the management I/O module. Management functions requiring the I/O module will not work.
    
   Action: 
        The management I/O module must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6524 }

sPFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fault has been detected on the storage processor. The system may experience unexpected behavior.
    
   Action: 
        The failed storage processor must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6526 }

chassisFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A fault has been detected in one of the chassis components. The system may experience unexpected behavior. 
    
   Action: 
        The chassis must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6527 }

forcedControllerShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An environmental condition or hardware fault has been detected that may cause significant damage to the hardware or possible data loss, and the system unit is being shut down.
    
   Action: 
        Check the environment, If the system environment is at a normal state, restart the system and review the alert history to determine the source of the problem.
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6528 }

systemReset NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A critical failure caused the system to unexpectedly shut down.
        The system was able to boot successfully either because the fault was corrected, the fault was transient, or the system was able to boot without the component causing the fault.
    
   Action: 
        Review the system for earlier alerts to determine the cause of the unexpected shut down.
        Check the system inventory to verify all components are configured as expected. 
        If this alert is issued again, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6529 }

enclosureHighTemp NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The temperature inside the enclosure is high and has exceeded the warning threshold.If no action is taken and the temperature continue to increase, the system will shut down.
    
   Action: 
        If the temperature around the system unit is within the documented operating limits and the air flow is not blocked,
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6535 }

unsupportedSystemType NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            An unsupported system type has been detected and needs 
            to be modified or replaced.
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com
        "
::= { dataDomainMibTraps 6536 }

bMCHangShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       While BMC is unresponsive, environmental monitoring and remote access via IPMI is not operational.  This could mask serious problems such as overheating.
    
   Action: 
        After the system is shut down, pull out all power cables from the controller to reset BMC.
        Wait until all LEDs are off, reinsert the power cables, and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6537 }

bMCFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        BMC is unresponsive or unable to access some required information. This may be corrected by a BMC reset procedure. If the condition is serious, the file system will be disabled.
    
   Action: 
        Shut the system down and pull out all power cables from the controller to reset BMC.
        Wait until all LEDs are off, reinsert the power cables, and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7000 }

unsupportedHardwareConfig NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Unsupported virtual machine hardware configuration was detected.  
        This configuration may cause unexpected behavior.
    
   Action: 
        Fix the hardware configuration to resolve the problem.
        If problem persists, restart the system.
    "
::= { dataDomainMibTraps 7502 }

unsupportedVirtualCPU NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            The current number of CPU sockets and cores in the 
            virtual machine configuration is not valid for the 
            current storage capacity. The file system will not 
            be enabled.
        
   Action: 
            Power off this virtual machine instance and change 
            the virtual machine configuration to match the supported values. 
            If you made this change to add storage capacity, you must 
            add the additional capacity before enabling the file system.
        "
::= { dataDomainMibTraps 7503 }

unsupportedPowerSupply NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The installed power supply is an unsupported model.
        The system may exhibit unexpected behavior.
    
   Action: 
        Replace the power supply with a supported model.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10001 }

openFanDrawer NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Fan drawer is not closed securely.
        Failure to secure the fan drawer may allow the system to overheat and shut down.
    
   Action: 
        Close and secure the fan drawer.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10002 }

memoryRiserFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A critical failure has been detected in a memory riser.
        Memory attached to this riser may disappear from the system inventory.
        The system may exhibit unexpected behavior.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to replace the memory riser.
    "
::= { dataDomainMibTraps 10003 }

bMCFailureSysBBU NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        BMC is unresponsive or unable to access some required information. This may be corrected by a BMC reset procedure. If the condition is serious, the file system will be disabled.
    
   Action: 
        Shut the system down and remove all external power sources from the controller.
        Remove the system battery backup unit (BBU).
        Wait until all LEDs are off, reinsert the BBU, connect external power, and then power the system up.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7524 }

unsupportedEnclosurePSU NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A power supply has been detected in an enclosure which does not provide complete environmental information. 
        This will not affect normal operations but should be replaced as soon as convenient.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10000 }

pCILinkDegraded NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The system has detected that PCI communication speed is degraded.
        This will not cause the system to fail but may impact performance of affected components.
    
   Action: 
        Reboot the system.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10004 }

invalidHardwareCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        During device inventory verification, the system found missing or invalid hardware, or devices installed in the wrong location. 
        The filesystem cannot be enabled with the current hardware configuration.
        
   Action: 
        View enclosure misconfiguration to determine the problem.
        For assistance, contact your contracted support provider or visit us online at https://support.emc.com.
        "
::= { dataDomainMibTraps 10005 }

invalidHardwareWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        During device inventory verification, the system found missing or invalid hardware, or devices installed in the wrong location. 
        Some components may not operate as expected.
        
   Action: 
        View enclosure misconfiguration to determine the problem.
        For assistance, contact your contracted support provider or visit us online at https://support.emc.com.
        "
::= { dataDomainMibTraps 10006 }

correctableErrorWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The system has detected excessive correctable errors on a component that may indicate a hardware failure.
    
   Action: 
         Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10007 }

generalHardwareFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A critical failure has been detected in one or more chassis components.
        The system may exhibit unexpected behavior.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10011 }

targetDriverPortOffline NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        No connection is detected on the Fibre Channel Port. 
    
   Action: 
        If this port is not in use, please disable it. If port is being
        used, please restore Fibre Channel Port connectivity. Alert will be cleared
        when port is disabled or online.
    "
::= { dataDomainMibTraps 7508 }

targetDriverPortOnline NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Fibre Channel Port is online. 
    
   Action: 
        No action required
    "
::= { dataDomainMibTraps 7509 }

targetDriverPortCore NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An error occurred on the Fibre Channel Port that resulted in a HBA core. 
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to extract and analyze the HBA core.
    "
::= { dataDomainMibTraps 7510 }

targetDriverPortMultipleCore NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An error occurred on the Fibre Channel Port that caused multiple HBA core attempts. The Fibre Channel Port is disabled. 
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to replace Fibre Channel HBA.
    "
::= { dataDomainMibTraps 7511 }

targetDriverPortFWLoadFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The Fibre Channel Port is unable to load required firmware. The Fibre Channel Port is disabled. 
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to replace Fibre Channel HBA.
    "
::= { dataDomainMibTraps 7512 }

targetDriverPortUnreadable NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Unable to access one or more resources on the HBA. The Fibre Channel Port is disabled. 
    
   Action: 
        Contact contact your contracted support provider or visit us online at https://support.emc.com to replace Fibre Channel HBA.
    "
::= { dataDomainMibTraps 7513 }

targetDriverPortTooManyOsc NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The system has detected a Fibre Channel Port has been transitioning
        between online and offline too frequently. This may cause disruption in i/o.
    
   Action: 
        Please ensure stable port connectivity. Disable the port if it is not in use.
    "
::= { dataDomainMibTraps 7514 }

tooManyRelaunches NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    The filesystem has failed to restart after multiple attempts.  Backup and restore services are unavailable.
    
   Action: 
    Contact your contracted support provider or visit us online at https://support.emc.com to diagnose the failure.
    "
::= { dataDomainMibTraps 5027 }

filesystemProblem NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    A problem is preventing the filesystem from running. This usually 
    involves the storage being unavailable.
    
   Action: 
    Check disk shelf cabling.  If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5028 }

dDFSFailedInShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    The filesystem shut down encountered an error. This can potentially leave data in the NVRAM 
    which would be normally flushed to the disk.  Failed shutdown can leave the filesystem in a 
    state that can prevent certain operations from being performed (i.e. system upgrade,
    filesys destroy, etc.) and may cause data loss.
    
   Action: 
    Re-enable the filesystem and try shutting it down again. If the problem persists, 
    Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5030 }

dDFSNoHeartbeat NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Filesystem has encountered an error and is forced to restart.
    
   Action: 
    If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5034 }

dDFSDiedAfterReboot NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    The filesystem failed to startup immediately after system reboot.
    
   Action: 
    Contact your contracted support provider or visit us online at https://support.emc.com for diagnosis of the cause of failure. No other 
    action required if filesystem successfully restarts.
    "
::= { dataDomainMibTraps 5036 }

dDFSDied NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    The filesystem process failed and is restarting.
    
   Action: 
    Contact your contracted support provider or visit us online at https://support.emc.com for diagnosis of the cause of failure. No other 
    action required if filesystem successfully restarts.
    "
::= { dataDomainMibTraps 5037 }

dDFSRebooted NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
    This is just a notification alert that the system has been rebooted.
    
   Action: 
    No action required.
    "
::= { dataDomainMibTraps 5038 }

dDFSRebootedDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
    This is a notification alert sent out during system startup following a 
    reboot. The system is rebooted with the filesystem in disabled state.
    
   Action: 
    No action is required unless the intent is to have the filesystem enabled. 
    In that case, invoke the 'filesys enable' command.
    "
::= { dataDomainMibTraps 5039 }

indexRebuildComplete NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The metadata rebuild operation that was started by support has completed. The system is ready for the next action. 
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to perform the next operation on your system. 
    "
::= { dataDomainMibTraps 5040 }

filesystemNVRAMDataLoss NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
       Filesystem cannot be enabled because of NVRAM mismatch. This can happen because of an improper headswap or NVRAM card replacement operation.
    
   Action: 
      Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6005 }

recoverFromNVRAMFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Due to NVRAM issues, filesystem has been disabled in order to maintain data integrity.
        No backups will run until this problem is resolved.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6013 }

dDFSRequiresReboot NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    File system was unable to start because an abnormal internal
    condition was detected.
    
   Action: 
    Reboot the system. If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7516 }

metadataWarningThreshold NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Large amount of metadata has been created by the filesystem.
        Continuing without cleaning would cause the filesystem to enter into read-only mode.
    
   Action: 
        Clean the filesystem as soon as possible.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7519 }

filesystemCorruption NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Filesystem processing detected some corruption.
        This may be isolated to a single file or multiple files, 
        preventing you from reading the corrupted files. 
        If a system file is affected, the filesystem could be disabled.
        Until this corruption is fixed, you will not be able to run cleaning,
        data-movement or space reclamation on the affected tier.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7521 }

physicalCapacityMeasurementTasksLost NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        File system has been disabled either by a failure or user action.
        Physical-capacity-measurement samples for these tasks are no longer available.
    
   Action: 
        Restart physical-capacity-measurement sample tasks that have not completed.
    "
::= { dataDomainMibTraps 10504 }

physicalCapacityMeasurementTasksLostMTree NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        MTree replication was broken or resynced causing one or more physical-capacity-measurement sample tasks to be cancelled.
        Physical-capacity-measurement samples for these tasks are no longer available.
    
   Action: 
        Restart physical-capacity-measurement sample tasks that have not completed for this MTree.
    "
::= { dataDomainMibTraps 10505 }

physicalCapacityMeasurementScheduleFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A physical-capacity-measurement sample task that was scheduled to run was unable to start due to an error.
        Some or all of the physical-capacity-measurement samples for these tasks are not available.
    
   Action: 
        Review scheduled physical-capacity-measurement sample task errors to determine the reason for the failure.
        Resubmit failed tasks if needed.
    "
::= { dataDomainMibTraps 10506 }

uncertifiedFirmware NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Firmware version is not compatible with this version of DDOS and may cause unexpected behavior.
    
   Action: 
    The firmware must be updated as soon as possible, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6004 }

fileMigrationError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        File migration is suspended until this problem is resolved.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6016 }

cleaningError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Unable to reclaim unused space and this may impact the ability to backup.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6014 }

hAdegraded NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       A condition has been detected causing the configured HA system to be degraded.
       Any failover attempt will not succeed.
    
   Action: 
       Display the HA detailed status to determine the failed component.
       Repairing the failed component can return the HA system to available state.
    "
::= { dataDomainMibTraps 10508 }

hAofflineErrors NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       Critical system errors have been detected causing the configured HA system to be in offline state.
       Access to system services may be unavailable.
    
   Action: 
       Review exisiting alerts.
       Fixing issues identified by these alerts may resolve this alert.
       If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10510 }

hATimeOutOfSync NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       Timestamps of logs and system events will not match between the HA nodes.
       This condition will not have an effect on HA functionality or system operation.
    
   Action: 
       Set the time on the standby node to match the active node.
       If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10514 }

historicalDatabaseRecoverError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        All existing historical data has been lost. Data in the historical
        database is used for some system reports, but it is not required for proper
        system functioning. This failure may occur during head swap processing if
        recovery to create the new head primary historical database from the shelf
        database files fails or historical database may be corrupted.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
        to debug this problem and possibly attempt recovery of the lost historical data.
    "
::= { dataDomainMibTraps 5041 }

historicalDatabaseBackupError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        Historical database backup failed. If the primary historical
        database fails without a recent backup present, some or all of the historical
        data can be lost when the primary historical database is restored from its
        backup and log files. Data in the historical database is used for some system
        reports, but it is not required for proper system functioning.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
        to help debug the problem.
    "
::= { dataDomainMibTraps 5042 }

historicalDatabaseUpgradeError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            Historical database upgrade failed during software upgrade.  New historical
            data fields will not be saved.  Some UI reports might be unavailable, or
            be missing data. Otherwise, this should not affect the system.
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com
            to help debug the problem and upgrade the historical database.
        "
::= { dataDomainMibTraps 5043 }

historicalDatabasePruneError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            Historical database pruning failed.  Pruning ensures the historical database
            does not consume more than its allocated disk space.
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com
            to help debug the problem.
        "
::= { dataDomainMibTraps 5044 }

noHistoricalDatabaseError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        Data in the historical database is used for some system reports,
        but it is not required for proper system functioning. This failure may occur
        during head swap processing if recovery to create the new head primary
        historical database from the shelf database files fails or the database has
        been corrupted. The system is running without historical database after failing
        to create a new one.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
        to debug this problem and possibly attempt recovery of the lost historical data.
    "
::= { dataDomainMibTraps 5045 }

historicalDatabaseFailoverError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            Historical database failover failed.
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com
            to help debug the problem.
        "
::= { dataDomainMibTraps 10507 }

hDTFileTransferFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
		Unable to send historical data update file to Data Domain Management Center.
    
   Action: 
		Check DNS settings and network connection between the Data Domain
		System and Data Domain Management Center.  If the problem persists,
		contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5046 }

hDTSystemError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
		System Error occurred while sending historical data to Data Domain Management Center.
    
   Action: 
		Check configuration and system status, messages logs for indicative problems.
		If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5047 }

spuriousInterruptDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       Excessive spurious hardware messages have been detected that may indicate a hardware failure.
    
   Action: 
       Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10008 }

licenseExpiring NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The temporary license indicated is about to expire. The function provided by this license will cease to operate.
    
   Action: 
        Contact your [account team] to purchase a license if you wish to continue to use this feature.
    "
::= { dataDomainMibTraps 8001 }

licenseExpired NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The temporary license indicated has expired. The function provided by this license has stopped operating.
    
   Action: 
        Contact your [account team] to purchase a license if you wish to use this feature.
    "
::= { dataDomainMibTraps 8002 }

dIMMFailureAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            DIMM failure detected. DDFS can't be enabled.
        
   Action: 
            Replace the bad DIMMS.
        "
::= { dataDomainMibTraps 5048 }

memoryAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            Current memory is less than configured. DDFS can't be enabled.
        
   Action: 
            Check the DIMMS in DDR. Add new DIMMS and/or replace the bad DIMMS.
        "
::= { dataDomainMibTraps 5049 }

portPathDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A path is disabled. Multipath failover will not work correctly until the port is enabled.
    
   Action: 
        Verify all storage cabling is connected correctly.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com. 
    "
::= { dataDomainMibTraps 5050 }

diskPathRedundancy NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Since only one HBA controller has been detected, disk path redundancy is lost.
    
   Action: 
        If the HBA configuration is correct, disable multipath configuration. Otherwise: 1)Use 
        'disk multipath status' to examine connection status; 2)Verify all storage cabling is connected 
        correctly; 3)Use 'enclosure' commands to verify that HBA controllers are functional.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5051 }

missingPortConnection NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A port and the attached storage have disappeared.
    
   Action: 
        Verify the connection and any external storage connected to the missing port.
    "
::= { dataDomainMibTraps 5052 }

missingLunPath NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The number of active paths to a LUN has changed.
    
   Action: 
        If this is due to reconfiguration, verify the topology and disable then re-enable
        multipath monitoring to reset the monitoring. If not, verify the storage cabling and that
        all paths are connected.
    "
::= { dataDomainMibTraps 5053 }

missingDiskPath NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        The number of active paths to a disk has decreased. Multipath redundancy has been lost
        unless there are more than one connection paths.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5054 }

missingEnclosurePath NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The number of active paths to a disk has decreased. Multipath redundancy has been lost
        unless there are more than one connection paths.
    
   Action: 
        Use 'enclosure' commands to verify that HBAs are functional.
        If this condition is due to configuration change: verify the topology, and then disable and 
        re-enable disk multipath monitoring to reset connection counts.
        If not, verify the storage cabling is connected correctly.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5055 }

interfaceConnectivityDown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, ifIndex, ifDescr }
STATUS current
   DESCRIPTION
   "Description: 
        Network connectivity on this interface is down.
        This interface is not pingable and cannot be used to send or receive any data.
        All data transfers using this interface will fail.  
    
   Action: 
       Please view the Hardware->Network GUI page, or use the 'net show settings' and 'net show config' CLIs to check the current configuration.
       Make sure that the cable is securely connected to both endpoints and that the interfaces on both ends are properly configured.
       Otherwise the cable or network hardware could be faulty and may need to be replaced.
    "
::= { dataDomainMibTraps 6009 }

interfaceConnectivityIntermittent NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, ifIndex, ifDescr }
STATUS current
   DESCRIPTION
   "Description: 
        Network connectivity on this interface is intermittent. Data may not be sent or received successfully. 
        Data transfers using this interface may experience performance degradation or intermittent failures.
    
   Action: 
        Please view the Hardware->Network GUI page, or use the 'net show settings' and 'net show config' CLIs to check the current configuration.
        Make sure that the cable is securely connected to both endpoints and that the interfaces on both ends are properly configured.
        Otherwise the cable or network hardware could be faulty and may need to be replaced.
        If the network is configured properly and the problem still persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6010 }

interfaceMisconfiguration NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, ifIndex, ifDescr }
STATUS current
   DESCRIPTION
   "Description: 
        Network interface configuration problem detected. This may be a result of changing the NIC hardware without reconfiguring the interface.
        This could indicate an unsupported configuration, network environment or hardware problem.
        A misconfigured interface can potentially cause connectivity problems on subsequent reboots.
    
   Action: 
        Please view the Hardware->Network GUI page, or use 'net show settings', 'net config' and 'net show hardware' to check network configuration.
        Try to fix the problem by reconfiguring the interface with 'net config'.
    "
::= { dataDomainMibTraps 6011 }

interfaceConnectivityUpAndRunning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, ifIndex, ifDescr }
STATUS current
   DESCRIPTION
   "Description: 
        Network interface is up and running for a period of time after experiencing instability.
    
   Action: 
        No action needed.
    "
::= { dataDomainMibTraps 6020 }

duplicateAddressDetection NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        This IP address has been detected on the network. Connections to this IP address will be adversely impacted.
    
   Action: 
        Make sure the local and remote systems are using different IP addresses. Clear the alert once the problem is resolved.
    "
::= { dataDomainMibTraps 6530 }

invalidNICSlot NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Network interface cards must be installed in their designated slots.
        The network card may not function correctly when installed in the wrong slot.
    
   Action: 
        Please make sure that the Network interface card is installed in a valid slot.
    "
::= { dataDomainMibTraps 6512 }

unsupportedNIC NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An unsupported network interface card is installed in the system.
        Having an unsupported card installed in the system can cause unexpected errors.
    
   Action: 
        Remove the card from system.
    "
::= { dataDomainMibTraps 6513 }

tcpZeroWindowAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The TCP connection is active but data is not being transferred.
        This may cause other communication failures.
    
   Action: 
        Check the remote application that uses this TCP connection.
        Check the network path between the local and remote systems.
        If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6101 }

dNSUnresponsive NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        No DNS servers responded to a DNS query. This may be caused by a network failure or a DNS server problem.
        System functions that require name address resolution will fail.
    
   Action: 
        Check the network path and the DNS server to establish communication or remove the DNS entries and add host entries to the hosts table.
        If this problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 7504 }

nISCommFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Unable to query information from the NIS server. 
        This could be a network problem or an authentication failure with the NIS server.
        This could prevent NIS servers from completing backups or restoring existing backups.
        This alert will clear when communication is established with the NIS server.
    
   Action: 
        Verify network connection to the NIS server and verify NIS configurations for both systems.
        If this problem persists, contact your contracted support provider or visit us online at https://support.emc.com .
    "
::= { dataDomainMibTraps 7501 }

iOModuleMacFault NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A Mac fault has been detected with an I/O module and it will not function correctly.
    
   Action: 
        The I/O module must be replaced.Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10013 }

missingSlaveInterface NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The bonded interface has been created with the remaining interfaces and
        may experience degraded performance or reduced failover capability.
    
   Action: 
        Replace the network interface or reconfigure the bonded interface to
        restore functionality. If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10523 }

nTPDFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
      Network Time Protocol (NTP) daemon is not running. NTP daemon has detected a
      time difference between this system and the configured network time server
      (drift) greater than the configured maximum and has shut down.
    
   Action: 
      Disable NTP. Verify time on the NTP server. Set system date/time to match the
      time on the NTP server. Enable NTP. If this problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 7505 }

nvramWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
			DDFS is disabled due to a problem with the NVRAM subsystem.
        
   Action: 
			Check for other NVRAM-related alerts and follow the action described in those alerts.
        "
::= { dataDomainMibTraps 5059 }

nvramBatteryAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, nvramBatteryStatus }
STATUS current
   DESCRIPTION
   "Description: 
		DD OS detected an NVRAM card Battery Unit hardware fault.
		Probable causes:  1) Battery charging is disabled, or  2) One or more batteries are not charging.
    
   Action: 
		Run CLI command 'system show nvram' to check battery status.
		If the problem persists,   contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5060 }

nvramErrorAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        DD OS detected an NVRAM card fault.
        Probable cause: Excessive correctable errors exceed the warning threshold number.
    
   Action: 
        The NVRAM card may need to be replaced.
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5061 }

nvramBatteryLowChargeAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The NVRAM battery is charging, but is below the threshold.  
        The filesystem will not start until the battery charge reaches the threshold.
    
   Action: 
        Wait for the battery charge to reach threshold.
        If the battery does not reach threshold within 3 hours, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 5508 }

ext3NvlogDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
		DDOS RAID log is disabled due to a problem with the NVRAM subsystem.
        
   Action: 
		Check for other NVRAM-related alerts and follow the action described in those alerts.
        "
::= { dataDomainMibTraps 5527 }

nvramHWAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The NVRAM card is not in a usable state.
        The file system cannot be accessed until the fault is corrected or the NVRAM card is replaced.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6504 }

nvramBattAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The remote battery for an NVRAM card is not in a usable state.
        The file system cannot be accessed until the fault is corrected or the NVRAM remote battery is replaced.
    
   Action: 
        Check NVRAM battery status.
        Check the cables between the remote battery and the power source, and between the remote battery 
        and the NVRAM card. If the cables are correctly connected and the problem persists, 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6507 }

nvramEnvAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
	The temperature sensor on the NVRAM card or the remote battery detected a temperature 
        that is higher than the warning threshold.  If there are other environmental alerts, 
        this may indicate a cooling problem where the system is installed.  
        If there are no other environmental alerts, this may indicate a problem with this component.  
        When the temperature is reduced, this alert will clear.
    
   Action: 
        Check cooling and airflow around the system unit.  Remove any blockage, or ensure 
        the environmental systems are operating correctly, and the environment is at a normal 
        temperature.  If there is no problem in the environment, or if only the NVRAM or 
        remote battery are reporting this problem, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6505 }

nvramCondAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The NVRAM battery power or charging has been set as disabled. 
        This condition is either the result of an explicit setting, or indicates a fault 
        in the NVRAM card or battery unit. 
    
   Action: 
        If the NVRAM battery power was explicitly disabled, enable it using 
        the reverse of the procedure used to disable it.  If not, reboot the system.  
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6508 }

nvramEventHWAlert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        NVRAM firmware detects an NVRAM card hardware fault.
    
   Action: 
        The NVRAM card may need to be replaced. Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6506 }

nvramBattEndOfLife NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The remote battery for NVRAM us currently usable but is approaching the end of its life.
        If the battery is not replaced before it reaches end of its life, the filesystem will be disabled.
    
   Action: 
        To schedule a replacement, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6545 }

phyalert NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Enclosure slot has been disabled due to continuous discovery caused by a failed drive.
    
   Action: 
        Replace the failed drive.
        Use 'disk rescan' command specifying the disk and enclosure id to re-enable the slot.
    "
::= { dataDomainMibTraps 5062 }

mtreeQuotaSoftLimit NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
	The Soft Limit Quota for this MTree has been reached.  This will not affect the operation of the system and is only a warning.
    If a Quota Hard Limit is set for this MTree, you are approaching that limit and operations writing data to this MTree may fail if the hard limit is reached.
    
   Action: 
     To clear this alert, either raise the quota limits or reduce the data in the MTree by deleting files. 
    "
::= { dataDomainMibTraps 6007 }

mtreeQuotaHardLimit NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
	The Hard Limit Quota for this MTree has been reached. No more data will be written to this MTree.
    If a backup operation was running, it probably failed and must be restarted after clearing this condition.
    
   Action: 
	To clear this alert, either raise the Quota Hard Limit for this MTree, or reduce the data in the MTree by deleting files.
    "
::= { dataDomainMibTraps 6008 }

storageUnitStreamSoftLimit NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Stream soft limit quota for this storage-unit and type of stream has been exceeded.
        Crossing this limit may cause reduced performance as the system hard limits are reached.
    
   Action: 
        To clear this alert, configure the backup application to use fewer concurrent streams.
        Otherwise, increase the soft limit quota on this storage-unit for this stream type.
    "
::= { dataDomainMibTraps 7517 }

replProgressThreshholdReached NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, replStatus }
STATUS current
   DESCRIPTION
   "Description: 
       Collection replication has not made progress and there is data waiting to be replicated.
       This means your replica is not up to date.
    
   Action: 
        - check the 'replication status' or the GUI for errors
        - check the replica filesystem
        - check the network
        - if problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5063 }

replNeedResync NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, replStatus }
STATUS current
   DESCRIPTION
   "Description: 
        Replication will not proceed until this context is resynced. 
    
   Action: 
        To re-establish replication
            - Note the context information
            - break the context
            - reconfigure contexts on both source and destination
            - resync context on the source
    "
::= { dataDomainMibTraps 5064 }

replLogFull NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        No data can be written to the filesystem. Too much data remains to be replicated.
        This can be caused by disabled contexts or slow replication.
        When the condition is resolved, the filesystem will be writable again.
    
   Action: 
        Refer to knowledge base for articles discussing ways to reduce replication lag.
    "
::= { dataDomainMibTraps 5065 }

replIncompatibleWorm NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        New retention-locked files detected on destination during replication resync. Resync cannot proceed.
    
   Action: 
        Break the existing context and resync to a new destination.
    "
::= { dataDomainMibTraps 5066 }

replDestNotConfigured NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, replConfigDest }
STATUS current
   DESCRIPTION
   "Description: 
        Replication context on the destination is missing or mis-matched. Cannot initialize context.
    
   Action: 
        - Check both the source and destination configured context URL pair. The context is identified by the destination URL.
        - Make sure the destination has a matching configuration for this context.
    "
::= { dataDomainMibTraps 5067 }

replLagThreshholdReached NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Replication lag threshold has been exceeded.
    
   Action: 
        Refer to the knowledge base on ways to resolve the replication lag.
    "
::= { dataDomainMibTraps 5068 }

replPathTooLong NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       Path is too long
    
   Action: 
        Shorten the path.
    "
::= { dataDomainMibTraps 5531 }

missingCreplUnits NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The collection replication destination is not in a consistent state and will not be usable until matching retention units are added to the destination.
    
   Action: 
        Add the required retention units to the collection replication destination.
    "
::= { dataDomainMibTraps 6544 }

mtreeCascadeNeedResync NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, replStatus }
STATUS current
   DESCRIPTION
   "Description: 
       This Mtree is now a cascaded source.  Since this mtree already has data, this replication context requires a resync to proceed. 
    
   Action: 
        To re-establish replication
            - Note the context information
            - break the context
            - reconfigure contexts on both source and destination
            - resync context on the source
    "
::= { dataDomainMibTraps 7520 }

insecureEncryptedReplication NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
       Encrypted replication is configured between this system and the remote host.
       The remote host is running a DDOS version that does not support a fully secure communication protocol.
       Unauthorized individuals may be able to intercept and decrypt the replicated data.
    
   Action: 
       Upgrade the remote system to a release that supports the desired communication protocol.
       Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6102 }

suspendedMReplMissingUnits NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Mtree replication on this context cannot continue until all archive units used by this Mtree are online.
        Replication will resume automatically once all archive units come online.
    
   Action: 
        Return missing archive units to the system to resume MTree replication for this context.
        If the archive units cannot be restored, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10511 }

sASEnclosureCheck NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The number of enclosures connected to the port has exceeded the maximum supported limit.
        This may cause an unexpected behavior.
    
   Action: 
        Reduce the number of enclosures down to the limit for the system.
    "
::= { dataDomainMibTraps 5069 }

sASTopologyCheck NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Invalid cabling has been detected. This may cause unexpected behavior.
    
   Action: 
        Refer to the cabling guide. Verify the connectivity of the SAS enclosures by using command
         'enclosure show topology'.
    "
::= { dataDomainMibTraps 5070 }

sASPortDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Excessive errors have caused this port to be disabled. This could be caused by faulty cables or shelf controllers.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com .
    "
::= { dataDomainMibTraps 5071 }

sASHBAFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Storage is no longer accessible through this HBA.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6514 }

sASHBAErrors NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A SAS HBA has detected correctable errors exceeding the warning threshold.
        This will reduce throughput and may indicate a failing HBA.
    
   Action: 
        If this condition persists for more than 24 hours, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6515 }

unsupportedSASDevice NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A SAS HBA has reported a device that is not recognized.
        This device is not usable and may adversely affect the system.
    
   Action: 
        Identify the device and remove it from the topology.
        If you believe it is a supported device, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6516 }

invalidEnclosureTopology NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The cables connecting enclosures to the controller are not installed correctly. This can cause unpredictable system behavior. Recable the enclosures immediately.
    
   Action: 
        Display the topology to identify the invalid path(s). Power down the system and cable the enclosures according to the cabling guide for your system.
    "
::= { dataDomainMibTraps 7506 }

diskPathSpeedDegraded NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        SAS connection speed is degraded and could reduce disk performance.
    
   Action: 
        Check cable seating and replace if needed.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7507 }

enclosureMixType NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An unsupported mix of enclosure models have been detected. For example, a mixture of ES30s and ES20s.
    
   Action: 
        Refer to the configuration guide for valid cabling configurations.
    "
::= { dataDomainMibTraps 5528 }

enclosureMixDriveType NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An unsupported mix of enclosures with different drive types may impact performance and lead to unexpected system behavior.
    
   Action: 
        Recable the enclosures to eliminate the unsupported mix. Refer to the configuration guide for valid cabling configurations.
    "
::= { dataDomainMibTraps 10515 }

sCSITGTInvalidRegistry NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        A registry entry used in the SCSI Target system is invalid.
        The SCSI Target services, including VTL, DD-BOOST FC and VDISK will be
        inaccessible until this problem is resolved.
    
   Action: 
        The registry entry must be corrected.  
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6539 }

sSLCertificateCorrupted NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            The SSL certificate used to establish mutual trust between Data Domain systems is broken.        
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com
        "
::= { dataDomainMibTraps 5072 }

unusableHostCertificate NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
            Enterprise Manager will not start up using the imported certificate. However, it will start up using the default self-signed certificate. 
 Following is the list of possible causes of this alert,
    - the file system is locked
    - the system passphrase is missing
    - the imported certificate is corrupted
        
   Action: 
            Unlock the file system if it is locked.
            Enter the system passphrase if it is missing.
            Delete and reimport the current host certificate if it is corrupted.
            In all cases, the Enterprise Manager needs to be restarted.
            If the condition persists, contact your contracted support provider or visit us online at https://support.emc.com
        "
::= { dataDomainMibTraps 6017 }

missingHostCertificate NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
            The imported certificate is missing. However, the Enterprise Manager will start up using the default self-signed certificate.
        
   Action: 
            If you want to use your own certificate, import it again.
        "
::= { dataDomainMibTraps 6018 }

expiredHostCertificate NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
            Communication using this certificate is vulnerable to disclosure.
        
   Action: 
            Replace the host certificate with a valid certificate.
        "
::= { dataDomainMibTraps 6538 }

sMSUnresponsive NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The System Management Service has reached the maximum number of concurrent 
        requests and has been unresponsive for over five minutes. This may indicate 
        a serious condition that may need to be addressed by support. The system is 
        unable to accept additional commands.
    
   Action: 
        If this condition persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7500 }

mailserverError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            There is a problem communicating with the configured mail server.
            The system will not be able to send out any email notifications that includes alerts, autosupports and daily summary emails.
        
   Action: 
            Make sure that the mail server is configured correctly.
            Verify the configured mail server by sending out a test email from the system.
            If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
        "
::= { dataDomainMibTraps 6511 }

snapshotOver90Percent NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Current number of snapshots for an Mtree is at 90% or more of the maximum number of snapshots allowed.
    
   Action: 
        Consider expiring existing snapshots of that Mtree with the 'snapshot expire' command or 
        adjusting scheduled snapshot retention periods with the 'snapshot schedule modify' command.
    "
::= { dataDomainMibTraps 5075 }

snapshotLimitReached NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Data Domain system has a limit on the number of existing snapshots held at once for a single Mtree.
        Once this limit is reached no more snapshots can be created of that Mtree.
    
   Action: 
        Consider expiring existing snapshots of that Mtree with 'snapshot expire' command to make room for newer ones.
    "
::= { dataDomainMibTraps 5076 }

sNTZMultipleIterations NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
    Indicates that sanitization will be run in multiple iterations.
    In the worst case this can take 15-20 days for 280 TBs. It means 
    that the system will not run at full speed for that period of 
    time and that GC will not be run to reclaim space. However, 
    sanitization will also reclaim space back but it might not be 
    as efficient as GC is.

   Action: 
    Make sure that you have enough space to not run out of space and
    that you can wait that many days in the worst case. You should have
    at least 20% of physical space available. Other option is
    to abort sanitization, delete a bunch of files and try again. You can
    do this process iteratively until you do not get this alert anymore. 
"
::= { dataDomainMibTraps 5077 }

coredumpWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        /ddvar might soon run out of space to save system core dumps.
    
   Action: 
        Remove unwanted files from /ddvar to free up some space.
        Lack of space can result in missing core dumps that will hamper debugging operations.
    "
::= { dataDomainMibTraps 5078 }

coredumpDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Space in /ddvar is not sufficient enough for saving system core dumps.
    
   Action: 
        Remove unwanted files from /ddvar to free up some space.
        Lack of space can result in missing core dumps that will hamper debugging operations.
    "
::= { dataDomainMibTraps 5079 }

spaceOver80Percent NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, fileSystemSpaceUsed }
STATUS current
   DESCRIPTION
   "Description: 
        Space usage has exceeded 80% of the total capacity.
        Lack of space can result in missing important logs or loss of file system functionality.
    
   Action: 
        If it is in root, contact your contracted support provider or visit us online at https://support.emc.com to free up some space.
        If it is in /ddvar, remove unwanted files to free up some space.
        If it is in active tier, remove unwanted files and start file system cleaning or add storage.
        If it is in archive tier, remove unwanted files or add more archive units to the filesystem.
    "
::= { dataDomainMibTraps 5080 }

spaceOver90Percent NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, fileSystemSpaceUsed }
STATUS current
   DESCRIPTION
   "Description: 
        Space usage has exceeded 90% of the total capacity.
        Lack of space can result in missing important logs or loss of file system functionality.
    
   Action: 
        If it is in root, contact your contracted support provider or visit us online at https://support.emc.com to free up some space.
        If it is in /ddvar, remove unwanted files to free up some space.
        If it is in active tier, remove unwanted files and start file system cleaning or add storage.
        If it is in archive tier, remove unwanted files or add more archive units to the filesystem.
    "
::= { dataDomainMibTraps 5081 }

spaceReclRestartFailed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Archive space reclamation was running before the last shutdown of the filesystem and could not be restarted automatically.
        Space will not be reclaimed in the archive tier until space reclamation is restarted.
    
   Action: 
        Start space reclamation.
    "
::= { dataDomainMibTraps 6531 }

spaceReclMissingUnit NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Archive units are missing. Space reclamation cannot run unless all configured units are attached.
        Space will not be reclaimed in the archive tier until space reclamation is restarted.
    
   Action: 
        Return missing archive units to the system and start space reclamation.
    "
::= { dataDomainMibTraps 6532 }

spaceReclUnitReclaimed NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        All space on the archive unit has been reclaimed. This unit is available to reuse.
    
   Action: 
        No action is required.
    "
::= { dataDomainMibTraps 6533 }

spaceReclError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Archive space reclamation has stopped due to the specified error.
        Space will not be reclaimed in the archive tier until the error condition is resolved and space reclamation is restarted.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6534 }

spaceReclSuspended NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Archive space reclamation has suspended due to the specified error.
        Space will not be reclaimed in the archive tier until the error condition is resolved and space reclamation is resumed.
    
   Action: 
         Resume space reclamation after the underlying error condition is corrected. If problem persists,
         Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 7518 }

spaceReclUnitError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Space reclamation encountered an error and cannot proceed on the specified archive unit. 
        If you have more than one archive unit, space reclamation will continue on the next unit. 
        Space will not be reclaimed in this archive unit until the error condition is resolved 
        and space reclamation finishes successfully on this unit.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 7523 }

diskAccessError NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        A hardware fault has been detected for this drive. The drive or the cabling to it may have a problem.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5082 }

diskFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        Unable to communicate with a disk. This disk cannot be used at this time.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5083 }

diskTemperatureWarning NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskTemperature }
STATUS current
   DESCRIPTION
   "Description: 
        The disk temperature has exceeded a specified threshold. If the temperature continues to increase,
        the system could fail or the system could be shut down.
    
   Action: 
        If the temperature is high due to high activity for a short period, it should return to normal in a short time.
        Verify the environment is at its normal temperature. If not, correct this condition.
        Check for fan failures and free air flow.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 5084 }

diskTemperatureShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        One or more disks have exceeded the shut down temperature threshold. The system is shutting down.
    
   Action: 
        Verify the environment is at its normal temperature. If not, correct this condition.
        Check for fan failures and free air flow.
    "
::= { dataDomainMibTraps 5085 }

unsupportedHardwareSpareSize NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        The disk capacity is too small for it to be of any use in the system.
    
   Action: 
        Replace with a larger capacity disk.
    "
::= { dataDomainMibTraps 5086 }

missingDiskGroup NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        One or more critical disks/LUNs are unavailable.
    
   Action: 
        Make sure all storage is securely connected.
        Look at alert history for any path failures.
        Check for any core files, a critical layer may be unavailable.
        Verify that all storage is working and recognized correctly.
    "
::= { dataDomainMibTraps 5087 }

diskGroupReconstructionNoProgress NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        One or more disks are failed and the RAID reconstruction is not running.
    
   Action: 
        Make sure there is/are spare disk(s) in the system.
    "
::= { dataDomainMibTraps 5088 }

diskGroupReconstruction NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Two or more disks are failed and the RAID protection is in critical state.
        If an additional disk fails, there might be permanent loss of data.
        The system will shut down if the problem is not fixed.
    
   Action: 
        Make sure there is/are spare disk(s) in the system.
    "
::= { dataDomainMibTraps 5089 }

diskGroupReconstructionShutdown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Two or more disks are failed and the RAID protection is in critical state for over a
        considerable period of time. If an additional disk fails, there might be permanent loss of
        data. The system will shut down.
    
   Action: 
        Restart the system and make sure there is/are spare disk(s) in the system.
    "
::= { dataDomainMibTraps 5090 }

diskGroupReconstructionCritical NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Two or more disks are failed and the RAID protection is in critical state for over a
        considerable period of time. If an additional disk fails, there might be permanent loss of
        data.
    
   Action: 
        Make sure there is/are spare disk(s) in the system.
    "
::= { dataDomainMibTraps 5091 }

diskUnknown NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Disk is unknown.
    
   Action: 
        Add disk to the system.
    "
::= { dataDomainMibTraps 5092 }

lowSpares NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Not enough spare disks can be detected for this tier.
        There may be failed disks or connectivity issues with disks or enclosures.
    
   Action: 
        Replace any failed or absent disks. Check enclosure connectivity.
    "
::= { dataDomainMibTraps 5094 }

unsupportedConfigurationROL NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Raid on LUN from 4.8 is no longer supported.  Please follow instruction on how to increase 
        usable space in your filesystem. 
    
   Action: 
                1) migrate data away 
                2) do a filesystem destroy
                3) add your LUN
                4) migrate data back  
    "
::= { dataDomainMibTraps 5095 }

foreignEnclosure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, enclosureListNum }
STATUS current
   DESCRIPTION
   "Description: 
        The enclosure is unusable by the filesystem.
        Storage configuration can not be modified while a foreign device is present.
    
   Action: 
        If this is expected as part of a chassis swap or chassis upgrade, proceed with
        the headswap operation to make the foreign filesystem available.
        Otherwise contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 6019 }

sSDEndOfLife NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, diskSerialNumber }
STATUS current
   DESCRIPTION
   "Description: 
        The SSD has reached end of life and could fail at any time.
        The system may shut down unexpectedly if the drive is not replaced.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 6541 }

multipleDiskReadErrors NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        There are too many disks reporting read errors in the same RAID group.
        As each of these disks completes reconstruction it will be failed.
        If the ongoing RAID reconstruction fails, there might be permanent loss of data.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com to determine the number of drives that need to be replaced.
    "
::= { dataDomainMibTraps 6543 }

unsupportedDriveModel NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        This system only supports certain drive models.
        The unsupported drives are unusable and must be replaced for proper system operation.
    
   Action: 
        Identify and remove/replace the unsupported drives.
        For assistance, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7001 }

driveMixType NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Mixing drive types within an enclosure is not supported and may cause unexpected system behavior.
    
   Action: 
        Ensure that all drives within the enclosure are of the same type.
        For assistance, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7002 }

missingTierStorage NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        Critical storage resources are unavailable, preventing the tier from functioning.
    
   Action: 
        Verify all storage is functional and configured correctly.
        If the problem persists, contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 7522 }

storageMigrationCopyComplete NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description:                                                                                                                                                                         
        All data has been moved from old enclosures to the new enclosures.
        Storage migration must be finalized to decommission old enclosures and
        add new capacity to the file system.
    
   Action:  
        Finalize the storage migration.
    "
::= { dataDomainMibTraps 10501 }

storageMigrationCannotResume NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The system has detected a condition which prevented the storage
        migration from continuing and has suspended the data copy phase.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10500 }

storageMigrationUserSuspend NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        An administrator has suspended the data copy phase of storage migration.
        Data copy cannot proceed until it is manually resumed.
    
   Action: 
        Resume the storage migration.
    "
::= { dataDomainMibTraps 10502 }

foreignPack NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, enclosurePackID }
STATUS current
   DESCRIPTION
   "Description: 
        The enclosure pack is unusable by the file system.
        Storage configuration can not be modified while a foreign device is present.
    
   Action: 
        If this is expected as part of a chassis swap or chassis upgrade, proceed with
        the headswap operation to make the foreign filesystem available.
        Otherwise contact your contracted support provider or visit us online at https://support.emc.com.
    "
::= { dataDomainMibTraps 10512 }

upgradeFailure NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            During the upgrade process, there was an error that requires manual intervention. The system is not usable.
        
   Action: 
            Contact your contracted support provider or visit us online at https://support.emc.com.
        "
::= { dataDomainMibTraps 6509 }

upgradeCompleted NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            The DD OS upgrade is completed successfully. The system will be ready to use when the filesystem is enabled.
        
   Action: 
            No action required.
        "
::= { dataDomainMibTraps 6510 }

upgradeInProgress NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
            DD OS upgrade is in progress.
            The system will not be available for backup and restore operations.
            The alert will be cleared after the upgrade operation is complete.
        
   Action: 
            Allow the upgrade operation to complete. If the upgrade takes longer than usual, contact your contracted support provider or visit us online at https://support.emc.com
        "
::= { dataDomainMibTraps 10509 }

vDiskSCSITargetMismatch NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        VDisk configuration does not match SCSI Target configuration.
        Non-matching devices are not available and must be deleted or repaired.
    
   Action: 
        Contact your contracted support provider or visit us online at https://support.emc.com
    "
::= { dataDomainMibTraps 10513 }

tapeReposition NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        The tape volume was adjusted to the last consistent data position during
        VTL recovery. Recent pending updates to the volume may have been discarded.
    
   Action: 
        Verify that any backup sent to the affected tape at the time of this alert
        completed successfully. Reissue the backup if necessary.
    "
::= { dataDomainMibTraps 6542 }

duplicateVTLPoolNames NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        VTL directory pool and a VTL mtree pool have been detected with the same name.
        Viewing pool information will only retrieve tapes from the mtree pool. Tapes in the
        directory pool will not be visible to DataDomain system administration interfaces.
        Backups and restores using the directory pool will operate normally.
    
   Action: 
        Rename one of the pools to make the name unique. The duplicate pool name may have been created as
        part of a replication pair.If problem persists, contact your contracted support provider or visit us online at https://support.emc.com
"
::= { dataDomainMibTraps 10010 }

vTLEnabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        VTL is enabled. VTL functionality is available to the user.
    
   Action: 
        Some backup applications may need manual rescanning for devices.
    "
::= { dataDomainMibTraps 11105 }

vTLDisabled NOTIFICATION-TYPE
   OBJECTS { systemSerialNumber, alertInfoDescription }
STATUS current
   DESCRIPTION
   "Description: 
        VTL is disabled. VTL functionality is unavailable to the user.
    
   Action: 
        No Action required.
    "
::= { dataDomainMibTraps 11106 }

-- **********************************************************************
--
-- DataDomainMibCompliance 
-- =======================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibConformance           (*******.4.1.19746.0)
--      dataDomainMibComplicances       (*******.4.1.19746.0.1)
--          dataDoamainMibCompliance    (*******.4.1.19746.0.1.1)
--
-- **********************************************************************

    dataDomainMibCompliances    OBJECT IDENTIFIER ::= { dataDomainMibConformance 1 }

    dataDomainMibCompliance MODULE-COMPLIANCE
        STATUS  deprecated
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup, nvramGroup, fileSystemGroup, alertsGroup, statisticsGroup,
                               replGroup, basicNotificationsGroup, nfsGroup, cifsGroup, vtlGroup }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   internalDiskStorageNotificationsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

    ::= { dataDomainMibCompliances 1 }

    dataDomainMibComplianceRev1 MODULE-COMPLIANCE
        STATUS  deprecated
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup,
                               nvramGroup,
                               fileSystemGroupRev1,
                               alertsGroup,
                               statisticsGroup, 
                               replGroup,
                               nfsGroup,
                               cifsGroup,
                               vtlGroup,
                               ddboostGroup,
                               ddsystemGroup,
                               artGroup,
                               mtreeGroup,
                               enclosureGroup,
                               networkGroup,
                               generatedNotificationsGroup
                             }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

            GROUP   managedObjectsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support multiple Data Domain Systems"

    ::= { dataDomainMibCompliances 2 }

 dataDomainMibComplianceRev2 MODULE-COMPLIANCE
        STATUS  deprecated
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup,
                               nvramGroup,
                               fileSystemGroupRev1,
                               alertsGroup,
                               statisticsGroup,
                               replGroup,
                               nfsGroup,
                               cifsGroup,
                               vtlGroup,
                               ddboostGroup,
                               ddsystemGroupRev1,
                               artGroup,
                               mtreeGroup,
                               enclosureGroup,
                               networkGroup,
                               generatedNotificationsGroup
                             }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

            GROUP   managedObjectsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support multiple Data Domain Systems"

    ::= { dataDomainMibCompliances 3 }

 dataDomainMibComplianceRev3 MODULE-COMPLIANCE
        STATUS  deprecated
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup,
                               nvramGroup,
                               fileSystemGroupRev1,
                               alertsGroup,
                               statisticsGroup,
                               replGroup,
                               nfsGroup,
                               cifsGroup,
                               vtlGroup,
                               ddboostGroupRev1,
                               ddsystemGroupRev1,
                               artGroup,
                               mtreeGroup,
                               enclosureGroup,
                               networkGroup,
                               generatedNotificationsGroup,
                               smtGroup,
                               quotaGroup
                             }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

            GROUP   managedObjectsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support multiple Data Domain Systems"

    ::= { dataDomainMibCompliances 4 }
    
 dataDomainMibComplianceRev4 MODULE-COMPLIANCE
        STATUS  deprecated
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup,
                               nvramGroup,
                               fileSystemGroupRev1,
                               alertsGroup,
                               statisticsGroup,
                               replGroup,
                               nfsGroup,
                               cifsGroup,
                               vtlGroup,
                               ddboostGroupRev2,
                               ddsystemGroupRev1,
                               artGroup,
                               mtreeGroup,
                               enclosureGroup,
                               networkGroup,
                               generatedNotificationsGroup,
                               smtGroup,
                               quotaGroup,
                               highAvailabilityGroup,
                               scsitargetObjectGroup
                             }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

            GROUP   managedObjectsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support multiple Data Domain Systems"

    ::= { dataDomainMibCompliances 5 }

 dataDomainMibComplianceRev5 MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "The compliance statement for SNMP entities which
            implement this MIB module."
        MODULE  -- this module
            MANDATORY-GROUPS { environmentalsGroup,
                               nvramGroup,
                               fileSystemGroupRev1,
                               alertsGroup,
                               statisticsGroup,
                               replGroup,
                               nfsGroup,
                               cifsGroupRev1,
                               vtlGroup,
                               ddboostGroupRev2,
                               ddsystemGroupRev1,
                               artGroup,
                               mtreeGroup,
                               enclosureGroup,
                               networkGroup,
                               generatedNotificationsGroup,
                               smtGroup,
                               quotaGroup,
                               highAvailabilityGroup,
                               scsitargetObjectGroup
                             }

            GROUP   internalDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support internal disk storage"

            GROUP   externalUnmanagedDiskStorageGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support external unmanaged disk storage"

            GROUP   managedObjectsGroup
            DESCRIPTION
                "This group is mandatory for SNMP entities which
                support multiple Data Domain Systems"

    ::= { dataDomainMibCompliances 6 }

-- **********************************************************************
--
-- DataDomainMibCompliance 
-- =======================
--
-- dataDomainMib                        (*******.4.1.19746)
--   dataDomainMibConformance           (*******.4.1.19746.0)
--      dataDoamainMibGroups            (*******.4.1.19746.0.2)
--
-- **********************************************************************

    environmentalsGroup OBJECT-GROUP
        OBJECTS {
            powerModuleDescription,
            powerModuleStatus,
            tempSensorDescription,
            tempSensorCurrentValue,
            tempSensorStatus,
            fanDescription,
            fanLevel,
            fanStatus,
            tempSensorTrapIndex,
            fanTrapIndex
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing environmental monitoring information."
        ::= { dataDomainMibGroups 1 }

    nvramGroup OBJECT-GROUP
        OBJECTS {
            nvramMemorySize,
            nvramWindowSize,
            nvramPCIErrorCount,
            nvramMemoryErrorCount,
            nvramBatteryStatus,
            nvramBatteryCharge,
            nvramHCMemorySize
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing nvram monitoring information."
        ::= { dataDomainMibGroups 2 }

    fileSystemGroup OBJECT-GROUP        
        OBJECTS {
            fileSystemStatus,
            fileSystemVirtualSpace,
            fileSystemResourceName,
            fileSystemSpaceSize,
            fileSystemSpaceUsed,
            fileSystemSpaceAvail,
            fileSystemPercentUsed,
            fileSystemSpaceCleanable,
            fileSystemCompressionPeriod,
            fileSystemCompressionStartTime,
            fileSystemCompressionEndTime, 
            fileSystemPreCompressionSize,
            fileSystemPostCompressionSize,
            fileSystemGlobalCompressionFactor,
            fileSystemLocalCompressionFactor,
            fileSystemTotalCompressionFactor,
            fileSystemReductionPercent
        }   
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing file system monitoring information."
        ::= { dataDomainMibGroups 3 }

    alertsGroup OBJECT-GROUP
        OBJECTS {
            currentAlertTimestamp,
            currentAlertDescription,
            currentAlertSeverity,
	    currentAlertID,
            alertHistoryTimestamp,
            alertHistoryDescription,
            alertHistorySeverity,
            alertHistoryStatus,
            alertInfoDescription
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing alert monitoring information."
        ::= { dataDomainMibGroups 4 }

    statisticsGroup OBJECT-GROUP
        OBJECTS {
            cpuAvgPercentageBusy,
            cpuMaxPercentageBusy,
            nfsOpsPerSecond,
            nfsIdlePercentage,
            nfsProcPercentage,
            nfsSendPercentage,
            nfsReceivePercentage,
            cifsOpsPerSecond,
            diskReadKBytesPerSecond,
            diskWriteKBytesPerSecond,
            diskBusyPercentage,
            nvramReadKBytesPerSecond,
            nvramWriteKBytesPerSecond,
            replInKBytesPerSecond,
            replOutKBytesPerSecond
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing statistics information."
        ::= { dataDomainMibGroups 5 }

    internalDiskStorageGroup OBJECT-GROUP
        OBJECTS {
            diskModel,
            diskFirmwareVersion,
            diskSerialNumber,
            diskCapacity,
            diskPropState,
	    diskPack,
            diskSectorsRead,
            diskSectorsWritten,
            diskTotalKBytes,
            diskBusy,
            diskPerfState,
            diskTemperature,
            diskTimeoutCount,
            diskReadFailCount,
            diskWriteFailCount,
            diskMiscFailCount,
            diskOffTrackErrCount,
            diskSoftEccCount,
            diskCrcErrCount,
            diskProbationalCount,
            diskReallocCount,
            diskErrState
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing internal disk monitoring information."
        ::= { dataDomainMibGroups 7 }

    externalUnmanagedDiskStorageGroup OBJECT-GROUP
        OBJECTS {
            diskModel,
            diskFirmwareVersion,
            diskSerialNumber,
            diskCapacity,
            diskPropState,
            diskSectorsRead,
            diskSectorsWritten,
            diskTotalKBytes,
            diskBusy,
            diskPerfState,
            diskPropTrapIndex,
            diskErrTrapIndex
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing external unmanaged disk monitoring information."
        ::= { dataDomainMibGroups 8 }

    basicNotificationsGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            powerSupplyFailedAlarm,
            systemOverheatWarningAlarm,
            systemOverheatAlertAlarm,
            systemOverheatShutdownAlarm,
            fanModuleFailedAlarm,
            nvramFailingAlarm,
            fileSystemFailedAlarm,
            fileSpaceMaintenanceAlarm,
            fileSpacePreWarningAlarm,
            fileSpaceWarningAlarm,
            fileSpaceSevereAlarm,
            fileSpaceCriticalAlarm,
            diskOverheatWarningAlarm, 
            diskOverheatAlertAlarm, 
            diskOverheatShutdownAlarm, 
            diskFailedAlarm, 
            diskNoSpareAlarm, 
            diskPathAlarm, 
            diskSASAlarm, 
            diskSASHBAAlarm, 
            snapshotFullAlarm, 
            snapshotHWMAlarm, 
            clusterNodeAlarm, 
            clusterInterfaceAlarm, 
            replSyncAlarm, 
            systemStartupAlarm, 
            filesysRelaunchAlarm, 
            filesysDDGCFailedAlarm, 
            filesysGeneralProblemAlarm, 
            diskUnsupportedAlarm,
            eventIPMIUnmanageAlarm,
            raidReconSevereAlarm,
            raidReconCriticalAlarm,
            raidReconCriticalShutdownAlarm,
            raidGroupMissingAlarm
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing basic notifications."
        ::= { dataDomainMibGroups 9 }

    internalDiskStorageNotificationsGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            diskFailedAlarm,
            diskOverheatWarningAlarm,
            diskOverheatAlertAlarm,
            diskOverheatShutdownAlarm
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing internal disk storage notifications."
        ::= { dataDomainMibGroups 10 }

        replGroup OBJECT-GROUP
        OBJECTS {
                replState,
                replStatus,
                replFileSysStatus,
                replConnTime,
                replSource, 
                replDestination,
                replPreCompBytesSent,
                replPostCompBytesSent,
                replPreCompBytesRemaining,
                replPostCompBytesReceived,
                replThrottle,
                replSyncedAsOfTime,
                replConfigContextId,
                replConfigSource,
                replConfigDest,
                replConfigConnHost,
                replConfigConnPort,
                replConfigLowBWOptim,
                replConfigEnabled,
                replConfigTenantUnit,
                replHistoryDate,
                replHistoryTime,
                replHistoryPreCompWritten,
                replHistoryPreCompRemaining,
                replHistoryPreCompressed,
                replHistoryPostFiltered,
                replHistoryPostLowBwOptim,
                replHistoryPostLocalComp,
                replHistoryBytesNetwork,
                replHistorySyncedAsOfTime,
                replTrapContext,
                replPerformancePreCompKBPerSec,
                replPerformanceNetworkKBPerSec,
                replPerformanceStreams,
                replPerformanceBusyReading, 
                replPerformanceBusyMeta,
                replPerformanceWaitingDest,
                replPerformanceWaitingNetwork
        }
        STATUS  current
        DESCRIPTION
                "a collection of objects providing replication pair config and monitoring information."
        ::= { dataDomainMibGroups 11 }
        
    nfsGroup OBJECT-GROUP
        OBJECTS {
            nfsStatus,
            nfsClientPath,
            nfsClientClients,
            nfsClientOptions,
            nfsStatsExportPoint,
            nfsStatsFilesystemType,
            nfsStatsCacheEntry,
            nfsStatsFileHandleLookup,
            nfsStatsMaxCacheSize,
            nfsStatsCurrentOpenStreams,
            nfsActivePath,
            nfsActiveClients,
            nfsPortService,
            nfsPortPort
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing NFS monitoring information."
        ::= { dataDomainMibGroups 12 }

    cifsGroup OBJECT-GROUP
        OBJECTS {
            cifsStatus,
            cifsConfigMode,
            cifsConfigWINSServer,
            cifsConfigNetBIOSHostname,
            cifsConfigDomainController,
            cifsConfigDNS,
            cifsConfigGroupName,
            cifsConfigMaxConnection,
            cifsConfigMaxOpenFilesPerConnection,           
            cifsShareName,
            cifsSharePath,
            cifsShareClients,
            cifsShareUser,
            cifsShareComment,
            cifsShareBrowsing,
            cifsShareWriteable,
            cifsShareMaxConnection,
            cifsOptionsName,
            cifsOptionsValue
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing CIFS monitoring information."
        ::= { dataDomainMibGroups 13 }

    vtlGroup OBJECT-GROUP
        OBJECTS {
            vtlAdminState,
            vtlProcessState,
            vtlLibraryName,
            vtlLibraryVendor,
            vtlLibraryModel,
            vtlLibraryRevision,
            vtlLibrarySerial,
            vtlLibraryTotalDrives,
            vtlLibraryTotalSlots,
            vtlLibraryTotalCaps,
            vtlLibraryStatus,
            vtlDriveName,
            vtlDriveVendor,
            vtlDriveModel,
            vtlDriveRevision,
            vtlDriveSerial,
            vtlDriveLibraryName,
            vtlDriveStatus,
            vtlDriveTapeVolume,
            vtlGroupName,
            vtlGroupInitiaterCount,
            vtlGroupDeviceCount,
            vtlGroupDeviceGroupName,
            vtlGroupDeviceDeviceName,
            vtlGroupDeviceLun,
            vtlGroupDevicePrimaryPorts,
            vtlGroupDeviceSecondaryPorts,
            vtlGroupDeviceInUsePorts,
            vtlInitiatorName,
            vtlInitiatorStatus,
            vtlInitiatorGroup,
            vtlInitiatorWWNN,
            vtlInitiatorWWPN,
            vtlInitiatorPort,
            vtlPoolPool,
            vtlPoolStatus,
            vtlPoolTapes,
            vtlPoolSize,
            vtlPoolUsed,
            vtlPoolComp,
            vtlPortName,
            vtlPortID,
            vtlPortModel,
            vtlPortFirmware,
            vtlPortWWNN,
            vtlPortWWPN,
            vtlPortConnectionType,
            vtlPortSpeed,
            vtlPortEnabled,
            vtlPortStatus,
            vtlPortTrapIndex,
            vtlStatsPort,
            vtlStatsConrolCommands,
            vtlStatsWriteCommands,
            vtlStatsReadCommands,
            vtlStatsIn,
            vtlStatsOut,
            vtlStatsLinkFailures,
            vtlStatsLIPCount,
            vtlStatsSyncLosses,
            vtlStatsSignalLosses,
            vtlStatsPrimSeqProtoErrors,
            vtlStatsInvalidTxWords,
            vtlStatsInvalidCRCs,
            vtlTapeBarCode,
            vtlTapePool,
            vtlTapeLocation,
            vtlTapeState,
            vtlTapeSize,
            vtlTapeUsed,
            vtlTapeComp,
            vtlTapeModTime
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing VTL monitoring information."
        ::= { dataDomainMibGroups 14 }

    ddboostGroup OBJECT-GROUP
        OBJECTS {
            ddboostAccessClientsName,
            ddboostAccessClientsEncryStrength,
            ddboostAccessClientsAuthMode,
            ddboostInterface,
            ddboostifGroupMember,
            ddboostBackupConnections,
            ddboostRestoreConnections,
            ddboostControlConnections,
            ddboostTotalConnections,
            ddboostFileReplHistoryDirection,
            ddboostFileReplHistoryNetwork,
            ddboostFileReplHistoryPreComp,
            ddboostFileReplHistoryPostComp,
            ddboostFileReplHistoryLowBWOpt,
            ddboostFileReplHistoryErrors,
            ddboostFileReplHistoryDate,
            ddboostFileReplHistoryTime,
            ddboostFileReplStatsDirection,
            ddboostFileReplStatsNetworkSent,
            ddboostFileReplStatsPreCompSent,
            ddboostFileReplStatsFiltered,
            ddboostFileReplStatsLowBWOpt,
            ddboostFileReplStatsLocalComp,
            ddboostFileReplStatsCompRatio,
            ddboostIfGroupInterface,
            ddboostOptionsName,
            ddboostOptionsStatus,
            ddboostStatus,
            ddboostUser,
            ddboostIfGroupStatus,
            ddboostPreCompKBytesPerSecond,
            ddboostPostCompKBytesPerSecond,
            ddboostNetworkKBytesPerSecond,
            ddboostReadKBytesPerSecond,
            ddboostStatsBackupConn,
            ddboostStatsRestoreConn,
            ddboostStatsImageCreatesCount,
            ddboostStatsImageCreatesErrors,
            ddboostStatsImageDeletesCount,
            ddboostStatsImageDeletesErrors,
            ddboostStatsPrecompBytesReceived,
            ddboostStatsBytesAfterFiltering,
            ddboostStatsBytesAfterLc,
            ddboostStatsNetworkBytesReceived,
            ddboostStatsCompressionRatio,
            ddboostStatsTotalBytesReadCount,
            ddboostStatsTotalBytesReadErrors,
            ddboostStorageUnitName,
            ddboostStorageUnitBytes,
            ddboostStorageUnitGlobalComp,
            ddboostStorageUnitLocalComp,
            ddboostStorageUnitMetaData,
            ddboostFileRepliPerfInPreCompKBPerSec,
            ddboostFileRepliPerfInNetworkKBPerSec,
            ddboostFileRepliPerfOutPreCompKBPerSec,
            ddboostFileRepliPerfOutNetworkKBPerSec
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing DDBoost monitoring information."
        ::= { dataDomainMibGroups 15 }

    ddsystemGroup OBJECT-GROUP
        OBJECTS {
            systemLicenseKey,
            systemLicenseFeature,
            systemCapacityLicenseKey,
            systemCapacityLicenseFeature,
            systemCapacityLicenseModel,
            systemCapacityLicenseCapacity,
            systemHardwareSlot,
            systemHardwareVendor,
            systemHardwareDevice,
            systemHardwarePorts,
            systemPortsPort,
            systemPortsConnectionType,
            systemPortsLinkSpeed,
            systemPortsFirmware,
            systemPortsHardwareAddress,
            systemSerialNumber,
            systemCurrentTime,
            systemVersion,
            systemModelNumber,
            sysNotes,
            systemTimeZoneName,
            systemUserName,
            systemUserUID,
            systemUserRole,
            systemUserStatus
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing system monitoring information."
        ::= { dataDomainMibGroups 16 }

    artGroup OBJECT-GROUP
        OBJECTS {
            artConfigStatus,
            artConfigMigrationSchedule,
            artConfigDefaultAge,
            artConfigFileSystemClean,
            artConfigCompression,
            artMigrationPolicyMtreeName,
            artMigrationPolicyDefaultAge,
            artMigrationScheduleSchedule,
            artMigrationScheduleStatus
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing art monitoring information."
        ::= { dataDomainMibGroups 17 }

    mtreeGroup OBJECT-GROUP
        OBJECTS {
            mtreeCompressionMtreePath,
            mtreeCompressionPreCompGib,
            mtreeCompressionPostCompGib,
            mtreeCompressionGlobalCompFactor,
            mtreeCompressionLocalCompFactor,
            mtreeCompressionPostTotalCompFactor,
            mtreeCompressionTimePeriod,
            mtreeListMtreeName,
            mtreeListPreCompGib,
            mtreeListStatus,
            mtreeRetentionLockMtreeName,
            mtreeRetentionLockStatus,
            mtreeRetentionLockUUID,
            mtreeRetentionLockMinRetentionPeriod,
            mtreeRetentionLockMaxRetentionPeriod
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing mtree monitoring information."
        ::= { dataDomainMibGroups 18 }

    enclosureGroup OBJECT-GROUP
        OBJECTS {
            enclosureListNum,
            enclosureListModel,
            enclosureListSerialNum,
            enclosureListOemName,
            enclosureListOemValue,
            enclosureListCapacity,
            enclosurePackID
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing enclousure information."
        ::= { dataDomainMibGroups 19 }

    managedObjectsGroup OBJECT-GROUP
        OBJECTS {
            managedSystemHostname,
            managedSystemSerial,
            managedSystemState,
            managedSystemStatus,
            managedSystemDDOSVersion,
            managedSystemHDSyncTime,
            managedSystemCDSyncTime,
            taskHistoryUser,
            taskHistoryID,
            taskHistoryParent,
            taskHistoryName,
            taskHistoryState,
            taskHistoryStartTime,
            taskHistoryDuration,
            taskActiveUser,
            taskActiveID,
            taskActiveParent,
            taskActiveName,
            taskActiveState,
            taskActiveStartTime,
            taskActiveDuration
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing ddms system information."
        ::= { dataDomainMibGroups 20 }

    networkGroup OBJECT-GROUP
        OBJECTS {
            dnsServer,
            searchDomainsName,
            snmpTrapHostsName,
            snmpTrapHostsVersion,
            nisDomain,
            nisServers,
            nisAdminGroups,
            nisUserGroups,
            nisBackupOperatorGroups,
            nisEnabled,
            nisStatus
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing network information."
        ::= { dataDomainMibGroups 21 }

    fileSystemGroupRev1 OBJECT-GROUP
        OBJECTS {
            fileSystemStatus,
            fileSystemVirtualSpace,
            fileSystemResourceName,
            fileSystemSpaceSize,
            fileSystemSpaceUsed,
            fileSystemSpaceAvail,
            fileSystemPercentUsed,
            fileSystemSpaceCleanable,
            fileSystemResourceTier,
            fileSystemCompressionPeriod,
            fileSystemCompressionStartTime,
            fileSystemCompressionEndTime,
            fileSystemPreCompressionSize,
            fileSystemPostCompressionSize,
            fileSystemGlobalCompressionFactor,
            fileSystemLocalCompressionFactor,
            fileSystemTotalCompressionFactor,
            fileSystemArchiveUnitName,
            fileSystemArchiveUnitState,
            fileSystemArchiveUnitStatus,
            fileSystemArchiveUnitStartTime,
            fileSystemArchiveUnitEndTime,
            fileSystemArchiveUnitSize,
            fileSystemArchiveUnitDiskGroups,
            fileSystemCleanStatus,
            fileSystemCleanSchedule,
            fileSystemCleanThrottle,
            fileSystemReductionPercent1,
            fileSystemOptionsName,
            fileSystemOptionsValue,
            fileSystemUpTime,
            fileSystemStatusMessage,
            fileSystemResourceTrapIndex
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing file system monitoring information."
        ::= { dataDomainMibGroups 22 }

     ddsystemGroupRev1 OBJECT-GROUP
        OBJECTS {
            systemLicenseKey,
            systemLicenseFeature,
            systemCapacityLicenseKey,
            systemCapacityLicenseFeature,
            systemCapacityLicenseModel,
            systemCapacityLicenseCapacity,
            systemHardwareVendor,
            systemHardwareDevice,
            systemHardwarePorts,
            systemHardwareSlotName,
            systemPortsPort,
            systemPortsConnectionType,
            systemPortsLinkSpeed,
            systemPortsFirmware,
            systemPortsHardwareAddress,
            systemSerialNumber,
            systemCurrentTime,
            systemVersion,
            systemModelNumber,
            systemTimeZoneName,
            systemUserName,
            systemUserUID,
            systemUserRole,
            systemUserStatus,
            systemActiveUserName,
            systemActiveUserIdleTime,
            systemActiveUserLoginTime,
            systemActiveUserLoginFrom,
            systemActiveUserTty
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing system monitoring information."
        ::= { dataDomainMibGroups 23 }

        smtGroup OBJECT-GROUP
        OBJECTS {
            smtStatus,
            tenantUnitListName,
            tenantUnitListNumberOfMgmtUsers,
            tenantUnitListNumberOfMtrees,
            tenantUnitListNumberOfDdboostStus,
            tenantUnitListTenantSelfServiceMode,
	    tenantUnitListParentTenantName,
	    tenantUnitListType,
            tenantUnitListSecurityMode,
            tenantUnitListNumberOfMgmtGroups,
            tenantUnitMgmtUserListUserRole,
            tenantUnitMtreeListMtreeName,
            tenantUnitDdboostStuListStuName,
            tenantUnitAdminIpInfoType,
    	    tenantInfoTenantName,
	    tenantInfoTenantUnitName,
            tenantUnitMgmtGroupListGroupRole,
            tenantUnitMgmtGroupListGroupType
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing Secure Multi-Tenancy (SMT) information."
        ::= { dataDomainMibGroups 24 }

        quotaGroup OBJECT-GROUP
        OBJECTS {
            quotaCapacityStatus,
            quotaCapacityMtreeName,
            quotaCapacityPreCompMiB,
            quotaCapacitySoftLimitMiB,
            quotaCapacityHardLimitMiB,
            quotaCapacityTenantUnit
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing quota information."
        ::= { dataDomainMibGroups 25 }

    ddboostGroupRev1 OBJECT-GROUP
        OBJECTS {
            ddboostAccessClientsName,
            ddboostInterface,
            ddboostifGroupMember,
            ddboostBackupConnections,
            ddboostRestoreConnections,
            ddboostControlConnections,
            ddboostTotalConnections,
            ddboostFileReplHistoryDirection,
            ddboostFileReplHistoryNetwork,
            ddboostFileReplHistoryPreComp,
            ddboostFileReplHistoryPostComp,
            ddboostFileReplHistoryLowBWOpt,
            ddboostFileReplHistoryErrors,
            ddboostFileReplHistoryDate,
            ddboostFileReplHistoryTime,
            ddboostFileReplStatsDirection,
            ddboostFileReplStatsNetworkSent,
            ddboostFileReplStatsPreCompSent,
            ddboostFileReplStatsFiltered,
            ddboostFileReplStatsLowBWOpt,
            ddboostFileReplStatsLocalComp,
            ddboostFileReplStatsCompRatio,
            ddboostIfGroupInterface,
            ddboostOptionsName,
            ddboostOptionsStatus,
            ddboostStatus,
            ddboostUserName,
            ddboostIfGroupStatus,
            ddboostPreCompKBytesPerSecond,
            ddboostPostCompKBytesPerSecond,
            ddboostNetworkKBytesPerSecond,
            ddboostReadKBytesPerSecond,
            ddboostStatsBackupConn,
            ddboostStatsRestoreConn,
            ddboostStatsImageCreatesCount,
            ddboostStatsImageCreatesErrors,
            ddboostStatsImageDeletesCount,
            ddboostStatsImageDeletesErrors,
            ddboostStatsPrecompBytesReceived,
            ddboostStatsBytesAfterFiltering,
            ddboostStatsBytesAfterLc,
            ddboostStatsNetworkBytesReceived,
            ddboostStatsCompressionRatio,
            ddboostStatsTotalBytesReadCount,
            ddboostStatsTotalBytesReadErrors,
            ddboostStorageUnitName,
            ddboostStorageUnitBytes,
            ddboostStorageUnitGlobalComp,
            ddboostStorageUnitLocalComp,
            ddboostStorageUnitMetaData,
            ddboostFileRepliPerfInPreCompKBPerSec,
            ddboostFileRepliPerfInNetworkKBPerSec,
            ddboostFileRepliPerfOutPreCompKBPerSec,
            ddboostFileRepliPerfOutNetworkKBPerSec
        }
        STATUS  deprecated
        DESCRIPTION
            "A collection of objects providing DDBoost monitoring information."
        ::= { dataDomainMibGroups 26 }

    ddboostGroupRev2 OBJECT-GROUP
        OBJECTS {
            ddboostAccessClientsName,
            ddboostInterface,
            ddboostifGroupMember,
            ddboostBackupConnections,
            ddboostRestoreConnections,
            ddboostControlConnections,
            ddboostTotalConnections,
            ddboostFileReplHistoryDirection,
            ddboostFileReplHistoryNetwork,
            ddboostFileReplHistoryPreComp,
            ddboostFileReplHistoryPostComp,
            ddboostFileReplHistoryLowBWOpt,
            ddboostFileReplHistoryErrors,
            ddboostFileReplHistoryDate,
            ddboostFileReplHistoryTime,
            ddboostFileReplStatsDirection,
            ddboostFileReplStatsNetworkSent,
            ddboostFileReplStatsPreCompSent,
            ddboostFileReplStatsFiltered,
            ddboostFileReplStatsLowBWOpt,
            ddboostFileReplStatsLocalComp,
            ddboostFileReplStatsCompRatio,
            ddboostIfGroupInterface,
            ddboostOptionsName,
            ddboostOptionsStatus,
            ddboostStatus,
            ddboostUserName,
	    ddboostUserDefaultTenantUnit,
	    ddboostIfGroupName,
	    ddboostIfGroupCurrentStatus,
            ddboostPreCompKBytesPerSecond,
            ddboostPostCompKBytesPerSecond,
            ddboostNetworkKBytesPerSecond,
            ddboostReadKBytesPerSecond,
            ddboostStatsBackupConn,
            ddboostStatsRestoreConn,
            ddboostStatsImageCreatesCount,
            ddboostStatsImageCreatesErrors,
            ddboostStatsImageDeletesCount,
            ddboostStatsImageDeletesErrors,
            ddboostStatsPrecompBytesReceived,
            ddboostStatsBytesAfterFiltering,
            ddboostStatsBytesAfterLc,
            ddboostStatsNetworkBytesReceived,
            ddboostStatsCompressionRatio,
            ddboostStatsTotalBytesReadCount,
            ddboostStatsTotalBytesReadErrors,
            ddboostStorageUnitName,
            ddboostStorageUnitBytes,
            ddboostStorageUnitGlobalComp,
            ddboostStorageUnitLocalComp,
            ddboostStorageUnitMetaData,
            ddboostStorageUnitStatus,
            ddboostStorageUnitPreComp,
            ddboostStorageUnitUser,
            ddboostStorageUnitReportPhysicalSize,
            ddboostFileRepliPerfInPreCompKBPerSec,
            ddboostFileRepliPerfInNetworkKBPerSec,
            ddboostFileRepliPerfOutPreCompKBPerSec,
            ddboostFileRepliPerfOutNetworkKBPerSec
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing DDBoost monitoring information."
        ::= { dataDomainMibGroups 27 }

        highAvailabilityGroup OBJECT-GROUP
        OBJECTS {
            haSystemName,
            haSystemStatus,
            haInterconnectStatus,
            haPrimaryHeartbeatStatus,
            haExternalLanHeartbeatStatus,
            haHardwareCompatibilityCheck,
            haSoftwareVersionCheck,
            highAvailabilityNodeName,
            highAvailabilityNodeId,
            highAvailabilityNodeRole,
            highAvailabilityNodeState,
            highAvailabilityNodeHealth,
            highAvailabilityComponentName,
            highAvailabilityComponentState
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing High Availability information."
        ::= { dataDomainMibGroups 28 }

        scsitargetObjectGroup OBJECT-GROUP
        OBJECTS {
            scsitargetAdminState,
            scsitargetProcessState,
            scsitargetGroupName,
            scsitargetGroupService,
            scsitargetGroupActiveState,
            scsitargetGroupNumInitiators,
            scsitargetGroupNumDevices,
            scsitargetInitiatorName,
            scsitargetInitiatorSystemAddress,
            scsitargetInitiatorGroup,
            scsitargetInitiatorService,
            scsitargetInitiatorAddressMethod,
            scsitargetInitiatorTransport,
            scsitargetInitiatorFcWwpn,
            scsitargetInitiatorFcWwnn,
            scsitargetInitiatorFcSymbolicPortName,
            scsitargetInitiatorEndpInitiator,
            scsitargetInitiatorEndpEndpoint,
            scsitargetInitiatorEndpStatus,
            scsitargetEndpointName,
            scsitargetEndpointCurrentSystemAddress,
            scsitargetEndpointPrimarySystemAddress,
            scsitargetEndpointSecondarySystemAddress,
            scsitargetEndpointEnabled,
            scsitargetEndpointStatus,
            scsitargetEndpointTransport,
            scsitargetEndpointFcWwnn,
            scsitargetEndpointFcWwpn,
            scsitargetPortSystemAddress,
            scsitargetPortEnabled,
            scsitargetPortStatus,
            scsitargetPortTransport,
            scsitargetPortOperationalStatus,
            scsitargetPortFcNpiv,
            scsitargetPortPortId,
            scsitargetPortModel,
            scsitargetPortFirmware,
            scsitargetPortFcBaseWwnn,
            scsitargetPortFcBaseWwpn,
            scsitargetPortFcCurrentWwnn,
            scsitargetPortFcCurrentWwpn,
            scsitargetPortFcp2Retry,
            scsitargetPortConnectionType,
            scsitargetPortLinkSpeed,
            scsitargetPortFcTopology,
            scsitargetPortEndpPort,
            scsitargetPortEndpEndpoint,
            scsitargetPortEndpEnabled,
            scsitargetPortEndpStatus,
            scsitargetPortEndpCurrentInstance,
            scsitargetDeviceName,
            scsitargetDeviceService,
            scsitargetDeviceActiveState,
            scsitargetDeviceAddress,
            scsitargetDeviceGrpDevice,
            scsitargetDeviceGrpGroupName,
            scsitargetDeviceGrpLun,
            scsitargetDeviceGrpPrimaryEndpoints,
            scsitargetDeviceGrpSecondaryEndpoints,
            scsitargetDeviceGrpInUseEndpoints
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing Scsitarget information."
        ::= { dataDomainMibGroups 29 }

    cifsGroupRev1 OBJECT-GROUP
        OBJECTS {
            cifsStatus,
            cifsConfigMode,
            cifsConfigWINSServer,
            cifsConfigNetBIOSHostname,
            cifsConfigDomainController,
            cifsConfigDNS,
            cifsConfigGroupName,
            cifsConfigMaxConnection,
            cifsConfigMaxOpenFiles,
            cifsShareName,
            cifsSharePath,
            cifsShareClients,
            cifsShareUser,
            cifsShareComment,
            cifsShareBrowsing,
            cifsShareWriteable,
            cifsShareMaxConnection,
            cifsOptionsName,
            cifsOptionsValue
        }
        STATUS  current
        DESCRIPTION
            "A collection of objects providing CIFS monitoring information."
        ::= { dataDomainMibGroups 30 }

END
