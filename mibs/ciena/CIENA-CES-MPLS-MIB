-- This file was included in WWP MIB release 04-16-00-0047
--
 -- CIENA-CES-MPLS-MIB.my
 --
 --

 CIENA-CES-MPLS-MIB DEFINITIONS ::= BEGIN

 IMPORTS                
   TimeTicks, Counter32, Integer32, Unsigned32, <PERSON><PERSON><PERSON><PERSON><PERSON>, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE                       
        FROM SNMPv2-SMI                 
   TruthValue, <PERSON><PERSON>layString, Mac<PERSON><PERSON><PERSON>, TEXTUAL-CONVENTION, RowStatus, RowPointer, StorageType,                
   TimeStamp                 
        FROM SNMPv2-TC
   AddressFamilyNumbers
            FROM IANA-ADDRESS-FAMILY-NUMBERS-MIB
   CienaGlobalState
	FROM CIENA-TC                                                                   
   cienaCesConfig, cienaCesNotifications 
	FROM CIENA-SMI
   cienaGlobalSeverity, cienaGlobalMacAddress
        FROM CIENA-GLOBAL-MIB
   MplsBitRate
        FROM MPLS-TC-STD-MIB;
	
 cienaCesMplsMIB MODULE-IDENTITY
            LAST-UPDATED "201611220000Z"
            ORGANIZATION "Ciena, Inc"
            CONTACT-INFO
                    "   Mib Meister
		              115 North Sullivan Road
	                   Spokane Valley, WA 99037
		              USA		 		
				    Phone:  ****** 242 9000
				    Email:  <EMAIL>"
            DESCRIPTION
                    "This MIB module defines the mgmt objects for the MPLS 
                     feature for LEOS based Ciena products."

	   REVISION
           "201611220000Z"
           DESCRIPTION
           "Added new OID cienaCesGmplsDynamicIngressCoroutedLspId in cienaCesGmplsDynamicIngressCoroutedTunnelTable"


	   REVISION
           "201611170000Z"
           DESCRIPTION
           "Added new OID cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeMode in cienaCesGmplsDynamicIngressCoroutedTunnelTable"
 
	   REVISION
           "201610210000Z"
           DESCRIPTION
           "Added new OID cienaCesTeLinkSrlgCount in cienaCesTeLinkTable"
 
	   REVISION
           "201610120000Z"
           DESCRIPTION
           "Added new OID cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingPackets,
            cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingPackets,
            cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingBytes,
            cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingBytes
            in cienaCesGmplsDynamicTransitCoroutedTunnelTable"
       
	   REVISION
           "201609210000Z"
           DESCRIPTION
           "Added new OID cienaCesTeResourceColorUseCount,cienaCesTeResourceColorGroupUseCount in cienaCesTeResourceColorsTable
            and cienaCesTeResourceColorsTable respectively"

	   REVISION
           "201608290000Z"
           DESCRIPTION
           "Added new OIDs cienaCesMplsPwConfigBandwidth, cienaCesMplsPwOperBandwidth and cienaCesMplsPwBandwidthState in cienaCesMplsPwTable" 

	   REVISION
           "201608220000Z"
           DESCRIPTION
           "Modified enums AutoSizeFailHdlr, AutoSizeState, PathDisjointType, PathDisjointMode and added new enums TEMode, MplsGlobalState.
            updated type of cienaCesGmplsDynamicIngressCoroutedTunnelLspReoptimization and cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeEnable, 
            and description of cienaCesGmplsDynamicIngressCoroutedTunnelAutoBackupEnable, cienaCesGmplsDynamicIngressCoroutedTunnelLspReOptTimeInterval, 
            cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeInterval, cienaCesGmplsDynamicIngressCoroutedTunnelMinBandwidth, 
            cienaCesGmplsDynamicIngressCoroutedTunnelMaxBandwidth, cienaCesGmplsDynamicIngressCoroutedTunnelIncBandwidth, 
            cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAll, cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAny, 
            cienaCesGmplsDynamicIngressCoroutedTunnelResourceExcludeAny in cienaCesGmplsDynamicIngressCoroutedTunnelTable. Added new OIDs 
            cienaCesTeResourceColorBitMask, cienaCesTeLinkMode and per priority unreserved bandwidth in cienaCesTeLinkTable and updated maximum bandwidth range"

	   REVISION
           "201607180000Z"
           DESCRIPTION
           "Updated the description of some of the attributes in following tables cienaCesMplsStaticTransitTunnelTable, 
            cienaCesMplsDynamicTransitTunnelTable, cienaCesGmplsStaticTransitUniDirTunnelTable,
            cienaCesGmplsStaticTransitCoroutedTunnelTable, cienaCesGmplsDynamicTransitUniDirTunnelTable, cienaCesGmplsDynamicTransitCoroutedTunnelTable"

	   REVISION
           "201607120000Z"
           DESCRIPTION
           "Added tables: cienaCesTeLinkTable, cienaCesTeResourceGrpTable, cienaCesTeResourceColorsTable, cienaCesTeLinkSrlgTable"

	   REVISION
           "201607110000Z"
           DESCRIPTION
           "Added attributes cienaCesGmplsDynamicIngressCoroutedTunnelLspReoptimization, cienaCesGmplsDynamicIngressCoroutedTunnelLspReOptTimeInterval,
            cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointType,  cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointMode,
            cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeEnable, cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeInterval,
            cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeFailureHdlr, cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeState,
            cienaCesGmplsDynamicIngressCoroutedTunnelMinBandwidth, cienaCesGmplsDynamicIngressCoroutedTunnelMaxBandwidth, cienaCesGmplsDynamicIngressCoroutedTunnelIncBandwidth,
            cienaCesGmplsDynamicIngressCoroutedTunnelCurBandwidth, cienaCesGmplsDynamicIngressCoroutedTunnelReqBandwidth,
            cienaCesGmplsDynamicIngressCoroutedTunnelUsedBandwidth, cienaCesGmplsDynamicIngressCoroutedTunnelClassType,
            cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAll, cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAny,
            cienaCesGmplsDynamicIngressCoroutedTunnelResourceExcludeAny in cienaCesGmplsDynamicIngressCoroutedTunnelTable"

	   REVISION
           "201607040000Z"
           DESCRIPTION
           "Added support for MPLS-CAC (Diffserv-TE) feature"

	   REVISION
           "201606100000Z"
           DESCRIPTION
           "Added cienaCesGmplsEncapTunnelResizeResultTrap, cienaCesGmplsEncapTunnelMbbResultTrap, cienaCesMplsCacInterfaceThresholdTrap"

	   REVISION
	   "201603110000Z"
	   DESCRIPTION
           "Added new OID for auto-backup tunnel in the cienaCesGmplsDynamicIngressCoroutedTunnelTable"

	   REVISION
           "201602150000Z"
           DESCRIPTION
           "Modified attributes related to if-num, LSP-id, src-tunnel-id and dest-tunnel-id in cienaCesGmplsStaticTransitCoroutedTunnelTable
	   to match with the previous release 6.13.1"

           REVISION
           "201601040000Z"
           DESCRIPTION
           "Added new attributes if-num, LSP-id, src-tunel-id and dest-tunnel-id in static tp
           corouted tunnel tables" 

           REVISION
           "201508180000Z"

           DESCRIPTION
           "Added new OIDs for the Traffic Statistics in the following tables cienaCesMplsStaticTransitTunnelTable, 
            cienaCesMplsDynamicTransitTunnelTable, cienaCesGmplsStaticTransitUniDirTunnelTable,
            cienaCesGmplsStaticTransitCoroutedTunnelTable, cienaCesGmplsDynamicTransitUniDirTunnelTable, cienaCesGmplsDynamicTransitCoroutedTunnelTable"
           
           REVISION
           "201502230000Z"
           DESCRIPTION
           "Added MS-PW support"

           REVISION
           "201412010000Z"
           DESCRIPTION
           "Added new OIDs cienaCesMplsPwProtectionRole & cienaCesMplsPwProtectionState
            and deprecate OID cienaCesMplsPwRole"

           REVISION
           "201411040000Z"
           DESCRIPTION
           "Added new OIDs cienaCesMplsPwReversion, cienaCesMplsPwRevertTime and modifed status of 
           cienaCesServiceDelimiterVID, cienaCesServiceDelimiterTPID to Deprecated in cienaCesMplsPwEntry table"            
           REVISION
	   "201406110000Z"  
	   DESCRIPTION
	   "Modified tables: cienaCesGmplsStaticEgressCoroutedTunnelTable, cienaCesGmplsDynamicEgressCoroutedTunnelTable, 
	   cienaCesMplsTunnelCosProfileTable."  

	       REVISION
		   "201404080000Z"
	       DESCRIPTION
		   "Modified the OID of cienaCesMplsAssociatedTunnelOperStateChgTrap. Updated the description of
		   cienaCesGmplsAssociatedTunnelAisFaultStateChgTrap and added varbinds cienaGlobalSeverity,  
		   cienaGlobalMacAddress to cienaCesMplsPwDown, cienaCesMplsPwUp and cienaCesMplsPwBundleActivePwChange
		   traps"
		     
            REVISION
		   "201402280000Z"
	       DESCRIPTION
		   "Created cienaCesMplsPw under cienaCesMplsMIBObjects. Added cienaCesMplsPwTable, cienaCesMplsPwCosProfileTable 
		   and cienaCesMplsPwVccvProfileTable under cienaCesMplsPw. Added a scalar cienaCesMplsGlobalNextFreeStaticVcLabel 
		   and cienaCesMplsFreeStaticTunnelTable under cienaCesMplsGlobal. Added BFD profile index in cienaCesMplsAssociatedTunnelTable,
		   cienaCesGmplsStaticIngressCoroutedTunnelTable, cienaCesGmplsDynamicIngressCoroutedTunnelTable, 
		   cienaCesGmplsStaticEgressCoroutedTunnelTable, cienaCesGmplsDynamicEgressCoroutedTunnelTable and 
		   cienaCesGmplsAssociatedTunnelTable. Added cienaCesMplsAssociatedTunnelOperStateChgTrap,
		   cienaCesGmplsEncapUnidirTunnelOperStateChgTrap, cienaCesGmplsEncapCoroutedTunnelOperStateChgTrap, 
		   cienaCesGmplsDecapCoroutedTunnelOperStateChgTrap, cienaCesGmplsTransitUnidirTunnelOperStateChgTrap, 
		   cienaCesGmplsTransitCoroutedTunnelOperStateChgTrap, cienaCesGmplsAssociatedTunnelOperStateChgTrap, 
		   cienaCesGmplsEncapUnidirTunnelGrpActiveEncapTunnelChangeTrap, cienaCesGmplsEncapCoroutedTunnelGrpActiveEncapTunnelChangeTrap, 
		   cienaCesGmplsDecapCoroutedTunnelGrpActiveEncapTunnelChangeTrap, cienaCesGmplsEncapCoroutedTunnelAisFaultStateChgTrap, 
		   cienaCesGmplsDecapCoroutedTunnelAisFaultStateChgTrap, cienaCesGmplsAssociatedTunnelAisFaultStateChgTrap, 
		   cienaCesGmplsTunnelAisFaultErrorTrap, cienaCesMplsPwDown, cienaCesMplsPwUp and cienaCesMplsPwBundleActivePwChange"  
                     
	       REVISION
		   "201309270000Z"
	       DESCRIPTION
		   "Created new table cienaCesMplsGlobal under cienaCesMplsMIBObjects and added cienaCesMplsTunnelCosProfileTable and 
		   added cienaCesMplsAttrs and cienaCesMplsGlobalTunnelPath from cienaCesMpls.Modified tunnel tables under cienaCesMpls, 
		   adding protection information, CosProfileIndex and CosProfileName, deprecated cos-profile parameters from 
		   individual tunnel tables. Added new tables cienaCesMplsAssociatedTunnelTable and cienaCesMplsTunnelARHopTable under 
		   cienaCesMpls.  Added gmpls tunnel cienaCesGmpls under cienaCesMplsMIBObjects which contains all tuunel table, 
		   path table, AR hop table"	
 
	       REVISION
	    	   "201305080000Z"
	       DESCRIPTION
		   "Modified the status of cienaCesMplsStaticIngressTunnelFixedExp under cienaCesMplsStaticIngressTunnelTable
		   and cienaCesMplsDynamicIngressTunnelFixedExp under cienaCesMplsDynamicIngressTunnelTable to deprecated. 
		   Added objects cienaCesMplsStaticIngressTunnelFixedTC under cienaCesMplsStaticIngressTunnelTable
		   and cienaCesMplsDynamicIngressTunnelFixedTC under cienaCesMplsDynamicIngressTunnelTable. Modified the
		   description of certain MIB tables and objects to match the description in 7.X"

	       REVISION
	    	   "201102020000Z"
	       DESCRIPTION
		   "Initial version."

           ::= { cienaCesConfig 18 }
        
--
 -- Textual convention
 --
 TTLPolicy ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "TTL Policy"
     SYNTAX       INTEGER {
                    decrement(0), 
                    fixed(1),
                    inherit(2)
                   } 
         
 PseudoWireType  ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Pseudo Wire Type"
     SYNTAX       INTEGER {
                    raw(1),
                    tagged(2)
                   }      
                   
 RCosPolicy ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Resolved Cos Policy"
     SYNTAX       INTEGER {
                    fixed(1),
                    exp-mapped(2)
                   }
 FCosPolicy   ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Frame Cos Policy"
     SYNTAX       INTEGER {
                    fixed(1),
                    rcos-mapped(2)
                   }  
 
 --StatusTLV  ::= TEXTUAL-CONVENTION
 --    STATUS       current
 --    DESCRIPTION  "Status TLV"
 --    SYNTAX       INTEGER {
 --                   on(1),
 --                   off(2)
 --                  } 
      
 PrivateForwardGroup ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Private Forwarding Group"
     SYNTAX       INTEGER {
                    groupA(1),        
                    groupB(2),
                    groupC(3)
                   } 
 
-- AdminState ::=   TEXTUAL-CONVENTION
--     STATUS       current                                
--     DESCRIPTION  "Virtual Circuit Admin State."
--     SYNTAX       INTEGER {
--                    enabled(1),
--                    disabled(2)
--                   } 
 OperState ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Virtual Circuit Opererational State."
     SYNTAX       INTEGER {
                    capable(1),
                    not-capable(2)
                   } 

 VCFailReason   ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Virtual Circuit Failure Reason"
     SYNTAX       INTEGER {
                    indeterminate(1),
                    ignore(2)
                  }  

 VCStatus   ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "VC Status. This is used for
                  both local and remote status faults"
     SYNTAX       BITS {
                    notForwarding(0),
		    servicePwRxFault(1),
		    servicePwTxFault(2),
                    psnPwIngressRxFault(3),
                    psnPwEgressTxFault(4),
                    pwStandby(5)
                  }  
          
 VCState    ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Current VC state"
     SYNTAX       INTEGER {
                    active(1),
                    notActive(2)
                   } 

 AttachGroupType ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Attach Group Type"
     SYNTAX       INTEGER {
                    static(1),
                    dynamic(2)
                   } 

 TunnelType ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Transport tunnel types for
                  MPLS VC"
     SYNTAX       INTEGER {
 	                   mplsStaticIngress(1),
			   mplsDynamicIngress(2),
			   mplsAssociated(3),
			   gmplsStaticIngressCorouted(4),
			   gmplsDynamicIngressCorouted(5),
			   gmplsStaticEgressCorouted(6),
			   gmplsDynamicEgressCorouted(7),
			   gmplsAssociated(8)
                   } 

 TunnelAisFault ::= TEXTUAL-CONVENTION
     STATUS       obsolete
     DESCRIPTION  "This is replaced by TunnelOamFault."
     SYNTAX       INTEGER {
                    fault(1),
                    nofault(2)
                   }

 TunnelOamFault ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Oam fault status on the tunnel."
     SYNTAX       INTEGER {
                    aisfault(1),
                    bfdfault(2),
                    aisbfdfault(3),
                    nofault(4)
                   }

 AutoSizeFailHdlr ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Auto size failure handler."
     SYNTAX       INTEGER {
                    none(1),
                    alarm(2),
                    mbb(3),
                    notApplicable(4)
                   }

 AutoSizeState ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Auto size dynamic states."
     SYNTAX       INTEGER {
                    ok(1),
                    upsizeInProgress(2),
                    upsizeFailed(3),
                    downsizeInProgress(4),
                    downsizeFailed(5),
                    notApplicable(6)
                   }

 AutoSizeMode ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Auto size mode."
     SYNTAX       INTEGER {
                    none(1),
                    cac(2),
                    utilization(3),
                    notApplicable(4)
                   }

 CacPolicy ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "MPLS Class Profile Cac Policy"
     SYNTAX       INTEGER {
                    invalid(1),
                    mam(2),
                    rdm(3)
                   }
 
 PathDisjointType ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Tunnel Path Disjoint Type"
     SYNTAX       INTEGER {
                    unknown(1),
                    none(2),
                    link(3),
                    srlg(4),
                    node(5),
                    srlgAndNode(6),
                    srlgOrNode(7),
                    notApplicable(8)
                   }
                           
 PathDisjointMode ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "Tunnel Path Disjoint Mode"
     SYNTAX       INTEGER {
                    none(1),
                    strict(2),
                    maximal(3),
                    notApplicable(4)
                   }

 SRLGState ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "SRLG state"
     SYNTAX       INTEGER {
                    active(1),
                    inActive(2)
                   } 

 TEMode ::= TEXTUAL-CONVENTION
     STATUS       current
     DESCRIPTION  "TE mode"
     SYNTAX       INTEGER {
                    diffServ(1),
                    diffServ-Te(2)
                   }

 MplsGlobalState  ::=  TEXTUAL-CONVENTION
     STATUS  current
     DESCRIPTION
         "This textual convention enumerates the administrative and operational state with not applicable value."
     SYNTAX       INTEGER {
                    enabled(1),
                    disabled(2),
                    notApplicable(3)
                   }
 

 --
 -- Node definitions
 --
 cienaCesMplsMIBObjects OBJECT IDENTIFIER ::= { cienaCesMplsMIB 1 }
 cienaCesMpls           OBJECT IDENTIFIER ::= { cienaCesMplsMIBObjects 1 }
 cienaCesGmpls          OBJECT IDENTIFIER ::= { cienaCesMplsMIBObjects 2 }   
 cienaCesMplsGlobal	OBJECT IDENTIFIER ::= { cienaCesMplsMIBObjects 3 }
 cienaCesMplsPw	        OBJECT IDENTIFIER ::= { cienaCesMplsMIBObjects 4 }
 cienaCesMplsTe         OBJECT IDENTIFIER ::= { cienaCesMplsMIBObjects 5 }

  
 -- Notifications   
 cienaCesMplsMIBNotificationPrefix  OBJECT IDENTIFIER ::=  { cienaCesNotifications 17 } 
 cienaCesMplsMIBNotifications       OBJECT IDENTIFIER ::=  { cienaCesMplsMIBNotificationPrefix 0 }
 cienaCesGmplsMIBNotifications       OBJECT IDENTIFIER ::=  { cienaCesMplsMIBNotificationPrefix 1 }
 cienaCesMplsPwMIBNotifications     OBJECT IDENTIFIER ::=  { cienaCesMplsMIBNotificationPrefix 2 }
                     
 --
 -- Mpls Global Attrs
 --                                                           
 cienaCesMplsGlobalAttrs  OBJECT IDENTIFIER ::= { cienaCesMpls 1 } 
 
  cienaCesMplsStaticAdminLabelRangeStart OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        " The beginning of the static administrative label range. There cannot be any 
          overlap between static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsGlobalAttrs 1 }
 
 cienaCesMplsStaticAdminLabelRangeEnd OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        " The end of the static administrative label range. There cannot be any overlap between
          static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsGlobalAttrs 2 }
 
 cienaCesMplsStaticOperLabelRangeStart OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        " The beginning of the static operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 3 }
 
 cienaCesMplsStaticOperLabelRangeEnd OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        " The end of the static operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 4 }

 
 cienaCesMplsDynamicAdminLabelRangeStart OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        "The beginning of the dynamic administrative label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 5 }

 cienaCesMplsDynamicAdminLabelRangeEnd OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        "The end of the dynamic administrative label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 6 }
 
 cienaCesMplsDynamicOperLabelRangeStart OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        "The beginning of the dynamic operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 7 }

 cienaCesMplsDynamicOperLabelRangeEnd OBJECT-TYPE
     SYNTAX      INTEGER ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      deprecated
     DESCRIPTION
        "The end of the dynamic operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsGlobalAttrs 8 }

 --
 -- MPLS Static Encap tunnel table
 --

 cienaCesMplsStaticIngressTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsStaticIngressTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static encap tunnels."

     ::= { cienaCesMpls 2 }
     
 cienaCesMplsStaticIngressTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsStaticIngressTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS static encap tunnel table."
     INDEX {cienaCesMplsStaticIngressTunnelIndex}
     ::= { cienaCesMplsStaticIngressTunnelTable 1 }
     
 CienaCesMplsStaticIngressTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsStaticIngressTunnelIndex                 	Unsigned32,
     cienaCesMplsStaticIngressTunnelName                  	DisplayString,
     cienaCesMplsStaticIngressTunnelWeight                	INTEGER,
     --cienaCesMplsStaticIngressTunnelLspId      	       	Unsigned32,
     cienaCesMplsStaticIngressTunnelNextHopIp	       	IpAddress,
     cienaCesMplsStaticIngressTunnelAdminState	       	CienaGlobalState,
     cienaCesMplsStaticIngressTunnelOperState	       	CienaGlobalState,
     cienaCesMplsStaticIngressTunnelDestIpAddr	       	IpAddress,
     cienaCesMplsStaticIngressTunnelLabel			INTEGER,
     --cienaCesMplsStaticIngressTunnelCspfEnabled		INTEGER, 
     cienaCesMplsStaticIngressTunnelFrmCosPolicy		FCosPolicy,
     cienaCesMplsStaticIngressTunnelFrmCosMapId		INTEGER,
     cienaCesMplsStaticIngressTunnelFixedExp		Unsigned32,
     --cienaCesMplsStaticIngressTunnelFastRoute		INTEGER,
     cienaCesMplsStaticIngressTunnelTTLPolicy		TTLPolicy,
     cienaCesMplsStaticIngressTunnelFixedTTL		Unsigned32,
     cienaCesMplsStaticIngressTunnelGrpIndex		Unsigned32,
--     cienaCesMplsStaticIngressTunnelRowStatus              RowStatus,
     cienaCesMplsStaticIngressTunnelReversion       	INTEGER,
     cienaCesMplsStaticIngressTunnelReversionTimeout    INTEGER,
     cienaCesMplsStaticIngressTunnelPrimaryTunnelName   DisplayString,
     cienaCesMplsStaticIngressTunnelFixedTC				Unsigned32,
     cienaCesMplsStaticIngressTunnelProtectionRole		INTEGER,	
     cienaCesMplsStaticIngressTunnelProtectionState 	INTEGER,
     cienaCesMplsStaticIngressTunnelProtectionPartnerName	DisplayString,
     cienaCesMplsStaticIngressTunnelCosProfileIndex			INTEGER,
     cienaCesMplsStaticIngressTunnelCosProfileName			DisplayString,
     cienaCesMplsStaticIngressTunnelRecoveryDisjoint        INTEGER
   }
 
 cienaCesMplsStaticIngressTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesMplsStaticIngressTunnelEntry 1 }
  
 cienaCesMplsStaticIngressTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name. Tunnel names are common across static and dynamic."
     ::= { cienaCesMplsStaticIngressTunnelEntry 2 }
     
 cienaCesMplsStaticIngressTunnelWeight   OBJECT-TYPE
 	 SYNTAX		INTEGER   (1..8)
 	 MAX-ACCESS	read-only				
 	 STATUS		current
 	 DESCRIPTION
 	 	"A parameter to select the order of precedence of encap tunnels 
 	 	 during tunnel switchover or reversion (if tunnel reversion is enabled).
 	 	 A higher weight indicates higher preference during switchover or reversion."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 3 }
 
 cienaCesMplsStaticIngressTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the static encap tunnel."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 4 }
 
 cienaCesMplsStaticIngressTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the static encap tunnel."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 5 }
 
 cienaCesMplsStaticIngressTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the static encap tunnel."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 6 }
 
 cienaCesMplsStaticIngressTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static encap tunnel. If not specified, the static encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the static encap tunnel
 	 	entry is created."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 7 }
 
 cienaCesMplsStaticIngressTunnelLabel			OBJECT-TYPE
 	 SYNTAX		INTEGER   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesMplsStaticIngressTunnelEntry 8 }
 
 cienaCesMplsStaticIngressTunnelFrmCosPolicy	OBJECT-TYPE
 	 SYNTAX		FCosPolicy  
 	 MAX-ACCESS  read-only
 	 STATUS		 deprecated
 	 DESCRIPTION
 	 	"Frame CoS policy of the static encap tunnel."
 	 DEFVAL { rcos-mapped }
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 9 }
 
 cienaCesMplsStaticIngressTunnelFrmCosMapId    OBJECT-TYPE
 	 SYNTAX		INTEGER   (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"The frame CoS map ID of the static encap tunnel. This value is valid if the 
 	 	frame CoS policy selected for the static encap tunnel is rcos-mapped. This value 
 	 	corresponds to the index cienaCesTceDpTsCosMapFcosMapId of the 
 	 	cienaCesTceDpTsCosMapFcosMapTable."
 	 ::= {cienaCesMplsStaticIngressTunnelEntry 10 }
 
 cienaCesMplsStaticIngressTunnelFixedExp      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0..7 )	
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"This object is deprecated and the new object to provide this information is
		 cienaCesMplsStaticIngressTunnelFixedTC in this table." 
 	 DEFVAL { 0 }
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 11 } 	 
 
 --cienaCesMplsStaticIngressTunnelFastRoute  OBJECT-TYPE
 --	 SYNTAX		INTEGER	 {
 --	 						none(1),
 --	 						link-protect(2),
 --	 						node-protect(3)
 --	 			}
 --	 MAX-ACCESS  read-only
 --	 STATUS		 current
 --	 DESCRIPTION
 --	 	"" 
 --	 DEFVAL	{ link-protect }
 --	 ::= { cienaCesMplsStaticIngressTunnelEntry 17 }
 
 cienaCesMplsStaticIngressTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesMplsStaticIngressTunnelEntry 12 }
 
 cienaCesMplsStaticIngressTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the static encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesMplsStaticIngressTunnelEntry 13 }		
 
 cienaCesMplsStaticIngressTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the static encap tunnel is a member."
     ::= { cienaCesMplsStaticIngressTunnelEntry 14 }

  --cienaCesMplsStaticIngressTunnelRowStatus OBJECT-TYPE
  --    SYNTAX      RowStatus
  --   MAX-ACCESS  read-only
  --   STATUS      current
  --   DESCRIPTION
  --      "Setting this object to 'createAndGo' will create the entry 
  --       in the table. Setting this object to 'destroy' will delete 
  --       the entry from the table."
  --    ::= { cienaCesMplsStaticIngressTunnelEntry 15 }

 cienaCesMplsStaticIngressTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesMplsStaticIngressTunnelEntry 16 }

 cienaCesMplsStaticIngressTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesMplsStaticIngressTunnelEntry 17 }

 cienaCesMplsStaticIngressTunnelPrimaryTunnelName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"This object represents the primary tunnel it is protecting,
		 if this is a backup tunnel."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry	18 } 

 cienaCesMplsStaticIngressTunnelFixedTC      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0..7 )	
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"If the frame CoS policy for the static encap tunnel is fixed, this value is used 
 	 	for MPLS encapsulation." 
 	 DEFVAL { 0 }
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 19 }
 
 cienaCesMplsStaticIngressTunnelProtectionRole      OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection role of the static encap tunnel " 
  	 ::= { cienaCesMplsStaticIngressTunnelEntry 20 }      
  	 
 cienaCesMplsStaticIngressTunnelProtectionState      OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			} 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection status of the static encap tunnel " 
  	 ::= { cienaCesMplsStaticIngressTunnelEntry 21 }
 
  cienaCesMplsStaticIngressTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the primary tunnel it is protecting,
		 if this is a backup tunnel."
 	 ::= { cienaCesMplsStaticIngressTunnelEntry	22 } 
 	 
  cienaCesMplsStaticIngressTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesMplsStaticIngressTunnelEntry 23 }
                                                        
  cienaCesMplsStaticIngressTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesMplsStaticIngressTunnelEntry 24 }
                                                 
  cienaCesMplsStaticIngressTunnelRecoveryDisjoint       OBJECT-TYPE
  	 SYNTAX		INTEGER  {
  	                      none(1),
  	                      link(2),
  	                      node(3),
  	                      srlg(4),
  	                      unknown(5)
  	 }                           
  	   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the type of  Recovery Disjoint"
 	 ::= {cienaCesMplsStaticIngressTunnelEntry 25 }  
 --
 -- MPLS Dynamic Encap tunnel table    
 --

 cienaCesMplsDynamicIngressTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsDynamicIngressTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS encap tunnels."                 

     ::= { cienaCesMpls 3 }
     
 cienaCesMplsDynamicIngressTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsDynamicIngressTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the dynamic encap tunnel table."
     INDEX {cienaCesMplsDynamicIngressTunnelIndex} 
     ::= { cienaCesMplsDynamicIngressTunnelTable 1 }
 
 CienaCesMplsDynamicIngressTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsDynamicIngressTunnelIndex                 Unsigned32,
     cienaCesMplsDynamicIngressTunnelName                  DisplayString,
     cienaCesMplsDynamicIngressTunnelWeight                INTEGER,
     cienaCesMplsDynamicIngressTunnelLspId      	        Unsigned32,
     cienaCesMplsDynamicIngressTunnelNextHopIp		IpAddress,
     cienaCesMplsDynamicIngressTunnelAdminState		CienaGlobalState,
     cienaCesMplsDynamicIngressTunnelOperState		CienaGlobalState,
     cienaCesMplsDynamicIngressTunnelDestIpAddr		IpAddress,
     cienaCesMplsDynamicIngressTunnelLabel			INTEGER,
     --cienaCesMplsDynamicIngressTunnelCspfEnabled		INTEGER, 
     cienaCesMplsDynamicIngressTunnelFrmCosPolicy		INTEGER,
     cienaCesMplsDynamicIngressTunnelFrmCosMapId		INTEGER,   
     --include frame cos map name as RO
     cienaCesMplsDynamicIngressTunnelFixedExp		Unsigned32,
     cienaCesMplsDynamicIngressTunnelSetupPriority		Unsigned32,
     cienaCesMplsDynamicIngressTunnelHoldPriority		Unsigned32,
     cienaCesMplsDynamicIngressTunnelRecordRoute		INTEGER,
     cienaCesMplsDynamicIngressTunnelFastRoute		INTEGER,
     cienaCesMplsDynamicIngressTunnelTTLPolicy		INTEGER,
     cienaCesMplsDynamicIngressTunnelPathIndex		Unsigned32,
     cienaCesMplsDynamicIngressTunnelPathName		DisplayString,
     cienaCesMplsDynamicIngressTunnelFixedTTL		Unsigned32,
     cienaCesMplsDynamicIngressTunnelGrpIndex		Unsigned32,
--     cienaCesMplsDynamicIngressTunnelRowStatus             RowStatus,
     cienaCesMplsDynamicIngressTunnelResourcePointer       RowPointer,
     cienaCesMplsDynamicIngressTunnelReversion       	INTEGER,
     cienaCesMplsDynamicIngressTunnelReversionTimeout   INTEGER,
     cienaCesMplsDynamicIngressTunnelBandwidthProfile   DisplayString,
     cienaCesMplsDynamicIngressTunnelPrimaryTunnelName  DisplayString,
     cienaCesMplsDynamicIngressTunnelFixedTC		Unsigned32,
     cienaCesMplsDynamicIngressTunnelProtectionRole	INTEGER,	
     cienaCesMplsDynamicIngressTunnelProtectionState INTEGER,
     cienaCesMplsDynamicIngressTunnelProtectionPartnerName  DisplayString,
     cienaCesMplsDynamicIngressTunnelCosProfileIndex	INTEGER,
     cienaCesMplsDynamicIngressTunnelCosProfileName		DisplayString 
   }
 
 cienaCesMplsDynamicIngressTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32  (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 1 }
  
 cienaCesMplsDynamicIngressTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	--	Is the size range correct?
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the dynamic encap tunnel name."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 2 }
     
 cienaCesMplsDynamicIngressTunnelWeight   OBJECT-TYPE
 	 SYNTAX		INTEGER   (1..8)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"A parameter to select the order of precedence of encap tunnels 
 	     during tunnel switchover or reversion (if tunnel reversion is enabled).
 	     A higher weight indicates higher preference during switchover or reversion."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 3 }
 
 cienaCesMplsDynamicIngressTunnelLspId		OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"A unique index within a tunnel group."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 4 }
 
 cienaCesMplsDynamicIngressTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only   			
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the dynamic encap tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 5 }
 
 cienaCesMplsDynamicIngressTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the dynamic encap tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 6 }
 
 cienaCesMplsDynamicIngressTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the dynamic encap tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 7 }
 
 cienaCesMplsDynamicIngressTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the dynamic encap tunnel. If not specified, the dynamic encap tunnel 
 	 	inherits the IP address of the tunnel group. This object cannot be modified once the 
 	 	dynamic encap tunnel entry is created."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 8 }
 
 cienaCesMplsDynamicIngressTunnelLabel			OBJECT-TYPE
 	 SYNTAX		INTEGER   (-1..1048575)
 	 MAX-ACCESS	read-only				-- for dynamic it is read-only??
 	 STATUS		current
 	 DESCRIPTION
 	 	"Label for the dynamic encap tunnel."
 	 ::= {cienaCesMplsDynamicIngressTunnelEntry 9 }
 
 cienaCesMplsDynamicIngressTunnelFrmCosPolicy	OBJECT-TYPE
 	 SYNTAX		INTEGER  {
 	 						fixed(1),
 	 						rcos-mapped(2)
 	 				}  
 	 MAX-ACCESS  read-only
 	 STATUS		 deprecated
 	 DESCRIPTION
 	 	"Frame CoS policy of the dynamic encap tunnel."
 	 DEFVAL { rcos-mapped }
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 10 }
 
 cienaCesMplsDynamicIngressTunnelFrmCosMapId    OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"The frame CoS map ID of the dynamic encap tunnel. This value is valid if the 
 		frame CoS policy selected for the dynamic encap tunnel is rcos-mapped. This value 
 		corresponds to the index cienaCesTceDpTsCosMapFcosMapId of the 
 		cienaCesTceDpTsCosMapFcosMapTable."
 	 ::= {cienaCesMplsDynamicIngressTunnelEntry 11 }
 
 cienaCesMplsDynamicIngressTunnelFixedExp      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0..7 )
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"This object is deprecated and the new object to provide this information is
		cienaCesMplsDynamicIngressTunnelFixedTC in this table." 
 	 DEFVAL { 0 }
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 12 }
 
 cienaCesMplsDynamicIngressTunnelSetupPriority OBJECT-TYPE
     SYNTAX		Unsigned32	(0..7)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates the set up priority of the dynamic encap tunnel."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 13 }
 
 cienaCesMplsDynamicIngressTunnelHoldPriority OBJECT-TYPE
     SYNTAX	    Unsigned32   (0..7)
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
     	"Indicates the holding priority of the dynamic encap tunnel."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 14 }
 
 cienaCesMplsDynamicIngressTunnelRecordRoute OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether or not an FRR (fast re-route) tunnel needs to be created for this dynamic encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesMplsDynamicIngressTunnelEntry 15 }
 
 cienaCesMplsDynamicIngressTunnelFastRoute  OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						none(1),
 	 						link-protect(2),
 	 						node-protect(3)
 	 			}
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"Indicates the fast route method for the dynamic encap tunnel. This object cannot be modified once the
 	 	 dynamic encap tunnel entry is created." 
 	 DEFVAL	{ link-protect }
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 16 }
 
 cienaCesMplsDynamicIngressTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		INTEGER  {
     						fixed(1),
     						inherit(2)
     			}
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesMplsDynamicIngressTunnelEntry 17 }
 
 cienaCesMplsDynamicIngressTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the dynamic encap tunnel is fixed, this object is 
 		the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesMplsDynamicIngressTunnelEntry 18 }		
 cienaCesMplsDynamicIngressTunnelPathIndex	OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Index into the cienaCesmplsTunnelPathTable entry that
 		specifies the explicit route hops for this dynamic encap tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 19 }
 
 cienaCesMplsDynamicIngressTunnelPathName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The path name corresponding to the pathIndex object."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry	20 }
 	 
 cienaCesMplsDynamicIngressTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group id of the tunnel group of which the dynamic encap tunnel is a member."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 21 }
     
 --cienaCesMplsDynamicIngressTunnelRowStatus OBJECT-TYPE
 --    SYNTAX      RowStatus
  --   MAX-ACCESS  read-only
 --    STATUS      current
 --    DESCRIPTION
 --       "Setting this object to 'createAndGo' will create the entry 
 --        in the table. Setting this object to 'destroy' will delete 
 --        the entry from the table."
 --    ::= { cienaCesMplsDynamicIngressTunnelEntry 22 }

 cienaCesMplsDynamicIngressTunnelResourcePointer OBJECT-TYPE
     SYNTAX      RowPointer
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The OID must be one of the entries in mplsTunnelResourceTable."
     ::= { cienaCesMplsDynamicIngressTunnelEntry 23 }

 cienaCesMplsDynamicIngressTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesMplsDynamicIngressTunnelEntry 24 }

 cienaCesMplsDynamicIngressTunnelReversionTimeout    OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesMplsDynamicIngressTunnelEntry 25 }

 cienaCesMplsDynamicIngressTunnelBandwidthProfile	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the bandwidth profile attached to this tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry	26 }

 cienaCesMplsDynamicIngressTunnelPrimaryTunnelName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		deprecated 
 	 DESCRIPTION
 	 	"This object represents the primary tunnel it is protecting,
		 if this is a backup tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry	27 } 

  cienaCesMplsDynamicIngressTunnelFixedTC      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0..7 )
 	 MAX-ACCESS	read-only
 	 STATUS		deprecated
 	 DESCRIPTION
 	 	"If the frame CoS policy for the dynamic encap tunnel is fixed, this value is used 
 		 for MPLS encapsulation." 
 	 DEFVAL { 0 }
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 28 }

 cienaCesMplsDynamicIngressTunnelProtectionRole      OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection role of the static encap tunnel " 
  	 ::= { cienaCesMplsDynamicIngressTunnelEntry 29 }      
  	 
 cienaCesMplsDynamicIngressTunnelProtectionState      OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection status of the static encap tunnel " 
  	 ::= { cienaCesMplsDynamicIngressTunnelEntry 30 }  
  	 
  cienaCesMplsDynamicIngressTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the primary tunnel it is protecting,
		 if this is a backup tunnel."
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry	31 } 	                                                      

  cienaCesMplsDynamicIngressTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesMplsDynamicIngressTunnelEntry 32 }
                                                        
  cienaCesMplsDynamicIngressTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesMplsDynamicIngressTunnelEntry 33 }
                                                            
                                                            
 --
 -- MPLS static Decap tunnel table
 --

 cienaCesMplsStaticEgressTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsStaticEgressTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS decap tunnels."
     ::= { cienaCesMpls 4 }
     
 cienaCesMplsStaticEgressTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsStaticEgressTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static decap tunnel table. "
     INDEX {cienaCesMplsStaticEgressTunnelIndex}
     ::= { cienaCesMplsStaticEgressTunnelTable 1 }
     
 CienaCesMplsStaticEgressTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsStaticEgressTunnelIndex                 	Unsigned32,
     cienaCesMplsStaticEgressTunnelName                  	DisplayString,
     cienaCesMplsStaticEgressTunnelAdminState		CienaGlobalState,
     cienaCesMplsStaticEgressTunnelOperState		CienaGlobalState,
     cienaCesMplsStaticEgressTunnelSourceIpAddr		IpAddress,
     cienaCesMplsStaticEgressTunnelLabel			Unsigned32
--     cienaCesMplsStaticEgressTunnelRowStatus             	RowStatus
   }

 cienaCesMplsStaticEgressTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesMplsStaticEgressTunnelEntry 1 }
  
 cienaCesMplsStaticEgressTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the decap tunnel name."
     ::= { cienaCesMplsStaticEgressTunnelEntry 2 }
  
 
 cienaCesMplsStaticEgressTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Administrative status of the static decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesMplsStaticEgressTunnelEntry 3 }	
 
 cienaCesMplsStaticEgressTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static decap tunnel."
 	 ::= { cienaCesMplsStaticEgressTunnelEntry 4 }
 
 cienaCesMplsStaticEgressTunnelSourceIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesMplsStaticEgressTunnelEntry 5 }
 
 cienaCesMplsStaticEgressTunnelLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0.. 1048575 )		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the label of the static decap tunnel."
 	 ::= { cienaCesMplsStaticEgressTunnelEntry 6 }
 
  
-- cienaCesMplsStaticEgressTunnelRowStatus OBJECT-TYPE
--     SYNTAX      RowStatus
--     MAX-ACCESS  read-only
--     STATUS      current
--     DESCRIPTION
--        "Setting this object to 'createAndGo' will create the entry 
--         in the table. Setting this object to 'destroy' will delete 
--         the entry from the table."
--     ::= { cienaCesMplsStaticEgressTunnelEntry 7 }

 
 --
 -- MPLS Dynamic Decap tunnel table
 --                       

 cienaCesMplsDynamicEgressTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsDynamicEgressTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS dynamic decap tunnels.
            "
     ::= { cienaCesMpls 5 }
     
 cienaCesMplsDynamicEgressTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsDynamicEgressTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the dynamic decap tunnel table."
     INDEX {cienaCesMplsDynamicEgressTunnelIndex,cienaCesMplsDynamicEgressTunnelInstance,cienaCesMplsDynamicEgressTunnelSourceIpAddr,cienaCesMplsDynamicEgressTunnelDestIpAddr}
     ::= { cienaCesMplsDynamicEgressTunnelTable 1 }
     
 CienaCesMplsDynamicEgressTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsDynamicEgressTunnelIndex          Unsigned32,
     cienaCesMplsDynamicEgressTunnelName           DisplayString,
     cienaCesMplsDynamicEgressTunnelAdminState		CienaGlobalState,
     cienaCesMplsDynamicEgressTunnelOperState		CienaGlobalState,
     cienaCesMplsDynamicEgressTunnelInstance       Unsigned32,
     cienaCesMplsDynamicEgressTunnelSourceIpAddr	IpAddress,
     cienaCesMplsDynamicEgressTunnelDestIpAddr		IpAddress,
     cienaCesMplsDynamicEgressTunnelLabel			   Unsigned32
   }

 cienaCesMplsDynamicEgressTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesMplsDynamicEgressTunnelEntry 1 }
  
 cienaCesMplsDynamicEgressTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the dynamic decap tunnel name."
     ::= { cienaCesMplsDynamicEgressTunnelEntry 2 }
  
 cienaCesMplsDynamicEgressTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the dynamic decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 3 }	
 
 cienaCesMplsDynamicEgressTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the dynamic decap tunnel."
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 4 }
 
 cienaCesMplsDynamicEgressTunnelInstance   OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the LSP ID."
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 5 }
 
 cienaCesMplsDynamicEgressTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the dynamic decap tunnel."
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 6 }

 cienaCesMplsDynamicEgressTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the dynamic decap tunnel."
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 7 }
 

 cienaCesMplsDynamicEgressTunnelLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Idicates the label of the dynamic decap tunnel."
 	 ::= { cienaCesMplsDynamicEgressTunnelEntry 8 }
 
 --
 -- MPLS Static transit tunnel table
 --                       

 cienaCesMplsStaticTransitTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsStaticTransitTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static transit tunnels.
            "
     ::= { cienaCesMpls 6 }
     
 cienaCesMplsStaticTransitTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsStaticTransitTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static transit tunnel table."
     INDEX {cienaCesMplsStaticTransitTunnelIndex}
     ::= { cienaCesMplsStaticTransitTunnelTable 1 }
     
 CienaCesMplsStaticTransitTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsStaticTransitTunnelIndex          	Unsigned32,
     cienaCesMplsStaticTransitTunnelName           	DisplayString,
     cienaCesMplsStaticTransitTunnelAdminState		CienaGlobalState,
     cienaCesMplsStaticTransitTunnelOperState		CienaGlobalState,
     cienaCesMplsStaticTransitTunnelNextHopIpAddr	IpAddress,
     cienaCesMplsStaticTransitTunnelInLabel		INTEGER,
     cienaCesMplsStaticTransitTunnelOutLabel		INTEGER,
     cienaCesMplsStaticTransitTunnelFcosPolicy		FCosPolicy,
     cienaCesMplsStaticTransitTunnelFixedTc		Unsigned32,
     cienaCesMplsStaticTransitTunnelFrmCosMapId		INTEGER,
     cienaCesMplsStaticTransitTunnelTTLPolicy		TTLPolicy,
     cienaCesMplsStaticTransitTunnelFixedTTL 		Unsigned32,
     cienaCesMplsStaticTransitTunnelRcosPolicy		FCosPolicy,
     cienaCesMplsStaticTransitTunnelRCosMapId		INTEGER,
     cienaCesMplsStaticTransitTunnelCosProfileIndex		INTEGER,
     cienaCesMplsStaticTransitTunnelCosProfileName      DisplayString,
     cienaCesMplsStaticTransitTunnelSourceIpAddr	IpAddress, 
     cienaCesMplsStaticTransitTunnelDestIpAddr	IpAddress,
     cienaCesMplsStaticTransitTunnelIncomingPackets Unsigned32,
     cienaCesMplsStaticTransitTunnelOutgoingPackets Unsigned32,
     cienaCesMplsStaticTransitTunnelIncomingBytes Unsigned32,
     cienaCesMplsStaticTransitTunnelOutgoingBytes Unsigned32
   }

 cienaCesMplsStaticTransitTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesMplsStaticTransitTunnelEntry 1 }
  
 cienaCesMplsStaticTransitTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the static transit tunnel name."
     ::= { cienaCesMplsStaticTransitTunnelEntry 2 }
  
 cienaCesMplsStaticTransitTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the static transit tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 3 }	
 
 cienaCesMplsStaticTransitTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 4 }

 cienaCesMplsStaticTransitTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 5 }

  cienaCesMplsStaticTransitTunnelInLabel		OBJECT-TYPE
 	 SYNTAX		INTEGER   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 6 }


  cienaCesMplsStaticTransitTunnelOutLabel		OBJECT-TYPE
 	 SYNTAX		INTEGER   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 7 }

  cienaCesMplsStaticTransitTunnelFcosPolicy	   OBJECT-TYPE
      SYNTAX		FCosPolicy  
      MAX-ACCESS  read-only
      STATUS		 deprecated
      DESCRIPTION
      "Frame CoS policy of the static transit tunnel."
      DEFVAL { rcos-mapped }
      ::= { cienaCesMplsStaticTransitTunnelEntry 8 }

  cienaCesMplsStaticTransitTunnelFixedTc	   OBJECT-TYPE
      SYNTAX		Unsigned32  (0..7 )	
      MAX-ACCESS	read-only
      STATUS		deprecated
      DESCRIPTION
      "If frame CoS policy for the static transit tunnel is fixed, this value is used 
      for MPLS encapsulation." 
      DEFVAL { 0 }
      ::= { cienaCesMplsStaticTransitTunnelEntry 9 }

  cienaCesMplsStaticTransitTunnelFrmCosMapId OBJECT-TYPE
      SYNTAX		INTEGER   (1..65535)
      MAX-ACCESS	read-only
      STATUS		deprecated
      DESCRIPTION
      "The frame CoS map ID of the static transit tunnel. This value is valid if the 
      frame cos policy selected for the static transit tunnel is rcos-mapped."
      ::= { cienaCesMplsStaticTransitTunnelEntry 10 }

  cienaCesMplsStaticTransitTunnelTTLPolicy   OBJECT-TYPE
      SYNTAX		TTLPolicy
      MAX-ACCESS	 read-only
      STATUS		 current
      DESCRIPTION
      "Specifies whether TTL is fixed or inherited from the frame."
      DEFVAL { fixed }
      ::= { cienaCesMplsStaticTransitTunnelEntry 11 }

  cienaCesMplsStaticTransitTunnelFixedTTL    OBJECT-TYPE
      SYNTAX		Unsigned32  (1..255)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "If the TTL policy for the static transit tunnel is fixed, then this object is 
      the TTL value."
      DEFVAL { 255 }
      ::= { cienaCesMplsStaticTransitTunnelEntry 12 }  
 
  cienaCesMplsStaticTransitTunnelRcosPolicy	   OBJECT-TYPE
      SYNTAX		FCosPolicy  
      MAX-ACCESS  read-only
      STATUS		 deprecated
      DESCRIPTION
      "Resolved CoS policy of the static transit tunnel."
      DEFVAL { rcos-mapped }
      ::= { cienaCesMplsStaticTransitTunnelEntry 13 } 
      
  cienaCesMplsStaticTransitTunnelRCosMapId OBJECT-TYPE
      SYNTAX		INTEGER   (1..65535)
      MAX-ACCESS	read-only
      STATUS		deprecated
      DESCRIPTION
      "The resolved CoS map ID of the static transit tunnel. This value is valid if the 
      resolved CoS policy selected for the static transit tunnel is rcos-mapped."
      ::= { cienaCesMplsStaticTransitTunnelEntry 14 } 
      
  cienaCesMplsStaticTransitTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 15 }
                                                        
  cienaCesMplsStaticTransitTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesMplsStaticTransitTunnelEntry 16 }     
 	 
 cienaCesMplsStaticTransitTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 17 }

 cienaCesMplsStaticTransitTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static transit tunnel."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 18 }
 	 
 cienaCesMplsStaticTransitTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
           "The number of incoming packets for the given transit tunnel.
            This object is supported only on specific platforms."
     ::= { cienaCesMplsStaticTransitTunnelEntry 19 }  
   
 cienaCesMplsStaticTransitTunnelOutgoingPackets	OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
           "The number of outgoing packets for the given transit tunnel.
            This object is supported only on specific platforms."
 	 ::= { cienaCesMplsStaticTransitTunnelEntry 20 }
   
 cienaCesMplsStaticTransitTunnelIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
           "The number of incoming bytes for the given transit tunnel.
            This object is supported only on specific platforms."
 	 ::= {cienaCesMplsStaticTransitTunnelEntry 21 }  
 	
 cienaCesMplsStaticTransitTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
           "The number of outgoing bytes for the given transit tunnel.
            This object is supported only on specific platforms."
     ::= { cienaCesMplsStaticTransitTunnelEntry 22 } 
 	  
 --
 -- MPLS Dynamic transit tunnel table
 --                       

 cienaCesMplsDynamicTransitTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsDynamicTransitTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS dynamic transit tunnels.
            "
     ::= { cienaCesMpls 7 }
     
 cienaCesMplsDynamicTransitTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsDynamicTransitTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the dynamic transit tunnel table."
     INDEX {cienaCesMplsDynamicTransitTunnelIndex}
     ::= { cienaCesMplsDynamicTransitTunnelTable 1 }
     
 CienaCesMplsDynamicTransitTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsDynamicTransitTunnelIndex         Unsigned32,
     cienaCesMplsDynamicTransitTunnelName          DisplayString,
     cienaCesMplsDynamicTransitTunnelAdminState		CienaGlobalState,
     cienaCesMplsDynamicTransitTunnelOperState		CienaGlobalState,
     cienaCesMplsDynamicTransitTunnelInLabel			INTEGER,
     cienaCesMplsDynamicTransitTunnelOutLabel		INTEGER,
     cienaCesMplsDynamicTransitTunnelNextHopIpAddr	IpAddress,
     cienaCesMplsDynamicTransitTunnelSourceIpAddr   IpAddress,
     cienaCesMplsDynamicTransitTunnelDestIpAddr   IpAddress,
     cienaCesMplsDynamicTransitTunnelIncomingPackets Unsigned32,
     cienaCesMplsDynamicTransitTunnelOutgoingPackets Unsigned32,
     cienaCesMplsDynamicTransitTunnelIncomingBytes Unsigned32,
     cienaCesMplsDynamicTransitTunnelOutgoingBytes Unsigned32
   }

 cienaCesMplsDynamicTransitTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesMplsDynamicTransitTunnelEntry 1 }
  
 cienaCesMplsDynamicTransitTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the dynamic transit tunnel name."
     ::= { cienaCesMplsDynamicTransitTunnelEntry 2 }
  
 cienaCesMplsDynamicTransitTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the dynamic transit tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 3 }	
 
 cienaCesMplsDynamicTransitTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the dynamic transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 4 }
 
  cienaCesMplsDynamicTransitTunnelInLabel		OBJECT-TYPE
 	 SYNTAX		INTEGER   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the dynamic transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 5 }


 cienaCesMplsDynamicTransitTunnelOutLabel		OBJECT-TYPE
 	 SYNTAX		INTEGER   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the dynamic transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 6 }
 
 cienaCesMplsDynamicTransitTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the Dynamic transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 7 }
 	 
 cienaCesMplsDynamicTransitTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 8 }

 cienaCesMplsDynamicTransitTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static transit tunnel."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 9 }
 	 
 cienaCesMplsDynamicTransitTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesMplsDynamicTransitTunnelEntry 10 }  
   
 cienaCesMplsDynamicTransitTunnelOutgoingPackets	OBJECT-TYPE
     SYNTAX		Unsigned32  
     MAX-ACCESS  read-only
     STATUS		 current
     DESCRIPTION
        "The number of outgoing packets for the given transit tunnel.
         This object is supported only on specific platforms."
 	 ::= { cienaCesMplsDynamicTransitTunnelEntry 11 }
   
 cienaCesMplsDynamicTransitTunnelIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The number of incoming bytes for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= {cienaCesMplsDynamicTransitTunnelEntry 12 }  
 	
 cienaCesMplsDynamicTransitTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesMplsDynamicTransitTunnelEntry 13 } 

 
 --
 -- MPLS tunnel Path table (going to be deprecated)
 --
 cienaCesMplsTunnelPath	OBJECT IDENTIFIER	::= { cienaCesMpls 8 }

 cienaCesMplsTunnelPathTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsTunnelPathEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS tunnel paths.
             To create an entry in the tunnel path table, 
             cienaCesMplsTunnelPathName and cienaCesMplsTunnelPathRowStatus 
             must both be specified."
     ::= { cienaCesMplsTunnelPath 1 }
     
 cienaCesMplsTunnelPathEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsTunnelPathEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the tunnel path table."
     INDEX {cienaCesMplsTunnelPathIndex}
     ::= { cienaCesMplsTunnelPathTable 1 }
     
 CienaCesMplsTunnelPathEntry ::=  SEQUENCE { 
     cienaCesMplsTunnelPathIndex                 Unsigned32,
     cienaCesMplsTunnelPathName			DisplayString,
     cienaCesMplsTunnelPathUseCount		Counter32
 --    cienaCesMplsTunnelPathRowStatus		RowStatus
    }
    
 cienaCesMplsTunnelPathIndex	OBJECT-TYPE
  	 SYNTAX		Unsigned32
  	 MAX-ACCESS	not-accessible
  	 STATUS		current
  	 DESCRIPTION
  	 	"A unique index in the path entry table."
  	 ::= { cienaCesMplsTunnelPathEntry 1 }

 cienaCesMplsTunnelPathName 	OBJECT-TYPE
 	 SYNTAX		DisplayString (SIZE ( 1..31) )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Name associated with the path."
 	 ::= { cienaCesMplsTunnelPathEntry 2 }
 	 
 cienaCesMplsTunnelPathUseCount	  OBJECT-TYPE
 	 SYNTAX		Counter32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the number of encap tunnels that are using this path."
 	 ::= { cienaCesMplsTunnelPathEntry 3 }
  
-- cienaCesMplsTunnelPathRowStatus   OBJECT-TYPE
-- 	 SYNTAX		RowStatus
 --	 MAX-ACCESS	read-only
 --	 STATUS		current
 --	 DESCRIPTION
 --	 	"Setting this object to 'createAndGo' will create the entry 
 --		in the table. Setting this object to 'destroy' will delete 
 --		the entry from the table."
 --	 ::= { cienaCesMplsTunnelPathEntry 4 }
  
 
 --
 -- MPLS tunnel Path Hop table(going to be deprecated)
 --

 cienaCesMplsTunnelPathHopTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsTunnelPathHopEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS tunnel path hop. 
            To create an entry in the path-hop table, cienaCesMplsTunnelPathHopIpAddr 
            and cienaCesMplsTunnelPathHopRowStatus must both be specified."
     ::= { cienaCesMplsTunnelPath 2 }
     
 cienaCesMplsTunnelPathHopEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsTunnelPathHopEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "A tunnel hop entry."
     INDEX {cienaCesMplsTunnelPathIndex, cienaCesMplsTunnelPathHopIndex}
     ::= { cienaCesMplsTunnelPathHopTable 1 }
     
 CienaCesMplsTunnelPathHopEntry ::=  SEQUENCE { 
     cienaCesMplsTunnelPathHopIndex              Unsigned32,
     cienaCesMplsTunnelPathHopIpAddr		IpAddress,
     cienaCesMplsTunnelPathHopType		INTEGER
     --cienaCesMplsTunnelPathHopRowStatus		RowStatus
 }
 
 cienaCesMplsTunnelPathHopIndex  OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	not-accessible
 	 STATUS		current
 	 DESCRIPTION
 	 	"Unique index in the path hop table."
 	 ::= { cienaCesMplsTunnelPathHopEntry 1 }
 
 cienaCesMplsTunnelPathHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"IP address associated with this hop. This object cannot be modified once the path hop is created."
 	 ::= { cienaCesMplsTunnelPathHopEntry 2 }
 
 cienaCesMplsTunnelPathHopType   OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	 					 strict(1),
 	 					 loose(2)            
 	 	}
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	    "Indidcates the hop type of the path member. This object cannot be modified once the path hop is created."
 	 DEFVAL	{ strict }
 	  ::= { cienaCesMplsTunnelPathHopEntry 3 }
 
 --cienaCesMplsTunnelPathHopRowStatus  OBJECT-TYPE
 --	 SYNTAX		RowStatus
 --	 MAX-ACCESS	read-only
 --	 STATUS		current
 --	 DESCRIPTION
 --	 	"Setting this object to 'createAndGo' will create the entry 
 --		in the table. Setting this object to 'destroy' will delete 
 --		the entry from the table."
 --	 ::= { cienaCesMplsTunnelPathHopEntry 4 }  
 
   --
   --   Extension to the MPLS Tunnel table table
   --
   --

 
 cienaCesMplsEncapTunnelNotif OBJECT IDENTIFIER ::= { cienaCesMpls 9 }
 
 cienaCesMplsNotifEncapTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsNotifEncapTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Table of notification objects required for encap tunnel notification."
     ::= { cienaCesMplsEncapTunnelNotif 1 }
 
  cienaCesMplsNotifEncapTunnelEntry OBJECT-TYPE
    SYNTAX     CienaCesMplsNotifEncapTunnelEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesMplsNotifEncapTunnelTable.
	     No Get, GetNext or Set is allowed on this table."
    INDEX      { cienaCesMplsNotifEncapTunnelType, cienaCesMplsNotifEncapTunnelIndex}
    ::= { cienaCesMplsNotifEncapTunnelTable 1 }  

 CienaCesMplsNotifEncapTunnelEntry ::= SEQUENCE {
    cienaCesMplsNotifEncapTunnelIndex Unsigned32,
    cienaCesMplsNotifEncapTunnelType	INTEGER,
    cienaCesMplsNotifEncapTunnelName   DisplayString,
    cienaCesMplsNotifEncapTunnelAdminState CienaGlobalState,
    cienaCesMplsNotifEncapTunnelOperState	CienaGlobalState,
    cienaCesMplsNotifEncapTunnelOamFaulted	TunnelOamFault,
    cienaCesMplsNotifEncapTunnelFaultedNodeId   IpAddress
 }
 

 cienaCesMplsNotifEncapTunnelIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel index of the encap tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelEntry 1 } 

 cienaCesMplsNotifEncapTunnelType   OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
     				dynamic(2),
     				frr(3)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Encap tunnel type of the encap tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelEntry 2 } 
  
  cienaCesMplsNotifEncapTunnelName OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name." 
     ::= { cienaCesMplsNotifEncapTunnelEntry 3 } 
 
  cienaCesMplsNotifEncapTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Administrative state of the encap tunnel."
     ::= { cienaCesMplsNotifEncapTunnelEntry 4 }  
 
  cienaCesMplsNotifEncapTunnelOperState OBJECT-TYPE
  	SYNTAX      CienaGlobalState
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "Operational state of the encap tunnel."
     ::= { cienaCesMplsNotifEncapTunnelEntry 5 } 
    
 cienaCesMplsNotifEncapTunnelOamFaulted OBJECT-TYPE
     SYNTAX      TunnelOamFault
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Indication if the tunnel has Oam fault."
     ::= { cienaCesMplsNotifEncapTunnelEntry 6 } 
    
  cienaCesMplsNotifEncapTunnelFaultedNodeId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "IP address of the node with the AIS fault."
     ::= { cienaCesMplsNotifEncapTunnelEntry 7 }

  cienaCesMplsTransitTunnelNotif OBJECT IDENTIFIER ::= { cienaCesMpls 10 }
 
 cienaCesMplsNotifTransitTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsNotifTransitTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Table of notification objects required for transit tunnel notification."
     ::= { cienaCesMplsTransitTunnelNotif 1 }
 
  cienaCesMplsNotifTransitTunnelEntry OBJECT-TYPE
    SYNTAX     CienaCesMplsNotifTransitTunnelEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesMplsNotifTransitTunnelTable.
	     No Get, GetNext or Set is allowed on this table."
    INDEX      { cienaCesMplsNotifTransitTunnelType, cienaCesMplsNotifTransitTunnelIndex}
    ::= { cienaCesMplsNotifTransitTunnelTable 1 }

 CienaCesMplsNotifTransitTunnelEntry ::= SEQUENCE {
    cienaCesMplsNotifTransitTunnelIndex Unsigned32,
    cienaCesMplsNotifTransitTunnelType	INTEGER,
    cienaCesMplsNotifTransitTunnelName   DisplayString,
    cienaCesMplsNotifTransitTunnelAdminState CienaGlobalState,
    cienaCesMplsNotifTransitTunnelOperState	CienaGlobalState,
    cienaCesMplsNotifTransitTunnelOamFaulted    TunnelOamFault
 } 

 cienaCesMplsNotifTransitTunnelIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel index of the transit tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifTransitTunnelEntry 1 } 

 cienaCesMplsNotifTransitTunnelType   OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
     				dynamic(2)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Transit tunnel type of the transit tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifTransitTunnelEntry 2 } 
  
  cienaCesMplsNotifTransitTunnelName OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
        "This represents the transit tunnel name." 
     ::= { cienaCesMplsNotifTransitTunnelEntry 3 } 
 
  cienaCesMplsNotifTransitTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Administrative state of the transit tunnel."
     ::= { cienaCesMplsNotifTransitTunnelEntry 4 }  
 
  cienaCesMplsNotifTransitTunnelOperState OBJECT-TYPE
  	SYNTAX      CienaGlobalState
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "Operational state of the transit tunnel."
     ::= { cienaCesMplsNotifTransitTunnelEntry 5 }      

 cienaCesMplsNotifTransitTunnelOamFaulted OBJECT-TYPE
      SYNTAX      TunnelOamFault
      MAX-ACCESS accessible-for-notify
      STATUS     current
      DESCRIPTION
             "Indication if the tunnel has Oam fault."
      ::= { cienaCesMplsNotifTransitTunnelEntry 6 } 

  cienaCesMplsEncapTunnelGrpNotif OBJECT IDENTIFIER ::= { cienaCesMpls 11 }

  cienaCesMplsNotifEncapTunnelGrpTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsNotifEncapTunnelGrpEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Table of notification objects required for encap tunnel group notification."
     ::= { cienaCesMplsEncapTunnelGrpNotif 1 }


  cienaCesMplsNotifEncapTunnelGrpEntry OBJECT-TYPE
    SYNTAX     CienaCesMplsNotifEncapTunnelGrpEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesMplsNotifEncapTunnelGrpTable.
	     No Get, GetNext or Set is allowed on this table."
    INDEX      {cienaCesMplsNotifEncapTunnelGrpIndex}
    ::= { cienaCesMplsNotifEncapTunnelGrpTable 1 }    
 
  CienaCesMplsNotifEncapTunnelGrpEntry ::= SEQUENCE {
    cienaCesMplsNotifEncapTunnelGrpIndex Unsigned32,
    cienaCesMplsNotifEncapTunnelGrpName	DisplayString,
    cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlIndex   Unsigned32,
    cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlName DisplayString,
    cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlType	INTEGER
 }

   cienaCesMplsNotifEncapTunnelGrpIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel group index of the encap tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelGrpEntry 1 } 

   cienaCesMplsNotifEncapTunnelGrpName   OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
            "Tunnel group name of the encap tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelGrpEntry 2 } 
  
  cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlIndex  OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Active Encap Tunnel index of the tunnel group associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelGrpEntry 3 } 
 
  cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlName   OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
            "Active Encap Tunnel name of the Tunnel group associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelGrpEntry 4 } 
     
   cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlType  OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
     				dynamic(2),
     				frr(3)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Encap tunnel type of the encap tunnel associated with the 
            notification."
     ::= { cienaCesMplsNotifEncapTunnelGrpEntry 5 } 


 --
 -- MPLS Associated Tunnel
 --
 
 cienaCesMplsAssociatedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The table listing the MPLS Associated tunnels."

     ::= { cienaCesMpls 12 }
     
 cienaCesMplsAssociatedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsAssociatedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS associated tunnel table."
     INDEX {cienaCesMplsAssociatedTunnelIndex}
     ::= { cienaCesMplsAssociatedTunnelTable 1 }
     
 CienaCesMplsAssociatedTunnelEntry ::=  SEQUENCE { 
     cienaCesMplsAssociatedTunnelIndex                 	Unsigned32,  
     cienaCesMplsAssociatedTunnelName                   DisplayString,
     cienaCesMplsAssociatedForwardTunnelName            	DisplayString,
     cienaCesMplsAssociatedForwardTunnelType				DisplayString, 
     cienaCesMplsAssociatedReverseTunnelName           		DisplayString, 
     cienaCesMplsAssociatedReverseTunnelType				DisplayString,
     cienaCesMplsAssociatedForwardTunnelDestIpAddr	        IpAddress,
     cienaCesMplsAssociatedDynamicTunnelSrcIpAddr	IpAddress,  
     cienaCesMplsAssociatedTunnelAdminState	       	CienaGlobalState,
     cienaCesMplsAssociatedTunnelOperState	       	CienaGlobalState,
     cienaCesMplsAssociatedForwardTunnelOperState	       	CienaGlobalState,
     cienaCesMplsAssociatedReverseTunnelOperState	       	CienaGlobalState,
     cienaCesMplsAssociatedProtectionRole                   INTEGER,
     cienaCesMplsAssociatedProtectionState                  INTEGER,  
     cienaCesMplsAssociatedTunnelProtectionPartnerName      DisplayString,
     cienaCesMplsAssociatedBfdMonitoring            CienaGlobalState,
     cienaCesMplsAssociatedBfdProfileName    		DisplayString,
     cienaCesMplsAssociatedBfdSessionName			DisplayString,
     cienaCesMplsAssociatedBfdSessionFaulted  		INTEGER,
     cienaCesMplsAssociatedBfdProfileIndex    		Unsigned32
      }
 
 cienaCesMplsAssociatedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table. Specifies  mpls associated tunnel index"
     ::= { cienaCesMplsAssociatedTunnelEntry 1 }   
     
  cienaCesMplsAssociatedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies mpls associated tunnel name."
     ::= { cienaCesMplsAssociatedTunnelEntry 2 }    
     
  cienaCesMplsAssociatedForwardTunnelName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated forward tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 3 }  
  
  cienaCesMplsAssociatedForwardTunnelType OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated forward tunnel type."
     ::= { cienaCesMplsAssociatedTunnelEntry 4 }    
      
  cienaCesMplsAssociatedReverseTunnelName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated reverse tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 5 }    
  
   cienaCesMplsAssociatedReverseTunnelType OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated reverse tunnel type."
     ::= { cienaCesMplsAssociatedTunnelEntry 6 }  
     
  cienaCesMplsAssociatedForwardTunnelDestIpAddr OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated mpls forward tunnel destination IP address"
     ::= { cienaCesMplsAssociatedTunnelEntry 7 }
     
  cienaCesMplsAssociatedDynamicTunnelSrcIpAddr OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated mpls reverse tunnel source IP address."
     ::= { cienaCesMplsAssociatedTunnelEntry 8 } 
     
  cienaCesMplsAssociatedTunnelAdminState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Administrative status of associated tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 9 }     
     
  cienaCesMplsAssociatedTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 10 }     
     
  cienaCesMplsAssociatedForwardTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated forward tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 11 } 
     
  cienaCesMplsAssociatedReverseTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated reverse tunnel."
     ::= { cienaCesMplsAssociatedTunnelEntry 12 }    
     
 cienaCesMplsAssociatedProtectionRole      OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection role of the associated  tunnel " 
  	 ::= { cienaCesMplsAssociatedTunnelEntry 13 }      
  	 
 cienaCesMplsAssociatedProtectionState      OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection status of the associated  tunnel " 
  	 ::= { cienaCesMplsAssociatedTunnelEntry 14 }    
  	 
 cienaCesMplsAssociatedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel name it is protecting"
 	 ::= { cienaCesMplsAssociatedTunnelEntry	15 }   	       
     
  cienaCesMplsAssociatedBfdMonitoring OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of BFD monitoring."
     ::= { cienaCesMplsAssociatedTunnelEntry 16 }     
     
  cienaCesMplsAssociatedBfdProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesMplsAssociatedTunnelEntry 17 }   
     
  cienaCesMplsAssociatedBfdSessionName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session name."
     ::= { cienaCesMplsAssociatedTunnelEntry 18 }     

  cienaCesMplsAssociatedBfdSessionFaulted OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if BFD is faulted."
     ::= { cienaCesMplsAssociatedTunnelEntry 19 } 
     
  cienaCesMplsAssociatedBfdProfileIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the BFD profile index in the BFD profile table configured
	for this associated tunnel"
     ::= { cienaCesMplsAssociatedTunnelEntry 20 }     


 --
 --  	MPLS AR HOP TABLE
 --
        
 cienaCesMplsTunnelARHopTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsTunnelARHopEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The mplsTunnelARHopTable is used to indicate the
          hops for an MPLS tunnel defined in mplsTunnelTable,
          as reported by the MPLS signalling protocol. Thus at
          a transit LSR, this table (if the table is supported
          and if the signaling protocol is recording actual
          route information) contains the actual route of the
          whole tunnel. If the signaling protocol is not
          recording the actual route, this table MAY report
          the information from the mplsTunnelHopTable or the
          mplsTunnelCHopTable.

         Each row in this table is indexed by
          mplsTunnelARHopListIndex. Each row also has a
          secondary index mplsTunnelARHopIndex, corresponding
          to the next hop that this row corresponds to.

         Please note that since the information necessary to
          build entries within this table is not provided by
          some MPLS signalling protocols, implementation of
          this table is optional. Furthermore, since the
          information in this table is actually provided by
          the MPLS signalling protocol after the path has
          been set-up, the entries in this table are provided
          only for observation, and hence, all variables in
          this table are accessible exclusively as read-
          only.

         Note also that the contencts of this table may change
          while it is being read because of re-routing
          activities. A network administrator may verify that
          the actual route read is consistent by reference to
          the mplsTunnelLastPathChange object."
     ::= { cienaCesMpls 13 }
     
 cienaCesMplsTunnelARHopEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsTunnelARHopEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in this table represents a tunnel hop.  An
          entry is created by the agent for signaled ERLSP
          set up by an MPLS signalling protocol."
      INDEX { cienaMplsTunnelARHopListIndex, cienaMplsTunnelARHopIndex }     
      ::= { cienaCesMplsTunnelARHopTable 1 }
     
 CienaCesMplsTunnelARHopEntry ::=  SEQUENCE { 
     cienaMplsTunnelARHopListIndex                 	Unsigned32,  
     cienaMplsTunnelARHopIndex                    	Unsigned32,    
     cienaMplsTunnelARHopAddrType		        INTEGER	,         	  
     cienaMplsTunnelARHopIpAddr          	       	IpAddress,
     cienaMplsTunnelARHopAddrUnnum          	    Unsigned32,
     cienaMplsTunnelARHopLspId              	    Unsigned32
     }
 
cienaMplsTunnelARHopListIndex OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
        "Primary index into this table identifying a
          particular recorded hop list."
   ::= { cienaCesMplsTunnelARHopEntry 1 }

cienaMplsTunnelARHopIndex OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
        "Secondary index into this table identifying the
          particular hop."
   ::= { cienaCesMplsTunnelARHopEntry 2 }

cienaMplsTunnelARHopAddrType OBJECT-TYPE
   SYNTAX        INTEGER
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "The Hop Address Type of this tunnel hop.

         Note that lspid(5) is a valid option only
         for tunnels signaled via CRLDP."
   ::= { cienaCesMplsTunnelARHopEntry 3 }

cienaMplsTunnelARHopIpAddr OBJECT-TYPE
   SYNTAX        IpAddress
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "The Tunnel Hop Address for this tunnel hop.

         The type of this address is determined by the
         value of the corresponding mplsTunnelARHopAddrType.
         
         If mplsTunnelARHopAddrType is set to unnum(4),
          then this value contains the LSR Router ID of the
          unnumbered interface. Otherwise the agent SHOULD
          set this object to the zero-length string and the
          manager should ignore this object."
::= { cienaCesMplsTunnelARHopEntry 4 }

cienaMplsTunnelARHopAddrUnnum OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "If mplsTunnelARHopAddrType is set to unnum(4), then
          this value will contain the interface identifier of
          the unnumbered interface for this hop. This object
          should be used in conjunction with
          mplsTunnelARHopIpAddr which would contain the LSR
          Router ID in this case. Otherwise the agent should
          set this object to zero-length string and the
          manager should ignore this."
   ::= { cienaCesMplsTunnelARHopEntry 5 }

cienaMplsTunnelARHopLspId OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "If mplsTunnelARHopAddrType is set to lspid(5), then
          this value will contain the LSP ID of this hop.
          This object is otherwise insignificant and should
          contain a value of 0 to indicate this fact."
   ::= { cienaCesMplsTunnelARHopEntry 6 }
   
                                  
 cienaCesMplsAssociatedTunnelNotif OBJECT IDENTIFIER ::= { cienaCesMpls 14 }
 
 cienaCesMplsNotifAssociatedTunnelTable    OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsNotifAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for associated tunnel
          notification."
     ::= { cienaCesMplsAssociatedTunnelNotif 1 }
 
 cienaCesMplsNotifAssociatedTunnelEntry    OBJECT-TYPE
     SYNTAX     CienaCesMplsNotifAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "An entry (conceptual row) in the cienaCesMplsNotifAssociatedTunnelTable.
         No Get, GetNext or Set is allowed on this table."
     INDEX      { cienaCesMplsNotifAssociatedTunnelType, cienaCesMplsNotifAssociatedTunnelIndex}
     ::= { cienaCesMplsNotifAssociatedTunnelTable 1 }

 CienaCesMplsNotifAssociatedTunnelEntry ::= SEQUENCE {
     cienaCesMplsNotifAssociatedTunnelIndex      Unsigned32,
     cienaCesMplsNotifAssociatedTunnelType       INTEGER,
     cienaCesMplsNotifAssociatedTunnelName       DisplayString,
     cienaCesMplsNotifAssociatedTunnelAdminState CienaGlobalState,
     cienaCesMplsNotifAssociatedTunnelOperState  CienaGlobalState,
     cienaCesMplsNotifAssociatedTunnelOamFaulted TunnelOamFault,
     cienaCesMplsNotifAssociatedTunnelFaultedNodeId IpAddress
 } 

 cienaCesMplsNotifAssociatedTunnelIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Tunnel index of the associated tunnel associated with the 
         notification."
     ::= { cienaCesMplsNotifAssociatedTunnelEntry 1 } 

 cienaCesMplsNotifAssociatedTunnelType    OBJECT-TYPE
     SYNTAX    INTEGER 
     {
         static(1),
         dynamic(2)
     }
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Associated tunnel type of the associated tunnel associated with the 
         notification."
     ::= { cienaCesMplsNotifAssociatedTunnelEntry 2 } 
  
  cienaCesMplsNotifAssociatedTunnelName    OBJECT-TYPE
     SYNTAX      DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
         "This represents the associated tunnel name." 
     ::= { cienaCesMplsNotifAssociatedTunnelEntry 3 } 
 
  cienaCesMplsNotifAssociatedTunnelAdminState    OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Administrative state of the associated tunnel."
     ::= { cienaCesMplsNotifAssociatedTunnelEntry 4 }  
 
  cienaCesMplsNotifAssociatedTunnelOperState    OBJECT-TYPE
      SYNTAX     CienaGlobalState
      MAX-ACCESS accessible-for-notify
      STATUS     current
      DESCRIPTION
          "Operational state of the associated tunnel."
     ::= { cienaCesMplsNotifAssociatedTunnelEntry 5 }      

  cienaCesMplsNotifAssociatedTunnelOamFaulted OBJECT-TYPE
      SYNTAX      TunnelOamFault
      MAX-ACCESS accessible-for-notify
      STATUS     current
      DESCRIPTION
             "Indication if the tunnel has Oam fault."
      ::= { cienaCesMplsNotifAssociatedTunnelEntry 6 } 
     
   cienaCesMplsNotifAssociatedTunnelFaultedNodeId OBJECT-TYPE
      SYNTAX      IpAddress
      MAX-ACCESS accessible-for-notify
      STATUS     current
      DESCRIPTION
             "IP address of the node with the AIS fault."
      ::= { cienaCesMplsNotifAssociatedTunnelEntry 7 }

 --
 -- CAC TE Table
 --

 cienaCesMplsCacInterfaceNotif OBJECT IDENTIFIER ::= { cienaCesMpls 15 }

 cienaCesMplsNotifCacInterfaceTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsNotifCacInterfaceEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Table of notification objects required for CAC interface notification."
     ::= { cienaCesMplsCacInterfaceNotif 1 }

  cienaCesMplsNotifCacInterfaceEntry OBJECT-TYPE
    SYNTAX     CienaCesMplsNotifCacInterfaceEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesMplsNotifCACInterfaceTable.
             No Get, GetNext or Set is allowed on this table."
    INDEX      { cienaCesMplsNotifCacInterfaceClassType, cienaCesMplsNotifCacInterfaceIndex}
    ::= { cienaCesMplsNotifCacInterfaceTable 1 }

 CienaCesMplsNotifCacInterfaceEntry ::= SEQUENCE {
    cienaCesMplsNotifCacInterfaceIndex      Unsigned32,
    cienaCesMplsNotifCacInterfaceClassType  Unsigned32,
    cienaCesMplsNotifCacInterfaceName       DisplayString,
    cienaCesMplsNotifCacInterfaceThreshold  Unsigned32 
 }

  cienaCesMplsNotifCacInterfaceIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "CAC entry index associated with the
            notification."
     ::= { cienaCesMplsNotifCacInterfaceEntry 1 }

 cienaCesMplsNotifCacInterfaceClassType    OBJECT-TYPE
     SYNTAX     Unsigned32 (0..7)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "CAC class type value of the associated interface associated with the                                     
         notification."
     ::= { cienaCesMplsNotifCacInterfaceEntry 2 }  

 cienaCesMplsNotifCacInterfaceName    OBJECT-TYPE
     SYNTAX      DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
         "This represents the associated interface name." 
     ::= { cienaCesMplsNotifCacInterfaceEntry 3 } 

 cienaCesMplsNotifCacInterfaceThreshold    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Alarm threshold value of the associated interface associated with the 
         notification."
     ::= { cienaCesMplsNotifCacInterfaceEntry 4 } 

      
 cienaCesMplsClassProfileTable    OBJECT-TYPE
      SYNTAX     SEQUENCE OF CienaCesMplsClassProfileEntry
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "The (conceptual) table listing the MPLS Class Profiles."
     ::= { cienaCesMpls 16 }

 cienaCesMplsClassProfileEntry    OBJECT-TYPE
      SYNTAX     CienaCesMplsClassProfileEntry
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "This represents an entry for MPLS Class Profile."
      INDEX { cienaCesMplsClassProfileIndex }
     ::= { cienaCesMplsClassProfileTable 1 }

 CienaCesMplsClassProfileEntry ::=  SEQUENCE {
      cienaCesMplsClassProfileIndex      Unsigned32,
      cienaCesMplsClassProfileName       DisplayString,
      cienaCesMplsClassProfileCacPolicy  CacPolicy
 }

 cienaCesMplsClassProfileIndex    OBJECT-TYPE
      SYNTAX     Unsigned32   (1..500)
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "This represents the unique index for MPLS Class Profile."
     ::= { cienaCesMplsClassProfileEntry 1 }

 cienaCesMplsClassProfileName    OBJECT-TYPE
      SYNTAX     DisplayString (SIZE (1..31))
      MAX-ACCESS read-only
      STATUS     current
      DESCRIPTION
          "This represents the MPLS Class Profile name."
     ::= { cienaCesMplsClassProfileEntry 2 }

 cienaCesMplsClassProfileCacPolicy    OBJECT-TYPE
      SYNTAX     CacPolicy
      MAX-ACCESS read-only
      STATUS     current
      DESCRIPTION
          "MPLS Class Profile Cac policy."
      DEFVAL { mam }
     ::= { cienaCesMplsClassProfileEntry 3 }

 cienaCesMplsTEClassTypeTable    OBJECT-TYPE
      SYNTAX     SEQUENCE OF CienaCesMplsTEClassTypeEntry
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "The (conceptual) table listing the MPLS Class Types."
     ::= { cienaCesMpls 17 }

 cienaCesMplsTEClassTypeEntry    OBJECT-TYPE
      SYNTAX     CienaCesMplsTEClassTypeEntry
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "This represents an entry for MPLS Class Type."
      INDEX { cienaCesMplsClassProfileIndex, cienaCesMplsClassType }
     ::= { cienaCesMplsTEClassTypeTable 1 }
                                                                                                                                                      
 CienaCesMplsTEClassTypeEntry ::=  SEQUENCE {
      cienaCesMplsClassType                    Unsigned32,
      cienaCesMplsClassTypeQueueGroupIndex     Unsigned32,
      cienaCesMplsClassTypeQueueGroupInstance  Unsigned32,
      cienaCesMplsClassTypeLom                 Unsigned32,
      cienaCesMplsClassTypeAlarmThreshold      Unsigned32
 }

 cienaCesMplsClassType    OBJECT-TYPE
      SYNTAX     Unsigned32   (0..7)
      MAX-ACCESS not-accessible
      STATUS     current
      DESCRIPTION
          "Indicates the MPLS Class Type value. Used as an Index in combination with MPLS Class Profile Index."
     ::= { cienaCesMplsTEClassTypeEntry 1 }

 cienaCesMplsClassTypeQueueGroupIndex    OBJECT-TYPE
      SYNTAX     Unsigned32   (1..500)
      MAX-ACCESS read-only
      STATUS     current
      DESCRIPTION
          "Indicates the queue group index associated with MPLS Class Type."
     ::= { cienaCesMplsTEClassTypeEntry 2 }

 cienaCesMplsClassTypeQueueGroupInstance OBJECT-TYPE
     SYNTAX      Unsigned32 (1..65534)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates the instance for MPLS Class Type mapped to a particular 
         queue group."
     ::= { cienaCesMplsTEClassTypeEntry 3 }


 cienaCesMplsClassTypeLom    OBJECT-TYPE
      SYNTAX     Unsigned32   (1..4)
      MAX-ACCESS read-only
      STATUS     current
      DESCRIPTION
          "Indicates the link multiplier value for the MPLS Class Type."
      DEFVAL { 1 }
     ::= { cienaCesMplsTEClassTypeEntry 4 }

 cienaCesMplsClassTypeAlarmThreshold     OBJECT-TYPE
      SYNTAX     Unsigned32   (0..100)
      MAX-ACCESS read-only
      STATUS     current
      DESCRIPTION
          "Indicates the Alarm Threshold percentage (%) for MPLS Class Type."
      DEFVAL { 100 }
     ::= { cienaCesMplsTEClassTypeEntry 5 }



 --
 -- TE Tunnel Traps
 -- 
    cienaCesMplsTunnelOperStateChgTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	     	 cienaGlobalSeverity,  
     			 cienaGlobalMacAddress,
	              cienaCesMplsNotifEncapTunnelIndex,
	              cienaCesMplsNotifEncapTunnelType,
	              cienaCesMplsNotifEncapTunnelName,
	              cienaCesMplsNotifEncapTunnelAdminState,
	              cienaCesMplsNotifEncapTunnelOperState,
	              cienaCesMplsNotifEncapTunnelOamFaulted,
	              cienaCesMplsNotifEncapTunnelFaultedNodeId
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent when the operational state of an encap
	       tunnel changes. Variable bindings include: cienaGlobalSeverity, 
	       cienaGlobalMacAddress, cienaCesMplsNotifEncapTunnelIndex, 
	       cienaCesMplsNotifEncapTunnelType, cienaCesMplsNotifEncapTunnelName, 
	       cienaCesMplsNotifEncapTunnelAdminState, 
	       cienaCesMplsNotifEncapTunnelOperState,
	       cienaCesMplsNotifEncapTunnelOamFaulted,
	       cienaCesMplsNotifEncapTunnelFaultNodeId."   
	::= { cienaCesMplsMIBNotifications 1 }

  cienaCesMplsEncapTunnelGrpActiveEncapTunnelChange  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	     	 cienaGlobalSeverity,  
     			 cienaGlobalMacAddress,
	              cienaCesMplsNotifEncapTunnelGrpIndex,
	              cienaCesMplsNotifEncapTunnelGrpName,
	              cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlIndex,
	              cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlName,
	              cienaCesMplsNotifEncapTunnelGrpActiveEncapTunlType 
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent whenever the active encap tunnel in 
	       a tunnel group changes."  
	::= { cienaCesMplsMIBNotifications 2 } 
	
     
  cienaCesMplsTransitTunnelOperStateChgTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	      cienaGlobalSeverity,  
     			  cienaGlobalMacAddress,
	              cienaCesMplsNotifTransitTunnelIndex,
	              cienaCesMplsNotifTransitTunnelType,
	              cienaCesMplsNotifTransitTunnelName,
	              cienaCesMplsNotifTransitTunnelAdminState,
	              cienaCesMplsNotifTransitTunnelOperState,
	              cienaCesMplsNotifTransitTunnelOamFaulted 
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent when the operational state of a transit
	       tunnel changes. Variable bindings include: cienaGlobalSeverity, 
	       cienaGlobalMacAddress, cienaCesMplsNotifTransitTunnelIndex,
	       cienaCesMplsNotifTransitTunnelType, cienaCesMplsNotifTransitTunnelName,
	       cienaCesMplsNotifTransitTunnelAdminState,
               cienaCesMplsNotifTransitTunnelOperState,
               cienaCesMplsNotifTransitTunnelOamFaulted." 
	::= { cienaCesMplsMIBNotifications 3 } 


 cienaCesMplsAssociatedTunnelOperStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,  
         cienaGlobalMacAddress,
         cienaCesMplsNotifAssociatedTunnelIndex,
         cienaCesMplsNotifAssociatedTunnelType,
         cienaCesMplsNotifAssociatedTunnelName,
         cienaCesMplsNotifAssociatedTunnelAdminState,
         cienaCesMplsNotifAssociatedTunnelOperState, 
         cienaCesMplsNotifAssociatedTunnelOamFaulted,
         cienaCesMplsNotifAssociatedTunnelFaultedNodeId 
     }
                              
     STATUS                   current
     DESCRIPTION  
         "This notification is sent when the operational state of an associated
         tunnel changes. Variable bindings include: cienaGlobalSeverity, 
         cienaGlobalMacAddress, cienaCesMplsNotifAssociatedTunnelIndex, 
         cienaCesMplsNotifAssociatedTunnelType, cienaCesMplsNotifAssociatedTunnelName, 
         cienaCesMplsNotifAssociatedTunnelAdminState, 
         cienaCesMplsNotifAssociatedTunnelOperState,
         cienaCesMplsNotifAssociatedTunnelOamFaulted,
         cienaCesMplsNotifAssociatedTunnelFaultedNodeId."
     ::= { cienaCesMplsMIBNotifications 6 }

 cienaCesMplsCacInterfaceThresholdTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesMplsNotifCacInterfaceIndex,
         cienaCesMplsNotifCacInterfaceClassType,
         cienaCesMplsNotifCacInterfaceName,
         cienaCesMplsNotifCacInterfaceThreshold
     }

     STATUS                   current
     DESCRIPTION
         "This notification is sent when LSP bandwith usage exceeds the threshold.
         Variable bindings include: cienaGlobalSeverity,
         cienaGlobalMacAddress, cienaCesMplsNotifCasInterfaceIndex,
         cienaCesMplsNotifCacInterfaceClassType, cienaCesMplsNotifCacInterfaceName,
         cienaCesMplsNotifCacInterfaceThreshold."
     ::= { cienaCesMplsMIBNotifications 7 }


 --
 -- GMPLS-TP Static Encap uni directional tunnel table
 --

 cienaCesGmplsStaticIngressUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticIngressUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static encap tunnels."

     ::= { cienaCesGmpls 1 }
     
 cienaCesGmplsStaticIngressUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticIngressUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS static encap TP unidirectional tunnel table."
     INDEX {cienaCesGmplsStaticIngressUniDirTunnelIndex}
     ::= { cienaCesGmplsStaticIngressUniDirTunnelTable 1 }
     
 CienaCesGmplsStaticIngressUniDirTunnelEntry ::=  SEQUENCE {                                
     cienaCesGmplsStaticIngressUniDirTunnelIndex                 	Unsigned32,
     cienaCesGmplsStaticIngressUniDirTunnelName                  	DisplayString,
     cienaCesGmplsStaticIngressUniDirTunnelNextHopIp	       	IpAddress,
     cienaCesGmplsStaticIngressUniDirTunnelSrcIpAddr          IpAddress,                                
     cienaCesGmplsStaticIngressUniDirTunnelDestIpAddr	       	IpAddress,  
     cienaCesGmplsStaticIngressUniDirTunnelAdminState	       	CienaGlobalState,
     cienaCesGmplsStaticIngressUniDirTunnelOperState	       	CienaGlobalState, 
     cienaCesGmplsStaticIngressUniDirTunnelForwardOutLabel		Unsigned32,    
     cienaCesGmplsStaticIngressUniDirTunnelProtectionRole  		INTEGER,
     cienaCesGmplsStaticIngressUniDirTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsStaticIngressUniDirTunnelProtectionState		INTEGER,
     cienaCesGmplsStaticIngressUniDirTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsStaticIngressUniDirTunnelFixedTTL		Unsigned32,
     cienaCesGmplsStaticIngressUniDirTunnelGrpIndex		Unsigned32,
     cienaCesGmplsStaticIngressUniDirTunnelReversion       	INTEGER,
     cienaCesGmplsStaticIngressUniDirTunnelReversionTimeout    Unsigned32,
     cienaCesGmplsStaticIngressUniDirTunnelCosProfileIndex	INTEGER,
     cienaCesGmplsStaticIngressUniDirTunnelCosProfileName		DisplayString,
     cienaCesGmplsStaticIngressUniDirTunnelRecoveryDisjoint     INTEGER 
   }
 
 cienaCesGmplsStaticIngressUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 1 }
  
 cienaCesGmplsStaticIngressUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name. Tunnel names are common across static and dynamic."
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 2 }

 cienaCesGmplsStaticIngressUniDirTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 3 }
 
 cienaCesGmplsStaticIngressUniDirTunnelSrcIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static encap tunnel. If not specified, the static encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the static encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 4 }

 cienaCesGmplsStaticIngressUniDirTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static encap tunnel. If not specified, the static encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the static encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 5 }
 

 
 cienaCesGmplsStaticIngressUniDirTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 6 }
 
 cienaCesGmplsStaticIngressUniDirTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 7 }

 cienaCesGmplsStaticIngressUniDirTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 8 }

                          
 cienaCesGmplsStaticIngressUniDirTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 9 }
  	 
 cienaCesGmplsStaticIngressUniDirTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 10 }
 
 cienaCesGmplsStaticIngressUniDirTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 11 }	
 	                                                  
 
 cienaCesGmplsStaticIngressUniDirTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 12 }
 
 cienaCesGmplsStaticIngressUniDirTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the static encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 13 }		
 
 cienaCesGmplsStaticIngressUniDirTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the static encap tunnel is a member."
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 14 }


 cienaCesGmplsStaticIngressUniDirTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 15 }

 cienaCesGmplsStaticIngressUniDirTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 16 }

  cienaCesGmplsStaticIngressUniDirTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsStaticIngressUniDirTunnelEntry 17 }
                                                        
  cienaCesGmplsStaticIngressUniDirTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 18 }
   
  cienaCesGmplsStaticIngressUniDirTunnelRecoveryDisjoint     OBJECT-TYPE
  	 SYNTAX		INTEGER  {
  	                      none(1),
  	                      link(2),
  	                      node(3),
  	                      srlg(4),
  	                      unknown(5)
  	 }                    
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the type of Recovery Disjoint"
 	 ::= {cienaCesGmplsStaticIngressUniDirTunnelEntry 19 }

 --
 -- GMPLS-TP Static Encap corouted tunnel table
 --

 cienaCesGmplsStaticIngressCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticIngressCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static encap tunnels."

     ::= { cienaCesGmpls 2 }
     
 cienaCesGmplsStaticIngressCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticIngressCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS static encap tunnel table."
     INDEX {cienaCesGmplsStaticIngressCoroutedTunnelIndex}
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelTable 1 }
     
 CienaCesGmplsStaticIngressCoroutedTunnelEntry ::=  SEQUENCE {                                
     cienaCesGmplsStaticIngressCoroutedTunnelIndex                 	Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelName                  	DisplayString,
     cienaCesGmplsStaticIngressCoroutedTunnelNextHopIp	       	IpAddress,
     cienaCesGmplsStaticIngressCoroutedTunnelSrcIpAddr          IpAddress,                                
     cienaCesGmplsStaticIngressCoroutedTunnelDestIpAddr	       	IpAddress,  
     cienaCesGmplsStaticIngressCoroutedTunnelAdminState	       	CienaGlobalState,
     cienaCesGmplsStaticIngressCoroutedTunnelOperState	       	CienaGlobalState, 
     cienaCesGmplsStaticIngressCoroutedTunnelForwardOutLabel	Unsigned32,    
     cienaCesGmplsStaticIngressCoroutedTunnelReverseInLabel     Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelProtectionRole  	INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsStaticIngressCoroutedTunnelProtectionState		INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsStaticIngressCoroutedTunnelFixedTTL		Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelGrpIndex		Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelReversion       	INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelReversionTimeout    Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelCosProfileIndex	INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelCosProfileName		DisplayString,
     cienaCesGmplsStaticIngressCoroutedTunnelBfdMonitoring       CienaGlobalState,
     cienaCesGmplsStaticIngressCoroutedTunnelBfdProfileName		DisplayString,
     cienaCesGmplsStaticIngressCoroutedTunnelBfdSessionName		DisplayString,
     cienaCesGmplsStaticIngressCoroutedTunnelAisMonitoring		CienaGlobalState,
     cienaCesGmplsStaticIngressCoroutedTunnelAisProfileName		DisplayString,
     cienaCesGmplsStaticIngressCoroutedTunnelBfdSessionFaulted 	INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelBfdProfileIndex		Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelRecoveryDisjoint    INTEGER,
     cienaCesGmplsStaticIngressCoroutedTunnelNextHopIfNum        Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelLspId               Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelSrcTunnelId         Unsigned32,
     cienaCesGmplsStaticIngressCoroutedTunnelDestTunnelId        Unsigned32
   }
 
 cienaCesGmplsStaticIngressCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 1 }
  
 cienaCesGmplsStaticIngressCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name. Tunnel names are common across static and dynamic."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 2 }

 cienaCesGmplsStaticIngressCoroutedTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 3 }
 
 cienaCesGmplsStaticIngressCoroutedTunnelSrcIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static encap tunnel. If not specified, the static encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the static encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 4 }

 cienaCesGmplsStaticIngressCoroutedTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static encap tunnel. If not specified, the static encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the static encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 5 }
 

 
 cienaCesGmplsStaticIngressCoroutedTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 6 }
 
 cienaCesGmplsStaticIngressCoroutedTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 7 }

 cienaCesGmplsStaticIngressCoroutedTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 8 }

 cienaCesGmplsStaticIngressCoroutedTunnelReverseInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 9 }
                          
 cienaCesGmplsStaticIngressCoroutedTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 10 }
  	 
 cienaCesGmplsStaticIngressCoroutedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the static encap tunnel."
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 11 }
 
 cienaCesGmplsStaticIngressCoroutedTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 12 }	
 	                                                  

 cienaCesGmplsStaticIngressCoroutedTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 13 }
 
 cienaCesGmplsStaticIngressCoroutedTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the static encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 14 }		
 
 cienaCesGmplsStaticIngressCoroutedTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the static encap tunnel is a member."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 15 }


 cienaCesGmplsStaticIngressCoroutedTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 16 }

 cienaCesGmplsStaticIngressCoroutedTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 17 }

  cienaCesGmplsStaticIngressCoroutedTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		INTEGER  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 18 }
                                                        
  cienaCesGmplsStaticIngressCoroutedTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsStaticIngressCoroutedTunnelEntry 19 }
 
  cienaCesGmplsStaticIngressCoroutedTunnelBfdMonitoring        OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of BFD monitoring."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 20 }     
     
  cienaCesGmplsStaticIngressCoroutedTunnelBfdProfileName 	 OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 21 }   
     
  cienaCesGmplsStaticIngressCoroutedTunnelBfdSessionName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session name."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 22 }     
  
  cienaCesGmplsStaticIngressCoroutedTunnelAisMonitoring			OBJECT-TYPE
  	 SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of AIS monitoring."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 23 }  
   
  cienaCesGmplsStaticIngressCoroutedTunnelAisProfileName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent AIS profile name."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 24 }     

  cienaCesGmplsStaticIngressCoroutedTunnelBfdSessionFaulted  OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if BFD is faulted."
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 25 }
     
 cienaCesGmplsStaticIngressCoroutedTunnelBfdProfileIndex   OBJECT-TYPE
     SYNTAX      Unsigned32  (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the BFD profile index in the BFD profile table
	configured for this gmpls static ingress corouted tunnel"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 26 }  
       
 cienaCesGmplsStaticIngressCoroutedTunnelRecoveryDisjoint  OBJECT-TYPE
     SYNTAX      INTEGER {
  	                      none(1),
  	                      link(2),
  	                      node(3),
  	                      srlg(4),
  	                      unknown(5)
  	 }                   
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies the type of Recovery Disjoint"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 27 } 

 cienaCesGmplsStaticIngressCoroutedTunnelNextHopIfNum   OBJECT-TYPE
     SYNTAX      Unsigned32  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents next hop interface number for this 
        gmpls static ingress corouted tunnel"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 28 }  
                                                           
 
 cienaCesGmplsStaticIngressCoroutedTunnelLspId   OBJECT-TYPE
     SYNTAX      Unsigned32  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents LSP ID for this gmpls static 
        ingress corouted tunnel"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 29 }  
                                                           

 cienaCesGmplsStaticIngressCoroutedTunnelSrcTunnelId   OBJECT-TYPE
     SYNTAX      Unsigned32  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents source tunnel ID for this gmpls 
        static ingress corouted tunnel"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 30 }  


 cienaCesGmplsStaticIngressCoroutedTunnelDestTunnelId   OBJECT-TYPE
     SYNTAX      Unsigned32  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents destination tunnel ID for this gmpls 
        static ingress corouted tunnel"
     ::= { cienaCesGmplsStaticIngressCoroutedTunnelEntry 31 }   
    
 --
 -- GMPLS-TP Dynamic Encap uni directional tunnel table
 --

 cienaCesGmplsDynamicIngressUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicIngressUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS Dynamic encap tunnels."

     ::= { cienaCesGmpls 3 }
     
 cienaCesGmplsDynamicIngressUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicIngressUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS Dynamic encap tunnel table."
     INDEX {cienaCesGmplsDynamicIngressUniDirTunnelIndex}
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelTable 1 }
     
 CienaCesGmplsDynamicIngressUniDirTunnelEntry ::=  SEQUENCE {                                
     cienaCesGmplsDynamicIngressUniDirTunnelIndex                 	Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelName                  	DisplayString,
     cienaCesMplsDynamicIngressUniDirTunnelLspId      	       	Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelNextHopIp	       	IpAddress,
     cienaCesGmplsDynamicIngressUniDirTunnelSrcIpAddr          IpAddress,                                
     cienaCesGmplsDynamicIngressUniDirTunnelDestIpAddr	       	IpAddress,  
     cienaCesGmplsDynamicIngressUniDirTunnelAdminState	       	CienaGlobalState,
     cienaCesGmplsDynamicIngressUniDirTunnelOperState	       	CienaGlobalState, 
     cienaCesGmplsDynamicIngressUniDirTunnelForwardOutLabel		Unsigned32,    
     cienaCesGmplsDynamicIngressUniDirTunnelProtectionRole  INTEGER,
     cienaCesGmplsDynamicIngressUniDirTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsDynamicIngressUniDirTunnelProtectionState		INTEGER,
    cienaCesGmplsDynamicIngressUniDirTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsDynamicIngressUniDirTunnelFixedTTL		Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelGrpIndex		Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelReversion       	INTEGER,
     cienaCesGmplsDynamicIngressUniDirTunnelReversionTimeout    Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelCosProfileIndex		Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelCosProfileName		DisplayString,
     cienaCesGmplsDynamicIngressUniDirTunnelRecordRoute			INTEGER,
     cienaCesGmplsDynamicIngressUniDirTunnelFastRoute			INTEGER,
     cienaCesGmplsDynamicIngressUniDirTunnelSetupPriority		Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelHoldPriority		Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelPathIndex			Unsigned32,
     cienaCesGmplsDynamicIngressUniDirTunnelPathName			DisplayString,
     cienaCesGmplsDynamicIngressUniDirTunnelBandwidthProfile   	DisplayString,   
     cienaCesGmplsDynamicIngressUniDirTunnelResourcePointer     RowPointer
   }
 
 cienaCesGmplsDynamicIngressUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 1 }
  
 cienaCesGmplsDynamicIngressUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name. Tunnel names are common across Dynamic and dynamic."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 2 }
 
 cienaCesMplsDynamicIngressUniDirTunnelLspId		OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"A unique index within a tunnel group."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 3 }
       
       
 cienaCesGmplsDynamicIngressUniDirTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 4 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelSrcIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the Dynamic encap tunnel. If not specified, the Dynamic encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the Dynamic encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 5 }

 cienaCesGmplsDynamicIngressUniDirTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the Dynamic encap tunnel. If not specified, the Dynamic encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the Dynamic encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 6 }
 

 
 cienaCesGmplsDynamicIngressUniDirTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 7 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 8 }

 cienaCesGmplsDynamicIngressUniDirTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32
  	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressUniDirTunnelEntry 9 }
                          
 cienaCesGmplsDynamicIngressUniDirTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressUniDirTunnelEntry 10 }
  	 
 cienaCesGmplsDynamicIngressUniDirTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressUniDirTunnelEntry 11 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 12 }	
 	                                                  
 
 cienaCesGmplsDynamicIngressUniDirTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 13 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the Dynamic encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 14 }		
 
 cienaCesGmplsDynamicIngressUniDirTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the Dynamic encap tunnel is a member."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 15 }


 cienaCesGmplsDynamicIngressUniDirTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 16 }

 cienaCesGmplsDynamicIngressUniDirTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsDynamicIngressUniDirTunnelEntry 17 }

  cienaCesGmplsDynamicIngressUniDirTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 18 }
                                                        
  cienaCesGmplsDynamicIngressUniDirTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsDynamicIngressUniDirTunnelEntry 19 }

 cienaCesGmplsDynamicIngressUniDirTunnelRecordRoute OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether or not an FRR (fast re-route) tunnel needs to be created for this dynamic encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 20 } 
     
 cienaCesGmplsDynamicIngressUniDirTunnelFastRoute  OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						none(1),
 	 						link-protect(2),
 	 						node-protect(3)
 	 			}
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"Indicates the fast route method for the dynamic encap tunnel. This object cannot be modified once the
 	 	 dynamic encap tunnel entry is created." 
 	 DEFVAL	{ link-protect }
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 21 }
     
 cienaCesGmplsDynamicIngressUniDirTunnelSetupPriority OBJECT-TYPE
     SYNTAX		Unsigned32	(0..7)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates the set up priority of the dynamic encap tunnel."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 22 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelHoldPriority OBJECT-TYPE
     SYNTAX	    Unsigned32   (0..7)
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
     	"Indicates the holding priority of the dynamic encap tunnel."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 23 }
                 
 cienaCesGmplsDynamicIngressUniDirTunnelPathIndex	OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Index into the cienaCesmplsTunnelPathTable entry that
 		specifies the explicit route hops for this dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 24 }
 
 cienaCesGmplsDynamicIngressUniDirTunnelPathName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The path name corresponding to the pathIndex object."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry	25 }
 	 
 cienaCesGmplsDynamicIngressUniDirTunnelBandwidthProfile	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the bandwidth profile attached to this tunnel."
 	 ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry	26 }
  
 cienaCesGmplsDynamicIngressUniDirTunnelResourcePointer OBJECT-TYPE
     SYNTAX      RowPointer
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The OID must be one of the entries in mplsTunnelResourceTable."
     ::= { cienaCesGmplsDynamicIngressUniDirTunnelEntry 27 }
  

 --
 -- GMPLS-TP Dynamic Encap corouted tunnel table
 --

 cienaCesGmplsDynamicIngressCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicIngressCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS Dynamic encap co-routed tunnels."

     ::= { cienaCesGmpls 4 }
     
 cienaCesGmplsDynamicIngressCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicIngressCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS Dynamic encap co-routed tunnel table."
     INDEX {cienaCesGmplsDynamicIngressCoroutedTunnelIndex}
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelTable 1 }
     
 CienaCesGmplsDynamicIngressCoroutedTunnelEntry ::=  SEQUENCE {                                
     cienaCesGmplsDynamicIngressCoroutedTunnelIndex                 	Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelName                  	DisplayString,
     cienaCesGmplsDynamicIngressCoroutedTunnelNextHopIp	       	IpAddress,
     cienaCesGmplsDynamicIngressCoroutedTunnelSrcIpAddr          IpAddress,                                
     cienaCesGmplsDynamicIngressCoroutedTunnelDestIpAddr	       	IpAddress,  
     cienaCesGmplsDynamicIngressCoroutedTunnelAdminState	       	CienaGlobalState,
     cienaCesGmplsDynamicIngressCoroutedTunnelOperState	       	CienaGlobalState, 
     cienaCesGmplsDynamicIngressCoroutedTunnelForwardOutLabel	Unsigned32,    
     cienaCesGmplsDynamicIngressCoroutedTunnelReverseInLabel     Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelProtectionRole  	INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsDynamicIngressCoroutedTunnelProtectionState		INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsDynamicIngressCoroutedTunnelFixedTTL		Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelGrpIndex		Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelReversion       	INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelReversionTimeout    Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelCosProfileIndex	Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelCosProfileName		DisplayString,
     cienaCesGmplsDynamicIngressCoroutedTunnelRecordRoute		INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelFastRoute			INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelSetupPriority		Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelHoldPriority		Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelPathIndex			Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelPathName			DisplayString,
     cienaCesGmplsDynamicIngressCoroutedTunnelBandwidthProfile   DisplayString,   
     cienaCesGmplsDynamicIngressCoroutedTunnelResourcePointer    RowPointer,
     cienaCesGmplsDynamicIngressCoroutedTunnelBfdMonitoring      CienaGlobalState,
     cienaCesGmplsDynamicIngressCoroutedTunnelBfdProfileName		DisplayString,
     cienaCesGmplsDynamicIngressCoroutedTunnelBfdSessionName		DisplayString,
     cienaCesGmplsDynamicIngressCoroutedTunnelBfdSessionFaulted 	INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelBfdProfileIndex		Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoBackupEnable          INTEGER,
     cienaCesGmplsDynamicIngressCoroutedTunnelLspReoptimization         MplsGlobalState,
     cienaCesGmplsDynamicIngressCoroutedTunnelLspReOptTimeInterval      Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointType          PathDisjointType,
     cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointMode          PathDisjointMode,
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeEnable            MplsGlobalState, 
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeInterval          Unsigned32,  
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeFailureHdlr       AutoSizeFailHdlr,  
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeState             AutoSizeState,  
     cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeMode              AutoSizeMode,  
     cienaCesGmplsDynamicIngressCoroutedTunnelMinBandwidth              MplsBitRate,  
     cienaCesGmplsDynamicIngressCoroutedTunnelMaxBandwidth              MplsBitRate,
     cienaCesGmplsDynamicIngressCoroutedTunnelIncBandwidth              MplsBitRate,
     cienaCesGmplsDynamicIngressCoroutedTunnelCurBandwidth              MplsBitRate,  
     cienaCesGmplsDynamicIngressCoroutedTunnelReqBandwidth              MplsBitRate,
     cienaCesGmplsDynamicIngressCoroutedTunnelUsedBandwidth             MplsBitRate,
     cienaCesGmplsDynamicIngressCoroutedTunnelClassType                 Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAll        Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAny        Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedTunnelResourceExcludeAny        Unsigned32,
     cienaCesGmplsDynamicIngressCoroutedLspId                           Unsigned32 
   }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 1 }
  
 cienaCesGmplsDynamicIngressCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))  	
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name. Tunnel names are common across Dynamic and dynamic."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 2 }

 cienaCesGmplsDynamicIngressCoroutedTunnelNextHopIp		OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the next hop IP address for the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 3 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelSrcIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the Dynamic encap tunnel. If not specified, the Dynamic encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the Dynamic encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 4 }

 cienaCesGmplsDynamicIngressCoroutedTunnelDestIpAddr	OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the Dynamic encap tunnel. If not specified, the Dynamic encap tunnel 
 	 	inherits the IP address of the tunnel-group. This object cannot be modified once the Dynamic encap tunnel
 	 	entry is created."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 5 }

 cienaCesGmplsDynamicIngressCoroutedTunnelAdminState	OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the administrative status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 6 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelOperState	    OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the operational status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 7 }

 cienaCesGmplsDynamicIngressCoroutedTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 8 }

 cienaCesGmplsDynamicIngressCoroutedTunnelReverseInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 9 }
                          
 cienaCesGmplsDynamicIngressCoroutedTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The label for the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 10 }
  	 
 cienaCesGmplsDynamicIngressCoroutedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the Dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 11 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the Dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 12 }	
 	                                                   
 cienaCesGmplsDynamicIngressCoroutedTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 13 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the Dynamic encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 14 }		
 
 cienaCesGmplsDynamicIngressCoroutedTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the Dynamic encap tunnel is a member."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 15 }

 cienaCesGmplsDynamicIngressCoroutedTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 16 }

 cienaCesGmplsDynamicIngressCoroutedTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 17 }

  cienaCesGmplsDynamicIngressCoroutedTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 18 }
                                                        
  cienaCesGmplsDynamicIngressCoroutedTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsDynamicIngressCoroutedTunnelEntry 19 } 
 	 
 cienaCesGmplsDynamicIngressCoroutedTunnelRecordRoute OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether or not an FRR (fast re-route) tunnel needs to be created for this dynamic encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 20 } 
     
 cienaCesGmplsDynamicIngressCoroutedTunnelFastRoute  OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						none(1),
 	 						link-protect(2),
 	 						node-protect(3)
 	 			}
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"Indicates the fast route method for the dynamic encap tunnel. This object cannot be modified once the
 	 	 dynamic encap tunnel entry is created." 
 	 DEFVAL	{ link-protect }
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 21 }
     
 cienaCesGmplsDynamicIngressCoroutedTunnelSetupPriority OBJECT-TYPE
     SYNTAX		Unsigned32	(0..7)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates the set up priority of the dynamic encap tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 22 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelHoldPriority OBJECT-TYPE
     SYNTAX	    Unsigned32   (0..7)
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
     	"Indicates the holding priority of the dynamic encap tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 23 }
                 
 cienaCesGmplsDynamicIngressCoroutedTunnelPathIndex	OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Index into the cienaCesmplsTunnelPathTable entry that
 		specifies the explicit route hops for this dynamic encap tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 24 }
 
 cienaCesGmplsDynamicIngressCoroutedTunnelPathName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The path name corresponding to the pathIndex object."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry	25 }
 	 
 cienaCesGmplsDynamicIngressCoroutedTunnelBandwidthProfile	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the bandwidth profile attached to this tunnel."
 	 ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry	26 }
  
 cienaCesGmplsDynamicIngressCoroutedTunnelResourcePointer OBJECT-TYPE
     SYNTAX      RowPointer
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The OID must be one of the entries in mplsTunnelResourceTable."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 27 } 
 
  cienaCesGmplsDynamicIngressCoroutedTunnelBfdMonitoring        OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of BFD monitoring."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 28 }     
     
  cienaCesGmplsDynamicIngressCoroutedTunnelBfdProfileName 	 OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 29 }   
     
  cienaCesGmplsDynamicIngressCoroutedTunnelBfdSessionName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session name."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 30 }     
  
  cienaCesGmplsDynamicIngressCoroutedTunnelBfdSessionFaulted  OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if BFD is faulted."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 31 }
     
 cienaCesGmplsDynamicIngressCoroutedTunnelBfdProfileIndex   OBJECT-TYPE
     SYNTAX      Unsigned32  (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the BFD profile index in the BFD profile table
	configured for this gmpls dynamic ingress corouted tunnel"
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 32 }     

  cienaCesGmplsDynamicIngressCoroutedTunnelAutoBackupEnable  OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if auto-backup is enabled for this tunnel and 
         always remains 0 in case of backup tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 33 }

  cienaCesGmplsDynamicIngressCoroutedTunnelLspReoptimization OBJECT-TYPE
     SYNTAX      MplsGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if Lsp-Reoptimization is enabled or disabled for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 34 }

  cienaCesGmplsDynamicIngressCoroutedTunnelLspReOptTimeInterval OBJECT-TYPE
     SYNTAX     Unsigned32   (5..60)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates Lsp-Reoptimization Time interval(in minutes) for this tunnel and 
        always remains 0 in case of backup tunnel."
     DEFVAL {30}
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 35 }

 cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointType OBJECT-TYPE
     SYNTAX      PathDisjointType
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates path diversification criteria used for this tunnel.
         It can either be node based or srlg based or link based or combination."
     DEFVAL { srlg }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 36 }

 cienaCesGmplsDynamicIngressCoroutedTunnelPathDisjointMode OBJECT-TYPE
     SYNTAX      PathDisjointMode
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates mode used for path diversification for this tunnel ."
     DEFVAL { strict }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 37 }

  cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeEnable  OBJECT-TYPE
     SYNTAX      MplsGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if auto-size is enabled for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 38 }

  cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeInterval  OBJECT-TYPE
     SYNTAX      Unsigned32 (5..60)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-sizing interval(in minutes) for this tunnel and 
        always remains 0 in case of backup tunnel."
     DEFVAL { 5 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 39 }

  cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeFailureHdlr  OBJECT-TYPE
     SYNTAX      AutoSizeFailHdlr
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-sizing failure handler for this tunnel."
     DEFVAL { alarm }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 40 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeState  OBJECT-TYPE
     SYNTAX      AutoSizeState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates dynamic auto-sizing states for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 41 }

  cienaCesGmplsDynamicIngressCoroutedTunnelAutoSizeMode  OBJECT-TYPE
     SYNTAX      AutoSizeMode
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-sizing mode for this tunnel."
     DEFVAL { cac }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 42 }

  cienaCesGmplsDynamicIngressCoroutedTunnelMinBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size minimum bandwidth(in kbps) for this tunnel and 
        always remains 0 in case of backup tunnel."
     DEFVAL { 0 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 43 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelMaxBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size maximum bandwidth(in kbps) for this tunnel and 
        always remains 0 in case of backup tunnel."
     DEFVAL { 1000000000 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 44 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelIncBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size increment bandwidth(in kbps) for this tunnel and
        always remains 0 in case of backup tunnel."
     DEFVAL { 0 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 45 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelCurBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size current bandwidth(in kbps) for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 46 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelReqBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size requested bandwidth(in kbps) for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 47 }
           
  cienaCesGmplsDynamicIngressCoroutedTunnelUsedBandwidth  OBJECT-TYPE
     SYNTAX      MplsBitRate
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates auto-size used bandwidth(in kbps) for this tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 48 }

 cienaCesGmplsDynamicIngressCoroutedTunnelClassType  OBJECT-TYPE
     SYNTAX      Unsigned32 (0..7)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates the DSTE class type (CT0-CT7) mapped to this Tunnel."
     DEFVAL { 0 }
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 49 }

 cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAll  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Resource affinity Admin Color-Group which is applied in 
         include-all colors mode to constrain path selection and
         always remains 0 in case of backup tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 50 }

 cienaCesGmplsDynamicIngressCoroutedTunnelResourceIncludeAny  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Resource affinity Admin Color-Group which is applied in 
         include-any colors mode to constrain path selection and
         always remains 0 in case of backup tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 51 }

 cienaCesGmplsDynamicIngressCoroutedTunnelResourceExcludeAny  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Resource affinity Admin Color-Group which is applied in 
         exclude-any colors mode to constrain path selection and
         always remains 0 in case of backup tunnel."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 52 }

 cienaCesGmplsDynamicIngressCoroutedLspId   OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "Indicates the Ingress LSP ID."
     ::= { cienaCesGmplsDynamicIngressCoroutedTunnelEntry 53 }          
    
 --
 -- GMPLS static Decap uni dir tunnel table
 --

 cienaCesGmplsStaticEgressUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticEgressUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS decap tunnels."
     ::= { cienaCesGmpls 5 }
     
 cienaCesGmplsStaticEgressUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticEgressUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static decap tunnel table. "
     INDEX {cienaCesGmplsStaticEgressUniDirTunnelIndex}
     ::= { cienaCesGmplsStaticEgressUniDirTunnelTable 1 }
     
 CienaCesGmplsStaticEgressUniDirTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsStaticEgressUniDirTunnelIndex                 	Unsigned32,
     cienaCesGmplsStaticEgressUniDirTunnelName                  	DisplayString,
     cienaCesGmplsStaticEgressUniDirTunnelAdminState		CienaGlobalState,
     cienaCesGmplsStaticEgressUniDirTunnelOperState			CienaGlobalState, 
     cienaCesGmplsStaticEgressUniDirTunnelPrevHopIpAddr	       			IpAddress, 
     cienaCesGmplsStaticEgressUniDirTunnelSourceIpAddr				IpAddress,                               
     cienaCesGmplsStaticEgressUniDirTunnelDestIpAddr	       		IpAddress, 
     cienaCesGmplsStaticEgressUniDirTunnelForwardInLabel			Unsigned32
   }

 cienaCesGmplsStaticEgressUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 1 }
  
 cienaCesGmplsStaticEgressUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the decap tunnel name."
     ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 2 }
  
 
 cienaCesGmplsStaticEgressUniDirTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Administrative status of the static decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 3 }	
 
 cienaCesGmplsStaticEgressUniDirTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static decap tunnel."
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 4 }
 
 cienaCesGmplsStaticEgressUniDirTunnelPrevHopIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Prev hop IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 5 }    
 	   
 cienaCesGmplsStaticEgressUniDirTunnelSourceIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 6 }

 cienaCesGmplsStaticEgressUniDirTunnelDestIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"destination IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 7 }

 
 cienaCesGmplsStaticEgressUniDirTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0.. 1048575 )		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the label of the static decap tunnel."
 	 ::= { cienaCesGmplsStaticEgressUniDirTunnelEntry 8 }
 
  

 --
 -- GMPLS static Decap co routed tunnel table
 --
    
 cienaCesGmplsStaticEgressCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticEgressCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS decap tunnels."
     ::= { cienaCesGmpls 6 }
     
 cienaCesGmplsStaticEgressCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticEgressCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static decap tunnel table. "
     INDEX {cienaCesGmplsStaticEgressCoroutedTunnelIndex}
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelTable 1 }
     
 CienaCesGmplsStaticEgressCoroutedTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsStaticEgressCoroutedTunnelIndex                	Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelName                 	DisplayString,
     cienaCesGmplsStaticEgressCoroutedTunnelAdminState				CienaGlobalState,
     cienaCesGmplsStaticEgressCoroutedTunnelOperState				CienaGlobalState,
     cienaCesGmplsStaticEgressCoroutedTunnelPrevHopIpAddr	       		IpAddress, 
     cienaCesGmplsStaticEgressCoroutedTunnelSourceIpAddr				IpAddress,                               
     cienaCesGmplsStaticEgressCoroutedTunnelDestIpAddr	       		IpAddress,  
     cienaCesGmplsStaticEgressCoroutedTunnelForwardInLabel			Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelReverseOutLabel			Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelProtectionRole  				INTEGER,
     cienaCesGmplsStaticEgressCoroutedTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsStaticEgressCoroutedTunnelProtectionState			INTEGER,
     cienaCesGmplsStaticEgressCoroutedTunnelGrpIndex					Unsigned32,  
     cienaCesGmplsStaticEgressCoroutedTunnelReversion       			INTEGER,
     cienaCesGmplsStaticEgressCoroutedTunnelReversionTimeout    		Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelCosProfileIndex			Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelCosProfileName			DisplayString,
     cienaCesGmplsStaticEgressCoroutedTunnelBfdMonitoring      		CienaGlobalState,
     cienaCesGmplsStaticEgressCoroutedTunnelBfdProfileName			DisplayString,
     cienaCesGmplsStaticEgressCoroutedTunnelBfdSessionName			DisplayString,
     cienaCesGmplsStaticEgressCoroutedTunnelAisMonitoring			CienaGlobalState,
     cienaCesGmplsStaticEgressCoroutedTunnelAisProfileName			DisplayString,
     cienaCesGmplsStaticEgressCoroutedTunnelBfdSessionFaulted 		INTEGER,
     cienaCesGmplsStaticEgressCoroutedTunnelBfdProfileIndex			Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelRecoveryDisjoint        INTEGER,
     cienaCesGmplsStaticEgressCoroutedTunnelTTLPolicy         TTLPolicy,
     cienaCesGmplsStaticEgressCoroutedTunnelFixedTTL                Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelPrevHopIfNum            Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelLspId                   Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelSrcTunnelId             Unsigned32,
     cienaCesGmplsStaticEgressCoroutedTunnelDestTunnelId            Unsigned32                                                               
   }

 cienaCesGmplsStaticEgressCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 1 }
  
 cienaCesGmplsStaticEgressCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the decap tunnel name."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 2 }
  
 
 cienaCesGmplsStaticEgressCoroutedTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Administrative status of the static decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 3 }	
 
 cienaCesGmplsStaticEgressCoroutedTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static decap tunnel."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 4 }
 
 cienaCesGmplsStaticEgressCoroutedTunnelPrevHopIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Prev hop IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 5 }    
 	   
 cienaCesGmplsStaticEgressCoroutedTunnelSourceIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 6 }

 cienaCesGmplsStaticEgressCoroutedTunnelDestIpAddr OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"destination IP address of the static decap tunnel. This object cannot be modified once the static decap
 	 	 tunnel entry is created."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 7 }

 
 cienaCesGmplsStaticEgressCoroutedTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0.. 1048575 )		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the in label of the static decap tunnel."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 8 }

 cienaCesGmplsStaticEgressCoroutedTunnelReverseOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0.. 1048575 )		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the out label of the static decap tunnel."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 9 } 

 cienaCesGmplsStaticEgressCoroutedTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies protection tunnel role."
 	 ::= {cienaCesGmplsStaticEgressCoroutedTunnelEntry 10 }
  	 
 cienaCesGmplsStaticEgressCoroutedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the static encap tunnel."
 	 ::= {cienaCesGmplsStaticEgressCoroutedTunnelEntry 11 }
 
 cienaCesGmplsStaticEgressCoroutedTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the static encap tunnel."
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 12 }
                
 cienaCesGmplsStaticEgressCoroutedTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the Dynamic encap tunnel is a member."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 13 }

 cienaCesGmplsStaticEgressCoroutedTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 14 }

 cienaCesGmplsStaticEgressCoroutedTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsStaticEgressCoroutedTunnelEntry 15 }

  cienaCesGmplsStaticEgressCoroutedTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 16 }
                                                        
  cienaCesGmplsStaticEgressCoroutedTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsStaticEgressCoroutedTunnelEntry 17 }    
 	 
 
  cienaCesGmplsStaticEgressCoroutedTunnelBfdMonitoring        OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of BFD monitoring."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 18 }     
     
  cienaCesGmplsStaticEgressCoroutedTunnelBfdProfileName 	 OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 19 }   
     
  cienaCesGmplsStaticEgressCoroutedTunnelBfdSessionName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session name."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 20 }     
  
  cienaCesGmplsStaticEgressCoroutedTunnelAisMonitoring			OBJECT-TYPE
  	 SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of AIS monitoring."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 21 }  
   
  cienaCesGmplsStaticEgressCoroutedTunnelAisProfileName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent AIS profile name."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 22 }     

  cienaCesGmplsStaticEgressCoroutedTunnelBfdSessionFaulted  OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates if BFD is faulted."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 23 }
     
 cienaCesGmplsStaticEgressCoroutedTunnelBfdProfileIndex   OBJECT-TYPE
     SYNTAX      Unsigned32  (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the BFD profile index in the BFD profile table
	configured for this gmpls static egress corouted tunnel"
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 24 }     
 
 cienaCesGmplsStaticEgressCoroutedTunnelRecoveryDisjoint 	OBJECT-TYPE 	 
 	 SYNTAX      INTEGER {
  	                      none(1),
  	                      link(2),
  	                      node(3),
  	                      srlg(4),
  	                      unknown(5)
  	 }                   
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies the type of Recovery Disjoint"
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 25 }  

  cienaCesGmplsStaticEgressCoroutedTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 26 }
 
 cienaCesGmplsStaticEgressCoroutedTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the Dynamic encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 27 }		
                                                      
 cienaCesGmplsStaticEgressCoroutedTunnelPrevHopIfNum	OBJECT-TYPE
     SYNTAX		Unsigned32  
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"This represents previous hop interface number 
     	 for this gmpls static egress corouted tunnel."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 28 }		
   
 cienaCesGmplsStaticEgressCoroutedTunnelLspId	OBJECT-TYPE
     SYNTAX		Unsigned32 
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"This represents LSP ID 
     	 for this gmpls static egress corouted tunnel."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 29 }		
   
 cienaCesGmplsStaticEgressCoroutedTunnelSrcTunnelId	OBJECT-TYPE
     SYNTAX		Unsigned32  
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"This represents source tunnel ID 
     	 for this gmpls static egress corouted tunnel."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 30 }		

 cienaCesGmplsStaticEgressCoroutedTunnelDestTunnelId	OBJECT-TYPE
     SYNTAX		Unsigned32  
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"This represents source tunnel ID 
     	 for this gmpls static egress corouted tunnel."
     ::= { cienaCesGmplsStaticEgressCoroutedTunnelEntry 31 }		  

 --
 -- GMPLS Dynamic Decap uni dir tunnel table
 --                       

 cienaCesGmplsDynamicEgressUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicEgressUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS dynamic decap tunnels." 
     ::= { cienaCesGmpls 7 }
     
 cienaCesGmplsDynamicEgressUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicEgressUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the dynamic decap tunnel table."
     INDEX {cienaCesGmplsDynamicEgressUniDirTunnelIndex}
     ::= { cienaCesGmplsDynamicEgressUniDirTunnelTable 1 }
     
 CienaCesGmplsDynamicEgressUniDirTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsDynamicEgressUniDirTunnelIndex          		Unsigned32,
     cienaCesGmplsDynamicEgressUniDirTunnelName           		DisplayString,
     cienaCesGmplsDynamicEgressUniDirTunnelAdminState			CienaGlobalState,
     cienaCesGmplsDynamicEgressUniDirTunnelOperState			CienaGlobalState,
     cienaCesGmplsDynamicEgressUniDirLspId      		Unsigned32, 
	 cienaCesGmplsDynamicEgressUniDirTunnelPrevHopIpAddr	       	IpAddress,
     cienaCesGmplsDynamicEgressUniDirTunnelSourceIpAddr			IpAddress,
     cienaCesGmplsDynamicEgressUniDirTunnelDestIpAddr			IpAddress,
     cienaCesGmplsDynamicEgressUniDirTunnelForwardInLabel		Unsigned32
   }

 cienaCesGmplsDynamicEgressUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 1 }
  
 cienaCesGmplsDynamicEgressUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the dynamic decap tunnel name."
     ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 2 }
  
 cienaCesGmplsDynamicEgressUniDirTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the dynamic decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 3 }	
 
 cienaCesGmplsDynamicEgressUniDirTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 4 }
 
 cienaCesGmplsDynamicEgressUniDirLspId   OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the LSP ID."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 5 }
 	 
 cienaCesGmplsDynamicEgressUniDirTunnelPrevHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 6 } 	 
 
 cienaCesGmplsDynamicEgressUniDirTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 7 }

 cienaCesGmplsDynamicEgressUniDirTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 8 }
 

 cienaCesGmplsDynamicEgressUniDirTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Idicates the label of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressUniDirTunnelEntry 9 }  
 	                  
 --
 -- GMPLS Dynamic Decap co routed tunnel table
 --                       

 cienaCesGmplsDynamicEgressCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicEgressCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS dynamic decap tunnels.
            "
     ::= { cienaCesGmpls 8 }
     
 cienaCesGmplsDynamicEgressCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicEgressCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the dynamic decap tunnel table."
     INDEX {cienaCesGmplsDynamicEgressCoroutedTunnelIndex}
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelTable 1 }
 
 CienaCesGmplsDynamicEgressCoroutedTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsDynamicEgressCoroutedTunnelIndex          	Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelName           		DisplayString,
     cienaCesGmplsDynamicEgressCoroutedTunnelAdminState			CienaGlobalState,
     cienaCesGmplsDynamicEgressCoroutedTunnelOperState			CienaGlobalState,
     cienaCesGmplsDynamicEgressCoroutedLspId      		Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelPrevHopIpAddr	       	IpAddress,
     cienaCesGmplsDynamicEgressCoroutedTunnelSourceIpAddr			IpAddress,
     cienaCesGmplsDynamicEgressCoroutedTunnelDestIpAddr			IpAddress,
     cienaCesGmplsDynamicEgressCoroutedTunnelForwardInLabel		Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelReverseOutLabel			Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelProtectionRole  				INTEGER,
     cienaCesGmplsDynamicEgressCoroutedTunnelProtectionPartnerName	DisplayString, 
     cienaCesGmplsDynamicEgressCoroutedTunnelProtectionState			INTEGER,
     cienaCesGmplsDynamicEgressCoroutedTunnelGrpIndex					Unsigned32,  
     cienaCesGmplsDynamicEgressCoroutedTunnelReversion       			INTEGER,
     cienaCesGmplsDynamicEgressCoroutedTunnelReversionTimeout    		Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelCosProfileIndex			Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelCosProfileName			DisplayString,
     cienaCesGmplsDynamicEgressCoroutedTunnelBfdMonitoring      		CienaGlobalState,
     cienaCesGmplsDynamicEgressCoroutedTunnelBfdProfileName			DisplayString,
     cienaCesGmplsDynamicEgressCoroutedTunnelBfdSessionName			DisplayString,
     cienaCesGmplsDynamicEgressCoroutedTunnelBfdSessionFaulted 		INTEGER,
     cienaCesGmplsDynamicEgressCoroutedTunnelBfdProfileIndex		Unsigned32,
     cienaCesGmplsDynamicEgressCoroutedTunnelTTLPolicy              TTLPolicy,
     cienaCesGmplsDynamicEgressCoroutedTunnelFixedTTL               Unsigned32
   }

 cienaCesGmplsDynamicEgressCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 1 }
  
 cienaCesGmplsDynamicEgressCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the dynamic decap tunnel name."
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 2 }
  
 cienaCesGmplsDynamicEgressCoroutedTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the dynamic decap tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 3 }	
 
 cienaCesGmplsDynamicEgressCoroutedTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 4 }
 
 cienaCesGmplsDynamicEgressCoroutedLspId   OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the LSP ID."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 5 }

 cienaCesGmplsDynamicEgressCoroutedTunnelPrevHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Prev hop IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 6 }
 
 cienaCesGmplsDynamicEgressCoroutedTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 7 }

 cienaCesGmplsDynamicEgressCoroutedTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 8 }
 

 cienaCesGmplsDynamicEgressCoroutedTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32 
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Idicates the label of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 9 }  
 	     

 cienaCesGmplsDynamicEgressCoroutedTunnelReverseOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0.. 1048575 )		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the out label of the dynamic decap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 10 } 

 cienaCesGmplsDynamicEgressCoroutedTunnelProtectionRole		OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies role of this dynamic tunnel"
 	 ::= {cienaCesGmplsDynamicEgressCoroutedTunnelEntry 11 }
  	 
 cienaCesGmplsDynamicEgressCoroutedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Protection partner name of the dynamic encap tunnel."
 	 ::= {cienaCesGmplsDynamicEgressCoroutedTunnelEntry 12 }
 
 cienaCesGmplsDynamicEgressCoroutedTunnelProtectionState		OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the Protection status of the static encap tunnel."
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 13 }
                
 cienaCesGmplsDynamicEgressCoroutedTunnelGrpIndex			OBJECT-TYPE
     SYNTAX		Unsigned32
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"The group ID of the tunnel group of which the Dynamic encap tunnel is a member."
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 14 }

 cienaCesGmplsDynamicEgressCoroutedTunnelReversion 	OBJECT-TYPE
     SYNTAX		INTEGER {
     					on(1),
     					off(2)                         
     			}
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"Indicates whether tunnel reversion is turned on/off for this encap tunnel."
     DEFVAL {  off }
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 15 }

 cienaCesGmplsDynamicEgressCoroutedTunnelReversionTimeout   OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel reversion hold time in seconds before
		the primary encap tunnel switches back as active tunnel in its tunnel group."
 	 ::= {cienaCesGmplsDynamicEgressCoroutedTunnelEntry 16 }

  cienaCesGmplsDynamicEgressCoroutedTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 17 }
                                                        
  cienaCesGmplsDynamicEgressCoroutedTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsDynamicEgressCoroutedTunnelEntry 18 }    
 	 
 
  cienaCesGmplsDynamicEgressCoroutedTunnelBfdMonitoring        OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This parameter indicates if BFD monitoring is enabled or disableb on tunnel"
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 19 }     
     
  cienaCesGmplsDynamicEgressCoroutedTunnelBfdProfileName 	 OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "It Indicates assigned BFD profile names to tunnel"
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 20 }   
     
  cienaCesGmplsDynamicEgressCoroutedTunnelBfdSessionName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "It Indicates BFD session names."
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 21 }     
  
  cienaCesGmplsDynamicEgressCoroutedTunnelBfdSessionFaulted  OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies if BFD faulted"
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 22 }

 cienaCesGmplsDynamicEgressCoroutedTunnelBfdProfileIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)	
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the BFD profile index in the BFD profile table
	configured for this gmpls dynamic egress corouted tunnel" 
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 23 }
     
  cienaCesGmplsDynamicEgressCoroutedTunnelTTLPolicy	 OBJECT-TYPE
     SYNTAX		TTLPolicy
     MAX-ACCESS	 read-only
     STATUS		 current
     DESCRIPTION
     	"Specifies whether TTL is fixed or inherited from the frame."
     DEFVAL { fixed }
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 24 }
 
 cienaCesGmplsDynamicEgressCoroutedTunnelFixedTTL	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..255)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
     	"If the TTL policy for the Dynamic encap tunnel is fixed, then this object is 
     	the TTL value."
     DEFVAL { 255 }
     ::= { cienaCesGmplsDynamicEgressCoroutedTunnelEntry 25 }		
      

 --
 -- GMPLS Static transit uni dir tunnel table
 --                       

 cienaCesGmplsStaticTransitUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticTransitUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static TransitUniDir tunnels.
            "
     ::= { cienaCesGmpls 9 }
     
 cienaCesGmplsStaticTransitUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticTransitUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static TransitUniDir tunnel table."
     INDEX {cienaCesGmplsStaticTransitUniDirTunnelIndex}
     ::= { cienaCesGmplsStaticTransitUniDirTunnelTable 1 }
     
 CienaCesGmplsStaticTransitUniDirTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsStaticTransitUniDirTunnelIndex          	Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelName           	DisplayString,
     cienaCesGmplsStaticTransitUniDirTunnelAdminState		CienaGlobalState,
     cienaCesGmplsStaticTransitUniDirTunnelOperState		CienaGlobalState,  
     cienaCesGmplsStaticTransitUniDirTunnelSourceIpAddr			IpAddress,
     cienaCesGmplsStaticTransitUniDirTunnelDestIpAddr			IpAddress,     
     cienaCesGmplsStaticTransitUniDirTunnelNextHopIpAddr	IpAddress,
     cienaCesGmplsStaticTransitUniDirTunnelPrevHopIpAddr	       	IpAddress,
     cienaCesGmplsStaticTransitUniDirTunnelForwardInLabel		Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelForwardOutLabel		Unsigned32,  
     cienaCesGmplsStaticTransitUniDirTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsStaticTransitUniDirTunnelFixedTTL 		Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelCosProfileIndex	Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelCosProfileName	DisplayString, 
     cienaCesGmplsStaticTransitUniDirTunnelAisMonitoring			CienaGlobalState,
     cienaCesGmplsStaticTransitUniDirTunnelAisProfileName			DisplayString,
     cienaCesGmplsStaticTransitUniDirTunnelIncomingPackets  Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelOutgoingPackets  Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelIncomingBytes  Unsigned32,
     cienaCesGmplsStaticTransitUniDirTunnelOutgoingBytes  Unsigned32
   }

 cienaCesGmplsStaticTransitUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 1 }
  
 cienaCesGmplsStaticTransitUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the static TransitUniDir tunnel name."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 2 }
  
 cienaCesGmplsStaticTransitUniDirTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the static TransitUniDir tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 3 }	
 
 cienaCesGmplsStaticTransitUniDirTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 4 }

 cienaCesGmplsStaticTransitUniDirTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 5 }
 
 cienaCesGmplsStaticTransitUniDirTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 6 }  
 	 
 cienaCesGmplsStaticTransitUniDirTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 7 } 
 	 
 cienaCesGmplsStaticTransitUniDirTunnelPrevHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Previous hop IP address of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 8 }
                                                            
                                                            
  cienaCesGmplsStaticTransitUniDirTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 9 }


  cienaCesGmplsStaticTransitUniDirTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the static TransitUniDir tunnel."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 10 }

  cienaCesGmplsStaticTransitUniDirTunnelTTLPolicy   OBJECT-TYPE
      SYNTAX		TTLPolicy
      MAX-ACCESS	 read-only
      STATUS		 current
      DESCRIPTION
      "Specifies whether TTL is fixed or inherited from the frame."
      DEFVAL { fixed }
      ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 11 }

  cienaCesGmplsStaticTransitUniDirTunnelFixedTTL    OBJECT-TYPE
      SYNTAX		Unsigned32  (1..255)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "If the TTL policy for the static TransitUniDir tunnel is fixed, then this object is 
      the TTL value."
      DEFVAL { 255 }
      ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 12 }     
      
  cienaCesGmplsStaticTransitUniDirTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 13 }
                                                        
  cienaCesGmplsStaticTransitUniDirTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsStaticTransitUniDirTunnelEntry 14 }          
      
  cienaCesGmplsStaticTransitUniDirTunnelAisMonitoring			OBJECT-TYPE
  	 SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies AIS monitoring's status."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 15 }  
   
  cienaCesGmplsStaticTransitUniDirTunnelAisProfileName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object contais AIS profile name."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 16 } 
     
  cienaCesGmplsStaticTransitUniDirTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 17 }  
   
  cienaCesGmplsStaticTransitUniDirTunnelOutgoingPackets	OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"The number of outgoing packets for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 18 }
   
  cienaCesGmplsStaticTransitUniDirTunnelIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The number of incoming bytes for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= {cienaCesGmplsStaticTransitUniDirTunnelEntry 19 }  
 	
  cienaCesGmplsStaticTransitUniDirTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitUniDirTunnelEntry 20 }         
  
 --
 -- GMPLS Static transit co routed tunnel table
 --                       
               
 cienaCesGmplsStaticTransitCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsStaticTransitCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS static TransitCorouted tunnels.
            "
     ::= { cienaCesGmpls 10 }
     
 cienaCesGmplsStaticTransitCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsStaticTransitCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the static TransitCorouted tunnel table."
     INDEX {cienaCesGmplsStaticTransitCoroutedTunnelIndex}
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelTable 1 }
     
 CienaCesGmplsStaticTransitCoroutedTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsStaticTransitCoroutedTunnelIndex          	Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelName           	DisplayString,
     cienaCesGmplsStaticTransitCoroutedTunnelAdminState		CienaGlobalState,
     cienaCesGmplsStaticTransitCoroutedTunnelOperState		CienaGlobalState,  
     cienaCesGmplsStaticTransitCoroutedTunnelSourceIpAddr			IpAddress,
     cienaCesGmplsStaticTransitCoroutedTunnelDestIpAddr			IpAddress,     
     cienaCesGmplsStaticTransitCoroutedTunnelNextHopIpAddr	IpAddress,
     cienaCesGmplsStaticTransitCoroutedTunnelPrevHopIpAddr	       	IpAddress,
     cienaCesGmplsStaticTransitCoroutedTunnelForwardInLabel		Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelForwardOutLabel		Unsigned32, 
	 cienaCesGmplsStaticTransitCoroutedTunnelReverseInLabel		Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelReverseOutLabel		Unsigned32,            
     cienaCesGmplsStaticTransitCoroutedTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsStaticTransitCoroutedTunnelFixedTTL 		Unsigned32, 
     cienaCesGmplsStaticTransitCoroutedTunnelCosProfileIndex	Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelCosProfileName		DisplayString,
     cienaCesGmplsStaticTransitCoroutedTunnelAisMonitoring			CienaGlobalState,
     cienaCesGmplsStaticTransitCoroutedTunnelAisProfileName			DisplayString,
     cienaCesGmplsStaticTransitCoroutedTunnelPrevHopIfNum            Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelNextHopIfNum            Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelLspId                   Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelSrcTunnelId             Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelDestTunnelId            Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelIncomingPackets  Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelOutgoingPackets  Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelIncomingBytes  Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelOutgoingBytes  Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelReverseIncomingPackets Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelReverseOutgoingPackets Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelReverseIncomingBytes Unsigned32,
     cienaCesGmplsStaticTransitCoroutedTunnelReverseOutgoingBytes Unsigned32
   }

 cienaCesGmplsStaticTransitCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 1 }
  
 cienaCesGmplsStaticTransitCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the static TransitCorouted tunnel name."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 2 }
  
 cienaCesGmplsStaticTransitCoroutedTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the static TransitCorouted tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 3 }	
 
 cienaCesGmplsStaticTransitCoroutedTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 4 }

 cienaCesGmplsStaticTransitCoroutedTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 5 }
 
 cienaCesGmplsStaticTransitCoroutedTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 6 }  
 	 
 cienaCesGmplsStaticTransitCoroutedTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 7 } 
 	 
 cienaCesGmplsStaticTransitCoroutedTunnelPrevHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Previous hop IP address of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 8 }
                                                                                                                        
  cienaCesGmplsStaticTransitCoroutedTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 9 }


  cienaCesGmplsStaticTransitCoroutedTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 10 }       
 	 
  cienaCesGmplsStaticTransitCoroutedTunnelReverseInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 11 }


  cienaCesGmplsStaticTransitCoroutedTunnelReverseOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the static TransitCorouted tunnel."
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 12 }
 	 

  cienaCesGmplsStaticTransitCoroutedTunnelTTLPolicy   OBJECT-TYPE
      SYNTAX		TTLPolicy
      MAX-ACCESS	 read-only
      STATUS		 current
      DESCRIPTION
      "Specifies whether TTL is fixed or inherited from the frame."
      DEFVAL { fixed }
      ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 13 }

  cienaCesGmplsStaticTransitCoroutedTunnelFixedTTL    OBJECT-TYPE
      SYNTAX		Unsigned32  (1..255)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "If the TTL policy for the static TransitCorouted tunnel is fixed, then this object is 
      the TTL value."
      DEFVAL { 255 }
      ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 14 }  
      
  cienaCesGmplsStaticTransitCoroutedTunnelCosProfileIndex      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (1..65535)	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents Cos profile index" 
 	 --DEFVAL { 0 }
 	 ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 15 }
                                                        
  cienaCesGmplsStaticTransitCoroutedTunnelCosProfileName 		OBJECT-TYPE
  	 SYNTAX		DisplayString   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Specifies the name of Cos profile"
 	 ::= {cienaCesGmplsStaticTransitCoroutedTunnelEntry 16 }    
        
      
  cienaCesGmplsStaticTransitCoroutedTunnelAisMonitoring			OBJECT-TYPE
  	 SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies AIS monitoring."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 17 }  
   
  cienaCesGmplsStaticTransitCoroutedTunnelAisProfileName			OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object contains AIS profile name "
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 18 }

  cienaCesGmplsStaticTransitCoroutedTunnelPrevHopIfNum      OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "This represents previous hop interface number 
         for this gmpls transit corouted tunnel."
         --DEFVAL { 0 }
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 19 }


  cienaCesGmplsStaticTransitCoroutedTunnelNextHopIfNum      OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "This represents next hop interface number 
         for this gmpls transit corouted tunnel."
         --DEFVAL { 0 }
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 20 }

  cienaCesGmplsStaticTransitCoroutedTunnelLspId      OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "This represents LSP ID for this gmpls transit corouted tunnel."
         --DEFVAL { 0 }
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 21 }

  cienaCesGmplsStaticTransitCoroutedTunnelSrcTunnelId      OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "This represents source tunnel ID 
         for this gmpls transit corouted tunnel."
         --DEFVAL { 0 }
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 22 }

  cienaCesGmplsStaticTransitCoroutedTunnelDestTunnelId      OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "This represents destination tunnel ID 
         for this gmpls transit corouted tunnel."
         --DEFVAL { 0 }
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 23 }
     
  cienaCesGmplsStaticTransitCoroutedTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 24 }  
   
  cienaCesGmplsStaticTransitCoroutedTunnelOutgoingPackets	OBJECT-TYPE
     SYNTAX		Unsigned32  
     MAX-ACCESS         read-only
     STATUS		current
     DESCRIPTION
        "The number of outgoing packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 25 }
   
  cienaCesGmplsStaticTransitCoroutedTunnelIncomingBytes    OBJECT-TYPE
     SYNTAX		Unsigned32   
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
        "The number of incoming bytes for the given transit tunnel. 
         This object is supported only on specific platforms."
     ::= {cienaCesGmplsStaticTransitCoroutedTunnelEntry 26 }  
 
  cienaCesGmplsStaticTransitCoroutedTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 27 }  
     
  cienaCesGmplsStaticTransitCoroutedTunnelReverseIncomingPackets         OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of reverse direction incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 28 }

  cienaCesGmplsStaticTransitCoroutedTunnelReverseOutgoingPackets OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS  read-only
     STATUS          current
     DESCRIPTION
        "The number of reverse direction outgoing packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 29 }

  cienaCesGmplsStaticTransitCoroutedTunnelReverseIncomingBytes    OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
        "The number of reverse direction incoming bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= {cienaCesGmplsStaticTransitCoroutedTunnelEntry 30 }

  cienaCesGmplsStaticTransitCoroutedTunnelReverseOutgoingBytes           OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of reverse direction outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsStaticTransitCoroutedTunnelEntry 31 }
 	 
 --
 -- GMPLS Dynamic transit unidir tunnel table
 --                                          

 cienaCesGmplsDynamicTransitUniDirTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicTransitUniDirTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS Dynamic TransitUniDir tunnels.
            "
     ::= { cienaCesGmpls 11 }
     
 cienaCesGmplsDynamicTransitUniDirTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicTransitUniDirTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the Dynamic TransitUniDir tunnel table."
     INDEX {cienaCesGmplsDynamicTransitUniDirTunnelIndex}
     ::= { cienaCesGmplsDynamicTransitUniDirTunnelTable 1 }
     
 CienaCesGmplsDynamicTransitUniDirTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsDynamicTransitUniDirTunnelIndex          	Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelName           	DisplayString,
     cienaCesGmplsDynamicTransitUniDirTunnelAdminState		CienaGlobalState,
     cienaCesGmplsDynamicTransitUniDirTunnelOperState		CienaGlobalState,  
     cienaCesGmplsDynamicTransitUniDirTunnelSourceIpAddr			IpAddress,
     cienaCesGmplsDynamicTransitUniDirTunnelDestIpAddr			IpAddress,     
     cienaCesGmplsDynamicTransitUniDirTunnelNextHopIpAddr	IpAddress,
     cienaCesGmplsDynamicTransitUniDirTunnelForwardInLabel		Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelForwardOutLabel		Unsigned32,  
     cienaCesGmplsDynamicTransitUniDirTunnelTTLPolicy		TTLPolicy,
     cienaCesGmplsDynamicTransitUniDirTunnelFixedTTL 		Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelIncomingPackets  Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelOutgoingPackets  Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelIncomingBytes  Unsigned32,
     cienaCesGmplsDynamicTransitUniDirTunnelOutgoingBytes  Unsigned32 
   }

 cienaCesGmplsDynamicTransitUniDirTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 1 }
  
 cienaCesGmplsDynamicTransitUniDirTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the Dynamic TransitUniDir tunnel name."
     ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 2 }
  
 cienaCesGmplsDynamicTransitUniDirTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the Dynamic TransitUniDir tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 3 }	
 
 cienaCesGmplsDynamicTransitUniDirTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 4 }

 cienaCesGmplsDynamicTransitUniDirTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 5 }
 
 cienaCesGmplsDynamicTransitUniDirTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 6 }  
 	 
 cienaCesGmplsDynamicTransitUniDirTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 7 } 
 	                                                             
  cienaCesGmplsDynamicTransitUniDirTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 8 }


  cienaCesGmplsDynamicTransitUniDirTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the Dynamic TransitUniDir tunnel."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 9 }

  cienaCesGmplsDynamicTransitUniDirTunnelTTLPolicy   OBJECT-TYPE
      SYNTAX		TTLPolicy
      MAX-ACCESS	 read-only
      STATUS		 current
      DESCRIPTION
      "Specifies whether TTL is fixed or inherited from the frame."
      DEFVAL { fixed }
      ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 10 }

  cienaCesGmplsDynamicTransitUniDirTunnelFixedTTL    OBJECT-TYPE
      SYNTAX		Unsigned32  (1..255)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "If the TTL policy for the Dynamic TransitUniDir tunnel is fixed, then this object is 
      the TTL value."
      DEFVAL { 255 }
      ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 11 }    
      
  cienaCesGmplsDynamicTransitUniDirTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 12 }  
   
  cienaCesGmplsDynamicTransitUniDirTunnelOutgoingPackets	OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"The number of outgoing packets for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 13 }
   
  cienaCesGmplsDynamicTransitUniDirTunnelIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The number of incoming bytes for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= {cienaCesGmplsDynamicTransitUniDirTunnelEntry 14 }  
 	
  cienaCesGmplsDynamicTransitUniDirTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitUniDirTunnelEntry 15 } 
           
     
 --
 -- GMPLS Dynamic transit corouted tunnel table
 --                       
 cienaCesGmplsDynamicTransitCoroutedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsDynamicTransitCoroutedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS Dynamic TransitCoRouted tunnels.
            "
     ::= { cienaCesGmpls 12 }
     
 cienaCesGmplsDynamicTransitCoroutedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsDynamicTransitCoroutedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the Dynamic TransitCorouted tunnel table."
     INDEX {cienaCesGmplsDynamicTransitCoroutedTunnelIndex}
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelTable 1 }
     
 CienaCesGmplsDynamicTransitCoroutedTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsDynamicTransitCoroutedTunnelIndex          	        Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelName           	        DisplayString,
     cienaCesGmplsDynamicTransitCoroutedTunnelAdminState		CienaGlobalState,
     cienaCesGmplsDynamicTransitCoroutedTunnelOperState                 CienaGlobalState,  
     cienaCesGmplsDynamicTransitCoroutedTunnelSourceIpAddr              IpAddress,
     cienaCesGmplsDynamicTransitCoroutedTunnelDestIpAddr		IpAddress,     
     cienaCesGmplsDynamicTransitCoroutedTunnelNextHopIpAddr	        IpAddress,
     cienaCesGmplsDynamicTransitCoroutedTunnelPrevHopIpAddr	       	IpAddress,
     cienaCesGmplsDynamicTransitCoroutedTunnelForwardInLabel		Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelForwardOutLabel		Unsigned32, 
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseInLabel		Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutLabel		Unsigned32,            
     cienaCesGmplsDynamicTransitCoroutedTunnelTTLPolicy		        TTLPolicy,
     cienaCesGmplsDynamicTransitCoroutedTunnelFixedTTL 		        Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelIncomingPackets           Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelOutgoingPackets           Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelIncomingBytes             Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelOutgoingBytes             Unsigned32,  
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingPackets    Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingPackets    Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingBytes      Unsigned32,
     cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingBytes      Unsigned32
   }

 cienaCesGmplsDynamicTransitCoroutedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index in the table."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 1 }
  
 cienaCesGmplsDynamicTransitCoroutedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..32))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the Dynamic TransitCorouted tunnel name."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 2 }
  
 cienaCesGmplsDynamicTransitCoroutedTunnelAdminState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the administrative status of the Dynamic TransitCorouted tunnel."
 	 DEFVAL {enabled}
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 3 }	
 
 cienaCesGmplsDynamicTransitCoroutedTunnelOperState OBJECT-TYPE
 	 SYNTAX		CienaGlobalState
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the operational status of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 4 }

 cienaCesGmplsDynamicTransitCoroutedTunnelSourceIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Source IP address of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 5 }
 
 cienaCesGmplsDynamicTransitCoroutedTunnelDestIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Destination IP address of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 6 }  
 	 
 cienaCesGmplsDynamicTransitCoroutedTunnelNextHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Next hop IP address of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 7 } 
 	 
 cienaCesGmplsDynamicTransitCoroutedTunnelPrevHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Previous hop IP address of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 8 }
                                                                                                                        
  cienaCesGmplsDynamicTransitCoroutedTunnelForwardInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 9 }


  cienaCesGmplsDynamicTransitCoroutedTunnelForwardOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 10 }       
 	 
  cienaCesGmplsDynamicTransitCoroutedTunnelReverseInLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the incoming label of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 11 }


  cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutLabel		OBJECT-TYPE
 	 SYNTAX		Unsigned32   ( 0..1048575 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the outgoing label of the Dynamic TransitCorouted tunnel."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 12 }
 	 

  cienaCesGmplsDynamicTransitCoroutedTunnelTTLPolicy   OBJECT-TYPE
      SYNTAX		TTLPolicy
      MAX-ACCESS	 read-only
      STATUS		 current
      DESCRIPTION
      "Specifies whether TTL is fixed or inherited from the frame."
      DEFVAL { fixed }
      ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 13 }

  cienaCesGmplsDynamicTransitCoroutedTunnelFixedTTL    OBJECT-TYPE
      SYNTAX		Unsigned32  (1..255)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "If the TTL policy for the Dynamic TransitCorouted tunnel is fixed, then this object is 
      the TTL value."
      DEFVAL { 255 }
      ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 14 }    
      
  cienaCesGmplsDynamicTransitCoroutedTunnelIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 15 }  
   
  cienaCesGmplsDynamicTransitCoroutedTunnelOutgoingPackets	OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"The number of outgoing packets for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 16 }
   
  cienaCesGmplsDynamicTransitCoroutedTunnelIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The number of incoming bytes for the given transit tunnel.
 	 	 This object is supported only on specific platforms."
 	 ::= {cienaCesGmplsDynamicTransitCoroutedTunnelEntry 17 }  
 	
  cienaCesGmplsDynamicTransitCoroutedTunnelOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 18 } 
          
  cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingPackets       OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of reverse incoming packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 19 }

  cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingPackets       OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS  read-only
     STATUS          current
     DESCRIPTION
        "The number of reverse outgoing packets for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 20 }

  cienaCesGmplsDynamicTransitCoroutedTunnelReverseIncomingBytes         OBJECT-TYPE
     SYNTAX         Unsigned32
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
         "The number of reverse incoming bytes for the given transit tunnel.
          This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 21 }

  cienaCesGmplsDynamicTransitCoroutedTunnelReverseOutgoingBytes         OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of reverse outgoing bytes for the given transit tunnel.
         This object is supported only on specific platforms."
     ::= { cienaCesGmplsDynamicTransitCoroutedTunnelEntry 22 }

 --
 -- GMPLS Associated Tunnel
 --
 
 cienaCesGmplsAssociatedTunnelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the Gmpls Associated tunnels."

     ::= { cienaCesGmpls 13 }
     
 cienaCesGmplsAssociatedTunnelEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsAssociatedTunnelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the Gmpls static encap tunnel table."
     INDEX {cienaCesGmplsAssociatedTunnelIndex}
     ::= { cienaCesGmplsAssociatedTunnelTable 1 }
     
 CienaCesGmplsAssociatedTunnelEntry ::=  SEQUENCE { 
     cienaCesGmplsAssociatedTunnelIndex                 	Unsigned32,  
     cienaCesGmplsAssociatedTunnelName                   	DisplayString,
     cienaCesGmplsAssociatedForwardTunnelName            	DisplayString,
     cienaCesGmplsAssociatedForwardTunnelType				DisplayString,            
     cienaCesGmplsAssociatedReverseTunnelName           	DisplayString, 
     cienaCesGmplsAssociatedReverseTunnelType				DisplayString,
     cienaCesGmplsAssociatedForwardTunnelDestIpAddr	        IpAddress,
     cienaCesGmplsAssociatedDynamicTunnelSrcIpAddr			IpAddress,
     cienaCesGmplsAssociatedTunnelAdminState	       	CienaGlobalState,
     cienaCesGmplsAssociatedTunnelOperState	       		CienaGlobalState,
     cienaCesGmplsAssociatedForwardTunnelOperState	       	CienaGlobalState,
     cienaCesGmplsAssociatedReverseTunnelOperState	       	CienaGlobalState,
     cienaCesGmplsAssociatedProtectionRole                   INTEGER,
     cienaCesGmplsAssociatedProtectionState                  INTEGER,  
     cienaCesGmplsAssociatedTunnelProtectionPartnerName      DisplayString,
     cienaCesGmplsAssociatedBfdMonitoring            	CienaGlobalState,
     cienaCesGmplsAssociatedBfdProfileName    			DisplayString,
     cienaCesGmplsAssociatedBfdSessionName				DisplayString,  
     cienaCesGmplsAssociatedAisMonitoring				CienaGlobalState,
     cienaCesGmplsAssociatedAisProfileName				DisplayString,
     cienaCesGmplsAssociatedBfdSessionFaulted	  		INTEGER,
     cienaCesGmplsAssociatedBfdProfileIndex                     Unsigned32 
      }
 
 cienaCesGmplsAssociatedTunnelIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique tunnel index in the table."
     ::= { cienaCesGmplsAssociatedTunnelEntry 1 }   
     
  cienaCesGmplsAssociatedTunnelName OBJECT-TYPE
     SYNTAX      DisplayString  
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies Gmpls associated tunnel name."
     ::= { cienaCesGmplsAssociatedTunnelEntry 2 }    
     
  cienaCesGmplsAssociatedForwardTunnelName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated forward tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 3 }       

  cienaCesGmplsAssociatedForwardTunnelType OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated forward tunnel type."
     ::= { cienaCesGmplsAssociatedTunnelEntry 4 } 
      
  cienaCesGmplsAssociatedReverseTunnelName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated reverse tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 5 }   

  cienaCesGmplsAssociatedReverseTunnelType OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated reverse tunnel type."
     ::= { cienaCesGmplsAssociatedTunnelEntry 6 }
     
  cienaCesGmplsAssociatedForwardTunnelDestIpAddr OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated Gmpls forward tunnel destination IP address"
     ::= { cienaCesGmplsAssociatedTunnelEntry 7 }
     
  cienaCesGmplsAssociatedDynamicTunnelSrcIpAddr OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "specifies associated Gmpls reverse tunnel source IP address."
     ::= { cienaCesGmplsAssociatedTunnelEntry 8 } 
     
  cienaCesGmplsAssociatedTunnelAdminState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Administrator status of associated tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 9 }     
     
  cienaCesGmplsAssociatedTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 10 }     
     
  cienaCesGmplsAssociatedForwardTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated forward tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 11 } 
     
  cienaCesGmplsAssociatedReverseTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Operational status of associated reverse tunnel."
     ::= { cienaCesGmplsAssociatedTunnelEntry 12 }    
     
 cienaCesGmplsAssociatedProtectionRole      OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	                      none(0),
 	                      primary(1),
 	                      backup(2)
 	 }	
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection role of the associated  tunnel " 
  	 ::= { cienaCesGmplsAssociatedTunnelEntry 13 }      
  	 
 cienaCesGmplsAssociatedProtectionState      OBJECT-TYPE
 	 SYNTAX		INTEGER	 {
 	 						standby(0),
 	 						active(1)	 						
 	 			}		
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"specifies the protection status of the associated  tunnel " 
  	 ::= { cienaCesGmplsAssociatedTunnelEntry 14 }    
  	 
 cienaCesGmplsAssociatedTunnelProtectionPartnerName	OBJECT-TYPE
 	 SYNTAX		DisplayString
 	 MAX-ACCESS read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"This object represents the tunnel name it is protecting"
 	 ::= { cienaCesGmplsAssociatedTunnelEntry	15 }   	       
     
  cienaCesGmplsAssociatedBfdMonitoring OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of BFD monitoring."
     ::= { cienaCesGmplsAssociatedTunnelEntry 16 }     
     
  cienaCesGmplsAssociatedBfdProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesGmplsAssociatedTunnelEntry 17 }   
     
  cienaCesGmplsAssociatedBfdSessionName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session name."
     ::= { cienaCesGmplsAssociatedTunnelEntry 18 }     
  
  cienaCesGmplsAssociatedAisMonitoring OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies status of AIS monitoring."
     ::= { cienaCesGmplsAssociatedTunnelEntry 19 }  
     
  cienaCesGmplsAssociatedAisProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This object represent BFD profile name."
     ::= { cienaCesGmplsAssociatedTunnelEntry 20 }        

  cienaCesGmplsAssociatedBfdSessionFaulted OBJECT-TYPE
     SYNTAX      INTEGER
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Specifies BFD session error code."
     ::= { cienaCesGmplsAssociatedTunnelEntry 21 }
     
 cienaCesGmplsAssociatedBfdProfileIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the BFD profile index in the BFD profile table configured
	for this gmpls associated tunnel"
     ::= { cienaCesGmplsAssociatedTunnelEntry 22 }       

 --
 --  	GMPLS AR HOP TABLE
 --
        
 cienaCesGmplsTunnelARHopTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsTunnelARHopEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The GmplsTunnelARHopTable is used to indicate the
          hops for an Gmpls tunnel defined in GmplsTunnelTable,
          as reported by the Gmpls signalling protocol. Thus at
          a transit LSR, this table (if the table is supported
          and if the signaling protocol is recording actual
          route information) contains the actual route of the
          whole tunnel. If the signaling protocol is not
          recording the actual route, this table MAY report
          the information from the GmplsTunnelHopTable or the
          GmplsTunnelCHopTable.

         Each row in this table is indexed by
          GmplsTunnelARHopListIndex. Each row also has a
          secondary index GmplsTunnelARHopIndex, corresponding
          to the next hop that this row corresponds to.

         Please note that since the information necessary to
          build entries within this table is not provided by
          some Gmpls signalling protocols, implementation of
          this table is optional. Furthermore, since the
          information in this table is actually provided by
          the Gmpls signalling protocol after the path has
          been set-up, the entries in this table are provided
          only for observation, and hence, all variables in
          this table are accessible exclusively as read-
          only.

         Note also that the contencts of this table may change
          while it is being read because of re-routing
          activities. A network administrator may verify that
          the actual route read is consistent by reference to
          the GmplsTunnelLastPathChange object."
     ::= { cienaCesGmpls 14 }
     
 cienaCesGmplsTunnelARHopEntry  OBJECT-TYPE
     SYNTAX       CienaCesGmplsTunnelARHopEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in this table represents a tunnel hop.  An
          entry is created by the agent for signaled ERLSP
          set up by an Gmpls signalling protocol."
      INDEX { cienaGmplsTunnelARHopListIndex, cienaGmplsTunnelARHopIndex }     
      ::= { cienaCesGmplsTunnelARHopTable 1 }
     
 CienaCesGmplsTunnelARHopEntry ::=  SEQUENCE { 
     cienaGmplsTunnelARHopListIndex                 	Unsigned32,  
     cienaGmplsTunnelARHopIndex                    	Unsigned32,    
     cienaGmplsTunnelARHopAddrType		        INTEGER	,         	  
     cienaGmplsTunnelARHopIpAddr          	       	IpAddress,
     cienaGmplsTunnelARHopAddrUnnum          	    Unsigned32,
     cienaGmplsTunnelARHopLspId              	    Unsigned32
     }
 
cienaGmplsTunnelARHopListIndex OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
        "Primary index into this table identifying a
          particular recorded hop list."
   ::= { cienaCesGmplsTunnelARHopEntry 1 }

cienaGmplsTunnelARHopIndex OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
        "Secondary index into this table identifying the
          particular hop."
   ::= { cienaCesGmplsTunnelARHopEntry 2 }

cienaGmplsTunnelARHopAddrType OBJECT-TYPE
   SYNTAX       INTEGER 
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "The Hop Address Type of this tunnel hop.

         Note that lspid(5) is a valid option only
         for tunnels signaled via CRLDP."
   ::= { cienaCesGmplsTunnelARHopEntry 3 }

cienaGmplsTunnelARHopIpAddr OBJECT-TYPE
   SYNTAX        IpAddress
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "The Tunnel Hop Address for this tunnel hop.

         The type of this address is determined by the
         value of the corresponding GmplsTunnelARHopAddrType.
         
         If GmplsTunnelARHopAddrType is set to unnum(4),
          then this value contains the LSR Router ID of the
          unnumbered interface. Otherwise the agent SHOULD
          set this object to the zero-length string and the
          manager should ignore this object."
::= { cienaCesGmplsTunnelARHopEntry 4 }

cienaGmplsTunnelARHopAddrUnnum OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "If GmplsTunnelARHopAddrType is set to unnum(4), then
          this value will contain the interface identifier of
          the unnumbered interface for this hop. This object
          should be used in conjunction with
          GmplsTunnelARHopIpAddr which would contain the LSR
          Router ID in this case. Otherwise the agent should
          set this object to zero-length string and the
          manager should ignore this."
   ::= { cienaCesGmplsTunnelARHopEntry 5 }

cienaGmplsTunnelARHopLspId OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "If GmplsTunnelARHopAddrType is set to lspid(5), then
          this value will contain the LSP ID of this hop.
          This object is otherwise insignificant and should
          contain a value of 0 to indicate this fact."
   ::= { cienaCesGmplsTunnelARHopEntry 6 }
  
                          
   --
 --   Extension to the GMPLS Tunnel table
   --
   --

 
 cienaCesGmplsEncapTunnelNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 15 }
 
 cienaCesGmplsNotifEncapTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifEncapTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Table of notification objects required for encap tunnel notification."
     ::= { cienaCesGmplsEncapTunnelNotif 1 }
 
  cienaCesGmplsNotifEncapTunnelEntry OBJECT-TYPE
    SYNTAX     CienaCesGmplsNotifEncapTunnelEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesGmplsNotifEncapTunnelTable.
	     No Get, GetNext or Set is allowed on this table."
    INDEX      { cienaCesGmplsNotifEncapTunnelType, cienaCesGmplsNotifEncapTunnelIndex}
    ::= { cienaCesGmplsNotifEncapTunnelTable 1 }  

 CienaCesGmplsNotifEncapTunnelEntry ::= SEQUENCE {
    cienaCesGmplsNotifEncapTunnelIndex Unsigned32,
    cienaCesGmplsNotifEncapTunnelType	INTEGER,
    cienaCesGmplsNotifEncapTunnelName   DisplayString,
    cienaCesGmplsNotifEncapTunnelAdminState CienaGlobalState,
    cienaCesGmplsNotifEncapTunnelOperState     CienaGlobalState,
    cienaCesGmplsNotifEncapTunnelAisFaulted    TunnelAisFault,
    cienaCesGmplsNotifEncapTunnelFaultedNodeId IpAddress,
    cienaCesGmplsNotifEncapTunnelFarEndLerId IpAddress,
    cienaCesGmplsNotifEncapTunnelResult INTEGER,
    cienaCesGmplsNotifEncapTunnelProtectionRole INTEGER,
    cienaCesGmplsNotifEncapTunnelRequestedBw Unsigned32,
    cienaCesGmplsNotifEncapTunnelOperationalBw Unsigned32,
    cienaCesGmplsNotifEncapTunnelMbbParentApp INTEGER,
    cienaCesGmplsNotifEncapTunnelOamFaulted   TunnelOamFault
 }
 

 cienaCesGmplsNotifEncapTunnelIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel index of the encap tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 1 } 

 cienaCesGmplsNotifEncapTunnelType   OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
          dynamic(2)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Encap tunnel type of the encap tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 2 } 
  
  cienaCesGmplsNotifEncapTunnelName OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
        "This represents the encap tunnel name." 
     ::= { cienaCesGmplsNotifEncapTunnelEntry 3 } 
 
  cienaCesGmplsNotifEncapTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Administrative state of the encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 4 }  
 
  cienaCesGmplsNotifEncapTunnelOperState OBJECT-TYPE
  	SYNTAX      CienaGlobalState
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "Operational state of the encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 5 } 
    
 cienaCesGmplsNotifEncapTunnelAisFaulted OBJECT-TYPE
     SYNTAX      TunnelAisFault
     MAX-ACCESS accessible-for-notify
     STATUS     obsolete
     DESCRIPTION
            "This is replaced by cienaCesGmplsNotifOamTunnelAisFaulted."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 6 } 
    
 cienaCesGmplsNotifEncapTunnelFaultedNodeId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "IP address of the node with the AIS fault."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 7 } 

 cienaCesGmplsNotifEncapTunnelFarEndLerId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "IP address of the far end LER for the tunne with the AIS fault."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 8 }
 
 cienaCesGmplsNotifEncapTunnelResult OBJECT-TYPE
     SYNTAX     INTEGER
                        {
                                success(1),
                                fail(2)
                        }
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "specifies the result of encap tunnel auto sizing/MBB."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 9 }

 cienaCesGmplsNotifEncapTunnelProtectionRole OBJECT-TYPE
     SYNTAX     INTEGER
                        {
                                none(0),
                                primary(1),
                                backup(2)
                        }
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "specifies the protection role of the dynamic encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 10 }

 cienaCesGmplsNotifEncapTunnelRequestedBw OBJECT-TYPE
    SYNTAX     Unsigned32 (1..65535)
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "specifies the requested bandwidth of the dynamic encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 11 }

  cienaCesGmplsNotifEncapTunnelOperationalBw OBJECT-TYPE
    SYNTAX     Unsigned32 (1..65535)
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "specifies the operational bandwidth of the dynamic encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 12 }

  cienaCesGmplsNotifEncapTunnelMbbParentApp OBJECT-TYPE
    SYNTAX     INTEGER
                       {
                                none(1),
                                autosize(2),
                                lspreoptimization(3)
                       }
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "specifies the parent application using MBB of the dynamic encap tunnel."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 13 }
 
 cienaCesGmplsNotifEncapTunnelOamFaulted OBJECT-TYPE
     SYNTAX      TunnelOamFault
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Indication if the tunnel has Oam fault."
     ::= { cienaCesGmplsNotifEncapTunnelEntry 14 } 
 
 cienaCesGmplsDecapTunnelNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 16 }
 
 cienaCesGmplsNotifDecapTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifDecapTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current 
     DESCRIPTION
         "Table of notification objects required for decap tunnel notification."
     ::= { cienaCesGmplsDecapTunnelNotif 1 }
 
 cienaCesGmplsNotifDecapTunnelEntry OBJECT-TYPE
     SYNTAX     CienaCesGmplsNotifDecapTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current 
     DESCRIPTION
         "An entry (conceptual row) in the cienaCesGmplsNotifDecapTunnelTable.
         No Get, GetNext or Set is allowed on this table." 
     INDEX      { cienaCesGmplsNotifDecapTunnelType, cienaCesGmplsNotifDecapTunnelIndex}
     ::= { cienaCesGmplsNotifDecapTunnelTable 1 }  

 CienaCesGmplsNotifDecapTunnelEntry ::= SEQUENCE {
     cienaCesGmplsNotifDecapTunnelIndex         Unsigned32,
     cienaCesGmplsNotifDecapTunnelType          INTEGER,
     cienaCesGmplsNotifDecapTunnelName          DisplayString,
     cienaCesGmplsNotifDecapTunnelAdminState    CienaGlobalState,
     cienaCesGmplsNotifDecapTunnelOperState     CienaGlobalState,
     cienaCesGmplsNotifDecapTunnelAisFaulted    TunnelAisFault,
     cienaCesGmplsNotifDecapTunnelFaultedNodeId IpAddress,
     cienaCesGmplsNotifDecapTunnelFarEndLerId   IpAddress,
     cienaCesGmplsNotifDecapTunnelOamFaulted    TunnelOamFault
 }
 

 cienaCesGmplsNotifDecapTunnelIndex     OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current 
     DESCRIPTION
         "Tunnel index of the decap tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 1 } 

 cienaCesGmplsNotifDecapTunnelType   OBJECT-TYPE
     SYNTAX    INTEGER 
     {       
         static(1),
         dynamic(2)
     }       
        
     MAX-ACCESS accessible-for-notify
     STATUS     current 
     DESCRIPTION
         "Decap tunnel type of the decap tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 2 } 
 
 cienaCesGmplsNotifDecapTunnelName OBJECT-TYPE
     SYNTAX     DisplayString
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "This represents the decap tunnel name."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 3 }

 cienaCesGmplsNotifDecapTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Administrative state of the decap tunnel."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 4 }

 cienaCesGmplsNotifDecapTunnelOperState OBJECT-TYPE
     SYNTAX     CienaGlobalState
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Operational state of the decap tunnel."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 5 }

 cienaCesGmplsNotifDecapTunnelAisFaulted OBJECT-TYPE
     SYNTAX      TunnelAisFault
     MAX-ACCESS accessible-for-notify
     STATUS     obsolete
     DESCRIPTION
         "This is replaced by cienaCesGmplsNotifDecapTunnelOamFaulted."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 6 } 
    
 cienaCesGmplsNotifDecapTunnelFaultedNodeId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "IP address of the node with the AIS fault."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 7 } 

 cienaCesGmplsNotifDecapTunnelFarEndLerId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "IP address of the far end LER for the tunne with the AIS fault."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 8 } 
 cienaCesGmplsNotifDecapTunnelOamFaulted OBJECT-TYPE
     SYNTAX      TunnelOamFault
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Indication if the tunnel has Oam fault."
     ::= { cienaCesGmplsNotifDecapTunnelEntry 9 } 

 cienaCesGmplsTransitTunnelNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 17 }
 
 cienaCesGmplsNotifTransitTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifTransitTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for transit tunnel
         notification."
     ::= { cienaCesGmplsTransitTunnelNotif 1 }
 
  cienaCesGmplsNotifTransitTunnelEntry OBJECT-TYPE
    SYNTAX     CienaCesGmplsNotifTransitTunnelEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesGmplsNotifTransitTunnelTable.
	     No Get, GetNext or Set is allowed on this table."
     INDEX    { cienaCesGmplsNotifTransitTunnelType,
                cienaCesGmplsNotifTransitTunnelIndex
              }
    ::= { cienaCesGmplsNotifTransitTunnelTable 1 }

 CienaCesGmplsNotifTransitTunnelEntry ::= SEQUENCE {
    cienaCesGmplsNotifTransitTunnelIndex Unsigned32,
    cienaCesGmplsNotifTransitTunnelType	INTEGER,
    cienaCesGmplsNotifTransitTunnelName   DisplayString,
    cienaCesGmplsNotifTransitTunnelAdminState CienaGlobalState,
    cienaCesGmplsNotifTransitTunnelOperState	CienaGlobalState,
    cienaCesGmplsNotifTransitTunnelOamFaulted   TunnelOamFault
 } 

 cienaCesGmplsNotifTransitTunnelIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel index of the transit tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifTransitTunnelEntry 1 } 

 cienaCesGmplsNotifTransitTunnelType   OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
     				dynamic(2)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Transit tunnel type of the transit tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifTransitTunnelEntry 2 } 
  
  cienaCesGmplsNotifTransitTunnelName OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
        "This represents the transit tunnel name." 
     ::= { cienaCesGmplsNotifTransitTunnelEntry 3 } 
 
  cienaCesGmplsNotifTransitTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Administrative state of the transit tunnel."
     ::= { cienaCesGmplsNotifTransitTunnelEntry 4 }  
 
  cienaCesGmplsNotifTransitTunnelOperState OBJECT-TYPE
  	SYNTAX      CienaGlobalState
    MAX-ACCESS accessible-for-notify
    STATUS     current
    DESCRIPTION
            "Operational state of the transit tunnel."
     ::= { cienaCesGmplsNotifTransitTunnelEntry 5 }      

 cienaCesGmplsNotifTransitTunnelOamFaulted OBJECT-TYPE
      SYNTAX      TunnelOamFault
      MAX-ACCESS accessible-for-notify
      STATUS     current
      DESCRIPTION
             "Indication if the tunnel has Oam fault."
      ::= { cienaCesGmplsNotifTransitTunnelEntry 6 } 

 cienaCesGmplsAssociatedTunnelNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 18 }

 cienaCesGmplsNotifAssociatedTunnelTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for associated tunnel
         notification."
     ::= { cienaCesGmplsAssociatedTunnelNotif 1 }
 
 cienaCesGmplsNotifAssociatedTunnelEntry OBJECT-TYPE
     SYNTAX     CienaCesGmplsNotifAssociatedTunnelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "An entry (conceptual row) in the
         cienaCesGmplsNotifAssociatedTunnelTable.  No Get, GetNext or Set is
         allowed on this table."
     INDEX    { cienaCesGmplsNotifAssociatedTunnelType,
                cienaCesGmplsNotifAssociatedTunnelIndex
              }
     ::= { cienaCesGmplsNotifAssociatedTunnelTable 1 }

 CienaCesGmplsNotifAssociatedTunnelEntry ::= SEQUENCE {
     cienaCesGmplsNotifAssociatedTunnelIndex         Unsigned32,
     cienaCesGmplsNotifAssociatedTunnelType          INTEGER,
     cienaCesGmplsNotifAssociatedTunnelName          DisplayString,
     cienaCesGmplsNotifAssociatedTunnelAdminState    CienaGlobalState,
     cienaCesGmplsNotifAssociatedTunnelOperState     CienaGlobalState,
     cienaCesGmplsNotifAssociatedTunnelAisFaulted    TunnelAisFault,
     cienaCesGmplsNotifAssociatedTunnelFaultedNodeId IpAddress,
     cienaCesGmplsNotifAssociatedTunnelFarEndLerId   IpAddress,
     cienaCesGmplsNotifAssociatedTunnelOamFaulted    TunnelOamFault
 }

 cienaCesGmplsNotifAssociatedTunnelIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Tunnel index of the associated tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 1 }

 cienaCesGmplsNotifAssociatedTunnelType   OBJECT-TYPE
     SYNTAX    INTEGER 
     {
         static(1),
         dynamic(2)
     }

     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Associated tunnel type of the associated tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 2 }

 cienaCesGmplsNotifAssociatedTunnelName OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
         "This represents the associated tunnel name." 
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 3 }

 cienaCesGmplsNotifAssociatedTunnelAdminState   OBJECT-TYPE
     SYNTAX     CienaGlobalState 
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Administrative state of the associated tunnel."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 4 }

 cienaCesGmplsNotifAssociatedTunnelOperState OBJECT-TYPE
     SYNTAX      CienaGlobalState
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Operational state of the associated tunnel."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 5 }

 cienaCesGmplsNotifAssociatedTunnelAisFaulted OBJECT-TYPE
     SYNTAX      TunnelAisFault
     MAX-ACCESS accessible-for-notify
     STATUS     obsolete
     DESCRIPTION
         "Replaced by cienaCesGmplsNotifAssociatedTunnelOamFaulted"
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 6 }

 cienaCesGmplsNotifAssociatedTunnelFaultedNodeId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "IP address of the node with the AIS fault."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 7 }

 cienaCesGmplsNotifAssociatedTunnelFarEndLerId OBJECT-TYPE
     SYNTAX      IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "IP address of the far end LER for the tunne with the AIS fault."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 8 } 

 cienaCesGmplsNotifAssociatedTunnelOamFaulted OBJECT-TYPE
     SYNTAX      TunnelOamFault
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Indication if the tunnel has Oam fault."
     ::= { cienaCesGmplsNotifAssociatedTunnelEntry 9 }


 cienaCesGmplsEncapTunnelGrpNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 19 }

  cienaCesGmplsNotifEncapTunnelGrpTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifEncapTunnelGrpEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for encap tunnel group
         notification."
     ::= { cienaCesGmplsEncapTunnelGrpNotif 1 }

  cienaCesGmplsNotifEncapTunnelGrpEntry OBJECT-TYPE
    SYNTAX     CienaCesGmplsNotifEncapTunnelGrpEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the cienaCesGmplsNotifEncapTunnelGrpTable.
	     No Get, GetNext or Set is allowed on this table."
    INDEX      {cienaCesGmplsNotifEncapTunnelGrpIndex}
    ::= { cienaCesGmplsNotifEncapTunnelGrpTable 1 }    
 
  CienaCesGmplsNotifEncapTunnelGrpEntry ::= SEQUENCE {
    cienaCesGmplsNotifEncapTunnelGrpIndex Unsigned32,
    cienaCesGmplsNotifEncapTunnelGrpName	DisplayString,
    cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlIndex   Unsigned32,
    cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlName DisplayString,
    cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlType	INTEGER
 }

   cienaCesGmplsNotifEncapTunnelGrpIndex	OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Tunnel group index of the encap tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelGrpEntry 1 } 

   cienaCesGmplsNotifEncapTunnelGrpName   OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
            "Tunnel group name of the encap tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelGrpEntry 2 } 
  
  cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlIndex  OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Active Encap Tunnel index of the tunnel group associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelGrpEntry 3 } 
 
  cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlName   OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
            "Active Encap Tunnel name of the Tunnel group associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelGrpEntry 4 } 
  
     
  cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlType  OBJECT-TYPE
     SYNTAX     INTEGER 
     			{
     				static(1), 
         dynamic(2)
     			}
     					
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
            "Encap tunnel type of the encap tunnel associated with the 
            notification."
     ::= { cienaCesGmplsNotifEncapTunnelGrpEntry 5 } 


 cienaCesGmplsDecapTunnelGrpNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 20 }

 cienaCesGmplsNotifDecapTunnelGrpTable    OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifDecapTunnelGrpEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for decap tunnel group
         notification."
     ::= { cienaCesGmplsDecapTunnelGrpNotif 1 }


 cienaCesGmplsNotifDecapTunnelGrpEntry    OBJECT-TYPE
     SYNTAX     CienaCesGmplsNotifDecapTunnelGrpEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "An entry(conceptual row) in the cienaCesGmplsNotifDecapTunnelGrpTable.
         No Get, GetNext or Set is allowed on this table."
     INDEX      {cienaCesGmplsNotifDecapTunnelGrpIndex}
     ::= { cienaCesGmplsNotifDecapTunnelGrpTable 1 }    
 
 CienaCesGmplsNotifDecapTunnelGrpEntry ::= SEQUENCE {
     cienaCesGmplsNotifDecapTunnelGrpIndex                   Unsigned32,
     cienaCesGmplsNotifDecapTunnelGrpName                    DisplayString,
     cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlIndex    Unsigned32,
     cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlName     DisplayString,
     cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlType     INTEGER
 }

 cienaCesGmplsNotifDecapTunnelGrpIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Tunnel group index of the decap tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelGrpEntry 1 }

 cienaCesGmplsNotifDecapTunnelGrpName    OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
         "Tunnel group name of the decap tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelGrpEntry 2 }
  
 cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..65535)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Active Decap Tunnel index of the tunnel group associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelGrpEntry 3 }
 
 cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlName    OBJECT-TYPE
     SYNTAX		 DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
         "Active Decap Tunnel name of the Tunnel group associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelGrpEntry 4 }


 cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlType    OBJECT-TYPE
     SYNTAX    INTEGER
     {
         static(1),
         dynamic(2)
     }

     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Decap tunnel type of the decap tunnel associated with the 
         notification."
     ::= { cienaCesGmplsNotifDecapTunnelGrpEntry 5 }


 cienaCesGmplsTunnelAisFaultErrorNotif OBJECT IDENTIFIER ::= { cienaCesGmpls 21 }

 cienaCesGmplsNotifTunnelAisFaultErrorTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesGmplsNotifTunnelAisFaultErrorEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for tunnel Ais Fault error
          notification."
     ::= { cienaCesGmplsTunnelAisFaultErrorNotif 1 }

 cienaCesGmplsNotifTunnelAisFaultErrorEntry OBJECT-TYPE
     SYNTAX     CienaCesGmplsNotifTunnelAisFaultErrorEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "An entry (conceptual row) in the
         cienaCesGmplsNotifTunnelAisFaultErrorTable No Get, GetNext or Set is
         allowed on this table."
     INDEX      { cienaCesGmplsNotifTunnelDecapLabel}
     ::= { cienaCesGmplsNotifTunnelAisFaultErrorTable 1 }

 CienaCesGmplsNotifTunnelAisFaultErrorEntry ::= SEQUENCE {
     cienaCesGmplsNotifTunnelDecapLabel Unsigned32,
     cienaCesGmplsNotifTunnelErrorMsg   DisplayString
     }

 cienaCesGmplsNotifTunnelDecapLabel OBJECT-TYPE
     SYNTAX     Unsigned32 (16..1048575)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Decap label associated with the notification."
     ::= { cienaCesGmplsNotifTunnelAisFaultErrorEntry 1 } 
  
 cienaCesGmplsNotifTunnelErrorMsg OBJECT-TYPE
     SYNTAX                           DisplayString 
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
        "This represents the fault error message for decap
        label associated with the notification" 
     ::= { cienaCesGmplsNotifTunnelAisFaultErrorEntry 2 }


  --       
 -- TP Tunnel Traps
  --      
 cienaCesGmplsEncapUnidirTunnelOperStateChgTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	     	 cienaGlobalSeverity,  
     			 cienaGlobalMacAddress,
	              cienaCesGmplsNotifEncapTunnelIndex,
	              cienaCesGmplsNotifEncapTunnelType,
	              cienaCesGmplsNotifEncapTunnelName,
	              cienaCesGmplsNotifEncapTunnelAdminState,
	              cienaCesGmplsNotifEncapTunnelOperState,
	              cienaCesGmplsNotifEncapTunnelOamFaulted,
	              cienaCesGmplsNotifEncapTunnelFaultedNodeId
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent when the operational state of an encap
         uni-direction tunnel changes. Variable bindings include: cienaGlobalSeverity, 
	       cienaGlobalMacAddress, cienaCesGmplsNotifEncapTunnelIndex, 
	       cienaCesGmplsNotifEncapTunnelType, cienaCesGmplsNotifEncapTunnelName, 
	       cienaCesGmplsNotifEncapTunnelAdminState,
	       cienaCesGmplsNotifEncapTunnelOperState,
	       cienaCesGmplsNotifEncapTunnelOamFaulted,
	       cienaCesGmplsNotifEncapTunnelFaultedNodeId."   
	::= { cienaCesGmplsMIBNotifications 1 }


 cienaCesGmplsEncapCoroutedTunnelOperStateChgTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	     	 cienaGlobalSeverity,  
     			 cienaGlobalMacAddress,
         cienaCesGmplsNotifEncapTunnelIndex,
         cienaCesGmplsNotifEncapTunnelType,
         cienaCesGmplsNotifEncapTunnelName,
         cienaCesGmplsNotifEncapTunnelAdminState,
         cienaCesGmplsNotifEncapTunnelOperState,
         cienaCesGmplsNotifEncapTunnelOamFaulted,
         cienaCesGmplsNotifEncapTunnelFaultedNodeId
	           }
	              
	STATUS	   current
	DESCRIPTION  
         "This notification is sent when the operational state of an encap
         corouted tunnel changes. Variable bindings include: cienaGlobalSeverity,
         cienaGlobalMacAddress, cienaCesGmplsNotifEncapTunnelIndex,
         cienaCesGmplsNotifEncapTunnelType, cienaCesGmplsNotifEncapTunnelName,
         cienaCesGmplsNotifEncapTunnelAdminState,
         cienaCesGmplsNotifEncapTunnelOperState,
         cienaCesGmplsNotifEncapTunnelOamFaulted,
         cienaCesGmplsNotifEncapTunnelFaultedNodeId."
	::= { cienaCesGmplsMIBNotifications 2 } 
	
     
 cienaCesGmplsDecapCoroutedTunnelOperStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifDecapTunnelIndex,
         cienaCesGmplsNotifDecapTunnelType,
         cienaCesGmplsNotifDecapTunnelName,
         cienaCesGmplsNotifDecapTunnelAdminState,
         cienaCesGmplsNotifDecapTunnelOperState,
         cienaCesGmplsNotifDecapTunnelOamFaulted,
         cienaCesGmplsNotifDecapTunnelFaultedNodeId
     }

     STATUS    current
     DESCRIPTION
         "This notification is sent when the operational state of an decap
         corouted tunnel changes. Variable bindings include: cienaGlobalSeverity,
         cienaGlobalMacAddress, cienaCesGmplsNotifDecapTunnelIndex,
         cienaCesGmplsNotifDecapTunnelType, cienaCesGmplsNotifDecapTunnelName,
         cienaCesGmplsNotifDecapTunnelAdminState,
         cienaCesGmplsNotifDecapTunnelOperState,
         cienaCesGmplsNotifDecapTunnelOamFaulted,
         cienaCesGmplsNotifDecapTunnelFaultedNodeId."
     ::= { cienaCesGmplsMIBNotifications 3 }


 cienaCesGmplsTransitUnidirTunnelOperStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifTransitTunnelIndex,
         cienaCesGmplsNotifTransitTunnelType,
         cienaCesGmplsNotifTransitTunnelName,
         cienaCesGmplsNotifTransitTunnelAdminState,
         cienaCesGmplsNotifTransitTunnelOperState,
         cienaCesGmplsNotifTransitTunnelOamFaulted
     }

     STATUS    current
     DESCRIPTION
         "This notification is sent when the operational state of a transit
         uni-direcition tunnel changes. Variable bindings include: cienaGlobalSeverity,
         cienaGlobalMacAddress, cienaCesGmplsNotifTransitTunnelIndex,
         cienaCesGmplsNotifTransitTunnelType, cienaCesGmplsNotifTransitTunnelName,
         cienaCesGmplsNotifTransitTunnelAdminState,
         cienaCesGmplsNotifTransitTunnelOperState,
         cienaCesGmplsNotifTransitTunnelOamFaulted."
     ::= { cienaCesGmplsMIBNotifications 4 }


 cienaCesGmplsTransitCoroutedTunnelOperStateChgTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	      cienaGlobalSeverity,  
     			  cienaGlobalMacAddress,
	              cienaCesGmplsNotifTransitTunnelIndex,
	              cienaCesGmplsNotifTransitTunnelType,
	              cienaCesGmplsNotifTransitTunnelName,
	              cienaCesGmplsNotifTransitTunnelAdminState,
	              cienaCesGmplsNotifTransitTunnelOperState,
	              cienaCesGmplsNotifTransitTunnelOamFaulted
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent when the operational state of a transit
         corouted tunnel changes. Variable bindings include: cienaGlobalSeverity,
	       cienaGlobalMacAddress, cienaCesGmplsNotifTransitTunnelIndex,
	       cienaCesGmplsNotifTransitTunnelType, cienaCesGmplsNotifTransitTunnelName,
         cienaCesGmplsNotifTransitTunnelAdminState,
         cienaCesGmplsNotifTransitTunnelOperState,
	 cienaCesGmplsNotifTransitTunnelOamFaulted."
     ::= { cienaCesGmplsMIBNotifications 5 }


 cienaCesGmplsEncapUnidirTunnelGrpActiveEncapTunnelChangeTrap  NOTIFICATION-TYPE
     OBJECTS    {  
         cienaGlobalSeverity,  
         cienaGlobalMacAddress,
         cienaCesGmplsNotifEncapTunnelGrpIndex,
         cienaCesGmplsNotifEncapTunnelGrpName,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlIndex,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlName,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlType 
     }

     STATUS    current
     DESCRIPTION
         "This notification is sent whenever the active encap tunnel in 
         an encap uni-direction tunnel group changes."  
     ::= { cienaCesGmplsMIBNotifications 6 }


 cienaCesGmplsEncapCoroutedTunnelGrpActiveEncapTunnelChangeTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifEncapTunnelGrpIndex,
         cienaCesGmplsNotifEncapTunnelGrpName,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlIndex,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlName,
         cienaCesGmplsNotifEncapTunnelGrpActiveEncapTunlType
     }

     STATUS    current
     DESCRIPTION
         "This notification is sent whenever the active encap tunnel in
         an encap corouted tunnel group changes."
     ::= { cienaCesGmplsMIBNotifications 7 }


 cienaCesGmplsDecapCoroutedTunnelGrpActiveDecapTunnelChangeTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifDecapTunnelGrpIndex,
         cienaCesGmplsNotifDecapTunnelGrpName,
         cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlIndex,
         cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlName,
         cienaCesGmplsNotifDecapTunnelGrpActiveDecapTunlType
     }

     STATUS    current
     DESCRIPTION
         "This notification is sent whenever the active decap tunnel in
         a decap corouted tunnel group changes."
     ::= { cienaCesGmplsMIBNotifications 8 }

 cienaCesGmplsAssociatedTunnelOperStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifAssociatedTunnelIndex,
         cienaCesGmplsNotifAssociatedTunnelType,
         cienaCesGmplsNotifAssociatedTunnelName,
         cienaCesGmplsNotifAssociatedTunnelAdminState,
         cienaCesGmplsNotifAssociatedTunnelOperState,
         cienaCesGmplsNotifAssociatedTunnelOamFaulted,
         cienaCesGmplsNotifAssociatedTunnelFaultedNodeId
     }

     STATUS    current
     DESCRIPTION  
         "This notification is sent when the operational state of an encap
         tunnel changes. Variable bindings include: cienaGlobalSeverity,
         cienaGlobalMacAddress, cienaCesGmplsNotifAssociatedTunnelIndex,
         cienaCesGmplsNotifAssociatedTunnelType, cienaCesGmplsNotifAssociatedTunnelName,
         cienaCesGmplsNotifAssociatedTunnelAdminState,
         cienaCesGmplsNotifAssociatedTunnelOperState,
         cienaCesGmplsNotifAssociatedTunnelOamFaulted,
         cienaCesGmplsNotifAssociatedTunnelFaultedNodeId."
     ::= { cienaCesGmplsMIBNotifications 9 }


 cienaCesGmplsEncapCoroutedTunnelAisFaultStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifEncapTunnelIndex,
         cienaCesGmplsNotifEncapTunnelType,
         cienaCesGmplsNotifEncapTunnelName,
         cienaCesGmplsNotifEncapTunnelAisFaulted,
         cienaCesGmplsNotifEncapTunnelFaultedNodeId,
         cienaCesGmplsNotifEncapTunnelFarEndLerId
     }

     STATUS    obsolete
     DESCRIPTION  
         "This notification is obsoleted, its functionality is absorbed in the OperState Change
          notification."  
     ::= { cienaCesGmplsMIBNotifications 10 }


 cienaCesGmplsDecapCoroutedTunnelAisFaultStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    { 
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifDecapTunnelIndex,
         cienaCesGmplsNotifDecapTunnelType,
         cienaCesGmplsNotifDecapTunnelName,
         cienaCesGmplsNotifDecapTunnelAisFaulted,
         cienaCesGmplsNotifDecapTunnelFaultedNodeId,
         cienaCesGmplsNotifDecapTunnelFarEndLerId
     }

     STATUS    obsolete
     DESCRIPTION  
         "This notification is obsoleted, its functionality is absorbed in the OperState Change
          notification."  
     ::= { cienaCesGmplsMIBNotifications 11 }

 cienaCesGmplsAssociatedTunnelAisFaultStateChgTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifAssociatedTunnelIndex,
         cienaCesGmplsNotifAssociatedTunnelType,
         cienaCesGmplsNotifAssociatedTunnelName,
         cienaCesGmplsNotifAssociatedTunnelAisFaulted,
         cienaCesGmplsNotifAssociatedTunnelFaultedNodeId,
         cienaCesGmplsNotifAssociatedTunnelFarEndLerId
     }

     STATUS    obsolete
     DESCRIPTION  
         "This notification is obsoleted, its functionality is absorbed in the OperState Change
          notification."  
     ::= { cienaCesGmplsMIBNotifications 12 }

 cienaCesGmplsTunnelAisFaultErrorTrap  NOTIFICATION-TYPE
     OBJECTS    {  
         cienaGlobalSeverity,  
         cienaGlobalMacAddress,
         cienaCesGmplsNotifTunnelDecapLabel,
         cienaCesGmplsNotifTunnelErrorMsg
     }

     STATUS    current
     DESCRIPTION  
         "This notification is sent when there is a fault sent for a 
         LSP whose label is not associated with any tunnel. This will happen 
         whenever the AIS admin state is disabled for the bidirectional tunnel.
         Variable bindings include: cienaGlobalSeverity, 
         cienaGlobalMacAddress, cienaCesGmplsNotifTunnelDecapLabel, 
         and cienaCesGmplsNotifTunnelErrorMsg"  
     ::= { cienaCesGmplsMIBNotifications 13 }

  cienaCesGmplsEncapTunnelResizeResultTrap  NOTIFICATION-TYPE
	OBJECTS	   {  
	     	      cienaGlobalSeverity,  
     		      cienaGlobalMacAddress,
	              cienaCesGmplsNotifEncapTunnelIndex,
	              cienaCesGmplsNotifEncapTunnelType,
	              cienaCesGmplsNotifEncapTunnelName,
	              cienaCesGmplsNotifEncapTunnelResult,
	              cienaCesGmplsNotifEncapTunnelProtectionRole,
	              cienaCesGmplsNotifEncapTunnelRequestedBw,
	              cienaCesGmplsNotifEncapTunnelOperationalBw
	           }
	              
	STATUS	   current
	DESCRIPTION  
	       "This notification is sent when the encap tunnel auto size is done
               Variable bindings include: cienaGlobalSeverity, 
	       cienaGlobalMacAddress, cienaCesGmplsNotifEncapTunnelIndex, 
	       cienaCesGmplsNotifEncapTunnelType, cienaCesGmplsNotifEncapTunnelName, 
	       cienaCesGmplsNotifEncapTunnelResult,
               cienaCesGmplsNotifEncapTunnelProtectionRole, 
	       cienaCesGmplsNotifEncapTunnelRequestdBw and
               cienaCesGmplsNotifEncapTunnelOperationalBw."   
	::= { cienaCesGmplsMIBNotifications 14 }      
              
 cienaCesGmplsEncapTunnelMbbResultTrap  NOTIFICATION-TYPE
     OBJECTS    {
         cienaGlobalSeverity,
         cienaGlobalMacAddress,
         cienaCesGmplsNotifEncapTunnelIndex,
         cienaCesGmplsNotifEncapTunnelType,
         cienaCesGmplsNotifEncapTunnelName,
         cienaCesGmplsNotifEncapTunnelResult,
         cienaCesGmplsNotifEncapTunnelProtectionRole,
         cienaCesGmplsNotifEncapTunnelRequestedBw,
         cienaCesGmplsNotifEncapTunnelOperationalBw,
         cienaCesGmplsNotifEncapTunnelMbbParentApp
     }

     STATUS     current
     DESCRIPTION
               "This notification is sent when the encap tunnel MBB done
               Variable bindings include: cienaGlobalSeverity,
               cienaGlobalMacAddress, cienaCesGmplsNotifEncapTunnelIndex,
               cienaCesGmplsNotifEncapTunnelType, cienaCesGmplsNotifEncapTunnelName,
               cienaCesGmplsNotifEncapTunnelResult,
               cienaCesGmplsNotifEncapTunnelProtectionRole,
               cienaCesGmplsNotifEncapTunnelRequestdBw and
               cienaCesGmplsNotifEncapTunnelOperationalBw,
               cienaCesGmplsNotifEncapTunnelMbbParentApp."
     ::= { cienaCesGmplsMIBNotifications 15 }
 --
 -- Mpls Global 
 --                                                           
  cienaCesMplsAttrs  OBJECT IDENTIFIER ::= { cienaCesMplsGlobal 1 } 
 
  cienaCesMplsGlobalStaticAdminLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static administrative label range. There cannot be any 
          overlap between static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 1 }
 
 cienaCesMplsGlobalStaticAdminLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static administrative label range. There cannot be any overlap between
          static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 2 }
 
 cienaCesMplsGlobalStaticOperLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 3 }
 
 cienaCesMplsGlobalStaticOperLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 4 }

 
 cienaCesMplsGlobalDynamicAdminLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The beginning of the dynamic administrative label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 5 }

 cienaCesMplsGlobalDynamicAdminLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The end of the dynamic administrative label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 6 }
 
 cienaCesMplsGlobalDynamicOperLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The beginning of the dynamic operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 7 }

 cienaCesMplsGlobalDynamicOperLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The end of the dynamic operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 8 }     
     
  cienaCesMplsGlobalStaticAdminTunnelLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static tunnel administrative label range. There cannot be any 
          overlap between static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 9 }
 
 cienaCesMplsGlobalStaticAdminTunnelLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static tunnel administrative label range. There cannot be any overlap between
          static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 10 }
 
 cienaCesMplsGlobalStaticOperTunnelLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static tunnel operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 11 }
 
 cienaCesMplsGlobalStaticOperTunnelLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static tunnel operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 12 }
 
  cienaCesMplsGlobalStaticAdminVcLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static VC administrative label range. There cannot be any 
          overlap between static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 13 }
 
 cienaCesMplsGlobalStaticAdminVcLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static VC administrative label range. There cannot be any overlap between
          static and dynamic label ranges.
          Setting this object does not become effective until the device is rebooted."
     ::= { cienaCesMplsAttrs 14 }
 
 cienaCesMplsGlobalStaticOperVcLabelRangeStart OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The beginning of the static VC operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 15 }
 
 cienaCesMplsGlobalStaticOperVcLabelRangeEnd OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        " The end of the static VC operational label range. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 16 }

 cienaCesMplsGlobalNextFreeStaticVcLabel OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The next free static VC label. There cannot be any overlap between
          static and dynamic label ranges."
     ::= { cienaCesMplsAttrs 17 }
     
 --
 -- Mpls Global Cos profile
 --     
     
 cienaCesMplsTunnelCosProfileTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsTunnelCosProfileEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Mpls cos-profile table."
     ::= { cienaCesMplsGlobal 2 }
     
 cienaCesMplsTunnelCosProfileEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsTunnelCosProfileEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "Mpls cos-profile table entry."
     INDEX {cienaCesMplsTunnelCosProfileIndex}
     ::= { cienaCesMplsTunnelCosProfileTable 1 }
     
 CienaCesMplsTunnelCosProfileEntry ::=  SEQUENCE { 
     cienaCesMplsTunnelCosProfileIndex              Unsigned32,
     cienaCesMplsTunnelCosProfileName		DisplayString,
     cienaCesMplsTunnelFrmCosPolicy		FCosPolicy,
     cienaCesMplsTunnelFrmCosMapId		INTEGER,
     cienaCesMplsTunnelFrmCosMapName	DisplayString,
     cienaCesMplsTunnelFixedTC			Unsigned32,
     cienaCesMplsTunnelRcosPolicy		RCosPolicy,
     cienaCesMplsTunnelRcosMapName		DisplayString,
     cienaCesMplsTunnelRCosMapId		INTEGER,
     cienaCesMplsTunnelRcosProfileName  DisplayString,  
     cienaCesMplsTunnelRCosProfileId    Unsigned32
                                      
     --cienaCesGmplsTunnelPathHopRowStatus		RowStatus
 }
     
-- cienaCesMplsTunnelCosProfile
--  OBJECT IDENTIFIER ::= { cienaCesMplsGlobal 2 } 
  
  cienaCesMplsTunnelCosProfileIndex		OBJECT-TYPE  
  	 SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index for Mpls tunnel Cos profile."
     ::= { cienaCesMplsTunnelCosProfileEntry 1 }
  
  cienaCesMplsTunnelCosProfileName		OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls tunnel Cos profile name"
     ::= { cienaCesMplsTunnelCosProfileEntry 2 }  
   
  cienaCesMplsTunnelFrmCosPolicy	OBJECT-TYPE
 	 SYNTAX		FCosPolicy  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"Frame CoS policy of the tunnel."
 	 DEFVAL { rcos-mapped }
 	 ::= { cienaCesMplsTunnelCosProfileEntry 3 }
   
  cienaCesMplsTunnelFrmCosMapId    OBJECT-TYPE
 	 SYNTAX		INTEGER   (1..65535)
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The frame CoS map ID of  tunnel. This value is valid if the 
 	 	frame CoS policy selected for the  tunnel is rcos-mapped."
 	 ::= {cienaCesMplsTunnelCosProfileEntry 4 }  
 	
  cienaCesMplsTunnelFrmCosMapName		OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls tunnel frame cos Map name"
     ::= { cienaCesMplsTunnelCosProfileEntry 5 }	 
 
  cienaCesMplsTunnelFixedTC      OBJECT-TYPE
 	 SYNTAX		Unsigned32  (0..7 )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"If the frame CoS policy for the tunnel is fixed, this value is used 
 		 for MPLS encapsulation." 
 	 DEFVAL { 0 }
 	 ::= { cienaCesMplsTunnelCosProfileEntry 6 }
	                                                                                           
  cienaCesMplsTunnelRcosPolicy	   OBJECT-TYPE
      SYNTAX		RCosPolicy  
      MAX-ACCESS  read-only
      STATUS		 current
      DESCRIPTION
      "Resolved CoS policy of the tunnel."
      DEFVAL { exp-mapped }
      ::= { cienaCesMplsTunnelCosProfileEntry 7 } 
      
  cienaCesMplsTunnelRcosMapName		OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls tunnel resolved cos Map name. This object is valid only for 39XX and
	 51XX platforms"
     ::= { cienaCesMplsTunnelCosProfileEntry 8 }      
      
  cienaCesMplsTunnelRCosMapId OBJECT-TYPE
      SYNTAX		INTEGER   (1..65535)
      MAX-ACCESS	read-only
      STATUS		current
      DESCRIPTION
      "The resolved CoS map ID of the tunnel. This value is valid if the 
      resolved CoS policy selected for the tunnel is rcos-mapped.
      This object is valid only for 39XX and 51XX platforms"
      ::= { cienaCesMplsTunnelCosProfileEntry 9 }  
      
 cienaCesMplsTunnelRcosProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Mpls Tunnel resolved cos Profile name.
	 This object is valid only for 87XX platforms"
     ::= { cienaCesMplsTunnelCosProfileEntry 10 }      
      
 cienaCesMplsTunnelRCosProfileId OBJECT-TYPE
     SYNTAX		Unsigned32   (1..65535)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
	 "The resolved Cos profile ID of the Tunnel. 
     This value is valid if the resolved Cos policy 
     selected for the pseudowire is rcos-mapped.
     This object is valid only for 87XX platforms"
     DEFVAL { 1 }
     ::= { cienaCesMplsTunnelCosProfileEntry 11 }



 --
 -- MPLS tunnel Path table
 --
 cienaCesMplsGlobalTunnelPath	OBJECT IDENTIFIER	::= { cienaCesMplsGlobal 3 }

 cienaCesMplsGlobalTunnelPathTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsGlobalTunnelPathEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS tunnel paths.
             To create an entry in the tunnel path table, 
             cienaCesMplsGlobalTunnelPathName and cienaCesMplsGlobalTunnelPathRowStatus 
             must both be specified."
     ::= { cienaCesMplsGlobalTunnelPath 1 }
     
 cienaCesMplsGlobalTunnelPathEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsGlobalTunnelPathEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "An entry in the tunnel path table."
     INDEX {cienaCesMplsGlobalTunnelPathIndex}
     ::= { cienaCesMplsGlobalTunnelPathTable 1 }
     
 CienaCesMplsGlobalTunnelPathEntry ::=  SEQUENCE { 
     cienaCesMplsGlobalTunnelPathIndex                 Unsigned32,
     cienaCesMplsGlobalTunnelPathName			DisplayString,
     cienaCesMplsGlobalTunnelPathUseCount		Counter32
 --    cienaCesMplsGlobalTunnelPathRowStatus		RowStatus
    }
    
 cienaCesMplsGlobalTunnelPathIndex	OBJECT-TYPE
  	 SYNTAX		Unsigned32
  	 MAX-ACCESS	not-accessible
  	 STATUS		current
  	 DESCRIPTION
  	 	"A unique index in the path entry table."
  	 ::= { cienaCesMplsGlobalTunnelPathEntry 1 }

 cienaCesMplsGlobalTunnelPathName 	OBJECT-TYPE
 	 SYNTAX		DisplayString (SIZE ( 1..31) )
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Name associated with the path."
 	 ::= { cienaCesMplsGlobalTunnelPathEntry 2 }
 	 
 cienaCesMplsGlobalTunnelPathUseCount	  OBJECT-TYPE
 	 SYNTAX		Counter32
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"Indicates the number of encap tunnels that are using this path."
 	 ::= { cienaCesMplsGlobalTunnelPathEntry 3 }
  
-- cienaCesMplsGlobalTunnelPathRowStatus   OBJECT-TYPE
-- 	 SYNTAX		RowStatus
 --	 MAX-ACCESS	read-only
 --	 STATUS		current
 --	 DESCRIPTION
 --	 	"Setting this object to 'createAndGo' will create the entry 
 --		in the table. Setting this object to 'destroy' will delete 
 --		the entry from the table."
 --	 ::= { cienaCesMplsGlobalTunnelPathEntry 4 }
  
 
 --
 -- MPLS tunnel Path Hop table
 --

 cienaCesMplsGlobalTunnelPathHopTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsGlobalTunnelPathHopEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS tunnel path hop. 
            To create an entry in the path-hop table, cienaCesMplsGlobalTunnelPathHopIpAddr 
            and cienaCesMplsGlobalTunnelPathHopRowStatus must both be specified."
     ::= { cienaCesMplsGlobalTunnelPath 2 }
     
 cienaCesMplsGlobalTunnelPathHopEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsGlobalTunnelPathHopEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "A tunnel hop entry."
     INDEX {cienaCesMplsGlobalTunnelPathIndex, cienaCesMplsGlobalTunnelPathHopIndex}
     ::= { cienaCesMplsGlobalTunnelPathHopTable 1 }
     
 CienaCesMplsGlobalTunnelPathHopEntry ::=  SEQUENCE { 
     cienaCesMplsGlobalTunnelPathHopIndex              Unsigned32,
     cienaCesMplsGlobalTunnelPathHopIpAddr		IpAddress,
     cienaCesMplsGlobalTunnelPathHopType		INTEGER
 }
 
 cienaCesMplsGlobalTunnelPathHopIndex  OBJECT-TYPE
 	 SYNTAX		Unsigned32
 	 MAX-ACCESS	not-accessible
 	 STATUS		current
 	 DESCRIPTION
 	 	"Unique index in the path hop table."
 	 ::= { cienaCesMplsGlobalTunnelPathHopEntry 1 }
 
 cienaCesMplsGlobalTunnelPathHopIpAddr  OBJECT-TYPE
 	 SYNTAX		IpAddress
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"IP address associated with this hop. This object cannot be modified once the path hop is created."
 	 ::= { cienaCesMplsGlobalTunnelPathHopEntry 2 }
 
 cienaCesMplsGlobalTunnelPathHopType   OBJECT-TYPE
 	 SYNTAX		INTEGER {
 	 					 strict(1),
 	 					 loose(2)            
 	 	}
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
         "Indidcates the hop type of the path member. This object cannot be
         modified once the path hop is created."
 	 DEFVAL	{ strict }
 	  ::= { cienaCesMplsGlobalTunnelPathHopEntry 3 }

 --
 -- Mpls Global Free Static Tunnel Label table
 --     
     
 cienaCesMplsGlobalFreeStaticTunnelLabelTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsGlobalFreeStaticTunnelLabelEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "Mpls Free static Tunnel Label table."
     ::= { cienaCesMplsGlobal 4 }
     
 cienaCesMplsGlobalFreeStaticTunnelLabelEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsGlobalFreeStaticTunnelLabelEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "Mpls Free static Tunnel Label table."
     INDEX {cienaCesMplsGlobalFreeStaticTunnelLabelIndex}
     ::= { cienaCesMplsGlobalFreeStaticTunnelLabelTable 1 }
     
 CienaCesMplsGlobalFreeStaticTunnelLabelEntry ::=  SEQUENCE { 
     cienaCesMplsGlobalFreeStaticTunnelLabelIndex     Unsigned32,
     cienaCesMplsGlobalFreeStaticTunnelLabel          Unsigned32
 }
     
  cienaCesMplsGlobalFreeStaticTunnelLabelIndex       OBJECT-TYPE  
     SYNTAX      Unsigned32 ( 1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index for Mpls Free static Tunnel Label table."
     ::= { cienaCesMplsGlobalFreeStaticTunnelLabelEntry 1 }
  
  cienaCesMplsGlobalFreeStaticTunnelLabel		OBJECT-TYPE
     SYNTAX      Unsigned32 ( 16..1048575)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls Free static Tunnel Label."
     ::= { cienaCesMplsGlobalFreeStaticTunnelLabelEntry 2 }  

--
 -- Mpls TE Link Table
 --     

 cienaCesTeLinkTable OBJECT-TYPE
     SYNTAX        SEQUENCE OF CienaCesTeLinkEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This table captures the TE-link entries along with their 
         respective attributes."
     ::= { cienaCesMplsTe 1 }

 cienaCesTeLinkEntry OBJECT-TYPE
     SYNTAX        CienaCesTeLinkEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This represents an entry for the TE-Link interface"
     INDEX         { cienaCesTeIfIndex }
     ::= { cienaCesTeLinkTable 1 }

 CienaCesTeLinkEntry ::= SEQUENCE {
     cienaCesTeIfIndex                         Unsigned32,
     cienaCesTeInterfaceName                   DisplayString,
     cienaCesTeLinkMetric                      Unsigned32,
     cienaCesTeResourceColorGroupIndex         Unsigned32,
     cienaCesTeResourceColorBitMask            OCTET STRING,
     cienaCesMplsClassProfIndex                Unsigned32,
     cienaCesTeLinkMode                        TEMode,
     cienaCesTeLinkSrlgCount                   Unsigned32,
     cienaCesTeLinkMaximumBandwidth            MplsBitRate,
     cienaCesTeLinkMaximumReservableBandwidth  MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio0         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio0      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio0    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio1         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio1      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio1    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio2         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio2      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio2    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio3         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio3      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio3    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio4         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio4      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio4    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio5         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio5      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio5    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio6         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio6      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio6    MplsBitRate,
     cienaCesTeLinkTotalBandwidthPrio7         MplsBitRate,
     cienaCesTeLinkReservedBandwidthPrio7      MplsBitRate,
     cienaCesTeLinkUnReservedBandwidthPrio7    MplsBitRate
   }

 cienaCesTeIfIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index for TE-Interface."            
     ::= { cienaCesTeLinkEntry 1 }

 cienaCesTeInterfaceName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the TE Interface name."
     ::= { cienaCesTeLinkEntry 2 }

 cienaCesTeLinkMetric  OBJECT-TYPE
     SYNTAX         Unsigned32    (1..4294967295)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Link cost of the TE-Interface."
     DEFVAL { 1 }
     ::= { cienaCesTeLinkEntry 3 }
              
 cienaCesTeResourceColorGroupIndex  OBJECT-TYPE
     SYNTAX         Unsigned32    (1..64)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Resource Color Group Index this TE-link is associated to."
     ::= { cienaCesTeLinkEntry 4 }
              
 cienaCesTeResourceColorBitMask OBJECT-TYPE
     SYNTAX      OCTET STRING (SIZE(4))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents individual bit for Resource Color which will be 
         set in Resource Color Group."

     ::= { cienaCesTeLinkEntry 5 }
 cienaCesMplsClassProfIndex  OBJECT-TYPE
     SYNTAX         Unsigned32    (1..500)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Class Profile Index this TE-link is associated to."
     ::= { cienaCesTeLinkEntry 6 }

 cienaCesTeLinkMode  OBJECT-TYPE
     SYNTAX         TEMode 
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "TE mode of the TE-Interface."
     ::= { cienaCesTeLinkEntry 7 }

 cienaCesTeLinkSrlgCount  OBJECT-TYPE
     SYNTAX         Unsigned32 
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "This identifies the total number of SRLGs configured on 
                    this TE link."
     ::= { cienaCesTeLinkEntry 8 }
              
 cienaCesTeLinkMaximumBandwidth  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Maximum TE-link Bandwidth(in kbps).
                   This is same as the port bandwidth on which this TE-link
                   is created"
     ::= { cienaCesTeLinkEntry 9 }   
   
 cienaCesTeLinkMaximumReservableBandwidth  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Maximum Reservable bandwidth(in kbps) on this TE-link."
     ::= { cienaCesTeLinkEntry 10 }

 cienaCesTeLinkTotalBandwidthPrio0  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 0 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 11 }
             
 cienaCesTeLinkReservedBandwidthPrio0  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 0 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 12 }
            
 cienaCesTeLinkUnReservedBandwidthPrio0  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 0 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 13 }
            
 cienaCesTeLinkTotalBandwidthPrio1  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 1 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 14 }
            
 cienaCesTeLinkReservedBandwidthPrio1  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 1 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 15 }
              
 cienaCesTeLinkUnReservedBandwidthPrio1  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 1 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 16 }
            
 cienaCesTeLinkTotalBandwidthPrio2  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 2 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 17 }
              
 cienaCesTeLinkReservedBandwidthPrio2  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 2 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 18 }
              
 cienaCesTeLinkUnReservedBandwidthPrio2  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 2 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 19 }
            
 cienaCesTeLinkTotalBandwidthPrio3  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 3 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 20 }
            
 cienaCesTeLinkReservedBandwidthPrio3  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 3 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 21 }
            
 cienaCesTeLinkUnReservedBandwidthPrio3  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 3 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 22 }
            
 cienaCesTeLinkTotalBandwidthPrio4  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 4 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 23 }
              
 cienaCesTeLinkReservedBandwidthPrio4  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 4 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 24 }
              
 cienaCesTeLinkUnReservedBandwidthPrio4  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 4 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 25 }
            
 cienaCesTeLinkTotalBandwidthPrio5  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 5 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 26 }
            
 cienaCesTeLinkReservedBandwidthPrio5  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 5 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 27 }
              
 cienaCesTeLinkUnReservedBandwidthPrio5  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 5 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 28 }
            
 cienaCesTeLinkTotalBandwidthPrio6  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 6 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 29 }
              
 cienaCesTeLinkReservedBandwidthPrio6  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 6 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 30 }
              
 cienaCesTeLinkUnReservedBandwidthPrio6  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 6 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 31 }
            
 cienaCesTeLinkTotalBandwidthPrio7  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Total Priority 7 Bandwidth(in kbps) allocated in this TE-link."
     ::= { cienaCesTeLinkEntry 32 }
            
 cienaCesTeLinkReservedBandwidthPrio7  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "Reserved Priority 7 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 33 }

 cienaCesTeLinkUnReservedBandwidthPrio7  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..1000000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
                   "UnReserved Priority 7 Bandwidth(in kbps) in this TE-link."
     ::= { cienaCesTeLinkEntry 34 }
            

--
 -- Mpls Resource Color group Table
 --     

 cienaCesTeResourceGrpTable  OBJECT-TYPE
     SYNTAX        SEQUENCE OF CienaCesTeResGrpEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This table captures the Resource Color Group Template entries."
     ::= { cienaCesMplsTe 2 }

 cienaCesTeResGrpEntry OBJECT-TYPE
     SYNTAX        CienaCesTeResGrpEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "An entry in this table exists for each of the Resource
         color group template."
     INDEX         { cienaCesTeResourceColorGrpIndex }
     ::= { cienaCesTeResourceGrpTable 1 }

 CienaCesTeResGrpEntry ::= SEQUENCE {
     cienaCesTeResourceColorGrpIndex           Unsigned32,
     cienaCesTeResourceGrpName                 DisplayString,
     cienaCesTeResourceColorGroupBitMask       OCTET STRING,
     cienaCesTeResourceColorGroupUseCount      Counter32
   }

 cienaCesTeResourceColorGrpIndex  OBJECT-TYPE
     SYNTAX      Unsigned32 (0..64)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index for Resource Color Group entry.
         Index 0 is used to get the colors which are not associated to 
         any color group cienaCesTeResourceColorsTable"
     ::= { cienaCesTeResGrpEntry 1 } 

 cienaCesTeResourceGrpName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the name of the Resource Color Group."
     ::= { cienaCesTeResGrpEntry 2 }

 cienaCesTeResourceColorGroupBitMask  OBJECT-TYPE
     SYNTAX      OCTET STRING (SIZE(4))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the bitmask value for colors added to 
         this Resource Color Group."
     ::= { cienaCesTeResGrpEntry 3 }

 cienaCesTeResourceColorGroupUseCount  OBJECT-TYPE
     SYNTAX      Counter32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates the number of Tunnels using this MPLS Resource Color 
         Group."
     ::= { cienaCesTeResGrpEntry 4 }


 -- End of Mpls Resource Color group Table


 -- MPLS Resource Color Table

 cienaCesTeResourceColorsTable  OBJECT-TYPE
     SYNTAX        SEQUENCE OF CienaCesTeResColorEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This table captures the Resource Colors entries.
         These colors can be associated to any Resource Color Group"
     ::= { cienaCesMplsTe 3 }

 cienaCesTeResColorEntry OBJECT-TYPE
     SYNTAX        CienaCesTeResColorEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "An entry in this table exists for each of the Resource
         colors configured along with its associated Resource Group."
     INDEX         { cienaCesTeResourceColorGrpIndex, cienaCesTeResourceColorIndex }
     ::= { cienaCesTeResourceColorsTable 1 }

 CienaCesTeResColorEntry ::= SEQUENCE {
     cienaCesTeResourceColorIndex              Unsigned32,
     cienaCesTeResourceColorName               DisplayString,
     cienaCesTeResourceColorBit                OCTET STRING,
     cienaCesTeResourceColorUseCount           Counter32
   }
   
 cienaCesTeResourceColorIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..32)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This represents the unique index for Resource Color entry."            
     ::= { cienaCesTeResColorEntry 1 }

 cienaCesTeResourceColorName OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..31))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents the name of the Resource Color."
     ::= { cienaCesTeResColorEntry 2 }

 cienaCesTeResourceColorBit OBJECT-TYPE
     SYNTAX      OCTET STRING (SIZE(4))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "This represents individual bit for Resource Color which will be 
         set in Resource Color Group's cienaCesTeResourceColorGroupBitMask."
     ::= { cienaCesTeResColorEntry 3 }

  cienaCesTeResourceColorUseCount OBJECT-TYPE
     SYNTAX      Counter32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Indicates the number of MPLS Resource Groups using this Resource 
         Color."
     ::= { cienaCesTeResColorEntry 4 }


 -- End of Mpls Resource Color Table


 -- TE Link Shared Risk Link Group Table

 cienaCesTeLinkSrlgTable OBJECT-TYPE
     SYNTAX        SEQUENCE OF CienaCesTeLinkSrlgEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This table specifies the SRLGs associated with TE links."
     ::= { cienaCesMplsTe 4 }

 cienaCesTeLinkSrlgEntry OBJECT-TYPE
     SYNTAX        CienaCesTeLinkSrlgEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "An entry in this table contains information about an
          SRLG associated with a TE link.
          An ifEntry in the ifTable must exist before a
          teLinkSrlgEntry using the same ifIndex is created.
          The ifType of ifEntry must be teLink(200).
          If a TE link entry in the ifTable is destroyed, then so
          are all of the entries in the teLinkSrlgTable that use the
          ifIndex of this TE link."
     INDEX         { cienaCesTeIfIndex, cienaCesTeLinkSrlg }
     ::= { cienaCesTeLinkSrlgTable 1 }

 CienaCesTeLinkSrlgEntry ::= SEQUENCE {
     cienaCesTeLinkSrlg            Unsigned32,
     cienaCesTeLinkSrlgStatus      SRLGState
   }

 cienaCesTeLinkSrlg OBJECT-TYPE
     SYNTAX        Unsigned32 (0..4294967295)
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "This identifies an SRLG supported by the TE link.  An SRLG is
          identified with a 32-bit number that is unique within an IGP
          domain.  Zero is a valid SRLG number."
     REFERENCE
         "OSPF Extensions in Support of Generalized Multi-Protocol
          Label Switching (GMPLS), RFC 4203"
     ::= { cienaCesTeLinkSrlgEntry 1 }

 cienaCesTeLinkSrlgStatus OBJECT-TYPE
     SYNTAX        SRLGState
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
         "This variable represents the SRLG value status. 
          Currently we support only active(1) indicating SRLG
          is configured."
     ::= { cienaCesTeLinkSrlgEntry 2 }

 -- End of teLinkSrlgTable

--
-- MPLS PW table
--

 cienaCesMplsPwTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsPwEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS pseudo wires."

     ::= { cienaCesMplsPw 1 }
     
 cienaCesMplsPwEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsPwEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS pseudowire table."
     INDEX {cienaCesMplsPwIndex}
     ::= { cienaCesMplsPwTable 1 }
     
 CienaCesMplsPwEntry ::=  SEQUENCE {
      cienaCesMplsPwIndex		Unsigned32,
      cienaCesMplsPwSignallingType	INTEGER,
      cienaCesMplsPwId			Unsigned32,
      cienaCesMplsPwName		DisplayString,
      cienaCesMplsPwCustomerName        DisplayString,
      cienaCesMplsPwAdminState          CienaGlobalState,
      cienaCesMplsPwOperState           CienaGlobalState,
      cienaCesMplsPwPeerIpAddr          IpAddress,
      cienaCesMplsPwInLabel             Integer32,
      cienaCesMplsPwOutLabel            Integer32,
      cienaCesMplsPwStatusTlv           INTEGER,
      cienaCesMplsPwRefreshStatusIntvl  Unsigned32,  
      cienaCesMplsPwLocalFault          VCStatus,
      cienaCesMplsPwRemoteFault         VCStatus,
      cienaCesMplsPwMtu                 Unsigned32,
      cienaCesMplsPwType                INTEGER,
      cienaCesMplsPwMode                INTEGER,
      cienaCesMplsPwCoSProfileName	DisplayString,
      cienaCesMplsPwCoSProfileIndex	Unsigned32,
      cienaCesMplsPwEgressL2PtTransform CienaGlobalState,
      cienaCesMplsPwVccVProfileName	DisplayString,
      cienaCesMplsPwVccVProfileIndex	Unsigned32,
      cienaCesMplsPwLocalCcCv		DisplayString,
      cienaCesMplsPwRemoteCcCv		DisplayString,
      cienaCesMplsPwOperatingCcCv	DisplayString,
      cienaCesMplsPwBlocking		INTEGER,
      cienaCesMplsPwVifIndex		Unsigned32,
      cienaCesMplsPwConfigTunnelName    DisplayString,
      cienaCesMplsPwConfigTunnelType	TunnelType,
      cienaCesMplsPwConfigTunnelIndex	Unsigned32,
      cienaCesMplsPwActiveTunnelName    DisplayString,
      cienaCesMplsPwActiveTunnelType	TunnelType,
      cienaCesMplsPwActiveTunnelIndex	Unsigned32,
      cienaCesMplsPwRole		INTEGER,
      cienaCesMplsPwPrimaryPwName	DisplayString,
      cienaCesMplsPwPrimaryPwIndex	Unsigned32,
      cienaCesMplsPwVsIndex		Unsigned32,
      cienaCesServiceDelimiterVID	Unsigned32,
      cienaCesServiceDelimiterTPID	Unsigned32,
      cienaCesMplsPwReversion           INTEGER,
      cienaCesMplsPwRevertTime          Unsigned32,
      cienaCesMplsPwProtectionRole      INTEGER,
      cienaCesMplsPwProtectionState     INTEGER,
      cienaCesMplsPwVsName              DisplayString,
      cienaCesMplsPwStatusQuery         INTEGER,
      cienaCesMplsMsPwPeerPwName        DisplayString,
      cienaCesMplsMsPwPeerPwIndex       Unsigned32,
      cienaCesMplsPwIdInRemoteFault     Unsigned32,
      cienaCesMplsLocalIpInRemoteFault  IpAddress,
      cienaCesMplsRemoteIpInRemoteFault IpAddress,
      cienaCesMplsPwFaultToNextHop      VCStatus,
      cienaCesMplsPwIdInFaultToNextHop  Unsigned32,
      cienaCesMplsLocalIpInFaultToNextHop IpAddress,
      cienaCesMplsRemoteIpInFaultToNextHop IpAddress,
      cienaCesMplsPwConfigBandwidth        MplsBitRate,
      cienaCesMplsPwOperBandwidth          MplsBitRate,
      cienaCesMplsPwBandwidthState         INTEGER
   }
 
 cienaCesMplsPwIndex OBJECT-TYPE
     SYNTAX      Unsigned32   (1..65535)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index in the table."
     ::= { cienaCesMplsPwEntry 1 }

 cienaCesMplsPwSignallingType OBJECT-TYPE
     SYNTAX      INTEGER {
 	                   static(1),
 	                   dynamic(2)
 	         }   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"Specifies Pw signalling type"
     ::= { cienaCesMplsPwEntry 2 }
  
 cienaCesMplsPwId OBJECT-TYPE
     SYNTAX      	Unsigned32 (1..2147483647)
     MAX-ACCESS  	read-only
     STATUS      	current
     DESCRIPTION
        "This represents the unique id that is signalled in LDP"
     ::= { cienaCesMplsPwEntry 3 }
 
 cienaCesMplsPwName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire name"
     ::= { cienaCesMplsPwEntry 4 }
 
 cienaCesMplsPwCustomerName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the customer name"
     ::= { cienaCesMplsPwEntry 5 }

 cienaCesMplsPwAdminState OBJECT-TYPE
     SYNTAX		CienaGlobalState
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire administrative status"
     ::= { cienaCesMplsPwEntry 6 }

 cienaCesMplsPwOperState OBJECT-TYPE
     SYNTAX		CienaGlobalState
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire operational status"
     ::= { cienaCesMplsPwEntry 7 }
 
 cienaCesMplsPwPeerIpAddr OBJECT-TYPE
     SYNTAX		IpAddress
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Peer Ip address of the pseudowire"
     ::= { cienaCesMplsPwEntry 8 }    
 	   
 cienaCesMplsPwInLabel OBJECT-TYPE
     SYNTAX		Integer32  (-1.. 1048575 )		
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Indicates the inbound label of the pseudowire"
     DEFVAL      { -1 }
 	 ::= { cienaCesMplsPwEntry 9 }

 cienaCesMplsPwOutLabel OBJECT-TYPE
     SYNTAX		Integer32  (-1.. 1048575 )		
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Indicates the outbound label of the pseudowire"
     DEFVAL      { -1 }
 	 ::= { cienaCesMplsPwEntry 10 }

 cienaCesMplsPwStatusTlv OBJECT-TYPE
     SYNTAX      INTEGER {
			   on(1),
 	                   off(2)
 	 }   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"This represents whether the pseudowire status tlv is turned on or off"
     DEFVAL      { off }
     ::= { cienaCesMplsPwEntry 11 }

 cienaCesMplsPwRefreshStatusIntvl OBJECT-TYPE
     SYNTAX		Unsigned32 (1..2147483647)
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
         "This represents the refresh interval for status tlv. This is only
          applicable for static Pseudowire. For dynamic Pseudowire this is
          displayed as 0."
     DEFVAL      { 600 }
     ::= { cienaCesMplsPwEntry 12 }

 cienaCesMplsPwLocalFault OBJECT-TYPE
     SYNTAX		VCStatus
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire local fault"
     ::= { cienaCesMplsPwEntry 13 }

 cienaCesMplsPwRemoteFault OBJECT-TYPE
     SYNTAX		VCStatus
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire remote fault"
     ::= { cienaCesMplsPwEntry 14 }
 

 cienaCesMplsPwMtu   OBJECT-TYPE
     SYNTAX		Unsigned32  (1500.. 9128 )		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"Indicates the pseudowire mtu "
     DEFVAL      { 1500 }
     ::= { cienaCesMplsPwEntry 15 }

 cienaCesMplsPwType OBJECT-TYPE
     SYNTAX      INTEGER {
			   raw(1),
 	                   tagged(2),
			   tdm(3)
 	 }
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire type raw or tagged"
     DEFVAL      { raw }
     ::= { cienaCesMplsPwEntry 16 }

 cienaCesMplsPwMode OBJECT-TYPE
     SYNTAX      INTEGER {
			   mesh(1),
 	                   spoke(2),
 	                   switching(3)
 	 }
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire mode whether it is mesh, spoke, or 
          a MS-PW segment"
     DEFVAL      { mesh }
     ::= { cienaCesMplsPwEntry 17 }

 cienaCesMplsPwCoSProfileName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the cos profile name that is being used by 
	  pseudowire"
     ::= { cienaCesMplsPwEntry 18 }

 cienaCesMplsPwCoSProfileIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the index in cienaCesMplsPwCosProfile table "
     DEFVAL      { 1 }
     ::= { cienaCesMplsPwEntry 19 }

 cienaCesMplsPwEgressL2PtTransform OBJECT-TYPE
     SYNTAX		CienaGlobalState
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the pseudowire L2PT transform for 
	 classified L2 CFT control frames"
     ::= { cienaCesMplsPwEntry 20 }

 cienaCesMplsPwVccVProfileName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the vccv profile name that is being used by 
	  pseudowire"
     ::= { cienaCesMplsPwEntry 21 }

 cienaCesMplsPwVccVProfileIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the index in cienaCesMplsPwVccvProfile table "
     DEFVAL      { 1 }
     ::= { cienaCesMplsPwEntry 22 }

 cienaCesMplsPwLocalCcCv OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the CC/CV configured on the local pseudowire"
     ::= { cienaCesMplsPwEntry 23 }

 cienaCesMplsPwRemoteCcCv OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the CC/CV configured on the remote pseudowire"
     ::= { cienaCesMplsPwEntry 24 }

 cienaCesMplsPwOperatingCcCv OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the negotiated/operating CC/CV"
     ::= { cienaCesMplsPwEntry 25 }

 cienaCesMplsPwBlocking OBJECT-TYPE
     SYNTAX      INTEGER {
 	                   yes(1),
 	                   no(2)
 	 }   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"Specifies whether pseudowire is blocking or forwarding "
     DEFVAL      { yes }
     ::= { cienaCesMplsPwEntry 26 }

 cienaCesMplsPwVifIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (32769..2147483647)
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the Vif/tunnel group that is being used 
	for the pseudowire "
     ::= { cienaCesMplsPwEntry 27 }

 cienaCesMplsPwConfigTunnelName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the configured transport tunnel name
	 for the pseudowire"
     ::= { cienaCesMplsPwEntry 28 }

 cienaCesMplsPwConfigTunnelType OBJECT-TYPE
     SYNTAX     TunnelType   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"This represents the configured transport tunnel type
	 for the pseudowire"
     ::= { cienaCesMplsPwEntry 29 }

 cienaCesMplsPwConfigTunnelIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"Specifies the configured transport tunnel index in one of the 
	 cienaCesMplsTunnel/cienaCesGmplsTunnel tables for 
	 the pseudowire"
     ::= { cienaCesMplsPwEntry 30 }

 cienaCesMplsPwActiveTunnelName OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the active transport tunnel name
	 for the pseudowire"
     ::= { cienaCesMplsPwEntry 31 }

 cienaCesMplsPwActiveTunnelType OBJECT-TYPE
     SYNTAX     TunnelType 
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"This represents the active transport tunnel type
	 for the pseudo wire. This would be the same as 
	 configured tunnel type"
     ::= { cienaCesMplsPwEntry 32 }

 cienaCesMplsPwActiveTunnelIndex OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"Specifies the active transport tunnel index in one of the 
	 cienaCesMplsTunnel/cienaCesGmplsTunnel tables for 
	 the pseudowire"
     ::= { cienaCesMplsPwEntry 33 }

 cienaCesMplsPwRole	OBJECT-TYPE
     SYNTAX      INTEGER {    
                       
 	                   primary(1),
			           secondary(2),
			           stand-alone(3)	 
		 }   
     MAX-ACCESS	read-only
     STATUS		deprecated
     DESCRIPTION
 	"This represents the pseudowire role whether it is a 
	 protecting or primary pseudowire"
     DEFVAL      { primary }
     ::= { cienaCesMplsPwEntry 34 }	

 cienaCesMplsPwPrimaryPwName	OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the primary pseudowire name if 
	 this is a secondary pseudowire"
     ::= { cienaCesMplsPwEntry 35 }

 cienaCesMplsPwPrimaryPwIndex	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the primary pseudowire index in
	 cienaCesMplsPw table if this is a secondary pseudowire"
     ::= { cienaCesMplsPwEntry 36 }

 cienaCesMplsPwVsIndex	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the virtual switch index in the 
	 virtual switch MIB table"
     ::= { cienaCesMplsPwEntry 37 }

 cienaCesServiceDelimiterVID OBJECT-TYPE
     SYNTAX		Unsigned32  (1..4094)		
     MAX-ACCESS	        read-only
     STATUS		deprecated
     DESCRIPTION
 	"This represents the service delimiter VID. This object
	is valid only for 39XX and 51XX platforms"
     DEFVAL      { 1 }
     ::= { cienaCesMplsPwEntry 38 }

 cienaCesServiceDelimiterTPID	OBJECT-TYPE
     SYNTAX		Unsigned32  		
     MAX-ACCESS	        read-only
     STATUS		deprecated
     DESCRIPTION
 	"This represents the service delimiter TPID. This object
	is valid only for 39XX and 51XX platforms"
     ::= { cienaCesMplsPwEntry 39 }

 cienaCesMplsPwReversion OBJECT-TYPE
     SYNTAX      INTEGER {
                           on(1),
                           off(2)
         }
     MAX-ACCESS read-only
     STATUS             current
     DESCRIPTION
        "This represents whether the Pseudowire Reversion is turned on or off.
         With the Reversion turned on, when the Primary Pseudowire has recovered
         from earlier failure, it will be held down for a period of
         cienaCesMplsPwRevertTime(sec) before the switchover from the Backup
         Pseudowire to Primary Pseudowire takes place. With the Reversion turned
         off, the user must use manual switchover command to perform a
         switchover back to the Primary Pseudowire"
     DEFVAL      { on }
     ::= { cienaCesMplsPwEntry 40 }

 cienaCesMplsPwRevertTime OBJECT-TYPE
     SYNTAX             Unsigned32 (0..3600)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "This represents the Reversion Hold-time(in seconds) on the Primary
          Pseudowire. When the Primary Pseudowire has recovered from earlier
          failure, it will be held down for the length of this hold time. Once
          the time has elapsed, and if the Primary Pseudowire does not
          encountered any more failure, there will be a switchover from the
          Backup Pseudowire back to the Primary Pseudowire"
     DEFVAL      { 30 }
     ::= { cienaCesMplsPwEntry 41 }

 cienaCesMplsPwProtectionRole OBJECT-TYPE
     SYNTAX      INTEGER {
                           primary-pseudowire(1),
                           backup-pseudowire(2),
                           stand-alone-pseudowire(3)
                  }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "This represents the role of the pseudowire in a Protection bundle."
     DEFVAL      { stand-alone-pseudowire }
     ::= { cienaCesMplsPwEntry 42 }

 cienaCesMplsPwProtectionState OBJECT-TYPE
     SYNTAX      INTEGER {
                           not-applicable(1),
                           active(2),
                           standby(3),
                           man-swo-active(4),
                           man-swo-standby(5),
                           pw-reversion-pending(6),
                           pw-activation-pending(7)
                  }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "This represents the pseudowire protection state. It only applies to
          those pseudowires in the protection bundle. For stand alone pseudowire
          this has no meaning and is reflected as n/a in this object"
     DEFVAL      { not-applicable }
     ::= { cienaCesMplsPwEntry 43 }

 cienaCesMplsPwVsName	OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the virtual switch name to which the pseudowire is 
          attached."
     ::= { cienaCesMplsPwEntry 44 }

 cienaCesMplsPwStatusQuery OBJECT-TYPE
     SYNTAX      INTEGER {
                           on(1),
                           off(2)
         }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This represents whether the pseudowire status query is enabled.
         With the status query enabled, the pseudowire is able to query
         the remote status when it comes up and the remote status is unknown."
     DEFVAL      { on }
     ::= { cienaCesMplsPwEntry 45 }

 cienaCesMplsMsPwPeerPwName	OBJECT-TYPE
     SYNTAX		DisplayString (SIZE (1..31))
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the peer pseudowire name if this is a MS-PW."
     ::= { cienaCesMplsPwEntry 46 }

 cienaCesMplsMsPwPeerPwIndex	OBJECT-TYPE
     SYNTAX		Unsigned32  (1..65535)		
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	"This represents the peer pseudowire index if this is a MS-PW."
     ::= { cienaCesMplsPwEntry 47 }

 cienaCesMplsPwIdInRemoteFault OBJECT-TYPE
     SYNTAX      	Unsigned32 (1..2147483647)
     MAX-ACCESS  	read-only
     STATUS      	current
     DESCRIPTION
        "This represents the PW Id in the SP-PE TLV of the remote Status"
     ::= { cienaCesMplsPwEntry 48 }
 
 cienaCesMplsLocalIpInRemoteFault OBJECT-TYPE
     SYNTAX		IpAddress
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Local Ip address in the SP-PE TLV of the remote Status"
     ::= { cienaCesMplsPwEntry 49 }    
 	   
 cienaCesMplsRemoteIpInRemoteFault OBJECT-TYPE
     SYNTAX		IpAddress
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Remote Ip address in the SP-PE TLV of the remote Status"
     ::= { cienaCesMplsPwEntry 50 }    
 	   
 cienaCesMplsPwFaultToNextHop OBJECT-TYPE
     SYNTAX		VCStatus
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
 	 "This represents the aggregated MS-PW faults sent to the next hop"
     ::= { cienaCesMplsPwEntry 51 }
 
 cienaCesMplsPwIdInFaultToNextHop OBJECT-TYPE
     SYNTAX      	Unsigned32 (1..2147483647)
     MAX-ACCESS  	read-only
     STATUS      	current
     DESCRIPTION
        "This represents the PW Id in the SP-PE TLV sent to the next hop"
     ::= { cienaCesMplsPwEntry 52 }
 
 cienaCesMplsLocalIpInFaultToNextHop OBJECT-TYPE
     SYNTAX		IpAddress
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Local Ip address in the SP-PE TLV sent to the next hop"
     ::= { cienaCesMplsPwEntry 53 }    
 	   
 cienaCesMplsRemoteIpInFaultToNextHop OBJECT-TYPE
     SYNTAX		IpAddress
     MAX-ACCESS		read-only
     STATUS		current
     DESCRIPTION
	"Remote Ip address in the SP-PE TLV sent to the next hop"
     ::= { cienaCesMplsPwEntry 54 }   
 
 cienaCesMplsPwConfigBandwidth  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..10000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
	"This represents the configured Bandwidth(in kbps) of pseudowire"
     ::= { cienaCesMplsPwEntry 55 }

 cienaCesMplsPwOperBandwidth  OBJECT-TYPE
     SYNTAX         MplsBitRate    (0..10000000)
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
	"This represents the operational Bandwidth(in kbps) of pseudowire"
     ::= { cienaCesMplsPwEntry 56 }

 cienaCesMplsPwBandwidthState  OBJECT-TYPE
     SYNTAX      INTEGER {
                           none(1),
                           configured(2),
                           notRequired(3),
                           admitted(4),
                           acquired(5),
                           rejected(6)
                  }
     MAX-ACCESS     read-only
     STATUS         current
     DESCRIPTION
	"This represents the Bandwidth state of pseudowire"
     ::= { cienaCesMplsPwEntry 57 }

--
-- MPLS Pw Cos profile table
--

 cienaCesMplsPwCosProfileTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsPwCosProfileEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS pseudowire cos profiles."

     ::= { cienaCesMplsPw 2 }
     
 cienaCesMplsPwCosProfileEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsPwCosProfileEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS pseudowire Cos profile table."
     INDEX {cienaCesMplsPwCosProfileIndex}
     ::= { cienaCesMplsPwCosProfileTable 1 }
     
 CienaCesMplsPwCosProfileEntry ::=  SEQUENCE { 
     cienaCesMplsPwCosProfileIndex      	Unsigned32,
     cienaCesMplsPwCosProfileName		DisplayString,
     cienaCesMplsPwCosProfileFrmCosPolicy	FCosPolicy,
     cienaCesMplsPwCosProfileFrmCosMapId	Unsigned32,
     cienaCesMplsPwCosProfileFrmCosMapName	DisplayString,
     cienaCesMplsPwCosProfileFixedTC		Unsigned32,
     cienaCesMplsPwCosProfileRcosPolicy		RCosPolicy,
     cienaCesMplsPwCosProfileRcosMapName	DisplayString,
     cienaCesMplsPwCosProfileRCosMapId		Unsigned32,
     cienaCesMplsPwCosProfileRcosProfileName	DisplayString,
     cienaCesMplsPwCosProfileRCosProfileId	INTEGER,
     cienaCesMplsPwCosProfileRCosFixed		Unsigned32
 }

 cienaCesMplsPwCosProfileIndex OBJECT-TYPE  
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index for Mpls Pw Cos profile."
     ::= { cienaCesMplsPwCosProfileEntry 1 }
  
 cienaCesMplsPwCosProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls pseudowire Cos profile name"
     ::= { cienaCesMplsPwCosProfileEntry 2 }  
   
 cienaCesMplsPwCosProfileFrmCosPolicy OBJECT-TYPE
     SYNTAX		FCosPolicy  
     MAX-ACCESS         read-only
     STATUS		current
     DESCRIPTION
 	 "Frame CoS policy of the pseudowire"
     DEFVAL { rcos-mapped }
     ::= { cienaCesMplsPwCosProfileEntry 3 }
   
 cienaCesMplsPwCosProfileFrmCosMapId OBJECT-TYPE
     SYNTAX		Unsigned32   (1..65535)
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
	"The frame CoS map ID of pseudo wire. 
	This value is valid if the frame Cos policy 
	selected for the  pseudowire is rcos-mapped"
     DEFVAL { 1 }
     ::= {cienaCesMplsPwCosProfileEntry 4 }  
 	
 cienaCesMplsPwCosProfileFrmCosMapName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls pseudowire frame cos Map name"
     ::= { cienaCesMplsPwCosProfileEntry 5 }	 
 
 cienaCesMplsPwCosProfileFixedTC OBJECT-TYPE
     SYNTAX		Unsigned32  (0..7 )
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
 	 "If the frame CoS policy for the pseudowire 
	  is fixed, this value is used for MPLS 
	  encapsulation." 
     DEFVAL { 0 }
     ::= { cienaCesMplsPwCosProfileEntry 6 }
	                                                                                           
 cienaCesMplsPwCosProfileRcosPolicy OBJECT-TYPE
     SYNTAX		RCosPolicy  
     MAX-ACCESS  	read-only
     STATUS		 current
     DESCRIPTION
	"Resolved CoS policy of the pseudowire"
     DEFVAL { exp-mapped }
     ::= { cienaCesMplsPwCosProfileEntry 7 } 
      
 cienaCesMplsPwCosProfileRcosMapName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls pseudowire resolved cos Map name. This object
	is valid only for 39XX and 51XX platforms"
     ::= { cienaCesMplsPwCosProfileEntry 8 }      
      
 cienaCesMplsPwCosProfileRCosMapId OBJECT-TYPE
     SYNTAX		Unsigned32   (1..65535)
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
	"The resolved Cos map ID of the pseudo wire. 
	This value is valid if the resolved Cos policy 
	selected for the pseudowire is rcos-mapped.
	This object is valid only for 39XX and 51XX platforms"
     DEFVAL { 1 }
     ::= { cienaCesMplsPwCosProfileEntry 9 }

 cienaCesMplsPwCosProfileRcosProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls pseudowire resolved cos Profile name.
	This object is valid only for 87XX"
     ::= { cienaCesMplsPwCosProfileEntry 10 }      
      
 cienaCesMplsPwCosProfileRCosProfileId OBJECT-TYPE
     SYNTAX		INTEGER   (1..65535)
     MAX-ACCESS	        read-only
     STATUS		current
     DESCRIPTION
	"The resolved Cos profile ID of the pseudo wire. 
        This value is valid if the resolved Cos policy 
        selected for the pseudowire is rcos-mapped.
        This object is valid only for 87XX"
     DEFVAL { 1 }
     ::= { cienaCesMplsPwCosProfileEntry 11 }

 cienaCesMplsPwCosProfileRCosFixed OBJECT-TYPE
     SYNTAX		Unsigned32  (0..7 )
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	 "If the resolved CoS policy for the pseudowire 
	  is fixed, this value is used" 
     DEFVAL { 0 }
     ::= { cienaCesMplsPwCosProfileEntry 12 }

--
-- MPLS Pw Vccv profile table
--

 cienaCesMplsPwVccvProfileTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsPwVccvProfileEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
            "The (conceptual) table listing the MPLS pseudowire 
	    Vccv profiles."

     ::= { cienaCesMplsPw 3 }
     
 cienaCesMplsPwVccvProfileEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsPwVccvProfileEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "This represents an entry of the MPLS pseudowire Vccv profile table."
     INDEX {cienaCesMplsPwVccvProfileIndex}
     ::= { cienaCesMplsPwVccvProfileTable 1 }
     
 CienaCesMplsPwVccvProfileEntry ::=  SEQUENCE { 
     cienaCesMplsPwVccvProfileIndex       Unsigned32,
     cienaCesMplsPwVccvProfileName	  DisplayString,
     cienaCesMplsPwVccvProfileCcTtlExp	  INTEGER,
     cienaCesMplsPwVccvProfileCcCienaOob  INTEGER
 }

 cienaCesMplsPwVccvProfileIndex OBJECT-TYPE  
     SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index for Mpls Pw Vccv profile."
     ::= { cienaCesMplsPwVccvProfileEntry 1 }
  
 cienaCesMplsPwVccvProfileName OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "Mpls pseudowire Vccv profile name"
     ::= { cienaCesMplsPwVccvProfileEntry 2 } 
 
 cienaCesMplsPwVccvProfileCcTtlExp	OBJECT-TYPE
     SYNTAX      INTEGER {
 	                   on(1),
			   off(2)	 
		 }   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"This represents the pseudowire TTL-expiry VCCV control 
	channel (CC Type-3) operation"
     DEFVAL { on }
     ::= { cienaCesMplsPwVccvProfileEntry 3 } 

 cienaCesMplsPwVccvProfileCcCienaOob	OBJECT-TYPE
     SYNTAX      INTEGER {
 	                   on(1),
			   off(2)	 
		 }   
     MAX-ACCESS	read-only
     STATUS		current
     DESCRIPTION
 	"This represents the pseudowire CIENA proprietary out-of-band 
	VCCV control channel (CC Type-4) operation"
     DEFVAL { on }
     ::= { cienaCesMplsPwVccvProfileEntry 4 } 
  
 --
 -- MPLS PW Notifications table
 --

 cienaCesMplsPwNotif OBJECT IDENTIFIER ::= { cienaCesMplsPw 4 }


 cienaCesMplsPwNotifTable   OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsPwNotifEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "Table of notification objects required for pseudowire notification."
     ::= { cienaCesMplsPwNotif 1 }

 cienaCesMplsPwNotifEntry OBJECT-TYPE
     SYNTAX     CienaCesMplsPwNotifEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
         "An entry (conceptual row) in the cienaCesMplsPwNotifTable.
         No Get, GetNext or Set is allowed on this table."
     INDEX    { cienaCesMplsPwNotifPwIndex}
     ::= { cienaCesMplsPwNotifTable 1 }  

 CienaCesMplsPwNotifEntry ::= SEQUENCE {
     cienaCesMplsPwNotifPwIndex         Unsigned32,
     cienaCesMplsPwNotifPwId            Unsigned32,
     cienaCesMplsPwNotifPwName          DisplayString,
     cienaCesMplsPwNotifPwPeerIpAddr    IpAddress,
     cienaCesMplsPwNotifPriPwId         Unsigned32,
     cienaCesMplsPwNotifPriPwName       DisplayString,
     cienaCesMplsPwNotifPriPwPeerIpAddr IpAddress,
     cienaCesMplsPwNotifActPwIndex      Unsigned32,
     cienaCesMplsPwNotifActPwId         Unsigned32,
     cienaCesMplsPwNotifActPwName       DisplayString,
     cienaCesMplsPwNotifActPwPeerIpAddr IpAddress
 }

 cienaCesMplsPwNotifPwIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..4294967295)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Unique pseudowire index in the PW table"
     ::= { cienaCesMplsPwNotifEntry 1 }

 cienaCesMplsPwNotifPwId    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..4294967295)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Unique Pseudowire ID used to identify the PW in the signaling. For the
          static PW this represents the locally configured value."
     ::= { cienaCesMplsPwNotifEntry 2 }

 cienaCesMplsPwNotifPwName    OBJECT-TYPE
     SYNTAX     DisplayString
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
        "This represents the locally configured PW name."
     ::= { cienaCesMplsPwNotifEntry 3 }

 cienaCesMplsPwNotifPwPeerIpAddr    OBJECT-TYPE
     SYNTAX     IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
        "This represents the IP address of the Peer on the remote end of the PW."
     ::= { cienaCesMplsPwNotifEntry 4 }

 cienaCesMplsPwNotifPriPwId    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..4294967295)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "This represents the PW Id of the primary PW."
     ::= { cienaCesMplsPwNotifEntry 5 }

 cienaCesMplsPwNotifPriPwName    OBJECT-TYPE
     SYNTAX     DisplayString
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "This represents the PW name of the primary PW."
     ::= { cienaCesMplsPwNotifEntry 6 }

 cienaCesMplsPwNotifPriPwPeerIpAddr    OBJECT-TYPE
     SYNTAX     IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
        "This represents the IP address of the Peer on the remote end of the
        primary PW."
     ::= { cienaCesMplsPwNotifEntry 7 }

 cienaCesMplsPwNotifActPwIndex    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..4294967295)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "Unique pseudowire index in the PW table"
     ::= { cienaCesMplsPwNotifEntry 8 }

 cienaCesMplsPwNotifActPwId    OBJECT-TYPE
     SYNTAX     Unsigned32 (1..4294967295)
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "This represents the PW Id of the active PW."
     ::= { cienaCesMplsPwNotifEntry 9 }

 cienaCesMplsPwNotifActPwName    OBJECT-TYPE
     SYNTAX     DisplayString
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
         "This represents the PW name of the active PW."
     ::= { cienaCesMplsPwNotifEntry 10 }

 cienaCesMplsPwNotifActPwPeerIpAddr    OBJECT-TYPE
     SYNTAX     IpAddress
     MAX-ACCESS accessible-for-notify
     STATUS     current
     DESCRIPTION
        "This represents the IP address of the Peer on the remote end of the
        active PW."
     ::= { cienaCesMplsPwNotifEntry 11 }


 --
 -- PW Traps
 --

 cienaCesMplsPwDown NOTIFICATION-TYPE
    OBJECTS {
        cienaGlobalSeverity,  
        cienaGlobalMacAddress,
        cienaCesMplsPwNotifPwIndex,
        cienaCesMplsPwNotifPwName,
        cienaCesMplsPwNotifPwId,
        cienaCesMplsPwNotifPwPeerIpAddr
    }
    STATUS  current
    DESCRIPTION
         "This notification is sent when the operational state of a pseudowire
         has changed from up to down."
    ::= { cienaCesMplsPwMIBNotifications  1 }


 cienaCesMplsPwUp NOTIFICATION-TYPE
    OBJECTS {
        cienaGlobalSeverity,  
        cienaGlobalMacAddress,
        cienaCesMplsPwNotifPwIndex,
        cienaCesMplsPwNotifPwName,
        cienaCesMplsPwNotifPwId,
        cienaCesMplsPwNotifPwPeerIpAddr
    }
    STATUS  current
    DESCRIPTION
         "This notification is sent when the operational state of a pseudowire
         has changed from down to up."

    ::= { cienaCesMplsPwMIBNotifications 2 }


 cienaCesMplsPwBundleActivePwChange NOTIFICATION-TYPE
    OBJECTS {
        cienaGlobalSeverity,  
        cienaGlobalMacAddress,
        cienaCesMplsPwNotifActPwIndex,
        cienaCesMplsPwNotifActPwName,
        cienaCesMplsPwNotifActPwId,
        cienaCesMplsPwNotifActPwPeerIpAddr,
        cienaCesMplsPwNotifPriPwName,
        cienaCesMplsPwNotifPriPwId,
        cienaCesMplsPwNotifPriPwPeerIpAddr
    }
    STATUS  current
    DESCRIPTION
         "This notification is sent when the active member of the pseudowire
          bundle has changed."

    ::= { cienaCesMplsPwMIBNotifications  3 }

--
 -- Mpls PW Traffic Statistics Table
 --     
     
 cienaCesMplsPWTrafficStatsTable OBJECT-TYPE
     SYNTAX     SEQUENCE OF CienaCesMplsPWTrafficStatsEntry
     MAX-ACCESS not-accessible
     STATUS     current
     DESCRIPTION
           "Mpls PW Traffic Statistics table.
            The attributes in this table are supported only on specific platforms."
     ::= { cienaCesMplsPw 5 }
     
 cienaCesMplsPWTrafficStatsEntry  OBJECT-TYPE
     SYNTAX       CienaCesMplsPWTrafficStatsEntry
     MAX-ACCESS   not-accessible
     STATUS       current
     DESCRIPTION
             "Mpls PW Traffic Statistics table entry."
     INDEX {cienaCesMplsPWTrafficStatsPWIndex}
     ::= { cienaCesMplsPWTrafficStatsTable 1 }
     
 CienaCesMplsPWTrafficStatsEntry ::=  SEQUENCE { 
     cienaCesMplsPWTrafficStatsPWIndex             Unsigned32,
     cienaCesMplsPWTrafficStatsIncomingPackets     Unsigned32,
     cienaCesMplsPWTrafficStatsOutgoingPackets     Unsigned32,
     cienaCesMplsPWTrafficStatsIncomingBytes       Unsigned32,
     cienaCesMplsPWTrafficStatsOutgoingBytes       Unsigned32
 }
      
 cienaCesMplsPWTrafficStatsPWIndex		OBJECT-TYPE  
  	 SYNTAX      Unsigned32
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "Unique index for Mpls PW Traffic Statistics table."
     ::= { cienaCesMplsPWTrafficStatsEntry 1 }
  
 cienaCesMplsPWTrafficStatsIncomingPackets		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of incoming packets for the given PW."
     ::= { cienaCesMplsPWTrafficStatsEntry 2 }  
   
 cienaCesMplsPWTrafficStatsOutgoingPackets	OBJECT-TYPE
 	 SYNTAX		Unsigned32  
 	 MAX-ACCESS  read-only
 	 STATUS		 current
 	 DESCRIPTION
 	 	"The number of outgoing packets for the given PW."
 	 ::= { cienaCesMplsPWTrafficStatsEntry 3 }
   
 cienaCesMplsPWTrafficStatsIncomingBytes    OBJECT-TYPE
 	 SYNTAX		Unsigned32   
 	 MAX-ACCESS	read-only
 	 STATUS		current
 	 DESCRIPTION
 	 	"The number of incoming bytes for the given PW."
 	 ::= {cienaCesMplsPWTrafficStatsEntry 4 }  
 	
 cienaCesMplsPWTrafficStatsOutgoingBytes		OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
        "The number of outgoing bytes for the given PW."
     ::= { cienaCesMplsPWTrafficStatsEntry 5 }	 

 END






