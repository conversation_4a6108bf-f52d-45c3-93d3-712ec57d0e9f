-- MIB module odule defining SNMPv2 traps for the 3584 family of IBM tape libraries.
IBM-TS3500-MIBv2 DEFINITIONS ::= BEGIN


-- Imports from other sources that are used in this MIB module.
IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, enterprises, Integer32
                FROM SNMPv2-SMI         -- RFC1902

        DisplayString
                FROM SNMPv2-TC          -- RFC1903

        MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
                FROM SNMPv2-CONF;       -- RFC1904


-- Module identification.
ibm3584 MODULE-IDENTITY
        LAST-UPDATED    "200906230000Z"  --  September 23, 2009
        ORGANIZATION    "IBM RMSS - 3584 Development"
        CONTACT-INFO    "IBM Help"--
		DESCRIPTION     "Added Host Message to most of Audit traps and fix trap 450 to indicate the corect message"
        REVISION        "200909230000Z"  --  September 23, 2009  
        DESCRIPTION     "Change description on trap ibm3884Trap 441 to match the v1 MIB"
        REVISION        "200904060000Z"  --  August 26, 2009       
        DESCRIPTION     "Added ibm3584Trap419 thru ibm3584Trap422 and ibm3584Trap440 thru ibm3884Trap 452"
        REVISION        "200904060000Z"  --  April 06, 2009
        DESCRIPTION     "Modified ibm3584Trap402 for logical library full state vs library full"
        REVISION        "200807160000Z"  --  July 16, 2008
        DESCRIPTION     "Modified ibm3584Trap411-412 for physical library approaching full state"
        REVISION        "200807160000Z"  --  July 16, 2008
        DESCRIPTION     "Added ibm3584Trap416-418 for logical library and physical library full states"
        REVISION        "200807160000Z"  --  July 16, 2008
        DESCRIPTION     "Added ibm3584Trap415 call home failure warning"
        REVISION        "200601120000Z"  --  January 12, 2006
        DESCRIPTION     "Fixed ibm3584MIBObjectsSeverity values to match firmware"
        REVISION        "200601030000Z"  --  January 3, 2006
        DESCRIPTION     "Added Severity Indications to the TRAP and Index Linkages back to the SNIA-SML MIB"
        REVISION        "200506150000Z"   -- June 15, 2005
        DESCRIPTION     "New Tape Alerts and updated descriptions"
        REVISION        "200505030000Z"   -- May 3, 2005
        DESCRIPTION     "Added Trap 414."
        REVISION        "200412010000Z"   -- December 1, 2004
        DESCRIPTION     "Fixed SMI problem that would cause some SNMP managment stations to fail to load MIB"
        REVISION        "200403040000Z"   -- March 4, 2004
        DESCRIPTION     "Added Trap 413."
        REVISION        "200403030000Z"   -- March 3, 2004
        DESCRIPTION     "Trap definitions for 3584 libraries using SNMPv2c."
        REVISION        "200402030000Z"   -- Febuary 3, 2004
        DESCRIPTION     "Addition of traps to support 3592 media and ALMS"
        REVISION        "200310220000Z"   -- October 22, 2003
        DESCRIPTION     "Improvement of description fields."
        REVISION        "200204230000Z"   -- April 23, 2002
        DESCRIPTION     "Minor revision of this module."
        REVISION        "200101010000Z"   -- October 8, 2001
        DESCRIPTION     "Initial revision of this module."

        ::= { ibmProd 182 }

-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Tree structure ID setup

ibm OBJECT IDENTIFIER
        ::= { enterprises 2 }

ibmProd OBJECT IDENTIFIER
        ::= { ibm 6 }

--ibm3584 OBJECT IDENTIFIER
--        ::= { ibmProd 182 }

ibm3584MIB OBJECT IDENTIFIER
        ::= { ibm3584 1 }

ibm3584MIBTraps OBJECT IDENTIFIER
        ::= { ibm3584MIB 0 }

ibm3584MIBAdmin OBJECT IDENTIFIER
        ::= { ibm3584MIB 1 }

ibm3584MIBObjects OBJECT IDENTIFIER
        ::= { ibm3584MIB 2 }

ibm3584MIBConformance OBJECT IDENTIFIER
        ::= { ibm3584MIB 3 }

-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Specific trap object setup

-- Machine type, model number, library serial number ID
ibm3584MIBGroupMTMNLSN OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 11 }

-- Sense key, additional sense code, additional sense code qualifier ID
ibm3584MIBGroupSKASCASCQ OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 21 }

-- Hardware error code, hardware error code qualifier ID
ibm3584MIBGroupHECHECQ OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 31 }

-- Tape alert ID
ibm3584MIBGroupTA OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 41 }

-- URC ID
ibm3584MIBGroupURC OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 51 }

-- Failing frame, failing drive ID
ibm3584MIBGroupFFFD OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 61 }

-- Text description ID
ibm3584MIBGroupTD OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 71 }

-- FSC
ibm3584MIBGroupFSC OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 81 }

-- Single Character Display
ibm3584MIBGroupSCD OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 91 }

-- Cartridge Volume Serial Number
ibm3584MIBGroupVOLSER OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 101 }

-- Logical Library Number
ibm3584MIBGroupLL OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 111 }

-- World Wide Node Name
ibm3584MIBGroupWWNN OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 121 }

-- Element Address
ibm3584MIBGroupEA OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 131 }

-- Drive Serial Number
ibm3584MIBGroupDrvSN OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 141 }

-- Severity Code
ibm3584MIBSeverity OBJECT IDENTIFIER
        ::= { ibm3584MIBObjects 151 }

-- UserID
ibm3584MIBUserID OBJECT IDENTIFIER    
        ::= { ibm3584MIBObjects 171 }

-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Trap object definitions

-- Machine type, model number, library serial number definition
-- Size is 14 plus one space b/n MT and MN and one space b/n MN and LSN
ibm3584MIBObjectsMTMNLSN OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..16))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the machine type associated with the trap."
        ::= { ibm3584MIBGroupMTMNLSN 1 }

-- Sense key, additional sense code, additional sense code qualifier definition
-- Size is 6 plus one space b/n SK and ASC and one space b/n ASC and ASCQ
ibm3584MIBObjectsSKASCASCQ OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..8))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the sense key associated with the trap."
        ::= { ibm3584MIBGroupSKASCASCQ 1 }

-- Hardware error code, hardware error code qualifier definition
-- Size is 4 plus one space b/n HEC and HECQ
ibm3584MIBObjectsHECHECQ OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..5))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the hardware error code associated with the trap."
        ::= { ibm3584MIBGroupHECHECQ 1 }

-- Tape alert definition
ibm3584MIBObjectsTA OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..2))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the TapeAlert flag number associated with the trap."
        ::= { ibm3584MIBGroupTA 1 }

-- URC definition
ibm3584MIBObjectsURC OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..4))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the URC associated with the trap."
        ::= { ibm3584MIBGroupURC 1 }

-- Failing frame, failing drive definition
-- Size is 4 plus one space b/n FF and FD
ibm3584MIBObjectsFFFD OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..5))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the failing frame associated with the trap."
        ::= { ibm3584MIBGroupFFFD 1 }

-- Text description definition
ibm3584MIBObjectsTD OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..255))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the text description of the trap."
        ::= { ibm3584MIBGroupTD 1 }

-- FSC definition
ibm3584MIBObjectsFSC OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..4))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the drive Fault Symptom Code."
        ::= { ibm3584MIBGroupFSC 1 }

-- Text description definition
ibm3584MIBObjectsSCD OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the Single Character Display on the drive."
        ::= { ibm3584MIBGroupSCD 1 }

-- Text description definition
ibm3584MIBObjectsVOLSER OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..8))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the Volume Serial number on the cartridge."
        ::= { ibm3584MIBGroupVOLSER 1 }


-- Logical Library Definition
ibm3584MIBObjectsLL OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..16))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the Logical Library that is having a problem within the physical library."
        ::= { ibm3584MIBGroupLL 1 }

-- World Wide Node Name Definition
ibm3584MIBObjectsWWNN OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..8))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The World Wide Node Name of the Drive that is having problems."
        ::= { ibm3584MIBGroupWWNN 1 }

-- Element Address Definition
ibm3584MIBObjectsEA OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Element Address of the Drive that is having problems."
        ::= { ibm3584MIBGroupEA 1 }

-- Element Address Definition
ibm3584MIBObjectsDrvSN OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..12))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Serial Number of the Drive that is having problems."
        ::= { ibm3584MIBGroupDrvSN 1 }


-- Severity Code
ibm3584MIBObjectsSeverity OBJECT-TYPE
            SYNTAX          INTEGER { 
                              informational (1),
                              warning  (2),
                              critical (3),
                              unknown (4),
                              configuration (5),
                              security (6),
                              authentication (7)
                              }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     
         "Indicates the percieved severity of the problem"
::= { ibm3584MIBSeverity 1 }

-- User ID of request from external UI
ibm3584MIBObjectsUserID OBJECT-TYPE
        SYNTAX DisplayString (SIZE (1..20))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "This is the User ID associated with a request via external UI."
::= { ibm3584MIBUserID 1 }




-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Trap definitions - Specific TapeAlert traps for the 3584 library
-- Range is 001 to 199

-- Trap for library TapeAlert 1
ibm3584Trap001 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 001.
                 Flag: Library hardware A
                 Type: C
                 Cause: The changer mechanism is having trouble communicating with the
                        internal drive.
                 Required host message:
                 The library is having difficulty communicating with the drive.
                 1. Restart the operation.
                 2. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 1 }

-- Trap for library TapeAlert 002
ibm3584Trap002 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 002.
                 Flag: Library hardware B.
                 Type: W
                 Cause: The changer mechanism has a hardware fault.
                 Required host message:
                 The library has a hardware failure.
                 1. Restart the operation.
                 2. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 2 }

-- Trap for library TapeAlert 003
ibm3584Trap003 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 003.
                 Flag: Library hardware C.
                 Type: C
                 Cause: The changer mechanism has a hardware fault that requires a reset
                        to recover.
                 Required host message:
                 The library has a hardware fault:
                 1. Reset the library.
                 2. Restart the operation. Check the library users manual for device specific instructions on resetting the device."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 3 }

-- Trap for library TapeAlert 004
ibm3584Trap004 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 004.
                 Flag: Library hardware D.
                 Type: C
                 Cause: The changer mechanism has a hardware fault that is not mechanically
                        related, or that requires a power cycle to recover.
                 Required host message:
                 The library has a hardware fault that is not mechanically related.
                 1. Restart the operation.
                 2. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 4 }

-- Trap for library TapeAlert 005
ibm3584Trap005 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 005.
                 Flag: Library diagnostics required.
                 Type: W
                 Cause: The changer mechanism has a hardware fault which would be
                        identified by extended diagnostics (eg SCSI Send Diagnostic).
                 Required host message:
                 The library mechanism may have a hardware fault.
                 Run extended diagnostics to verify and diagnose the problem.
                 Check the library users manual for device specific instructions
                 on running extended diagnostic tests."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 5 }

-- Trap for library TapeAlert 006
ibm3584Trap006 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 006.
                 Flag: Library interface.
                 Type: C
                 Cause: The library has identified an interfacing fault.
                 Required host message:
                 The library has a problem with the host interface:
                 1. Check the cables and cable connections.
                 2. Restart the operation."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 6 }

-- Trap for library TapeAlert 007
ibm3584Trap007 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 007.
                 Flag: Predictive failure.
                 Type: W
                 Cause: Predictive failure of library hardware.
                 Required host message:
                 A hardware failure of the library is predicted. Call the library supplier
                 helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 7 }

-- Trap for library TapeAlert 008
ibm3584Trap008 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 008.
                 Flag: Library maintenance.
                 Type: W
                 Cause: Library preventive maintenance required.
                 Required host message:
                 Preventative maintenance of the library is required. Check the library
                 users manual for device specific preventative maintenance tasks, or call
                 your library supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 8 }

-- Trap for library TapeAlert 009
ibm3584Trap009 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 009.
                 Flag: Library humidity limits.
                 Type: C
                 Cause: Library humidity limits exceeded.
                 Required host message:
                 General environmental conditions inside the library are outside the
                 specified humidity range."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 9 }

-- Trap for library TapeAlert 010
ibm3584Trap010 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 010.
                 Flag: Library temperature limits.
                 Type: C
                 Cause: Library temperature limits exceeded.
                 Required host message:
                 General environmental conditions inside the library have exceeded
                 the specified temperature range."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 10 }

-- Trap for library TapeAlert 011
ibm3584Trap011 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 011.
                 Flag: Library voltage limits.
                 Type: C
                 Cause: Library voltage limits exceeded.
                 Required host message:
                 A potential failure of a power supply exists. Call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 11 }

-- Trap for library TapeAlert 012
ibm3584Trap012 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 012.
                 Flag: Library stray tape.
                 Type: C
                 Cause: Stray cartridge left in library drive after previous error
                        recovery.
                 Required host message:
                 A cartridge has been left in a drive inside the library by a previous
                 hardware fault:
                 1. Insert an empty magazine to clear the fault.
                 2. If the fault does not clear, turn the library off and then on again.
                 3. If the problem persists, call the library supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 12 }

-- Trap for library TapeAlert 013
ibm3584Trap013 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 013.
                 Flag: Library pick retry.
                 Type: W
                 Cause: Operation to pick a cartridge from a slot had to perform an
                        excessive number of retries before succeeding.
                 Required host message:
                 There is a potential problem with a drive ejecting cartridges short
                 or with the library mechanism picking a cartridge from a slot.
                 1. No action needs to be taken at this time.
                 2. If the problem persists, call the library supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 13 }

-- Trap for library TapeAlert 014
ibm3584Trap014 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 014.
                 Flag: Library place retry.
                 Type: W
                 Cause: Operation to place a cartridge into a slot had to perform an
                        excessive number of retries before succeeding.
                 Required host message:
                 There is a potential problem with the library mechanism placing a
                 cartridge into a slot:
                 1. No action needs to be taken at this time.
                 2. If the problem persists, call the library supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 14 }

-- Trap for library TapeAlert 015
ibm3584Trap015 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 015.
                 Flag: Library load retry.
                 Type: W
                 Cause: Operation to load a cartridge into a drive had to perform an
                        excessive number of retries before succeeding.
                 Required host message:
                 There is a potential problem with the library mechanism placing a
                 cartridge into a slot:
                 1. No action needs to be taken at this time.
                 2. If the problem persists, call the library supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 15 }

-- Trap for library TapeAlert 016
ibm3584Trap016 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 016.
                 Flag: Library door.
                 Type: C
                 Cause: Changer door open prevents library functioning.
                 Required host message:
                 A library door is open and prevents the library from functioning.
                 1. Clear any obstructions from the library door.
                 2. Close the library door."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 16 }

-- Trap for library TapeAlert 017
ibm3584Trap017 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 017.
                 Flag: Library mail slot.
                 Type: C
                 Cause: Mechanical problem with import/export mail slot.
                 Required host message:
                 A problem with an I/O station exists.
                 1. Ensure that there is no obstruction in the I/O station.
                 2. Restart the operation.
                 3. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 17 }

-- Trap for library TapeAlert 018
ibm3584Trap018 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 018.
                 Flag: Library magazine.
                 Type: C
                 Cause: Library magazine not present.
                 Required host message:
                 The library cannot operate without the magazine.
                 1. Insert the magazine into the library.
                 2. Restart the operation."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 18 }

-- Trap for library TapeAlert 019
ibm3584Trap019 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 019.
                 Flag: Library security mode.
                 Type: W
                 Cause: Library door opened then closed during operation.
                 Required host message:
                 Library security has been compromised."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 19 }

-- Trap for library TapeAlert 020
ibm3584Trap020 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 020.
                 Flag: Library security mode.
                 Type: I
                 Cause: Library security mode has changed.
                 Required host message:
                 The security mode of the library has been changed. Either the library
                 has been put into secure mode or the library has exited secure mode.
                 This is for informational purposes only. No action is required."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 20 }

-- Trap for library TapeAlert 021
ibm3584Trap021 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 021.
                 Flag: Library offline.
                 Type: I
                 Cause: Library manually turned offline.
                 Required host message:
                 The library has been manually turned offline and is unavailable for use."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 21 }

-- Trap for library TapeAlert 022
ibm3584Trap022 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 022.
                 Flag: Library drive offline.
                 Type: I
                 Cause: Library turned internal drive offline.
                 Required host message:
                 A drive inside the library has been taken offline. This is for
                 informational purposes only. No action is required."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 22 }

-- Trap for library TapeAlert 023
ibm3584Trap023 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 023.
                 Flag: Library scan retry.
                 Type: W
                 Cause: Operation to scan the barcode on a cartridge had to perform an
                        excessive number of retries before succeeding.
                 Required host message:
                 There is a potential problem with the barcode label or the scanner hardware in the library mechanism.
                 1. Check for damaged, misaligned, or peeling barcode labels on cartridges.
                 2. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 23 }

-- Trap for library TapeAlert 024
ibm3584Trap024 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 024.
                 Flag: Library inventory.
                 Type: C
                 Cause: Inconsistent media inventory.
                 Required host message:
                 Library has detected an inconsistency in its inventory.
                 1. Run a library inventory to correct the inconsistency.
                 2. Restart operation.
                 3. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 24 }

-- Trap for library TapeAlert 025
ibm3584Trap025 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 025.
                 Flag: Library illegal operation.
                 Type: W
                 Cause: Illegal operation detected.
                 Required host message:
                 The library detected an illegal operation. If the problem persists, call your IBM Service Representative."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 25 }

-- Trap for library TapeAlert 026
ibm3584Trap026 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 026.
                 Flag: Dual-port interface error.
                 Type: W
                 Cause: Failure of one interface port in a dual-port configuration, eg
                        Fibre channel
                 Required host message:
                 A redundant interface port on the library has failed."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 26 }

-- Trap for library TapeAlert 027
ibm3584Trap027 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 027.
                 Flag: Cooling fan failure.
                 Type: W
                 Cause: One or more fans inside the library have failed. Internal flag
                        state only cleared when all fans are working again.
                 Required host message:
                 A library cooling fan has failed."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 27 }

-- Trap for library TapeAlert 028
ibm3584Trap028 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 028.
                 Flag: Power supply.
                 Type: W
                 Cause: Redundant PSU failure inside the library subsystem.
                 Required host message:
                 A redundant power supply has failed inside the library. Call your IBM Service Representative."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 28 }

-- Trap for library TapeAlert 029
ibm3584Trap029 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 029.
                 Flag: Power consumption.
                 Type: W
                 Cause: Power consumption of one or more devices inside the library is
                        outside specified range.
                 Required host message:
                 The library power consumption is outside the specified range."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 29 }

-- Trap for library TapeAlert 030
ibm3584Trap030 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 030.
                 Flag: Pass-through mechanism failure.
                 Type: C
                 Cause: Error occurred in pass-through mechanism during self test or
                        while attempting to transfer a cartridge between library modules.
                 Required host message:
                 A failure has occurred in the cartridge pass-through mechanism between
                 two library modules."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 30 }

-- Trap for library TapeAlert 031
ibm3584Trap031 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 031.
                 Flag: Cartridge in pass-through mechanism.
                 Type: C
                 Cause: Cartridge left in the pass-through mechanism between two library
                        modules.
                 Required host message:
                 A cartridge has been left in the library pass-through mechanism from a
                 previous hardware fault. Check the library users guide for instructions
                 on clearing this fault."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 31 }

-- Trap for library TapeAlert 032
ibm3584Trap032 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsHECHECQ, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsURC,     ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for library TapeAlert 032.
                 Flag: Unreadable bar code labels.
                 Type: I
                 Cause: Unable to read a bar code label on a cartridge during library
                        inventory/scan.
                 Required host message:
                 During an inventory or scan, the library was unable to read a bar code label on a cartridge.
                 1. Check for damaged, misaligned, or peeling barcode labels on the cartridge.
                 2. If no problem is found, call your IBM Service Representative."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 32 }


-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Trap definitions - Specific TapeAlert traps for the LTO tape drive
-- Range is 201 to 399

-- Trap for drive TapeAlert 001
ibm3584Trap201 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                          ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 001.
                 Flag: Read warning.
                 Type: W
                 Cause: The drive is having severe trouble reading.
                 Required host message:
                 The tape drive is having problems reading data. No data has been
                 lost, but there has been a reduction in the performance of the
                 tape."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 201 }

-- Trap for drive TapeAlert 002
ibm3584Trap202 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 002.
                 Flag: Write warning.
                 Type: W
                 Cause: The drive is having severe trouble writing.
                 Required host message:
                 The tape drive is having problems writing data. No data has been lost,
                 but there has been a reduction in the performance of the tape."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 202 }

-- Trap for drive TapeAlert 003
ibm3584Trap203 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 003.
                 Flag: Hard error.
                 Type: W
                 Cause: The drive had a hard read or write error.
                 Required host message:
                 The operation has stopped because an error has occurred while reading
                 or writing data which the drive cannot correct."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 203 }

-- Trap for drive TapeAlert 004
ibm3584Trap204 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 004.
                 Flag: Media.
                 Type: C
                 Cause: Media can no longer be written/read, or performance is severely
                        degraded.
                 Required host message:
                 Unrecoverable read, write, or positioning error that is due to a faulty tape cartridge. Replace the tape cartridge."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 204 }

-- Trap for drive TapeAlert 005
ibm3584Trap205 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 005.
                 Flag: Read failure.
                 Type: C
                 Cause: The drive can no longer read data from the tape.
                 Required host message:
                 The tape is damaged or the drive is faulty. Call the tape drive supplier
                 helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 205 }

-- Trap for drive TapeAlert 006
ibm3584Trap206 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 006.
                 Flag: Write failure.
                 Type: C
                 Cause: The drive can no longer write data to the tape.
                 Required host message:
                 The tape is from a faulty batch or the tape drive is faulty.
                 1. Use a good tape to test the drive.
                 2. If the problem persists, call the tape drive supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 206 }

-- Trap for drive TapeAlert 007
ibm3584Trap207 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 007.
                 Flag: Media life.
                 Type: W
                 Cause: The media has exceeded its specified life.
                 Required host message:
                 The tape cartridge has reached the end of its calculated useful life:
                 1. Copy any data you need to another tape.
                 2. Discard the old tape."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 207 }

-- Trap for drive TapeAlert 008
ibm3584Trap208 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 008.
                 Flag: Not data grade.
                 Type: W
                 Cause: The drive has not been able to read the MRS stripes.
                 Required host message:
                 The cartridge is not data-grade.  Any data that you write to the tape is at risk.
                 Replace the tape with a data-grade tape."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 208 }

-- Trap for drive TapeAlert 009
ibm3584Trap209 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 009.
                 Flag: Write protect.
                 Type: C
                 Cause: Write command is attempted to a write protected tape.
                 Required host message:
                 You are trying to write to a write-protected cartridge. Remove the
                 write-protection or use another tape."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 209 }

-- Trap for drive TapeAlert 010
ibm3584Trap210 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 010.
                 Flag: No removal.
                 Type: I
                 Cause: Manual or s/w unload attempted when prevent media removal on.
                 Required host message:
                 The tape drive received an UNLOAD command after the server prevented
                 the tape cartridge from being removed. Refer to the documentation for
                 your server's operating system."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 210 }

-- Trap for drive TapeAlert 011
ibm3584Trap211 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 011.
                 Flag: Cleaning media.
                 Type: I
                 Cause: Cleaning tape loaded into drive.
                 Required host message:
                 The tape in the drive is a cleaning cartridge."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 211 }

-- Trap for drive TapeAlert 012
ibm3584Trap212 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 012.
                 Flag: Unsupported format.
                 Type: I
                 Cause: Attempted load of unsupported tape format, eg DDS2 in DDS1 drive.
                 Required host message:
                 You have tried to load a cartridge of a type which is not supported by
                 the drive."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 212 }

-- Trap for drive TapeAlert 013
ibm3584Trap213 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 013.
                 Flag: Recoverable snapped tape.
                 Type: C
                 Cause: Tape snapped/cut in the drive where media can be ejected.
                 Required host message:
                 The tape has split apart. Do not attempt to extract the old tape cartridge.
                 Call your IBM Service Representative."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 213 }

-- Trap for drive TapeAlert 014
ibm3584Trap214 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 014.
                 Flag: Unrecoverable snapped tape.
                 Type: C
                 Cause: Tape snapped/cut in the drive where media cannot be ejected.
                 Required host message:
                 The operation has failed because the tape in the drive has snapped:
                 1. Do not attempt to extract the tape cartridge.
                 2. Call the tape drive supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 214 }

-- Trap for drive TapeAlert 015
ibm3584Trap215 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 015.
                 Flag: Memory chip in cartridge failure.
                 Type: W
                 Cause: Memory chip failed in cartridge.
                 Required host message:
                 The memory in the tape cartridge has failed, which reduces performance. Replace the tape cartridge.
                 If this error occurs on multiple cartridges, see Error Code 6 located in the list of drive error
                 codes in the 3584 Maintenance Information Guide."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 215 }

-- Trap for drive TapeAlert 016
ibm3584Trap216 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 016.
                 Flag: Forced eject.
                 Type: C
                 Cause: Manual or forced eject while drive actively writing or reading.
                 Required host message:
                 The operation has failed because the tape cartridge was manually ejected
                 while the tape drive was actively writing or reading."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 216 }

-- Trap for drive TapeAlert 017
ibm3584Trap217 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 017.
                 Flag: Read only format.
                 Type: W
                 Cause: Media loaded that is read-only format.
                 Required host message:
                 You have loaded a cartridge of a type that is read-only in this drive.
                 The cartridge will appear as write-protected."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 217 }

-- Trap for drive TapeAlert 018
ibm3584Trap218 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 018.
                 Flag: Tape directory corrupted on load.
                 Type: W
                 Cause: Tape drive powered down with tape loaded, or permanent error
                        prevented the tape directory being updated.
                 Required host message:
                 The drive detected that the tape directory in the cartridge memory has been
                 corrupted.  Re-read all data from the tape to rebuild the tape directory."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 218 }

-- Trap for drive TapeAlert 019
ibm3584Trap219 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 019.
                 Flag: Nearing media life.
                 Type: I
                 Cause: Media may have exceeded its specified number of passes.
                 Required host message:
                 The tape cartridge is nearing the end of its calculated life. It is
                 recommended that you:
                 1. Use another cartridge for your next backup.
                 2. Store this cartridge in a safe place in case you need to restore
                    data from it."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 219 }

-- Trap for drive TapeAlert 020
ibm3584Trap220 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 020.
                 Flag: Clean now.
                 Type: C
                 Cause: The drive thinks it has a head clog, or needs cleaning.
                 Required host message:
                 The tape drive needs cleaning:
                 1. If the operation has stopped, eject the tape and clean the drive.
                 2. If the operation has not stopped, wait for it to finish and then clean
                    the drive.
                 Check the tape drive users manual for device specific cleaning
                 instructions."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 220 }

-- Trap for drive TapeAlert 021
ibm3584Trap221 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 021.
                 Flag: Clean periodic.
                 Type: W
                 Cause: The drive is ready for a periodic clean
                 Required host message:
                 The tape drive is due for routine cleaning:
                 1. Wait for the current operation to finish.
                 2. Then use a cleaning cartridge.
                 Check the tape drive users manual for device specific cleaning
                 instructions."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 221 }

-- Trap for drive TapeAlert 022
ibm3584Trap222 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 022.
                 Flag: Expired cleaning media.
                 Type: C
                 Cause: The cleaning tape has expired.
                 Required host message:
                 The last cleaning cartridge used in the tape drive has worn out:
                 1. Discard the worn out cleaning cartridge.
                 2. Wait for the current operation to finish.
                 3. Then use a new cleaning cartridge."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 222 }

-- Trap for drive TapeAlert 023
ibm3584Trap223 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 023.
                 Flag: Invalid cleaning tape.
                 Type: C
                 Cause: Invalid cleaning tape type used.
                 Required host message:
                 The last cleaning cartridge used in the tape drive was an invalid type:
                 1. Do not use this cleaning cartridge in this drive.
                 2. Wait for the current operation to finish.
                 3. Then use a valid cleaning cartridge."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 223 }

-- Trap for drive TapeAlert 024
ibm3584Trap224 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 024.
                 Flag: Retention requested.
                 Type: W
                 Cause: The drive is having severe trouble reading or writing, which
                        will be resolved by a retention cycle.
                 Required host message:
                 The tape drive has requested a retention operation."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 224 }

-- Trap for drive TapeAlert 025
ibm3584Trap225 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 025.
                 Flag: Dual-port interface error.
                 Type: W
                 Cause: Failure of one interface port in a dual-port configuration, eg
                        Fibrechannel.
                 Required host message:
                 A redundant interface port on the tape drive has failed."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 225 }

-- Trap for drive TapeAlert 026
ibm3584Trap226 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 026.
                 Flag: Cooling fan failure.
                 Type: W
                 Cause: Fan failure inside tape drive mechanism or tape drive enclosure.
                 Required host message:
                 A tape drive cooling fan has failed."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 226 }

-- Trap for drive TapeAlert 027
ibm3584Trap227 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 027.
                 Flag: Power supply.
                 Type: W
                 Cause: Redundant PSU failure inside the tape drive enclosure or rack
                        subsystem.
                 Required host message:
                 A redundant power supply has failed inside the tape drive enclosure.
                 Check the enclosure users manual for instructions on replacing the
                 failed power supply."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 227 }

-- Trap for drive TapeAlert 028
ibm3584Trap228 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 028.
                 Flag: Power consumption.
                 Type: W
                 Cause: Power consumption of the tape drive is outside specified range.
                 Required host message:
                 The tape drive power consumption is outside the specified range."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 228 }

-- Trap for drive TapeAlert 029
ibm3584Trap229 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 029.
                 Flag: Drive maintenance.
                 Type: W
                 Cause: The drive requires preventative maintenance (not cleaning).
                 Required host message:
                 Preventative maintenance of the tape drive is required. Check the
                 tape drive users manual for device specific preventative maintenance
                 tasks or call the tape drive supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 229 }

-- Trap for drive TapeAlert 030
ibm3584Trap230 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 030.
                 Flag: Hardware A.
                 Type: C
                 Cause: The drive has a hardware fault that requires reset to recover.
                 Required host message:
                 A hardware failure has occurred that requires a tape drive reset to recover.
                 If resetting the drive does not recover the error note the single-character
                 display and see the list of drive error codes in the 3584 Maintenance Information."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 230 }

-- Trap for drive TapeAlert 031
ibm3584Trap231 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 031.
                 Flag: Hardware B.
                 Type: C
                 Cause: The drive has a hardware fault which is not read/write related
                        or requires a power cycle to recover.
                 Required host message:
                 The tape drive failed it's internal Power-On Self Test.  Note the
                 single-character display and see the list of drive error codes in
                 the 3584 Maintenance Information."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 231 }

-- Trap for drive TapeAlert 032
ibm3584Trap232 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 032.
                 Flag: Interface.
                 Type: W
                 Cause: The drive has identified an interfacing fault.
                 Required host message:
                 The tape drive has detected a problem with the SCSI,
                 Fibre or RS-422 interface. See Error Code 8 or 9
                 located in the list of drive error codes in the 3584
                 Maintenance Information."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 232 }

-- Trap for drive TapeAlert 033
ibm3584Trap233 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 033.
                 Flag: Eject media.
                 Type: C
                 Cause: Error recovery action.
                 Required host message:
                 A failure has occurred that requires you to unload
                 the cartridge from the drive. Unload the tape cartridge,
                 the reinsert it and restart the operation."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 233 }

-- Trap for drive TapeAlert 034
ibm3584Trap234 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 034.
                 Flag: Download failed.
                 Type: W
                 Cause: Firmware download failed.
                 Required host message:
                 The firmware download has failed because you have tried to use the
                 incorrect firmware for this tape drive. Obtain the correct firmware
                 and try again."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 234 }

-- Trap for drive TapeAlert 035
ibm3584Trap235 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 035.
                 Flag: Drive humidity.
                 Type: W
                 Cause: Drive humidity limits exceeded.
                 Required host message:
                 Environmental conditions inside the tape drive are outside the specified
                 humidity range."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 235 }

-- Trap for drive TapeAlert 036
ibm3584Trap236 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 036.
                 Flag: Drive temperature.
                 Type: W
                 Cause: Drive temperature limits exceeded.
                 Required host message:
                 The drive detected the temperature is exceeding the recommended temperature
                 of the library. See Error Code 1 in the list of drive error codes in the
                 3584 Maintenance Information."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 236 }

-- Trap for drive TapeAlert 037
ibm3584Trap237 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 037.
                 Flag: Drive voltage.
                 Type: W
                 Cause: Drive voltage limits exceeded.
                 Required host message:
                 The drive detected externally supplied voltages are approaching or outside
                 the specified voltage limits.  See Error Code 2 in the list of drive error
                 codes in the 3584 Maintenance Information."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 237 }

-- Trap for drive TapeAlert 038
ibm3584Trap238 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 038.
                 Flag: Predictive failure.
                 Type: C
                 Cause: Predictive failure of drive hardware.
                 Required host message:
                 A hardware failure of the tape drive is predicted. Call the tape drive
                 supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 238 }

-- Trap for drive TapeAlert 039
ibm3584Trap239 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 039.
                 Flag: Diagnostics required.
                 Type: W
                 Cause: The drive may have had a failure which may be identified by
                        stored diagnostic information or by running extended diagnostics
                        (eg SCSI Send Diagnostic).
                 Required host message:
                 The drive detected a failure that requires diagnostics for isolation.
                 See Error Code 6 located in the list of drive error codes in the 3584
                 Maintenance Information."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 239 }

-- Trap for drive TapeAlert 040
ibm3584Trap240 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 040.
                 Flag: Loader hardware A.
                 Type: C
                 Cause: Loader mechanism is having trouble communicating with the tape
                        drive.
                 Required host message:
                 The changer mechanism is having difficulty communicating with the tape
                 drive:
                 1. Turn the autoloader off then on.
                 2. Restart the operation.
                 3. If problem persists, call the tape drive supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 240 }

-- Trap for drive TapeAlert 041
ibm3584Trap241 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 041.
                 Flag: Loader stray tape.
                 Type: C
                 Cause: Stray tape left in loader after previous error recovery.
                 Required host message:
                 A tape has been left in the autoloader by a previous hardware fault:
                 1. Insert an empty magazine to clear the fault.
                 2. If the fault does not clear, turn the autoloader off then on again.
                 3. If the problem persists, call the tape drive supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 241 }

-- Trap for drive TapeAlert 042
ibm3584Trap242 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 042.
                 Flag: Loader hardware B.
                 Type: W
                 Cause: Loader mechanism has a hardware fault.
                 Required host message:
                 There is a problem with the autoloader mechanism."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 242 }

-- Trap for drive TapeAlert 043
ibm3584Trap243 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 043.
                 Flag: Loader door.
                 Type: C
                 Cause: Tape changer door open.
                 Required host message:
                 The operation failed because the autoloader door is open:
                 1. Clear any obstructions from the autoloader door.
                 2. Eject the magazine and then insert it again.
                 3. If the fault does not clear, turn the autoloader off then on again.
                 4. If the problem persists, call the tape drive supplier helpline."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 243 }

-- Trap for drive TapeAlert 044
ibm3584Trap244 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 044.
                 Flag: Loader hardware C.
                 Type: C
                 Cause: The loader mechanism has a hardware fault that is not
                        mechanically related.
                 Required host message:
                 The autoloader has a hardware fault:
                 1. Turn the autoloader off then on again.
                 2. Restart the operation.
                 3. If the problem persists, call the tape drive supplier helpline.
                 Check the autoloader users manual for device specific instructions on
                 turning the device on and off."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 244 }

-- Trap for drive TapeAlert 045
ibm3584Trap245 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 045.
                 Flag: Loader magazine.
                 Type: C
                 Cause: Loader magazine not present.
                 Required host message:
                 The autoloader cannot operate without the magazine.
                 1. Insert the magazine into the autoloader.
                 2. Restart the operation."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 245 }

-- Trap for drive TapeAlert 046
ibm3584Trap246 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 046.
                 Flag: Loader predictive failure.
                 Type: W
                 Cause: Predictive failure of loader mechanism hardware.
                 Required host message:
                 A hardware failure of the changer mechanism is predicted. Call the
                 tape drive supplier helpline."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 246 }

-- Traps 47 through 49 do not exist for the LTO tape drive...

-- Trap for drive TapeAlert 050
ibm3584Trap250 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 050.
                 Flag: Lost statistics.
                 Type: W
                 Cause: Drive or library powered down with tape loaded.
                 Required host message:
                 Media statistics have been lost at some time in the past."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 250 }

-- Trap for drive TapeAlert 051
ibm3584Trap251 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 051.
                 Flag: Tape directory invalid at unload.
                 Type: W
                 Cause: Error prevented the tape directory being updated on unload.
                 Required host message:
                 The tape directory on the tape cartridge that was previously unloaded is corrupted.
                 The file-search performance is degraded.  Use your backup software to rebuild the
                 tape directory by reading all the data."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 251 }

-- Trap for drive TapeAlert 052
ibm3584Trap252 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 052.
                 Flag: Tape system area write failure.
                 Type: C
                 Cause: Write errors while writing the system log on unload.
                 Required host message:
                 The tape cartridge that was previously unloaded could not write it's system area
                 successfully.  Copy the data to another tape cartridge, then discard the old cartridge."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 252 }

-- Trap for drive TapeAlert 053
ibm3584Trap253 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 053.
                 Flag: Tape system area read failure.
                 Type: C
                 Cause: Read errors while reading the system area on load.
                 Required host message:
                 The tape system area could not be read successfully at load time. Copy the data to
                 another tape cartridge, then discard the old cartridge."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 253 }


-- Trap for drive TapeAlert 054
ibm3584Trap254 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for LTO drive TapeAlert 054.
                 Flag: No start of data.
                 Type: C
                 Cause: Tape damaged, bulk erased, or incorrect format.
                 Required host message:
                 The start of data could not be found on the tape:
                 1. Check you are using the correct format tape.
                 2. Discard the tape or return the tape to your supplier."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 254 }
        

-- Trap for drive TapeAlert 055
ibm3584Trap255 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 055.
                 Flag: No start of data.
                 Type: C
                 Cause: Loading failure
                 Required host message:
                 The operation has failed because the media cannot
                 be loaded and threaded.
                 1. Remove the cartridge, inspect it as specified in the
                 product manual, and retry the operation.
                 2. If the problem persists, call the tape drive supplier
                 help line."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 255 }


-- Trap for drive TapeAlert 056
ibm3584Trap256 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 056.
                 Flag: Unrecoverable unload failure.
                 Type: C
                 The operation has failed because the medium cannot
                 be unloaded:
                 1. Do not attempt to extract the tape cartridge.
                 2. Call the tape driver supplier help line."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 256 }

-- Trap for drive TapeAlert 057
ibm3584Trap257 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 057.
                 Flag: Automation interface failure.
                 Type: C
                 The tape drive has a problem with the automation
                 interface:
                 1. Check the power to the automation system.
                 2. Check the cables and cable connections.
                 3. Call the supplier help line if problem persists."
                 --#SEVERITY CRITICAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 257 }

-- Trap for drive TapeAlert 058
ibm3584Trap258 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 058.
                 Flag: Firmware failure.
                 Type: W
                 The tape drive has reset itself due to a detected firmware
                 fault. If problem persists, call the supplier help
                 line."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 258 }

-- Trap for drive TapeAlert 059
ibm3584Trap259 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 059.
                 Flag: WORM Medium - Integrity Check Failed
                 Type: W
                 The tape drive has detected an inconsistency during
                 the WORM medium integrity checks. Someone may
                 have tampered with the cartridge."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 259 }
        
-- Trap for drive TapeAlert 060
ibm3584Trap260 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsSKASCASCQ,
                          ibm3584MIBObjectsTA,  ibm3584MIBObjectsURC,   ibm3584MIBObjectsFSC,
                          ibm3584MIBObjectsSCD, ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsFFFD,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                           ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for drive TapeAlert 060.
                 Flag: WORM Medium - Overwrite Attempted
                 Type: W
                 An attempt had been made to overwrite user data on
                 a WORM medium:
                 1. If a WORM medium was used inadvertently, replace
                 it with a normal data medium.
                 2. If a WORM medium was used intentionally:
                 a) check that the software application is compatible
                 with the WORM medium format you are using.
                 b) check that the medium is bar-coded correctly for
                 WORM."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 260 }
        



-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Trap definitions - Non TapeAlert traps
-- Range is 401 to 599

-- Trap for non TapeAlert 001
ibm3584Trap401 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 001.
                 Cause: I/O station full.
                 Required host message:
                 The Import/Export station is full."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 401 }

-- Trap for non TapeAlert 002
ibm3584Trap402 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 002.
                 Cause: All cartridge slots in the associated logical library are occupied.
                 Required host message:
                 The logical library is full."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 402 }

-- Trap for non TapeAlert 003
ibm3584Trap403 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 003.
                 Cause: Library out of LTO cleaning cartridges.
                 Required host message:
                 The library is out of LTO cleaning cartridges."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 403 }

-- Trap for non TapeAlert 004
ibm3584Trap404 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 004.
                 Cause: Library out of DLT cleaning cartridges.
                 Required host message:
                 The library is out of DLT cleaning cartridges."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 404 }

-- Trap for non TapeAlert 005
ibm3584Trap405 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 005.
                 Cause: I/O station door has been open for five minutes.
                 Required host message:
                 The Import/Export station has been open for five minutes."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 405 }

-- Trap for non TapeAlert 006
ibm3584Trap406 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 006.
                 Cause: LTO cleaning cartridge expired.
                 Required host message:
                 An LTO cleaning cartridge has expired."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 406 }

-- Trap for non TapeAlert 007
ibm3584Trap407 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 007.
                 Cause: DLT cleaning cartridge expired.
                 Required host message:
                 A DLT cleaning cartridge has expired."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 407 }
-- Trap for non TapeAlert 008
ibm3584Trap408 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 008.
                 Cause: Test initiated from operator panel.
                 Required host message:
                 This is a test SNMP trap."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 408 }

-- Trap for non TapeAlert 009
ibm3584Trap409 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 009.
                 Cause: Library out of 3592 Enterprise Tape cleaning cartridges.
                 Required host message:
                 The library is out of Enterprise Tape cleaning cartridges."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 409 }

-- Trap for non TapeAlert 010
ibm3584Trap410 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 010.
                 Cause: 3593 Enterprise Tape cleaning cartridge expired.
                 Required host message:
                 A Enterprise Tape cleaning cartridge has expired."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 410 }

-- Trap for non TapeAlert 011
ibm3584Trap411 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 011.
                 Cause: LTO slots in the library are approaching full capacity.
                 Required host message:
                 LTO slots are approaching full capacity.  The library may not be able to import all cartridge in the IO station."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 411 }

-- Trap for non TapeAlert 012
ibm3584Trap412 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 012.
                 Cause: 3592 Enterprise Tape slots in the library are approaching full capacity.
                 Required host message:
                 3592 Enterprise Tape slots are approaching full capacity.  The library may not be able to import all cartridge in the IO station."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 412 }

-- Trap for non TapeAlert 013
ibm3584Trap413 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsWWNN, 
                          ibm3584MIBObjectsEA, ibm3584MIBObjectsDrvSN,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 013.
                 Cause: A host attempted a move to/from a shared drive that was in use by a different host on a different logaical library.
                 Required host message:
                 A Host attempted motion to/from a shared drive. The command failed because the shared drive was being used by another logical library."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 413 }

-- Trap for non TapeAlert 014
ibm3584Trap414 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 014.
                 Cause: The library had a problem reading the cartridge with the barcode indicated.  The cartridge label may be scratched or smeared.
                 Required host message:
                 The library had a problem reading the barcode of a cartridge.  Please inspect the label for scratches or smears."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 414 }

-- Trap for non TapeAlert 015
ibm3584Trap415 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                "Trap for non TapeAlert 015.
                 Cause: The library attempted to call home, but was unsuccessful.
                 Required host message:
                 The library attempted to call home, but was unsuccessful."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::= { ibm3584MIBTraps 415 }
-- Trap for non TapeAlert 016
ibm3584Trap416 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 016.
                 Cause: The associated logical library is nearing full capacity.
                 Required host message:
                 The logical library is almost full."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 416 }
-- Trap for non TapeAlert 017
ibm3584Trap417 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 017.
                 Cause: All 3592 Enterprise Tape slots in the library are allocated to a cartridge.
                 Required host message:
                 All physical 3592 Enterprise Tape slots are allocated.  No more 3592 Enterprise Tape cartridges may be added at this time."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 417 }
-- Trap for non TapeAlert 018
ibm3584Trap418 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD,      ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 018.
                 Cause: All LTO Tape slots in the library are allocated to a cartridge.
                 Required host message:
                 All physical LTO Tape slots are allocated.  No more LTO Tape cartridges may be added at this time."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 418 }
-- Trap for non TapeAlert 019
ibm3584Trap419 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 019.
                 Cause: Unable to reach associated EKM.
                 Required host message:
                 Library is unable to communicate with the associated EKM address."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 419 }
-- Trap for non TapeAlert 020
ibm3584Trap420 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsTD, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 020.
                 Cause: A cartridge that can not be encrypted has is loaded in a drives that is used to encrypt all cartridges.
                 Required host message:
                 A cartridge that can not be encrypted has been loaded into a drive that is setup for encryption."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 420 }
ibm3584Trap421 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsVOLSER,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 021.
                 Cause: A new unassigned cartridge is in the library.
                 Required host message:
                 A new cartridge is in the library and is currently unassigned."
                 --#SEVERITY WARNING
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 421 }
ibm3584Trap422 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA,
                          ibm3584MIBObjectsTD, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 022.
                 Cause: All library doors are closed.  The library will inventory and resume operations.
                 Required host message:
                 All library doors have been closed.  The library will now inventory and resume operations."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 422 }        
ibm3584Trap440 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD,
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                        }
        STATUS          current                  
        DESCRIPTION
                 "Trap for non TapeAlert 040.
                 Cause: A user has successfully logged in to the web or operator panel.
                 Required host message:
                 A user has successfully logged into the Web Specialist or Operator Panel."
                 --#SEVERITY AUTHENTICATION
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 440 }
-- Trap for non TapeAlert 041
ibm3584Trap441 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD,
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                          }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 041.
                 Cause: A user has attempted to log in to the web or operator panel and was unsuccessful.
                 Required host message:
                 A user has attempted to log in to the Web Specialist or Operator Panel and was unsuccessful after 3 attempts."
                 --#SEVERITY AUTHENTICATION
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 441 }
-- Trap for non TapeAlert 042
ibm3584Trap442 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD,
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                          }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 042.
                 Cause: A user has logged out of the web or operator panel.
                 Required host message:
                 A user has logged out of the Web Specialist or Operator Panel."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 442 }
-- Trap for non TapeAlert 043
ibm3584Trap443 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD,
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                          }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 043.
                 Cause: A library configuration setting has been changed.
                 Required host message:
                 A library configuration setting has been changed via the Op Panel or Web Specialist."
                 --#SEVERITY CONFIGURATION CHANGE
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 443 }
-- Trap for non TapeAlert 044
ibm3584Trap444 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 044.
                 Cause: A logical library configuration setting has been changed.
                 Required host message:
                 A logical library configuration setting has been changed."
                 --#SEVERITY CONFIGURATION CHANGE
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 444 }
-- Trap for non TapeAlert 045
ibm3584Trap445 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsWWNN, ibm3584MIBObjectsEA, ibm3584MIBObjectsDrvSN, ibm3584MIBObjectsSeverity 
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 045.
                 Cause: A tape drive configuration setting has been changed.
                 Required host message:
                 A tape drive configuration setting has been changed."
                 --#SEVERITY CONFIGURATION CHANGE
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 445 }
-- Trap for non TapeAlert 046
ibm3584Trap446 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsVOLSER, ibm3584MIBObjectsEA, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 046.
                 Cause: A cartridge has been modified from an external UI.
                 Required host message:
                 A cartridge has been modified from the Web Specialist or Op Panel."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 446 }
-- Trap for non TapeAlert 047
ibm3584Trap447 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 047.
                 Cause: A library or drive code load has been initiated from an external UI.
                 Required host message:
                 A library or drive code load has been initiated from the Web Specialist, Op Panel, or CE Tool."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 447 }
-- Trap for non TapeAlert 048
ibm3584Trap448 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 048.
                 Cause: An Accessor FRU been prepared/finished.
                 Required host message:
                 An Accessor has been placed in the prepared or finished Accessor service state."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 448 }
-- Trap for non TapeAlert 049
ibm3584Trap449 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL,
                          ibm3584MIBObjectsWWNN, ibm3584MIBObjectsEA, ibm3584MIBObjectsSeverity 
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 049.
                 Cause: A drive FRU been prepared/finished.
                 Required host message:
                 A drive has been placed in the prepared or finished Accessor service state."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 449 }
-- Trap for non TapeAlert 050
ibm3584Trap450 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL, 
                          ibm3584MIBObjectsWWNN, ibm3584MIBObjectsEA, ibm3584MIBObjectsDrvSN, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 050.
                 Cause: A drive Serial number has changed.
                 Required host message:
                 A drive Serial number has changed."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 450 }
-- Trap for non TapeAlert 051
ibm3584Trap451 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, 
                          ibm3584MIBObjectsUserID, ibm3584MIBObjectsLL, 
                          ibm3584MIBObjectsWWNN, ibm3584MIBObjectsEA, ibm3584MIBObjectsDrvSN, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 051.
                 Cause: A drive has been power cycled from the web UI.
                 Required host message:
                 A drive has been power cycled via the Web Specialist."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 451 }
-- Trap for non TapeAlert 052
ibm3584Trap452 NOTIFICATION-TYPE
        OBJECTS         { ibm3584MIBObjectsMTMNLSN, ibm3584MIBObjectsTA, ibm3584MIBObjectsTD, ibm3584MIBObjectsUserID, ibm3584MIBObjectsSeverity
                        }
        STATUS          current
        DESCRIPTION
                 "Trap for non TapeAlert 052.
                 Cause: A node card has been reset from the web UI.
                 Required host message:
                 A node card has been reset via the Web Specialist."
                 --#SEVERITY INFORMATIONAL
                 --#SOURCE_ID "T"
                 --#CATEGORY "Error Events"
        ::=  { ibm3584MIBTraps 452 }


-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Conformance information (mandatory)


ibm3584MIBCompliances OBJECT IDENTIFIER ::= { ibm3584MIBConformance 1 }
ibm3584MIBGroups      OBJECT IDENTIFIER ::= { ibm3584MIBConformance 2 }

-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- Compliance statements

ibm3584MIBCompliance MODULE-COMPLIANCE
      STATUS current
      DESCRIPTION
          "The compliance statement for the SNMP entities that
          implement this MIB."

      MODULE -- this module

--    Unconditionally mandatory groups
      MANDATORY-GROUPS  { ibm3584MIBNotificationsGroup1,
                          ibm3584MIBObjectsGroup
                        }
      ::= { ibm3584MIBCompliances 1 }

-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
-- MIB groupings
-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

-- Traps used in the MIB
ibm3584MIBNotificationsGroup1 NOTIFICATION-GROUP
      NOTIFICATIONS
         { ibm3584Trap001,
           ibm3584Trap002,
           ibm3584Trap003,
           ibm3584Trap004,
           ibm3584Trap005,
           ibm3584Trap006,
           ibm3584Trap007,
           ibm3584Trap008,
           ibm3584Trap009,
           ibm3584Trap010,
           ibm3584Trap011,
           ibm3584Trap012,
           ibm3584Trap013,
           ibm3584Trap014,
           ibm3584Trap015,
           ibm3584Trap016,
           ibm3584Trap017,
           ibm3584Trap018,
           ibm3584Trap019,
           ibm3584Trap020,
           ibm3584Trap021,
           ibm3584Trap022,
           ibm3584Trap023,
           ibm3584Trap024,
           ibm3584Trap025,
           ibm3584Trap026,
           ibm3584Trap027,
           ibm3584Trap028,
           ibm3584Trap029,
           ibm3584Trap030,
           ibm3584Trap031,
           ibm3584Trap032,
           ibm3584Trap201,
           ibm3584Trap202,
           ibm3584Trap203,
           ibm3584Trap204,
           ibm3584Trap205,
           ibm3584Trap206,
           ibm3584Trap207,
           ibm3584Trap208,
           ibm3584Trap209,
           ibm3584Trap210,
           ibm3584Trap211,
           ibm3584Trap212,
           ibm3584Trap213,
           ibm3584Trap214,
           ibm3584Trap215,
           ibm3584Trap216,
           ibm3584Trap217,
           ibm3584Trap218,
           ibm3584Trap219,
           ibm3584Trap220,
           ibm3584Trap221,
           ibm3584Trap222,
           ibm3584Trap223,
           ibm3584Trap224,
           ibm3584Trap225,
           ibm3584Trap226,
           ibm3584Trap227,
           ibm3584Trap228,
           ibm3584Trap229,
           ibm3584Trap230,
           ibm3584Trap231,
           ibm3584Trap232,
           ibm3584Trap233,
           ibm3584Trap234,
           ibm3584Trap235,
           ibm3584Trap236,
           ibm3584Trap237,
           ibm3584Trap238,
           ibm3584Trap239,
           ibm3584Trap240,
           ibm3584Trap241,
           ibm3584Trap242,
           ibm3584Trap243,
           ibm3584Trap244,
           ibm3584Trap245,
           ibm3584Trap246,
           ibm3584Trap250,
           ibm3584Trap251,
           ibm3584Trap252,
           ibm3584Trap253,
           ibm3584Trap254,
           ibm3584Trap255,
           ibm3584Trap256,
           ibm3584Trap257,
           ibm3584Trap258,
           ibm3584Trap259,
           ibm3584Trap260,
           ibm3584Trap401,
           ibm3584Trap402,
           ibm3584Trap403,
           ibm3584Trap404,
           ibm3584Trap405,
           ibm3584Trap406,
           ibm3584Trap407,
           ibm3584Trap408,
           ibm3584Trap409,
           ibm3584Trap410,
           ibm3584Trap411,
           ibm3584Trap412,
           ibm3584Trap413,
           ibm3584Trap414,
           ibm3584Trap415,
           ibm3584Trap416,
           ibm3584Trap417,
           ibm3584Trap418,
           ibm3584Trap419,
           ibm3584Trap420,
           ibm3584Trap421,
           ibm3584Trap422,
           ibm3584Trap440,
           ibm3584Trap441,
           ibm3584Trap442,
           ibm3584Trap443,
           ibm3584Trap444,
           ibm3584Trap445,
           ibm3584Trap446,
           ibm3584Trap447,
           ibm3584Trap448,
           ibm3584Trap449,
           ibm3584Trap450,
           ibm3584Trap451,
           ibm3584Trap452
         }
      STATUS current
      DESCRIPTION
          "Mandatory notification for entities implemented in this MIB."
      ::= { ibm3584MIBGroups 1 }

-- Objects used in the traps
ibm3584MIBObjectsGroup OBJECT-GROUP
      OBJECTS
         { ibm3584MIBObjectsMTMNLSN,
           ibm3584MIBObjectsSKASCASCQ,
           ibm3584MIBObjectsHECHECQ,
           ibm3584MIBObjectsTA,
           ibm3584MIBObjectsURC,
           ibm3584MIBObjectsFFFD,
           ibm3584MIBObjectsTD,
           ibm3584MIBObjectsFSC,
           ibm3584MIBObjectsSCD,
           ibm3584MIBObjectsVOLSER,
           ibm3584MIBObjectsLL,
           ibm3584MIBObjectsWWNN,
           ibm3584MIBObjectsEA,
           ibm3584MIBObjectsDrvSN,
           ibm3584MIBObjectsSeverity
         }
      STATUS current
      DESCRIPTION
          "Mandatory objects for entities implemented in this MIB."
      ::= { ibm3584MIBGroups 3 }



END
