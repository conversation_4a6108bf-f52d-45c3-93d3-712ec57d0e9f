
-- *****************************************************************************
-- Juniper-System-Clock-CONF
--
-- SNMP Agent Capabilities definitions for the System Clock MIB.
--
-- Copyright (c) 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003 Juniper Networks, Inc.
--   All rights reserved.
-- *****************************************************************************

Juniper-System-Clock-CONF  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    AGENT-CAPABILITIES
        FROM SNMPv2-CONF
    juniAgents
        FROM Juniper-Agents;

juniSysClockAgent  MODULE-IDENTITY
    LAST-UPDATED "200512141401Z"  -- 14-Dec-05 10:01 AM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        E-mail: <EMAIL>"
    DESCRIPTION
        "The agent capabilities definitions for the system clock component of
        the SNMP agent in the Juniper E-series family of products."
    -- Revision History
    REVISION    "200512141401Z"  -- 14-Dec-05 10:01 AM EDT  - JUNOSe 7.0
    DESCRIPTION
        "Added rsNtpPeerLastUpdateTime to Peer Table."
    REVISION    "200309151403Z"  -- 15-Sep-03 10:03 AM EDT  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names."
    REVISION    "200309121439Z"  -- 12-Sep-03 10:39 AM EDT  - JUNOSe 4.1
    DESCRIPTION
        "Added an indicator to stratum number that no stratum is set.
         Added traps for significant NTP state changes.
         Added replacement clock offset and frequency error objects with
         DisplaySting syntax."
    REVISION    "200204041847Z"  -- 04-Apr-02 01:47 PM EST  - JUNOSe 4.0
    DESCRIPTION
        "The initial release of this management information module."
    ::= { juniAgents 52 }


-- *****************************************************************************
-- System Clock SNMP Agent Capabilities definitions
-- *****************************************************************************
juniSysClockAgentV1  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 1 of the system clock component of the JUNOSe SNMP agent.  This
        version of the system clock component was supported in JUNOSe 4.0 system
        releases."
    STATUS      obsolete
    DESCRIPTION
        "The MIB supported by the SNMP agent for the system clock application in
        JUNOSe.  These capabilities became obsolete when NTP trap support was
        added."
    SUPPORTS    Juniper-System-Clock-MIB
        INCLUDES {
            juniSysClockTimeGroup,
            juniSysClockDstGroup,
            juniNtpSysClockGroup,
            juniNtpClientGroup,
            juniNtpServerGroup,
            juniNtpPeersGroup,
            juniNtpAccessGroupGroup }
    ::= { juniSysClockAgent 1 }

juniSysClockAgentV2  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 2 of the system clock component of the JUNOSe SNMP agent.  This
        version of the system clock component was supported in early JUNOSe 4.1
        and 5.0 system releases."
    STATUS      obsolete
    DESCRIPTION
        "The MIB supported by the SNMP agent for the system clock application in
        JUNOSe.  These capabilities became obsolete when new frequency and
        offset error obejects support were added."
    SUPPORTS    Juniper-System-Clock-MIB
        INCLUDES {
            juniSysClockTimeGroup,
            juniSysClockDstGroup,
            juniNtpSysClockGroup,
            juniNtpClientGroup,
            juniNtpServerGroup,
            juniNtpPeersGroup,
            juniNtpAccessGroupGroup,
            juniNtpNotificationGroup }
    ::= { juniSysClockAgent 2 }

juniSysClockAgentV3  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 3 of the system clock component of the JUNOSe SNMP agent.  This
        version of the system clock component is supported in JUNOSe 4.1.2,
        5.0.2 and subsequent system releases."
    STATUS      obsolete
    DESCRIPTION
        "The MIB supported by the SNMP agent for the system clock application in
        JUNOSe." 
    SUPPORTS    Juniper-System-Clock-MIB
        INCLUDES {
            juniSysClockTimeGroup,
            juniSysClockDstGroup,
            juniNtpSysClockGroup2,
            juniNtpClientGroup,
            juniNtpServerGroup,
            juniNtpPeersGroup,
            juniNtpAccessGroupGroup,
            juniNtpNotificationGroup,
            juniNtpSysClockDeprecatedGroup }
    ::= { juniSysClockAgent 3 }
    
juniSysClockAgentV4  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 4 of the system clock component of the JUNOSe SNMP agent.  This
        version of the system clock component is supported in JUNOSe 7.0 and 
        subsequent system releases."
    STATUS      current
    DESCRIPTION
        "The MIB supported by the SNMP agent for the system clock application in
        JUNOSe." 
    SUPPORTS    Juniper-System-Clock-MIB
        INCLUDES {
            juniSysClockTimeGroup,
            juniSysClockDstGroup,
            juniNtpSysClockGroup2,
            juniNtpClientGroup,
            juniNtpServerGroup,
            juniNtpPeersGroup1,
            juniNtpAccessGroupGroup,
            juniNtpNotificationGroup,
            juniNtpSysClockDeprecatedGroup }
    ::= { juniSysClockAgent 4 }    

END
