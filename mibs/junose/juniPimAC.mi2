
-- *****************************************************************************
-- Juniper-PIM-CONF
--
-- SNMP Agent Capabilities definitions for the PIM MIBs.
--
-- Copyright (c) 2001 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003 Juniper Networks, Inc.
--   All rights reserved.
-- *****************************************************************************

Juniper-PIM-CONF  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    AGENT-CAPABILITIES
        FROM SNMPv2-CONF
    juniAgents
        FROM Juniper-Agents;

juniPimAgent  MODULE-IDENTITY
    LAST-UPDATED "200209061654Z"  -- 06-Sep-02 12:54 PM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        E-mail: <EMAIL>"
    DESCRIPTION
        "The agent capabilities definitions for the Protocol Independent
        Multicast (PIM) component of the SNMP agent in the Juniper E-series
        family of products."
    -- Revision History
    REVISION    "200209061654Z"  -- 06-Sep-02 12:54 PM EDT  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names."
    REVISION    "200111152238Z"  -- 15-Nov-01 05:38 PM EST  - JUNOSe 3.2
    DESCRIPTION
        "The initial release of this management information module."
    ::= { juniAgents 29 }


-- *****************************************************************************
-- PIM SNMP Agent Capabilities definitions
-- *****************************************************************************
juniPimAgentV1  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 1 of the PIM component of the JUNOSe SNMP agent.  This version
        of the PIM component is supported in JUNOSe 3.0 and subsequent system
        releases."
    STATUS      current
    DESCRIPTION
        "The MIBs supported by the SNMP agent for the PIM application in
        JUNOSe."
    SUPPORTS    PIM-MIB
        INCLUDES {
            pimV2MIBGroup,
            pimDenseV2MIBGroup,
            pimV2CandidateRPMIBGroup,
            pimV1MIBGroup,
            pimNextHopGroup,
            pimAssertGroup }
    SUPPORTS    Juniper-PIM-MIB
        INCLUDES {
            juniPimGeneralGroup,
            juniPimInterfaceGroup,
            juniPimMRouteConfGroup,
            juniPimMRouteNextHopGroup,
            juniPimRPSetGroup,
            juniPimStaticRPConfGroup,
            juniPimAutoRPConfGroup,
            juniPimAutoRPCandGroup,
            juniPimComponentGroup,
            juniPimUnicastRouteGroup,
            juniPimSPTThresholdGroup }
    ::= { juniPimAgent 1 }

END
