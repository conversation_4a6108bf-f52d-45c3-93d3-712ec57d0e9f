
-- *****************************************************************************
-- Juniper-VRRP-CONF
--
-- SNMP Agent Capabilities definitions for the VRRP MIB.
--
-- Copyright (c) 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003 Juniper Networks, Inc.
--   All rights reserved.
-- *****************************************************************************

Juniper-VRRP-CONF  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    AGENT-CAPABILITIES
        FROM SNMPv2-CONF
    juniAgents
        FROM Juniper-Agents;

juniVrrpAgent  MODULE-IDENTITY
    LAST-UPDATED "200209061654Z"  -- 06-Sep-02 12:54 PM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        E-mail: <EMAIL>"
    DESCRIPTION
        "The agent capabilities definitions for the Virtual Router Redundancy
        Protocol (VRRP) component of the SNMP agent in the Juniper E-series
        family of products."
    -- Revision History
    REVISION    "200209061654Z"  -- 06-Sep-02 12:54 PM EDT  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names."
    REVISION    "200201241520Z"  -- 24-Jan-02 10:20 AM EST  - JUNOSe 3.4
    DESCRIPTION
        "The initial release of this management information module."
    ::= { juniAgents 53 }


-- *****************************************************************************
-- VRRP SNMP Agent Capabilities definitions
-- *****************************************************************************
juniVrrpAgentV1  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 1 of the VRRP component of the JUNOSe SNMP agent.  This version
        of the VRRP component is supported in JUNOSe 3.4 and subsequent system
        releases."
    STATUS      current
    DESCRIPTION
        "The MIB supported by the SNMP agent for the VRRP application in
        JUNOSe."
    SUPPORTS    VRRP-MIB
        INCLUDES {
            vrrpOperGroup,
            vrrpStatsGroup,
            vrrpTrapGroup,
            vrrpNotificationGroup }
    ::= { juniVrrpAgent 1 }

END
