
-- *****************************************************************************
-- Juniper-BGP-MIB
--
-- Juniper Networks Enterprise MIB
--   Extensions for BGP Protocol Management
--
-- Copyright (c) 2000, 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003, 2004, 2005, 2007 Juniper Networks, Inc.
--   All Rights Reserved.
-- *****************************************************************************

Juniper-BGP-MIB  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Unsigned32, IpAddress, Integer32, Counter32,
    Gauge32
        FROM SNMPv2-<PERSON><PERSON>
    DisplayString, TruthValue, RowStatus, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
    juniMibs
        FROM Juniper-MIBs
    JuniVrfName
        FROM Juniper-TC;

juniBgpMIB  MODULE-IDENTITY
    LAST-UPDATED "200705110517Z"  -- 11-May-07 01:17 AM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        Email:  <EMAIL>"
    DESCRIPTION
        "The BGP Protocol MIB for the Juniper Networks enterprise."
    -- Revision History
    REVISION    "200705110517Z"  -- 11-May-07 01:17 AM EDT  - JUNOSe 9.0
    DESCRIPTION
        "Added support for BGP conditional advertisement"
    REVISION    "200605151924Z"  -- 15-May-06 03:24 PM EDT  - JUNOSe 8.0
    DESCRIPTION
        "Added support for BGP over TCPv6."
    REVISION    "200512292137Z"  -- 29-Dec-05 04:37 PM EST  - JUNOSe 6.0
    DESCRIPTION
        "Changed DEFVAL for for juniBgpDefaultIPv4Unicast, 
         juniBgpAutomaticRouteTargetFilter, juniBgpGlobalRibOutEnabled
         juniBgpEnabled and changed range for 
         juniBgpGlobalConfigKeepAliveInterval."
    REVISION    "200510051846Z"  -- 05-Oct-05 02:46 PM EDT  - JUNOSe 7.2
    DESCRIPTION
        "Added support for BFD."
    REVISION    "200510031846Z"  -- 03-Oct-05 02:46 PM EDT  - JUNOSe 7.1
    DESCRIPTION
        "Obsoleted Storage group.
         Added the next-hop-unchanged attribute to the peer address-family 
         and peer-group address-family.
         Obsoleted the address-family IP and IP-service profile attributes.
         Removed the Carrier's Carriers flag in the VRF."
    REVISION    "200407061846Z"  -- 06-Jul-04 02:46 PM EDT  - JUNOSe 6.0
    DESCRIPTION
        "Added MIB support for graceful restart."
    REVISION    "200405261924Z"  -- 26-May-04 03:24 PM EDT  - JUNOSe 5.3 
    DESCRIPTION
        "Changed juniBgpVrfOperationalState and 
         juniBgpAddressFamilyOperationalState values to 
         be the same as the values for juniBgpOperationalState.
         Added support for route-map option in default-information originate and
         neighbor ... default-originate.
         Added support for IP profiles and IP service-profiles to be used by BGP
         when creating IP dynamic interfaces over MPLS tunnels.
         Added support for new dynamic capability negotiation draft.
         Added support of passive and promiscuous peers."
    REVISION    "200405261924Z"  -- 26-May-04 03:24 PM EDT  - JUNOSe 5.2 
    DESCRIPTION
        "Changed juniBgpVrfOperationalState and 
         juniBgpAddressFamilyOperationalState values to 
         be the same as the values for juniBgpOperationalState.
         Added support for send-label.
         Added support for carrier's carrier feature for BGP/MPLS VPN.
         Added support for check-vpn-next-hops."
    REVISION    "200405261924Z"  -- 26-May-04 03:24 PM EDT  - JUNOSe 5.1 
    DESCRIPTION
        "Changed juniBgpVrfOperationalState and 
         juniBgpAddressFamilyOperationalState values to 
         be the same as the values for juniBgpOperationalState.
         Added support for maximum-paths eiBGP.
         Added support for bgpIpV6 to JuniBgpAfi.
         Replaced VRF distance objects with address family distance objects.
         Changed default values for VRF maximum paths.
         Obsoleted storage heap size objects.
         Added support for leaked flag attribute of the BGP route."
    REVISION    "200405261924Z"  -- 26-May-04 03:24 PM EDT  - JUNOSe 5.0 
    DESCRIPTION
        "Changed juniBgpVrfOperationalState and 
         juniBgpAddressFamilyOperationalState values to 
         be the same as the values for juniBgpOperationalState.
         Replaced Unisphere names with Juniper names.
         Added support for neighbor site-of-origin.
         Added support for juniBgpPeerLenient.
         Added support for juniBgpPeerGroupLenient.
         Extended range for maximum paths from 0..6 to 0..16."
    REVISION    "200405261924Z"  -- 26-May-04 03:24 PM EDT  - JUNOSe 4.1
    DESCRIPTION
        "Changed juniBgpVrfOperationalState and 
         juniBgpAddressFamilyOperationalState values to 
         be the same as the values for juniBgpOperationalState.
         Added support for four-octet AS-numbers.
         Deprecated support for two-octet AS-numbers.
         Added support for dynamic capability negotiation.
         Renamed capability objects:
            juniBgpPeerSupportsCapabilityNegotiation,
            juniBgpPeerCapabilityMultiProtocol,
            juniBgpPeerCapabilityRouteRefresh and
            juniBgpPeerCapabilityRouteRefreshCiscoProprietary
         to
            juniBgpPeerReceivedCapabilitiesOption,
            juniBgpPeerReceivedCapabilityMultiProtocol,
            juniBgpPeerReceivedCapabilityRouteRefresh and
            juniBgpPeerReceivedCapabilityRouteRefreshCisco
         respectively.
         Added support for iBGP multipath.
         Obsoleted juniBgpEqualCostLimit.
         Added support for confederation peers filter-list.
         Added support for juniBgpPeerAddressFamilyMaximumPrefixStrict.
         Added support for juniBgpPeerGroupAddressFamilyMaximumPrefixStrict.
         Added support for juniBgpNewRouteMplsInLabel and
            juniBgpNewRouteMplsOutLabel.
         Obsoleted juniBgpNewRouteMplsLabel."
    REVISION    "200208311822Z"  -- 31-Aug-02 02:22 PM EDT  - JUNOSe 4.0
    DESCRIPTION
        "Added support for BGP internal redistribute.
         Obsoleted juniBgpStorageInitialHistoryRoutePoolSize and
         juniBgpStorageMaxHistoryRoutePoolSize.
         Added the ability to unconfigure BGP attributes from the MIB."
    REVISION    "200203011654Z"  -- 01-Mar-02 11:54 AM EST  - JUNOSe 3.5
    DESCRIPTION
        "Added support for adding unicast BGP routes into a multicast view."
    REVISION    "200201231316Z"  -- 23-Jan-02 01:16 PM EST  - JUNOSe 3.4
    DESCRIPTION
        "Added support for peer and peer-group local-as."
    REVISION    "200112041523Z"  -- 04-Dec-01 10:23 AM EST  - JUNOSe 3.3
    DESCRIPTION
        "Replaced the route tables to add original route destination as an
        index:
          juniBgpRouteTable                  > juniBgpNewRouteTable
          juniBgpRouteFlapHistoryTable       > juniBgpNewRouteFlapHistoryTable
          juniBgpRouteCommunityTable         > juniBgpNewRouteCommunityTable
          juniBgpRouteExtendedCommunityTable >
              juniBgpNewRouteExtendedCommunityTable
          juniBgpRouteClusterIdTable         > juniBgpNewRouteClusterIdTable "
    REVISION    "200111302220Z"  -- 30-Nov-01 05:20 PM EST  - JUNOSe 3.2
    DESCRIPTION
        "Added support for BGP default IPv4 unicast."
    REVISION    "200106181859Z"  -- 18-Jun-01 02:59 PM EDT  - JUNOSe 3.0
    DESCRIPTION
        "Redesigned version of this MIB module."
    REVISION    "200001120000Z"  -- 12-Jan-00               - JUNOSe 1.3
    DESCRIPTION
        "Initial version of this MIB module."
    ::= { juniMibs 29 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Textual conventions
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
JuniBgpAfi ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Address family identifier (AFI)."
    SYNTAX      INTEGER {
                    bgpIpV4(1),
                    bgpIpV6(2) }

JuniBgpSafi ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Subsequent address family identifier (SAFI)."
    SYNTAX      INTEGER {
                    bgpUnicast(1),
                    bgpMulticast(2),
                    bgpUnicastMulticast(3),
                    bgpVPNUnicast(128) }

JuniBgpStorageInteger ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies an unsigned integer.  If an object with this type is set
        while BGP is created, the value will not be used until a reboot occurs."
    SYNTAX      Unsigned32

JuniBgpResetConnectionType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Writing a value to an object of this type will cause the corresponding
        reset action to take place:
            resetTypeNoop                       - no action (this value is
                                                  always returned on a read)
            resetTypeHard                       - causes a BGP session to be
                                                  dropped and then
                                                  re-established
            resetTypeSoftIn                     - causes inbound policy to be
                                                  re-applied to received routes
            resetTypeSoftOut                    - causes outbound policy to be
                                                  re-applied to sent routes
            resetTypeSoftInOut                  - does both resetTypeSoftIn and
                                                  resetTypeSoftOut
            resetTypeRouteFlapHistory           - causes route-flap dampening
                                                  history to be discarded
            resetTypeSoftInWithPrefixOrfPush    - causes BGP to push out
                                                  prefix-list ORF and perform
                                                  inbound soft reconfiguration
            resetTypeWaitEndOfRib               - causes BGP to stop waiting for
                                                  the End-of-RIB flag from the
                                                  peer(s)
            resetTypeRecreateAllIpDynInterfaces - causes all dynamically created
                                                  IP interfaces to be recreated
            resetTypeDynamicPeers               - causes dynamically create
                                                  peers to be removed "
    SYNTAX      INTEGER    {
                    resetTypeNoop(0),
                    resetTypeHard(1),
                    resetTypeSoftIn(2),
                    resetTypeSoftOut(3),
                    resetTypeSoftInOut(4),
                    resetTypeRouteFlapHistory(5),
                    resetTypeSoftInWithPrefixOrfPush(6),
                    resetTypeWaitEndOfRib(7),
                    resetTypeRecreateAllIpDynInterfaces(8),
                    resetTypeDynamicPeers(9) }

JuniBgpFourOctetAsNumber ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Four-octet AS-number."
    SYNTAX      Unsigned32

JuniBgpAdvertiseMapName ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "32a"
    STATUS      current
    DESCRIPTION
        "Advertise route-map name.  Represents textual information taken from the NVT
        ASCII character set."
    REFERENCE
        "RFC 854: NVT ASCII character set.  See SNMPv2-TC.DisplayString
        DESCRIPTION for a summary."
    SYNTAX      OCTET STRING (SIZE(0..32))

JuniBgpConditionalAdvStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "BGP Conditional Advertisement Status"
    SYNTAX      INTEGER {
                    advertise(1),
                    withdraw(2) }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Managed objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniBgpObjects                      OBJECT IDENTIFIER ::= { juniBgpMIB 1 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP General Group attributes (scalars)
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniBgpGeneralGroup                 OBJECT IDENTIFIER ::= { juniBgpObjects 1 }

juniBgpLocalAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
        "Deprecated.  The local autonomous system number.  This object has been
        replaced by juniBgpFourOctetLocalAsNumber.  It is still possible to
        get and set this object, but if the actual local AS number is greater
        than 65535, getting this object returns 23456 (AS-TRANS)."
    DEFVAL    { 0 }
    ::= { juniBgpGeneralGroup 1 }

juniBgpEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to have BGP router enabled."
    DEFVAL    { true }
    ::= { juniBgpGeneralGroup 2 }

juniBgpIdentifier OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The BGP identifier of the local system.  The identifier is the system
        router ID by default."
    ::= { juniBgpGeneralGroup 3 }

juniBgpAlwaysCompareMed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to compare metrics for paths from
        neighors of different ASs."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 4 }

juniBgpDefaultLocalPreference OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default local preference."
    DEFVAL    { 100 }
    ::= { juniBgpGeneralGroup 5 }

juniBgpEqualCostLimit OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum equal cost paths to store in the routing table.  This
        object has been replaced by the juniBgpVrfMaximumPathsEbgp object."
    DEFVAL    { 1 }
    ::= { juniBgpGeneralGroup 6 }

juniBgpClientToClientReflection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to have client-to-client route
        reflection enabled."
    DEFVAL    { true }
    ::= { juniBgpGeneralGroup 7 }

juniBgpClusterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The cluster ID.  When this object is zero, the router ID will be used."
    DEFVAL    { 0 }
    ::= { juniBgpGeneralGroup 8 }

juniBgpConfederationId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The confederation ID.  When this object is zero, the router is not in a
        confederation."
    DEFVAL    { 0 }
    ::= { juniBgpGeneralGroup 9 }

juniBgpMissingAsWorst OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If this object is set to true(1) routes without a MED attribute are
        considered worse than routes with a MED attribute.  If this object is
        set to false(0) routes without a MED attribute are considered better
        than routes with a MED attribute."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 10 }

juniBgpResetAllConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes all sessions to all peers to be
        cleared; the value determines what type of clear is executed (hard
        clear, soft clear in, soft clear out, etc.).  Reading this object has no
        effect and always returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpGeneralGroup 11 }

juniBgpAdvertiseInactive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to propagate received BGP routes
        which are not the best route in the IP forwarding table."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 12 }

juniBgpEnforceFirstAs OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to check whether the first AS
        number in the AS-path in routes from EBGP peers is consistent with the
        AS number of the peer."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 13 }

juniBgpConfedCompareMed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to compare metrics for paths from
        neighors of different sub-ASs in the same confederation."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 14 }

juniBgpGlobalRetryInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default number of seconds before BGP retries to establish an
        outgoing BGP session."
    DEFVAL    { 120 }
    ::= { juniBgpGeneralGroup 15 }

juniBgpGlobalConfigKeepAliveInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default time interval in seconds for the KeepAlive timer for all peers.
        Zero means don't send KeepAlive messages."
    DEFVAL    { 30 }
    ::= { juniBgpGeneralGroup 16 }

juniBgpGlobalConfigHoldTime OBJECT-TYPE
    SYNTAX      Integer32 (0|3..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default time interval in seconds for the Hold timer for all peers.
        Zero means don't expect KeepAlive message to be received."
    DEFVAL    { 90 }
    ::= { juniBgpGeneralGroup 17 }

juniBgpGlobalAsOriginationInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default time interval in seconds for the MinASOriginationInterval timer
        for all peers."
    DEFVAL    { 10 }
    ::= { juniBgpGeneralGroup 18 }

juniBgpExternalAdvertisementInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default time interval in seconds for the MinRouteAdvertisementInterval
        timer for all external peers."
    DEFVAL    { 30 }
    ::= { juniBgpGeneralGroup 19 }

juniBgpGlobalRibOutEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to enable RIB-out by default for
        all peers, or to false(2) to disable RIB-out by default for all peers."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 20 }

juniBgpOverloadShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP to shut itself down when it runs out
        of memory.  Set this object to false(2) for BGP to continue running
        (with incomplete information) when it runs out of memory."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 21 }

juniBgpLogNeighborChanges OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP log a message whenever a peer enters
        or leaves the established state."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 22 }

juniBgpFastExternalFallover OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP to immediately bring down any
        directly adjacent EBGP session on a link down."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 23 }

juniBgpInternalAdvertisementInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default time interval in seconds for the MinRouteAdvertisementInterval
        timer for all internal peers."
    DEFVAL    { 5 }
    ::= { juniBgpGeneralGroup 24 }

juniBgpMaxAsLimit OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Maximum length of received AS-path.  Received routes with an AS-path
        longer than this are not placed in the IP forwarding table and are not
        propagated to other peers.  Zero means no maximum length."
    DEFVAL    { 0 }
    ::= { juniBgpGeneralGroup 25 }

juniBgpOperationalState OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    up(1),
                    down(2),
                    overload(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BGP instance operational state."
    ::= { juniBgpGeneralGroup 26 }

juniBgpPreviousOperationalState OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    up(1),
                    down(2),
                    overload(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BGP instance previous operational state."
    ::= { juniBgpGeneralGroup 27 }

juniBgpAutomaticRouteTargetFilter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP to enable automatic route-target
        filtering."
    DEFVAL    { true }
    ::= { juniBgpGeneralGroup 28 }

juniBgpDefaultIPv4Unicast OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP to automatically activate peers in
        the IPv4 unicast address family."
    DEFVAL    { true }
    ::= { juniBgpGeneralGroup 29 }

juniBgpRedistributeInternal OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) for BGP to automatically redistribute iBGP
        routes to IGP protocols."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 30 }

juniBgpFourOctetLocalAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The four-octet local autonomous system number.  This attribute replaces
        the old two-octet local autonomous system number."
    DEFVAL    { 0 }
    ::= { juniBgpGeneralGroup 31 }

juniBgpConfederationPeersFilterList OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The confederation peers filter-list contains the name of an as-path
        access-list.  Every neighbor whose remote-AS number matches this as-path
        access-list is considered a confederation peer.  An empty string means
        that no confederation peers filter-list is configured."
    DEFVAL    { "" }
    ::= { juniBgpGeneralGroup 32 }

juniBgpUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpEnabled(0),
        juniBgpIdentifier(1),
        juniBgpAlwaysCompareMed(2),
        juniBgpDefaultLocalPreference(3),
        juniBgpEqualCostLimit(4),
        juniBgpClientToClientReflection(5),
        juniBgpClusterId(6),
        juniBgpConfederationId(7),
        juniBgpMissingAsWorst(8),
        juniBgpAdvertiseInactive(9),
        juniBgpEnforceFirstAs(10),
        juniBgpConfedCompareMed(11),
        juniBgpGlobalRetryInterval(12),
        juniBgpGlobalConfigKeepAliveInterval(13),
        juniBgpGlobalConfigHoldTime(14),
        juniBgpGlobalAsOriginationInterval(15),
        juniBgpExternalAdvertisementInterval(16),
        juniBgpGlobalRibOutEnabled(17),
        juniBgpOverloadShutdown(18),
        juniBgpLogNeighborChanges(19),
        juniBgpFastExternalFallover(20),
        juniBgpInternalAdvertisementInterval(21),
        juniBgpMaxAsLimit(22),
        juniBgpAutomaticRouteTargetFilter(23),
        juniBgpDefaultIPv4Unicast(24),
        juniBgpRedistributeInternal(25),
        juniBgpFourOctetLocalAsNumber(26),
        juniBgpConfederationPeersFilterList(27),
        juniBgpAdvertiseBestExternalToInternal(28),
        juniBgpGracefulRestartEnabled(29),
        juniBgpGracefulRestartRestartTime(30),
        juniBgpGracefulRestartStalePathsTime(31),
        juniBgpGracefulRestartPathSelectionDeferTimeLimit(32) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpGeneralGroup 33 }

juniBgpAdvertiseBestExternalToInternal OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If this object is set to true(1) then BGP advertises the best route
        received from external (and confederation) peers to internal peers.

        If this object is set to false(2) then BGP advertises the best route
        received from all peers to internal peers except if the best route was
        received from an internal peer in which case BGP doesn't advertise any
        route to internal peers."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 34 }

juniBgpGracefulRestartEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) to enable BGP graceful restart."
    DEFVAL    { false }
    ::= { juniBgpGeneralGroup 35 }
    
juniBgpGracefulRestartRestartTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The restart-time advertised in graceful-restart capabilities sent to 
        all peers. This is the estimated time (in seconds) it will take for 
        the BGP session to be re-established after a restart. This can be used 
        to speed up routing convergence by the peer in case that this BGP 
        speaker does not come back after a restart. This value is used by
        all peers unless overridden by juniBgpPeerGroupGracefulRestartRestartTime 
        or juniBgpPeerGracefulRestartRestartTime."
    DEFVAL    { 120 }
    ::= { juniBgpGeneralGroup 36 }

juniBgpGracefulRestartStalePathsTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The maximum amount of time in seconds we keep stale routes after a
        session goes down. This value is used for all peers unless it has
        been overridden by juniBgpPeerGroupGracefulRestartStalePathsTime or
        juniBgpPeerGracefulRestartStalePathsTime."
    DEFVAL    { 360 }
    ::= { juniBgpGeneralGroup 37 }
    
juniBgpGracefulRestartPathSelectionDeferTimeLimit OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The maximum amount of time in seconds route selection is deferred
        after a restart."
    DEFVAL    { 360 }
    ::= { juniBgpGeneralGroup 38 }

juniBgpPlatformSupportsNonStopForwarding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Does this platform support non-stop forwarding?"
    ::= { juniBgpGeneralGroup 39 }

juniBgpDeviceCanPreserveForwardingState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is this router at this moment able to do a graceful restart (i.e.
        is it able to switch over from the primary controller to the standby 
        controller without losing forwarding state on the line cards). The
        router may not be able to do a graceful restart because the platform
        does not support non-stop forwarding, because there is no secondary
        controller present, because the secondary controller is not in
        standby state, etc."
    ::= { juniBgpGeneralGroup 40 }

juniBgpLastRestartWasGraceful OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Was the most recent restart graceful? (i.e. was forwarding state
        preserved on the line cards during the most recent switch-over
        from the primary controller to the standby controller?)"
    ::= { juniBgpGeneralGroup 41 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Route Table Statistics (scalars)
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniBgpRouteTableStatisticsGroup    OBJECT IDENTIFIER ::= { juniBgpObjects 2 }

juniBgpBaselineTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The sysUpTime at which the counters were most recently baselined."
    ::= { juniBgpRouteTableStatisticsGroup 1 }

juniBgpDestinationCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of distinct destinations in the local RIB."
    ::= { juniBgpRouteTableStatisticsGroup 2 }

juniBgpDestinationMemoryUsed OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Memory used by distinct destinations in the local RIB."
    ::= { juniBgpRouteTableStatisticsGroup 3 }

juniBgpRouteCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of routes in the local RIB."
    ::= { juniBgpRouteTableStatisticsGroup 4 }

juniBgpRouteMemoryUsed OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Memory used by routes in the local RIB."
    ::= { juniBgpRouteTableStatisticsGroup 5 }

juniBgpSelectedRouteCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of routes selected for route table installation."
    ::= { juniBgpRouteTableStatisticsGroup 6 }

juniBgpPathAttributeCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of entries in the path attribute database."
    ::= { juniBgpRouteTableStatisticsGroup 8 }

juniBgpPathAttributeMemoryUsed OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Memory used by entries in the path attribute database."
    ::= { juniBgpRouteTableStatisticsGroup 9 }

juniBgpRouteFlapHistoryCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of routes with active route flap histories."
    ::= { juniBgpRouteTableStatisticsGroup 10 }

juniBgpRouteFlapHistoryMemoryUsed OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Memory used by routes with active route flap histories."
    ::= { juniBgpRouteTableStatisticsGroup 11 }

juniBgpSuppressedRouteCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of routes suppressed due to route flap."
    ::= { juniBgpRouteTableStatisticsGroup 12 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Confederation Peer attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  Deprecated BGP ConfederationPeer Table
--
juniBgpConfederationPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpConfederationPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      deprecated
    DESCRIPTION
        "The Juniper BGP Confederation Peer Table describes the BGP-specific
        characteristics of confederation peers."
  ::= { juniBgpObjects 3 }

juniBgpConfederationPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpConfederationPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      deprecated
    DESCRIPTION
        "The BGP Confederation Peer Entry describes BGP-specific characteristics
        of one confederation peer."
    INDEX     { juniBgpConfederationPeerAsNumber }
    ::= { juniBgpConfederationPeerTable 1 }

JuniBgpConfederationPeerEntry ::= SEQUENCE {
    juniBgpConfederationPeerAsNumber     Integer32,
    juniBgpConfederationPeerRowStatus    RowStatus }

juniBgpConfederationPeerAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      deprecated
    DESCRIPTION
        "The AsNumber of this confederation peer."
    ::= { juniBgpConfederationPeerEntry 1 }

juniBgpConfederationPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpConfederationPeerEntry 2 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- New BGP Confederation Peer attributes (using four-octet AS-numbers)
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP ConfederationPeer Table (for four-octet AS-numbers)
--
juniBgpFourOctetConfederationPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpFourOctetConfederationPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP New Confederation Peer Table describes the BGP-specific
        characteristics of confederation peers using four-octet AS-numbers."
  ::= { juniBgpObjects 28 }

juniBgpFourOctetConfederationPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetConfederationPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP New Confederation Peer Entry describes BGP-specific
        characteristics of one confederation peer using four-octet AS-numbers."
    INDEX     { juniBgpFourOctetConfederationPeerAsNumber }
    ::= { juniBgpFourOctetConfederationPeerTable 1 }

JuniBgpFourOctetConfederationPeerEntry ::= SEQUENCE {
    juniBgpFourOctetConfederationPeerAsNumber     JuniBgpFourOctetAsNumber,
    juniBgpFourOctetConfederationPeerRowStatus    RowStatus }

juniBgpFourOctetConfederationPeerAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The four-octet AS-number of this confederation peer."
    ::= { juniBgpFourOctetConfederationPeerEntry 1 }

juniBgpFourOctetConfederationPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpFourOctetConfederationPeerEntry 2 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Table
--
juniBgpPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Table describes the BGP-specific characteristics
        of peers."
    ::= { juniBgpObjects 4 }

juniBgpPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Peer Entry describes BGP-specific characteristics of one peer."
    INDEX     { juniBgpPeerVrfName,
                juniBgpPeerRemoteAddress }
    ::= { juniBgpPeerTable 1 }

JuniBgpPeerEntry ::= SEQUENCE {
    juniBgpPeerVrfName                              JuniVrfName,
    juniBgpPeerRemoteAddress                        IpAddress,
    juniBgpPeerAdminStatus                          INTEGER,
    juniBgpPeerState                                INTEGER,
    juniBgpPeerNegotiatedVersion                    Integer32,
    juniBgpPeerLocalAddress                         IpAddress,
    juniBgpPeerLocalAddressMask                     IpAddress,
    juniBgpPeerLocalPort                            Integer32,
    juniBgpPeerRemoteAsNumber                       Integer32,
    juniBgpPeerRemotePort                           Integer32,
    juniBgpPeerInUpdates                            Counter32,
    juniBgpPeerOutUpdates                           Counter32,
    juniBgpPeerInTotalMessages                      Counter32,
    juniBgpPeerOutTotalMessages                     Counter32,
    juniBgpPeerLastErrorCode                        OCTET STRING,
    juniBgpPeerLastResetReason                      DisplayString,
    juniBgpPeerFsmEstablishedTransitions            Counter32,
    juniBgpPeerFsmEstablishedTime                   Gauge32,
    juniBgpPeerRetryInterval                        Integer32,
    juniBgpPeerHoldTime                             Integer32,
    juniBgpPeerKeepAliveInterval                    Integer32,
    juniBgpPeerConfigHoldTime                       Integer32,
    juniBgpPeerConfigKeepAliveInterval              Integer32,
    juniBgpPeerAsOriginationInterval                Integer32,
    juniBgpPeerAdvertisementInterval                Integer32,
    juniBgpPeerInUpdateElapsedTime                  Gauge32,
    juniBgpPeerDescription                          DisplayString,
    juniBgpPeerRemoteIdentifier                     IpAddress,
    juniBgpPeerWeight                               Unsigned32,
    juniBgpPeerEbgpMultihop                         TruthValue,
    juniBgpPeerEbgpMultihopTtl                      Integer32,
    juniBgpPeerUpdateSource                         IpAddress,
    juniBgpPeerMd5Password                          OCTET STRING,
    juniBgpPeerMaxUpdateSize                        Unsigned32,
    juniBgpPeerType                                 INTEGER,
    juniBgpPeerReceivedCapabilitiesOption           TruthValue,
    juniBgpPeerReceivedCapabilityMultiProtocol      TruthValue,
    juniBgpPeerReceivedCapabilityRouteRefresh       TruthValue,
    juniBgpPeerReceivedCapabilityRouteRefreshCisco  TruthValue,
    juniBgpPeerResetConnectionType                  JuniBgpResetConnectionType,
    juniBgpPeerRowStatus                            RowStatus,
    juniBgpPeerLocalAsNumber                        Integer32,
    juniBgpPeerFourOctetRemoteAsNumber              JuniBgpFourOctetAsNumber,
    juniBgpPeerFourOctetLocalAsNumber               JuniBgpFourOctetAsNumber,
    juniBgpPeerReceivedCapabilityFourOctetAsNumbers             TruthValue,
    juniBgpPeerReceivedCapabilityDynamicCapabilityNeg           TruthValue,
    juniBgpPeerShouldAdvertiseCapabilitiesOption                TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityRouteRefresh            TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco       TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers      TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg    TruthValue,
    juniBgpPeerSentCapabilitiesOption                           TruthValue,
    juniBgpPeerSentCapabilityMultiProtocol                      TruthValue,
    juniBgpPeerSentCapabilityRouteRefresh                       TruthValue,
    juniBgpPeerSentCapabilityRouteRefreshCisco                  TruthValue,
    juniBgpPeerSentCapabilityFourOctetAsNumbers                 TruthValue,
    juniBgpPeerSentCapabilityDynamicCapabilityNeg               TruthValue,
    juniBgpPeerReceivedUnsupportedOptionalParameterNotification TruthValue,
    juniBgpPeerReceivedUnsupportedCapabilityNotification        TruthValue,
    juniBgpPeerUnconfiguredAttributes                           BITS,
    juniBgpPeerSiteOfOrigin                                     OCTET STRING,
    juniBgpPeerLenient                                          TruthValue,
    juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg        TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg TruthValue,
    juniBgpPeerSentCapabilityOldDynamicCapabilityNeg            TruthValue,
    juniBgpPeerPassive                                          TruthValue,
    juniBgpPeerDynamic                                          TruthValue,
    juniBgpPeerShouldAdvertiseCapabilityGracefulRestart         TruthValue,
    juniBgpPeerSentCapabilityGracefulRestart                    TruthValue,
    juniBgpPeerReceivedCapabilityGracefulRestart                TruthValue,
    juniBgpPeerGracefulRestartRestartTime                       Integer32,
    juniBgpPeerGracefulRestartStalePathsTime                    Integer32,
    juniBgpPeerSentGracefulRestartRestartState                  TruthValue,
    juniBgpPeerReceivedGracefulRestartRestartState              TruthValue,
    juniBgpPeerSentGracefulRestartRestartTime                   Integer32,
    juniBgpPeerReceivedGracefulRestartRestartTime               Integer32,
    juniBgpPeerTimeUntilGracefulRestartRestartTimerExpires      Integer32,
    juniBgpPeerTimeUntilGracefulRestartStalePathsTimerExpires   Integer32,
    juniBgpPeerBfdEnabled                                       TruthValue,
    juniBgpPeerBfdMinTransmitInterval                           Integer32,
    juniBgpPeerBfdMinReceiveInterval                            Integer32,
    juniBgpPeerBfdMultiplier                                    Integer32,
    juniBgpPeerBfdSessionUp                                     TruthValue,
    juniBgpPeerBfdDetectionTime                                 Integer32,
    juniBgpPeerIbgpSinglehop                                    TruthValue }

juniBgpPeerVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpPeerEntry 1 }

juniBgpPeerRemoteAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpPeerEntry 2 }

juniBgpPeerAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    stop(1),
                    start(2) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The desired state of the BGP connection.  A transition from 'stop' to
        'start' will cause the BGP Start Event to be generated.  A transition
        from 'start' to 'stop' will cause the BGP Stop Event to be generated.
        This parameter can be used to restart BGP peer connections.  Care should
        be used in providing write access to this object without adequate
        authentication."
    ::= { juniBgpPeerEntry 3 }

juniBgpPeerState OBJECT-TYPE
    SYNTAX      INTEGER {
                    stop(0),
                    idle(1),
                    connect(2),
                    active(3),
                    opensent(4),
                    openconfirm(5),
                    established(6),
                    removing(7) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BGP peer connection state."
    ::= { juniBgpPeerEntry 4 }

juniBgpPeerNegotiatedVersion OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The negotiated version of BGP running between the two peers."
    ::= { juniBgpPeerEntry 5 }

juniBgpPeerLocalAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The local IP address of this entry's BGP connection."
    ::= { juniBgpPeerEntry 6 }

juniBgpPeerLocalAddressMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The local IP address mask of this entry's BGP connection."
    ::= { juniBgpPeerEntry 7 }

juniBgpPeerLocalPort OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The local port for the TCP connection between the BGP peers."
    ::= { juniBgpPeerEntry 8 }

juniBgpPeerRemoteAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Deprecated.  The peer's autonomous system number.  This object has been
        replaced by juniBgpPeerFourOctetRemoteAsNumber.  It is still possible to
        get and set this object, but if the actual remote AS number is greater
        than 65535, getting this object returns 23456 (AS-TRANS)."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 9 }

juniBgpPeerRemotePort OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The remote port for the TCP connection between the BGP peers.  Note
        that the objects bgpPeerLocalAddr, bgpPeerLocalPort, bgpPeerRemoteAddr
        and bgpPeerRemotePort provide the appropriate reference to the standard
        MIB TCP connection table."
    ::= { juniBgpPeerEntry 10 }

juniBgpPeerInUpdates OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of BGP UPDATE messages received on this connection.  This
        object should be initialized to zero (0) when the connection is
        established."
    ::= { juniBgpPeerEntry 11 }

juniBgpPeerOutUpdates OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of BGP UPDATE messages transmitted on this connection.  This
        object should be initialized to zero (0) when the connection is
        established."
    ::= { juniBgpPeerEntry 12 }

juniBgpPeerInTotalMessages OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of messages received from the remote peer on this
        connection.  This object should be initialized to zero when the
        connection is established."
    ::= { juniBgpPeerEntry 13 }

juniBgpPeerOutTotalMessages OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of messages transmitted to the remote peer on this
        connection.  This object should be initialized to zero when the
        connection is established."
    ::= { juniBgpPeerEntry 14 }

juniBgpPeerLastErrorCode OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(2))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The last error code and subcode seen by this peer on this connection.
        If no error has occurred, this field is zero.  Otherwise, the first byte
        of this two byte OCTET STRING contains the error code, and the second
        byte contains the subcode."
    ::= { juniBgpPeerEntry 15 }

juniBgpPeerLastResetReason OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The string representing the last error code seen by this peer on this
        connection.  If no error has occurred, this string is null."
    ::= { juniBgpPeerEntry 16 }

juniBgpPeerFsmEstablishedTransitions OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of times the BGP FSM transitioned into the established
        state."
    ::= { juniBgpPeerEntry 17 }

juniBgpPeerFsmEstablishedTime OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This timer indicates how long (in seconds) this peer has been in the
        Established state or how long since this peer was last in the
        Established state.  It is set to zero when a new peer is configured or
        the router is booted."
    ::= { juniBgpPeerEntry 18 }

juniBgpPeerRetryInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the ConnectRetry timer.  The suggested
        value for this timer is 120 seconds."
    DEFVAL    { 120 }
    ::= { juniBgpPeerEntry 19 }

juniBgpPeerHoldTime OBJECT-TYPE
    SYNTAX      Integer32 (0|3..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the Hold Timer established with the peer.
        The value of this object is calculated by this BGP speaker by using the
        smaller of the value in bgpPeerHoldTimeConfigured and the Hold Time
        received in the OPEN message.  This value must be at lease three seconds
        if it is not zero (0) in which case the Hold Timer has not been
        established with the peer, or, the value of bgpPeerHoldTimeConfigured is
        zero (0)."
    ::= { juniBgpPeerEntry 20 }

juniBgpPeerKeepAliveInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..21845)
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the KeepAlive timer established with the
        peer.  The value of this object is calculated by this BGP speaker such
        that, when compared with bgpPeerHoldTime, it has the same proportion as
        what bgpPeerKeepAliveConfigured has when compared with
        bgpPeerHoldTimeConfigured.  If the value of this object is zero (0), it
        indicates that the KeepAlive timer has not been established with the
        peer, or, the value of bgpPeerKeepAliveConfigured is zero (0)."
    ::= { juniBgpPeerEntry 21 }

juniBgpPeerConfigHoldTime OBJECT-TYPE
    SYNTAX      Integer32 (0|3..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the Hold Time configured for this BGP
        speaker with this peer.  This value is placed in an OPEN message sent to
        this peer by this BGP speaker, and is compared with the Hold Time field
        in an OPEN message received from the peer when determining the Hold Time
        (bgpPeerHoldTime) with the peer.  This value must not be less than three
        seconds if it is not zero (0) in which case the Hold Time is NOT to be
        established with the peer.  The suggested value for this timer is 90
        seconds."
    DEFVAL    { 90 }
    ::= { juniBgpPeerEntry 22 }

juniBgpPeerConfigKeepAliveInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..21845)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the KeepAlive timer configured for this
        BGP speaker with this peer.  The value of this object will only
        determine the KEEPALIVE messages' frequency relative to the value
        specified in bgpPeerHoldTimeConfigured; the actual time interval for the
        KEEPALIVE messages is indicated by bgpPeerKeepAlive.  A reasonable
        maximum value for this timer would be configured to be one third of that
        of bgpPeerHoldTimeConfigured.  If the value of this object is zero (0),
        no periodical KEEPALIVE messages are sent to the peer after the BGP
        connection has been established.  The suggested value for this timer is
        30 seconds."
    DEFVAL    { 30 }
    ::= { juniBgpPeerEntry 23 }

juniBgpPeerAsOriginationInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the MinASOriginationInterval timer.  The
        suggested value for this timer is 10 seconds."
    DEFVAL    { 10 }
    ::= { juniBgpPeerEntry 24 }

juniBgpPeerAdvertisementInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the MinRouteAdvertisementInterval timer.
        The suggested value for this timer is 30 seconds."
    DEFVAL    { 30 }
    ::= { juniBgpPeerEntry 25 }

juniBgpPeerInUpdateElapsedTime OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Elapsed time in seconds since the last BGP UPDATE message was received
        from the peer.  Each time bgpPeerInUpdates is incremented, the value of
        this object is set to zero (0)."
    ::= { juniBgpPeerEntry 26 }

juniBgpPeerDescription OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Peer textual description."
    DEFVAL    { "" }
    ::= { juniBgpPeerEntry 27 }

juniBgpPeerRemoteIdentifier OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Peer's remote router ID."
    ::= { juniBgpPeerEntry 28 }

juniBgpPeerWeight OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight assigned to routes learned from peer."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 29 }

juniBgpPeerEbgpMultihop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The EBGP multihop is enabled."
    DEFVAL    { false }
    ::= { juniBgpPeerEntry 30 }

juniBgpPeerEbgpMultihopTtl OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum number of hops to the external peer (only relevant if EBGP
        multihop turned on)."
    DEFVAL    { 255 }
    ::= { juniBgpPeerEntry 31 }

juniBgpPeerUpdateSource OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source IP address for peer connections."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 32 }

juniBgpPeerMd5Password OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The password for MD5 authentication.  Reading this object always
        results in an OCTET STRING of length zero."
    DEFVAL    { "" }
    ::= { juniBgpPeerEntry 33 }

juniBgpPeerMaxUpdateSize OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum size in bytes of transmitted UPDATE messages."
    DEFVAL    { 4096 }
    ::= { juniBgpPeerEntry 34 }

juniBgpPeerType OBJECT-TYPE
    SYNTAX      INTEGER {
                    peerTypeInternal(1),
                    peerTypeExternal(2),
                    peerTypeConfederation(3),
                    peerTypeUnknown(4) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of peer."
    ::= { juniBgpPeerEntry 35 }

juniBgpPeerReceivedCapabilitiesOption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer sent the capabilities optional parameter in its open message."
    ::= { juniBgpPeerEntry 36 }

juniBgpPeerReceivedCapabilityMultiProtocol OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the multi-protocol capability to us."
    ::= { juniBgpPeerEntry 37 }

juniBgpPeerReceivedCapabilityRouteRefresh OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the route-refresh capability to us."
    ::= { juniBgpPeerEntry 38 }

juniBgpPeerReceivedCapabilityRouteRefreshCisco OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the route-refresh-cisco capability to us."
    ::= { juniBgpPeerEntry 39 }

juniBgpPeerResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes the session to the peer to be
        cleared; the value determines what type of clear is executed (hard
        clear, soft clear in, soft clear out, etc.).  Reading this object has no
        effect and always returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpPeerEntry 40 }

juniBgpPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpPeerEntry 41 }

juniBgpPeerLocalAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Deprecated.  The local autonomous system number to be used for this
        peer.  Value zero (0) means that the global local automous system number
        (as specified in the object juniBgpPeerLocalAsNumber) is used.  This
        object has been replaced by juniBgpPeerFourOctetLocalAsNumber.  It is
        still possible to get and set this object, but if the actual local AS
        number is greater than 65535, getting this object returns AS-TRANS
        (23456)."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 42 }

juniBgpPeerFourOctetRemoteAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The peer's four-octet autonomous system number."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 43 }

juniBgpPeerFourOctetLocalAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The four-octet local autonomous system number to be used for this peer.
        Value zero (0) means that the global local automous system number (as
        specified in the object juniBgpPeerFourOctetLocalAsNumber) is used."
    DEFVAL    { 0 }
    ::= { juniBgpPeerEntry 44 }

juniBgpPeerReceivedCapabilityFourOctetAsNumbers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the four-octet-as-numbers capability to us."
    ::= { juniBgpPeerEntry 45 }

juniBgpPeerReceivedCapabilityDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the dynamic-capability-negotiation capability
        to us."
    ::= { juniBgpPeerEntry 46 }

juniBgpPeerShouldAdvertiseCapabilitiesOption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the capabilities option should be included
        in OPEN messages sent to this peer.  Even if this object is set to true,
        BGP may decide not to include the capabilities option in OPEN messages
        sent to this peer (for example when it is detected that the peer does
        not support capability negotiation).  The
        juniBgpPeerSentCapabilitiesOption can be used to determine whether or
        not the OPEN message sent to the peer actually contained the
        capabilities option."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 47 }

juniBgpPeerShouldAdvertiseCapabilityRouteRefresh OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the route-refresh capability should be
        advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the route-refresh capability to this peer (for
        example when it is detected that the peer does not support capability
        negotiation or when it is detected that the peer does not support the
        route-refresh capability and incorrectly sends an unsupported capability
        notification).  The juniBgpPeerSentCapabilityRouteRefresh can be used to
        determine whether or not the capability was actually advertised to the
        peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 48 }

juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the route-refresh-cisco capability should be
        advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the route-refresh-cisco capability to this peer
        (for example when it is detected that the peer does not support
        capability negotiation or when it is detected that the peer does not
        support the route-refresh-cisco capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityRouteRefreshCisco can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 49 }

juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the four-octet-as-numbers capability should
        be advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the four-octet-as-numbers capability to this
        peer (for example when it is detected that the peer does not support
        capability negotiation or when it is detected that the peer does not
        support the four-octet-as-numbers capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityFourOctetAsNumbers can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 50 }

juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the dynamic-capability-negotiation
        capability should be advertised to this peer.  Even if this object is
        set to true, BGP may decide not to advertise the
        dynamic-capability-negotiation capability to this peer (for example when
        it is detected that the peer does not support capability negotiation or
        when it is detected that the peer does not support the
        dynamic-capability-negotiation capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityDynamicCapabilityNeg can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 51 }

juniBgpPeerSentCapabilitiesOption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We sent the capabilities optional parameter in the OPEN message to this
        peer."
    ::= { juniBgpPeerEntry 52 }

juniBgpPeerSentCapabilityMultiProtocol OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the multi-protocol capability to this peer."
    ::= { juniBgpPeerEntry 53 }

juniBgpPeerSentCapabilityRouteRefresh OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the route-refresh capability to this peer."
    ::= { juniBgpPeerEntry 54 }

juniBgpPeerSentCapabilityRouteRefreshCisco OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the route-refresh-cisco capability to this peer."
    ::= { juniBgpPeerEntry 55 }

juniBgpPeerSentCapabilityFourOctetAsNumbers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the four-octet-as-numbers capability to this peer."
    ::= { juniBgpPeerEntry 56 }

juniBgpPeerSentCapabilityDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the dynamic-capability-negotiation capability to this
        peer."
    ::= { juniBgpPeerEntry 57 }

juniBgpPeerReceivedUnsupportedOptionalParameterNotification OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We received an unsupported optional parameter notification from this
        peer.  This indicates that the peer does not support capability
        negotiation.  When this object is set to true, we do not include the
        capabilities optional parameter in OPEN messages sent to this peer.  A
        hard clear of the session is needed to retry sending the capabilities
        optional parameter."
    ::= { juniBgpPeerEntry 58 }

juniBgpPeerReceivedUnsupportedCapabilityNotification OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We received an unsupported capability notification from this peer.
        This often happens because the peer did not recognize a capability which
        we advertised to the peer and that peer incorrectly send back an
        unsupported capability notification (the peer should ignore the
        unsupported capability instead).  When this object is set to true, we do
        not advertise capabilities that the peer does not support."
    ::= { juniBgpPeerEntry 59 }

juniBgpPeerUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpPeerAdminStatus(0),
        juniBgpPeerRetryInterval(1),
        juniBgpPeerConfigHoldTime(2),
        juniBgpPeerConfigKeepAliveInterval(3),
        juniBgpPeerAsOriginationInterval(4),
        juniBgpPeerAdvertisementInterval(5),
        juniBgpPeerDescription(6),
        juniBgpPeerWeight(7),
        juniBgpPeerEbgpMultihop(8),
        juniBgpPeerEbgpMultihopTtl(9),
        juniBgpPeerUpdateSource(10),
        juniBgpPeerMd5Password(11),
        juniBgpPeerMaxUpdateSize(12),
        juniBgpPeerFourOctetRemoteAsNumber(13),
        juniBgpPeerFourOctetLocalAsNumber(14),
        juniBgpPeerShouldAdvertiseCapabilitiesOption(15),
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh(16),
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco(17),
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers(18),
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg(19),
        juniBgpPeerSiteOfOrigin(20),
        juniBgpPeerLenient(21),
        juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg(22),
        juniBgpPeerPassive(23),
        juniBgpPeerShouldAdvertiseCapabilityGracefulRestart(24),
        juniBgpPeerGracefulRestartRestartTime(25),
        juniBgpPeerGracefulRestartStalePathsTime(26),
        juniBgpPeerBfdEnabled(27),
        juniBgpPeerBfdMinTransmitInterval(28),
        juniBgpPeerBfdMinReceiveInterval(29),
        juniBgpPeerBfdMultiplier(30),
        juniBgpPeerIbgpSinglehop(31) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpPeerEntry 60 }

juniBgpPeerSiteOfOrigin OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The site-of-origin.  This site-of-origin is added to the extended
        communities for all routes received from the peer.  When sending routes
        to this peer all routes with this extended community are filtered.  The
        null extended community (all 8 bytes zero) means that no site-of-origin
        is configured for this peer.  It is not allowed to set this object to
        any extended community other than a site-of-origin type of extended
        community or null."
    ::= { juniBgpPeerEntry 61 }

juniBgpPeerLenient OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Enable Lenient behavior for this peer so that it is more tolerant of
        finite state machine errors and malformed messages received from the
        remote peer and avoids terminating the peer session whenever possible."
    ::= { juniBgpPeerEntry 62 }

juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the old deprecated dynamic-capability-
        negotiation capability to us."
    ::= { juniBgpPeerEntry 63 }

juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the deprecated-dynamic-capability-
        negotiation capability should be advertised to this peer.  Even if this
        object is set to true, BGP may decide not to advertise the
        deprecated-dynamic-capability-negotiation capability to this peer (for
        example when it is detected that the peer does not support capability
        negotiation or when it is detected that the peer does not support the
        deprecated-dynamic-capability-negotiation capability and incorrectly
        sends an unsupported capability notification).  The
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg can be used to
        determine whether or not the capability was actually advertised to the
        peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 64 }

juniBgpPeerSentCapabilityOldDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the old-dynamic-capability-negotiation capability to this
        peer."
    ::= { juniBgpPeerEntry 65 }

juniBgpPeerPassive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) to make this a passive peer."
    DEFVAL    { false }
    ::= { juniBgpPeerEntry 66 }

juniBgpPeerDynamic OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object return true(1) if this is a dynamically created peer or
        false(2) if this is a configured peer."
    ::= { juniBgpPeerEntry 67 }

juniBgpPeerShouldAdvertiseCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the graceful-restart capability should be
        advertised to all peers.  Even if this object is set to true, BGP may
        decide not to advertise the graceful-restart capability to a peer 
        (for example when graceful-restart is not enabled or when it is 
        detected that the peer does not support capability negotiation).  
        The juniBgpPeerSentCapabilityGracefulRestart can be used to determine 
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerEntry 68 }

juniBgpPeerSentCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "We advertised the graceful-restart capability to this peer."
    ::= { juniBgpPeerEntry 69 }
    
juniBgpPeerReceivedCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has advertised the graceful-restart capability to us."
    ::= { juniBgpPeerEntry 70 }

juniBgpPeerGracefulRestartRestartTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The restart-time advertised in the graceful-restart capability sent to 
        this peer. This is the estimated time (in seconds) it will take for 
        the BGP session to be re-established after a restart. This can be used 
        to speed up routing convergence by the peer in case that this BGP 
        speaker does not come back after a restart. This value (if configured)
        overrides the global value configured in juniBgpGracefulRestartRestartTime
        and the value for the peer-group configured in 
        juniBgpPeerGroupGracefulRestartRestartTime."
    DEFVAL    { 120 }
    ::= { juniBgpPeerEntry 71 }

juniBgpPeerGracefulRestartStalePathsTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum amount of time in seconds we keep stale routes after the
        session to this peer goes down. This value (if configured)
        overrides the global value configured in juniBgpGracefulRestartStalePathsTime
        and the value for the peer-group configured in 
        juniBgpPeerGroupGracefulRestartStalePathsTime."
    DEFVAL    { 360 }
    ::= { juniBgpPeerEntry 72 }

juniBgpPeerSentGracefulRestartRestartState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that the OPEN message most recently sent to 
        this peer contained the graceful-restart capability with the 
        Restart-State (R) bit in the Restart Flags field set to one (this
        indicates that BGP has experienced a restart)."
    ::= { juniBgpPeerEntry 73 }

juniBgpPeerReceivedGracefulRestartRestartState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that the OPEN message most recently received
        from this peer contained the graceful-restart capability with the 
        Restart-State (R) bit in the Restart Flags field set to one (this 
        indicates that the peer has experienced a restart)."
    ::= { juniBgpPeerEntry 74 }

juniBgpPeerSentGracefulRestartRestartTime OBJECT-TYPE
    SYNTAX      Integer32 (0..3600)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The restart time in seconds field in the graceful-restart capability 
        which we sent to the peer."
    ::= { juniBgpPeerEntry 75 }

juniBgpPeerReceivedGracefulRestartRestartTime OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The restart time in seconds field in the graceful-restart capability 
        which we received from the peer."
    ::= { juniBgpPeerEntry 76 }

juniBgpPeerTimeUntilGracefulRestartRestartTimerExpires OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If the restart-timer is running this object contains the number of 
        seconds before it will expire. If the restart-timer is not running this 
        object contains zero. The restart-timer is used to limit the amount of 
        time that we are willing to wait for the session to come back up after 
        the peer restarts."
    ::= { juniBgpPeerEntry 77 }

juniBgpPeerTimeUntilGracefulRestartStalePathsTimerExpires OBJECT-TYPE
    SYNTAX      Integer32 (0..3600)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If the stale-paths-timer is running this object contains the number 
        of seconds before it will expire. If the stale-paths-timer is not 
        running this object contains zero. The stale-paths-timer is used to 
        limit the amount of time that we are willing continue using stale
        routes from a peer after that peer restarts."
    ::= { juniBgpPeerEntry 78 }

juniBgpPeerBfdEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Value true(1) the BFD is enabled for this peer."
    DEFVAL    { false }
    ::= { juniBgpPeerEntry 79 }

juniBgpPeerBfdMinTransmitInterval OBJECT-TYPE
    SYNTAX      Integer32 (100..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The minimum interval (in milliseconds) between BFD packets sent
        to the remote BFD speaker. In other words, it specifies the maximum
        rate at which we are willing to send BFD packets."
    DEFVAL    { 300 }
    ::= { juniBgpPeerEntry 80 }

juniBgpPeerBfdMinReceiveInterval OBJECT-TYPE
    SYNTAX      Integer32 (100..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The minimum interval (in milliseconds) between BFD packets received
        from the remote BFD speaker. In other words, it specifies the maximum
        rate at which we are willing to receive BFD packets."
    DEFVAL    { 300 }
    ::= { juniBgpPeerEntry 81 }

juniBgpPeerBfdMultiplier OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "How many BFD packets can be missed before the BFD session is declared
        down."
    DEFVAL    { 3 }
    ::= { juniBgpPeerEntry 82 }

juniBgpPeerBfdSessionUp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the BFD session up."
    ::= { juniBgpPeerEntry 83 }

juniBgpPeerBfdDetectionTime OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BFD detection time (in milliseconds) negotiated with the remote BFD 
        speaker. In other words, how quickly can a broken connection be detected."
    ::= { juniBgpPeerEntry 84 }

juniBgpPeerIbgpSinglehop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The IBGP single-hop is enabled."
    DEFVAL    { false }
    ::= { juniBgpPeerEntry 85 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Proposed AFI/SAFI peer attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP PeerProposedAfiSafiPeer Table
--
juniBgpPeerProposedAfiSafiPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerProposedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP PeerProposedAfiSafi Peer Table describes the
        BGP-specific AFI and SAFIs a peer proposed."
    ::= { juniBgpObjects 5 }

juniBgpPeerProposedAfiSafiPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerProposedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP PeerProposedAfiSafi Peer Entry describes a BGP-specific
        AFI/SAFIs proposed by one peer."
    INDEX     { juniBgpPeerProposedAfiSafiPeerVrfName,
                juniBgpPeerProposedAfiSafiPeerRemoteAddr,
                juniBgpPeerProposedAfiSafiPeerAfi,
                juniBgpPeerProposedAfiSafiPeerSafi }
    ::= { juniBgpPeerProposedAfiSafiPeerTable 1 }

JuniBgpPeerProposedAfiSafiPeerEntry ::= SEQUENCE {
    juniBgpPeerProposedAfiSafiPeerVrfName    JuniVrfName,
    juniBgpPeerProposedAfiSafiPeerRemoteAddr IpAddress,
    juniBgpPeerProposedAfiSafiPeerAfi        JuniBgpAfi,
    juniBgpPeerProposedAfiSafiPeerSafi       JuniBgpSafi,
    juniBgpPeerProposedAfiSafiPeerRowStatus  RowStatus }

juniBgpPeerProposedAfiSafiPeerVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpPeerProposedAfiSafiPeerEntry 1 }

juniBgpPeerProposedAfiSafiPeerRemoteAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpPeerProposedAfiSafiPeerEntry 2 }

juniBgpPeerProposedAfiSafiPeerAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI proposed by this peer to us."
    ::= { juniBgpPeerProposedAfiSafiPeerEntry 3 }

juniBgpPeerProposedAfiSafiPeerSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI proposed by this peer to us."
    ::= { juniBgpPeerProposedAfiSafiPeerEntry 4 }

juniBgpPeerProposedAfiSafiPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Should always be active according to the Row Status convention."
    ::= { juniBgpPeerProposedAfiSafiPeerEntry 5 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Local Proposed AFI/SAFI peer attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP LocalProposedAfiSafiPeer Table
--
juniBgpLocalProposedAfiSafiPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpLocalProposedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP LocalProposedAfiSafi Peer Table describes the
        BGP-specific AFI and SAFIs proposed to the peer."
    ::= { juniBgpObjects 6 }

juniBgpLocalProposedAfiSafiPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpLocalProposedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP LocalProposedAfiSafi Peer Entry describes a BGP-specific
        AFI/SAFIs proposed to one peer."
    INDEX     { juniBgpLocalProposedAfiSafiPeerVrfName,
                juniBgpLocalProposedAfiSafiPeerRemoteAddr,
                juniBgpLocalProposedAfiSafiPeerAfi,
                juniBgpLocalProposedAfiSafiPeerSafi }
    ::= { juniBgpLocalProposedAfiSafiPeerTable 1 }

JuniBgpLocalProposedAfiSafiPeerEntry ::= SEQUENCE {
    juniBgpLocalProposedAfiSafiPeerVrfName       JuniVrfName,
    juniBgpLocalProposedAfiSafiPeerRemoteAddr    IpAddress,
    juniBgpLocalProposedAfiSafiPeerAfi           JuniBgpAfi,
    juniBgpLocalProposedAfiSafiPeerSafi          JuniBgpSafi,
    juniBgpLocalProposedAfiSafiPeerRowStatus     RowStatus }

juniBgpLocalProposedAfiSafiPeerVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpLocalProposedAfiSafiPeerEntry 1 }

juniBgpLocalProposedAfiSafiPeerRemoteAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpLocalProposedAfiSafiPeerEntry 2 }

juniBgpLocalProposedAfiSafiPeerAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI we proposed to this peer."
    ::= { juniBgpLocalProposedAfiSafiPeerEntry 3 }

juniBgpLocalProposedAfiSafiPeerSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI we proposed to this peer."
    ::= { juniBgpLocalProposedAfiSafiPeerEntry 4 }

juniBgpLocalProposedAfiSafiPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Should always be active according to the Row Status convention."
    ::= { juniBgpLocalProposedAfiSafiPeerEntry 5 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Exchanged AFI/SAFI peer attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP ExchangedAfiSafiPeer Table
--
juniBgpExchangedAfiSafiPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpExchangedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP PeerExchangedAfiSafi Peer Table describes the
        BGP-specific AFI and SAFIs a peer exchanged."
    ::= { juniBgpObjects 7 }

juniBgpExchangedAfiSafiPeerEntry OBJECT-TYPE
    SYNTAX      JuniBgpExchangedAfiSafiPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP ExchangedAfiSafi Peer Entry describes a BGP-specific AFI/SAFIs
        exchanged by one peer."
    INDEX     { juniBgpExchangedAfiSafiPeerVrfName,
                juniBgpExchangedAfiSafiPeerRemoteAddr,
                juniBgpExchangedAfiSafiPeerAfi,
                juniBgpExchangedAfiSafiPeerSafi }
    ::= { juniBgpExchangedAfiSafiPeerTable 1 }

JuniBgpExchangedAfiSafiPeerEntry ::= SEQUENCE {
    juniBgpExchangedAfiSafiPeerVrfName       JuniVrfName,
    juniBgpExchangedAfiSafiPeerRemoteAddr    IpAddress,
    juniBgpExchangedAfiSafiPeerAfi           JuniBgpAfi,
    juniBgpExchangedAfiSafiPeerSafi          JuniBgpSafi,
    juniBgpExchangedAfiSafiPeerRowStatus     RowStatus }

juniBgpExchangedAfiSafiPeerVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpExchangedAfiSafiPeerEntry 1 }

juniBgpExchangedAfiSafiPeerRemoteAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpExchangedAfiSafiPeerEntry 2 }

juniBgpExchangedAfiSafiPeerAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI which we exchange with this peer."
    ::= { juniBgpExchangedAfiSafiPeerEntry 3 }

juniBgpExchangedAfiSafiPeerSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI which we exchange with this peer."
    ::= { juniBgpExchangedAfiSafiPeerEntry 4 }

juniBgpExchangedAfiSafiPeerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Should always be active according to the Row Status convention."
    ::= { juniBgpExchangedAfiSafiPeerEntry 5 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Dynamic Capbility attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP PeerDynamicCapability Table
--
juniBgpPeerDynamicCapabilityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerDynamicCapabilityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP PeerDynamicCapbility Table describes which dynamic capbilities
        we proposed to the peer, which the peer proposed to us, and which are
        being used."
    ::= { juniBgpObjects 29 }

juniBgpPeerDynamicCapabilityEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerDynamicCapabilityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP PeerDynamicCapbility Entry describes one dynamic capability."
    INDEX     { juniBgpPeerDynamicCapabilityPeerVrfName,
                juniBgpPeerDynamicCapabilityPeerRemoteAddr,
                juniBgpPeerDynamicCapabilityCode }
    ::= { juniBgpPeerDynamicCapabilityTable 1 }

JuniBgpPeerDynamicCapabilityEntry ::= SEQUENCE {
    juniBgpPeerDynamicCapabilityPeerVrfName        JuniVrfName,
    juniBgpPeerDynamicCapabilityPeerRemoteAddr     IpAddress,
    juniBgpPeerDynamicCapabilityCode               Integer32,
    juniBgpPeerDynamicCapabilitySent               TruthValue,
    juniBgpPeerDynamicCapabilityReceived           TruthValue }

juniBgpPeerDynamicCapabilityPeerVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpPeerDynamicCapabilityEntry 1 }

juniBgpPeerDynamicCapabilityPeerRemoteAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpPeerDynamicCapabilityEntry 2 }

juniBgpPeerDynamicCapabilityCode OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The capability code for the dynamic capability."
    ::= { juniBgpPeerDynamicCapabilityEntry 3 }

juniBgpPeerDynamicCapabilitySent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Returns true if we advertised capability code
        juniBgpPeerDynamicCapabilityCode in the dynamic-capability-negotiation
        capability which we sent to this peer."
    ::= { juniBgpPeerDynamicCapabilityEntry 4 }

juniBgpPeerDynamicCapabilityReceived OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Returns true if the peer advertised capability code
        juniBgpPeerDynamicCapabilityCode in the dynamic-capability-negotiation
        capability which this peer sent to us."
    ::= { juniBgpPeerDynamicCapabilityEntry 5 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Address Family attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Address Family Table
--
juniBgpPeerAddressFamilyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Address Family Table describes the BGP-specific
        attributes of an Address Family for a peer."
    ::= { juniBgpObjects 8 }

juniBgpPeerAddressFamilyEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP ExchangedAfiSafi Peer Entry describes a BGP-specific AFI/SAFIs
        exchanged by one peer."
    INDEX     { juniBgpPeerAddressFamilyVrfName,
                juniBgpPeerAddressFamilyAfi,
                juniBgpPeerAddressFamilySafi,
                juniBgpPeerAddressFamilyRemoteAddress }
    ::= { juniBgpPeerAddressFamilyTable 1 }

JuniBgpPeerAddressFamilyEntry ::= SEQUENCE {
    juniBgpPeerAddressFamilyVrfName                 JuniVrfName,
    juniBgpPeerAddressFamilyAfi                     JuniBgpAfi,
    juniBgpPeerAddressFamilySafi                    JuniBgpSafi,
    juniBgpPeerAddressFamilyRemoteAddress           IpAddress,
    juniBgpPeerAddressFamilyPeerGroup               DisplayString,
    juniBgpPeerAddressFamilyDefaultOriginate        TruthValue,
    juniBgpPeerAddressFamilyNextHopSelf             TruthValue,
    juniBgpPeerAddressFamilyNextHopUnchanged        TruthValue,
    juniBgpPeerAddressFamilySendCommunity           TruthValue,
    juniBgpPeerAddressFamilyDistributeListIn        DisplayString,
    juniBgpPeerAddressFamilyDistributeListOut       DisplayString,
    juniBgpPeerAddressFamilyPrefixListIn            DisplayString,
    juniBgpPeerAddressFamilyPrefixListOut           DisplayString,
    juniBgpPeerAddressFamilyPrefixTreeIn            DisplayString,
    juniBgpPeerAddressFamilyPrefixTreeOut           DisplayString,
    juniBgpPeerAddressFamilyFilterListIn            DisplayString,
    juniBgpPeerAddressFamilyFilterListOut           DisplayString,
    juniBgpPeerAddressFamilyFilterListWeight        DisplayString,
    juniBgpPeerAddressFamilyFilterListWeightValue   Unsigned32,
    juniBgpPeerAddressFamilyRouteMapIn              DisplayString,
    juniBgpPeerAddressFamilyRouteMapOut             DisplayString,
    juniBgpPeerAddressFamilyRouteReflectorClient    TruthValue,
    juniBgpPeerAddressFamilyRouteLimitWarn          Unsigned32,
    juniBgpPeerAddressFamilyRouteLimitReset         Unsigned32,
    juniBgpPeerAddressFamilyRouteLimitWarnOnly      TruthValue,
    juniBgpPeerAddressFamilyRemovePrivateAs         TruthValue,
    juniBgpPeerAddressFamilyUnsuppressMap           DisplayString,
    juniBgpPeerAddressFamilyInboundSoftReconfig     TruthValue,
    juniBgpPeerAddressFamilyResetConnectionType     JuniBgpResetConnectionType,
    juniBgpPeerAddressFamilyRowStatus               RowStatus,
    juniBgpPeerAddressFamilyAsOverride              TruthValue,
    juniBgpPeerAddressFamilyAllowAsIn               Integer32,
    juniBgpPeerAddressFamilySendExtendedCommunity   TruthValue,
    juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend           TruthValue,
    juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive        TruthValue,
    juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend      TruthValue,
    juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive   TruthValue,
    juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend            TruthValue,
    juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive         TruthValue,
    juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend       TruthValue,
    juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive    TruthValue,
    juniBgpPeerAddressFamilyReceivedOrfEntriesLimit                 Unsigned32,
    juniBgpPeerAddressFamilyReceivedPrefixListOrfName               DisplayString,
    juniBgpPeerAddressFamilyMaximumPrefixStrict                     TruthValue,
    juniBgpPeerAddressFamilyUnconfiguredAttributes                  BITS,
    juniBgpPeerAddressFamilySendLabel                               TruthValue,
    juniBgpPeerAddressFamilyDefaultOriginateRouteMap                DisplayString,
    juniBgpPeerAddressFamilySentCapabilityGracefulRestart           TruthValue,
    juniBgpPeerAddressFamilyReceivedCapabilityGracefulRestart       TruthValue,
    juniBgpPeerAddressFamilySentForwardingStatePreserved            TruthValue,
    juniBgpPeerAddressFamilyReceivedForwardingStatePreserved        TruthValue,
    juniBgpPeerAddressFamilySentEndOfRibMarker                      TruthValue,
    juniBgpPeerAddressFamilyReceivedEndOfRibMarker                  TruthValue,
    juniBgpPeerAddressFamilyWaitingForEndOfRibBeforeFlushStaleRoutes  TruthValue,
    juniBgpPeerAddressFamilyWaitingForEndOfRibBeforePathSelection     TruthValue }

juniBgpPeerAddressFamilyVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer is configured.  The empty string indicates that this peer is not in
        a VRF."
    ::= { juniBgpPeerAddressFamilyEntry 1 }

juniBgpPeerAddressFamilyAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family in which this peer is configured."
    ::= { juniBgpPeerAddressFamilyEntry 2 }

juniBgpPeerAddressFamilySafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family in which this peer is configured."
    ::= { juniBgpPeerAddressFamilyEntry 3 }

juniBgpPeerAddressFamilyRemoteAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The remote IP address of this entry's BGP peer."
    ::= { juniBgpPeerAddressFamilyEntry 4 }

juniBgpPeerAddressFamilyPeerGroup OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Peer group membership (null if none)."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 5 }

juniBgpPeerAddressFamilyDefaultOriginate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to advertise a default route to
        this peer, if present."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 6 }

juniBgpPeerAddressFamilyNextHopSelf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to always advertise this router as
        the next hop."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 7 }

juniBgpPeerAddressFamilySendCommunity OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send COMMUNITIES attributes in
        updates."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 8 }

juniBgpPeerAddressFamilyDistributeListIn OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound distribute IP access list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 9 }

juniBgpPeerAddressFamilyDistributeListOut OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound distribute IP access list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 10 }

juniBgpPeerAddressFamilyPrefixListIn OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound IP prefix list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 11 }

juniBgpPeerAddressFamilyPrefixListOut OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound IP prefix list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 12 }

juniBgpPeerAddressFamilyPrefixTreeIn OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound IP prefix tree name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 13 }

juniBgpPeerAddressFamilyPrefixTreeOut OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound IP prefix tree name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 14 }

juniBgpPeerAddressFamilyFilterListIn OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 15 }

juniBgpPeerAddressFamilyFilterListOut OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 16 }

juniBgpPeerAddressFamilyFilterListWeight OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 17 }

juniBgpPeerAddressFamilyFilterListWeightValue OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight to apply on weight filter list matches."
    DEFVAL    { 0 }
    ::= { juniBgpPeerAddressFamilyEntry 18 }

juniBgpPeerAddressFamilyRouteMapIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound route-map name.  An empty string means no inbound
        route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 19 }

juniBgpPeerAddressFamilyRouteMapOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound route-map name.  An empty string means no outbound
        route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 20 }

juniBgpPeerAddressFamilyRouteReflectorClient OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The peer is a route reflector client."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 21 }

juniBgpPeerAddressFamilyRouteLimitWarn OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Log a warning if pathCount exceeds this value."
    DEFVAL    { 1000000 }
    ::= { juniBgpPeerAddressFamilyEntry 22 }

juniBgpPeerAddressFamilyRouteLimitReset OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Reset connection is pathCount exceeds this value."
    DEFVAL    { 10000000 }
    ::= { juniBgpPeerAddressFamilyEntry 23 }

juniBgpPeerAddressFamilyRouteLimitWarnOnly OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Warn, but do not reset, if the path limit is exceeded."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 24 }

juniBgpPeerAddressFamilyRemovePrivateAs OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Remove private AS numbers from AS paths."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 25 }

juniBgpPeerAddressFamilyUnsuppressMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unsuppress route-map name.  Routes which are sent to this peer and
        which match this route-map are not subject to suppression by
        summary-only aggregates.  An empty string means no unsuppress
        route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 26 }

juniBgpPeerAddressFamilyInboundSoftReconfig OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Inbound soft-reconfiguration is enabled.  If inbound
        soft-reconfiguration is enabled we keep a copy of each received route
        before inbound policy was applied."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 27 }

juniBgpPeerAddressFamilyResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes one individual address family
        within the session to the peer to be cleared; the value determines what
        type of clear is executed (hard clear, soft clear in, soft clear out,
        etc.).  Note that hard clearing an address family within a session will
        bounce the session and thus also affect the other address families in
        the session.  Reading this object has no effect and always returns
        resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpPeerAddressFamilyEntry 28 }

juniBgpPeerAddressFamilyRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpPeerAddressFamilyEntry 29 }

juniBgpPeerAddressFamilyAsOverride OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Replace the AS number of the peer by our own AS number in the AS-path
        attribute in UPDATEs sent to this peer."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 30 }

juniBgpPeerAddressFamilyAllowAsIn OBJECT-TYPE
    SYNTAX      Integer32 (0..10)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of times our own AS number is allowed to occur in a
        received AS-path before that received route is considered to be a loop
        and therefore rejected."
    DEFVAL    { 0 }
    ::= { juniBgpPeerAddressFamilyEntry 31 }

juniBgpPeerAddressFamilySendExtendedCommunity OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send EXTENDED-COMMUNITIES
        attributes in updates."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 32 }

juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST ORF SEND
        CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 33 }

juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST ORF RECEIVE
        CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 34 }

juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST-CISCO ORF SEND
        CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 35 }

juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST-CISCO ORF
        RECEIVE CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 36 }

juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has sent PREFIX-LIST ORF SEND CAPABILITY to us for this
        address family."
    ::= { juniBgpPeerAddressFamilyEntry 37 }

juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has sent PREFIX-LIST ORF RECEIVE CAPABILITY to us for this
        address family."
    ::= { juniBgpPeerAddressFamilyEntry 38 }

juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has sent PREFIX-LIST-CISCO ORF SEND CAPABILITY to us for this
        address family."
    ::= { juniBgpPeerAddressFamilyEntry 39 }

juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The peer has sent PREFIX-LIST-CISCO ORF RECEIVE CAPABILITY to us for
        this address family."
    ::= { juniBgpPeerAddressFamilyEntry 40 }

juniBgpPeerAddressFamilyReceivedOrfEntriesLimit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Ignore received ORF entries for this address family if count exceeds
        this value."
    DEFVAL    { 4294967295 }
    ::= { juniBgpPeerAddressFamilyEntry 41 }

juniBgpPeerAddressFamilyReceivedPrefixListOrfName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The prefix list name containing ORF entries received from the peer
         for this address family"
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 42 }

juniBgpPeerAddressFamilyMaximumPrefixStrict OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If the juniBgpPeerAddressFamilyInboundSoftReconfig object is set to
        true then routes which were rejected by inbound policy are stored in
        memory anyway.  The juniBgpPeerAddressFamilyMaximumPrefixStrict object
        determines whether or not these routes count towards the maximum number
        of routes from the peer (as configured in
        juniBgpPeerAddressFamilyRouteLimitWarn and/or
        juniBgpPeerAddressFamilyRouteLimitReset)."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 43 }

juniBgpPeerAddressFamilyUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpPeerAddressFamilyPeerGroup(0),
        juniBgpPeerAddressFamilyDefaultOriginate(1),
        juniBgpPeerAddressFamilyNextHopSelf(2),
        juniBgpPeerAddressFamilySendCommunity(3),
        juniBgpPeerAddressFamilyDistributeListIn(4),
        juniBgpPeerAddressFamilyDistributeListOut(5),
        juniBgpPeerAddressFamilyPrefixListIn(6),
        juniBgpPeerAddressFamilyPrefixListOut(7),
        juniBgpPeerAddressFamilyPrefixTreeIn(8),
        juniBgpPeerAddressFamilyPrefixTreeOut(9),
        juniBgpPeerAddressFamilyFilterListIn(10),
        juniBgpPeerAddressFamilyFilterListOut(11),
        juniBgpPeerAddressFamilyFilterListWeight(12),
        juniBgpPeerAddressFamilyFilterListWeightValue(13),
        juniBgpPeerAddressFamilyRouteMapIn(14),
        juniBgpPeerAddressFamilyRouteMapOut(15),
        juniBgpPeerAddressFamilyRouteReflectorClient(16),
        juniBgpPeerAddressFamilyRouteLimitWarn(17),
        juniBgpPeerAddressFamilyRouteLimitReset(18),
        juniBgpPeerAddressFamilyRouteLimitWarnOnly(19),
        juniBgpPeerAddressFamilyRemovePrivateAs(20),
        juniBgpPeerAddressFamilyUnsuppressMap(21),
        juniBgpPeerAddressFamilyInboundSoftReconfig(22),
        juniBgpPeerAddressFamilyAsOverride(23),
        juniBgpPeerAddressFamilyAllowAsIn(24),
        juniBgpPeerAddressFamilySendExtendedCommunity(25),
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend(26),
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive(27),
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend(28),
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive(29),
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit(30),
        juniBgpPeerAddressFamilyMaximumPrefixStrict(31),
        juniBgpPeerAddressFamilySendLabel(32),
        juniBgpPeerAddressFamilyDefaultOriginateRouteMap(33),
        juniBgpPeerAddressFamilyNextHopUnchanged(34) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpPeerAddressFamilyEntry 44 }

juniBgpPeerAddressFamilySendLabel OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Send labeled prefixes to this peer for this address-family."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 45 }

juniBgpPeerAddressFamilyDefaultOriginateRouteMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The route-map to apply to the default route which is advertised to this
        peer as a result of setting juniBgpPeerAddressFamilyDefaultOriginate to
        true.  An empty string means that no route-map is applied."
    DEFVAL    { "" }
    ::= { juniBgpPeerAddressFamilyEntry 46 }

juniBgpPeerAddressFamilySentCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) means that an AFI-SAFI block for this address-family
        was included in the graceful-restart capability which we sent to this 
        peer."
    ::= { juniBgpPeerAddressFamilyEntry 47 }

juniBgpPeerAddressFamilyReceivedCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) means that an AFI-SAFI block for this address-family
        was included in the graceful-restart capability which we received from
        this peer."
    ::= { juniBgpPeerAddressFamilyEntry 48 }

juniBgpPeerAddressFamilySentForwardingStatePreserved OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that the OPEN message most recently sent to 
        this peer contained the graceful-restart capability which included an 
        AFI-SAFI block for this address-family with the Forwarding-State (F) 
        bit in the Flags for Address Family field set to one (this indicates 
        that forwarding state has been preserved for the address family during 
        the previous restart)."
    ::= { juniBgpPeerAddressFamilyEntry 49 }

juniBgpPeerAddressFamilyReceivedForwardingStatePreserved OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that the OPEN message most recently received 
        from this peer contained the graceful-restart capability which included
        an AFI-SAFI block for this address-family with the Forwarding-State 
        (F) bit in the Flags for Address Family field set to one (this 
        indicates that the peer preserved forwarding state for the address 
        family during the previous restart)."
    ::= { juniBgpPeerAddressFamilyEntry 50 }

juniBgpPeerAddressFamilySentEndOfRibMarker OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that we have sent an End-of-RIB marker
        for this address-family to this peer since the session became
        established."
    ::= { juniBgpPeerAddressFamilyEntry 51 }

juniBgpPeerAddressFamilyReceivedEndOfRibMarker OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that we have received an End-of-RIB marker
        for this address-family from this peer since the session became
        established."
    ::= { juniBgpPeerAddressFamilyEntry 52 }

juniBgpPeerAddressFamilyWaitingForEndOfRibBeforeFlushStaleRoutes OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that we are still waiting for an 
        End-of-RIB marker from this peer before flushing all remaining
        stale routes from the address-family."
    ::= { juniBgpPeerAddressFamilyEntry 53 }

juniBgpPeerAddressFamilyWaitingForEndOfRibBeforePathSelection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value true(1) indicates that we are still waiting for an
        End-of-RIB marker from this peer before we stop deferring
        path selection for this address-family."
    ::= { juniBgpPeerAddressFamilyEntry 54 }

juniBgpPeerAddressFamilyNextHopUnchanged OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to always advertise the 
        the next-hop unchanged to this peer for this address-family."
    DEFVAL    { false }
    ::= { juniBgpPeerAddressFamilyEntry 55 }

-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Address Family Conditional Advertisement attributes
-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Address Family Conditional Advertisement Table
--
juniBgpPeerAddressFamilyConditionalAdvTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerAddressFamilyConditionalAdvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Address Family Conditional Advertisement Table 
        describes the BGP conditional advertisement attributes in an Address 
        Family for a peer."
    ::= { juniBgpObjects 30 }

juniBgpPeerAddressFamilyConditionalAdvEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerAddressFamilyConditionalAdvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Address Family Conditional Advertisement Entry 
        describes a specific conditional advertisement entry in Address Family 
        for a peer."
    INDEX     { juniBgpPeerAddressFamilyVrfName,
                juniBgpPeerAddressFamilyAfi,
                juniBgpPeerAddressFamilySafi,
                juniBgpPeerAddressFamilyRemoteAddress,
                juniBgpPeerAddressFamilyConditionalAdvAdvertiseMap }
    ::= { juniBgpPeerAddressFamilyConditionalAdvTable 1 }

JuniBgpPeerAddressFamilyConditionalAdvEntry::= SEQUENCE {
    juniBgpPeerAddressFamilyConditionalAdvAdvertiseMap  JuniBgpAdvertiseMapName,
    juniBgpPeerAddressFamilyConditionalAdvConditionMap  DisplayString,
    juniBgpPeerAddressFamilyConditionalAdvIsExistMap    TruthValue,
    juniBgpPeerAddressFamilyConditionalAdvSequenceNum   Integer32,
    juniBgpPeerAddressFamilyConditionalAdvStatus    JuniBgpConditionalAdvStatus,
    juniBgpPeerAddressFamilyConditionalAdvRowStatus     RowStatus }

juniBgpPeerAddressFamilyConditionalAdvAdvertiseMap OBJECT-TYPE
    SYNTAX      JuniBgpAdvertiseMapName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The route-map for conditional advertisement."
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 1 }

juniBgpPeerAddressFamilyConditionalAdvConditionMap OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The condition-map to apply for this conditional advertisement entry."
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 2 }

juniBgpPeerAddressFamilyConditionalAdvIsExistMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this value to true(1) if the condition-map specified should be 
         applied as an exist-map. If the condition-map should be applied as 
         a non-exist-map, set it to false(2). "
    DEFVAL    { true }
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 3 }

juniBgpPeerAddressFamilyConditionalAdvSequenceNum OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The sequence number of this conditional advertisement entry."
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 4 }

juniBgpPeerAddressFamilyConditionalAdvStatus OBJECT-TYPE
    SYNTAX      JuniBgpConditionalAdvStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This value will be advertise(1) if the status of this advertise-map is
         evaluated as advertise based on the condition-map. Otherwise it is 
         withdraw(2)."
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 5 }

juniBgpPeerAddressFamilyConditionalAdvRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        " Controls creation/deletion of entries in this table according to the 
        RowStatus textual convention, constrained to support the following 
        values only:
            createAndGo,
            destroy "
    ::= { juniBgpPeerAddressFamilyConditionalAdvEntry 6 }



-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Group attributes
-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Group Table
--
juniBgpPeerGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Group Table describes the BGP-specific
        characteristics of peer groups."
    ::= { juniBgpObjects 9 }

juniBgpPeerGroupEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Peer Group Entry describes BGP-specific characteristics of one
        peer group."
    INDEX     { juniBgpPeerGroupVrfName,
                juniBgpPeerGroupGroupName }
    ::= { juniBgpPeerGroupTable 1 }

JuniBgpPeerGroupEntry ::= SEQUENCE {
    juniBgpPeerGroupVrfName                         JuniVrfName,
    juniBgpPeerGroupGroupName                       DisplayString,
    juniBgpPeerGroupAdminStatus                     INTEGER,
    juniBgpPeerGroupRemoteAsNumber                  Integer32,
    juniBgpPeerGroupRetryInterval                   Integer32,
    juniBgpPeerGroupConfigHoldTime                  Integer32,
    juniBgpPeerGroupConfigKeepAliveInterval         Integer32,
    juniBgpPeerGroupAsOriginationInterval           Integer32,
    juniBgpPeerGroupAdvertisementInterval           Integer32,
    juniBgpPeerGroupDescription                     DisplayString,
    juniBgpPeerGroupWeight                          Unsigned32,
    juniBgpPeerGroupEbgpMultihop                    TruthValue,
    juniBgpPeerGroupEbgpMultihopTtl                 Integer32,
    juniBgpPeerGroupUpdateSource                    IpAddress,
    juniBgpPeerGroupMd5Password                     OCTET STRING,
    juniBgpPeerGroupMaxUpdateSize                   Unsigned32,
    juniBgpPeerGroupResetConnectionType             JuniBgpResetConnectionType,
    juniBgpPeerGroupRowStatus                       RowStatus,
    juniBgpPeerGroupLocalAsNumber                   Integer32,
    juniBgpPeerGroupFourOctetRemoteAsNumber         JuniBgpFourOctetAsNumber,
    juniBgpPeerGroupFourOctetLocalAsNumber          JuniBgpFourOctetAsNumber,
    juniBgpPeerGroupShouldAdvertiseCapabilitiesOption               TruthValue,
    juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh           TruthValue,
    juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco      TruthValue,
    juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers     TruthValue,
    juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg   TruthValue,
    juniBgpPeerGroupUnconfiguredAttributes          BITS,
    juniBgpPeerGroupSiteOfOrigin                    OCTET STRING,
    juniBgpPeerGroupLenient                         TruthValue,
    juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg TruthValue,
    juniBgpPeerGroupPassive                         TruthValue,
    juniBgpPeerGroupConfiguredPeerType              INTEGER,
    juniBgpPeerGroupAllowAccessListName             DisplayString,
    juniBgpPeerGroupAllowMaxPeers                   Unsigned32,
    juniBgpPeerGroupCurrentDynamicPeerCount         Unsigned32,
    juniBgpPeerGroupHighWaterMarkDynamicPeerCount   Unsigned32,
    juniBgpPeerGroupRejectedDynamicPeerCount        Unsigned32,
    juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart         TruthValue,
    juniBgpPeerGroupGracefulRestartRestartTime                       Integer32,
    juniBgpPeerGroupGracefulRestartStalePathsTime                    Integer32,
    juniBgpPeerGroupBfdEnabled                                       TruthValue,
    juniBgpPeerGroupBfdMinTransmitInterval                           Integer32,
    juniBgpPeerGroupBfdMinReceiveInterval                            Integer32,
    juniBgpPeerGroupBfdMultiplier                                    Integer32,
    juniBgpPeerGroupIbgpSinglehop                                    TruthValue }

juniBgpPeerGroupVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer-group is configured.  The empty string indicates that this
        peer-group is not in a VRF."
    ::= { juniBgpPeerGroupEntry 1 }

juniBgpPeerGroupGroupName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the Peer group this instance configures."
    ::= { juniBgpPeerGroupEntry 2 }

juniBgpPeerGroupAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    stop(1),
                    start(2) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The desired state of the BGP connection.  A transition from 'stop' to
        'start' will cause the BGP Start Event to be generated.  A transition
        from 'start' to 'stop' will cause the BGP Stop Event to be generated.
        This parameter can be used to restart BGP peer connections.  Care should
        be used in providing write access to this object without adequate
        authentication."
    ::= { juniBgpPeerGroupEntry 3 }

juniBgpPeerGroupRemoteAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Deprecated.  The peer's autonomous system number.  This object has been
        replaced by juniBgpPeerGroupFourOctetRemoteAsNumber.  It is still
        possible to get and set this object, but if the actual remote AS number
        is greater than 65535, getting this object returns 23456 (AS-TRANS)."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 4 }

juniBgpPeerGroupRetryInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the ConnectRetry timer.  The suggested
        value for this timer is 120 seconds."
    DEFVAL    { 120 }
    ::= { juniBgpPeerGroupEntry 5 }

juniBgpPeerGroupConfigHoldTime OBJECT-TYPE
    SYNTAX      Integer32 (0|3..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the Hold Time configured for this BGP
        speaker with this peer.  This value is placed in an OPEN message sent to
        this peer by this BGP speaker, and is compared with the Hold Time field
        in an OPEN message received from the peer when determining the Hold Time
        (bgpPeerHoldTime) with the peer.  This value must not be less than three
        seconds if it is not zero (0) in which case the Hold Time is NOT to be
        established with the peer.  The suggested value for this timer is 90
        seconds."
    DEFVAL    { 90 }
    ::= { juniBgpPeerGroupEntry 6 }

juniBgpPeerGroupConfigKeepAliveInterval OBJECT-TYPE
    SYNTAX      Integer32 (0..21845)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the KeepAlive timer configured for this
        BGP speaker with this peer.  The value of this object will only
        determine the KEEPALIVE messages' frequency relative to the value
        specified in bgpPeerHoldTimeConfigured; the actual time interval for the
        KEEPALIVE messages is indicated by bgpPeerKeepAlive.  A reasonable
        maximum value for this timer would be configured to be one third of that
        of bgpPeerHoldTimeConfigured.  If the value of this object is zero (0),
        no periodical KEEPALIVE messages are sent to the peer after the BGP
        connection has been established.  The suggested value for this timer is
        30 seconds."
    DEFVAL    { 30 }
    ::= { juniBgpPeerGroupEntry 7 }

juniBgpPeerGroupAsOriginationInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the MinASOriginationInterval timer.  The
        suggested value for this timer is 10 seconds."
    DEFVAL    { 10 }
    ::= { juniBgpPeerGroupEntry 8 }

juniBgpPeerGroupAdvertisementInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Time interval in seconds for the MinRouteAdvertisementInterval timer.
        The suggested value for this timer is 30 seconds."
    DEFVAL    { 30 }
    ::= { juniBgpPeerGroupEntry 9 }

juniBgpPeerGroupDescription OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Peer textual description."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupEntry 10 }

juniBgpPeerGroupWeight OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight assigned to routes learned from peer."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 11 }

juniBgpPeerGroupEbgpMultihop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The EBGP multihop is enabled."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupEntry 12 }

juniBgpPeerGroupEbgpMultihopTtl OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum number of hops to the external peer (only relevant if EBGP
        multihop turned on)."
    DEFVAL    { 255 }
    ::= { juniBgpPeerGroupEntry 13 }

juniBgpPeerGroupUpdateSource OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source IP address for peer connections."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 14 }

juniBgpPeerGroupMd5Password OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The password for MD5 authentication.  Reading this object always
        results in an OCTET STRING of length zero."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupEntry 15 }

juniBgpPeerGroupMaxUpdateSize OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum size in bytes of transmitted UPDATE messages."
    DEFVAL    { 4096 }
    ::= { juniBgpPeerGroupEntry 16 }

juniBgpPeerGroupResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes the sessions to all peers in the
        peer-group to be cleared; the value determines what type of clear is
        executed (hard clear, soft clear in, soft clear out, etc.).  Reading
        this object has no effect and always returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpPeerGroupEntry 17 }

juniBgpPeerGroupRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpPeerGroupEntry 18 }

juniBgpPeerGroupLocalAsNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Deprecated.  The local autonomous system number to be used for members.
        of this peer-group.  Value zero (0) means that the global local automous
        system number (as specified in the object juniBgpPeerGroupLocalAsNumber)
        is used.  This object has been replaced by
        juniBgpPeerGroupFourOctetLocalAsNumber.  It is still possible to get and
        set this object, but if the actual local AS number is greater than
        65535, getting this object returns AS-TRANS (23456)."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 19 }

juniBgpPeerGroupFourOctetRemoteAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The peer's four-octet autonomous system number."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 20 }

juniBgpPeerGroupFourOctetLocalAsNumber OBJECT-TYPE
    SYNTAX      JuniBgpFourOctetAsNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The four-octet local autonomous system number to be used for this peer.
        Value zero (0) means that the global local automous system number (as
        specified in the object juniBgpPeerGroupFourOctetLocalAsNumber) is
        used."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 21 }

juniBgpPeerGroupShouldAdvertiseCapabilitiesOption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the capabilities option should be included
        in OPEN messages sent to this peer.  Even if this object is set to true,
        BGP may decide not to include the capabilities option in OPEN messages
        sent to this peer (for example when it is detected that the peer does
        not support capability negotiation).  The
        juniBgpPeerSentCapabilitiesOption can be used to determine whether or
        not the OPEN message sent to the peer actually contained the
        capabilities option."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 22 }

juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the route-refresh capability should be
        advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the route-refresh capability to this peer (for
        example when it is detected that the peer does not support capability
        negotiation or when it is detected that the peer does not support the
        route-refresh capability and incorrectly sends an unsupported capability
        notification).  The juniBgpPeerSentCapabilityRouteRefresh can be used to
        determine whether or not the capability was actually advertised to the
        peer."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 23 }

juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the route-refresh-cisco capability should be
        advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the route-refresh-cisco capability to this peer
        (for example when it is detected that the peer does not support
        capability negotiation or when it is detected that the peer does not
        support the route-refresh-cisco capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityRouteRefreshCisco can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 24 }

juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the four-octet-as-numbers capability should
        be advertised to this peer.  Even if this object is set to true, BGP may
        decide not to advertise the four-octet-as-numbers capability to this
        peer (for example when it is detected that the peer does not support
        capability negotiation or when it is detected that the peer does not
        support the four-octet-as-numbers capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityFourOctetAsNumbers can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 25 }

juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the dynamic-capability-negotiation
        capability should be advertised to this peer.  Even if this object is
        set to true, BGP may decide not to advertise the
        dynamic-capability-negotiation capability to this peer (for example when
        it is detected that the peer does not support capability negotiation or
        when it is detected that the peer does not support the
        dynamic-capability-negotiation capability and incorrectly sends an
        unsupported capability notification).  The
        juniBgpPeerSentCapabilityDynamicCapabilityNeg can be used to determine
        whether or not the capability was actually advertised to the peer."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 26 }

juniBgpPeerGroupUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpPeerGroupAdminStatus(0),
        juniBgpPeerGroupRetryInterval(1),
        juniBgpPeerGroupConfigHoldTime(2),
        juniBgpPeerGroupConfigKeepAliveInterval(3),
        juniBgpPeerGroupAsOriginationInterval(4),
        juniBgpPeerGroupAdvertisementInterval(5),
        juniBgpPeerGroupDescription(6),
        juniBgpPeerGroupWeight(7),
        juniBgpPeerGroupEbgpMultihop(8),
        juniBgpPeerGroupEbgpMultihopTtl(9),
        juniBgpPeerGroupUpdateSource(10),
        juniBgpPeerGroupMd5Password(11),
        juniBgpPeerGroupMaxUpdateSize(12),
        juniBgpPeerGroupFourOctetRemoteAsNumber(13),
        juniBgpPeerGroupFourOctetLocalAsNumber(14),
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption(15),
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh(16),
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco(17),
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers(18),
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg(19),
        juniBgpPeerGroupSiteOfOrigin(20),
        juniBgpPeerGroupLenient(21),
        juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg(22),
        juniBgpPeerGroupPassive(23),
        juniBgpPeerGroupConfiguredPeerType(24),
        juniBgpPeerGroupAllowAccessListName(25),
        juniBgpPeerGroupAllowMaxPeers(26),
        juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart(27),
        juniBgpPeerGroupGracefulRestartRestartTime(28),
        juniBgpPeerGroupGracefulRestartStalePathsTime(29),
        juniBgpPeerGroupBfdEnabled(30),
        juniBgpPeerGroupBfdMinTransmitInterval(31),
        juniBgpPeerGroupBfdMinReceiveInterval(32),
        juniBgpPeerGroupBfdMultiplier(33),
        juniBgpPeerGroupIbgpSinglehop(34) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpPeerGroupEntry 27 }

juniBgpPeerGroupSiteOfOrigin OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The site-of-origin.  This site-of-origin is added to the extended
        communities for all routes received from the peer.  When sending routes
        to this peer-group all routes with this extended community are filtered.
        The null extended community (all 8 bytes zero) means that no
        site-of-origin is configured for this peer.  It is not allowed to set
        this object to any extended community other than a site-of-origin type
        of extended community or null."
    ::= { juniBgpPeerGroupEntry 28 }

juniBgpPeerGroupLenient OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Enable Lenient behavior for member peers so that they are more tolerant
        of finite state machine errors and malformed messages received from the
        remote peer and avoid terminating the peer session whenever possible."
    ::= { juniBgpPeerGroupEntry 29 }

juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the deprecated-dynamic-capability-
        negotiation capability should be advertised to this peer.  Even if this
        object is set to true, BGP may decide not to advertise the
        deprecated-dynamic-capability-negotiation capability to this peer (for
        example when it is detected that the peer does not support capability
        negotiation or when it is detected that the peer does not support the
        deprecated-dynamic-capability-negotiation capability and incorrectly
        sends an unsupported capability notification).  The
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg can be used to
        determine whether or not the capability was actually advertised to the
        peer."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 30 }

juniBgpPeerGroupPassive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) to make members of this peer-group passive."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupEntry 31 }

juniBgpPeerGroupConfiguredPeerType OBJECT-TYPE
    SYNTAX      INTEGER {
                    peerTypeNotConfigured(0),
                    peerTypeInternal(1),
                    peerTypeExternal(2),
                    peerTypeConfederation(3) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The type of the members of the peer-group."
    DEFVAL    { peerTypeNotConfigured }
    ::= { juniBgpPeerGroupEntry 32 }

juniBgpPeerGroupAllowAccessListName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The name of the access-list for promiscuous peers.  When an incoming
        connection arrives whose remote address matches this access-list the
        connection is accepted and a dynamic peer is created.  An empty string
        means that promiscuous peers are disabled for this peer-group."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupEntry 33 }

juniBgpPeerGroupAllowMaxPeers OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of dynamic peers for this peer-group.  Zero means
        that there is no limit."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 34 }

juniBgpPeerGroupCurrentDynamicPeerCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current number of dynamic peers for this peer-group."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 35 }

juniBgpPeerGroupHighWaterMarkDynamicPeerCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The high water mark for the number of dynamic peers for this
        peer-group."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 36 }

juniBgpPeerGroupRejectedDynamicPeerCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of connection attempts for dynamic peers that were rejected
        because the number number of dynamic peers was reached."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupEntry 37 }

juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true if the graceful-restart capability should be
        advertised to all members of the peer-group."
    DEFVAL    { true }
    ::= { juniBgpPeerGroupEntry 38 }
    
juniBgpPeerGroupGracefulRestartRestartTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The restart-time advertised in the graceful-restart capability sent to 
        members of this peer-group. This is the estimated time (in seconds) it 
        will take for the BGP session to be re-established after a restart. This 
        can be used to speed up routing convergence by the peer in case that this 
        BGP speaker does not come back after a restart. This value (if configured)
        overrides the global value configured in juniBgpGracefulRestartRestartTime."
    DEFVAL    { 120 }
    ::= { juniBgpPeerGroupEntry 39 }

juniBgpPeerGroupGracefulRestartStalePathsTime OBJECT-TYPE
    SYNTAX      Integer32 (1..3600)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum amount of time in seconds we keep stale routes after the
        session to a member of this peer-group goes down. This value (if 
        configured) overrides the global value configured in 
        juniBgpGracefulRestartStalePathsTime."
    DEFVAL    { 360 }
    ::= { juniBgpPeerGroupEntry 40 }

juniBgpPeerGroupBfdEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Value true(1) the BFD is enabled for this peer-group."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupEntry 41 }

juniBgpPeerGroupBfdMinTransmitInterval OBJECT-TYPE
    SYNTAX      Integer32 (100..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The minimum interval (in milliseconds) between BFD packets sent
        to the remote BFD speaker. In other words, it specifies the maximum
        rate at which we are willing to send BFD packets."
    DEFVAL    { 300 }
    ::= { juniBgpPeerGroupEntry 42 }

juniBgpPeerGroupBfdMinReceiveInterval OBJECT-TYPE
    SYNTAX      Integer32 (100..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The minimum interval (in milliseconds) between BFD packets received
        from the remote BFD speaker. In other words, it specifies the maximum
        rate at which we are willing to receive BFD packets."
    DEFVAL    { 300 }
    ::= { juniBgpPeerGroupEntry 43 }

juniBgpPeerGroupBfdMultiplier OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "How many BFD packets can be missed before the BFD session is declared
        down."
    DEFVAL    { 3 }
    ::= { juniBgpPeerGroupEntry 44 }

juniBgpPeerGroupIbgpSinglehop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The IBGP single-hop is enabled."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupEntry 45 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Group Address Family attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Group Address Family Table
--
juniBgpPeerGroupAddressFamilyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerGroupAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Group Address Family Table describes the
        BGP-specific attributes of an Address Family for a peer group."
    ::= { juniBgpObjects 10 }

juniBgpPeerGroupAddressFamilyEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerGroupAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Peer Group Address Family Entry describes."
    INDEX     { juniBgpPeerGroupAddressFamilyVrfName,
                juniBgpPeerGroupAddressFamilyAfi,
                juniBgpPeerGroupAddressFamilySafi,
                juniBgpPeerGroupGroupAddressFamilyGroupName }
    ::= { juniBgpPeerGroupAddressFamilyTable 1 }

JuniBgpPeerGroupAddressFamilyEntry ::= SEQUENCE {
    juniBgpPeerGroupAddressFamilyVrfName                JuniVrfName,
    juniBgpPeerGroupAddressFamilyAfi                    JuniBgpAfi,
    juniBgpPeerGroupAddressFamilySafi                   JuniBgpSafi,
    juniBgpPeerGroupGroupAddressFamilyGroupName         DisplayString,
    juniBgpPeerGroupAddressFamilyDefaultOriginate       TruthValue,
    juniBgpPeerGroupAddressFamilyNextHopSelf            TruthValue,
    juniBgpPeerGroupAddressFamilyNextHopUnchanged       TruthValue,
    juniBgpPeerGroupAddressFamilySendCommunity          TruthValue,
    juniBgpPeerGroupAddressFamilyDistributeListIn       DisplayString,
    juniBgpPeerGroupAddressFamilyDistributeListOut      DisplayString,
    juniBgpPeerGroupAddressFamilyPrefixListIn           DisplayString,
    juniBgpPeerGroupAddressFamilyPrefixListOut          DisplayString,
    juniBgpPeerGroupAddressFamilyPrefixTreeIn           DisplayString,
    juniBgpPeerGroupAddressFamilyPrefixTreeOut          DisplayString,
    juniBgpPeerGroupAddressFamilyFilterListIn           DisplayString,
    juniBgpPeerGroupAddressFamilyFilterListOut          DisplayString,
    juniBgpPeerGroupAddressFamilyFilterListWeight       DisplayString,
    juniBgpPeerGroupAddressFamilyFilterListWeightValue  Unsigned32,
    juniBgpPeerGroupAddressFamilyRouteMapIn             DisplayString,
    juniBgpPeerGroupAddressFamilyRouteMapOut            DisplayString,
    juniBgpPeerGroupAddressFamilyRouteReflectorClient   TruthValue,
    juniBgpPeerGroupAddressFamilyRouteLimitWarn         Unsigned32,
    juniBgpPeerGroupAddressFamilyRouteLimitReset        Unsigned32,
    juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly     TruthValue,
    juniBgpPeerGroupAddressFamilyRemovePrivateAs        TruthValue,
    juniBgpPeerGroupAddressFamilyUnsuppressMap          DisplayString,
    juniBgpPeerGroupAddressFamilyInboundSoftReconfig    TruthValue,
    juniBgpPeerGroupAddressFamilyResetConnectionType
        JuniBgpResetConnectionType,
    juniBgpPeerGroupAddressFamilyRowStatus              RowStatus,
    juniBgpPeerGroupAddressFamilyAsOverride             TruthValue,
    juniBgpPeerGroupAddressFamilyAllowAsIn              Integer32,
    juniBgpPeerGroupAddressFamilySendExtendedCommunity  TruthValue,
    juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend      TruthValue,
    juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend TruthValue,
    juniBgpPeerGroupAddressFamilyMaximumPrefixStrict        TruthValue,
    juniBgpPeerGroupAddressFamilyUnconfiguredAttributes     BITS,
    juniBgpPeerGroupAddressFamilySendLabel                  TruthValue,
    juniBgpPeerGroupAddressFamilyDefaultOriginateRouteMap   DisplayString }

juniBgpPeerGroupAddressFamilyVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        peer-group is configured.  The empty string indicates that this
        peer-group is not in a VRF."
    ::= { juniBgpPeerGroupAddressFamilyEntry 1 }

juniBgpPeerGroupAddressFamilyAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family in which this peer-group is configured."
    ::= { juniBgpPeerGroupAddressFamilyEntry 2 }

juniBgpPeerGroupAddressFamilySafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family in which this peer-group is configured."
    ::= { juniBgpPeerGroupAddressFamilyEntry 3 }

juniBgpPeerGroupGroupAddressFamilyGroupName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the Peer group this instance configures."
    ::= { juniBgpPeerGroupAddressFamilyEntry 4 }

juniBgpPeerGroupAddressFamilyDefaultOriginate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to advertise a default route to
        this peer, if present."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 5 }

juniBgpPeerGroupAddressFamilyNextHopSelf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to always advertise this router as
        the next hop."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 6 }

juniBgpPeerGroupAddressFamilySendCommunity OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send COMMUNITIES attributes in
        updates."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 7 }

juniBgpPeerGroupAddressFamilyDistributeListIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound distribute IP access list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 8 }

juniBgpPeerGroupAddressFamilyDistributeListOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound distribute IP access list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 9 }

juniBgpPeerGroupAddressFamilyPrefixListIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound IP prefix list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 10 }

juniBgpPeerGroupAddressFamilyPrefixListOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound IP prefix list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 11 }

juniBgpPeerGroupAddressFamilyPrefixTreeIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound IP prefix tree name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 12 }

juniBgpPeerGroupAddressFamilyPrefixTreeOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound IP prefix tree name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 13 }

juniBgpPeerGroupAddressFamilyFilterListIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 14 }

juniBgpPeerGroupAddressFamilyFilterListOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 15 }

juniBgpPeerGroupAddressFamilyFilterListWeight OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight filter AS path list name."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 16 }

juniBgpPeerGroupAddressFamilyFilterListWeightValue OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The weight to apply on weight filter list matches."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupAddressFamilyEntry 17 }

juniBgpPeerGroupAddressFamilyRouteMapIn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The inbound route-map name.
         An empty string means no inbound route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 18 }

juniBgpPeerGroupAddressFamilyRouteMapOut OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The outbound route-map name.
         An empty string means no outbound route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 19 }

juniBgpPeerGroupAddressFamilyRouteReflectorClient OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The peer is a route reflector client."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 20 }

juniBgpPeerGroupAddressFamilyRouteLimitWarn OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Log a warning if pathCount exceeds this value."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupAddressFamilyEntry 21 }

juniBgpPeerGroupAddressFamilyRouteLimitReset OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Reset connection is pathCount exceeds this value."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupAddressFamilyEntry 22 }

juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Warn, but do not reset, if the path limit is exceeded."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 23 }

juniBgpPeerGroupAddressFamilyRemovePrivateAs OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Remove private AS numbers from AS paths."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 24 }

juniBgpPeerGroupAddressFamilyUnsuppressMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unsuppress route-map name.  Routes which are sent to this peer and
        which match this route-map are not subject to suppression by
        summary-only aggregates.  An empty string means no unsuppress
        route-map."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 25 }

juniBgpPeerGroupAddressFamilyInboundSoftReconfig OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Inbound soft-reconfiguration is enabled.  If inbound
        soft-reconfiguration is enabled we keep a copy of each received route
        before inbound policy was applied."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 26 }

juniBgpPeerGroupAddressFamilyResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes one individual address family
        within the sessions to all peers in the peer-group to be cleared; the
        value determines what type of clear is executed (hard clear, soft clear
        in, soft clear out, etc.).  Note that hard clearing an address family
        within a session will bounce the session and thus also affect the other
        address families in the session.  Reading this object has no effect and
        always returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpPeerGroupAddressFamilyEntry 27 }

juniBgpPeerGroupAddressFamilyRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpPeerGroupAddressFamilyEntry 28 }

juniBgpPeerGroupAddressFamilyAsOverride OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Replace the AS number of the peer by our own AS number in the AS-path
        attribute in UPDATEs sent to this peer-group."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 29 }

juniBgpPeerGroupAddressFamilyAllowAsIn OBJECT-TYPE
    SYNTAX      Integer32 (0..10)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of times our own AS number is allowed to occur in a
        received AS-path before that received route is considered to be a loop
        and therefore rejected."
    DEFVAL    { 0 }
    ::= { juniBgpPeerGroupAddressFamilyEntry 30 }

juniBgpPeerGroupAddressFamilySendExtendedCommunity OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send EXTENDED-COMMUNITIES
        attributes in updates."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 31 }

juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST ORF SEND
        CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 32 }

juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to send PREFIX-LIST-CISCO ORF SEND
        CAPABILITY in Open Message."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 33 }

juniBgpPeerGroupAddressFamilyMaximumPrefixStrict OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If the juniBgpPeerGroupAddressFamilyInboundSoftReconfig object is set
        to true then routes which were rejected by inbound policy are stored in
        memory anyway.  The juniBgpPeerGroupAddressFamilyMaximumPrefixStrict
        object determines whether or not these routes count towards the maximum
        number of routes from the peer-group members (as configured in
        juniBgpPeerGroupAddressFamilyRouteLimitWarn and/or
        juniBgpPeerGroupAddressFamilyRouteLimitReset)."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 34 }

juniBgpPeerGroupAddressFamilyUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate(0),
        juniBgpPeerGroupAddressFamilyNextHopSelf(1),
        juniBgpPeerGroupAddressFamilySendCommunity(2),
        juniBgpPeerGroupAddressFamilyDistributeListIn(3),
        juniBgpPeerGroupAddressFamilyDistributeListOut(4),
        juniBgpPeerGroupAddressFamilyPrefixListIn(5),
        juniBgpPeerGroupAddressFamilyPrefixListOut(6),
        juniBgpPeerGroupAddressFamilyPrefixTreeIn(7),
        juniBgpPeerGroupAddressFamilyPrefixTreeOut(8),
        juniBgpPeerGroupAddressFamilyFilterListIn(9),
        juniBgpPeerGroupAddressFamilyFilterListOut(10),
        juniBgpPeerGroupAddressFamilyFilterListWeight(11),
        juniBgpPeerGroupAddressFamilyFilterListWeightValue(12),
        juniBgpPeerGroupAddressFamilyRouteMapIn(13),
        juniBgpPeerGroupAddressFamilyRouteMapOut(14),
        juniBgpPeerGroupAddressFamilyRouteReflectorClient(15),
        juniBgpPeerGroupAddressFamilyRouteLimitWarn(16),
        juniBgpPeerGroupAddressFamilyRouteLimitReset(17),
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly(18),
        juniBgpPeerGroupAddressFamilyRemovePrivateAs(19),
        juniBgpPeerGroupAddressFamilyUnsuppressMap(20),
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig(21),
        juniBgpPeerGroupAddressFamilyAsOverride(22),
        juniBgpPeerGroupAddressFamilyAllowAsIn(23),
        juniBgpPeerGroupAddressFamilySendExtendedCommunity(24),
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend(25),
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfCiscoSend(26),
        juniBgpPeerGroupAddressFamilyMaximumPrefixStrict(27),
        juniBgpPeerGroupAddressFamilySendLabel(28),
        juniBgpPeerGroupAddressFamilyDefaultOriginateRouteMap(29),
        juniBgpPeerGroupAddressFamilyNextHopUnchanged (30) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpPeerGroupAddressFamilyEntry 35 }

juniBgpPeerGroupAddressFamilySendLabel OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Send labeled prefixes to this peer for this address-family."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 36 }

juniBgpPeerGroupAddressFamilyDefaultOriginateRouteMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The route-map to apply to the default route which is advertised to this
        peer-group as a result of setting
        juniBgpPeerGroupAddressFamilyDefaultOriginate to true.  An empty string
        means that no route-map is applied."
    DEFVAL    { "" }
    ::= { juniBgpPeerGroupAddressFamilyEntry 37 }

juniBgpPeerGroupAddressFamilyNextHopUnchanged OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to always advertise the next-hop
        unchanged to this peer-group in this address-family."
    DEFVAL    { false }
    ::= { juniBgpPeerGroupAddressFamilyEntry 38 }

-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Peer Group Address Family Conditional Advertisement attributes
-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Peer Group Address Family Conditional Advertisement Table
--
juniBgpPeerGroupAddressFamilyConditionalAdvTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpPeerGroupAddressFamilyConditionalAdvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Group Address Family Conditional Advertisement 
        Table describes the BGP conditional advertisement attributes in an 
        Address Family for a peer-group."
    ::= { juniBgpObjects 31 }

juniBgpPeerGroupAddressFamilyConditionalAdvEntry OBJECT-TYPE
    SYNTAX      JuniBgpPeerGroupAddressFamilyConditionalAdvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP Peer Group Address Family Conditional Advertisement 
        Entry describes a specific conditional advertisement entry in an 
        Address Family for a peer-group."
    INDEX     { juniBgpPeerGroupAddressFamilyVrfName,
                juniBgpPeerGroupAddressFamilyAfi,
                juniBgpPeerGroupAddressFamilySafi,
                juniBgpPeerGroupGroupAddressFamilyGroupName,
                juniBgpPeerGroupAddressFamilyConditionalAdvAdvertiseMap }
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvTable 1 }

JuniBgpPeerGroupAddressFamilyConditionalAdvEntry::= SEQUENCE {
    juniBgpPeerGroupAddressFamilyConditionalAdvAdvertiseMap  JuniBgpAdvertiseMapName,
    juniBgpPeerGroupAddressFamilyConditionalAdvConditionMap  DisplayString,
    juniBgpPeerGroupAddressFamilyConditionalAdvIsExistMap    TruthValue,
    juniBgpPeerGroupAddressFamilyConditionalAdvSequenceNum   Integer32,
    juniBgpPeerGroupAddressFamilyConditionalAdvStatus  JuniBgpConditionalAdvStatus,
    juniBgpPeerGroupAddressFamilyConditionalAdvRowStatus     RowStatus }

juniBgpPeerGroupAddressFamilyConditionalAdvAdvertiseMap OBJECT-TYPE
    SYNTAX      JuniBgpAdvertiseMapName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The route-map for conditional advertisement."
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 1 }

juniBgpPeerGroupAddressFamilyConditionalAdvConditionMap OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The condition-map to apply for this conditional advertisement entry."
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 2 }

juniBgpPeerGroupAddressFamilyConditionalAdvIsExistMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this value to true(1) if the condition-map specified should be 
        applied as an exist-map. If the condition-map should be applied as a 
        non-exist-map, set it to false(2). "
    DEFVAL    { true }
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 3 }

juniBgpPeerGroupAddressFamilyConditionalAdvSequenceNum OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The sequence number of this conditional advertisement entry."
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 4 }

juniBgpPeerGroupAddressFamilyConditionalAdvStatus OBJECT-TYPE
    SYNTAX      JuniBgpConditionalAdvStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This value will be advertise(1) if the status of this advertise-map is evaluated 
         as advertise based on the condition-map. Otherwise it is withdraw(2)."
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 5 }

juniBgpPeerGroupAddressFamilyConditionalAdvRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the 
        RowStatus textual convention, constrained to support the following 
        values only:
            createAndGo,
            destroy "
    ::= { juniBgpPeerGroupAddressFamilyConditionalAdvEntry 6 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Network attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Network Table
--
juniBgpNetworkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Network Table controls what networks are originated by this BGP
        router."
    ::= { juniBgpObjects 16 }

juniBgpNetworkEntry OBJECT-TYPE
    SYNTAX      JuniBgpNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An instance in the list of networks originated by this BGP router."
    INDEX     { juniBgpNetworkVrfName,
                juniBgpNetworkAfi,
                juniBgpNetworkSafi,
                juniBgpNetworkIpAddrPrefix,
                juniBgpNetworkIpAddrPrefixLen }
    ::= { juniBgpNetworkTable 1 }

JuniBgpNetworkEntry ::= SEQUENCE {
    juniBgpNetworkVrfName                   JuniVrfName,
    juniBgpNetworkAfi                       JuniBgpAfi,
    juniBgpNetworkSafi                      JuniBgpSafi,
    juniBgpNetworkIpAddrPrefix              IpAddress,
    juniBgpNetworkIpAddrPrefixLen           Integer32,
    juniBgpNetworkBackdoor                  TruthValue,
    juniBgpNetworkRowStatus                 RowStatus,
    juniBgpNetworkWeightSpecified           TruthValue,
    juniBgpNetworkWeight                    Integer32,
    juniBgpNetworkRouteMap                  DisplayString,
    juniBgpNetworkUnconfiguredAttributes    BITS }

juniBgpNetworkVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        network is configured.  The empty string indicates that this network is
        not in a VRF."
    ::= { juniBgpNetworkEntry 1 }

juniBgpNetworkAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family in which this network is configured."
    ::= { juniBgpNetworkEntry 2 }

juniBgpNetworkSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family in which this network is configured."
    ::= { juniBgpNetworkEntry 3 }

juniBgpNetworkIpAddrPrefix OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An IP address prefix in the Network Layer Reachability Information
        field.  This object is an IP address containing the prefix with length
        specified by bgpRouteIpAddrPrefixLen.  Any bits beyond the length
        specified by bgpRouteIpAddrPrefixLen are zeroed."
    ::= { juniBgpNetworkEntry 4 }

juniBgpNetworkIpAddrPrefixLen OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Length in bits of the IP address prefix in the Network Layer
        Reachability Information field."
    ::= { juniBgpNetworkEntry 5 }

juniBgpNetworkBackdoor OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to enable BGP backdoor in this
        network.  This object can only be set at row creation."
    DEFVAL    { false }
    ::= { juniBgpNetworkEntry 6 }

juniBgpNetworkRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpNetworkEntry 7 }

juniBgpNetworkWeightSpecified OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If this object is set to true(1) then the BGP weight of this network is
        set to the value specified in juniBgpNetworkWeightValue.  This object
        can only be set at row creation."
    DEFVAL    { false }
    ::= { juniBgpNetworkEntry 8 }

juniBgpNetworkWeight OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If juniBgpNetworkWeight specified is set to true(1) then this object
        specifies the weight of the network.  Otherwise the weight of the
        network is determined by the IGP metric of the prefix.  This object can
        only be set at row creation."
    DEFVAL    { 32768 }
    ::= { juniBgpNetworkEntry 9 }

juniBgpNetworkRouteMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The name of the route-map used to set the path attributes of this
        network.  A zero length route-map name means don't use a route-map for
        this.  This object can only be set at row creation."
    DEFVAL    { "" }
    ::= { juniBgpNetworkEntry 10 }

juniBgpNetworkUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpNetworkBackdoor(0),
        juniBgpNetworkWeight(1),
        juniBgpNetworkRouteMap(2) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpNetworkEntry 11 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Aggregate attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP Aggregate Table
--
juniBgpAggregateTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpAggregateEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Aggregate Table controls the consolidation of route
        information."
    ::= { juniBgpObjects 17 }

juniBgpAggregateEntry OBJECT-TYPE
    SYNTAX      JuniBgpAggregateEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An instance in the list of route aggregation."
    INDEX     { juniBgpAggregateVrfName,
                juniBgpAggregateAfi,
                juniBgpAggregateSafi,
                juniBgpAggregateIpAddrPrefix,
                juniBgpAggregateIpAddrPrefixLen }
    ::= { juniBgpAggregateTable 1 }

JuniBgpAggregateEntry ::= SEQUENCE {
    juniBgpAggregateVrfName                 JuniVrfName,
    juniBgpAggregateAfi                     JuniBgpAfi,
    juniBgpAggregateSafi                    JuniBgpSafi,
    juniBgpAggregateIpAddrPrefix            IpAddress,
    juniBgpAggregateIpAddrPrefixLen         Integer32,
    juniBgpAggregateAsSet                   TruthValue,
    juniBgpAggregateSummaryOnly             TruthValue,
    juniBgpAggregateAttributeMap            DisplayString,
    juniBgpAggregateAdvertiseMap            DisplayString,
    juniBgpAggregateRowStatus               RowStatus,
    juniBgpAggregateSuppressMap             DisplayString,
    juniBgpAggregateUnconfiguredAttributes  BITS }

juniBgpAggregateVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        aggregate is configured.  The empty string indicates that this aggregate
        is not in a VRF."
    ::= { juniBgpAggregateEntry 1 }

juniBgpAggregateAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family in which this aggregate is configured."
    ::= { juniBgpAggregateEntry 2 }

juniBgpAggregateSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family in which this aggregate is configured."
    ::= { juniBgpAggregateEntry 3 }

juniBgpAggregateIpAddrPrefix OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An IP address prefix in the Network Layer Reachability Information
        field.  This object is an IP address containing the prefix with length
        specified by bgpRouteIpAddrPrefixLen.  Any bits beyond the length
        specified by bgpRouteIpAddrPrefixLen are zeroed."
    ::= { juniBgpAggregateEntry 4 }

juniBgpAggregateIpAddrPrefixLen OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Length in bits of the IP address prefix in the Network Layer
        Reachability Information field."
    ::= { juniBgpAggregateEntry 5 }

juniBgpAggregateAsSet OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to generate AS_set path information
        by creating an aggregate entry that consists of all elements contained
        in all paths that are being summarized.  This object can only be set at
        row creation."
    DEFVAL    { false }
    ::= { juniBgpAggregateEntry 6 }

juniBgpAggregateSummaryOnly OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to filter specific routes from
        updates.  By doing that, advertisements of more specific routes will be
        suppressed to all neighbors.  This object can only be set at row
        creation."
    DEFVAL    { false }
    ::= { juniBgpAggregateEntry 7 }

juniBgpAggregateAttributeMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The name of the route-map used to set the attributes of the aggregate
        An empty string means no route-map for this purpose."
    DEFVAL    { "" }
    ::= { juniBgpAggregateEntry 8 }

juniBgpAggregateAdvertiseMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The name of the route-map used to select routes covered by the
        aggregate (for as-set aggregates the path attributes of the aggregate
        route are created by summarizing the path attributes of the covered
        routes).  An empty string means no route-map for this purpose."
    DEFVAL    { "" }
    ::= { juniBgpAggregateEntry 9 }

juniBgpAggregateRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpAggregateEntry 10 }

juniBgpAggregateSuppressMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The suppress route-map name.  If a suppress route-map has been
        specified, all routes which are covered by the aggregate and which match
        the suppress route-map are suppressed.  An empty string means no inbound
        route-map."
    DEFVAL    { "" }
    ::= { juniBgpAggregateEntry 11 }

juniBgpAggregateUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpAggregateAsSet(0),
        juniBgpAggregateSummaryOnly(1),
        juniBgpAggregateAttributeMap(2),
        juniBgpAggregateAdvertiseMap(3),
        juniBgpAggregateSuppressMap(4) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpAggregateEntry 12 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP VRF attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP VRF Table
--
juniBgpVrfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Juniper BGP VRF Table describes the BGP-specific characteristics
        of a VPN routing forwarding instance."
    ::= { juniBgpObjects 18 }

juniBgpVrfEntry OBJECT-TYPE
    SYNTAX      JuniBgpVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP VRF Entry describes BGP-specific characteristics of one VRF."
    INDEX     { juniBgpVrfName }
    ::= { juniBgpVrfTable 1 }

JuniBgpVrfEntry ::= SEQUENCE {
    juniBgpVrfName                               JuniVrfName,
    juniBgpVrfSynchronization                    TruthValue,
    juniBgpVrfAutoSummary                        TruthValue,
    juniBgpVrfExternalDistance                   Integer32,
    juniBgpVrfInternalDistance                   Integer32,
    juniBgpVrfLocalDistance                      Integer32,
    juniBgpVrfResetConnectionType                JuniBgpResetConnectionType,
    juniBgpVrfRowStatus                          RowStatus,
    juniBgpVrfOperationalState                   INTEGER,
    juniBgpVrfAddUnicastRoutesToMulticastView    TruthValue,
    juniBgpVrfMaximumPathsEbgp                   Integer32,
    juniBgpVrfMaximumPathsIbgp                   Integer32,
    juniBgpVrfUnconfiguredAttributes             BITS,
    juniBgpVrfMaximumPathsEIbgp                  Integer32,
    juniBgpVrfCarriersCarrierModeEnabled         TruthValue }

juniBgpVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance."
    ::= { juniBgpVrfEntry 1 }

juniBgpVrfSynchronization OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to have IGP route synchronization
        enabled."
    DEFVAL    { false }
    ::= { juniBgpVrfEntry 2 }

juniBgpVrfAutoSummary OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to automatically summarize
        redistributed routes to their natural network mask."
    DEFVAL    { false }
    ::= { juniBgpVrfEntry 3 }

juniBgpVrfExternalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete administrative distance for external routes.  Replaced by
        juniBgpAddressFamilyExternalDistance."
    DEFVAL    { 20 }
    ::= { juniBgpVrfEntry 4 }

juniBgpVrfInternalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete administrative distance for internal routes.  Replaced by
        juniBgpAddressFamilyInternalDistance."
    DEFVAL    { 200 }
    ::= { juniBgpVrfEntry 5 }

juniBgpVrfLocalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete administrative distance for local routes.  Replaced by
        juniBgpAddressFamilyLocalDistance."
    DEFVAL    { 200 }
    ::= { juniBgpVrfEntry 6 }

juniBgpVrfResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes the sessions to all peers in this
        VRF to be cleared; the value determines what type of clear is executed
        (hard clear, soft clear in, soft clear out, etc.).  Reading this object
        has no effect and always returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpVrfEntry 7 }

juniBgpVrfRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpVrfEntry 8 }

juniBgpVrfOperationalState OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    up(1),
                    down(2),
                    overload(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BGP VRF operational state."
    ::= { juniBgpVrfEntry 9 }

juniBgpVrfAddUnicastRoutesToMulticastView OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to add routes learnt by IPv4
        Unicast BGP to the multicast route table."
    DEFVAL    { false }
    ::= { juniBgpVrfEntry 10 }

juniBgpVrfMaximumPathsEbgp OBJECT-TYPE
    SYNTAX      Integer32 (0..16)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of routes received from EBGP peers which BGP
        can select as equal cost.  Zero means that routes from EBGP peers
        cannot be selected as equal cost."
    DEFVAL    { 1 }
    ::= { juniBgpVrfEntry 11 }

juniBgpVrfMaximumPathsIbgp OBJECT-TYPE
    SYNTAX      Integer32 (0..16)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of routes received from IBGP peers which BGP
        can select as equal cost.  Zero means that routes from IBGP peers
        cannot be selected as equal cost."
    DEFVAL    { 1 }
    ::= { juniBgpVrfEntry 12 }

juniBgpVrfUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpVrfSynchronization(0),
        juniBgpVrfAutoSummary(1),
        juniBgpVrfExternalDistance(2),
        juniBgpVrfInternalDistance(3),
        juniBgpVrfLocalDistance(4),
        juniBgpVrfAddUnicastRoutesToMulticastView(5),
        juniBgpVrfMaximumPathsEbgp(6),
        juniBgpVrfMaximumPathsIbgp(7),
        juniBgpVrfMaximumPathsEIbgp(8) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpVrfEntry 13 }

juniBgpVrfMaximumPathsEIbgp OBJECT-TYPE
    SYNTAX      Integer32 (0..16)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of routes received from both EBGP and IBGP peers
        which BGP can select as equal cost.  Zero means that routes from both
        EBGP and IBGP peers cannot be selected as equal cost."
    DEFVAL    { 1 }
    ::= { juniBgpVrfEntry 14 }

juniBgpVrfCarriersCarrierModeEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Obsolete the BGP carrier's carrier flag since there is no need for it
        when in carrier's carrier mode."
    ::= { juniBgpVrfEntry 16 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Address Family attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniBgpAddressFamilyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP AddressFamily Entry describes BGP-specific characteristics of
        one AddressFamily."
    ::= { juniBgpObjects 19 }

juniBgpAddressFamilyEntry OBJECT-TYPE
    SYNTAX      JuniBgpAddressFamilyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP address family entry"
    INDEX     { juniBgpAddressFamilyVrfName,
                juniBgpAddressFamilyAfi,
                juniBgpAddressFamilySafi }
    ::= { juniBgpAddressFamilyTable 1 }

JuniBgpAddressFamilyEntry ::= SEQUENCE {
    juniBgpAddressFamilyVrfName                      JuniVrfName,
    juniBgpAddressFamilyAfi                          JuniBgpAfi,
    juniBgpAddressFamilySafi                         JuniBgpSafi,
    juniBgpAddressFamilyDefaultOriginate             TruthValue,
    juniBgpAddressFamilyRouteFlapDampening           TruthValue,
    juniBgpAddressFamilyDampeningSuppressThreshold   Unsigned32,
    juniBgpAddressFamilyDampeningReuseThreshold      Unsigned32,
    juniBgpAddressFamilyDampeningMaxHoldDownTime     Unsigned32,
    juniBgpAddressFamilyDampeningHalfLifeReachable   Unsigned32,
    juniBgpAddressFamilyDampeningHalfLifeUnreachable Unsigned32,
    juniBgpAddressFamilyDampeningRouteMapName        DisplayString,
    juniBgpAddressFamilyResetConnectionType          JuniBgpResetConnectionType,
    juniBgpAddressFamilyRowStatus                    RowStatus,
    juniBgpAddressFamilyOperationalState             INTEGER,
    juniBgpAddressFamilyUnconfiguredAttributes       BITS,
    juniBgpAddressFamilyExternalDistance             Integer32,
    juniBgpAddressFamilyInternalDistance             Integer32,
    juniBgpAddressFamilyLocalDistance                Integer32,
    juniBgpAddressFamilyDefaultOriginateRouteMap     DisplayString,
    juniBgpAddressFamilyIpIntfProfileNameForMplsHeads        DisplayString,
    juniBgpAddressFamilyIpIntfProfileNameForMplsTails        DisplayString,
    juniBgpAddressFamilyIpIntfServiceProfileNameForMplsHeads DisplayString,
    juniBgpAddressFamilyIpIntfServiceProfileNameForMplsTails DisplayString,
    juniBgpAddressFamilyCheckVpnNextHops                     TruthValue,
    juniBgpAddressFamilyPathSelectionIsDeferred              TruthValue,
    juniBgpAddressFamilyPreventBgpRoutesFromBeingPushedToLineCards  TruthValue,
    juniBgpAddressFamilyTimeUntilPathSelectionDeferTimerExpires     Integer32 }

juniBgpAddressFamilyVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        address-family is configured.  The empty string indicates that this
        address-family is not in a VRF."
    ::= { juniBgpAddressFamilyEntry 1 }

juniBgpAddressFamilyAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family."
    ::= { juniBgpAddressFamilyEntry 2 }

juniBgpAddressFamilySafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family."
    ::= { juniBgpAddressFamilyEntry 3 }

juniBgpAddressFamilyDefaultOriginate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to advertise a default route, if
        present."
    DEFVAL    { false }
    ::= { juniBgpAddressFamilyEntry 4 }

juniBgpAddressFamilyRouteFlapDampening OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Set this object to true(1) in order to enable route flap dampening."
    DEFVAL    { false }
    ::= { juniBgpAddressFamilyEntry 5 }

juniBgpAddressFamilyDampeningSuppressThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The dampening cutoff threshold (scaled)."
    DEFVAL    { 1000 }
    ::= { juniBgpAddressFamilyEntry 6 }

juniBgpAddressFamilyDampeningReuseThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The dampening reuse threshold (scaled)."
    DEFVAL    { 1000 }
    ::= { juniBgpAddressFamilyEntry 7 }

juniBgpAddressFamilyDampeningMaxHoldDownTime OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum route holddown time, in seconds."
    DEFVAL    { 20 }
    ::= { juniBgpAddressFamilyEntry 8 }

juniBgpAddressFamilyDampeningHalfLifeReachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Figure of merit half life for reachable routes, in seconds."
    DEFVAL    { 5 }
    ::= { juniBgpAddressFamilyEntry 9 }

juniBgpAddressFamilyDampeningHalfLifeUnreachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Figure of merit half life for unreachable routes, in seconds."
    DEFVAL    { 5 }
    ::= { juniBgpAddressFamilyEntry 10 }

juniBgpAddressFamilyDampeningRouteMapName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The name of the route-map used to determine which routes are dampened
        and which aren't and to set the dampening parameters for those routes
        which are dampenend.  A zero length route-map name means don't use a
        route-map for this."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 11 }

juniBgpAddressFamilyResetConnectionType OBJECT-TYPE
    SYNTAX      JuniBgpResetConnectionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Writing a value to this object causes one individual address family
        within the sessions to all peers in this VRF to be cleared; the value
        determines what type of clear is executed (hard clear, soft clear in,
        soft clear out, etc.).  Note that hard clearing an address family within
        a session will bounce the session and thus also affect the other address
        families in the session.  Reading this object has no effect and always
        returns resetTypeNoop."
    DEFVAL    { resetTypeNoop }
    ::= { juniBgpAddressFamilyEntry 12 }

juniBgpAddressFamilyRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Controls creation/deletion of entries in this table according to the
        RowStatus textual convention, constrained to support the following
        values only:
            createAndGo
            destroy "
    ::= { juniBgpAddressFamilyEntry 13 }

juniBgpAddressFamilyOperationalState OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    up(1),
                    down(2),
                    overload(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The BGP address family operational state."
    ::= { juniBgpAddressFamilyEntry 14 }

juniBgpAddressFamilyUnconfiguredAttributes OBJECT-TYPE
    SYNTAX      BITS {
        juniBgpAddressFamilyDefaultOriginate(0),
        juniBgpAddressFamilyRouteFlapDampening(1),
        juniBgpAddressFamilyDampeningSuppressThreshold(2),
        juniBgpAddressFamilyDampeningReuseThreshold(3),
        juniBgpAddressFamilyDampeningMaxHoldDownTime(4),
        juniBgpAddressFamilyDampeningHalfLifeReachable(5),
        juniBgpAddressFamilyDampeningHalfLifeUnreachable(6),
        juniBgpAddressFamilyDampeningRouteMapName(7),
        juniBgpAddressFamilyExternalDistance(8),
        juniBgpAddressFamilyInternalDistance(9),
        juniBgpAddressFamilyLocalDistance(10),
        juniBgpAddressFamilyDefaultOriginateRouteMap(11),
        juniBgpAddressFamilyCheckVpnNextHops(12) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "On get each bit indicates whether the corresponding attribute is
        configured or unconfigured:
           0 = The attribute is configured (i.e. the configured value is the
               operational value).
           1 = The attribute is unconfigured (i.e. the operational value is
               inherited from the a higher level which could be the default
               value).

        Getting the attribute value itself always returns the operational value
        of that attribute regardless of whether the attribute is configured or
        unconfigured.

        On write each bit indicates whether or not the corresponding attribute
        should be set to the unconfigured state:
           0 = No operation (i.e. do not change the state of the attribute).
           1 = Set the attribute to the unconfigured state.

        Setting an attribute to a value has the automatic side-effect of setting
        the bit with the same name to zero."
    DEFVAL    { { } }
    ::= { juniBgpAddressFamilyEntry 15 }

juniBgpAddressFamilyExternalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The administrative distance for external routes."
    DEFVAL    { 20 }
    ::= { juniBgpAddressFamilyEntry 16 }

juniBgpAddressFamilyInternalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The administrative distance for internal routes."
    DEFVAL    { 200 }
    ::= { juniBgpAddressFamilyEntry 17 }

juniBgpAddressFamilyLocalDistance OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The administrative distance for local routes."
    DEFVAL    { 200 }
    ::= { juniBgpAddressFamilyEntry 18 }

juniBgpAddressFamilyDefaultOriginateRouteMap OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The route-map to apply to the default route which is advertised as a
        result of setting juniBgpAddressFamilyDefaultOriginate to true.  An
        empty string means that no route-map is applied."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 19 }

juniBgpAddressFamilyIpIntfProfileNameForMplsHeads OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete the name of the IP interface profile used by BGP to create IP dynamic
        interfaces on top of MPLS stacked tunnel heads. When support for BGP-created 
        IP dynamic interfaces was removed, BGP profile names were obsoleted too."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 20 }

juniBgpAddressFamilyIpIntfProfileNameForMplsTails OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete the name of the IP interface profile used by BGP to create IP dynamic
        interfaces on top of MPLS stacked tunnel tails. When support for BGP-created 
        IP dynamic interfaces was removed, BGP profile names were obsoleted too."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 21 }

juniBgpAddressFamilyIpIntfServiceProfileNameForMplsHeads OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Obsolete the name of the IP interface service-profile used by BGP to create IP
        dynamic interfaces on top of MPLS stacked tunnel heads. When support for BGP-created 
        dynamic interfaces was removed, BGP profile names were obsoleted too."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 22 }

juniBgpAddressFamilyIpIntfServiceProfileNameForMplsTails OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..80))
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The name of the IP interface service-profile used by BGP to create IP
        dynamic interfaces on top of MPLS stacked tunnel tails. When support for BGP-created 
        dynamic interfaces was removed, BGP profile names were obsoleted too."
    DEFVAL    { "" }
    ::= { juniBgpAddressFamilyEntry 23 }

juniBgpAddressFamilyCheckVpnNextHops OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If this object is set to true, the reachability of the next-hops of
        routes in this address-family are checked.  If this object is set to
        false, the next-hops if routes in this address-families are always
        considered to be reachable.  This attribute only applies to address-
        families with SAFI 128 (labeled-vpn-unicast).  For all other address-
        families this attributes is meaningless and attempting to set the
        attribute will result in an error."
    DEFVAL    { false }
    ::= { juniBgpAddressFamilyEntry 24 }

juniBgpAddressFamilyPathSelectionIsDeferred OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is path selection for the address-family currently deferred?"
    ::= { juniBgpAddressFamilyEntry 25 }

juniBgpAddressFamilyPreventBgpRoutesFromBeingPushedToLineCards OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is BGP still in the process of placing fresh routes in the route
        table after a restart? During this period the line cards are forced
        to continue forwarding using the old stale routes."
    ::= { juniBgpAddressFamilyEntry 26 }

juniBgpAddressFamilyTimeUntilPathSelectionDeferTimerExpires OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of seconds until the path selection defer timer expires
        or zero if it is not running."
    ::= { juniBgpAddressFamilyEntry 27 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Storage attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
-- Objects set in the juniBgpStorageGroup will not take affect until reboot
--
-- The objects in this group define the initial and maximum size of various
-- heaps and pools used in BGP.
--
-- Each heap or pool is used to store a certain type of configured (e.g. peers)
-- or dynamic (e.g. routes) entries.  The initial size of each heap or pool
-- indicates how large the heap or pool is when BGP is initially created.  As
-- entries are allocated from heaps and pools and the pool eventually becomes
-- exhausted, the heaps and pools grow automatically up the to maximum size for
-- the heap or pool.  Once the heap or pool reaches it maximum size, BGP goes
-- into "overload state" and will stop accepting new configuration or dynamic
-- information for that heap or pool.
--
juniBgpStorageGroup                 OBJECT IDENTIFIER ::= { juniBgpObjects 20 }

juniBgpStorageInitialHeapSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the BGP heap in octets.  The BGP heap is used for a
        small number of variable sized data structures."
    DEFVAL    { 16384 }
    ::= { juniBgpStorageGroup 1 }

juniBgpStorageMaxHeapSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the BGP heap in octets."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 2 }

juniBgpStorageInitialVrfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the VRF pool in entries.  One entry is allocated
        from the VRF pool for each VRF used by BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 4 }

juniBgpStorageMaxVrfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the VRF pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 5 }

juniBgpStorageInitialAddressFamilyPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the address-family pool in entries.  One entry is
        allocated from the address-family pool for each address-family used by
        BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 6 }

juniBgpStorageMaxAddressFamilyPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the address-family pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 7 }

juniBgpStorageInitialPeerPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the peer pool in entries.  One entry is allocated
        from the peer pool for each peer configured in BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 8 }

juniBgpStorageMaxPeerPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the peer pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 9 }

juniBgpStorageInitialPeerAfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the peer-address-family pool in entries.  One entry
        is allocated from the peer-address-family pool for each address family
        activated for any peer in BGP (including the IPv4 unicast address
        families which are usually activated by default for each peer)."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 10 }

juniBgpStorageMaxPeerAfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the peer-address-family pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 11 }

juniBgpStorageInitialPeerGroupPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the peer-group pool in entries.  One entry is
        allocated from the peer-group pool for each peer-group configured in
        BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 12 }

juniBgpStorageMaxPeerGroupPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the peer-group pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 13 }

juniBgpStorageInitialPeerGroupAfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the peer-group-address-family pool in entries.  One
        entry is allocated from the peer-group-address-family pool for each
        address family activated for any peer-group in BGP (including the IPv4
        unicast address families which are usually activated by default for each
        peer-group)."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 14 }

juniBgpStorageMaxPeerGroupAfPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the peer-group-address-family pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 15 }

juniBgpStorageInitialNetworkPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the network pool in entries.  One entry is
        allocated from the network pool for each network configured in BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 16 }

juniBgpStorageMaxNetworkPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the network pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 17 }

juniBgpStorageInitialAggregatePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the aggregate pool in entries.  One entry is
        allocated from the aggregate pool for each aggregate configured in BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 18 }

juniBgpStorageMaxAggregatePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the aggregate pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 19 }

juniBgpStorageInitialDestinationPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the destination pool in entries.  One entry is
        allocated from the destination pool for each unique prefix in the BGP
        RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 20 }

juniBgpStorageMaxDestinationPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the destination pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 21 }

juniBgpStorageInitialRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the route pool in entries.  One entry is allocated
        from the route pool for each received non-VPN route in the BGP RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 22 }

juniBgpStorageMaxRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 23 }

juniBgpStorageInitialAttributesPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the attributes pool in entries.  One entry is
        allocated from the attributes pool for each unique combination of path
        attributes of routes in the BGP RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 24 }

juniBgpStorageMaxAttributesPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the attributes pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 25 }

juniBgpStorageInitialRouteFlapHistoryPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the route-flap-history pool in entries.  One entry
        is allocated from the route-flap-history pool for each route in the BGP
        RIB which has route-flap dampening history associated with it."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 26 }

juniBgpStorageMaxRouteFlapHistoryPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the route-flap-history pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 27 }

juniBgpStorageInitialNetworkRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the network-route pool in entries.  One entry is
        allocated from the network-route pool for each network route in the BGP
        RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 28 }

juniBgpStorageMaxNetworkRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the network-route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 29 }

juniBgpStorageInitialRedistributedRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the redistributed-route pool in entries.  One entry
        is allocated from the redistributed-route pool for each redistributed
        route in the BGP RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 30 }

juniBgpStorageMaxRedistributedRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the redistributed-route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 31 }

juniBgpStorageInitialAggregateRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the aggregate-route pool in entries.  One entry is
        allocated from the aggregate-route pool for each aggregate route in the
        BGP RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 32 }

juniBgpStorageMaxAggregateRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the aggregate-route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 33 }

juniBgpStorageInitialAutoSummaryRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the auto-summary-route pool in entries.  One entry
        is allocated from the auto-summary-route pool for each auto-summary
        route in the BGP RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 34 }

juniBgpStorageMaxAutoSummaryRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the auto-summary-route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 35 }

juniBgpStorageInitialHistoryRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete object is obsolete.  Setting it has not effect and reading this
        object returns an undefined value."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 36 }

juniBgpStorageMaxHistoryRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete object is obsolete.  Setting it has not effect and reading this
        object returns an undefined value."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 37 }

juniBgpStorageInitialSendQueueEntryPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the send-queue-entry pool in entries.  One entry is
        allocated from the send-queue-entry pool for update messages which is
        queued for transmission to a peer."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 38 }

juniBgpStorageMaxSendQueueEntryPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the send-queue-entry pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 39 }

juniBgpStorageInitialVpnRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the vpn-route pool in entries.  One entry is
        allocated from the vpn-route pool for each received VPN route in the BGP
        RIB."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 40 }

juniBgpStorageMaxVpnRoutePoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the vpn-route pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 41 }

juniBgpStorageInitialRouteTargetPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete initial size of the route-target pool in entries.  One entry is
        allocated from the route-target pool for each route-target imported in
        any VRF used by BGP."
    DEFVAL    { 1 }
    ::= { juniBgpStorageGroup 42 }

juniBgpStorageMaxRouteTargetPoolSize OBJECT-TYPE
    SYNTAX      JuniBgpStorageInteger
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Obsolete maximum size of the route-target pool in entries."
    DEFVAL    { ********* }
    ::= { juniBgpStorageGroup 43 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP New Route attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  BGP New Route Attribute Table
--
juniBgpNewRouteTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNewRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP-4 Received Path Attribute Table contains additional information
        about paths to destination networks received from all BGP4 peers."
    ::= { juniBgpObjects 23 }

juniBgpNewRouteEntry OBJECT-TYPE
    SYNTAX      JuniBgpNewRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Additional information about a path to a network."
    INDEX     { juniBgpNewRouteVrfName,
                juniBgpNewRouteAfi,
                juniBgpNewRouteSafi,
                juniBgpNewRouteIpAddrPrefix,
                juniBgpNewRouteIpAddrPrefixLen,
                juniBgpNewRouteDistinguisher,
                juniBgpNewRoutePeer,
                juniBgpNewRouteRouteType,
                juniBgpNewRouteOriginalRd }
    ::= { juniBgpNewRouteTable 1 }

JuniBgpNewRouteEntry ::= SEQUENCE {
    juniBgpNewRouteVrfName                       JuniVrfName,
    juniBgpNewRouteAfi                           JuniBgpAfi,
    juniBgpNewRouteSafi                          JuniBgpSafi,
    juniBgpNewRouteIpAddrPrefix                  IpAddress,
    juniBgpNewRouteIpAddrPrefixLen               Integer32,
    juniBgpNewRouteDistinguisher                 OCTET STRING,
    juniBgpNewRoutePeer                          IpAddress,
    juniBgpNewRouteRouteType                     INTEGER,
    juniBgpNewRouteOriginalRd                    OCTET STRING,
    juniBgpNewRouteOriginatorId                  IpAddress,
    juniBgpNewRouteAtomicAggregatePresent        TruthValue,
    juniBgpNewRouteMedPresent                    TruthValue,
    juniBgpNewRouteLocalPrefPresent              TruthValue,
    juniBgpNewRouteAggregatorPresent             TruthValue,
    juniBgpNewRouteCommunitiesPresent            TruthValue,
    juniBgpNewRouteOriginatorIdPresent           TruthValue,
    juniBgpNewRouteClusterListPresent            TruthValue,
    juniBgpNewRouteWeight                        Unsigned32,
    juniBgpNewRouteOrigin                        INTEGER,
    juniBgpNewRouteASPathSegment                 OCTET STRING,
    juniBgpNewRouteNextHop                       IpAddress,
    juniBgpNewRouteMultiExitDisc                 Unsigned32,
    juniBgpNewRouteLocalPref                     Unsigned32,
    juniBgpNewRouteAtomicAggregate               INTEGER,
    juniBgpNewRouteAggregatorAS                  Integer32,
    juniBgpNewRouteAggregatorAddress             IpAddress,
    juniBgpNewRouteBestInIpRouteTable            TruthValue,
    juniBgpNewRouteUnknown                       OCTET STRING,
    juniBgpNewRouteExtendedCommunitiesPresent    TruthValue,
    juniBgpNewRouteValid                         TruthValue,
    juniBgpNewRouteSuppressedBy                  INTEGER,
    juniBgpNewRouteNextHopReachable              TruthValue,
    juniBgpNewRouteSynchronizedWithIgp           TruthValue,
    juniBgpNewRoutePlaceInIpRouteTable           TruthValue,
    juniBgpNewRouteAdvertiseToExternalPeers      TruthValue,
    juniBgpNewRouteAdvertiseToInternalPeers      TruthValue,
    juniBgpNewRouteMplsLabel                     Unsigned32,
    juniBgpNewRouteNextHopMetric                 Unsigned32,
    juniBgpNewRouteMplsInLabel                   Unsigned32,
    juniBgpNewRouteMplsOutLabel                  Unsigned32,
    juniBgpNewRouteLeaked                        TruthValue,
    juniBgpNewRouteStale                         TruthValue }

juniBgpNewRouteVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        route is present.  The empty string indicates that this route is not in
        a VRF."
    ::= { juniBgpNewRouteEntry 1 }

juniBgpNewRouteAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The AFI of the address-family in which this route is present."
    ::= { juniBgpNewRouteEntry 2 }

juniBgpNewRouteSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The SAFI of the address-family in which this route is present."
    ::= { juniBgpNewRouteEntry 3 }

juniBgpNewRouteIpAddrPrefix OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address prefix of the route.  This object is an IP address
        containing the prefix with length specified by
        juniBgpNewRouteIpAddrPrefixLen.  Any bits beyond the length specified by
        juniBgpNewRouteIpAddrPrefixLen are zeroed."
    ::= { juniBgpNewRouteEntry 4 }

juniBgpNewRouteIpAddrPrefixLen OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Length in bits of the IP address prefix of the route."
    ::= { juniBgpNewRouteEntry 5 }

juniBgpNewRouteDistinguisher OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Route Distinguisher (RD) of the route.  This object has an
        undefined value for non-VPN routes."
    ::= { juniBgpNewRouteEntry 6 }

juniBgpNewRoutePeer OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address of the peer from which the route was received.  This
        object has value 0.0.0.0 is the route was a locally originated route."
    ::= { juniBgpNewRouteEntry 7 }

juniBgpNewRouteRouteType OBJECT-TYPE
    SYNTAX      INTEGER {
                    routeTypeReceived(1),
                    routeTypeNetwork(2),
                    routeTypeRedistributed(3),
                    routeTypeAggregate(4),
                    routeTypeAutoSummary(5) }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The type of the route."
    ::= { juniBgpNewRouteEntry 8 }

juniBgpNewRouteOriginalRd OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The original Route Distinguisher (RD) of a VPN route.  For VPN routes
        which are originated in a VRF or which are received from a CE the
        original RD is equal to the RD of the VRF.  For VPN routes which are
        received from another PE and imported into a VRF, the original RD is the
        RD which was received from the other PE (the RD of the route itself is
        changed to the RD for the VRF when the route is imported into the VRF).
        For non-VPN routes the value of this object is undefined."
    ::= { juniBgpNewRouteEntry 9 }

juniBgpNewRouteOriginatorId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Originator-ID path attribute of the route if present.  The
        juniBgpNewRouteOriginatorIdPresent object indicates whether the
        Originator-ID path attribute is present."
    ::= { juniBgpNewRouteEntry 10 }

juniBgpNewRouteAtomicAggregatePresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Atomic-Aggregate path attribute is present."
    ::= { juniBgpNewRouteEntry 11 }

juniBgpNewRouteMedPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Multi-Exit-Discriminator path attribute is present."
    ::= { juniBgpNewRouteEntry 12 }

juniBgpNewRouteLocalPrefPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Local-Pref path attribute is present."
    ::= { juniBgpNewRouteEntry 13 }

juniBgpNewRouteAggregatorPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Aggregator path attribute is present."
    ::= { juniBgpNewRouteEntry 14 }

juniBgpNewRouteCommunitiesPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Communities path attribute is present."
    ::= { juniBgpNewRouteEntry 15 }

juniBgpNewRouteOriginatorIdPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Originator-ID path attribute is present."
    ::= { juniBgpNewRouteEntry 16 }

juniBgpNewRouteClusterListPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Cluster-List path attribute is present."
    ::= { juniBgpNewRouteEntry 17 }

juniBgpNewRouteWeight OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The weight of the route."
    ::= { juniBgpNewRouteEntry 18 }

juniBgpNewRouteOrigin OBJECT-TYPE
    SYNTAX      INTEGER {
                    igp(1),
                    egp(2),
                    incomplete(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the Origin path attribute."
    ::= { juniBgpNewRouteEntry 19 }

juniBgpNewRouteASPathSegment OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(2..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the AS-path path attribute (as encoded in the received
        update message)."
    ::= { juniBgpNewRouteEntry 20 }

juniBgpNewRouteNextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the Next-Hop path attribute."
    ::= { juniBgpNewRouteEntry 21 }

juniBgpNewRouteMultiExitDisc OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the Multi-Exit-Discriminator path attribute if present.
        The juniBgpNewRouteMedPresent object indicates whether the
        Multi-Exit-Discriminator path attribute is present."
    ::= { juniBgpNewRouteEntry 22 }

juniBgpNewRouteLocalPref OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the Local-Pref path attribute if present.  The
        juniBgpNewRouteLocalPrefPresent object indicates whether the Local-Pref
        path attribute is present."
    ::= { juniBgpNewRouteEntry 23 }

juniBgpNewRouteAtomicAggregate OBJECT-TYPE
    SYNTAX      INTEGER {
                    lessSpecificRouteNotSelected(1),
                    lessSpecificRouteSelected(2) }
    MAX-ACCESS read-only
    STATUS      current
    DESCRIPTION
        "Whether or not the local system has selected a less specific route
        without selecting a more specific route."
    ::= { juniBgpNewRouteEntry 24 }

juniBgpNewRouteAggregatorAS OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the AS field in the Aggregator path attribute if present.
        The juniBgpNewRouteAggregatorPresent object indicates whether the
        Aggregator path attribute is present."
    ::= { juniBgpNewRouteEntry 25 }

juniBgpNewRouteAggregatorAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the address field in the Aggregator path attribute if
        present.  The juniBgpNewRouteAggregatorPresent object indicates whether
        the Aggregator path attribute is present."
    ::= { juniBgpNewRouteEntry 26 }

juniBgpNewRouteBestInIpRouteTable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When BGP places a routes in the IP route table, that route may not be
        used for forwarding traffic if there are other routes (e.g. IGP routes
        or static routes) to that same prefix with a better administrative
        distance in the IP route table.  This object indicates whether or not
        the BGP route in the IP route table is the best route (and hence is used
        for forwarding traffic)."
    ::= { juniBgpNewRouteEntry 27 }

juniBgpNewRouteUnknown OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "One or more path attributes not understood by this BGP4 speaker.  Size
        zero (0) indicates the absence of such attribute(s).  Octets beyond the
        maximum size, if any, are not recorded by this object."
    ::= { juniBgpNewRouteEntry 28 }

juniBgpNewRouteExtendedCommunitiesPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the Extended-Communities path attribute present?"
    ::= { juniBgpNewRouteEntry 29 }

juniBgpNewRouteValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the route valid?  The route is declared invalid if there is
        something wrong with it which is not serious enough for sending a
        notification, but it serious enough for not placing it in the IP route
        table or propagating it to other peers (for example the next-hop of the
        route is a local address)."
    ::= { juniBgpNewRouteEntry 30 }

juniBgpNewRouteSuppressedBy OBJECT-TYPE
    SYNTAX      INTEGER {
                    suppressedByNothing(1),
                    suppressedByAggregate(2),
                    suppressedByAutoSummary(3),
                    suppressedByDampening(4) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether or not the route is suppressed, and if so why:
            suppressedByNothing       - Route is not suppressed.
            suppressedByAggregate     - Route is suppressed by a summary-only
                                        aggregate.
            suppressedByAutoSummary   - Route is suppressed by an auto-summary.
            suppressedByDampening     - Route is suppressed as a result of
                                        route-flap dampening."
    ::= { juniBgpNewRouteEntry 31 }

juniBgpNewRouteNextHopReachable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the indirect next-hop of the route reachable (i.e. can the indirect
        next-hop of the route be resolved to at least one direct next-hop)?"
    ::= { juniBgpNewRouteEntry 32 }

juniBgpNewRouteSynchronizedWithIgp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the route is synchronized (i.e. is there is an IGP route to the same
        prefix in the IP route table)?"
    ::= { juniBgpNewRouteEntry 33 }

juniBgpNewRoutePlaceInIpRouteTable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Has a BGP route for this prefix been placed in the IP route table?"
    ::= { juniBgpNewRouteEntry 34 }

juniBgpNewRouteAdvertiseToExternalPeers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Should this route be advertised to external peers?"
    ::= { juniBgpNewRouteEntry 35 }

juniBgpNewRouteAdvertiseToInternalPeers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Should this route be advertised to internal peers?"
    ::= { juniBgpNewRouteEntry 36 }

juniBgpNewRouteMplsLabel OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The obsolete MPLS label for the route.  This object was obsoleted when
        in-label and out-label were introduced."
    ::= { juniBgpNewRouteEntry 37 }

juniBgpNewRouteNextHopMetric OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The metric of direct next-hop of this BGP route."
    ::= { juniBgpNewRouteEntry 38 }

juniBgpNewRouteMplsInLabel OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MPLS in-label for the route.  This is the label that MPLS frames
        are going to be received with."
    ::= { juniBgpNewRouteEntry 39 }

juniBgpNewRouteMplsOutLabel OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MPLS out-label for the route.  This is the label that MPLS frames
        are going to be sent with."
    ::= { juniBgpNewRouteEntry 40 }

juniBgpNewRouteLeaked OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the route leaked into this address-family from another address-
        family?"
    ::= { juniBgpNewRouteEntry 41 }

juniBgpNewRouteStale OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Is the route stale?"
    ::= { juniBgpNewRouteEntry 42 }

--
--  BGP New Route Flap History Table
--
juniBgpNewRouteFlapHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNewRouteFlapHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP Route Flap History Table contains information about recorded
        route flap events."
    ::= { juniBgpObjects 24 }

juniBgpNewRouteFlapHistoryEntry OBJECT-TYPE
    SYNTAX      JuniBgpNewRouteFlapHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An instance in the list of route flap events."
    INDEX     { juniBgpNewRouteVrfName,
                juniBgpNewRouteAfi,
                juniBgpNewRouteSafi,
                juniBgpNewRouteIpAddrPrefix,
                juniBgpNewRouteIpAddrPrefixLen,
                juniBgpNewRouteDistinguisher,
                juniBgpNewRoutePeer,
                juniBgpNewRouteRouteType,
                juniBgpNewRouteOriginalRd }
    ::= { juniBgpNewRouteFlapHistoryTable 1 }

JuniBgpNewRouteFlapHistoryEntry ::= SEQUENCE {
    juniBgpNewRouteFlapState                 INTEGER,
    juniBgpNewRouteFlapFigureOfMerit         Unsigned32,
    juniBgpNewRouteFlapRemainingTime         Unsigned32,
    juniBgpNewRouteFlapSuppressThreshold     Unsigned32,
    juniBgpNewRouteFlapReuseThreshold        Unsigned32,
    juniBgpNewRouteFlapMaxHoldDownTime       Unsigned32,
    juniBgpNewRouteFlapHalfLifeReachable     Unsigned32,
    juniBgpNewRouteFlapHalfLifeUnreachable   Unsigned32 }

juniBgpNewRouteFlapState OBJECT-TYPE
    SYNTAX      INTEGER {
                    stateAvailable(1),
                    stateSuppressedReachable(2),
                    stateSuppressedUnreachable(3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state:
            stateAvailable(1)             - indicates path is available
            stateSuppressedReachable(2)   - indicates path is suppressed,
                                            destination is reachable
            stateSuppressedUnreachable(3) - indicates path in suppressed,
                                            destination is not reachable "
    ::= { juniBgpNewRouteFlapHistoryEntry 1 }

juniBgpNewRouteFlapFigureOfMerit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Calculated figure-of-merit."
    ::= { juniBgpNewRouteFlapHistoryEntry 2 }

juniBgpNewRouteFlapRemainingTime OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of seconds until reuse or removal."
    ::= { juniBgpNewRouteFlapHistoryEntry 3 }

juniBgpNewRouteFlapSuppressThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Damping cutoff threshold (scaled)."
    ::= { juniBgpNewRouteFlapHistoryEntry 4 }

juniBgpNewRouteFlapReuseThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Damping reuse threshold (scaled)."
    ::= { juniBgpNewRouteFlapHistoryEntry 5 }

juniBgpNewRouteFlapMaxHoldDownTime OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum route holddown time (seconds)."
    ::= { juniBgpNewRouteFlapHistoryEntry 6 }

juniBgpNewRouteFlapHalfLifeReachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Figure of merit half life for reachable routes (seconds)."
    ::= { juniBgpNewRouteFlapHistoryEntry 7 }

juniBgpNewRouteFlapHalfLifeUnreachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Figure of merit half life for unreachable routes (seconds)."
    ::= { juniBgpNewRouteFlapHistoryEntry 8 }


--
--  BGP 4 New Route Community Table
--
juniBgpNewRouteCommunityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNewRouteCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP-4 Path Community Table contains information about communities
        within destination networks received from all BGP4 peers."
    ::= { juniBgpObjects 25 }

juniBgpNewRouteCommunityEntry OBJECT-TYPE
    SYNTAX      JuniBgpNewRouteCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Community attribute information within a path to a network."
    INDEX     { juniBgpNewRouteVrfName,
                juniBgpNewRouteAfi,
                juniBgpNewRouteSafi,
                juniBgpNewRouteIpAddrPrefix,
                juniBgpNewRouteIpAddrPrefixLen,
                juniBgpNewRouteDistinguisher,
                juniBgpNewRoutePeer,
                juniBgpNewRouteRouteType,
                juniBgpNewRouteOriginalRd,
                juniBgpNewRouteCommunityNumber }
    ::= { juniBgpNewRouteCommunityTable 1 }

JuniBgpNewRouteCommunityEntry ::= SEQUENCE {
    juniBgpNewRouteCommunityNumber   Unsigned32 }

juniBgpNewRouteCommunityNumber OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The community number."
    ::= { juniBgpNewRouteCommunityEntry 1 }


--
--  BGP 4 New Route Extended Community Table
--
juniBgpNewRouteExtendedCommunityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNewRouteExtendedCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP-4 Path Extended Community Table contains information about
        communities within destination networks received from all BGP4 peers."
    ::= { juniBgpObjects 26 }

juniBgpNewRouteExtendedCommunityEntry OBJECT-TYPE
    SYNTAX      JuniBgpNewRouteExtendedCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Extended Community attribute information within a path to a network."
    INDEX     { juniBgpNewRouteVrfName,
                juniBgpNewRouteAfi,
                juniBgpNewRouteSafi,
                juniBgpNewRouteIpAddrPrefix,
                juniBgpNewRouteIpAddrPrefixLen,
                juniBgpNewRouteDistinguisher,
                juniBgpNewRoutePeer,
                juniBgpNewRouteRouteType,
                juniBgpNewRouteOriginalRd,
                juniBgpNewRouteExtendedCommunityNumber }
    ::= { juniBgpNewRouteExtendedCommunityTable 1 }

JuniBgpNewRouteExtendedCommunityEntry ::= SEQUENCE {
    juniBgpNewRouteExtendedCommunityNumber   OCTET STRING }

juniBgpNewRouteExtendedCommunityNumber OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The extended community number."
    ::= { juniBgpNewRouteExtendedCommunityEntry 1 }


--
--  BGP 4 New Route ClusterId Table
--
juniBgpNewRouteClusterIdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpNewRouteClusterIdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The BGP-4 Path ClusterId Table contains information about router
        reflector cluster IDs."
    ::= { juniBgpObjects 27 }

juniBgpNewRouteClusterIdEntry OBJECT-TYPE
    SYNTAX      JuniBgpNewRouteClusterIdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "ClusterId attribute information within a path to a network."
    INDEX     { juniBgpNewRouteVrfName,
                juniBgpNewRouteAfi,
                juniBgpNewRouteSafi,
                juniBgpNewRouteIpAddrPrefix,
                juniBgpNewRouteIpAddrPrefixLen,
                juniBgpNewRouteDistinguisher,
                juniBgpNewRoutePeer,
                juniBgpNewRouteRouteType,
                juniBgpNewRouteOriginalRd,
                juniBgpNewRouteClusterId }
    ::= { juniBgpNewRouteClusterIdTable 1 }

JuniBgpNewRouteClusterIdEntry ::= SEQUENCE {
    juniBgpNewRouteClusterId Unsigned32 }

juniBgpNewRouteClusterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cluster ID."
    ::= { juniBgpNewRouteClusterIdEntry 1 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Obsolete tables
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- The following tables have been obsoleted by their new versions above.

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- BGP Route attributes
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
--
--  Obsolete BGP Route Attribute Table
--
juniBgpRouteTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The obsolete BGP-4 Received Path Attribute Table contained additional
        information about paths to destination networks received from all BGP4
        peers.  This table has been replaced by the juniBgpNewRouteTable."
    ::= { juniBgpObjects 13 }

juniBgpRouteEntry OBJECT-TYPE
    SYNTAX      JuniBgpRouteEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "Additional information about a path to a network."
    INDEX     { juniBgpRouteVrfName,
                juniBgpRouteAfi,
                juniBgpRouteSafi,
                juniBgpRouteIpAddrPrefix,
                juniBgpRouteIpAddrPrefixLen,
                juniBgpRouteDistinguisher,
                juniBgpRoutePeer,
                juniBgpRouteRouteType }
    ::= { juniBgpRouteTable 1 }

JuniBgpRouteEntry ::= SEQUENCE {
    juniBgpRouteOriginatorId                 IpAddress,
    juniBgpRouteAtomicAggregatePresent       TruthValue,
    juniBgpRouteMedPresent                   TruthValue,
    juniBgpRouteLocalPrefPresent             TruthValue,
    juniBgpRouteAggregatorPresent            TruthValue,
    juniBgpRouteCommunitiesPresent           TruthValue,
    juniBgpRouteOriginatorIdPresent          TruthValue,
    juniBgpRouteClusterListPresent           TruthValue,
    juniBgpRouteWeight                       Unsigned32,
    juniBgpRouteVrfName                      JuniVrfName,
    juniBgpRouteAfi                          JuniBgpAfi,
    juniBgpRouteSafi                         JuniBgpSafi,
    juniBgpRoutePeer                         IpAddress,
    juniBgpRouteIpAddrPrefixLen              Integer32,
    juniBgpRouteIpAddrPrefix                 IpAddress,
    juniBgpRouteRouteType                    INTEGER,
    juniBgpRouteOrigin                       INTEGER,
    juniBgpRouteASPathSegment                OCTET STRING,
    juniBgpRouteNextHop                      IpAddress,
    juniBgpRouteMultiExitDisc                Unsigned32,
    juniBgpRouteLocalPref                    Unsigned32,
    juniBgpRouteAtomicAggregate              INTEGER,
    juniBgpRouteAggregatorAS                 Integer32,
    juniBgpRouteAggregatorAddress            IpAddress,
    juniBgpRouteBestInIpRouteTable           TruthValue,
    juniBgpRouteUnknown                      OCTET STRING,
    juniBgpRouteExtendedCommunitiesPresent   TruthValue,
    juniBgpRouteValid                        TruthValue,
    juniBgpRouteSuppressedBy                 INTEGER,
    juniBgpRouteNextHopReachable             TruthValue,
    juniBgpRouteSynchronizedWithIgp          TruthValue,
    juniBgpRoutePlaceInIpRouteTable          TruthValue,
    juniBgpRouteAdvertiseToExternalPeers     TruthValue,
    juniBgpRouteAdvertiseToInternalPeers     TruthValue,
    juniBgpRouteDistinguisher                OCTET STRING,
    juniBgpRouteMplsLabel                    Unsigned32,
    juniBgpRouteNextHopMetric                Unsigned32 }

juniBgpRouteOriginatorId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Originator-ID path attribute of the route if present.  The
        juniBgpRouteOriginatorIdPresent object indicates whether the
        Originator-ID path attribute is present."
    ::= { juniBgpRouteEntry 1 }

juniBgpRouteAtomicAggregatePresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Atomic-Aggregate path attribute is present."
    ::= { juniBgpRouteEntry 2 }

juniBgpRouteMedPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Multi-Exit-Discriminator path attribute is present."
    ::= { juniBgpRouteEntry 3 }

juniBgpRouteLocalPrefPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Local-Pref path attribute is present."
    ::= { juniBgpRouteEntry 4 }

juniBgpRouteAggregatorPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Aggregator path attribute is present."
    ::= { juniBgpRouteEntry 5 }

juniBgpRouteCommunitiesPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Communities path attribute is present."
    ::= { juniBgpRouteEntry 6 }

juniBgpRouteOriginatorIdPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Originator-ID path attribute is present."
    ::= { juniBgpRouteEntry 7 }

juniBgpRouteClusterListPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Cluster-List path attribute is present."
    ::= { juniBgpRouteEntry 8 }

juniBgpRouteWeight OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The weight of the route."
    ::= { juniBgpRouteEntry 9 }

juniBgpRouteVrfName OBJECT-TYPE
    SYNTAX      JuniVrfName
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The name of the VPN Routing Forwarding (VRF) instance in which this
        route is present.  The empty string indicates that this route is not in
        a VRF."
    ::= { juniBgpRouteEntry 10 }

juniBgpRouteAfi OBJECT-TYPE
    SYNTAX      JuniBgpAfi
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The AFI of the address-family in which this route is present."
    ::= { juniBgpRouteEntry 11 }

juniBgpRouteSafi OBJECT-TYPE
    SYNTAX      JuniBgpSafi
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The SAFI of the address-family in which this route is present."
    ::= { juniBgpRouteEntry 12 }

juniBgpRoutePeer OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The IP address of the peer from which the route was received.  This
        object has value 0.0.0.0 is the route was a locally originated route."
    ::= { juniBgpRouteEntry 13 }

juniBgpRouteIpAddrPrefixLen OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "Length in bits of the IP address prefix of the route."
    ::= { juniBgpRouteEntry 14 }

juniBgpRouteIpAddrPrefix OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The IP address prefix of the route.  This object is an IP address
        containing the prefix with length specified by
        juniBgpNewRouteIpAddrPrefixLen.  Any bits beyond the length specified by
        juniBgpNewRouteIpAddrPrefixLen are zeroed."
    ::= { juniBgpRouteEntry 15 }

juniBgpRouteRouteType OBJECT-TYPE
    SYNTAX      INTEGER {
                    routeTypeReceived(1),
                    routeTypeNetwork(2),
                    routeTypeRedistributed(3),
                    routeTypeAggregate(4),
                    routeTypeAutoSummary(5) }
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The type of the route."
    ::= { juniBgpRouteEntry 16 }

juniBgpRouteOrigin OBJECT-TYPE
    SYNTAX      INTEGER {
                    igp(1),
                    egp(2),
                    incomplete(3) }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the Origin path attribute."
    ::= { juniBgpRouteEntry 17 }

juniBgpRouteASPathSegment OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(2..255))
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the AS-path path attribute (as encoded in the received
        update message)."
    ::= { juniBgpRouteEntry 18 }

juniBgpRouteNextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the Next-Hop path attribute."
    ::= { juniBgpRouteEntry 19 }

juniBgpRouteMultiExitDisc OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the Multi-Exit-Discriminator path attribute if present.
        The juniBgpRouteMedPresent object indicates whether the
        Multi-Exit-Discriminator path attribute is present."
    ::= { juniBgpRouteEntry 20 }

juniBgpRouteLocalPref OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the Local-Pref path attribute if present.  The
        juniBgpRouteLocalPrefPresent object indicates whether the Local-Pref
        path attribute is present."
    ::= { juniBgpRouteEntry 21 }

juniBgpRouteAtomicAggregate OBJECT-TYPE
    SYNTAX      INTEGER {
                    lessSpecificRouteNotSelected(1),
                    lessSpecificRouteSelected(2) }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Whether or not the local system has selected a less specific route
        without selecting a more specific route."
    ::= { juniBgpRouteEntry 22 }

juniBgpRouteAggregatorAS OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the AS field in the Aggregator path attribute if present.
        The juniBgpRouteAggregatorPresent object indicates whether the
        Aggregator path attribute is present."
    ::= { juniBgpRouteEntry 23 }

juniBgpRouteAggregatorAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of the address field in the Aggregator path attribute if
        present.  The juniBgpRouteAggregatorPresent object indicates whether the
        Aggregator path attribute is present."
    ::= { juniBgpRouteEntry 24 }

juniBgpRouteBestInIpRouteTable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "When BGP places a routes in the IP route table, that route may not be
        used for forwarding traffic if there are other routes (e.g. IGP routes
        or static routes) to that same prefix with a better administrative
        distance in the IP route table.  This object indicates whether or not
        the BGP route in the IP route table is the best route (and hence is used
        for forwarding traffic)."
    ::= { juniBgpRouteEntry 25 }

juniBgpRouteUnknown OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "One or more path attributes not understood by this BGP4 speaker.  Size
        zero (0) indicates the absence of such attribute(s).  Octets beyond the
        maximum size, if any, are not recorded by this object."
    ::= { juniBgpRouteEntry 26 }

juniBgpRouteExtendedCommunitiesPresent OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Is the Extended-Communities path attribute present?"
    ::= { juniBgpRouteEntry 27 }

juniBgpRouteValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Is the route valid?  The route is declared invalid if there is
        something wrong with it which is not serious enough for sending a
        notification, but it serious enough for not placing it in the IP route
        table or propagating it to other peers (for example the next-hop of the
        route is a local address)."
    ::= { juniBgpRouteEntry 28 }

juniBgpRouteSuppressedBy OBJECT-TYPE
    SYNTAX      INTEGER {
                    suppressedByNothing(1),
                    suppressedByAggregate(2),
                    suppressedByAutoSummary(3),
                    suppressedByDampening(4) }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Indicates whether or not the route is suppressed, and if so why:
            suppressedByNothing       - Route is not suppressed.
            suppressedByAggregate     - Route is suppressed by a summary-only
                                        aggregate.
            suppressedByAutoSummary   - Route is suppressed by an auto-summary.
            suppressedByDampening     - Route is suppressed as a result of
                                        route-flap dampening."
    ::= { juniBgpRouteEntry 29 }

juniBgpRouteNextHopReachable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Is the indirect next-hop of the route reachable (i.e. can the indirect
        next-hop of the route be resolved to at least one direct next-hop)?"
    ::= { juniBgpRouteEntry 30 }

juniBgpRouteSynchronizedWithIgp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Is the route is synchronized (i.e. is there is an IGP route to the same
        prefix in the IP route table)?"
    ::= { juniBgpRouteEntry 31 }

juniBgpRoutePlaceInIpRouteTable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Has a BGP route for this prefix been placed in the IP route table?"
    ::= { juniBgpRouteEntry 32 }

juniBgpRouteAdvertiseToExternalPeers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Should this route be advertised to external peers?"
    ::= { juniBgpRouteEntry 33 }

juniBgpRouteAdvertiseToInternalPeers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Should this route be advertised to internal peers?"
    ::= { juniBgpRouteEntry 34 }

juniBgpRouteDistinguisher OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The Route Distinguisher (RD) of the route.  This object has an
        undefined value for non-VPN routes."
    ::= { juniBgpRouteEntry 35 }

juniBgpRouteMplsLabel OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The MPLS label for the route."
    ::= { juniBgpRouteEntry 36 }

juniBgpRouteNextHopMetric OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The metric of direct next-hop of this BGP route."
    ::= { juniBgpRouteEntry 37 }


--
--  Obsolete BGP Route Flap History Table
--
juniBgpRouteFlapHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpRouteFlapHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The obsolete BGP Route Flap History Table contained information about
        recorded route flap events.  This table has been replaced by the
        juniBgpNewRouteFlapHistoryTable."
    ::= { juniBgpObjects 12 }

juniBgpRouteFlapHistoryEntry OBJECT-TYPE
    SYNTAX      JuniBgpRouteFlapHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "An instance in the list of route flap events."
    INDEX     { juniBgpRouteVrfName,
                juniBgpRouteAfi,
                juniBgpRouteSafi,
                juniBgpRouteIpAddrPrefix,
                juniBgpRouteIpAddrPrefixLen,
                juniBgpRouteDistinguisher,
                juniBgpRoutePeer,
                juniBgpRouteRouteType }
    ::= { juniBgpRouteFlapHistoryTable 1 }

JuniBgpRouteFlapHistoryEntry ::= SEQUENCE {
    juniBgpRouteFlapState                INTEGER,
    juniBgpRouteFlapFigureOfMerit        Unsigned32,
    juniBgpRouteFlapRemainingTime        Unsigned32,
    juniBgpRouteFlapSuppressThreshold    Unsigned32,
    juniBgpRouteFlapReuseThreshold       Unsigned32,
    juniBgpRouteFlapMaxHoldDownTime      Unsigned32,
    juniBgpRouteFlapHalfLifeReachable    Unsigned32,
    juniBgpRouteFlapHalfLifeUnreachable  Unsigned32 }

juniBgpRouteFlapState OBJECT-TYPE
    SYNTAX      INTEGER {
                    stateAvailable(1),
                    stateSuppressedReachable(2),
                    stateSuppressedUnreachable(3) }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Current state:
            stateAvailable(1)             - indicates path is available
            stateSuppressedReachable(2)   - indicates path is suppressed,
                                            destination is reachable
            stateSuppressedUnreachable(3) - indicates path in suppressed,
                                            destination is not reachable"
    ::= { juniBgpRouteFlapHistoryEntry 1 }

juniBgpRouteFlapFigureOfMerit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Calculated figure-of-merit."
    ::= { juniBgpRouteFlapHistoryEntry 2 }

juniBgpRouteFlapRemainingTime OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Number of seconds until reuse or removal."
    ::= { juniBgpRouteFlapHistoryEntry 3 }

juniBgpRouteFlapSuppressThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Damping cutoff threshold (scaled)."
    ::= { juniBgpRouteFlapHistoryEntry 4 }

juniBgpRouteFlapReuseThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Damping reuse threshold (scaled)."
    ::= { juniBgpRouteFlapHistoryEntry 5 }

juniBgpRouteFlapMaxHoldDownTime OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Maximum route holddown time (seconds)."
    ::= { juniBgpRouteFlapHistoryEntry 6 }

juniBgpRouteFlapHalfLifeReachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Figure of merit half life for reachable routes (seconds)."
    ::= { juniBgpRouteFlapHistoryEntry 7 }

juniBgpRouteFlapHalfLifeUnreachable OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Figure of merit half life for unreachable routes (seconds)."
    ::= { juniBgpRouteFlapHistoryEntry 8 }


--
--  Obsolete BGP 4 Route Community Table
--
juniBgpRouteCommunityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpRouteCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The obsolete BGP-4 Path Community Table contained information about
        communities within destination networks received from all BGP4 peers.
        This table has been replaced by the juniBgpNewRouteCommunityTable."
    ::= { juniBgpObjects 14 }

juniBgpRouteCommunityEntry OBJECT-TYPE
    SYNTAX      JuniBgpRouteCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "Community attribute information within a path to a network."
    INDEX     { juniBgpRouteVrfName,
                juniBgpRouteAfi,
                juniBgpRouteSafi,
                juniBgpRouteIpAddrPrefix,
                juniBgpRouteIpAddrPrefixLen,
                juniBgpRouteDistinguisher,
                juniBgpRoutePeer,
                juniBgpRouteRouteType,
                juniBgpRouteCommunityNumber }
    ::= { juniBgpRouteCommunityTable 1 }

JuniBgpRouteCommunityEntry ::= SEQUENCE {
    juniBgpRouteCommunityNumber  Unsigned32 }

juniBgpRouteCommunityNumber OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The community number."
    ::= { juniBgpRouteCommunityEntry 1 }


--
--  Obsolete BGP 4 Route Extended Community Table
--
juniBgpRouteExtendedCommunityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpRouteExtendedCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The obsolete BGP-4 Path Extended Community Table contained information
        about communities within destination networks received from all BGP4
        peers.  This table has been replaced by the
        juniBgpNewRouteExtendedCommunityTable."
    ::= { juniBgpObjects 22 }

juniBgpRouteExtendedCommunityEntry OBJECT-TYPE
    SYNTAX      JuniBgpRouteExtendedCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "Extended Community attribute information within a path to a network."
    INDEX     { juniBgpRouteVrfName,
                juniBgpRouteAfi,
                juniBgpRouteSafi,
                juniBgpRouteIpAddrPrefix,
                juniBgpRouteIpAddrPrefixLen,
                juniBgpRouteDistinguisher,
                juniBgpRoutePeer,
                juniBgpRouteRouteType,
                juniBgpRouteExtendedCommunityNumber }
    ::= { juniBgpRouteExtendedCommunityTable 1 }

JuniBgpRouteExtendedCommunityEntry ::= SEQUENCE {
    juniBgpRouteExtendedCommunityNumber  OCTET STRING }

juniBgpRouteExtendedCommunityNumber OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The extended community number."
    ::= { juniBgpRouteExtendedCommunityEntry 1 }


--
--  Obsolete BGP 4 Route ClusterId Table
--
juniBgpRouteClusterIdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniBgpRouteClusterIdEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The obsolete BGP-4 Path ClusterId Table contained information about
        router reflector cluster IDs.  This table has been replaced by the
        juniBgpNewRouteClusterIdTable."
    ::= { juniBgpObjects 15 }

juniBgpRouteClusterIdEntry OBJECT-TYPE
    SYNTAX      JuniBgpRouteClusterIdEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "ClusterId attribute information within a path to a network."
    INDEX     { juniBgpRouteVrfName,
                juniBgpRouteAfi,
                juniBgpRouteSafi,
                juniBgpRouteIpAddrPrefix,
                juniBgpRouteIpAddrPrefixLen,
                juniBgpRouteDistinguisher,
                juniBgpRoutePeer,
                juniBgpRouteRouteType,
                juniBgpRouteClusterId }
    ::= { juniBgpRouteClusterIdTable 1 }

JuniBgpRouteClusterIdEntry ::= SEQUENCE {
    juniBgpRouteClusterId    Unsigned32 }

juniBgpRouteClusterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The cluster id."
    ::= { juniBgpRouteClusterIdEntry 1 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Notifications
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- No notifications are defined in this MIB.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Conformance information
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniBgpConformance OBJECT IDENTIFIER ::= { juniBgpMIB 2 }
juniBgpCompliances OBJECT IDENTIFIER ::= { juniBgpConformance 1 }
juniBgpConfGroups  OBJECT IDENTIFIER ::= { juniBgpConformance 2 }

--
-- compliance statements
--
juniBgpCompliance  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when the
        juniBgpDefaultIPv4Unicast object was added to the general cofiguation
        group."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup,
            juniBgpPeerGroupConfGroup,
            juniBgpPeerGroupAddressFamilyConfGroup,
            juniBgpRouteConfGroup,
            juniBgpNetworkConfGroup,
            juniBgpAggregateConfGroup,
            juniBgpVrfConfGroup,
            juniBgpAddressFamilyConfGroup,
            juniBgpStorageConfGroup }
    ::= { juniBgpCompliances 1 }                                   -- JUNOSe 3.0

juniBgpCompliance2  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when the original route
        destination was added as an index to each of the route configuration
        tables."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup2,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup,
            juniBgpPeerGroupConfGroup,
            juniBgpPeerGroupAddressFamilyConfGroup,
            juniBgpRouteConfGroup,
            juniBgpNetworkConfGroup,
            juniBgpAggregateConfGroup,
            juniBgpVrfConfGroup,
            juniBgpAddressFamilyConfGroup,
            juniBgpStorageConfGroup }
    ::= { juniBgpCompliances 2 }                                   -- JUNOSe 3.2

juniBgpCompliance3  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the
        Juniper BGP MIB.  This statement became obsolete when the peer and
        peer-group local-as support was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup2,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup,
            juniBgpPeerGroupConfGroup,
            juniBgpPeerGroupAddressFamilyConfGroup,
            juniBgpNewRouteConfGroup,
            juniBgpNetworkConfGroup,
            juniBgpAggregateConfGroup,
            juniBgpVrfConfGroup,
            juniBgpAddressFamilyConfGroup,
            juniBgpStorageConfGroup }
    ::= { juniBgpCompliances 3 }                                   -- JUNOSe 3.3

juniBgpCompliance4  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support was added for
        adding unicast BGP routes into a multicast view."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup2,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup2,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup,
            juniBgpPeerGroupConfGroup2,
            juniBgpPeerGroupAddressFamilyConfGroup,
            juniBgpNewRouteConfGroup,
            juniBgpNetworkConfGroup,
            juniBgpAggregateConfGroup,
            juniBgpVrfConfGroup,
            juniBgpAddressFamilyConfGroup,
            juniBgpStorageConfGroup }
    ::= { juniBgpCompliances 4 }                                   -- JUNOSe 3.4

juniBgpCompliance5  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when the
        juniBgpRedistributeInternal object was added to the general
        configuration group and the history pool size objects were obsoleted and
        the ability to unconfigure BGP attributes from the MIB was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup2,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup2,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup,
            juniBgpPeerGroupConfGroup2,
            juniBgpPeerGroupAddressFamilyConfGroup,
            juniBgpNewRouteConfGroup,
            juniBgpNetworkConfGroup,
            juniBgpAggregateConfGroup,
            juniBgpVrfConfGroup2,
            juniBgpAddressFamilyConfGroup,
            juniBgpStorageConfGroup }
    ::= { juniBgpCompliances 5 }                                   -- JUNOSe 3.5

juniBgpCompliance6  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for four-octet
        AS-numbers, dynamic capability negotiation, iBGP multipath,
        confederation peers filter-list, and address family maximum prefix
        strict was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup3,
            juniBgpStatisticsConfGroup,
            juniBgpConfederationPeerConfGroup,
            juniBgpPeerConfGroup3,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup2,
            juniBgpPeerGroupConfGroup3,
            juniBgpPeerGroupAddressFamilyConfGroup2,
            juniBgpNewRouteConfGroup,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup3,
            juniBgpAddressFamilyConfGroup2,
            juniBgpStorageConfGroup2 }
    ::= { juniBgpCompliances 6 }                                   -- JUNOSe 4.0

juniBgpCompliance7  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for
        advertise-best-external-to-internal was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup4,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup4,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup3,
            juniBgpPeerGroupConfGroup4,
            juniBgpPeerGroupAddressFamilyConfGroup3,
            juniBgpNewRouteConfGroup2,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup4,
            juniBgpAddressFamilyConfGroup2,
            juniBgpStorageConfGroup2 }
    ::= { juniBgpCompliances 7 }                                   -- JUNOSe 4.1

juniBgpCompliance8  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for maximum-paths
        eiBGP and other features were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup5,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup5,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup3,
            juniBgpPeerGroupConfGroup5,
            juniBgpPeerGroupAddressFamilyConfGroup3,
            juniBgpNewRouteConfGroup2,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup4,
            juniBgpAddressFamilyConfGroup2,
            juniBgpStorageConfGroup2 }
    ::= { juniBgpCompliances 8 }                                   -- JUNOSe 5.0

juniBgpCompliance9  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for send-label
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup5,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup5,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup3,
            juniBgpPeerGroupConfGroup5,
            juniBgpPeerGroupAddressFamilyConfGroup3,
            juniBgpNewRouteConfGroup3,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup5,
            juniBgpAddressFamilyConfGroup3,
            juniBgpStorageConfGroup3 }
    ::= { juniBgpCompliances 9 }                                   -- JUNOSe 5.1

juniBgpCompliance10  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for a route-map
        for default-information originate and neighbor ... default-originate
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup5,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup5,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup4,
            juniBgpPeerGroupConfGroup5,
            juniBgpPeerGroupAddressFamilyConfGroup4,
            juniBgpNewRouteConfGroup3,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup6,
            juniBgpAddressFamilyConfGroup4,
            juniBgpStorageConfGroup3 }
    ::= { juniBgpCompliances 10 }                                  -- JUNOSe 5.2

juniBgpCompliance11  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when MIB support for graceful
        restart was added"
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup5,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup6,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup5,
            juniBgpPeerGroupConfGroup6,
            juniBgpPeerGroupAddressFamilyConfGroup5,
            juniBgpNewRouteConfGroup3,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup6,
            juniBgpAddressFamilyConfGroup5,
            juniBgpStorageConfGroup3,
            juniBgpPeerDynamicCapabilityConfGroup }
    ::= { juniBgpCompliances 11 }                                  -- JUNOSe 5.3

juniBgpCompliance12  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when BGP storage support
        was removed and when MIB support for next-hop-unchanged was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup6,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup7,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup6,
            juniBgpPeerGroupConfGroup7,
            juniBgpPeerGroupAddressFamilyConfGroup5,
            juniBgpNewRouteConfGroup4,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup6,
            juniBgpAddressFamilyConfGroup6,
            juniBgpStorageConfGroup3,
            juniBgpPeerDynamicCapabilityConfGroup }
    ::= { juniBgpCompliances 12 }                                  -- JUNOSe 6.0

juniBgpCompliance13  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
       "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for BGP BFD
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup6,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup7,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup7,
            juniBgpPeerGroupAddressFamilyConfGroup6,
            juniBgpPeerGroupConfGroup7,
            juniBgpNewRouteConfGroup4,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup7,
            juniBgpAddressFamilyConfGroup7,
            juniBgpPeerDynamicCapabilityConfGroup }
    ::= { juniBgpCompliances 13 }                                  -- JUNOSe 7.1

juniBgpCompliance14  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper
        BGP MIB.  This statement became obsolete when support for ibgp-singlehop
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup6,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup8,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup7,
            juniBgpPeerGroupAddressFamilyConfGroup6,
            juniBgpPeerGroupConfGroup8,
            juniBgpNewRouteConfGroup4,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup7,
            juniBgpAddressFamilyConfGroup7,
            juniBgpPeerDynamicCapabilityConfGroup }
    ::= { juniBgpCompliances 14 }                                  -- JUNOSe 7.2

juniBgpCompliance15  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for entities which implement the Juniper 
        BGP MIB. This statement became obsolete when support for conditional
        advertisement was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup6,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup9,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup7,
            juniBgpPeerGroupAddressFamilyConfGroup6,
            juniBgpPeerGroupConfGroup9,
            juniBgpNewRouteConfGroup4,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup7,
            juniBgpAddressFamilyConfGroup7,
            juniBgpPeerDynamicCapabilityConfGroup }
    ::= { juniBgpCompliances 15 }                                  -- JUNOSe 8.0

juniBgpCompliance16  MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for entities which implement the Juniper BGP
        MIB."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniBgpGeneralConfGroup6,
            juniBgpStatisticsConfGroup,
            juniBgpFourOctetConfederationPeerConfGroup,
            juniBgpPeerConfGroup9,
            juniBgpAfiSafiPeerConfGroup,
            juniBgpPeerAddressFamilyConfGroup7,
            juniBgpPeerGroupAddressFamilyConfGroup6,
            juniBgpPeerGroupConfGroup9,
            juniBgpNewRouteConfGroup4,
            juniBgpNetworkConfGroup2,
            juniBgpAggregateConfGroup2,
            juniBgpVrfConfGroup7,
            juniBgpAddressFamilyConfGroup7,
            juniBgpPeerDynamicCapabilityConfGroup,
            juniBgpPeerAddressFamilyConditionalAdvConfGroup,
            juniBgpPeerGroupAddressFamilyConditionalAdvConfGroup }
    ::= { juniBgpCompliances 16 }                                  -- JUNOSe 9.0

--
-- units of conformance
--
juniBgpGeneralConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpLocalAsNumber,
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpEqualCostLimit,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing general management of BGP in a
        Juniper product.  This group became obsolete when the
        juniBgpDefaultIPv4Unicast object was added."
    ::= { juniBgpConfGroups 1 }                                    -- JUNOSe 3.0

juniBgpStatisticsConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpBaselineTime,
        juniBgpDestinationCount,
        juniBgpDestinationMemoryUsed,
        juniBgpRouteCount,
        juniBgpRouteMemoryUsed,
        juniBgpSelectedRouteCount,
        juniBgpPathAttributeCount,
        juniBgpPathAttributeMemoryUsed,
        juniBgpRouteFlapHistoryCount,
        juniBgpRouteFlapHistoryMemoryUsed,
        juniBgpSuppressedRouteCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing statistics of BGP operations in a
        Juniper product."
    ::= { juniBgpConfGroups 2 }                                    -- JUNOSe 3.0

juniBgpConfederationPeerConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpConfederationPeerRowStatus }
    STATUS      deprecated
    DESCRIPTION
        "Deprecated object providing management of BGP-specific confederation
        peers in a Juniper product.  This group became deprecated when support
        for four-octet AS-numbers was added."
    ::= { juniBgpConfGroups 3 }                                    -- JUNOSe 3.0

juniBgpPeerConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemoteAsNumber,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group became obsolete when the
        juniBgpPeerLocalAsNumber object was added."
    ::= { juniBgpConfGroups 4 }                                    -- JUNOSe 3.0

juniBgpAfiSafiPeerConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerProposedAfiSafiPeerRowStatus,
        juniBgpLocalProposedAfiSafiPeerRowStatus,
        juniBgpExchangedAfiSafiPeerRowStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP-specific AFI and
        SAFI peers in a Juniper product."
    ::= { juniBgpConfGroups 5 }                                    -- JUNOSe 3.0

juniBgpPeerAddressFamilyConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product.  This group became obsolete when the
        ability to unconfigure attributes using the MIB was added."
    ::= { juniBgpConfGroups 6 }                                    -- JUNOSe 3.0

juniBgpPeerGroupConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRemoteAsNumber,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups
        in a Juniper product.  This group became obsolete when the
        juniBgpPeerGroupLocalAsNumber object was added."
    ::= { juniBgpConfGroups 7 }                                    -- JUNOSe 3.0

juniBgpPeerGroupAddressFamilyConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer group
        address families in a Juniper product.  This group was obsoleted when
        the ability to unconfigure BGP attributes using the MIB was added."
    ::= { juniBgpConfGroups 8 }                                    -- JUNOSe 3.0

juniBgpRouteConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpRouteFlapState,
        juniBgpRouteFlapFigureOfMerit,
        juniBgpRouteFlapRemainingTime,
        juniBgpRouteFlapSuppressThreshold,
        juniBgpRouteFlapReuseThreshold,
        juniBgpRouteFlapMaxHoldDownTime,
        juniBgpRouteFlapHalfLifeReachable,
        juniBgpRouteFlapHalfLifeUnreachable,
        juniBgpRouteOriginatorId,
        juniBgpRouteAtomicAggregatePresent,
        juniBgpRouteMedPresent,
        juniBgpRouteLocalPrefPresent,
        juniBgpRouteAggregatorPresent,
        juniBgpRouteCommunitiesPresent,
        juniBgpRouteOriginatorIdPresent,
        juniBgpRouteClusterListPresent,
        juniBgpRouteWeight,
        juniBgpRouteOrigin,
        juniBgpRouteASPathSegment,
        juniBgpRouteNextHop,
        juniBgpRouteMultiExitDisc,
        juniBgpRouteLocalPref,
        juniBgpRouteAtomicAggregate,
        juniBgpRouteAggregatorAS,
        juniBgpRouteAggregatorAddress,
        juniBgpRouteBestInIpRouteTable,
        juniBgpRouteUnknown,
        juniBgpRouteExtendedCommunitiesPresent,
        juniBgpRouteValid,
        juniBgpRouteSuppressedBy,
        juniBgpRouteNextHopReachable,
        juniBgpRouteSynchronizedWithIgp,
        juniBgpRoutePlaceInIpRouteTable,
        juniBgpRouteAdvertiseToExternalPeers,
        juniBgpRouteAdvertiseToInternalPeers,
        juniBgpRouteDistinguisher,
        juniBgpRouteMplsLabel,
        juniBgpRouteNextHopMetric,
        juniBgpRouteCommunityNumber,
        juniBgpRouteExtendedCommunityNumber,
        juniBgpRouteClusterId }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP routes in a
        Juniper product.  This group became obsolete when the original route
        destination was added as an index to each of the tables."
    ::= { juniBgpConfGroups 9 }                                    -- JUNOSe 3.0

juniBgpNetworkConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpNetworkBackdoor,
        juniBgpNetworkRowStatus,
        juniBgpNetworkWeightSpecified,
        juniBgpNetworkWeight,
        juniBgpNetworkRouteMap }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP networks in
        a Juniper product.  This group was obsoleted when the ability to
        unconfigure BGP attributes using the MIB was added."
    ::= { juniBgpConfGroups 10 }                                   -- JUNOSe 3.0

juniBgpAggregateConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpAggregateAsSet,
        juniBgpAggregateSummaryOnly,
        juniBgpAggregateAttributeMap,
        juniBgpAggregateAdvertiseMap,
        juniBgpAggregateRowStatus,
        juniBgpAggregateSuppressMap }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing consolidation of BGP route
        information in a Juniper product.  This group was obsoleted when the
        ability to unconfigure BGP attributes using the MIB was added."
    ::= { juniBgpConfGroups 11 }                                   -- JUNOSe 3.0

juniBgpVrfConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfExternalDistance,
        juniBgpVrfInternalDistance,
        juniBgpVrfLocalDistance,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product.  This
        group became obsolete when the juniBgpVrfAddUnicastRoutesToMulticastView
        object was added."
    ::= { juniBgpConfGroups 12 }                                   -- JUNOSe 3.0

juniBgpAddressFamilyConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP address
        families in a Juniper product.  This group was obsoleted when the
        ability to unconfigure BGP attributes using the MIB was added."
    ::= { juniBgpConfGroups 13 }                                   -- JUNOSe 3.0

juniBgpStorageConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpStorageInitialHeapSize,
        juniBgpStorageMaxHeapSize,
        juniBgpStorageInitialVrfPoolSize,
        juniBgpStorageMaxVrfPoolSize,
        juniBgpStorageInitialAddressFamilyPoolSize,
        juniBgpStorageMaxAddressFamilyPoolSize,
        juniBgpStorageInitialPeerPoolSize,
        juniBgpStorageMaxPeerPoolSize,
        juniBgpStorageInitialPeerAfPoolSize,
        juniBgpStorageMaxPeerAfPoolSize,
        juniBgpStorageInitialPeerGroupPoolSize,
        juniBgpStorageMaxPeerGroupPoolSize,
        juniBgpStorageInitialPeerGroupAfPoolSize,
        juniBgpStorageMaxPeerGroupAfPoolSize,
        juniBgpStorageInitialNetworkPoolSize,
        juniBgpStorageMaxNetworkPoolSize,
        juniBgpStorageInitialAggregatePoolSize,
        juniBgpStorageMaxAggregatePoolSize,
        juniBgpStorageInitialDestinationPoolSize,
        juniBgpStorageMaxDestinationPoolSize,
        juniBgpStorageInitialRoutePoolSize,
        juniBgpStorageMaxRoutePoolSize,
        juniBgpStorageInitialAttributesPoolSize,
        juniBgpStorageMaxAttributesPoolSize,
        juniBgpStorageInitialRouteFlapHistoryPoolSize,
        juniBgpStorageMaxRouteFlapHistoryPoolSize,
        juniBgpStorageInitialNetworkRoutePoolSize,
        juniBgpStorageMaxNetworkRoutePoolSize,
        juniBgpStorageInitialRedistributedRoutePoolSize,
        juniBgpStorageMaxRedistributedRoutePoolSize,
        juniBgpStorageInitialAggregateRoutePoolSize,
        juniBgpStorageMaxAggregateRoutePoolSize,
        juniBgpStorageInitialAutoSummaryRoutePoolSize,
        juniBgpStorageMaxAutoSummaryRoutePoolSize,
        juniBgpStorageInitialHistoryRoutePoolSize,
        juniBgpStorageMaxHistoryRoutePoolSize,
        juniBgpStorageInitialSendQueueEntryPoolSize,
        juniBgpStorageMaxSendQueueEntryPoolSize,
        juniBgpStorageInitialVpnRoutePoolSize,
        juniBgpStorageMaxVpnRoutePoolSize,
        juniBgpStorageInitialRouteTargetPoolSize,
        juniBgpStorageMaxRouteTargetPoolSize }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP
        characteristics in a Juniper product that will not take affect until
        reboot.  This group became obsolete when the history pool size objects
        were obsoleted."
    ::= { juniBgpConfGroups 14 }                                   -- JUNOSe 3.0

juniBgpGeneralConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpLocalAsNumber,
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpEqualCostLimit,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter,
        juniBgpDefaultIPv4Unicast }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing general management in a
        Juniper product.  This group became obsolete when the
        juniBgpRedistributeInternal object and unconfigure support were added."
    ::= { juniBgpConfGroups 15 }                                   -- JUNOSe 3.2

juniBgpNewRouteConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpNewRouteOriginatorId,
        juniBgpNewRouteAtomicAggregatePresent,
        juniBgpNewRouteMedPresent,
        juniBgpNewRouteLocalPrefPresent,
        juniBgpNewRouteAggregatorPresent,
        juniBgpNewRouteCommunitiesPresent,
        juniBgpNewRouteOriginatorIdPresent,
        juniBgpNewRouteClusterListPresent,
        juniBgpNewRouteWeight,
        juniBgpNewRouteOrigin,
        juniBgpNewRouteASPathSegment,
        juniBgpNewRouteNextHop,
        juniBgpNewRouteMultiExitDisc,
        juniBgpNewRouteLocalPref,
        juniBgpNewRouteAtomicAggregate,
        juniBgpNewRouteAggregatorAS,
        juniBgpNewRouteAggregatorAddress,
        juniBgpNewRouteBestInIpRouteTable,
        juniBgpNewRouteUnknown,
        juniBgpNewRouteExtendedCommunitiesPresent,
        juniBgpNewRouteValid,
        juniBgpNewRouteSuppressedBy,
        juniBgpNewRouteNextHopReachable,
        juniBgpNewRouteSynchronizedWithIgp,
        juniBgpNewRoutePlaceInIpRouteTable,
        juniBgpNewRouteAdvertiseToExternalPeers,
        juniBgpNewRouteAdvertiseToInternalPeers,
        juniBgpNewRouteMplsLabel,
        juniBgpNewRouteNextHopMetric,
        juniBgpNewRouteFlapState,
        juniBgpNewRouteFlapFigureOfMerit,
        juniBgpNewRouteFlapRemainingTime,
        juniBgpNewRouteFlapSuppressThreshold,
        juniBgpNewRouteFlapReuseThreshold,
        juniBgpNewRouteFlapMaxHoldDownTime,
        juniBgpNewRouteFlapHalfLifeReachable,
        juniBgpNewRouteFlapHalfLifeUnreachable,
        juniBgpNewRouteCommunityNumber,
        juniBgpNewRouteExtendedCommunityNumber,
        juniBgpNewRouteClusterId }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP routes in a
        Juniper product.  This group was obsoleted when label was replaced by
        in-label and out-label."
    ::= { juniBgpConfGroups 16 }                                   -- JUNOSe 3.3

juniBgpPeerConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemoteAsNumber,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerLocalAsNumber }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group became obsolete when the ability to
        unconfigure BGP attributes from the MIB was added."
    ::= { juniBgpConfGroups 17 }                                   -- JUNOSe 3.4

juniBgpPeerGroupConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRemoteAsNumber,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupLocalAsNumber }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups
        in a Juniper product.  This group was obsoleted when the ability to
        unconfigure BGP attributes using the MIB was added."
    ::= { juniBgpConfGroups 18 }                                   -- JUNOSe 3.4

juniBgpVrfConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfExternalDistance,
        juniBgpVrfInternalDistance,
        juniBgpVrfLocalDistance,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product.  This
        group was obsoleted when the ability to unconfigure BGP attributes using
        the MIB was added."
    ::= { juniBgpConfGroups 19 }                                   -- JUNOSe 3.5

juniBgpGeneralConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpLocalAsNumber,
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpEqualCostLimit,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter,
        juniBgpDefaultIPv4Unicast,
        juniBgpRedistributeInternal,
        juniBgpUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing general management of BGP in a
        Juniper product.  This group became obsolete when four-octet AS-numbers
        and maximum-paths iBGP support was added."
    ::= { juniBgpConfGroups 20 }                                   -- JUNOSe 4.0

juniBgpStorageConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpStorageInitialHeapSize,
        juniBgpStorageMaxHeapSize,
        juniBgpStorageInitialVrfPoolSize,
        juniBgpStorageMaxVrfPoolSize,
        juniBgpStorageInitialAddressFamilyPoolSize,
        juniBgpStorageMaxAddressFamilyPoolSize,
        juniBgpStorageInitialPeerPoolSize,
        juniBgpStorageMaxPeerPoolSize,
        juniBgpStorageInitialPeerAfPoolSize,
        juniBgpStorageMaxPeerAfPoolSize,
        juniBgpStorageInitialPeerGroupPoolSize,
        juniBgpStorageMaxPeerGroupPoolSize,
        juniBgpStorageInitialPeerGroupAfPoolSize,
        juniBgpStorageMaxPeerGroupAfPoolSize,
        juniBgpStorageInitialNetworkPoolSize,
        juniBgpStorageMaxNetworkPoolSize,
        juniBgpStorageInitialAggregatePoolSize,
        juniBgpStorageMaxAggregatePoolSize,
        juniBgpStorageInitialDestinationPoolSize,
        juniBgpStorageMaxDestinationPoolSize,
        juniBgpStorageInitialRoutePoolSize,
        juniBgpStorageMaxRoutePoolSize,
        juniBgpStorageInitialAttributesPoolSize,
        juniBgpStorageMaxAttributesPoolSize,
        juniBgpStorageInitialRouteFlapHistoryPoolSize,
        juniBgpStorageMaxRouteFlapHistoryPoolSize,
        juniBgpStorageInitialNetworkRoutePoolSize,
        juniBgpStorageMaxNetworkRoutePoolSize,
        juniBgpStorageInitialRedistributedRoutePoolSize,
        juniBgpStorageMaxRedistributedRoutePoolSize,
        juniBgpStorageInitialAggregateRoutePoolSize,
        juniBgpStorageMaxAggregateRoutePoolSize,
        juniBgpStorageInitialAutoSummaryRoutePoolSize,
        juniBgpStorageMaxAutoSummaryRoutePoolSize,
        juniBgpStorageInitialSendQueueEntryPoolSize,
        juniBgpStorageMaxSendQueueEntryPoolSize,
        juniBgpStorageInitialVpnRoutePoolSize,
        juniBgpStorageMaxVpnRoutePoolSize,
        juniBgpStorageInitialRouteTargetPoolSize,
        juniBgpStorageMaxRouteTargetPoolSize }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP
        characteristics in a Juniper product that will not take affect until
        reboot.  This group became obsolete when storage heap size objects were
        obsoleted."
    ::= { juniBgpConfGroups 21 }                                   -- JUNOSe 4.0

juniBgpPeerConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemoteAsNumber,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerLocalAsNumber,
        juniBgpPeerUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This groups was obsoleted when support for four octet
        AS numbers was added."
    ::= { juniBgpConfGroups 22 }                                   -- JUNOSe 4.0

juniBgpPeerAddressFamilyConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product.  This group was obsoleted when support
        for ORFs was added."
    ::= { juniBgpConfGroups 23 }                                   -- JUNOSe 4.0

juniBgpPeerGroupConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRemoteAsNumber,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupLocalAsNumber,
        juniBgpPeerGroupUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups
        in a Juniper product.  This group was obsoleted when support for dynamic
        capability negotiation was added."
    ::= { juniBgpConfGroups 24 }                                   -- JUNOSe 4.0

juniBgpPeerGroupAddressFamilyConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity,
        juniBgpPeerGroupAddressFamilyUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer group
        address families in a Juniper product.  This group was obsoleted when
        support for ORFs was added."
    ::= { juniBgpConfGroups 25 }                                   -- JUNOSe 4.0

juniBgpNetworkConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpNetworkBackdoor,
        juniBgpNetworkRowStatus,
        juniBgpNetworkWeightSpecified,
        juniBgpNetworkWeight,
        juniBgpNetworkRouteMap,
        juniBgpNetworkUnconfiguredAttributes }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP networks in a
        Juniper product."
    ::= { juniBgpConfGroups 26 }                                   -- JUNOSe 4.0

juniBgpAggregateConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpAggregateAsSet,
        juniBgpAggregateSummaryOnly,
        juniBgpAggregateAttributeMap,
        juniBgpAggregateAdvertiseMap,
        juniBgpAggregateRowStatus,
        juniBgpAggregateSuppressMap,
        juniBgpAggregateUnconfiguredAttributes }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing consolidation of BGP route
        information in a Juniper product."
    ::= { juniBgpConfGroups 27 }                                   -- JUNOSe 4.0

juniBgpVrfConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfExternalDistance,
        juniBgpVrfInternalDistance,
        juniBgpVrfLocalDistance,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView,
        juniBgpVrfUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product.  This
        group was obsoleted when support for iBGP multipath was added."
    ::= { juniBgpConfGroups 28 }                                   -- JUNOSe 4.0

juniBgpAddressFamilyConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP
        address-family groups in a Juniper product.  This group was obsoleted
        when support for administrative distance was added."
    ::= { juniBgpConfGroups 29 }                                   -- JUNOSe 4.0

juniBgpGeneralConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter,
        juniBgpDefaultIPv4Unicast,
        juniBgpRedistributeInternal,
        juniBgpFourOctetLocalAsNumber,
        juniBgpConfederationPeersFilterList,
        juniBgpUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing general management of BGP in
        a Juniper product.  This group became obsolete when support for
        advertise-best-external-to-internal was added."
    ::= { juniBgpConfGroups 30 }                                   -- JUNOSe 4.1

juniBgpFourOctetConfederationPeerConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpFourOctetConfederationPeerRowStatus }
    STATUS      current
    DESCRIPTION
        "An object providing management of BGP-specific confederation peers in a
        Juniper product."
    ::= { juniBgpConfGroups 31 }                                   -- JUNOSe 4.1

juniBgpPeerConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group was obsoleted when support for
        site-of-origin was added."
    ::= { juniBgpConfGroups 32 }                                   -- JUNOSe 4.1

juniBgpPeerAddressFamilyConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit,
        juniBgpPeerAddressFamilyReceivedPrefixListOrfName,
        juniBgpPeerAddressFamilyMaximumPrefixStrict,
        juniBgpPeerAddressFamilyUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product.  This group was obsoleted when support
        for send-label was added "
    ::= { juniBgpConfGroups 33 }                                   -- JUNOSe 4.1

juniBgpPeerGroupConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups
        in a Juniper product.  This group was obsoleted when support for
        site-of-origin was added."
    ::= { juniBgpConfGroups 34 }                                   -- JUNOSe 4.1

juniBgpPeerGroupAddressFamilyConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerGroupAddressFamilyMaximumPrefixStrict,
        juniBgpPeerGroupAddressFamilyUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer group
        address families in a Juniper product.  This group was obsoleted when
        support for send-label was added."
    ::= { juniBgpConfGroups 35 }                                   -- JUNOSe 4.1

juniBgpVrfConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfExternalDistance,
        juniBgpVrfInternalDistance,
        juniBgpVrfLocalDistance,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView,
        juniBgpVrfMaximumPathsEbgp,
        juniBgpVrfMaximumPathsIbgp,
        juniBgpVrfUnconfiguredAttributes }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product.  This
        group was obsoleted when support for eiBGP multipath was added."
    ::= { juniBgpConfGroups 36 }                                   -- JUNOSe 4.1

juniBgpDeprecatedGroup OBJECT-GROUP
    OBJECTS {
        juniBgpLocalAsNumber,
        juniBgpConfederationPeerRowStatus,
        juniBgpPeerRemoteAsNumber,
        juniBgpPeerLocalAsNumber,
        juniBgpPeerGroupRemoteAsNumber,
        juniBgpPeerGroupLocalAsNumber }
    STATUS      deprecated
    DESCRIPTION
        "A collection of deprecated objects that continue to be supported on
        some current Juniper products."
    ::= { juniBgpConfGroups 37 }                                   -- JUNOSe 4.1

juniBgpNewRouteConfGroup2 OBJECT-GROUP
    OBJECTS {
        juniBgpNewRouteOriginatorId,
        juniBgpNewRouteAtomicAggregatePresent,
        juniBgpNewRouteMedPresent,
        juniBgpNewRouteLocalPrefPresent,
        juniBgpNewRouteAggregatorPresent,
        juniBgpNewRouteCommunitiesPresent,
        juniBgpNewRouteOriginatorIdPresent,
        juniBgpNewRouteClusterListPresent,
        juniBgpNewRouteWeight,
        juniBgpNewRouteOrigin,
        juniBgpNewRouteASPathSegment,
        juniBgpNewRouteNextHop,
        juniBgpNewRouteMultiExitDisc,
        juniBgpNewRouteLocalPref,
        juniBgpNewRouteAtomicAggregate,
        juniBgpNewRouteAggregatorAS,
        juniBgpNewRouteAggregatorAddress,
        juniBgpNewRouteBestInIpRouteTable,
        juniBgpNewRouteUnknown,
        juniBgpNewRouteExtendedCommunitiesPresent,
        juniBgpNewRouteValid,
        juniBgpNewRouteSuppressedBy,
        juniBgpNewRouteNextHopReachable,
        juniBgpNewRouteSynchronizedWithIgp,
        juniBgpNewRoutePlaceInIpRouteTable,
        juniBgpNewRouteAdvertiseToExternalPeers,
        juniBgpNewRouteAdvertiseToInternalPeers,
        juniBgpNewRouteNextHopMetric,
        juniBgpNewRouteFlapState,
        juniBgpNewRouteFlapFigureOfMerit,
        juniBgpNewRouteFlapRemainingTime,
        juniBgpNewRouteFlapSuppressThreshold,
        juniBgpNewRouteFlapReuseThreshold,
        juniBgpNewRouteFlapMaxHoldDownTime,
        juniBgpNewRouteFlapHalfLifeReachable,
        juniBgpNewRouteFlapHalfLifeUnreachable,
        juniBgpNewRouteCommunityNumber,
        juniBgpNewRouteExtendedCommunityNumber,
        juniBgpNewRouteClusterId,
        juniBgpNewRouteMplsInLabel,
        juniBgpNewRouteMplsOutLabel }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP routes in a
        Juniper product.  This group was obsoleted when support for leaked flag
        attribute of the BGP route was added."
    ::= { juniBgpConfGroups 38 }                                   -- JUNOSe 4.1

juniBgpGeneralConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter,
        juniBgpDefaultIPv4Unicast,
        juniBgpRedistributeInternal,
        juniBgpFourOctetLocalAsNumber,
        juniBgpConfederationPeersFilterList,
        juniBgpUnconfiguredAttributes,
        juniBgpAdvertiseBestExternalToInternal }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing general management of BGP in a
        Juniper product.  This group was obsoleted when MIB support for graceful
        restart was added."
    ::= { juniBgpConfGroups 39 }                                   -- JUNOSe 5.0

juniBgpPeerConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes,
        juniBgpPeerSiteOfOrigin,
        juniBgpPeerLenient }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group was obsoleted when support for the new
        dynamic capability negotiation draft was added."
    ::= { juniBgpConfGroups 40 }                                   -- JUNOSe 5.0

juniBgpPeerGroupConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes,
        juniBgpPeerGroupSiteOfOrigin,
        juniBgpPeerGroupLenient }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups
        in a Juniper product.  This group was obsoleted when support for the new
        dynamic capability negotiation draft was added."
    ::= { juniBgpConfGroups 41 }                                   -- JUNOSe 5.0

juniBgpVrfConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView,
        juniBgpVrfMaximumPathsEbgp,
        juniBgpVrfMaximumPathsIbgp,
        juniBgpVrfUnconfiguredAttributes,
        juniBgpVrfMaximumPathsEIbgp }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product.  This
        group was obsoleted when support for carrier's carrier was added."
    ::= { juniBgpConfGroups 42 }                                   -- JUNOSe 5.1

juniBgpAddressFamilyConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes,
        juniBgpAddressFamilyExternalDistance,
        juniBgpAddressFamilyInternalDistance,
        juniBgpAddressFamilyLocalDistance }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP address
        families in a Juniper product.  This group was obsoleted when support
        for check-vpn-next-hops was added."
    ::= { juniBgpConfGroups 43 }                                   -- JUNOSe 5.1

juniBgpStorageConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpStorageInitialVrfPoolSize,
        juniBgpStorageMaxVrfPoolSize,
        juniBgpStorageInitialAddressFamilyPoolSize,
        juniBgpStorageMaxAddressFamilyPoolSize,
        juniBgpStorageInitialPeerPoolSize,
        juniBgpStorageMaxPeerPoolSize,
        juniBgpStorageInitialPeerAfPoolSize,
        juniBgpStorageMaxPeerAfPoolSize,
        juniBgpStorageInitialPeerGroupPoolSize,
        juniBgpStorageMaxPeerGroupPoolSize,
        juniBgpStorageInitialPeerGroupAfPoolSize,
        juniBgpStorageMaxPeerGroupAfPoolSize,
        juniBgpStorageInitialNetworkPoolSize,
        juniBgpStorageMaxNetworkPoolSize,
        juniBgpStorageInitialAggregatePoolSize,
        juniBgpStorageMaxAggregatePoolSize,
        juniBgpStorageInitialDestinationPoolSize,
        juniBgpStorageMaxDestinationPoolSize,
        juniBgpStorageInitialRoutePoolSize,
        juniBgpStorageMaxRoutePoolSize,
        juniBgpStorageInitialAttributesPoolSize,
        juniBgpStorageMaxAttributesPoolSize,
        juniBgpStorageInitialRouteFlapHistoryPoolSize,
        juniBgpStorageMaxRouteFlapHistoryPoolSize,
        juniBgpStorageInitialNetworkRoutePoolSize,
        juniBgpStorageMaxNetworkRoutePoolSize,
        juniBgpStorageInitialRedistributedRoutePoolSize,
        juniBgpStorageMaxRedistributedRoutePoolSize,
        juniBgpStorageInitialAggregateRoutePoolSize,
        juniBgpStorageMaxAggregateRoutePoolSize,
        juniBgpStorageInitialAutoSummaryRoutePoolSize,
        juniBgpStorageMaxAutoSummaryRoutePoolSize,
        juniBgpStorageInitialSendQueueEntryPoolSize,
        juniBgpStorageMaxSendQueueEntryPoolSize,
        juniBgpStorageInitialVpnRoutePoolSize,
        juniBgpStorageMaxVpnRoutePoolSize,
        juniBgpStorageInitialRouteTargetPoolSize,
        juniBgpStorageMaxRouteTargetPoolSize }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP
        characteristics in a Juniper product that will not take affect until
        reboot.  This group became obsolete when storage support objects were
        obsoleted."
    ::= { juniBgpConfGroups 44 }                                   -- JUNOSe 7.1

juniBgpNewRouteConfGroup3 OBJECT-GROUP
    OBJECTS {
        juniBgpNewRouteOriginatorId,
        juniBgpNewRouteAtomicAggregatePresent,
        juniBgpNewRouteMedPresent,
        juniBgpNewRouteLocalPrefPresent,
        juniBgpNewRouteAggregatorPresent,
        juniBgpNewRouteCommunitiesPresent,
        juniBgpNewRouteOriginatorIdPresent,
        juniBgpNewRouteClusterListPresent,
        juniBgpNewRouteWeight,
        juniBgpNewRouteOrigin,
        juniBgpNewRouteASPathSegment,
        juniBgpNewRouteNextHop,
        juniBgpNewRouteMultiExitDisc,
        juniBgpNewRouteLocalPref,
        juniBgpNewRouteAtomicAggregate,
        juniBgpNewRouteAggregatorAS,
        juniBgpNewRouteAggregatorAddress,
        juniBgpNewRouteBestInIpRouteTable,
        juniBgpNewRouteUnknown,
        juniBgpNewRouteExtendedCommunitiesPresent,
        juniBgpNewRouteValid,
        juniBgpNewRouteSuppressedBy,
        juniBgpNewRouteNextHopReachable,
        juniBgpNewRouteSynchronizedWithIgp,
        juniBgpNewRoutePlaceInIpRouteTable,
        juniBgpNewRouteAdvertiseToExternalPeers,
        juniBgpNewRouteAdvertiseToInternalPeers,
        juniBgpNewRouteNextHopMetric,
        juniBgpNewRouteFlapState,
        juniBgpNewRouteFlapFigureOfMerit,
        juniBgpNewRouteFlapRemainingTime,
        juniBgpNewRouteFlapSuppressThreshold,
        juniBgpNewRouteFlapReuseThreshold,
        juniBgpNewRouteFlapMaxHoldDownTime,
        juniBgpNewRouteFlapHalfLifeReachable,
        juniBgpNewRouteFlapHalfLifeUnreachable,
        juniBgpNewRouteCommunityNumber,
        juniBgpNewRouteExtendedCommunityNumber,
        juniBgpNewRouteClusterId,
        juniBgpNewRouteMplsInLabel,
        juniBgpNewRouteMplsOutLabel,
        juniBgpNewRouteLeaked }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP routes in a Juniper
        product.  This group was obsoleted when MIB support for graceful restart was
        added."
    ::= { juniBgpConfGroups 45 }                                   -- JUNOSe 5.1

juniBgpPeerAddressFamilyConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit,
        juniBgpPeerAddressFamilyReceivedPrefixListOrfName,
        juniBgpPeerAddressFamilyMaximumPrefixStrict,
        juniBgpPeerAddressFamilyUnconfiguredAttributes,
        juniBgpPeerAddressFamilySendLabel }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product.  This group was obsoleted when support
        for a route-map for neighbor ... default-originate was added."
    ::= { juniBgpConfGroups 46 }                                   -- JUNOSe 5.2

juniBgpPeerGroupAddressFamilyConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerGroupAddressFamilyMaximumPrefixStrict,
        juniBgpPeerGroupAddressFamilyUnconfiguredAttributes,
        juniBgpPeerGroupAddressFamilySendLabel }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer group
        address families in a Juniper product.  This group was obsoleted when
        support for a route-map for neighbor ... default-originate was added."
    ::= { juniBgpConfGroups 47 }                                   -- JUNOSe 5.2

juniBgpVrfConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView,
        juniBgpVrfMaximumPathsEbgp,
        juniBgpVrfMaximumPathsIbgp,
        juniBgpVrfUnconfiguredAttributes,
        juniBgpVrfMaximumPathsEIbgp,
        juniBgpVrfCarriersCarrierModeEnabled }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product. This
        group was obsoleted when the BGP-specific carrier's carrier flag was 
        removed."
    ::= { juniBgpConfGroups 48 }                                   -- JUNOSe 5.2

juniBgpAddressFamilyConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes,
        juniBgpAddressFamilyExternalDistance,
        juniBgpAddressFamilyInternalDistance,
        juniBgpAddressFamilyLocalDistance,
        juniBgpAddressFamilyCheckVpnNextHops }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP address
        families in a Juniper product.  This group was obsoleted when support
        for the route-map option in default-information originate was added."
    ::= { juniBgpConfGroups 49 }                                   -- JUNOSe 5.2

juniBgpPeerAddressFamilyConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit,
        juniBgpPeerAddressFamilyReceivedPrefixListOrfName,
        juniBgpPeerAddressFamilyMaximumPrefixStrict,
        juniBgpPeerAddressFamilyUnconfiguredAttributes,
        juniBgpPeerAddressFamilySendLabel,
        juniBgpPeerAddressFamilyDefaultOriginateRouteMap }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product.  This group was obsoleted when MIB support
        for graceful restart was added."
    ::= { juniBgpConfGroups 50 }                                   -- JUNOSe 5.3

juniBgpPeerAddressFamilyConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit,
        juniBgpPeerAddressFamilyReceivedPrefixListOrfName,
        juniBgpPeerAddressFamilyMaximumPrefixStrict,
        juniBgpPeerAddressFamilyUnconfiguredAttributes,
        juniBgpPeerAddressFamilySendLabel,
        juniBgpPeerAddressFamilyDefaultOriginateRouteMap,
        juniBgpPeerAddressFamilySentCapabilityGracefulRestart,
        juniBgpPeerAddressFamilyReceivedCapabilityGracefulRestart,
        juniBgpPeerAddressFamilySentForwardingStatePreserved,
        juniBgpPeerAddressFamilyReceivedForwardingStatePreserved,
        juniBgpPeerAddressFamilySentEndOfRibMarker,
        juniBgpPeerAddressFamilyReceivedEndOfRibMarker,
        juniBgpPeerAddressFamilyWaitingForEndOfRibBeforeFlushStaleRoutes,
        juniBgpPeerAddressFamilyWaitingForEndOfRibBeforePathSelection }                   
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer address
        families in a Juniper product. This group was obsoleted when MIB support
        for next-hop-unchanged was added."
    ::= { juniBgpConfGroups 58 }                                   -- JUNOSe 6.0

juniBgpPeerGroupAddressFamilyConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerGroupAddressFamilyMaximumPrefixStrict,
        juniBgpPeerGroupAddressFamilyUnconfiguredAttributes,
        juniBgpPeerGroupAddressFamilySendLabel,
        juniBgpPeerGroupAddressFamilyDefaultOriginateRouteMap }
    STATUS      current
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer group address
        families in a Juniper product. This group was obsoleted when MIB support
        for next-hop-unchanged was added."
    ::= { juniBgpConfGroups 51 }                                   -- JUNOSe 5.3

juniBgpAddressFamilyConfGroup5 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes,
        juniBgpAddressFamilyExternalDistance,
        juniBgpAddressFamilyInternalDistance,
        juniBgpAddressFamilyLocalDistance,
        juniBgpAddressFamilyDefaultOriginateRouteMap,
        juniBgpAddressFamilyIpIntfProfileNameForMplsHeads,
        juniBgpAddressFamilyIpIntfProfileNameForMplsTails,
        juniBgpAddressFamilyIpIntfServiceProfileNameForMplsHeads,
        juniBgpAddressFamilyIpIntfServiceProfileNameForMplsTails,
        juniBgpAddressFamilyCheckVpnNextHops }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP address families in
        a Juniper product.  This group was obsoleted when MIB support for graceful
        restart was added."
    ::= { juniBgpConfGroups 52 }                                   -- JUNOSe 5.3

juniBgpPeerConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes,
        juniBgpPeerSiteOfOrigin,
        juniBgpPeerLenient,
        juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerPassive,
        juniBgpPeerDynamic }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a Juniper
        product.  This group was obsoleted when MIB support for graceful restart
        was added."
    ::= { juniBgpConfGroups 53 }                                   -- JUNOSe 5.3

juniBgpPeerGroupConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes,
        juniBgpPeerGroupSiteOfOrigin,
        juniBgpPeerGroupLenient,
        juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerGroupPassive,
        juniBgpPeerGroupConfiguredPeerType,
        juniBgpPeerGroupAllowAccessListName,
        juniBgpPeerGroupAllowMaxPeers,
        juniBgpPeerGroupCurrentDynamicPeerCount,
        juniBgpPeerGroupHighWaterMarkDynamicPeerCount,
        juniBgpPeerGroupRejectedDynamicPeerCount }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups in a
        Juniper product.  This group was obsoleted when MIB support for graceful
        restart was added."
    ::= { juniBgpConfGroups 54 }                                   -- JUNOSe 5.3

juniBgpPeerDynamicCapabilityConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerDynamicCapabilitySent,
        juniBgpPeerDynamicCapabilityReceived }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP dynamic capability
        negotiation."
    ::= { juniBgpConfGroups 55 }                                   -- JUNOSe 5.3

juniBgpGeneralConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpEnabled,
        juniBgpIdentifier,
        juniBgpAlwaysCompareMed,
        juniBgpDefaultLocalPreference,
        juniBgpClientToClientReflection,
        juniBgpClusterId,
        juniBgpConfederationId,
        juniBgpMissingAsWorst,
        juniBgpResetAllConnectionType,
        juniBgpAdvertiseInactive,
        juniBgpEnforceFirstAs,
        juniBgpConfedCompareMed,
        juniBgpGlobalRetryInterval,
        juniBgpGlobalConfigKeepAliveInterval,
        juniBgpGlobalConfigHoldTime,
        juniBgpGlobalAsOriginationInterval,
        juniBgpExternalAdvertisementInterval,
        juniBgpGlobalRibOutEnabled,
        juniBgpOverloadShutdown,
        juniBgpLogNeighborChanges,
        juniBgpFastExternalFallover,
        juniBgpInternalAdvertisementInterval,
        juniBgpMaxAsLimit,
        juniBgpOperationalState,
        juniBgpPreviousOperationalState,
        juniBgpAutomaticRouteTargetFilter,
        juniBgpDefaultIPv4Unicast,
        juniBgpRedistributeInternal,
        juniBgpFourOctetLocalAsNumber,
        juniBgpConfederationPeersFilterList,
        juniBgpUnconfiguredAttributes,
        juniBgpAdvertiseBestExternalToInternal,
        juniBgpGracefulRestartRestartTime,
        juniBgpGracefulRestartStalePathsTime,
        juniBgpGracefulRestartPathSelectionDeferTimeLimit,
        juniBgpPlatformSupportsNonStopForwarding,
        juniBgpDeviceCanPreserveForwardingState,
        juniBgpLastRestartWasGraceful }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing general management of BGP in a
        Juniper product."
    ::= { juniBgpConfGroups 56 }                                   -- JUNOSe 6.0

juniBgpPeerConfGroup7 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes,
        juniBgpPeerSiteOfOrigin,
        juniBgpPeerLenient,
        juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerPassive,
        juniBgpPeerDynamic,
        juniBgpPeerShouldAdvertiseCapabilityGracefulRestart, 
        juniBgpPeerSentCapabilityGracefulRestart,
        juniBgpPeerReceivedCapabilityGracefulRestart,
        juniBgpPeerGracefulRestartRestartTime,
        juniBgpPeerGracefulRestartStalePathsTime,
        juniBgpPeerSentGracefulRestartRestartState,
        juniBgpPeerReceivedGracefulRestartRestartState,
        juniBgpPeerSentGracefulRestartRestartTime,
        juniBgpPeerReceivedGracefulRestartRestartTime,
        juniBgpPeerTimeUntilGracefulRestartRestartTimerExpires,
        juniBgpPeerTimeUntilGracefulRestartStalePathsTimerExpires }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in 
        a Juniper product. This group was obsoleted when support for BFD was
        added."
    ::= { juniBgpConfGroups 57 }                                   -- JUNOSe 6.0

juniBgpPeerGroupConfGroup7 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes,
        juniBgpPeerGroupSiteOfOrigin,
        juniBgpPeerGroupLenient,
        juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerGroupPassive,
        juniBgpPeerGroupConfiguredPeerType,
        juniBgpPeerGroupAllowAccessListName,
        juniBgpPeerGroupAllowMaxPeers,
        juniBgpPeerGroupCurrentDynamicPeerCount,
        juniBgpPeerGroupHighWaterMarkDynamicPeerCount,
        juniBgpPeerGroupRejectedDynamicPeerCount,
        juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart,
        juniBgpPeerGroupGracefulRestartRestartTime,
        juniBgpPeerGroupGracefulRestartStalePathsTime }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peer groups 
        in a Juniper product. This group was obsoleted when support for BFD was
        added."
    ::= { juniBgpConfGroups 59 }                                   -- JUNOSe 6.0

juniBgpNewRouteConfGroup4 OBJECT-GROUP
    OBJECTS {
        juniBgpNewRouteOriginatorId,
        juniBgpNewRouteAtomicAggregatePresent,
        juniBgpNewRouteMedPresent,
        juniBgpNewRouteLocalPrefPresent,
        juniBgpNewRouteAggregatorPresent,
        juniBgpNewRouteCommunitiesPresent,
        juniBgpNewRouteOriginatorIdPresent,
        juniBgpNewRouteClusterListPresent,
        juniBgpNewRouteWeight,
        juniBgpNewRouteOrigin,
        juniBgpNewRouteASPathSegment,
        juniBgpNewRouteNextHop,
        juniBgpNewRouteMultiExitDisc,
        juniBgpNewRouteLocalPref,
        juniBgpNewRouteAtomicAggregate,
        juniBgpNewRouteAggregatorAS,
        juniBgpNewRouteAggregatorAddress,
        juniBgpNewRouteBestInIpRouteTable,
        juniBgpNewRouteUnknown,
        juniBgpNewRouteExtendedCommunitiesPresent,
        juniBgpNewRouteValid,
        juniBgpNewRouteSuppressedBy,
        juniBgpNewRouteNextHopReachable,
        juniBgpNewRouteSynchronizedWithIgp,
        juniBgpNewRoutePlaceInIpRouteTable,
        juniBgpNewRouteAdvertiseToExternalPeers,
        juniBgpNewRouteAdvertiseToInternalPeers,
        juniBgpNewRouteNextHopMetric,
        juniBgpNewRouteFlapState,
        juniBgpNewRouteFlapFigureOfMerit,
        juniBgpNewRouteFlapRemainingTime,
        juniBgpNewRouteFlapSuppressThreshold,
        juniBgpNewRouteFlapReuseThreshold,
        juniBgpNewRouteFlapMaxHoldDownTime,
        juniBgpNewRouteFlapHalfLifeReachable,
        juniBgpNewRouteFlapHalfLifeUnreachable,
        juniBgpNewRouteCommunityNumber,
        juniBgpNewRouteExtendedCommunityNumber,
        juniBgpNewRouteClusterId,
        juniBgpNewRouteMplsInLabel,
        juniBgpNewRouteMplsOutLabel,
        juniBgpNewRouteLeaked,
        juniBgpNewRouteStale }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP routes in a Juniper
        product."
    ::= { juniBgpConfGroups 60 }                                   -- JUNOSe 6.0

juniBgpAddressFamilyConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes,
        juniBgpAddressFamilyExternalDistance,
        juniBgpAddressFamilyInternalDistance,
        juniBgpAddressFamilyLocalDistance,
        juniBgpAddressFamilyDefaultOriginateRouteMap,
        juniBgpAddressFamilyIpIntfProfileNameForMplsHeads,
        juniBgpAddressFamilyIpIntfProfileNameForMplsTails,
        juniBgpAddressFamilyIpIntfServiceProfileNameForMplsHeads,
        juniBgpAddressFamilyIpIntfServiceProfileNameForMplsTails,
        juniBgpAddressFamilyCheckVpnNextHops,
        juniBgpAddressFamilyPathSelectionIsDeferred,
        juniBgpAddressFamilyPreventBgpRoutesFromBeingPushedToLineCards,
        juniBgpAddressFamilyTimeUntilPathSelectionDeferTimerExpires }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP address families in
        a Juniper product.  This group was obsoleted
        when BGP-created IP dynamic interface profile support has been removed."
    ::= { juniBgpConfGroups 61 }                                   -- JUNOSe 6.0

juniBgpVrfConfGroup7 OBJECT-GROUP
    OBJECTS {
        juniBgpVrfSynchronization,
        juniBgpVrfAutoSummary,
        juniBgpVrfResetConnectionType,
        juniBgpVrfRowStatus,
        juniBgpVrfOperationalState,
        juniBgpVrfAddUnicastRoutesToMulticastView,
        juniBgpVrfMaximumPathsEbgp,
        juniBgpVrfMaximumPathsIbgp,
        juniBgpVrfUnconfiguredAttributes,
        juniBgpVrfMaximumPathsEIbgp }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP-specific VPN
        routing forwarding (VRF) characteristics in a Juniper product."
    ::= { juniBgpConfGroups 62 }                                   -- JUNOSe 7.1

juniBgpPeerAddressFamilyConfGroup7 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyPeerGroup,
        juniBgpPeerAddressFamilyDefaultOriginate,
        juniBgpPeerAddressFamilyNextHopSelf,
        juniBgpPeerAddressFamilyNextHopUnchanged,
        juniBgpPeerAddressFamilySendCommunity,
        juniBgpPeerAddressFamilyDistributeListIn,
        juniBgpPeerAddressFamilyDistributeListOut,
        juniBgpPeerAddressFamilyPrefixListIn,
        juniBgpPeerAddressFamilyPrefixListOut,
        juniBgpPeerAddressFamilyPrefixTreeIn,
        juniBgpPeerAddressFamilyPrefixTreeOut,
        juniBgpPeerAddressFamilyFilterListIn,
        juniBgpPeerAddressFamilyFilterListOut,
        juniBgpPeerAddressFamilyFilterListWeight,
        juniBgpPeerAddressFamilyFilterListWeightValue,
        juniBgpPeerAddressFamilyRouteMapIn,
        juniBgpPeerAddressFamilyRouteMapOut,
        juniBgpPeerAddressFamilyRouteReflectorClient,
        juniBgpPeerAddressFamilyRouteLimitWarn,
        juniBgpPeerAddressFamilyRouteLimitReset,
        juniBgpPeerAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerAddressFamilyRemovePrivateAs,
        juniBgpPeerAddressFamilyUnsuppressMap,
        juniBgpPeerAddressFamilyInboundSoftReconfig,
        juniBgpPeerAddressFamilyResetConnectionType,
        juniBgpPeerAddressFamilyRowStatus,
        juniBgpPeerAddressFamilyAsOverride,
        juniBgpPeerAddressFamilyAllowAsIn,
        juniBgpPeerAddressFamilySendExtendedCommunity,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyAdvertiseCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListOrfReceive,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfSend,
        juniBgpPeerAddressFamilyReceivedCapPrefixListCiscoOrfReceive,
        juniBgpPeerAddressFamilyReceivedOrfEntriesLimit,
        juniBgpPeerAddressFamilyReceivedPrefixListOrfName,
        juniBgpPeerAddressFamilyMaximumPrefixStrict,
        juniBgpPeerAddressFamilyUnconfiguredAttributes,
        juniBgpPeerAddressFamilySendLabel,
        juniBgpPeerAddressFamilyDefaultOriginateRouteMap,
        juniBgpPeerAddressFamilySentCapabilityGracefulRestart,
        juniBgpPeerAddressFamilyReceivedCapabilityGracefulRestart,
        juniBgpPeerAddressFamilySentForwardingStatePreserved,
        juniBgpPeerAddressFamilyReceivedForwardingStatePreserved,
        juniBgpPeerAddressFamilySentEndOfRibMarker,
        juniBgpPeerAddressFamilyReceivedEndOfRibMarker,
        juniBgpPeerAddressFamilyWaitingForEndOfRibBeforeFlushStaleRoutes,
        juniBgpPeerAddressFamilyWaitingForEndOfRibBeforePathSelection }                   
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP peer address
        families in a Juniper product."
    ::= { juniBgpConfGroups 63 }                                   -- JUNOSe 7.1

juniBgpPeerGroupAddressFamilyConfGroup6 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyDefaultOriginate,
        juniBgpPeerGroupAddressFamilyNextHopSelf,
        juniBgpPeerGroupAddressFamilyNextHopUnchanged,
        juniBgpPeerGroupAddressFamilySendCommunity,
        juniBgpPeerGroupAddressFamilyDistributeListIn,
        juniBgpPeerGroupAddressFamilyDistributeListOut,
        juniBgpPeerGroupAddressFamilyPrefixListIn,
        juniBgpPeerGroupAddressFamilyPrefixListOut,
        juniBgpPeerGroupAddressFamilyPrefixTreeIn,
        juniBgpPeerGroupAddressFamilyPrefixTreeOut,
        juniBgpPeerGroupAddressFamilyFilterListIn,
        juniBgpPeerGroupAddressFamilyFilterListOut,
        juniBgpPeerGroupAddressFamilyFilterListWeight,
        juniBgpPeerGroupAddressFamilyFilterListWeightValue,
        juniBgpPeerGroupAddressFamilyRouteMapIn,
        juniBgpPeerGroupAddressFamilyRouteMapOut,
        juniBgpPeerGroupAddressFamilyRouteReflectorClient,
        juniBgpPeerGroupAddressFamilyRouteLimitWarn,
        juniBgpPeerGroupAddressFamilyRouteLimitReset,
        juniBgpPeerGroupAddressFamilyRouteLimitWarnOnly,
        juniBgpPeerGroupAddressFamilyRemovePrivateAs,
        juniBgpPeerGroupAddressFamilyUnsuppressMap,
        juniBgpPeerGroupAddressFamilyInboundSoftReconfig,
        juniBgpPeerGroupAddressFamilyResetConnectionType,
        juniBgpPeerGroupAddressFamilyRowStatus,
        juniBgpPeerGroupAddressFamilyAsOverride,
        juniBgpPeerGroupAddressFamilyAllowAsIn,
        juniBgpPeerGroupAddressFamilySendExtendedCommunity,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListOrfSend,
        juniBgpPeerGroupAddressFamilyAdvertiseCapPrefixListCiscoOrfSend,
        juniBgpPeerGroupAddressFamilyMaximumPrefixStrict,
        juniBgpPeerGroupAddressFamilyUnconfiguredAttributes,
        juniBgpPeerGroupAddressFamilySendLabel,
        juniBgpPeerGroupAddressFamilyDefaultOriginateRouteMap }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP peer group address
        families in a Juniper product."
    ::= { juniBgpConfGroups 64 }                                   -- JUNOSe 7.1

juniBgpAddressFamilyConfGroup7 OBJECT-GROUP
    OBJECTS {
        juniBgpAddressFamilyDefaultOriginate,
        juniBgpAddressFamilyRouteFlapDampening,
        juniBgpAddressFamilyDampeningSuppressThreshold,
        juniBgpAddressFamilyDampeningReuseThreshold,
        juniBgpAddressFamilyDampeningMaxHoldDownTime,
        juniBgpAddressFamilyDampeningHalfLifeReachable,
        juniBgpAddressFamilyDampeningHalfLifeUnreachable,
        juniBgpAddressFamilyDampeningRouteMapName,
        juniBgpAddressFamilyResetConnectionType,
        juniBgpAddressFamilyRowStatus,
        juniBgpAddressFamilyOperationalState,
        juniBgpAddressFamilyUnconfiguredAttributes,
        juniBgpAddressFamilyExternalDistance,
        juniBgpAddressFamilyInternalDistance,
        juniBgpAddressFamilyLocalDistance,
        juniBgpAddressFamilyDefaultOriginateRouteMap,
        juniBgpAddressFamilyCheckVpnNextHops,
        juniBgpAddressFamilyPathSelectionIsDeferred,
        juniBgpAddressFamilyPreventBgpRoutesFromBeingPushedToLineCards,
        juniBgpAddressFamilyTimeUntilPathSelectionDeferTimerExpires }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP address families in
        a Juniper product."
    ::= { juniBgpConfGroups 65 }                                   -- JUNOSe 7.1

juniBgpPeerConfGroup8 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes,
        juniBgpPeerSiteOfOrigin,
        juniBgpPeerLenient,
        juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerPassive,
        juniBgpPeerDynamic,
        juniBgpPeerShouldAdvertiseCapabilityGracefulRestart, 
        juniBgpPeerSentCapabilityGracefulRestart,
        juniBgpPeerReceivedCapabilityGracefulRestart,
        juniBgpPeerGracefulRestartRestartTime,
        juniBgpPeerGracefulRestartStalePathsTime,
        juniBgpPeerSentGracefulRestartRestartState,
        juniBgpPeerReceivedGracefulRestartRestartState,
        juniBgpPeerSentGracefulRestartRestartTime,
        juniBgpPeerReceivedGracefulRestartRestartTime,
        juniBgpPeerTimeUntilGracefulRestartRestartTimerExpires,
        juniBgpPeerTimeUntilGracefulRestartStalePathsTimerExpires,
        juniBgpPeerBfdEnabled,
        juniBgpPeerBfdMinTransmitInterval,
        juniBgpPeerBfdMinReceiveInterval,
        juniBgpPeerBfdMultiplier,
        juniBgpPeerBfdSessionUp,
        juniBgpPeerBfdDetectionTime }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group was obsoleted when support for
        ibgp-singlehop was added."
    ::= { juniBgpConfGroups 66 }                                   -- JUNOSe 7.2

juniBgpPeerGroupConfGroup8 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes,
        juniBgpPeerGroupSiteOfOrigin,
        juniBgpPeerGroupLenient,
        juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerGroupPassive,
        juniBgpPeerGroupConfiguredPeerType,
        juniBgpPeerGroupAllowAccessListName,
        juniBgpPeerGroupAllowMaxPeers,
        juniBgpPeerGroupCurrentDynamicPeerCount,
        juniBgpPeerGroupHighWaterMarkDynamicPeerCount,
        juniBgpPeerGroupRejectedDynamicPeerCount,
        juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart,
        juniBgpPeerGroupGracefulRestartRestartTime,
        juniBgpPeerGroupGracefulRestartStalePathsTime,
        juniBgpPeerGroupBfdEnabled,
        juniBgpPeerGroupBfdMinTransmitInterval,
        juniBgpPeerGroupBfdMinReceiveInterval,
        juniBgpPeerGroupBfdMultiplier }
    STATUS      current
    DESCRIPTION
       "Obsolete collection of objects providing management of BGP peers in a
        Juniper product.  This group was obsoleted when support for
        ibgp-singlehop was added."
    ::= { juniBgpConfGroups 67 }                                   -- JUNOSe 7.2

juniBgpPeerConfGroup9 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAdminStatus,
        juniBgpPeerState,
        juniBgpPeerNegotiatedVersion,
        juniBgpPeerLocalAddress,
        juniBgpPeerLocalAddressMask,
        juniBgpPeerLocalPort,
        juniBgpPeerRemotePort,
        juniBgpPeerInUpdates,
        juniBgpPeerOutUpdates,
        juniBgpPeerInTotalMessages,
        juniBgpPeerOutTotalMessages,
        juniBgpPeerLastErrorCode,
        juniBgpPeerLastResetReason,
        juniBgpPeerFsmEstablishedTransitions,
        juniBgpPeerFsmEstablishedTime,
        juniBgpPeerRetryInterval,
        juniBgpPeerHoldTime,
        juniBgpPeerKeepAliveInterval,
        juniBgpPeerConfigHoldTime,
        juniBgpPeerConfigKeepAliveInterval,
        juniBgpPeerAsOriginationInterval,
        juniBgpPeerAdvertisementInterval,
        juniBgpPeerInUpdateElapsedTime,
        juniBgpPeerDescription,
        juniBgpPeerRemoteIdentifier,
        juniBgpPeerWeight,
        juniBgpPeerEbgpMultihop,
        juniBgpPeerEbgpMultihopTtl,
        juniBgpPeerUpdateSource,
        juniBgpPeerMd5Password,
        juniBgpPeerMaxUpdateSize,
        juniBgpPeerType,
        juniBgpPeerReceivedCapabilitiesOption,
        juniBgpPeerReceivedCapabilityMultiProtocol,
        juniBgpPeerReceivedCapabilityRouteRefresh,
        juniBgpPeerReceivedCapabilityRouteRefreshCisco,
        juniBgpPeerResetConnectionType,
        juniBgpPeerRowStatus,
        juniBgpPeerFourOctetRemoteAsNumber,
        juniBgpPeerFourOctetLocalAsNumber,
        juniBgpPeerReceivedCapabilityFourOctetAsNumbers,
        juniBgpPeerReceivedCapabilityDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilitiesOption,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerSentCapabilitiesOption,
        juniBgpPeerSentCapabilityMultiProtocol,
        juniBgpPeerSentCapabilityRouteRefresh,
        juniBgpPeerSentCapabilityRouteRefreshCisco,
        juniBgpPeerSentCapabilityFourOctetAsNumbers,
        juniBgpPeerSentCapabilityDynamicCapabilityNeg,
        juniBgpPeerReceivedUnsupportedOptionalParameterNotification,
        juniBgpPeerReceivedUnsupportedCapabilityNotification,
        juniBgpPeerUnconfiguredAttributes,
        juniBgpPeerSiteOfOrigin,
        juniBgpPeerLenient,
        juniBgpPeerReceivedCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerSentCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerPassive,
        juniBgpPeerDynamic,
        juniBgpPeerShouldAdvertiseCapabilityGracefulRestart, 
        juniBgpPeerSentCapabilityGracefulRestart,
        juniBgpPeerReceivedCapabilityGracefulRestart,
        juniBgpPeerGracefulRestartRestartTime,
        juniBgpPeerGracefulRestartStalePathsTime,
        juniBgpPeerSentGracefulRestartRestartState,
        juniBgpPeerReceivedGracefulRestartRestartState,
        juniBgpPeerSentGracefulRestartRestartTime,
        juniBgpPeerReceivedGracefulRestartRestartTime,
        juniBgpPeerTimeUntilGracefulRestartRestartTimerExpires,
        juniBgpPeerTimeUntilGracefulRestartStalePathsTimerExpires,
        juniBgpPeerBfdEnabled,
        juniBgpPeerBfdMinTransmitInterval,
        juniBgpPeerBfdMinReceiveInterval,
        juniBgpPeerBfdMultiplier,
        juniBgpPeerBfdSessionUp,
        juniBgpPeerBfdDetectionTime,
        juniBgpPeerIbgpSinglehop }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP peers in a Juniper
        product."
    ::= { juniBgpConfGroups 68 }                                   -- JUNOSe 8.0

juniBgpPeerGroupConfGroup9 OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAdminStatus,
        juniBgpPeerGroupRetryInterval,
        juniBgpPeerGroupConfigHoldTime,
        juniBgpPeerGroupConfigKeepAliveInterval,
        juniBgpPeerGroupAsOriginationInterval,
        juniBgpPeerGroupAdvertisementInterval,
        juniBgpPeerGroupDescription,
        juniBgpPeerGroupWeight,
        juniBgpPeerGroupEbgpMultihop,
        juniBgpPeerGroupEbgpMultihopTtl,
        juniBgpPeerGroupUpdateSource,
        juniBgpPeerGroupMd5Password,
        juniBgpPeerGroupMaxUpdateSize,
        juniBgpPeerGroupResetConnectionType,
        juniBgpPeerGroupRowStatus,
        juniBgpPeerGroupFourOctetRemoteAsNumber,
        juniBgpPeerGroupFourOctetLocalAsNumber,
        juniBgpPeerGroupShouldAdvertiseCapabilitiesOption,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefresh,
        juniBgpPeerGroupShouldAdvertiseCapabilityRouteRefreshCisco,
        juniBgpPeerGroupShouldAdvertiseCapabilityFourOctetAsNumbers,
        juniBgpPeerGroupShouldAdvertiseCapabilityDynamicCapabilityNeg,
        juniBgpPeerGroupUnconfiguredAttributes,
        juniBgpPeerGroupSiteOfOrigin,
        juniBgpPeerGroupLenient,
        juniBgpPeerGroupShouldAdvertiseCapabilityOldDynamicCapabilityNeg,
        juniBgpPeerGroupPassive,
        juniBgpPeerGroupConfiguredPeerType,
        juniBgpPeerGroupAllowAccessListName,
        juniBgpPeerGroupAllowMaxPeers,
        juniBgpPeerGroupCurrentDynamicPeerCount,
        juniBgpPeerGroupHighWaterMarkDynamicPeerCount,
        juniBgpPeerGroupRejectedDynamicPeerCount,
        juniBgpPeerGroupShouldAdvertiseCapabilityGracefulRestart,
        juniBgpPeerGroupGracefulRestartRestartTime,
        juniBgpPeerGroupGracefulRestartStalePathsTime,
        juniBgpPeerGroupBfdEnabled,
        juniBgpPeerGroupBfdMinTransmitInterval,
        juniBgpPeerGroupBfdMinReceiveInterval,
        juniBgpPeerGroupBfdMultiplier,
        juniBgpPeerGroupIbgpSinglehop }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP peer groups in a
        Juniper product."
    ::= { juniBgpConfGroups 69 }                                   -- JUNOSe 8.0

juniBgpPeerAddressFamilyConditionalAdvConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerAddressFamilyConditionalAdvConditionMap,
        juniBgpPeerAddressFamilyConditionalAdvIsExistMap,
        juniBgpPeerAddressFamilyConditionalAdvSequenceNum,
        juniBgpPeerAddressFamilyConditionalAdvStatus,
        juniBgpPeerAddressFamilyConditionalAdvRowStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP Conditional 
        Advertisement for peers in a Juniper product."
    ::= { juniBgpConfGroups 70 }                                   -- JUNOSe 9.0

juniBgpPeerGroupAddressFamilyConditionalAdvConfGroup OBJECT-GROUP
    OBJECTS {
        juniBgpPeerGroupAddressFamilyConditionalAdvConditionMap,
        juniBgpPeerGroupAddressFamilyConditionalAdvIsExistMap,
        juniBgpPeerGroupAddressFamilyConditionalAdvSequenceNum,
        juniBgpPeerGroupAddressFamilyConditionalAdvStatus,
        juniBgpPeerGroupAddressFamilyConditionalAdvRowStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of BGP Conditional 
        Advertisement for peer groups in a Juniper product."
    ::= { juniBgpConfGroups 71 }                                   -- JUNOSe 9.0
END
