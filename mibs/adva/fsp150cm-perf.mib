CM-PERFORMANCE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
             FROM SNMPv2-CONF
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, <PERSON>teger32, Un<PERSON><PERSON>, Counter64
             FROM SNMPv2-SMI
    DateAndTime, TruthValue, VariablePointer, TEXTUAL-CONVENTION
             FROM SNMPv2-TC
    dot3adAggIndex
             FROM IEEE8023-LAG-MIB
    neIndex, shelfIndex, slotIndex
             FROM  CM-ENTITY-MIB
    cmEthernetAccPortIndex, cmEthernetNetPortIndex, cmFlowIndex, 
    cmQosShaperTypeIndex, cmQosShaperIndex,
    cmQosFlowPolicerTypeIndex, cmQosFlowPolicerIndex,
    cmAccPortQosShaperIndex, cmEthernetTrafficPortIndex,
    cmFlowPointIndex,cmQosPolicerV2Index,cmQosShaperV2Index,
    cmOAMFlowPointIndex, cmTrafficPortQosShaperIndex, 
    f3NetPortQosShaperIndex, ocnStmIndex, 
    stsVcPathParentIfIndex, stsVcPathIndex,
    vtVcPathParentIfIndex, vtVcPathIndex,
    e1t1ParentIfIndex, e1t1Index,
    e3t3ParentIfIndex, e3t3Index,
    cmFlowEntry
             FROM  CM-FACILITY-MIB
    f3LagIndex
             FROM F3-LAG-MIB
    fsp150cm
             FROM  ADVA-MIB 
    InterfaceIndex
             FROM IF-MIB
    PerfCounter64, CmPmBinAction, CmPmIntervalType
             FROM  CM-COMMON-MIB
    CounterBasedGauge64
             FROM HCNUM-TC;


cmPerformanceMIB MODULE-IDENTITY
    LAST-UPDATED    "201205180000Z"
    ORGANIZATION    "ADVA Optical Networking"
    CONTACT-INFO
            "        Raghav Trivedi
                     ADVA Optical Networking, Inc.
                Tel: ****** 759-1239
             E-mail: <EMAIL>
             Postal: 2301 N. Greenville Ave. #300
                     Richardson, TX USA 75082"
    DESCRIPTION
            "This module defines the Performance Monitoring MIB definitions 
             used by the F3 (FSP150CM/CC) product lines.  
             Copyright (C) ADVA Optical Networking."
    REVISION        "201205180000Z"
    DESCRIPTION
            "
            Notes from release 201307220000Z,
              (1)Added following objects
                 cmEthernetAccPortStatsIBRMaxRx,
                 cmEthernetAccPortStatsIBRMaxTx,
                 cmEthernetAccPortStatsIBRMinRx,
                 cmEthernetAccPortStatsIBRMinTx,
                 cmEthernetAccPortStatsIBRRx,
                 cmEthernetAccPortStatsIBRTx,
                 cmEthernetAccPortHistoryIBRMaxRx,
                 cmEthernetAccPortHistoryIBRMaxTx,
                 cmEthernetAccPortHistoryIBRMinRx,
                 cmEthernetAccPortHistoryIBRMinTx,
                 cmEthernetAccPortHistoryIBRRx,
                 cmEthernetAccPortHistoryIBRTx,
                 cmEthernetNetPortStatsIBRMaxRx,
                 cmEthernetNetPortStatsIBRMaxTx,
                 cmEthernetNetPortStatsIBRMinRx,
                 cmEthernetNetPortStatsIBRMinTx,
                 cmEthernetNetPortStatsIBRRx,
                 cmEthernetNetPortStatsIBRTx,
                 cmEthernetNetPortHistoryIBRMaxRx,
                 cmEthernetNetPortHistoryIBRMaxTx,
                 cmEthernetNetPortHistoryIBRMinRx,
                 cmEthernetNetPortHistoryIBRMinTx,
                 cmEthernetNetPortHistoryIBRRx,
                 cmEthernetNetPortHistoryIBRTx,
                 cmFlowStatsIBRA2NMax,
                 cmFlowStatsIBRRlA2NMax,
                 cmFlowStatsIBRA2NMin,
                 cmFlowStatsIBRRlA2NMin,
                 cmFlowStatsIBRA2N,
                 cmFlowStatsIBRRlA2N,
                 cmFlowStatsIBRN2AMax,
                 cmFlowStatsIBRRlN2AMax,
                 cmFlowStatsIBRN2AMin,
                 cmFlowStatsIBRRlN2AMin,
                 cmFlowStatsIBRN2A,
                 cmFlowStatsIBRRlN2A,
                 cmFlowHistoryIBRA2NMax,
                 cmFlowHistoryIBRRlA2NMax,
                 cmFlowHistoryIBRA2NMin,
                 cmFlowHistoryIBRRlA2NMin,
                 cmFlowHistoryIBRA2N,
                 cmFlowHistoryIBRRlA2N,
                 cmFlowHistoryIBRN2AMax,
                 cmFlowHistoryIBRRlN2AMax,
                 cmFlowHistoryIBRN2AMin,
                 cmFlowHistoryIBRRlN2AMin,
                 cmFlowHistoryIBRN2A,
                 cmFlowHistoryIBRRlN2A
                 
            Notes from release 201205180000Z,
              (1)Added following L2PT objects 
                 cmEthernetAccPortStatsL2PTRxFramesEncap, cmEthernetAccPortStatsL2PTTxFramesDecap,
                 cmEthernetAccPortHistoryL2PTRxFramesEncap,
                 cmEthernetAccPortHistoryL2PTTxFramesDecap,
                 cmEthernetNetPortStatsL2PTRxFramesEncap,
                 cmEthernetNetPortStatsL2PTTxFramesDecap, 
                 cmEthernetNetPortHistoryL2PTRxFramesEncap,
                 cmEthernetNetPortHistoryL2PTTxFramesDecap
            Notes from release 201108010000Z,
              (1)Post EG-X merge

            Notes from release 201107080000Z,
              (1)Moved CmPmIntervalType to fsp150cm-common.mib

            Notes from release 201105270000Z,
           -Formalized the OBJECT-GROUP definitions

             Notes from release 201101240000Z,
             (1)FMYD is now deprecated from Flow and Policer PM
             (2)ESUP description indicates Undersize Packets
             (3)Added the following MIB objects, 
                cmQosShaperStatsBREDD, cmQosShaperStatsFREDD,
                cmQosShaperHistoryBREDD, cmQosShaperHistoryFREDD,
                cmAccPortQosShaperStatsBREDD, cmAccPortQosShaperStatsFREDD,
                cmAccPortQosShaperHistoryBREDD, cmAccPortQosShaperHistoryFREDD,

             Notes from release 200803190000Z,
             (1)MIB version ready for release FSP150CC GE101 and GE206,
                (a)Following SNMP tables are new,
                    cmQosFlowPolicerStatsTable, cmQosFlowPolicerHistoryTable,
                    cmQosFlowPolicerThresholdTable, 
                    cmAccPortQosShaperStatsTable, cmAccPortQosShaperHistoryTable,
                    cmAccPortQosShaperThresholdTable 
                    
                (b)Following SNMP objects are added,
                    cmEthernetAccPortStatsTemp, cmEthernetAccPortStatsUAS,
                    cmEthernetAccPortHistoryTemp, cmEthernetAccPortHistoryUAS,

                    cmEthernetNetPortStatsTemp, cmEthernetNetPortStatsUAS,
                    cmEthernetNetPortHistoryTemp, cmEthernetNetPortHistoryUAS,

                    cmFlowStatsUAS, cmFlowStatsES, cmFlowStatsSES,
                    cmFlowStatsFMGA2N, cmFlowStatsFMYA2N, cmFlowStatsFMYDA2N,
                    cmFlowStatsFMRDA2N, cmFlowStatsBytesInA2N,
                    cmFlowStatsBytesOutA2N, cmFlowStatsFMGN2A,
                    cmFlowStatsFMYN2A, cmFlowStatsFMYDN2A,
                    cmFlowStatsFMRDN2A, cmFlowStatsBytesInN2A,
                    cmFlowStatsBytesOutN2A, cmFlowStatsFTDA2N,

                    cmFlowHistoryUAS, cmFlowHistoryES, cmFlowHistorySES,
                    cmFlowHistoryFMGA2N, cmFlowHistoryFMYA2N, 
                    cmFlowHistoryFMYDA2N,
                    cmFlowHistoryFMRDA2N, cmFlowHistoryBytesInA2N,
                    cmFlowHistoryBytesOutA2N, cmFlowHistoryFMGN2A,
                    cmFlowHistoryFMYN2A, cmFlowHistoryFMYDN2A,
                    cmFlowHistoryFMRDN2A, cmFlowHistoryBytesInN2A,
                    cmFlowHistoryBytesOutN2A, cmFlowHistoryFTDA2N,

                 (c)Following notifications are added,
                    cmQosFlowPolicerThresholdCrossingAlert,
                    cmAccPortQosShaperThresholdCrossingAlert
                 (d)Following notifications are added for CM5.1
                    cmEthernetTrafficPortThresholdCrossingAlert
                    cmFlowPointThresholdCrossingAlert
                    cmQosPolicerV2ThresholdCrossingAlert
                    cmQosShaperV2ThresholdCrossingAlert
             Notes from release 200803030000Z,
             (1)MIB version ready for release FSP150CM 3.1." 
    ::= {fsp150cm 5}    

-- 
-- OID definitions
-- 
cmPerfObjects        OBJECT IDENTIFIER ::= {cmPerformanceMIB 1}
cmPerfNotifications  OBJECT IDENTIFIER ::= {cmPerformanceMIB 2}
cmPerfConformance    OBJECT IDENTIFIER ::= {cmPerformanceMIB 3}

-- 
-- Textual Conventions 
-- 
-- CmPmIntervalType now in fsp150cm-common.mib 

--
-- Ethernet Access Port Current Statistics Table
--
cmEthernetAccPortStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetAccPortStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Ethernet Access Port related statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 1 }

cmEthernetAccPortStatsEntry OBJECT-TYPE
    SYNTAX      CmEthernetAccPortStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetAccPortStatsTable.
             Entries exist in this table for each Ethernet interface/port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex, 
            cmEthernetAccPortStatsIndex }
    ::= { cmEthernetAccPortStatsTable 1 }

CmEthernetAccPortStatsEntry ::= SEQUENCE {
    cmEthernetAccPortStatsIndex                 Integer32,
    cmEthernetAccPortStatsIntervalType          CmPmIntervalType,
    cmEthernetAccPortStatsValid                 TruthValue,
    cmEthernetAccPortStatsAction                CmPmBinAction,
    cmEthernetAccPortStatsESBF                  PerfCounter64,
    cmEthernetAccPortStatsESBP                  PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESBS                  PerfCounter64,
    cmEthernetAccPortStatsESC                   PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESCAE                 PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESDE                  PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESF                   PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESFS                  PerfCounter64,
    cmEthernetAccPortStatsESJ                   PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESMF                  PerfCounter64,
    cmEthernetAccPortStatsESMP                  PerfCounter64,--ES,EH
    cmEthernetAccPortStatsESO                   PerfCounter64,--ES,EH,HC
    cmEthernetAccPortStatsESOF                  PerfCounter64,
    cmEthernetAccPortStatsESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetAccPortStatsESP                   PerfCounter64,--ES,EH,HC
    cmEthernetAccPortStatsESP64                 PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP65                 PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP128                PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP256                PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP512                PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP1024               PerfCounter64,--ES,   HC
    cmEthernetAccPortStatsESP1519               PerfCounter64,
    cmEthernetAccPortStatsESUF                  PerfCounter64,
    cmEthernetAccPortStatsESUP                  PerfCounter64,--ES,EH
    cmEthernetAccPortStatsL2CPFD                PerfCounter64,
    cmEthernetAccPortStatsL2CPFP                PerfCounter64,
    cmEthernetAccPortStatsLES                   PerfCounter64,
    cmEthernetAccPortStatsLBC                   Integer32,
    cmEthernetAccPortStatsOPT                   Integer32,
    cmEthernetAccPortStatsOPR                   Integer32,
    cmEthernetAccPortStatsAUFD                  PerfCounter64,
    cmEthernetAccPortStatsAPFD                  PerfCounter64,
    cmEthernetAccPortStatsABRRx                 PerfCounter64,
    cmEthernetAccPortStatsABRTx                 PerfCounter64,
    cmEthernetAccPortStatsTemp                  Integer32,
    cmEthernetAccPortStatsUAS                   PerfCounter64,
    cmEthernetAccPortStatsL2PTRxFramesEncap     PerfCounter64,
    cmEthernetAccPortStatsL2PTTxFramesDecap     PerfCounter64,
    cmEthernetAccPortStatsIBRMaxRx              PerfCounter64,
    cmEthernetAccPortStatsIBRMaxTx              PerfCounter64,
    cmEthernetAccPortStatsIBRMinRx              PerfCounter64,
    cmEthernetAccPortStatsIBRMinTx              PerfCounter64,
    cmEthernetAccPortStatsIBRRx                 PerfCounter64,
    cmEthernetAccPortStatsIBRTx                 PerfCounter64,
    cmEthernetAccPortStatsFmcd                  PerfCounter64,
    cmEthernetAccPortStatsFbcd                  PerfCounter64,
    cmEthernetAccPortStatsAclDropNoMatch        PerfCounter64,
    cmEthernetAccPortStatsAclFwd2Cpu            PerfCounter64,
    cmEthernetAccPortStatsDhcpDropNoAssocIf     PerfCounter64,
    cmEthernetAccPortStatsLkupFails             PerfCounter64
}

cmEthernetAccPortStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..4)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Access Port statistics entry. 
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmEthernetAccPortStatsEntry 1 }

cmEthernetAccPortStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmEthernetAccPortStatsEntry 2 }

cmEthernetAccPortStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetAccPortStatsEntry 3 }

cmEthernetAccPortStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetAccPortStatsEntry 4 }

cmEthernetAccPortStatsESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent in N2A direction."
     ::= { cmEthernetAccPortStatsEntry 5 }

cmEthernetAccPortStatsESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received in A2N direction."
     ::= { cmEthernetAccPortStatsEntry 6 }

cmEthernetAccPortStatsESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent in N2A direction."
     ::= { cmEthernetAccPortStatsEntry 7 }

cmEthernetAccPortStatsESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected in A2N direction."
     ::= { cmEthernetAccPortStatsEntry 8 }

cmEthernetAccPortStatsESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 9 }

cmEthernetAccPortStatsESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetAccPortStatsEntry 10 }

cmEthernetAccPortStatsESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 11 }

cmEthernetAccPortStatsESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent in the N2A direction."
     ::= { cmEthernetAccPortStatsEntry 12 }

cmEthernetAccPortStatsESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 13 }

cmEthernetAccPortStatsESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent in the N2A direction."
     ::= { cmEthernetAccPortStatsEntry 14 }

cmEthernetAccPortStatsESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 15 }

cmEthernetAccPortStatsESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 16 }

cmEthernetAccPortStatsESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames detected and dropped in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 17 }

cmEthernetAccPortStatsESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 18 }

cmEthernetAccPortStatsESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 19 }

cmEthernetAccPortStatsESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 20 }

cmEthernetAccPortStatsESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 21 }

cmEthernetAccPortStatsESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 22 }

cmEthernetAccPortStatsESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 23 }

cmEthernetAccPortStatsESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 24 }

cmEthernetAccPortStatsESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 25 }

cmEthernetAccPortStatsESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 26 }

cmEthernetAccPortStatsESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent in the N2A direction."
     ::= { cmEthernetAccPortStatsEntry 27 }

cmEthernetAccPortStatsESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Undersize Packets received in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 28 }

cmEthernetAccPortStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 29 }

cmEthernetAccPortStatsL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 30 }

cmEthernetAccPortStatsLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected in the A2N direction.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetAccPortStatsEntry 31 }

cmEthernetAccPortStatsLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortStatsEntry 32 }

cmEthernetAccPortStatsOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortStatsEntry 33 }

cmEthernetAccPortStatsOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortStatsEntry 34 }


cmEthernetAccPortStatsAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD) in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 35 }

cmEthernetAccPortStatsAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD) in the A2N direction."
     ::= { cmEthernetAccPortStatsEntry 36 }

cmEthernetAccPortStatsABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmEthernetAccPortStatsEntry 37 }

cmEthernetAccPortStatsABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmEthernetAccPortStatsEntry 38 }

cmEthernetAccPortStatsTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetAccPortStatsEntry 39 }

cmEthernetAccPortStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Unavailable Seconds on the Port."
     ::= { cmEthernetAccPortStatsEntry 40 }

cmEthernetAccPortStatsL2PTRxFramesEncap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Recevied Frames Encapsulated."
     ::= { cmEthernetAccPortStatsEntry 41 }

cmEthernetAccPortStatsL2PTTxFramesDecap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Transmitted Frames De-encapsulated."
     ::= { cmEthernetAccPortStatsEntry 42 }

cmEthernetAccPortStatsIBRMaxRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits received on a port in any 1-sec with the PM interval."
     ::= { cmEthernetAccPortStatsEntry 43 }
     
cmEthernetAccPortStatsIBRMaxTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits transmitted on a port in any 1-sec with the PM interval."
     ::= { cmEthernetAccPortStatsEntry 44 }

cmEthernetAccPortStatsIBRMinRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits received on a port in any 1-sec with the PM interval"
     ::= { cmEthernetAccPortStatsEntry 45 }

cmEthernetAccPortStatsIBRMinTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits transmitted on a port in any 1-sec with the PM interval"
     ::= { cmEthernetAccPortStatsEntry 46 }

cmEthernetAccPortStatsIBRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits received in the most recent 1-sec"
     ::= { cmEthernetAccPortStatsEntry 47 }
     
cmEthernetAccPortStatsIBRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits transmitted in the most recent 1-sec"
     ::= { cmEthernetAccPortStatsEntry 48 }

cmEthernetAccPortStatsFmcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame of multicast drop."
     ::= { cmEthernetAccPortStatsEntry 49 }

cmEthernetAccPortStatsFbcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame of broadcast drop."
     ::= { cmEthernetAccPortStatsEntry 50 }

cmEthernetAccPortStatsAclDropNoMatch OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 control protocol frames dropped due to no acl rule match."
     ::= { cmEthernetAccPortStatsEntry 51 }

cmEthernetAccPortStatsAclFwd2Cpu OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 Control protocol frames foward to cpu."
     ::= { cmEthernetAccPortStatsEntry 52 }

cmEthernetAccPortStatsDhcpDropNoAssocIf OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of DHCP packets dropped by CPU due to no associated Traffic IP Interface found."
     ::= { cmEthernetAccPortStatsEntry 53 }

cmEthernetAccPortStatsLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Membership lookup fail counts."
     ::= { cmEthernetAccPortStatsEntry 54 }

--
-- Ethernet Access Port History Table
--
cmEthernetAccPortHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetAccPortHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Ethernet Access Port related statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 2 }

cmEthernetAccPortHistoryEntry OBJECT-TYPE
    SYNTAX      CmEthernetAccPortHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetAccPortHistoryTable.
             Entries exist in this table for each Ethernet Access port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex, 
            cmEthernetAccPortStatsIndex, cmEthernetAccPortHistoryIndex }
    ::= { cmEthernetAccPortHistoryTable 1 }

CmEthernetAccPortHistoryEntry ::= SEQUENCE {
    cmEthernetAccPortHistoryIndex                 Integer32,
    cmEthernetAccPortHistoryTime                  DateAndTime,
    cmEthernetAccPortHistoryValid                 TruthValue,
    cmEthernetAccPortHistoryAction                CmPmBinAction,
    cmEthernetAccPortHistoryESBF                  PerfCounter64,
    cmEthernetAccPortHistoryESBP                  PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESBS                  PerfCounter64,
    cmEthernetAccPortHistoryESC                   PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESCAE                 PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESDE                  PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESF                   PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESFS                  PerfCounter64,
    cmEthernetAccPortHistoryESJ                   PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESMF                  PerfCounter64,
    cmEthernetAccPortHistoryESMP                  PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryESO                   PerfCounter64,--ES,EH,HC
    cmEthernetAccPortHistoryESOF                  PerfCounter64,
    cmEthernetAccPortHistoryESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetAccPortHistoryESP                   PerfCounter64,--ES,EH,HC
    cmEthernetAccPortHistoryESP64                 PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP65                 PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP128                PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP256                PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP512                PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP1024               PerfCounter64,--ES,   HC
    cmEthernetAccPortHistoryESP1519               PerfCounter64,
    cmEthernetAccPortHistoryESUF                  PerfCounter64,
    cmEthernetAccPortHistoryESUP                  PerfCounter64,--ES,EH
    cmEthernetAccPortHistoryL2CPFD                PerfCounter64,
    cmEthernetAccPortHistoryL2CPFP                PerfCounter64,
    cmEthernetAccPortHistoryLES                   PerfCounter64,
    cmEthernetAccPortHistoryLBC                   Integer32,
    cmEthernetAccPortHistoryOPT                   Integer32,
    cmEthernetAccPortHistoryOPR                   Integer32,
    cmEthernetAccPortHistoryAUFD                  PerfCounter64,
    cmEthernetAccPortHistoryAPFD                  PerfCounter64,
    cmEthernetAccPortHistoryABRRx                 PerfCounter64,
    cmEthernetAccPortHistoryABRTx                 PerfCounter64,
    cmEthernetAccPortHistoryTemp                  Integer32,
    cmEthernetAccPortHistoryUAS                   PerfCounter64,
    cmEthernetAccPortHistoryL2PTRxFramesEncap     PerfCounter64,
    cmEthernetAccPortHistoryL2PTTxFramesDecap     PerfCounter64,
    cmEthernetAccPortHistoryIBRMaxRx              PerfCounter64,
    cmEthernetAccPortHistoryIBRMaxTx              PerfCounter64,
    cmEthernetAccPortHistoryIBRMinRx              PerfCounter64,
    cmEthernetAccPortHistoryIBRMinTx              PerfCounter64,
    cmEthernetAccPortHistoryIBRRx                 PerfCounter64,
    cmEthernetAccPortHistoryIBRTx                 PerfCounter64,
    cmEthernetAccPortHistoryFmcd                  PerfCounter64,
    cmEthernetAccPortHistoryFbcd                  PerfCounter64,
    cmEthernetAccPortHistoryAclDropNoMatch        PerfCounter64,
    cmEthernetAccPortHistoryAclFwd2Cpu            PerfCounter64,
    cmEthernetAccPortHistoryDhcpDropNoAssocIf     PerfCounter64,
    cmEthernetAccPortHistoryLkupFails             PerfCounter64
}

cmEthernetAccPortHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Access Port statistics entry."
    ::= { cmEthernetAccPortHistoryEntry 1 }

cmEthernetAccPortHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmEthernetAccPortHistoryEntry 2 }

cmEthernetAccPortHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetAccPortHistoryEntry 3 }

cmEthernetAccPortHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetAccPortHistoryEntry 4 }

cmEthernetAccPortHistoryESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent in N2A direction."
     ::= { cmEthernetAccPortHistoryEntry 5 }

cmEthernetAccPortHistoryESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received in A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 6 }

cmEthernetAccPortHistoryESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent in N2A direction."
     ::= { cmEthernetAccPortHistoryEntry 7 }

cmEthernetAccPortHistoryESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected in A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 8 }

cmEthernetAccPortHistoryESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 9 }

cmEthernetAccPortHistoryESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetAccPortHistoryEntry 10 }

cmEthernetAccPortHistoryESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 11 }

cmEthernetAccPortHistoryESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent in the N2A direction."
     ::= { cmEthernetAccPortHistoryEntry 12 }

cmEthernetAccPortHistoryESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 13 }

cmEthernetAccPortHistoryESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent in the N2A direction."
     ::= { cmEthernetAccPortHistoryEntry 14 }

cmEthernetAccPortHistoryESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 15 }

cmEthernetAccPortHistoryESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 16 }

cmEthernetAccPortHistoryESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames detected and dropped in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 17 }

cmEthernetAccPortHistoryESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 18 }

cmEthernetAccPortHistoryESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 19 }

cmEthernetAccPortHistoryESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 20 }

cmEthernetAccPortHistoryESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 21 }

cmEthernetAccPortHistoryESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 22 }

cmEthernetAccPortHistoryESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 23 }

cmEthernetAccPortHistoryESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 24 }

cmEthernetAccPortHistoryESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 25 }

cmEthernetAccPortHistoryESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 26 }

cmEthernetAccPortHistoryESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent in the N2A direction."
     ::= { cmEthernetAccPortHistoryEntry 27 }

cmEthernetAccPortHistoryESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Undersize Packets received in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 28 }

cmEthernetAccPortHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 29 }

cmEthernetAccPortHistoryL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 30 }

cmEthernetAccPortHistoryLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected in the A2N direction.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetAccPortHistoryEntry 31 }

cmEthernetAccPortHistoryLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortHistoryEntry 32 }

cmEthernetAccPortHistoryOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortHistoryEntry 33 }

cmEthernetAccPortHistoryOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Access Port is fiber."
     ::= { cmEthernetAccPortHistoryEntry 34 }


cmEthernetAccPortHistoryAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD) in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 35 }

cmEthernetAccPortHistoryAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD) in the A2N direction."
     ::= { cmEthernetAccPortHistoryEntry 36 }

cmEthernetAccPortHistoryABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmEthernetAccPortHistoryEntry 37 }

cmEthernetAccPortHistoryABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmEthernetAccPortHistoryEntry 38 }

cmEthernetAccPortHistoryTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetAccPortHistoryEntry 39 }

cmEthernetAccPortHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Unavailable Seconds on the Port."
     ::= { cmEthernetAccPortHistoryEntry 40 }

cmEthernetAccPortHistoryL2PTRxFramesEncap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Recevied Frames Encapsulated."
     ::= { cmEthernetAccPortHistoryEntry 41 }

cmEthernetAccPortHistoryL2PTTxFramesDecap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Transmitted Frames De-encapsulated."
     ::= { cmEthernetAccPortHistoryEntry 42 }

cmEthernetAccPortHistoryIBRMaxRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits received on a port in any 1-sec with the PM interval."
     ::= { cmEthernetAccPortHistoryEntry 43 }
     
cmEthernetAccPortHistoryIBRMaxTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits transmitted on a port in any 1-sec with the PM interval."
     ::= { cmEthernetAccPortHistoryEntry 44 }

cmEthernetAccPortHistoryIBRMinRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits received on a port in any 1-sec with the PM interval"
     ::= { cmEthernetAccPortHistoryEntry 45 }

cmEthernetAccPortHistoryIBRMinTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits transmitted on a port in any 1-sec with the PM interval"
     ::= { cmEthernetAccPortHistoryEntry 46 }

cmEthernetAccPortHistoryIBRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits received in the most recent 1-sec"
     ::= { cmEthernetAccPortHistoryEntry 47 }
     
cmEthernetAccPortHistoryIBRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits transmitted in the most recent 1-sec"
     ::= { cmEthernetAccPortHistoryEntry 48 }
     
cmEthernetAccPortHistoryFmcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of multicast drop"
     ::= { cmEthernetAccPortHistoryEntry 49 }

cmEthernetAccPortHistoryFbcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of broadcast drop."
     ::= { cmEthernetAccPortHistoryEntry 50 }

cmEthernetAccPortHistoryAclDropNoMatch OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 control protocol frames dropped due to acl no match."
     ::= { cmEthernetAccPortHistoryEntry 51 }

cmEthernetAccPortHistoryAclFwd2Cpu OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 Control protocol frames foward to cpu."
     ::= { cmEthernetAccPortHistoryEntry 52 }

cmEthernetAccPortHistoryDhcpDropNoAssocIf OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of DHCP packets dropped by CPU due to no associated Traffic IP Interface found."
     ::= { cmEthernetAccPortHistoryEntry 53 }

cmEthernetAccPortHistoryLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Membership lookup fail counts."
     ::= { cmEthernetAccPortHistoryEntry 54 }

--
-- Ethernet Access Port Threshold Table
--
cmEthernetAccPortThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetAccPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Ethernet
             Access Port Thresholds."
    ::= { cmPerfObjects 3 }

cmEthernetAccPortThresholdEntry OBJECT-TYPE
    SYNTAX      CmEthernetAccPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetAccPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex, 
            cmEthernetAccPortStatsIndex, cmEthernetAccPortThresholdIndex }
    ::= { cmEthernetAccPortThresholdTable 1 }

CmEthernetAccPortThresholdEntry ::= SEQUENCE {
    cmEthernetAccPortThresholdIndex       Integer32,
    cmEthernetAccPortThresholdInterval    CmPmIntervalType,
    cmEthernetAccPortThresholdVariable    VariablePointer,
    cmEthernetAccPortThresholdValueLo     Unsigned32,
    cmEthernetAccPortThresholdValueHi     Unsigned32,
    cmEthernetAccPortThresholdMonValue    Counter64
}

cmEthernetAccPortThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmEthernetAccPortThresholdTable."
    ::= { cmEthernetAccPortThresholdEntry 1 }

cmEthernetAccPortThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmEthernetAccPortThresholdEntry 2 }

cmEthernetAccPortThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmEthernetAccPortThresholdEntry 3 }

cmEthernetAccPortThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value.  
         Please note that in case of 
         cmEthernetAccPortStatsOPR, cmEthernetAccPortStatsOPT and cmEthernetAccPortStatsTEMP, 
         the values are 2's complement, since these could have negative values."
    ::= { cmEthernetAccPortThresholdEntry 4 }

cmEthernetAccPortThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value.  Please note that in case of 
         cmEthernetAccPortStatsOPR, cmEthernetAccPortStatsOPT and cmEthernetAccPortStatsTEMP, 
         the values are 2's complement, since these could have negative values."
    ::= { cmEthernetAccPortThresholdEntry 5 }

cmEthernetAccPortThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmEthernetAccPortThresholdVariable.
         Please note that in case of cmEthernetAccPortStatsOPR, cmEthernetAccPortStatsOPT 
         and cmEthernetAccPortStatsTEMP, the values are 2's complement, since these could be 
         negative values."
    ::= { cmEthernetAccPortThresholdEntry 6 }

--
-- Ethernet Access Port Threshold Variance Table
--
cmEthernetAccPortThresholdVarTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetAccPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Ethernet
             Access Port Threshold variances."
    ::= { cmPerfObjects 4 }

cmEthernetAccPortThresholdVarEntry OBJECT-TYPE
    SYNTAX      CmEthernetAccPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetAccPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex, 
            cmEthernetAccPortStatsIndex}
    ::= { cmEthernetAccPortThresholdVarTable 1 }

CmEthernetAccPortThresholdVarEntry ::= SEQUENCE {
    cmEthernetAccPortThresholdVarOprVariance    Integer32,
    cmEthernetAccPortThresholdVarOptVariance    Integer32
}

cmEthernetAccPortThresholdVarOprVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power received (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetAccPortThresholdVarEntry 1 }

cmEthernetAccPortThresholdVarOptVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power transmitted (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetAccPortThresholdVarEntry 2 }


--
-- Ethernet Network Port Current Statistics Table
--
cmEthernetNetPortStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetNetPortStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Ethernet Network Port related statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 5 }

cmEthernetNetPortStatsEntry OBJECT-TYPE
    SYNTAX      CmEthernetNetPortStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetNetPortStatsTable.
             Entries exist in this table for each Ethernet interface/port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex, 
            cmEthernetNetPortStatsIndex }
    ::= { cmEthernetNetPortStatsTable 1 }

CmEthernetNetPortStatsEntry ::= SEQUENCE {
    cmEthernetNetPortStatsIndex                 Integer32,
    cmEthernetNetPortStatsIntervalType          CmPmIntervalType,
    cmEthernetNetPortStatsValid                 TruthValue,
    cmEthernetNetPortStatsAction                CmPmBinAction,
    cmEthernetNetPortStatsESBF                  PerfCounter64,
    cmEthernetNetPortStatsESBP                  PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESBS                  PerfCounter64,
    cmEthernetNetPortStatsESC                   PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESCAE                 PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESDE                  PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESF                   PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESFS                  PerfCounter64,
    cmEthernetNetPortStatsESJ                   PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESMF                  PerfCounter64,
    cmEthernetNetPortStatsESMP                  PerfCounter64,--ES,EH
    cmEthernetNetPortStatsESO                   PerfCounter64,--ES,EH,HC
    cmEthernetNetPortStatsESOF                  PerfCounter64,
    cmEthernetNetPortStatsESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetNetPortStatsESP                   PerfCounter64,--ES,EH,HC
    cmEthernetNetPortStatsESP64                 PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP65                 PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP128                PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP256                PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP512                PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP1024               PerfCounter64,--ES,   HC
    cmEthernetNetPortStatsESP1519               PerfCounter64,
    cmEthernetNetPortStatsESUF                  PerfCounter64,
    cmEthernetNetPortStatsESUP                  PerfCounter64,--ES,EH
    cmEthernetNetPortStatsL2CPFD                PerfCounter64,
    cmEthernetNetPortStatsL2CPFP                PerfCounter64,
    cmEthernetNetPortStatsLES                   PerfCounter64,
    cmEthernetNetPortStatsLBC                   Integer32,
    cmEthernetNetPortStatsOPT                   Integer32,
    cmEthernetNetPortStatsOPR                   Integer32,
    cmEthernetNetPortStatsAUFD                  PerfCounter64,
    cmEthernetNetPortStatsAPFD                  PerfCounter64,
    cmEthernetNetPortStatsABRRx                 PerfCounter64,
    cmEthernetNetPortStatsABRTx                 PerfCounter64,
    cmEthernetNetPortStatsPSC                   PerfCounter64,
    cmEthernetNetPortStatsTemp                  Integer32,
    cmEthernetNetPortStatsUAS                   PerfCounter64,
    cmEthernetNetPortStatsL2PTRxFramesEncap     PerfCounter64,
    cmEthernetNetPortStatsL2PTTxFramesDecap     PerfCounter64,
    cmEthernetNetPortStatsIBRMaxRx              PerfCounter64,
    cmEthernetNetPortStatsIBRMaxTx              PerfCounter64,
    cmEthernetNetPortStatsIBRMinRx              PerfCounter64,
    cmEthernetNetPortStatsIBRMinTx              PerfCounter64,
    cmEthernetNetPortStatsIBRRx                 PerfCounter64,
    cmEthernetNetPortStatsIBRTx                 PerfCounter64,
    cmEthernetNetPortStatsFmcd                  PerfCounter64,
    cmEthernetNetPortStatsFbcd                  PerfCounter64,
    cmEthernetNetPortStatsAclDropNoMatch        PerfCounter64,
    cmEthernetNetPortStatsAclFwd2Cpu            PerfCounter64,
    cmEthernetNetPortStatsDhcpDropNoAssocIf     PerfCounter64,
    cmEthernetNetPortStatsLkupFails             PerfCounter64
}

cmEthernetNetPortStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Network Port statistics entry.
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmEthernetNetPortStatsEntry 1 }

cmEthernetNetPortStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmEthernetNetPortStatsEntry 2 }

cmEthernetNetPortStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetNetPortStatsEntry 3 }

cmEthernetNetPortStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetNetPortStatsEntry 4 }

cmEthernetNetPortStatsESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent in A2N direction."
     ::= { cmEthernetNetPortStatsEntry 5 }

cmEthernetNetPortStatsESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received in N2A direction."
     ::= { cmEthernetNetPortStatsEntry 6 }

cmEthernetNetPortStatsESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent in A2N direction."
     ::= { cmEthernetNetPortStatsEntry 7 }

cmEthernetNetPortStatsESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected in N2A direction."
     ::= { cmEthernetNetPortStatsEntry 8 }

cmEthernetNetPortStatsESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 9 }

cmEthernetNetPortStatsESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetNetPortStatsEntry 10 }

cmEthernetNetPortStatsESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 11 }

cmEthernetNetPortStatsESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent in the A2N direction."
     ::= { cmEthernetNetPortStatsEntry 12 }

cmEthernetNetPortStatsESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 13 }

cmEthernetNetPortStatsESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent in the A2N direction."
     ::= { cmEthernetNetPortStatsEntry 14 }

cmEthernetNetPortStatsESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 15 }

cmEthernetNetPortStatsESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 16 }

cmEthernetNetPortStatsESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames detected and dropped in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 17 }

cmEthernetNetPortStatsESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 18 }

cmEthernetNetPortStatsESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 19 }

cmEthernetNetPortStatsESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 20 }

cmEthernetNetPortStatsESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 21 }

cmEthernetNetPortStatsESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 22 }

cmEthernetNetPortStatsESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 23 }

cmEthernetNetPortStatsESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 24 }

cmEthernetNetPortStatsESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 25 }

cmEthernetNetPortStatsESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 26 }

cmEthernetNetPortStatsESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent in the A2N direction."
     ::= { cmEthernetNetPortStatsEntry 27 }

cmEthernetNetPortStatsESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Undersize Packets received in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 28 }

cmEthernetNetPortStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 29 }

cmEthernetNetPortStatsL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed in the N2A direction."
     ::= { cmEthernetNetPortStatsEntry 30 }

cmEthernetNetPortStatsLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected in the N2A direction.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetNetPortStatsEntry 31 }

cmEthernetNetPortStatsLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortStatsEntry 32 }

cmEthernetNetPortStatsOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortStatsEntry 33 }

cmEthernetNetPortStatsOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortStatsEntry 34 }


cmEthernetNetPortStatsAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD) in the N2A direction. This counter is not applicable
          for GE206."
     ::= { cmEthernetNetPortStatsEntry 35 }

cmEthernetNetPortStatsAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD) in the N2A direction. This counter is not applicable
          for GE206."
     ::= { cmEthernetNetPortStatsEntry 36 }

cmEthernetNetPortStatsABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmEthernetNetPortStatsEntry 37 }

cmEthernetNetPortStatsABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmEthernetNetPortStatsEntry 38 }

cmEthernetNetPortStatsPSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The Protection Switch Counts on the port ." 
     ::= { cmEthernetNetPortStatsEntry 39 }

cmEthernetNetPortStatsTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetNetPortStatsEntry 40 }

cmEthernetNetPortStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Unavailable Seconds on the Port."
     ::= { cmEthernetNetPortStatsEntry 41 }

cmEthernetNetPortStatsL2PTRxFramesEncap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Recevied Frames Encapsulated."
     ::= { cmEthernetNetPortStatsEntry 42 }

cmEthernetNetPortStatsL2PTTxFramesDecap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Transmitted Frames De-encapsulated."
     ::= { cmEthernetNetPortStatsEntry 43 }

cmEthernetNetPortStatsIBRMaxRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits received on a port in any 1-sec with the PM interval."
     ::= { cmEthernetNetPortStatsEntry 44 }
     
cmEthernetNetPortStatsIBRMaxTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits transmitted on a port in any 1-sec with the PM interval."
     ::= { cmEthernetNetPortStatsEntry 45 }

cmEthernetNetPortStatsIBRMinRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits received on a port in any 1-sec with the PM interval"
     ::= { cmEthernetNetPortStatsEntry 46 }

cmEthernetNetPortStatsIBRMinTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits transmitted on a port in any 1-sec with the PM interval"
     ::= { cmEthernetNetPortStatsEntry 47 }

cmEthernetNetPortStatsIBRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits received in the most recent 1-sec"
     ::= { cmEthernetNetPortStatsEntry 48 }

cmEthernetNetPortStatsIBRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits transmitted in the most recent 1-sec"
     ::= { cmEthernetNetPortStatsEntry 49 }

cmEthernetNetPortStatsFmcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of multicast drop."
     ::= { cmEthernetNetPortStatsEntry 50 }

cmEthernetNetPortStatsFbcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of broadcast drop"
     ::= { cmEthernetNetPortStatsEntry 51 }

cmEthernetNetPortStatsAclDropNoMatch OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 control protocol frames dropped due to acl no match."
     ::= { cmEthernetNetPortStatsEntry 52 }

cmEthernetNetPortStatsAclFwd2Cpu OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 Control protocol frames foward to cpu."
     ::= { cmEthernetNetPortStatsEntry 53 }

cmEthernetNetPortStatsDhcpDropNoAssocIf OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of DHCP packets dropped by CPU due to no associated Traffic IP Interface found."
     ::= { cmEthernetNetPortStatsEntry 54 }

cmEthernetNetPortStatsLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Membership lookup fail counts."
     ::= { cmEthernetNetPortStatsEntry 55 }

--
-- Ethernet Network Port History Table
--
cmEthernetNetPortHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetNetPortHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Ethernet Network Port related statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 6 }

cmEthernetNetPortHistoryEntry OBJECT-TYPE
    SYNTAX      CmEthernetNetPortHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetNetPortHistoryTable.
             Entries exist in this table for each Ethernet Network port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex, 
            cmEthernetNetPortStatsIndex, cmEthernetNetPortHistoryIndex }
    ::= { cmEthernetNetPortHistoryTable 1 }

CmEthernetNetPortHistoryEntry ::= SEQUENCE {
    cmEthernetNetPortHistoryIndex                 Integer32,
    cmEthernetNetPortHistoryTime                  DateAndTime,
    cmEthernetNetPortHistoryValid                 TruthValue,
    cmEthernetNetPortHistoryAction                CmPmBinAction,
    cmEthernetNetPortHistoryESBF                  PerfCounter64,
    cmEthernetNetPortHistoryESBP                  PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESBS                  PerfCounter64,
    cmEthernetNetPortHistoryESC                   PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESCAE                 PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESDE                  PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESF                   PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESFS                  PerfCounter64,
    cmEthernetNetPortHistoryESJ                   PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESMF                  PerfCounter64,
    cmEthernetNetPortHistoryESMP                  PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryESO                   PerfCounter64,--ES,EH,HC
    cmEthernetNetPortHistoryESOF                  PerfCounter64,
    cmEthernetNetPortHistoryESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetNetPortHistoryESP                   PerfCounter64,--ES,EH,HC
    cmEthernetNetPortHistoryESP64                 PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP65                 PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP128                PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP256                PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP512                PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP1024               PerfCounter64,--ES,   HC
    cmEthernetNetPortHistoryESP1519               PerfCounter64,
    cmEthernetNetPortHistoryESUF                  PerfCounter64,
    cmEthernetNetPortHistoryESUP                  PerfCounter64,--ES,EH
    cmEthernetNetPortHistoryL2CPFD                PerfCounter64,
    cmEthernetNetPortHistoryL2CPFP                PerfCounter64,
    cmEthernetNetPortHistoryLES                   PerfCounter64,
    cmEthernetNetPortHistoryLBC                   Integer32,
    cmEthernetNetPortHistoryOPT                   Integer32,
    cmEthernetNetPortHistoryOPR                   Integer32,
    cmEthernetNetPortHistoryAUFD                  PerfCounter64,
    cmEthernetNetPortHistoryAPFD                  PerfCounter64,
    cmEthernetNetPortHistoryABRRx                 PerfCounter64,
    cmEthernetNetPortHistoryABRTx                 PerfCounter64,
    cmEthernetNetPortHistoryPSC                   PerfCounter64,
    cmEthernetNetPortHistoryTemp                  Integer32,
    cmEthernetNetPortHistoryUAS                   PerfCounter64,
    cmEthernetNetPortHistoryL2PTRxFramesEncap     PerfCounter64,
    cmEthernetNetPortHistoryL2PTTxFramesDecap     PerfCounter64,
    cmEthernetNetPortHistoryIBRMaxRx              PerfCounter64,
    cmEthernetNetPortHistoryIBRMaxTx              PerfCounter64,
    cmEthernetNetPortHistoryIBRMinRx              PerfCounter64,
    cmEthernetNetPortHistoryIBRMinTx              PerfCounter64,
    cmEthernetNetPortHistoryIBRRx                 PerfCounter64,
    cmEthernetNetPortHistoryIBRTx                 PerfCounter64,
    cmEthernetNetPortHistoryFmcd                  PerfCounter64,
    cmEthernetNetPortHistoryFbcd                  PerfCounter64,
    cmEthernetNetPortHistoryAclDropNoMatch        PerfCounter64,
    cmEthernetNetPortHistoryAclFwd2Cpu            PerfCounter64,
    cmEthernetNetPortHistoryDhcpDropNoAssocIf     PerfCounter64,
    cmEthernetNetPortHistoryLkupFails             PerfCounter64
}

cmEthernetNetPortHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Network Port statistics entry."
    ::= { cmEthernetNetPortHistoryEntry 1 }

cmEthernetNetPortHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmEthernetNetPortHistoryEntry 2 }

cmEthernetNetPortHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetNetPortHistoryEntry 3 }

cmEthernetNetPortHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetNetPortHistoryEntry 4 }

cmEthernetNetPortHistoryESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent in A2N direction."
     ::= { cmEthernetNetPortHistoryEntry 5 }

cmEthernetNetPortHistoryESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received in N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 6 }

cmEthernetNetPortHistoryESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent in A2N direction."
     ::= { cmEthernetNetPortHistoryEntry 7 }

cmEthernetNetPortHistoryESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected in N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 8 }

cmEthernetNetPortHistoryESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 9 }

cmEthernetNetPortHistoryESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetNetPortHistoryEntry 10 }

cmEthernetNetPortHistoryESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 11 }

cmEthernetNetPortHistoryESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent in the A2N direction."
     ::= { cmEthernetNetPortHistoryEntry 12 }

cmEthernetNetPortHistoryESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 13 }

cmEthernetNetPortHistoryESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent in the A2N direction."
     ::= { cmEthernetNetPortHistoryEntry 14 }

cmEthernetNetPortHistoryESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 15 }

cmEthernetNetPortHistoryESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 16 }

cmEthernetNetPortHistoryESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames detected and dropped in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 17 }

cmEthernetNetPortHistoryESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 18 }

cmEthernetNetPortHistoryESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 19 }

cmEthernetNetPortHistoryESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 20 }

cmEthernetNetPortHistoryESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 21 }

cmEthernetNetPortHistoryESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 22 }

cmEthernetNetPortHistoryESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 23 }

cmEthernetNetPortHistoryESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 24 }

cmEthernetNetPortHistoryESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 25 }

cmEthernetNetPortHistoryESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 26 }

cmEthernetNetPortHistoryESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 27 }

cmEthernetNetPortHistoryESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Undersize Packets received in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 28 }

cmEthernetNetPortHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 29 }

cmEthernetNetPortHistoryL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed in the N2A direction."
     ::= { cmEthernetNetPortHistoryEntry 30 }

cmEthernetNetPortHistoryLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected in the N2A direction.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetNetPortHistoryEntry 31 }

cmEthernetNetPortHistoryLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortHistoryEntry 32 }

cmEthernetNetPortHistoryOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortHistoryEntry 33 }

cmEthernetNetPortHistoryOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Network Port is fiber."
     ::= { cmEthernetNetPortHistoryEntry 34 }


cmEthernetNetPortHistoryAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD) in the N2A direction. This counter is not applicable
          for GE206."
     ::= { cmEthernetNetPortHistoryEntry 35 }

cmEthernetNetPortHistoryAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD) in the N2A direction. This counter is not applicable
          for GE206."
     ::= { cmEthernetNetPortHistoryEntry 36 }

cmEthernetNetPortHistoryABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmEthernetNetPortHistoryEntry 37 }

cmEthernetNetPortHistoryABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmEthernetNetPortHistoryEntry 38 }

cmEthernetNetPortHistoryPSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The Protection Switch Counts on the port ." 
     ::= { cmEthernetNetPortHistoryEntry 39 }

cmEthernetNetPortHistoryTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetNetPortHistoryEntry 40 }

cmEthernetNetPortHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Unavailable Seconds on the Port."
     ::= { cmEthernetNetPortHistoryEntry 41 }

cmEthernetNetPortHistoryL2PTRxFramesEncap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Recevied Frames Encapsulated."
     ::= { cmEthernetNetPortHistoryEntry 42 }

cmEthernetNetPortHistoryL2PTTxFramesDecap OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of Layer 2 Protocol Tunneling Transmitted Frames De-encapsulated."
     ::= { cmEthernetNetPortHistoryEntry 43 }

cmEthernetNetPortHistoryIBRMaxRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits received on a port in any 1-sec with the PM interval."
     ::= { cmEthernetNetPortHistoryEntry 44 }
     
cmEthernetNetPortHistoryIBRMaxTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum bits transmitted on a port in any 1-sec with the PM interval."
     ::= { cmEthernetNetPortHistoryEntry 45 }

cmEthernetNetPortHistoryIBRMinRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits received on a port in any 1-sec with the PM interval"
     ::= { cmEthernetNetPortHistoryEntry 46 }

cmEthernetNetPortHistoryIBRMinTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum bits transmitted on a port in any 1-sec with the PM interval"
     ::= { cmEthernetNetPortHistoryEntry 47 }

cmEthernetNetPortHistoryIBRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits received in the most recent 1-sec"
     ::= { cmEthernetNetPortHistoryEntry 48 }
     
cmEthernetNetPortHistoryIBRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bits transmitted in the most recent 1-sec"
     ::= { cmEthernetNetPortHistoryEntry 49 }
     
cmEthernetNetPortHistoryFmcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of multicast drop."
     ::= { cmEthernetNetPortHistoryEntry 50 }
     
cmEthernetNetPortHistoryFbcd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame of broadcast drop."
     ::= { cmEthernetNetPortHistoryEntry 51 }

cmEthernetNetPortHistoryAclDropNoMatch OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 control protocol frames dropped due to acl no match."
     ::= { cmEthernetNetPortHistoryEntry 52 }

cmEthernetNetPortHistoryAclFwd2Cpu OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of L3 Control protocol frames foward to cpu."
     ::= { cmEthernetNetPortHistoryEntry 53 }

cmEthernetNetPortHistoryDhcpDropNoAssocIf OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Counter of DHCP packets dropped by CPU due to no associated Traffic IP Interface found."
     ::= { cmEthernetNetPortHistoryEntry 54 }

cmEthernetNetPortHistoryLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Membership lookup fail counts."
     ::= { cmEthernetNetPortHistoryEntry 55 }

--
-- Ethernet Network Port Threshold Table
--
cmEthernetNetPortThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetNetPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Ethernet
             Network Port Thresholds."
    ::= { cmPerfObjects 7 }

cmEthernetNetPortThresholdEntry OBJECT-TYPE
    SYNTAX      CmEthernetNetPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetNetPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex, 
            cmEthernetNetPortStatsIndex, cmEthernetNetPortThresholdIndex }
    ::= { cmEthernetNetPortThresholdTable 1 }

CmEthernetNetPortThresholdEntry ::= SEQUENCE {
    cmEthernetNetPortThresholdIndex       Integer32,
    cmEthernetNetPortThresholdInterval    CmPmIntervalType,
    cmEthernetNetPortThresholdVariable    VariablePointer,
    cmEthernetNetPortThresholdValueLo     Unsigned32,
    cmEthernetNetPortThresholdValueHi     Unsigned32,
    cmEthernetNetPortThresholdMonValue    Counter64
}

cmEthernetNetPortThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmEthernetNetPortThresholdTable."
    ::= { cmEthernetNetPortThresholdEntry 1 }

cmEthernetNetPortThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmEthernetNetPortThresholdEntry 2 }

cmEthernetNetPortThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmEthernetNetPortThresholdEntry 3 }

cmEthernetNetPortThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value.  Please note that in case of 
         cmEthernetNetPortStatsOPR, cmEthernetNetPortStatsOPT and cmEthernetNetPortStatsTEMP, 
         the values are 2's complement, since these could have negative values."
    ::= { cmEthernetNetPortThresholdEntry 4 }

cmEthernetNetPortThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value.  Please note that in case of 
         cmEthernetNetPortStatsOPR, cmEthernetNetPortStatsOPT and cmEthernetNetPortStatsTEMP, 
         the values are 2's complement, since these could have negative values."
    ::= { cmEthernetNetPortThresholdEntry 5 }

cmEthernetNetPortThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmEthernetNetPortThresholdVariable. 
         Please note that in case of cmEthernetNetPortStatsOPR, cmEthernetNetPortStatsOPT 
         and cmEthernetNetPortStatsTEMP, the values are 2's complement, since these could be 
         negative values."
    ::= { cmEthernetNetPortThresholdEntry 6 }

--
-- Ethernet Network Port Threshold Variance Table
--
cmEthernetNetPortThresholdVarTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetNetPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Ethernet
             Network Port Threshold variances."
    ::= { cmPerfObjects 8 }

cmEthernetNetPortThresholdVarEntry OBJECT-TYPE
    SYNTAX      CmEthernetNetPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetNetPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex, 
            cmEthernetNetPortStatsIndex}
    ::= { cmEthernetNetPortThresholdVarTable 1 }

CmEthernetNetPortThresholdVarEntry ::= SEQUENCE {
    cmEthernetNetPortThresholdVarOprVariance    Integer32,
    cmEthernetNetPortThresholdVarOptVariance    Integer32
}

cmEthernetNetPortThresholdVarOprVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power received (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetNetPortThresholdVarEntry 1 }

cmEthernetNetPortThresholdVarOptVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power transmitted (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetNetPortThresholdVarEntry 2 }

--
-- Ethernet Flow Current Statistics Table
--
cmFlowStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of EVC statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 9 }

cmFlowStatsEntry OBJECT-TYPE
    SYNTAX      CmFlowStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowStatsTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmFlowStatsIndex }
    ::= { cmFlowStatsTable 1 }

CmFlowStatsEntry ::= SEQUENCE {
    cmFlowStatsIndex                 Integer32,
    cmFlowStatsIntervalType          CmPmIntervalType,
    cmFlowStatsValid                 TruthValue,
    cmFlowStatsAction                CmPmBinAction,
    cmFlowStatsL2CPFD                PerfCounter64,
    cmFlowStatsABRA2N                PerfCounter64,
    cmFlowStatsABRRLA2N              PerfCounter64,
    cmFlowStatsABRRLRA2N             PerfCounter64,
    cmFlowStatsABRN2A                PerfCounter64,
    cmFlowStatsABRRLN2A              PerfCounter64,

    --New objects available with FSP150CC GE101, GE206 devices 
    cmFlowStatsUAS                   PerfCounter64,
    cmFlowStatsES                    PerfCounter64,
    cmFlowStatsSES                   PerfCounter64,
    cmFlowStatsFMGA2N                PerfCounter64,
    cmFlowStatsFMYA2N                PerfCounter64,
    cmFlowStatsFMYDA2N               PerfCounter64,
    cmFlowStatsFMRDA2N               PerfCounter64,
    cmFlowStatsBytesInA2N            PerfCounter64,
    cmFlowStatsBytesOutA2N           PerfCounter64,
    cmFlowStatsFMGN2A                PerfCounter64,
    cmFlowStatsFMYN2A                PerfCounter64,
    cmFlowStatsFMYDN2A               PerfCounter64,
    cmFlowStatsFMRDN2A               PerfCounter64,
    cmFlowStatsBytesInN2A            PerfCounter64,
    cmFlowStatsBytesOutN2A           PerfCounter64,
    cmFlowStatsFTDA2N                PerfCounter64,
    cmFlowStatsIBRA2NMax             PerfCounter64,
    cmFlowStatsIBRRlA2NMax           PerfCounter64,
    cmFlowStatsIBRA2NMin             PerfCounter64,
    cmFlowStatsIBRRlA2NMin           PerfCounter64,
    cmFlowStatsIBRA2N                PerfCounter64,
    cmFlowStatsIBRRlA2N              PerfCounter64,
    cmFlowStatsIBRN2AMax             PerfCounter64,
    cmFlowStatsIBRRlN2AMax           PerfCounter64,
    cmFlowStatsIBRN2AMin             PerfCounter64,
    cmFlowStatsIBRRlN2AMin           PerfCounter64,
    cmFlowStatsIBRN2A                PerfCounter64,
    cmFlowStatsIBRRlN2A              PerfCounter64,

    --New objects available with FSP150CC GE206V/XG210 (Release 7.1CC)
    cmFlowStatsFMCDA2N               PerfCounter64,
    cmFlowStatsFBCDA2N               PerfCounter64,
    cmFlowStatsACLN2ADrop            PerfCounter64,
    cmFlowStatsACLA2NDrop            PerfCounter64
}

cmFlowStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Flow statistics entry.
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmFlowStatsEntry 1 }

cmFlowStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmFlowStatsEntry 2 }

cmFlowStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmFlowStatsEntry 3 }

cmFlowStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmFlowStatsEntry 4 }

cmFlowStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in both A2N and N2A directions."
     ::= { cmFlowStatsEntry 5 }

cmFlowStatsABRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmFlowStatsEntry 6 }

cmFlowStatsABRRLA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited in the A2N direction." 
     ::= { cmFlowStatsEntry 7 }

cmFlowStatsABRRLRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate,rate limited (replicated) in the A2N direction." 
     ::= { cmFlowStatsEntry 8 }

cmFlowStatsABRN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmFlowStatsEntry 9 }

cmFlowStatsABRRLN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited in the N2A direction." 
     ::= { cmFlowStatsEntry 10 }

cmFlowStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS) on the flow."
     ::= { cmFlowStatsEntry 11 }

cmFlowStatsES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Errored Seconds (ES) on the flow."
     ::= { cmFlowStatsEntry 12 }

cmFlowStatsSES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES) on the flow."
     ::= { cmFlowStatsEntry 13 }

cmFlowStatsFMGA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green in A2N direction on the flow."
     ::= { cmFlowStatsEntry 14 }

cmFlowStatsFMYA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow in A2N direction on the flow."
     ::= { cmFlowStatsEntry 15 }

cmFlowStatsFMYDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "The number of Frames Marked Yellow and Discarded in A2N direction 
          on the flow."
     ::= { cmFlowStatsEntry 16 }

cmFlowStatsFMRDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded in A2N direction 
          on the flow."
     ::= { cmFlowStatsEntry 17 }

cmFlowStatsBytesInA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes received on the Flow in A2N direction."
     ::= { cmFlowStatsEntry 18 }

cmFlowStatsBytesOutA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes transmitted on the Flow in A2N direction."
     ::= { cmFlowStatsEntry 19 }

cmFlowStatsFMGN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green in N2A direction on the flow."
     ::= { cmFlowStatsEntry 20 }

cmFlowStatsFMYN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow in N2A direction on the flow."
     ::= { cmFlowStatsEntry 21 }

cmFlowStatsFMYDN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "The number of Frames Marked Yellow and Discarded in N2A direction 
          on the flow."
     ::= { cmFlowStatsEntry 22 }

cmFlowStatsFMRDN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded in N2A direction 
          on the flow."
     ::= { cmFlowStatsEntry 23 }

cmFlowStatsBytesInN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes received on the Flow in N2A direction."
     ::= { cmFlowStatsEntry 24 }

cmFlowStatsBytesOutN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes transmitted on the Flow in N2A direction."
     ::= { cmFlowStatsEntry 25 }

cmFlowStatsFTDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes FTD in the A2N direction."
     ::= { cmFlowStatsEntry 26 }

cmFlowStatsIBRA2NMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 27 }

cmFlowStatsIBRRlA2NMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' after rate limiting
          (sum of 'Bytes Out' for all policers * 8) for the Flow on the 
          Access Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 28 }

cmFlowStatsIBRA2NMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 29 }

cmFlowStatsIBRRlA2NMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' after rate limiting
          (sum of 'Bytes Out' for all policers * 8) for the Flow on the 
          Access Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 30 }

cmFlowStatsIBRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in the most recent 1-sec."
     ::= { cmFlowStatsEntry 31 }

cmFlowStatsIBRRlA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Access Port in the most recent 1-sec."
     ::= { cmFlowStatsEntry 32 }

cmFlowStatsIBRN2AMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 33 }

cmFlowStatsIBRRlN2AMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 34 }

cmFlowStatsIBRN2AMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' (sum of 'Bytes In' for all policers * 8)
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 35 }

cmFlowStatsIBRRlN2AMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8) 
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowStatsEntry 36 }

cmFlowStatsIBRN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' (sum of 'Bytes In' for all policers * 8)
          for the Flow on the Network Port in the most recent 1-sec."
     ::= { cmFlowStatsEntry 37 }

cmFlowStatsIBRRlN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Network Port in the most recent 1-sec."
     ::= { cmFlowStatsEntry 38 }

cmFlowStatsFMCDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of multicast frames dropped in A2N direction."
     ::= { cmFlowStatsEntry 39 }

cmFlowStatsFBCDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of broadcast frames dropped in A2N direction."
     ::= { cmFlowStatsEntry 40 }

cmFlowStatsACLN2ADrop OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame droped due to n2a direction ACL rule."
     ::= { cmFlowStatsEntry 41 }

cmFlowStatsACLA2NDrop OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame droped due to a2n direction ACL rule."
     ::= { cmFlowStatsEntry 42 }

--
-- Ethernet Flow History Statistics Table
--
cmFlowHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of EVC statistics.  These reflect the history data."
    ::= { cmPerfObjects 10 }

cmFlowHistoryEntry OBJECT-TYPE
    SYNTAX      CmFlowHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowHistoryTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmFlowStatsIndex, cmFlowHistoryIndex }
    ::= { cmFlowHistoryTable 1 }

CmFlowHistoryEntry ::= SEQUENCE {
    cmFlowHistoryIndex                 Integer32,
    cmFlowHistoryTime                  DateAndTime,
    cmFlowHistoryValid                 TruthValue,
    cmFlowHistoryAction                CmPmBinAction,
    cmFlowHistoryL2CPFD                PerfCounter64,
    cmFlowHistoryABRA2N                PerfCounter64,
    cmFlowHistoryABRRLA2N              PerfCounter64,
    cmFlowHistoryABRRLRA2N             PerfCounter64,
    cmFlowHistoryABRN2A                PerfCounter64,
    cmFlowHistoryABRRLN2A              PerfCounter64,

    --New objects available with FSP150CC GE101, GE206 devices 
    cmFlowHistoryUAS                   PerfCounter64,
    cmFlowHistoryES                    PerfCounter64,
    cmFlowHistorySES                   PerfCounter64,
    cmFlowHistoryFMGA2N                PerfCounter64,
    cmFlowHistoryFMYA2N                PerfCounter64,
    cmFlowHistoryFMYDA2N               PerfCounter64,
    cmFlowHistoryFMRDA2N               PerfCounter64,
    cmFlowHistoryBytesInA2N            PerfCounter64,
    cmFlowHistoryBytesOutA2N           PerfCounter64,
    cmFlowHistoryFMGN2A                PerfCounter64,
    cmFlowHistoryFMYN2A                PerfCounter64,
    cmFlowHistoryFMYDN2A               PerfCounter64,
    cmFlowHistoryFMRDN2A               PerfCounter64,
    cmFlowHistoryBytesInN2A            PerfCounter64,
    cmFlowHistoryBytesOutN2A           PerfCounter64,
    cmFlowHistoryFTDA2N                PerfCounter64,
    cmFlowHistoryIBRA2NMax             PerfCounter64,
    cmFlowHistoryIBRRlA2NMax           PerfCounter64,
    cmFlowHistoryIBRA2NMin             PerfCounter64,
    cmFlowHistoryIBRRlA2NMin           PerfCounter64,
    cmFlowHistoryIBRA2N                PerfCounter64,
    cmFlowHistoryIBRRlA2N              PerfCounter64,
    cmFlowHistoryIBRN2AMax             PerfCounter64,
    cmFlowHistoryIBRRlN2AMax           PerfCounter64,
    cmFlowHistoryIBRN2AMin             PerfCounter64,
    cmFlowHistoryIBRRlN2AMin           PerfCounter64,
    cmFlowHistoryIBRN2A                PerfCounter64,
    cmFlowHistoryIBRRlN2A              PerfCounter64,

    --New objects available with FSP150CC GE206V/XG210 (Release 7.1CC)
    cmFlowHistoryFMCDA2N               PerfCounter64,
    cmFlowHistoryFBCDA2N               PerfCounter64,
    cmFlowHistoryACLN2ADrop            PerfCounter64,
    cmFlowHistoryACLA2NDrop            PerfCounter64
}

cmFlowHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Flow statistics entry."
    ::= { cmFlowHistoryEntry 1 }

cmFlowHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Time of history interval."
    ::= { cmFlowHistoryEntry 2 }

cmFlowHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmFlowHistoryEntry 3 }

cmFlowHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmFlowHistoryEntry 4 }

cmFlowHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded in both A2N and N2A directions."
     ::= { cmFlowHistoryEntry 5 }

cmFlowHistoryABRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the A2N direction." 
     ::= { cmFlowHistoryEntry 6 }

cmFlowHistoryABRRLA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited in the A2N direction." 
     ::= { cmFlowHistoryEntry 7 }

cmFlowHistoryABRRLRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate,rate limited (replicated) in the A2N direction." 
     ::= { cmFlowHistoryEntry 8 }

cmFlowHistoryABRN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate in the N2A direction." 
     ::= { cmFlowHistoryEntry 9 }

cmFlowHistoryABRRLN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited in the N2A direction." 
     ::= { cmFlowHistoryEntry 10 }

cmFlowHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS) on the flow."
     ::= { cmFlowHistoryEntry 11 }

cmFlowHistoryES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Errored Seconds (ES) on the flow."
     ::= { cmFlowHistoryEntry 12 }

cmFlowHistorySES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES) on the flow."
     ::= { cmFlowHistoryEntry 13 }

cmFlowHistoryFMGA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green in A2N direction on the flow."
     ::= { cmFlowHistoryEntry 14 }

cmFlowHistoryFMYA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow in A2N direction on the flow."
     ::= { cmFlowHistoryEntry 15 }

cmFlowHistoryFMYDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "The number of Frames Marked Yellow and Discarded in A2N direction 
          on the flow."
     ::= { cmFlowHistoryEntry 16 }

cmFlowHistoryFMRDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded in A2N direction 
          on the flow."
     ::= { cmFlowHistoryEntry 17 }

cmFlowHistoryBytesInA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes received on the Flow in A2N direction."
     ::= { cmFlowHistoryEntry 18 }

cmFlowHistoryBytesOutA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes transmitted on the Flow in A2N direction."
     ::= { cmFlowHistoryEntry 19 }

cmFlowHistoryFMGN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green in N2A direction on the flow."
     ::= { cmFlowHistoryEntry 20 }

cmFlowHistoryFMYN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow in N2A direction on the flow."
     ::= { cmFlowHistoryEntry 21 }

cmFlowHistoryFMYDN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "The number of Frames Marked Yellow and Discarded in N2A direction 
          on the flow."
     ::= { cmFlowHistoryEntry 22 }

cmFlowHistoryFMRDN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded in N2A direction 
          on the flow."
     ::= { cmFlowHistoryEntry 23 }

cmFlowHistoryBytesInN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes received on the Flow in N2A direction."
     ::= { cmFlowHistoryEntry 24 }

cmFlowHistoryBytesOutN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes transmitted on the Flow in N2A direction."
     ::= { cmFlowHistoryEntry 25 }

cmFlowHistoryFTDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes FTD in the A2N direction."
     ::= { cmFlowHistoryEntry 26 }

cmFlowHistoryIBRA2NMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 27 }

cmFlowHistoryIBRRlA2NMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' after rate limiting
          (sum of 'Bytes Out' for all policers * 8) for the Flow on the 
          Access Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 28 }

cmFlowHistoryIBRA2NMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 29 }

cmFlowHistoryIBRRlA2NMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' after rate limiting
          (sum of 'Bytes Out' for all policers * 8) for the Flow on the 
          Access Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 30 }

cmFlowHistoryIBRA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Access Port in the most recent 1-sec."
     ::= { cmFlowHistoryEntry 31 }

cmFlowHistoryIBRRlA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Access Port in the most recent 1-sec."
     ::= { cmFlowHistoryEntry 32 }

cmFlowHistoryIBRN2AMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' (sum of 'Bytes In' for all policers * 8) 
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 33 }

cmFlowHistoryIBRRlN2AMax OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Maximum 'bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 34 }

cmFlowHistoryIBRN2AMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' (sum of 'Bytes In' for all policers * 8)
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 35 }

cmFlowHistoryIBRRlN2AMin OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Minimum 'bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8) 
          for the Flow on the Network Port in any 1-sec within a PM interval."
     ::= { cmFlowHistoryEntry 36 }

cmFlowHistoryIBRN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' (sum of 'Bytes In' for all policers * 8)
          for the Flow on the Network Port in the most recent 1-sec."
     ::= { cmFlowHistoryEntry 37 }

cmFlowHistoryIBRRlN2A OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "'Bits received' after rate limiting (sum of 'Bytes Out' for all policers * 8)
          for the Flow on the Network Port in the most recent 1-sec."
     ::= { cmFlowHistoryEntry 38 }     

cmFlowHistoryFMCDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of multicast frames dropped in A2N direction."
     ::= { cmFlowHistoryEntry 39 }

cmFlowHistoryFBCDA2N OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of broadcast frames dropped in A2N direction."
     ::= { cmFlowHistoryEntry 40 }

cmFlowHistoryACLN2ADrop OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame dropped due to n2a direction ACL rule."
     ::= { cmFlowHistoryEntry 41 }

cmFlowHistoryACLA2NDrop OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame dropped due to a2n direction ACL rule."
     ::= { cmFlowHistoryEntry 42 }

--
-- EVC Threshold Table
--
cmFlowThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Ethernet
             Flow Thresholds."
    ::= { cmPerfObjects 11 }

cmFlowThresholdEntry OBJECT-TYPE
    SYNTAX      CmFlowThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex, 
            cmFlowIndex, cmFlowStatsIndex, cmFlowThresholdIndex }
    ::= { cmFlowThresholdTable 1 }

CmFlowThresholdEntry ::= SEQUENCE {
    cmFlowThresholdIndex       Integer32,
    cmFlowThresholdInterval    CmPmIntervalType,
    cmFlowThresholdVariable    VariablePointer,
    cmFlowThresholdValueLo     Unsigned32,
    cmFlowThresholdValueHi     Unsigned32,
    cmFlowThresholdMonValue    Counter64
}

cmFlowThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmFlowThresholdTable."
    ::= { cmFlowThresholdEntry 1 }

cmFlowThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmFlowThresholdEntry 2 }

cmFlowThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmFlowThresholdEntry 3 }

cmFlowThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmFlowThresholdEntry 4 }

cmFlowThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmFlowThresholdEntry 5 }

cmFlowThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmFlowThresholdVariable."
    ::= { cmFlowThresholdEntry 6 }


--
-- Quality of Service(QOS) Shaper Current Statistics Table
--
cmQosShaperStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Shaper statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 12 }

cmQosShaperStatsEntry OBJECT-TYPE
    SYNTAX      CmQosShaperStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperStatsTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosShaperTypeIndex, cmQosShaperIndex, 
            cmQosShaperStatsIndex }
    ::= { cmQosShaperStatsTable 1 }

CmQosShaperStatsEntry ::= SEQUENCE {
    cmQosShaperStatsIndex             Integer32,
    cmQosShaperStatsIntervalType      CmPmIntervalType,
    cmQosShaperStatsValid             TruthValue,
    cmQosShaperStatsAction            CmPmBinAction,
    cmQosShaperStatsBT                PerfCounter64,
    cmQosShaperStatsBTD               PerfCounter64,
    cmQosShaperStatsFD                PerfCounter64,
    cmQosShaperStatsFTD               PerfCounter64,
    cmQosShaperStatsBR                PerfCounter64,
    cmQosShaperStatsFR                PerfCounter64,
    cmQosShaperStatsABRRL             PerfCounter64,
    cmQosShaperStatsABRRLR            PerfCounter64,
    cmQosShaperStatsBREDD             PerfCounter64,
    cmQosShaperStatsFREDD             PerfCounter64
}

cmQosShaperStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry.
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmQosShaperStatsEntry 1 }

cmQosShaperStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmQosShaperStatsEntry 2 }

cmQosShaperStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosShaperStatsEntry 3 }

cmQosShaperStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosShaperStatsEntry 4 }

cmQosShaperStatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmQosShaperStatsEntry 5 }

cmQosShaperStatsBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmQosShaperStatsEntry 6 }

cmQosShaperStatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmQosShaperStatsEntry 7 }

cmQosShaperStatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmQosShaperStatsEntry 8 }

cmQosShaperStatsBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { cmQosShaperStatsEntry 9 }

cmQosShaperStatsFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { cmQosShaperStatsEntry 10 }

cmQosShaperStatsABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmQosShaperStatsEntry 11 }

cmQosShaperStatsABRRLR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited, Replicated."
     ::= { cmQosShaperStatsEntry 12 }

cmQosShaperStatsBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { cmQosShaperStatsEntry 13 }

cmQosShaperStatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { cmQosShaperStatsEntry 14 }

--
-- Quality of Service(QOS) Shaper History Statistics Table
--
cmQosShaperHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Shaper statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 13 }

cmQosShaperHistoryEntry OBJECT-TYPE
    SYNTAX      CmQosShaperHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperHistoryTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosShaperTypeIndex, cmQosShaperIndex, 
            cmQosShaperStatsIndex, cmQosShaperHistoryIndex }
    ::= { cmQosShaperHistoryTable 1 }

CmQosShaperHistoryEntry ::= SEQUENCE {
    cmQosShaperHistoryIndex             Integer32,
    cmQosShaperHistoryTime              DateAndTime,
    cmQosShaperHistoryValid             TruthValue,
    cmQosShaperHistoryAction            CmPmBinAction,
    cmQosShaperHistoryBT                PerfCounter64,
    cmQosShaperHistoryBTD               PerfCounter64,
    cmQosShaperHistoryFD                PerfCounter64,
    cmQosShaperHistoryFTD               PerfCounter64,
    cmQosShaperHistoryBR                PerfCounter64,
    cmQosShaperHistoryFR                PerfCounter64,
    cmQosShaperHistoryABRRL             PerfCounter64,
    cmQosShaperHistoryABRRLR            PerfCounter64,
    cmQosShaperHistoryBREDD             PerfCounter64,
    cmQosShaperHistoryFREDD             PerfCounter64
}

cmQosShaperHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry."
    ::= { cmQosShaperHistoryEntry 1 }

cmQosShaperHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmQosShaperHistoryEntry 2 }

cmQosShaperHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosShaperHistoryEntry 3 }

cmQosShaperHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosShaperHistoryEntry 4 }

cmQosShaperHistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmQosShaperHistoryEntry 5 }

cmQosShaperHistoryBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmQosShaperHistoryEntry 6 }

cmQosShaperHistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmQosShaperHistoryEntry 7 }

cmQosShaperHistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmQosShaperHistoryEntry 8 }

cmQosShaperHistoryBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { cmQosShaperHistoryEntry 9 }

cmQosShaperHistoryFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { cmQosShaperHistoryEntry 10 }

cmQosShaperHistoryABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmQosShaperHistoryEntry 11 }

cmQosShaperHistoryABRRLR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited, Replicated."
     ::= { cmQosShaperHistoryEntry 12 }

cmQosShaperHistoryBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { cmQosShaperHistoryEntry 13 }

cmQosShaperHistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { cmQosShaperHistoryEntry 14 }


--
-- QOS Shaper Threshold Table
--
cmQosShaperThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             Shaper Thresholds."
    ::= { cmPerfObjects 14 }

cmQosShaperThresholdEntry OBJECT-TYPE
    SYNTAX      CmQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosShaperTypeIndex, cmQosShaperIndex, 
            cmQosShaperStatsIndex, cmQosShaperThresholdIndex }
    ::= { cmQosShaperThresholdTable 1 }

CmQosShaperThresholdEntry ::= SEQUENCE {
    cmQosShaperThresholdIndex       Integer32,
    cmQosShaperThresholdInterval    CmPmIntervalType,
    cmQosShaperThresholdVariable    VariablePointer,
    cmQosShaperThresholdValueLo     Unsigned32,
    cmQosShaperThresholdValueHi     Unsigned32,
    cmQosShaperThresholdMonValue    Counter64
}

cmQosShaperThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmQosShaperThresholdTable."
    ::= { cmQosShaperThresholdEntry 1 }

cmQosShaperThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmQosShaperThresholdEntry 2 }

cmQosShaperThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmQosShaperThresholdEntry 3 }

cmQosShaperThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmQosShaperThresholdEntry 4 }

cmQosShaperThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmQosShaperThresholdEntry 5 }

cmQosShaperThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmQosShaperThresholdVariable."
    ::= { cmQosShaperThresholdEntry 6 }

--
-- Quality of Service(QOS) Policer Current Statistics Table
--
cmQosFlowPolicerStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosFlowPolicerStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Policer statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 15 }

cmQosFlowPolicerStatsEntry OBJECT-TYPE
    SYNTAX      CmQosFlowPolicerStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosFlowPolicerStatsTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosFlowPolicerTypeIndex, cmQosFlowPolicerIndex, 
            cmQosFlowPolicerStatsIndex }
    ::= { cmQosFlowPolicerStatsTable 1 }

CmQosFlowPolicerStatsEntry ::= SEQUENCE {
    cmQosFlowPolicerStatsIndex           Integer32,
    cmQosFlowPolicerStatsIntervalType    CmPmIntervalType,
    cmQosFlowPolicerStatsValid           TruthValue,
    cmQosFlowPolicerStatsAction          CmPmBinAction,
    cmQosFlowPolicerStatsFMG             PerfCounter64,
    cmQosFlowPolicerStatsFMY             PerfCounter64,
    cmQosFlowPolicerStatsFMYD            PerfCounter64,
    cmQosFlowPolicerStatsFMRD            PerfCounter64,
    cmQosFlowPolicerStatsBytesIn         PerfCounter64,
    cmQosFlowPolicerStatsBytesOut        PerfCounter64,
    cmQosFlowPolicerStatsABR             PerfCounter64
}

cmQosFlowPolicerStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
            this QOS Policer statistics entry.
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmQosFlowPolicerStatsEntry 1 }

cmQosFlowPolicerStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmQosFlowPolicerStatsEntry 2 }

cmQosFlowPolicerStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosFlowPolicerStatsEntry 3 }

cmQosFlowPolicerStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosFlowPolicerStatsEntry 4 }

cmQosFlowPolicerStatsFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Green."
     ::= { cmQosFlowPolicerStatsEntry 5 }

cmQosFlowPolicerStatsFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow."
     ::= { cmQosFlowPolicerStatsEntry 6 }

cmQosFlowPolicerStatsFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Frames Marked Yellow and Dropped."
     ::= { cmQosFlowPolicerStatsEntry 7 }

cmQosFlowPolicerStatsFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Red and Discarded."
     ::= { cmQosFlowPolicerStatsEntry 8 }

cmQosFlowPolicerStatsBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes received by the Policer."
     ::= { cmQosFlowPolicerStatsEntry 9 }

cmQosFlowPolicerStatsBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes transmitted by the Policer."
     ::= { cmQosFlowPolicerStatsEntry 10 }

cmQosFlowPolicerStatsABR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate  on the Policer."
     ::= { cmQosFlowPolicerStatsEntry 11 }


--
-- Quality of Service(QOS) Policer History Statistics Table
--
cmQosFlowPolicerHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosFlowPolicerHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Policer statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 16 }

cmQosFlowPolicerHistoryEntry OBJECT-TYPE
    SYNTAX      CmQosFlowPolicerHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosFlowPolicerHistoryTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosFlowPolicerTypeIndex, cmQosFlowPolicerIndex, 
            cmQosFlowPolicerStatsIndex, cmQosFlowPolicerHistoryIndex }
    ::= { cmQosFlowPolicerHistoryTable 1 }

CmQosFlowPolicerHistoryEntry ::= SEQUENCE {
    cmQosFlowPolicerHistoryIndex           Integer32,
    cmQosFlowPolicerHistoryTime            DateAndTime,
    cmQosFlowPolicerHistoryValid           TruthValue,
    cmQosFlowPolicerHistoryAction          CmPmBinAction,
    cmQosFlowPolicerHistoryFMG             PerfCounter64,
    cmQosFlowPolicerHistoryFMY             PerfCounter64,
    cmQosFlowPolicerHistoryFMYD            PerfCounter64,
    cmQosFlowPolicerHistoryFMRD            PerfCounter64,
    cmQosFlowPolicerHistoryBytesIn         PerfCounter64,
    cmQosFlowPolicerHistoryBytesOut        PerfCounter64,
    cmQosFlowPolicerHistoryABR             PerfCounter64
}

cmQosFlowPolicerHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Policer history entry."
    ::= { cmQosFlowPolicerHistoryEntry 1 }

cmQosFlowPolicerHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmQosFlowPolicerHistoryEntry 2 }

cmQosFlowPolicerHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosFlowPolicerHistoryEntry 3 }

cmQosFlowPolicerHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosFlowPolicerHistoryEntry 4 }

cmQosFlowPolicerHistoryFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Green."
     ::= { cmQosFlowPolicerHistoryEntry 5 }

cmQosFlowPolicerHistoryFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow."
     ::= { cmQosFlowPolicerHistoryEntry 6 }

cmQosFlowPolicerHistoryFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Frames Marked Yellow and Dropped."
     ::= { cmQosFlowPolicerHistoryEntry 7 }

cmQosFlowPolicerHistoryFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Red and Discarded."
     ::= { cmQosFlowPolicerHistoryEntry 8 }

cmQosFlowPolicerHistoryBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes received by the Policer."
     ::= { cmQosFlowPolicerHistoryEntry 9 }

cmQosFlowPolicerHistoryBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes transmitted by the Policer."
     ::= { cmQosFlowPolicerHistoryEntry 10 }

cmQosFlowPolicerHistoryABR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate  on the Policer."
     ::= { cmQosFlowPolicerHistoryEntry 11 }

--
-- QOS Policer Threshold Table
--
cmQosFlowPolicerThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosFlowPolicerThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             Policer Thresholds."
    ::= { cmPerfObjects 17 }

cmQosFlowPolicerThresholdEntry OBJECT-TYPE
    SYNTAX      CmQosFlowPolicerThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosFlowPolicerThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmFlowIndex, cmQosFlowPolicerTypeIndex, cmQosFlowPolicerIndex, 
            cmQosFlowPolicerStatsIndex, cmQosFlowPolicerThresholdIndex }
    ::= { cmQosFlowPolicerThresholdTable 1 }

CmQosFlowPolicerThresholdEntry ::= SEQUENCE {
    cmQosFlowPolicerThresholdIndex       Integer32,
    cmQosFlowPolicerThresholdInterval    CmPmIntervalType,
    cmQosFlowPolicerThresholdVariable    VariablePointer,
    cmQosFlowPolicerThresholdValueLo     Unsigned32,
    cmQosFlowPolicerThresholdValueHi     Unsigned32,
    cmQosFlowPolicerThresholdMonValue    Counter64
}

cmQosFlowPolicerThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmQosFlowPolicerThresholdTable."
    ::= { cmQosFlowPolicerThresholdEntry 1 }

cmQosFlowPolicerThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmQosFlowPolicerThresholdEntry 2 }

cmQosFlowPolicerThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmQosFlowPolicerThresholdEntry 3 }

cmQosFlowPolicerThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmQosFlowPolicerThresholdEntry 4 }

cmQosFlowPolicerThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmQosFlowPolicerThresholdEntry 5 }

cmQosFlowPolicerThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmQosFlowPolicerThresholdVariable."
    ::= { cmQosFlowPolicerThresholdEntry 6 }


--
-- Port Level Quality of Service(QOS) Shaper Current Statistics Table
--
cmAccPortQosShaperStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmAccPortQosShaperStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Access Port Level QOS Shaper statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 18 }

cmAccPortQosShaperStatsEntry OBJECT-TYPE
    SYNTAX      CmAccPortQosShaperStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmAccPortQosShaperStatsTable.
             Entries exist in this table for each QOS Shaper per Access Port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmAccPortQosShaperIndex, cmAccPortQosShaperStatsIndex }
    ::= { cmAccPortQosShaperStatsTable 1 }

CmAccPortQosShaperStatsEntry ::= SEQUENCE {
    cmAccPortQosShaperStatsIndex             Integer32,
    cmAccPortQosShaperStatsIntervalType      CmPmIntervalType,
    cmAccPortQosShaperStatsValid             TruthValue,
    cmAccPortQosShaperStatsAction            CmPmBinAction,
    cmAccPortQosShaperStatsBT                PerfCounter64,
    cmAccPortQosShaperStatsBTD               PerfCounter64,
    cmAccPortQosShaperStatsFD                PerfCounter64,
    cmAccPortQosShaperStatsFTD               PerfCounter64,
    cmAccPortQosShaperStatsBR                PerfCounter64,
    cmAccPortQosShaperStatsFR                PerfCounter64,
    cmAccPortQosShaperStatsABRRL             PerfCounter64,
    cmAccPortQosShaperStatsBREDD             PerfCounter64,
    cmAccPortQosShaperStatsFREDD             PerfCounter64
}

cmAccPortQosShaperStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry.
            1 - 15min
            2 - 1day
            3 - rollover
            4 - 5min"
    ::= { cmAccPortQosShaperStatsEntry 1 }

cmAccPortQosShaperStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmAccPortQosShaperStatsEntry 2 }

cmAccPortQosShaperStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmAccPortQosShaperStatsEntry 3 }

cmAccPortQosShaperStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmAccPortQosShaperStatsEntry 4 }

cmAccPortQosShaperStatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmAccPortQosShaperStatsEntry 5 }

cmAccPortQosShaperStatsBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmAccPortQosShaperStatsEntry 6 }

cmAccPortQosShaperStatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmAccPortQosShaperStatsEntry 7 }

cmAccPortQosShaperStatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmAccPortQosShaperStatsEntry 8 }

cmAccPortQosShaperStatsBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { cmAccPortQosShaperStatsEntry 9 }

cmAccPortQosShaperStatsFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { cmAccPortQosShaperStatsEntry 10 }

cmAccPortQosShaperStatsABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmAccPortQosShaperStatsEntry 11 }

cmAccPortQosShaperStatsBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { cmAccPortQosShaperStatsEntry 12 }

cmAccPortQosShaperStatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { cmAccPortQosShaperStatsEntry 13 }

--
-- Quality of Service(QOS) Shaper History Statistics Table
--
cmAccPortQosShaperHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmAccPortQosShaperHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Access Port Level QOS Shaper statistics.
             These reflect the history data."
    ::= { cmPerfObjects 19 }

cmAccPortQosShaperHistoryEntry OBJECT-TYPE
    SYNTAX      CmAccPortQosShaperHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmAccPortQosShaperHistoryTable.
             Entries exist in this table for each Shaper per Access Port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmAccPortQosShaperIndex, cmAccPortQosShaperStatsIndex, cmAccPortQosShaperHistoryIndex }
    ::= { cmAccPortQosShaperHistoryTable 1 }

CmAccPortQosShaperHistoryEntry ::= SEQUENCE {
    cmAccPortQosShaperHistoryIndex             Integer32,
    cmAccPortQosShaperHistoryTime              DateAndTime,
    cmAccPortQosShaperHistoryValid             TruthValue,
    cmAccPortQosShaperHistoryAction            CmPmBinAction,
    cmAccPortQosShaperHistoryBT                PerfCounter64,
    cmAccPortQosShaperHistoryBTD               PerfCounter64,
    cmAccPortQosShaperHistoryFD                PerfCounter64,
    cmAccPortQosShaperHistoryFTD               PerfCounter64,
    cmAccPortQosShaperHistoryBR                PerfCounter64,
    cmAccPortQosShaperHistoryFR                PerfCounter64,
    cmAccPortQosShaperHistoryABRRL             PerfCounter64,
    cmAccPortQosShaperHistoryBREDD             PerfCounter64,
    cmAccPortQosShaperHistoryFREDD             PerfCounter64
}

cmAccPortQosShaperHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry."
    ::= { cmAccPortQosShaperHistoryEntry 1 }

cmAccPortQosShaperHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmAccPortQosShaperHistoryEntry 2 }

cmAccPortQosShaperHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmAccPortQosShaperHistoryEntry 3 }

cmAccPortQosShaperHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmAccPortQosShaperHistoryEntry 4 }

cmAccPortQosShaperHistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmAccPortQosShaperHistoryEntry 5 }

cmAccPortQosShaperHistoryBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmAccPortQosShaperHistoryEntry 6 }

cmAccPortQosShaperHistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmAccPortQosShaperHistoryEntry 7 }

cmAccPortQosShaperHistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmAccPortQosShaperHistoryEntry 8 }

cmAccPortQosShaperHistoryBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { cmAccPortQosShaperHistoryEntry 9 }

cmAccPortQosShaperHistoryFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { cmAccPortQosShaperHistoryEntry 10 }

cmAccPortQosShaperHistoryABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmAccPortQosShaperHistoryEntry 11 }

cmAccPortQosShaperHistoryBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { cmAccPortQosShaperHistoryEntry 12 }

cmAccPortQosShaperHistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { cmAccPortQosShaperHistoryEntry 13 }

--
-- Access Port QOS Shaper Threshold Table
--
cmAccPortQosShaperThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmAccPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             Shaper Thresholds."
    ::= { cmPerfObjects 20 }

cmAccPortQosShaperThresholdEntry OBJECT-TYPE
    SYNTAX      CmAccPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmAccPortQosShaperThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetAccPortIndex,
            cmAccPortQosShaperIndex, cmAccPortQosShaperStatsIndex, 
            cmAccPortQosShaperThresholdIndex }
    ::= { cmAccPortQosShaperThresholdTable 1 }

CmAccPortQosShaperThresholdEntry ::= SEQUENCE {
    cmAccPortQosShaperThresholdIndex       Integer32,
    cmAccPortQosShaperThresholdInterval    CmPmIntervalType,
    cmAccPortQosShaperThresholdVariable    VariablePointer,
    cmAccPortQosShaperThresholdValueLo     Unsigned32,
    cmAccPortQosShaperThresholdValueHi     Unsigned32,
    cmAccPortQosShaperThresholdMonValue    Counter64
}

cmAccPortQosShaperThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmAccPortQosShaperThresholdTable."
    ::= { cmAccPortQosShaperThresholdEntry 1 }

cmAccPortQosShaperThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmAccPortQosShaperThresholdEntry 2 }

cmAccPortQosShaperThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmAccPortQosShaperThresholdEntry 3 }

cmAccPortQosShaperThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmAccPortQosShaperThresholdEntry 4 }

cmAccPortQosShaperThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmAccPortQosShaperThresholdEntry 5 }

cmAccPortQosShaperThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmAccPortQosShaperThresholdVariable."
    ::= { cmAccPortQosShaperThresholdEntry 6 }

--
-- Agg Ethernet Port Current Statistics Table
--
cmEthernetTrafficPortStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetTrafficPortStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Agg Ethernet Port related statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 21 }

cmEthernetTrafficPortStatsEntry OBJECT-TYPE
    SYNTAX      CmEthernetTrafficPortStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetTrafficPortStatsTable.
             Entries exist in this table for each Ethernet interface/port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, cmEthernetTrafficPortStatsIndex }
    ::= { cmEthernetTrafficPortStatsTable 1 }

CmEthernetTrafficPortStatsEntry ::= SEQUENCE {
    cmEthernetTrafficPortStatsIndex                 Integer32,
    cmEthernetTrafficPortStatsIntervalType          CmPmIntervalType,
    cmEthernetTrafficPortStatsValid                 TruthValue,
    cmEthernetTrafficPortStatsAction                CmPmBinAction,
    cmEthernetTrafficPortStatsESBF                  PerfCounter64,
    cmEthernetTrafficPortStatsESBP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESBS                  PerfCounter64,
    cmEthernetTrafficPortStatsESC                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESCAE                 PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESDE                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESF                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESFS                  PerfCounter64,
    cmEthernetTrafficPortStatsESJ                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESMF                  PerfCounter64,
    cmEthernetTrafficPortStatsESMP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsESO                   PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortStatsESOF                  PerfCounter64,
    cmEthernetTrafficPortStatsESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortStatsESP                   PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortStatsESP64                 PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP65                 PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP128                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP256                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP512                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP1024               PerfCounter64,--ES,   HC
    cmEthernetTrafficPortStatsESP1519               PerfCounter64,
    cmEthernetTrafficPortStatsESUF                  PerfCounter64,
    cmEthernetTrafficPortStatsESUP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortStatsL2CPFD                PerfCounter64,
    cmEthernetTrafficPortStatsL2CPFP                PerfCounter64,
    cmEthernetTrafficPortStatsLES                   PerfCounter64,
    cmEthernetTrafficPortStatsLBC                   Integer32,
    cmEthernetTrafficPortStatsOPT                   Integer32,
    cmEthernetTrafficPortStatsOPR                   Integer32,
    cmEthernetTrafficPortStatsAUFD                  PerfCounter64,
    cmEthernetTrafficPortStatsAPFD                  PerfCounter64,
    cmEthernetTrafficPortStatsABRRx                 PerfCounter64,
    cmEthernetTrafficPortStatsABRTx                 PerfCounter64,
    cmEthernetTrafficPortStatsATFD                  PerfCounter64,
    cmEthernetTrafficPortStatsUAS                   PerfCounter64,
    cmEthernetTrafficPortStatsTemp                  Integer32,
    cmEthernetTrafficPortStatsLkupFails             PerfCounter64,
    cmEthernetTrafficPortStatsPSC                   PerfCounter64
}

cmEthernetTrafficPortStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..4)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Ethernet Port statistics entry. 
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmEthernetTrafficPortStatsEntry 1 }

cmEthernetTrafficPortStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmEthernetTrafficPortStatsEntry 2 }

cmEthernetTrafficPortStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetTrafficPortStatsEntry 3 }

cmEthernetTrafficPortStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetTrafficPortStatsEntry 4 }

cmEthernetTrafficPortStatsESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent."
     ::= { cmEthernetTrafficPortStatsEntry 5 }

cmEthernetTrafficPortStatsESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received."
     ::= { cmEthernetTrafficPortStatsEntry 6 }

cmEthernetTrafficPortStatsESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent."
     ::= { cmEthernetTrafficPortStatsEntry 7 }

cmEthernetTrafficPortStatsESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected."
     ::= { cmEthernetTrafficPortStatsEntry 8 }

cmEthernetTrafficPortStatsESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected."
     ::= { cmEthernetTrafficPortStatsEntry 9 }

cmEthernetTrafficPortStatsESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetTrafficPortStatsEntry 10 }

cmEthernetTrafficPortStatsESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected."
     ::= { cmEthernetTrafficPortStatsEntry 11 }

cmEthernetTrafficPortStatsESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent."
     ::= { cmEthernetTrafficPortStatsEntry 12 }

cmEthernetTrafficPortStatsESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected."
     ::= { cmEthernetTrafficPortStatsEntry 13 }

cmEthernetTrafficPortStatsESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent."
     ::= { cmEthernetTrafficPortStatsEntry 14 }

cmEthernetTrafficPortStatsESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received."
     ::= { cmEthernetTrafficPortStatsEntry 15 }

cmEthernetTrafficPortStatsESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received."
     ::= { cmEthernetTrafficPortStatsEntry 16 }

cmEthernetTrafficPortStatsESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames Dropped On Sending Direction."
     ::= { cmEthernetTrafficPortStatsEntry 17 }

cmEthernetTrafficPortStatsESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets Dropped On Receiving Direction."
     ::= { cmEthernetTrafficPortStatsEntry 18 }

cmEthernetTrafficPortStatsESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received."
     ::= { cmEthernetTrafficPortStatsEntry 19 }

cmEthernetTrafficPortStatsESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 20 }

cmEthernetTrafficPortStatsESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 21 }

cmEthernetTrafficPortStatsESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 22 }

cmEthernetTrafficPortStatsESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 23 }

cmEthernetTrafficPortStatsESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 24 }

cmEthernetTrafficPortStatsESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 25 }

cmEthernetTrafficPortStatsESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received."
     ::= { cmEthernetTrafficPortStatsEntry 26 }

cmEthernetTrafficPortStatsESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent."
     ::= { cmEthernetTrafficPortStatsEntry 27 }

cmEthernetTrafficPortStatsESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Packets received."
     ::= { cmEthernetTrafficPortStatsEntry 28 }

cmEthernetTrafficPortStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmEthernetTrafficPortStatsEntry 29 }
     
cmEthernetTrafficPortStatsL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed."
     ::= { cmEthernetTrafficPortStatsEntry 30 }

cmEthernetTrafficPortStatsLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetTrafficPortStatsEntry 31 }

cmEthernetTrafficPortStatsLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortStatsEntry 32 }

cmEthernetTrafficPortStatsOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortStatsEntry 33 }

cmEthernetTrafficPortStatsOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortStatsEntry 34 }


cmEthernetTrafficPortStatsAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD)."
     ::= { cmEthernetTrafficPortStatsEntry 35 }

cmEthernetTrafficPortStatsAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD)."
     ::= { cmEthernetTrafficPortStatsEntry 36 }

cmEthernetTrafficPortStatsABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmEthernetTrafficPortStatsEntry 37 }

cmEthernetTrafficPortStatsABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmEthernetTrafficPortStatsEntry 38 }

cmEthernetTrafficPortStatsATFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The AFP tagged frames dropped." 
     ::= { cmEthernetTrafficPortStatsEntry 39 }

cmEthernetTrafficPortStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)." 
     ::= { cmEthernetTrafficPortStatsEntry 40 }

cmEthernetTrafficPortStatsTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetTrafficPortStatsEntry 41 }

cmEthernetTrafficPortStatsLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packet Discarded due to Lookup Fail."
     ::= { cmEthernetTrafficPortStatsEntry 42 }

cmEthernetTrafficPortStatsPSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Proctetion Switch Count."
     ::= { cmEthernetTrafficPortStatsEntry 43 }

--
-- Agg Ethernet Port History Table
--
cmEthernetTrafficPortHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetTrafficPortHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Agg Ethernet Port related statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 22 }

cmEthernetTrafficPortHistoryEntry OBJECT-TYPE
    SYNTAX      CmEthernetTrafficPortHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetTrafficPortHistoryTable.
             Entries exist in this table for each Agg Ethernet port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, 
            cmEthernetTrafficPortStatsIndex, cmEthernetTrafficPortHistoryIndex }
    ::= { cmEthernetTrafficPortHistoryTable 1 }

CmEthernetTrafficPortHistoryEntry ::= SEQUENCE {
    cmEthernetTrafficPortHistoryIndex                 Integer32,
    cmEthernetTrafficPortHistoryTime                  DateAndTime,
    cmEthernetTrafficPortHistoryValid                 TruthValue,
    cmEthernetTrafficPortHistoryAction                CmPmBinAction,
    cmEthernetTrafficPortHistoryESBF                  PerfCounter64,
    cmEthernetTrafficPortHistoryESBP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESBS                  PerfCounter64,
    cmEthernetTrafficPortHistoryESC                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESCAE                 PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESDE                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESF                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESFS                  PerfCounter64,
    cmEthernetTrafficPortHistoryESJ                   PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESMF                  PerfCounter64,
    cmEthernetTrafficPortHistoryESMP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryESO                   PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortHistoryESOF                  PerfCounter64,
    cmEthernetTrafficPortHistoryESOP                  PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortHistoryESP                   PerfCounter64,--ES,EH,HC
    cmEthernetTrafficPortHistoryESP64                 PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP65                 PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP128                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP256                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP512                PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP1024               PerfCounter64,--ES,   HC
    cmEthernetTrafficPortHistoryESP1519               PerfCounter64,
    cmEthernetTrafficPortHistoryESUF                  PerfCounter64,
    cmEthernetTrafficPortHistoryESUP                  PerfCounter64,--ES,EH
    cmEthernetTrafficPortHistoryL2CPFD                PerfCounter64,
    cmEthernetTrafficPortHistoryL2CPFP                PerfCounter64,
    cmEthernetTrafficPortHistoryLES                   PerfCounter64,
    cmEthernetTrafficPortHistoryLBC                   Integer32,
    cmEthernetTrafficPortHistoryOPT                   Integer32,
    cmEthernetTrafficPortHistoryOPR                   Integer32,
    cmEthernetTrafficPortHistoryAUFD                  PerfCounter64,
    cmEthernetTrafficPortHistoryAPFD                  PerfCounter64,
    cmEthernetTrafficPortHistoryABRRx                 PerfCounter64,
    cmEthernetTrafficPortHistoryABRTx                 PerfCounter64,
    cmEthernetTrafficPortHistoryATFD                  PerfCounter64,
    cmEthernetTrafficPortHistoryUAS                   PerfCounter64,
    cmEthernetTrafficPortHistoryTemp                  Integer32,
    cmEthernetTrafficPortHistoryLkupFails             PerfCounter64,
    cmEthernetTrafficPortHistoryPSC                   PerfCounter64
}

cmEthernetTrafficPortHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Port statistics entry."
    ::= { cmEthernetTrafficPortHistoryEntry 1 }

cmEthernetTrafficPortHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmEthernetTrafficPortHistoryEntry 2 }

cmEthernetTrafficPortHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmEthernetTrafficPortHistoryEntry 3 }

cmEthernetTrafficPortHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmEthernetTrafficPortHistoryEntry 4 }

cmEthernetTrafficPortHistoryESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent."
     ::= { cmEthernetTrafficPortHistoryEntry 5 }

cmEthernetTrafficPortHistoryESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 6 }

cmEthernetTrafficPortHistoryESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent."
     ::= { cmEthernetTrafficPortHistoryEntry 7 }

cmEthernetTrafficPortHistoryESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected."
     ::= { cmEthernetTrafficPortHistoryEntry 8 }

cmEthernetTrafficPortHistoryESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected."
     ::= { cmEthernetTrafficPortHistoryEntry 9 }

cmEthernetTrafficPortHistoryESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmEthernetTrafficPortHistoryEntry 10 }

cmEthernetTrafficPortHistoryESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected."
     ::= { cmEthernetTrafficPortHistoryEntry 11 }

cmEthernetTrafficPortHistoryESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent."
     ::= { cmEthernetTrafficPortHistoryEntry 12 }

cmEthernetTrafficPortHistoryESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected."
     ::= { cmEthernetTrafficPortHistoryEntry 13 }

cmEthernetTrafficPortHistoryESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent."
     ::= { cmEthernetTrafficPortHistoryEntry 14 }

cmEthernetTrafficPortHistoryESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received."
     ::= { cmEthernetTrafficPortHistoryEntry 15 }

cmEthernetTrafficPortHistoryESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received."
     ::= { cmEthernetTrafficPortHistoryEntry 16 }

cmEthernetTrafficPortHistoryESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames Dropped On Sending Direction."
     ::= { cmEthernetTrafficPortHistoryEntry 17 }

cmEthernetTrafficPortHistoryESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets Dropped On Receiving Direction."
     ::= { cmEthernetTrafficPortHistoryEntry 18 }

cmEthernetTrafficPortHistoryESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received."
     ::= { cmEthernetTrafficPortHistoryEntry 19 }

cmEthernetTrafficPortHistoryESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 20 }

cmEthernetTrafficPortHistoryESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 21 }

cmEthernetTrafficPortHistoryESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 22 }

cmEthernetTrafficPortHistoryESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 23 }

cmEthernetTrafficPortHistoryESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 24 }

cmEthernetTrafficPortHistoryESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 25 }

cmEthernetTrafficPortHistoryESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received."
     ::= { cmEthernetTrafficPortHistoryEntry 26 }

cmEthernetTrafficPortHistoryESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent."
     ::= { cmEthernetTrafficPortHistoryEntry 27 }

cmEthernetTrafficPortHistoryESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Packets received."
     ::= { cmEthernetTrafficPortHistoryEntry 28 }

cmEthernetTrafficPortHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmEthernetTrafficPortHistoryEntry 29 }
     
cmEthernetTrafficPortHistoryL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed."
     ::= { cmEthernetTrafficPortHistoryEntry 30 }

cmEthernetTrafficPortHistoryLES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     deprecated
     DESCRIPTION
         "Line Errored Seconds detected.  These are
          incremented if a False Carrier or Errored Symbol event occurs
          since the last 1-second poll.
          This object is deprecated."
     ::= { cmEthernetTrafficPortHistoryEntry 31 }

cmEthernetTrafficPortHistoryLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current (in mA) for the physical layer.  This is
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortHistoryEntry 32 }

cmEthernetTrafficPortHistoryOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Transmit (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortHistoryEntry 33 }

cmEthernetTrafficPortHistoryOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Optical Power Receive (in dBm) for the physical layer.  This is 
          applicable only if the media type for the Port is fiber."
     ::= { cmEthernetTrafficPortHistoryEntry 34 }


cmEthernetTrafficPortHistoryAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD)."
     ::= { cmEthernetTrafficPortHistoryEntry 35 }

cmEthernetTrafficPortHistoryAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD)."
     ::= { cmEthernetTrafficPortHistoryEntry 36 }

cmEthernetTrafficPortHistoryABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmEthernetTrafficPortHistoryEntry 37 }

cmEthernetTrafficPortHistoryABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmEthernetTrafficPortHistoryEntry 38 }

cmEthernetTrafficPortHistoryATFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The AFP tagged frames dropped." 
     ::= { cmEthernetTrafficPortHistoryEntry 39 }

cmEthernetTrafficPortHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)." 
     ::= { cmEthernetTrafficPortHistoryEntry 40 }

cmEthernetTrafficPortHistoryTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The temperature of the physical layer when media type is fiber."
     ::= { cmEthernetTrafficPortHistoryEntry 41 }

cmEthernetTrafficPortHistoryLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packet Discarded due to Lookup Fail."
     ::= { cmEthernetTrafficPortHistoryEntry 42 }

cmEthernetTrafficPortHistoryPSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Protection Switch Count."
     ::= { cmEthernetTrafficPortHistoryEntry 43 }

--
-- Agg Ethernet Port Threshold Table
--
cmEthernetTrafficPortThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetTrafficPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Agg Ethernet
             Port Thresholds."
    ::= { cmPerfObjects 23 }

cmEthernetTrafficPortThresholdEntry OBJECT-TYPE
    SYNTAX      CmEthernetTrafficPortThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetTrafficPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, 
            cmEthernetTrafficPortStatsIndex, cmEthernetTrafficPortThresholdIndex }
    ::= { cmEthernetTrafficPortThresholdTable 1 }

CmEthernetTrafficPortThresholdEntry ::= SEQUENCE {
    cmEthernetTrafficPortThresholdIndex       Integer32,
    cmEthernetTrafficPortThresholdInterval    CmPmIntervalType,
    cmEthernetTrafficPortThresholdVariable    VariablePointer,
    cmEthernetTrafficPortThresholdValueLo     Unsigned32,
    cmEthernetTrafficPortThresholdValueHi     Unsigned32,
    cmEthernetTrafficPortThresholdMonValue    Counter64
}

cmEthernetTrafficPortThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmEthernetTrafficPortThresholdTable."
    ::= { cmEthernetTrafficPortThresholdEntry 1 }

cmEthernetTrafficPortThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmEthernetTrafficPortThresholdEntry 2 }

cmEthernetTrafficPortThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmEthernetTrafficPortThresholdEntry 3 }

cmEthernetTrafficPortThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmEthernetTrafficPortThresholdEntry 4 }

cmEthernetTrafficPortThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmEthernetTrafficPortThresholdEntry 5 }

cmEthernetTrafficPortThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmEthernetTrafficPortThresholdVariable."
    ::= { cmEthernetTrafficPortThresholdEntry 6 }

--
-- Agg Ethernet Port Threshold Variance Table
--
cmEthernetTrafficPortThresholdVarTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmEthernetTrafficPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Agg Ethernet
             Port Threshold variances."
    ::= { cmPerfObjects 24 }

cmEthernetTrafficPortThresholdVarEntry OBJECT-TYPE
    SYNTAX      CmEthernetTrafficPortThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmEthernetTrafficPortThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, 
            cmEthernetTrafficPortStatsIndex}
    ::= { cmEthernetTrafficPortThresholdVarTable 1 }

CmEthernetTrafficPortThresholdVarEntry ::= SEQUENCE {
    cmEthernetTrafficPortThresholdVarOprVariance    Integer32,
    cmEthernetTrafficPortThresholdVarOptVariance    Integer32
}

cmEthernetTrafficPortThresholdVarOprVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power received (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetTrafficPortThresholdVarEntry 1 }

cmEthernetTrafficPortThresholdVarOptVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power transmitted (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { cmEthernetTrafficPortThresholdVarEntry 2 }

--
-- Agg Ethernet FlowPoint Current Statistics Table
--
cmFlowPointStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowPointStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of flowPiont statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 25 }

cmFlowPointStatsEntry OBJECT-TYPE
    SYNTAX      CmFlowPointStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowPointStatsTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, cmFlowPointIndex, cmFlowPointStatsIndex }
    ::= { cmFlowPointStatsTable 1 }

CmFlowPointStatsEntry ::= SEQUENCE {
    cmFlowPointStatsIndex                 Integer32,
    cmFlowPointStatsIntervalType          CmPmIntervalType,
    cmFlowPointStatsValid                 TruthValue,
    cmFlowPointStatsAction                CmPmBinAction,
    cmFlowPointStatsL2CPFD                PerfCounter64,
    cmFlowPointStatsABRRx                 PerfCounter64,
    cmFlowPointStatsABRRLRx               PerfCounter64,
    cmFlowPointStatsUAS                   PerfCounter64,
    cmFlowPointStatsSES                   PerfCounter64, 
    cmFlowPointStatsFMG                   PerfCounter64,
    cmFlowPointStatsFMY                   PerfCounter64,
    cmFlowPointStatsFMRD                  PerfCounter64,
    cmFlowPointStatsFTD                   PerfCounter64,
    cmFlowPointStatsBytesIn               PerfCounter64,
    cmFlowPointStatsBytesOut              PerfCounter64,
    cmFlowPointStatsFREDD                 PerfCounter64,
    cmFlowPointStatsFACLD                 PerfCounter64,
    cmFlowPointStatsFMYD                  PerfCounter64,
    cmFlowPointStatsFMGD                  PerfCounter64,
    cmFlowPointStatsFD                    PerfCounter64,
    cmFlowPointStatsFMCD                  PerfCounter64,
    cmFlowPointStatsFBCD                  PerfCounter64,
    cmFlowPointStatsBT                    PerfCounter64,
    cmFlowPointStatsFLD                   PerfCounter64
}

cmFlowPointStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Ethernet Flow statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmFlowPointStatsEntry 1 }

cmFlowPointStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmFlowPointStatsEntry 2 }

cmFlowPointStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmFlowPointStatsEntry 3 }

cmFlowPointStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmFlowPointStatsEntry 4 }

cmFlowPointStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmFlowPointStatsEntry 5 }

cmFlowPointStatsABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmFlowPointStatsEntry 6 }

cmFlowPointStatsABRRLRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited." 
     ::= { cmFlowPointStatsEntry 7 }

cmFlowPointStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)."
     ::= { cmFlowPointStatsEntry 8 }

cmFlowPointStatsSES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES)."
     ::= { cmFlowPointStatsEntry 9 }

cmFlowPointStatsFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green."
     ::= { cmFlowPointStatsEntry 10 }

cmFlowPointStatsFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow."
     ::= { cmFlowPointStatsEntry 11 }

cmFlowPointStatsFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded."
     ::= { cmFlowPointStatsEntry 12 }

cmFlowPointStatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Tail Dropped."
     ::= { cmFlowPointStatsEntry 13 }

cmFlowPointStatsBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes In in the ingress direction."
     ::= { cmFlowPointStatsEntry 14 }

cmFlowPointStatsBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes Out in the ingress direction."
     ::= { cmFlowPointStatsEntry 15 }
     
cmFlowPointStatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames WRED Dropped in the ingress direction."
     ::= { cmFlowPointStatsEntry 16 }

cmFlowPointStatsFACLD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of ACL Frames Discarded in the ingress directions."
     ::= { cmFlowPointStatsEntry 17 }

cmFlowPointStatsFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow and Dropped in the ingress direction."
     ::= { cmFlowPointStatsEntry 18 }

cmFlowPointStatsFMGD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green and Dropped in the ingress direction."
     ::= { cmFlowPointStatsEntry 19 }

cmFlowPointStatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Dequeued by the 8 queues of egress flow point."
     ::= { cmFlowPointStatsEntry 20 }

cmFlowPointStatsFMCD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame with Destination MAC MultiCast dropped in ingress FP."
     ::= { cmFlowPointStatsEntry 21 }

cmFlowPointStatsFBCD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame with Destination MAC BroadCast dropped in ingress FP."
     ::= { cmFlowPointStatsEntry 22 }

cmFlowPointStatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of bytes dequeued(Transmitted) by the 8 queues in egress FP."
     ::= { cmFlowPointStatsEntry 23 }

cmFlowPointStatsFLD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of frames dropped in STM1-4-ET or FE-36E card in egress direction."
     ::= { cmFlowPointStatsEntry 24 }

--
-- Agg Ethernet Flow History Table
--
cmFlowPointHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowPointHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of flowPiont statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 26 }

cmFlowPointHistoryEntry OBJECT-TYPE
    SYNTAX      CmFlowPointHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowPointHistoryTable.
             Entries exist in this table for each FlowPoint."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, cmFlowPointIndex, cmFlowPointStatsIndex, cmFlowPointHistoryIndex }
    ::= { cmFlowPointHistoryTable 1 }

CmFlowPointHistoryEntry ::= SEQUENCE {
    cmFlowPointHistoryIndex               Integer32,
    cmFlowPointHistoryTime                DateAndTime,
    cmFlowPointHistoryValid               TruthValue,
    cmFlowPointHistoryAction              CmPmBinAction,
    cmFlowPointHistoryL2CPFD              PerfCounter64,
    cmFlowPointHistoryABRRx               PerfCounter64,
    cmFlowPointHistoryABRRLRx             PerfCounter64,
    cmFlowPointHistoryUAS                 PerfCounter64,
    cmFlowPointHistorySES                 PerfCounter64, 
    cmFlowPointHistoryFMG                 PerfCounter64,
    cmFlowPointHistoryFMY                 PerfCounter64,
    cmFlowPointHistoryFMRD                PerfCounter64,
    cmFlowPointHistoryFTD                 PerfCounter64,
    cmFlowPointHistoryBytesIn             PerfCounter64,
    cmFlowPointHistoryBytesOut            PerfCounter64,
    cmFlowPointHistoryFREDD               PerfCounter64,
    cmFlowPointHistoryFACLD               PerfCounter64,
    cmFlowPointHistoryFMYD                PerfCounter64,
    cmFlowPointHistoryFMGD                PerfCounter64,
    cmFlowPointHistoryFD                  PerfCounter64,
    cmFlowPointHistoryFMCD                PerfCounter64,
    cmFlowPointHistoryFBCD                PerfCounter64,
    cmFlowPointHistoryBT                  PerfCounter64,
    cmFlowPointHistoryFLD                 PerfCounter64
}

cmFlowPointHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Ethernet FlowPoint History statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmFlowPointHistoryEntry 1 }

cmFlowPointHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { cmFlowPointHistoryEntry 2 }

cmFlowPointHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmFlowPointHistoryEntry 3 }

cmFlowPointHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmFlowPointHistoryEntry 4 }

cmFlowPointHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmFlowPointHistoryEntry 5 }

cmFlowPointHistoryABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmFlowPointHistoryEntry 6 }

cmFlowPointHistoryABRRLRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate, rate limited." 
     ::= { cmFlowPointHistoryEntry 7 }

cmFlowPointHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)."
     ::= { cmFlowPointHistoryEntry 8 }

cmFlowPointHistorySES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES)."
     ::= { cmFlowPointHistoryEntry 9 }

cmFlowPointHistoryFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green."
     ::= { cmFlowPointHistoryEntry 10 }

cmFlowPointHistoryFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow."
     ::= { cmFlowPointHistoryEntry 11 }

cmFlowPointHistoryFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Red and Discarded."
     ::= { cmFlowPointHistoryEntry 12 }

cmFlowPointHistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Tail Dropped."
     ::= { cmFlowPointHistoryEntry 13 }

cmFlowPointHistoryBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes In in the ingress direction."
     ::= { cmFlowPointHistoryEntry 14 }

cmFlowPointHistoryBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Bytes Out in the ingress direction."
     ::= { cmFlowPointHistoryEntry 15 }
     
cmFlowPointHistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames WRED Dropped in the ingress direction."
     ::= { cmFlowPointHistoryEntry 16 }

cmFlowPointHistoryFACLD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of ACL Frames Discarded in the ingress directions."
     ::= { cmFlowPointHistoryEntry 17 }

cmFlowPointHistoryFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Yellow and Dropped in the ingress direction."
     ::= { cmFlowPointHistoryEntry 18 }

cmFlowPointHistoryFMGD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Marked Green and Dropped in the ingress direction."
     ::= { cmFlowPointHistoryEntry 19 }

cmFlowPointHistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frames Dequeued by the 8 queues of egress flow point."
     ::= { cmFlowPointHistoryEntry 20 }

cmFlowPointHistoryFMCD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame with Destination MAC MultiCast dropped in ingress FP."
     ::= { cmFlowPointHistoryEntry 21 }

cmFlowPointHistoryFBCD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Frame with Destination MAC BroadCast dropped in ingress FP."
     ::= { cmFlowPointHistoryEntry 22 }

cmFlowPointHistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of bytes dequeued(Transmitted) by the 8 queues in egress FP."
     ::= { cmFlowPointHistoryEntry 23 }

cmFlowPointHistoryFLD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of frames dropped in STM1-4-ET or FE-36E card in egress direction."
     ::= { cmFlowPointHistoryEntry 24 }

--
-- Agg FlowPoint Threshold Table
--
cmFlowPointThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowPointThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Agg FlowPoint Thresholds."
    ::= { cmPerfObjects 27 }

cmFlowPointThresholdEntry OBJECT-TYPE
    SYNTAX      CmFlowPointThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowPointThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, 
            cmFlowPointIndex, cmFlowPointStatsIndex, cmFlowPointThresholdIndex }
    ::= { cmFlowPointThresholdTable 1 }

CmFlowPointThresholdEntry ::= SEQUENCE {
    cmFlowPointThresholdIndex       Integer32,
    cmFlowPointThresholdInterval    CmPmIntervalType,
    cmFlowPointThresholdVariable    VariablePointer,
    cmFlowPointThresholdValueLo     Unsigned32,
    cmFlowPointThresholdValueHi     Unsigned32,
    cmFlowPointThresholdMonValue    Counter64
}

cmFlowPointThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmFlowPointThresholdTable."
    ::= { cmFlowPointThresholdEntry 1 }

cmFlowPointThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmFlowPointThresholdEntry 2 }

cmFlowPointThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be sampled."
    ::= { cmFlowPointThresholdEntry 3 }

cmFlowPointThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmFlowPointThresholdEntry 4 }

cmFlowPointThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmFlowPointThresholdEntry 5 }

cmFlowPointThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmFlowPointThresholdVariable."
    ::= { cmFlowPointThresholdEntry 6 }

cmOAMFlowPointStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmOAMFlowPointStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of flowPiont statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 28 }

cmOAMFlowPointStatsEntry OBJECT-TYPE
    SYNTAX      CmOAMFlowPointStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmOAMFlowPointStatsTable.
             Entries exist in this table for each EVC."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, cmOAMFlowPointIndex, cmOAMFlowPointStatsIndex }
    ::= { cmOAMFlowPointStatsTable 1 }

CmOAMFlowPointStatsEntry ::= SEQUENCE {
    cmOAMFlowPointStatsIndex                 Integer32,
    cmOAMFlowPointStatsIntervalType          CmPmIntervalType,
    cmOAMFlowPointStatsValid                 TruthValue,
    cmOAMFlowPointStatsAction                CmPmBinAction,
    cmOAMFlowPointStatsUAS                   PerfCounter64,
    cmOAMFlowPointStatsSES                   PerfCounter64    
}

cmOAMFlowPointStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Ethernet Flow statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmOAMFlowPointStatsEntry 1 }

cmOAMFlowPointStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmOAMFlowPointStatsEntry 2 }

cmOAMFlowPointStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmOAMFlowPointStatsEntry 3 }

cmOAMFlowPointStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmOAMFlowPointStatsEntry 4 }

cmOAMFlowPointStatsUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)."
     ::= { cmOAMFlowPointStatsEntry 5 }

cmOAMFlowPointStatsSES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES)."
     ::= { cmOAMFlowPointStatsEntry 6 }

--
-- OAM FlowPoint History Table
--
cmOAMFlowPointHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmOAMFlowPointHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of flowPiont statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 29 }

cmOAMFlowPointHistoryEntry OBJECT-TYPE
    SYNTAX      CmOAMFlowPointHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmOAMFlowPointHistoryTable.
             Entries exist in this table for each OAMFlowPoint."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, cmOAMFlowPointIndex, cmOAMFlowPointStatsIndex, cmOAMFlowPointHistoryIndex }
    ::= { cmOAMFlowPointHistoryTable 1 }

CmOAMFlowPointHistoryEntry ::= SEQUENCE {
    cmOAMFlowPointHistoryIndex               Integer32,
    cmOAMFlowPointHistoryTime                DateAndTime,
    cmOAMFlowPointHistoryValid               TruthValue,
    cmOAMFlowPointHistoryAction              CmPmBinAction,
    cmOAMFlowPointHistoryUAS                 PerfCounter64,
    cmOAMFlowPointHistorySES                 PerfCounter64

}

cmOAMFlowPointHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Ethernet OAMFlowPoint History statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmOAMFlowPointHistoryEntry 1 }

cmOAMFlowPointHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { cmOAMFlowPointHistoryEntry 2 }

cmOAMFlowPointHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmOAMFlowPointHistoryEntry 3 }

cmOAMFlowPointHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmOAMFlowPointHistoryEntry 4 }

cmOAMFlowPointHistoryUAS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Unavailable Seconds (UAS)."
     ::= { cmOAMFlowPointHistoryEntry 5 }

cmOAMFlowPointHistorySES OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Severely Errored Seconds (ES)."
     ::= { cmOAMFlowPointHistoryEntry 6 }

--
-- OAMFlowPoint Threshold Table
--
cmOAMFlowPointThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmOAMFlowPointThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Agg OAMFlowPoint Thresholds."
    ::= { cmPerfObjects 30 }

cmOAMFlowPointThresholdEntry OBJECT-TYPE
    SYNTAX      CmOAMFlowPointThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmOAMFlowPointThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex, 
            cmOAMFlowPointIndex, cmOAMFlowPointStatsIndex, cmOAMFlowPointThresholdIndex }
    ::= { cmOAMFlowPointThresholdTable 1 }

CmOAMFlowPointThresholdEntry ::= SEQUENCE {
    cmOAMFlowPointThresholdIndex       Integer32,
    cmOAMFlowPointThresholdInterval    CmPmIntervalType,
    cmOAMFlowPointThresholdVariable    VariablePointer,
    cmOAMFlowPointThresholdValueLo     Unsigned32,
    cmOAMFlowPointThresholdValueHi     Unsigned32,
    cmOAMFlowPointThresholdMonValue    Counter64
}

cmOAMFlowPointThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmOAMFlowPointThresholdTable."
    ::= { cmOAMFlowPointThresholdEntry 1 }

cmOAMFlowPointThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmOAMFlowPointThresholdEntry 2 }

cmOAMFlowPointThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be sampled."
    ::= { cmOAMFlowPointThresholdEntry 3 }

cmOAMFlowPointThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmOAMFlowPointThresholdEntry 4 }

cmOAMFlowPointThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmOAMFlowPointThresholdEntry 5 }

cmOAMFlowPointThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmOAMFlowPointThresholdVariable."
    ::= { cmOAMFlowPointThresholdEntry 6 }


--
-- Quality of Service(QOS) FlowPointPolicer Current Statistics Table
--
cmQosPolicerV2StatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosPolicerV2StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Policer statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 31 }

cmQosPolicerV2StatsEntry OBJECT-TYPE
    SYNTAX      CmQosPolicerV2StatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosPolicerV2StatsTable.
             Entries exist in this table."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosPolicerV2Index, 
            cmQosPolicerV2StatsIndex }
    ::= { cmQosPolicerV2StatsTable 1 }

CmQosPolicerV2StatsEntry ::= SEQUENCE {
    cmQosPolicerV2StatsIndex           Integer32,
    cmQosPolicerV2StatsIntervalType    CmPmIntervalType,
    cmQosPolicerV2StatsValid           TruthValue,
    cmQosPolicerV2StatsAction          CmPmBinAction,
    cmQosPolicerV2StatsFMG             PerfCounter64,
    cmQosPolicerV2StatsFMY             PerfCounter64,
    cmQosPolicerV2StatsFMYD            PerfCounter64,
    cmQosPolicerV2StatsFMRD            PerfCounter64,
    cmQosPolicerV2StatsBytesIn         PerfCounter64,
    cmQosPolicerV2StatsBytesOut        PerfCounter64,
    cmQosPolicerV2StatsABR             PerfCounter64
}

cmQosPolicerV2StatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
            this QOS Policer statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmQosPolicerV2StatsEntry 1 }

cmQosPolicerV2StatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmQosPolicerV2StatsEntry 2 }

cmQosPolicerV2StatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosPolicerV2StatsEntry 3 }

cmQosPolicerV2StatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosPolicerV2StatsEntry 4 }

cmQosPolicerV2StatsFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Green."
     ::= { cmQosPolicerV2StatsEntry 5 }

cmQosPolicerV2StatsFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow."
     ::= { cmQosPolicerV2StatsEntry 6 }

cmQosPolicerV2StatsFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow and Dropped."
     ::= { cmQosPolicerV2StatsEntry 7 }

cmQosPolicerV2StatsFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Red and Discarded."
     ::= { cmQosPolicerV2StatsEntry 8 }

cmQosPolicerV2StatsBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes received by the Policer."
     ::= { cmQosPolicerV2StatsEntry 9 }

cmQosPolicerV2StatsBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes transmitted by the Policer."
     ::= { cmQosPolicerV2StatsEntry 10 }

cmQosPolicerV2StatsABR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate  on the Policer."
     ::= { cmQosPolicerV2StatsEntry 11 }

--
-- Quality of Service(QOS) FlowPoint Policer History Statistics Table
--
cmQosPolicerV2HistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosPolicerV2HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS Policer statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 32 }

cmQosPolicerV2HistoryEntry OBJECT-TYPE
    SYNTAX      CmQosPolicerV2HistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosPolicerV2HistoryTable.
             Entries exist in this table for each flowpoint."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosPolicerV2Index, 
            cmQosPolicerV2StatsIndex, cmQosPolicerV2HistoryIndex }
    ::= { cmQosPolicerV2HistoryTable 1 }

CmQosPolicerV2HistoryEntry ::= SEQUENCE {
    cmQosPolicerV2HistoryIndex           Integer32,
    cmQosPolicerV2HistoryTime            DateAndTime,
    cmQosPolicerV2HistoryValid           TruthValue,
    cmQosPolicerV2HistoryAction          CmPmBinAction,
    cmQosPolicerV2HistoryFMG             PerfCounter64,
    cmQosPolicerV2HistoryFMY             PerfCounter64,
    cmQosPolicerV2HistoryFMYD            PerfCounter64,
    cmQosPolicerV2HistoryFMRD            PerfCounter64,
    cmQosPolicerV2HistoryBytesIn         PerfCounter64,
    cmQosPolicerV2HistoryBytesOut        PerfCounter64,
    cmQosPolicerV2HistoryABR             PerfCounter64
}

cmQosPolicerV2HistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Policer history entry."
    ::= { cmQosPolicerV2HistoryEntry 1 }

cmQosPolicerV2HistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmQosPolicerV2HistoryEntry 2 }

cmQosPolicerV2HistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosPolicerV2HistoryEntry 3 }

cmQosPolicerV2HistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosPolicerV2HistoryEntry 4 }

cmQosPolicerV2HistoryFMG OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Green."
     ::= { cmQosPolicerV2HistoryEntry 5 }

cmQosPolicerV2HistoryFMY OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow."
     ::= { cmQosPolicerV2HistoryEntry 6 }

cmQosPolicerV2HistoryFMYD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Yellow and Dropped."
     ::= { cmQosPolicerV2HistoryEntry 7 }

cmQosPolicerV2HistoryFMRD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Marked Red and Discarded."
     ::= { cmQosPolicerV2HistoryEntry 8 }

cmQosPolicerV2HistoryBytesIn OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes received by the Policer."
     ::= { cmQosPolicerV2HistoryEntry 9 }

cmQosPolicerV2HistoryBytesOut OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Number of bytes transmitted by the Policer."
     ::= { cmQosPolicerV2HistoryEntry 10 }

cmQosPolicerV2HistoryABR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate  on the Policer."
     ::= { cmQosPolicerV2HistoryEntry 11 }

--
-- FlowPoint QOS Policer Threshold Table
--
cmQosPolicerV2ThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosPolicerV2ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             Policer Thresholds."
    ::= { cmPerfObjects 33 }

cmQosPolicerV2ThresholdEntry OBJECT-TYPE
    SYNTAX      CmQosPolicerV2ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosPolicerV2ThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosPolicerV2Index, 
            cmQosPolicerV2StatsIndex, cmQosPolicerV2ThresholdIndex }
    ::= { cmQosPolicerV2ThresholdTable 1 }

CmQosPolicerV2ThresholdEntry ::= SEQUENCE {
    cmQosPolicerV2ThresholdIndex       Integer32,
    cmQosPolicerV2ThresholdInterval    CmPmIntervalType,
    cmQosPolicerV2ThresholdVariable    VariablePointer,
    cmQosPolicerV2ThresholdValueLo     Unsigned32,
    cmQosPolicerV2ThresholdValueHi     Unsigned32,
    cmQosPolicerV2ThresholdMonValue    Counter64
}

cmQosPolicerV2ThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmQosPolicerV2ThresholdTable."
    ::= { cmQosPolicerV2ThresholdEntry 1 }

cmQosPolicerV2ThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmQosPolicerV2ThresholdEntry 2 }

cmQosPolicerV2ThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmQosPolicerV2ThresholdEntry 3 }

cmQosPolicerV2ThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmQosPolicerV2ThresholdEntry 4 }

cmQosPolicerV2ThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmQosPolicerV2ThresholdEntry 5 }

cmQosPolicerV2ThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmQosPolicerV2ThresholdVariable."
    ::= { cmQosPolicerV2ThresholdEntry 6 }


--
-- Quality of Service(QOS) FlowPoint Queue Current Statistics Table
--
cmQosShaperV2StatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperV2StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS ShaperV2 statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 34 }

cmQosShaperV2StatsEntry OBJECT-TYPE
    SYNTAX      CmQosShaperV2StatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperV2StatsTable.
             Entries exist in this table."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosShaperV2Index, 
            cmQosShaperV2StatsIndex }
    ::= { cmQosShaperV2StatsTable 1 }

CmQosShaperV2StatsEntry ::= SEQUENCE {
    cmQosShaperV2StatsIndex           Integer32,
    cmQosShaperV2StatsIntervalType    CmPmIntervalType,
    cmQosShaperV2StatsValid           TruthValue,
    cmQosShaperV2StatsAction          CmPmBinAction,
    cmQosShaperV2StatsBT              PerfCounter64,
    cmQosShaperV2StatsBTD             PerfCounter64,
    cmQosShaperV2StatsFD              PerfCounter64,
    cmQosShaperV2StatsFTD             PerfCounter64,
    cmQosShaperV2StatsABRRL           PerfCounter64,
    cmQosShaperV2StatsBREDD           PerfCounter64,
    cmQosShaperV2StatsFREDD           PerfCounter64
}

cmQosShaperV2StatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS ShaperV2 statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmQosShaperV2StatsEntry 1 }

cmQosShaperV2StatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmQosShaperV2StatsEntry 2 }

cmQosShaperV2StatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosShaperV2StatsEntry 3 }

cmQosShaperV2StatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosShaperV2StatsEntry 4 }

cmQosShaperV2StatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmQosShaperV2StatsEntry 5 }

cmQosShaperV2StatsBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmQosShaperV2StatsEntry 6 }

cmQosShaperV2StatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmQosShaperV2StatsEntry 7 }

cmQosShaperV2StatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmQosShaperV2StatsEntry 8 }

cmQosShaperV2StatsABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmQosShaperV2StatsEntry 9 }

cmQosShaperV2StatsBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Red Frame Dropped."
     ::= { cmQosShaperV2StatsEntry 10 }

cmQosShaperV2StatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames WRED Dropped."
     ::= { cmQosShaperV2StatsEntry 11 }
     
--
-- Quality of Service(QOS) Queue History Statistics Table
--
cmQosShaperV2HistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperV2HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of QOS ShaperV2 statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 35 }

cmQosShaperV2HistoryEntry OBJECT-TYPE
    SYNTAX      CmQosShaperV2HistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperV2HistoryTable.
             Entries exist in this table for each flowpoint."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosShaperV2Index, 
            cmQosShaperV2StatsIndex, cmQosShaperV2HistoryIndex }
    ::= { cmQosShaperV2HistoryTable 1 }

CmQosShaperV2HistoryEntry ::= SEQUENCE {
    cmQosShaperV2HistoryIndex           Integer32,
    cmQosShaperV2HistoryTime            DateAndTime,
    cmQosShaperV2HistoryValid           TruthValue,
    cmQosShaperV2HistoryAction          CmPmBinAction,
    cmQosShaperV2HistoryBT              PerfCounter64,
    cmQosShaperV2HistoryBTD             PerfCounter64,
    cmQosShaperV2HistoryFD              PerfCounter64,
    cmQosShaperV2HistoryFTD             PerfCounter64,
    cmQosShaperV2HistoryABRRL           PerfCounter64,
    cmQosShaperV2HistoryBREDD           PerfCounter64,
    cmQosShaperV2HistoryFREDD           PerfCounter64
}


cmQosShaperV2HistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS ShaperV2 statistics entry."
    ::= { cmQosShaperV2HistoryEntry 1 }

cmQosShaperV2HistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmQosShaperV2HistoryEntry 2 }

cmQosShaperV2HistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmQosShaperV2HistoryEntry 3 }

cmQosShaperV2HistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmQosShaperV2HistoryEntry 4 }

cmQosShaperV2HistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmQosShaperV2HistoryEntry 5 }

cmQosShaperV2HistoryBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmQosShaperV2HistoryEntry 6 }

cmQosShaperV2HistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmQosShaperV2HistoryEntry 7 }

cmQosShaperV2HistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmQosShaperV2HistoryEntry 8 }

cmQosShaperV2HistoryABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmQosShaperV2HistoryEntry 9 }

cmQosShaperV2HistoryBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Red Frame Dropped."
     ::= { cmQosShaperV2HistoryEntry 10 }

cmQosShaperV2HistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames WRED Dropped."
     ::= { cmQosShaperV2HistoryEntry 11 }

--
-- QOS Queue Threshold Table
--
cmQosShaperV2ThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmQosShaperV2ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             ShaperV2 Thresholds."
    ::= { cmPerfObjects 36 }

cmQosShaperV2ThresholdEntry OBJECT-TYPE
    SYNTAX      CmQosShaperV2ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmQosShaperV2ThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmFlowPointIndex, cmQosShaperV2Index, 
            cmQosShaperV2StatsIndex, cmQosShaperV2ThresholdIndex }
    ::= { cmQosShaperV2ThresholdTable 1 }

CmQosShaperV2ThresholdEntry ::= SEQUENCE {
    cmQosShaperV2ThresholdIndex       Integer32,
    cmQosShaperV2ThresholdInterval    CmPmIntervalType,
    cmQosShaperV2ThresholdVariable    VariablePointer,
    cmQosShaperV2ThresholdValueLo     Unsigned32,
    cmQosShaperV2ThresholdValueHi     Unsigned32,
    cmQosShaperV2ThresholdMonValue    Counter64
}

cmQosShaperV2ThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmQosShaperV2ThresholdTable."
    ::= { cmQosShaperV2ThresholdEntry 1 }

cmQosShaperV2ThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmQosShaperV2ThresholdEntry 2 }

cmQosShaperV2ThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmQosShaperV2ThresholdEntry 3 }

cmQosShaperV2ThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmQosShaperV2ThresholdEntry 4 }

cmQosShaperV2ThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmQosShaperV2ThresholdEntry 5 }

cmQosShaperV2ThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmQosShaperV2ThresholdVariable."
    ::= { cmQosShaperV2ThresholdEntry 6 }


--
-- Agg LAG Current Statistics Table
--
cmLagStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmLagStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Agg Lag related statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 37 }

cmLagStatsEntry OBJECT-TYPE
    SYNTAX      CmLagStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmLagStatsTable.
             Entries exist in this table for each Ethernet interface/port."
    INDEX { neIndex, f3LagIndex, cmLagStatsIndex }
    ::= { cmLagStatsTable 1 }

CmLagStatsEntry ::= SEQUENCE {
    cmLagStatsIndex                 Integer32,
    cmLagStatsIntervalType          CmPmIntervalType,
    cmLagStatsValid                 TruthValue,
    cmLagStatsAction                CmPmBinAction,
    cmLagStatsESBF                  PerfCounter64,
    cmLagStatsESBP                  PerfCounter64,--ES,EH
    cmLagStatsESBS                  PerfCounter64,
    cmLagStatsESC                   PerfCounter64,--ES,EH
    cmLagStatsESCAE                 PerfCounter64,--ES,EH
    cmLagStatsESDE                  PerfCounter64,--ES,EH
    cmLagStatsESF                   PerfCounter64,--ES,EH
    cmLagStatsESFS                  PerfCounter64,
    cmLagStatsESJ                   PerfCounter64,--ES,EH
    cmLagStatsESMF                  PerfCounter64,
    cmLagStatsESMP                  PerfCounter64,--ES,EH
    cmLagStatsESO                   PerfCounter64,--ES,EH,HC
    cmLagStatsESOF                  PerfCounter64,
    cmLagStatsESOP                  PerfCounter64,--ES,EH,HC
    cmLagStatsESP                   PerfCounter64,--ES,EH,HC
    cmLagStatsESP64                 PerfCounter64,--ES,   HC
    cmLagStatsESP65                 PerfCounter64,--ES,   HC
    cmLagStatsESP128                PerfCounter64,--ES,   HC
    cmLagStatsESP256                PerfCounter64,--ES,   HC
    cmLagStatsESP512                PerfCounter64,--ES,   HC
    cmLagStatsESP1024               PerfCounter64,--ES,   HC
    cmLagStatsESP1519               PerfCounter64,
    cmLagStatsESUF                  PerfCounter64,
    cmLagStatsESUP                  PerfCounter64,--ES,EH
    cmLagStatsL2CPFD                PerfCounter64,
    cmLagStatsL2CPFP                PerfCounter64,
    
    cmLagStatsAUFD                  PerfCounter64,
    cmLagStatsAPFD                  PerfCounter64,
    cmLagStatsABRRx                 PerfCounter64,
    cmLagStatsABRTx                 PerfCounter64,
    cmLagStatsATFD                  PerfCounter64,
    cmLagStatsLkupFails             PerfCounter64
}

cmLagStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..4)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Agg Lag statistics entry. 
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmLagStatsEntry 1 }

cmLagStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmLagStatsEntry 2 }

cmLagStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmLagStatsEntry 3 }

cmLagStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmLagStatsEntry 4 }

cmLagStatsESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent."
     ::= { cmLagStatsEntry 5 }

cmLagStatsESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received."
     ::= { cmLagStatsEntry 6 }

cmLagStatsESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent."
     ::= { cmLagStatsEntry 7 }

cmLagStatsESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected."
     ::= { cmLagStatsEntry 8 }

cmLagStatsESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected."
     ::= { cmLagStatsEntry 9 }

cmLagStatsESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmLagStatsEntry 10 }

cmLagStatsESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected."
     ::= { cmLagStatsEntry 11 }

cmLagStatsESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent."
     ::= { cmLagStatsEntry 12 }

cmLagStatsESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected."
     ::= { cmLagStatsEntry 13 }

cmLagStatsESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent."
     ::= { cmLagStatsEntry 14 }

cmLagStatsESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received."
     ::= { cmLagStatsEntry 15 }

cmLagStatsESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received."
     ::= { cmLagStatsEntry 16 }

cmLagStatsESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames Dropped On Sending Direction."
     ::= { cmLagStatsEntry 17 }

cmLagStatsESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets Dropped On Receiving Direction."
     ::= { cmLagStatsEntry 18 }

cmLagStatsESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received."
     ::= { cmLagStatsEntry 19 }

cmLagStatsESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received."
     ::= { cmLagStatsEntry 20 }

cmLagStatsESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received."
     ::= { cmLagStatsEntry 21 }

cmLagStatsESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received."
     ::= { cmLagStatsEntry 22 }

cmLagStatsESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received."
     ::= { cmLagStatsEntry 23 }

cmLagStatsESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received."
     ::= { cmLagStatsEntry 24 }

cmLagStatsESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received."
     ::= { cmLagStatsEntry 25 }

cmLagStatsESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received."
     ::= { cmLagStatsEntry 26 }

cmLagStatsESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent."
     ::= { cmLagStatsEntry 27 }

cmLagStatsESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Packets received."
     ::= { cmLagStatsEntry 28 }

cmLagStatsL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmLagStatsEntry 29 }
     
cmLagStatsL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed."
     ::= { cmLagStatsEntry 30 }

cmLagStatsAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD)."
     ::= { cmLagStatsEntry 31 }

cmLagStatsAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD)."
     ::= { cmLagStatsEntry 32 }

cmLagStatsABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmLagStatsEntry 33 }

cmLagStatsABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmLagStatsEntry 34 }

cmLagStatsATFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The AFP tagged frames dropped." 
     ::= { cmLagStatsEntry 35 }

cmLagStatsLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packet Discarded due to Lookup Fail." 
     ::= { cmLagStatsEntry 36 }

--
-- Agg LAG History Table
--
cmLagHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmLagHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Lag related statistics.  
             These reflect the history data."
    ::= { cmPerfObjects 38 }

cmLagHistoryEntry OBJECT-TYPE
    SYNTAX      CmLagHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmLagHistoryTable.
             Entries exist in this table for each Lag."
    INDEX { neIndex, f3LagIndex, cmLagStatsIndex, cmLagHistoryIndex }
    ::= { cmLagHistoryTable 1 }

CmLagHistoryEntry ::= SEQUENCE {
    cmLagHistoryIndex                 Integer32,
    cmLagHistoryTime                  DateAndTime,
    cmLagHistoryValid                 TruthValue,
    cmLagHistoryAction                CmPmBinAction,
    cmLagHistoryESBF                  PerfCounter64,
    cmLagHistoryESBP                  PerfCounter64,--ES,EH
    cmLagHistoryESBS                  PerfCounter64,
    cmLagHistoryESC                   PerfCounter64,--ES,EH
    cmLagHistoryESCAE                 PerfCounter64,--ES,EH
    cmLagHistoryESDE                  PerfCounter64,--ES,EH
    cmLagHistoryESF                   PerfCounter64,--ES,EH
    cmLagHistoryESFS                  PerfCounter64,
    cmLagHistoryESJ                   PerfCounter64,--ES,EH
    cmLagHistoryESMF                  PerfCounter64,
    cmLagHistoryESMP                  PerfCounter64,--ES,EH
    cmLagHistoryESO                   PerfCounter64,--ES,EH,HC
    cmLagHistoryESOF                  PerfCounter64,
    cmLagHistoryESOP                  PerfCounter64,--ES,EH,HC
    cmLagHistoryESP                   PerfCounter64,--ES,EH,HC
    cmLagHistoryESP64                 PerfCounter64,--ES,   HC
    cmLagHistoryESP65                 PerfCounter64,--ES,   HC
    cmLagHistoryESP128                PerfCounter64,--ES,   HC
    cmLagHistoryESP256                PerfCounter64,--ES,   HC
    cmLagHistoryESP512                PerfCounter64,--ES,   HC
    cmLagHistoryESP1024               PerfCounter64,--ES,   HC
    cmLagHistoryESP1519               PerfCounter64,
    cmLagHistoryESUF                  PerfCounter64,
    cmLagHistoryESUP                  PerfCounter64,--ES,EH
    cmLagHistoryL2CPFD                PerfCounter64,
    cmLagHistoryL2CPFP                PerfCounter64,
    cmLagHistoryAUFD                  PerfCounter64,
    cmLagHistoryAPFD                  PerfCounter64,
    cmLagHistoryABRRx                 PerfCounter64,
    cmLagHistoryABRTx                 PerfCounter64,
    cmLagHistoryATFD                  PerfCounter64,
    cmLagHistoryLkupFails             PerfCounter64
}

cmLagHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Ethernet Port statistics entry."
    ::= { cmLagHistoryEntry 1 }

cmLagHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmLagHistoryEntry 2 }

cmLagHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmLagHistoryEntry 3 }

cmLagHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmLagHistoryEntry 4 }

cmLagHistoryESBF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames sent."
     ::= { cmLagHistoryEntry 5 }

cmLagHistoryESBP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Broadcast frames received."
     ::= { cmLagHistoryEntry 6 }

cmLagHistoryESBS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes sent."
     ::= { cmLagHistoryEntry 7 }

cmLagHistoryESC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Collisions detected."
     ::= { cmLagHistoryEntry 8 }

cmLagHistoryESCAE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "CRC Aligned Errors detected."
     ::= { cmLagHistoryEntry 9 }

cmLagHistoryESDE OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Drop Events detected."
     ::= { cmLagHistoryEntry 10 }

cmLagHistoryESF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Fragments detected."
     ::= { cmLagHistoryEntry 11 }

cmLagHistoryESFS OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames sent."
     ::= { cmLagHistoryEntry 12 }

cmLagHistoryESJ OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Jabbers detected."
     ::= { cmLagHistoryEntry 13 }

cmLagHistoryESMF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Frames sent."
     ::= { cmLagHistoryEntry 14 }

cmLagHistoryESMP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Multicast Packets received."
     ::= { cmLagHistoryEntry 15 }

cmLagHistoryESO OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Octets received."
     ::= { cmLagHistoryEntry 16 }

cmLagHistoryESOF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Frames Dropped On Sending Direction."
     ::= { cmLagHistoryEntry 17 }

cmLagHistoryESOP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Oversize Packets Dropped On Receiving Direction."
     ::= { cmLagHistoryEntry 18 }

cmLagHistoryESP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packets received."
     ::= { cmLagHistoryEntry 19 }

cmLagHistoryESP64 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "64 Octet Frames received."
     ::= { cmLagHistoryEntry 20 }

cmLagHistoryESP65 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "65 to 127 Octet Frames received."
     ::= { cmLagHistoryEntry 21 }

cmLagHistoryESP128 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "128 to 255 Octet Frames received."
     ::= { cmLagHistoryEntry 22 }

cmLagHistoryESP256 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "256 to 511 Octet Frames received."
     ::= { cmLagHistoryEntry 23 }

cmLagHistoryESP512 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "512 to 1023 Octet Frames received."
     ::= { cmLagHistoryEntry 24 }

cmLagHistoryESP1024 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1024 to 1518 Octet Frames received."
     ::= { cmLagHistoryEntry 25 }

cmLagHistoryESP1519 OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "1519 to Max MTU Octet Frames received."
     ::= { cmLagHistoryEntry 26 }

cmLagHistoryESUF OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Frames sent."
     ::= { cmLagHistoryEntry 27 }

cmLagHistoryESUP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Unicast Packets received."
     ::= { cmLagHistoryEntry 28 }

cmLagHistoryL2CPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Discarded."
     ::= { cmLagHistoryEntry 29 }
     
cmLagHistoryL2CPFP OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Layer 2 Control Protocol Frames Processed."
     ::= { cmLagHistoryEntry 30 }

cmLagHistoryAUFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Untagged Frames
          Discarded (AUFD)."
     ::= { cmLagHistoryEntry 31 }

cmLagHistoryAPFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of AFP (Acceptable Frame Policy) Priority Tagged Frames
          Discarded (APFD)."
     ::= { cmLagHistoryEntry 32 }

cmLagHistoryABRRx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmLagHistoryEntry 33 }

cmLagHistoryABRTx OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average bit rate." 
     ::= { cmLagHistoryEntry 34 }

cmLagHistoryATFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The AFP tagged frames dropped." 
     ::= { cmLagHistoryEntry 35 }

cmLagHistoryLkupFails OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Packet Discarded due to Lookup Fail." 
     ::= { cmLagHistoryEntry 36 }

--
-- Agg Lag Threshold Table
--
cmLagThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmLagThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Agg Lag Thresholds."
    ::= { cmPerfObjects 39 }

cmLagThresholdEntry OBJECT-TYPE
    SYNTAX      CmLagThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmLagThresholdTable."
    INDEX { neIndex, f3LagIndex, cmLagStatsIndex, cmLagThresholdIndex }
    ::= { cmLagThresholdTable 1 }

CmLagThresholdEntry ::= SEQUENCE {
    cmLagThresholdIndex       Integer32,
    cmLagThresholdInterval    CmPmIntervalType,
    cmLagThresholdVariable    VariablePointer,
    cmLagThresholdValueLo     Unsigned32,
    cmLagThresholdValueHi     Unsigned32,
    cmLagThresholdMonValue    Counter64
}

cmLagThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmLagThresholdTable."
    ::= { cmLagThresholdEntry 1 }

cmLagThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmLagThresholdEntry 2 }

cmLagThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmLagThresholdEntry 3 }

cmLagThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmLagThresholdEntry 4 }

cmLagThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmLagThresholdEntry 5 }

cmLagThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmLagThresholdVariable."
    ::= { cmLagThresholdEntry 6 }

--
-- LAG - Quality of Service Qos Queue Current Statistics Table
--
cmTrafficPortQosShaperStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmTrafficPortQosShaperStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of LAG Shaper performance monitoring data. 
             These reflect the current data. (Now it is only relevant 
             for LAG ports and that the slotIndex value will 
             be 254 and cmEthernetTrafficPortIndex is the LAG index)"
    ::= { cmPerfObjects 40 }

cmTrafficPortQosShaperStatsEntry OBJECT-TYPE
    SYNTAX      CmTrafficPortQosShaperStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmTrafficPortQosShaperStatsTable.
             Entries exist in this table."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmTrafficPortQosShaperIndex, 
            cmTrafficPortQosShaperStatsIndex
          }
    ::= { cmTrafficPortQosShaperStatsTable 1 }

CmTrafficPortQosShaperStatsEntry ::= SEQUENCE {
    cmTrafficPortQosShaperStatsIndex           Integer32,
    cmTrafficPortQosShaperStatsIntervalType    CmPmIntervalType,
    cmTrafficPortQosShaperStatsValid           TruthValue,
    cmTrafficPortQosShaperStatsAction          CmPmBinAction,
    cmTrafficPortQosShaperStatsBT              PerfCounter64,
    cmTrafficPortQosShaperStatsBTD             PerfCounter64,
    cmTrafficPortQosShaperStatsFD              PerfCounter64,
    cmTrafficPortQosShaperStatsFTD             PerfCounter64,
    cmTrafficPortQosShaperStatsABRRL           PerfCounter64,
    cmTrafficPortQosShaperStatsBREDD           PerfCounter64,
    cmTrafficPortQosShaperStatsFREDD           PerfCounter64
}

cmTrafficPortQosShaperStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Traffic Port QOS Shaper statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { cmTrafficPortQosShaperStatsEntry 1 }

cmTrafficPortQosShaperStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { cmTrafficPortQosShaperStatsEntry 2 }

cmTrafficPortQosShaperStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmTrafficPortQosShaperStatsEntry 3 }

cmTrafficPortQosShaperStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmTrafficPortQosShaperStatsEntry 4 }

cmTrafficPortQosShaperStatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmTrafficPortQosShaperStatsEntry 5 }

cmTrafficPortQosShaperStatsBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmTrafficPortQosShaperStatsEntry 6 }

cmTrafficPortQosShaperStatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmTrafficPortQosShaperStatsEntry 7 }

cmTrafficPortQosShaperStatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmTrafficPortQosShaperStatsEntry 8 }

cmTrafficPortQosShaperStatsABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmTrafficPortQosShaperStatsEntry 9 }

cmTrafficPortQosShaperStatsBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Red Frame Dropped."
     ::= { cmTrafficPortQosShaperStatsEntry 10 }

cmTrafficPortQosShaperStatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames WRED Dropped."
     ::= { cmTrafficPortQosShaperStatsEntry 11 }


--
-- Traffic Port Quality of Service(QOS) Queue History Statistics Table
--
cmTrafficPortQosShaperHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmTrafficPortQosShaperHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Traffic Port QOS Shaper statistics.
             These reflect the history data.(Now it is only relevant 
             for LAG ports and that the slotIndex value will 
             be 254 and cmEthernetTrafficPortIndex is the LAG index)"
    ::= { cmPerfObjects 41 }

cmTrafficPortQosShaperHistoryEntry OBJECT-TYPE
    SYNTAX      CmTrafficPortQosShaperHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmTrafficPortQosShaperHistoryTable.
             Entries exist in this table for each flowpoint."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmTrafficPortQosShaperIndex, 
            cmTrafficPortQosShaperStatsIndex, 
            cmTrafficPortQosShaperHistoryIndex 
          }
    ::= { cmTrafficPortQosShaperHistoryTable 1 }

CmTrafficPortQosShaperHistoryEntry ::= SEQUENCE {
    cmTrafficPortQosShaperHistoryIndex           Integer32,
    cmTrafficPortQosShaperHistoryTime            DateAndTime,
    cmTrafficPortQosShaperHistoryValid           TruthValue,
    cmTrafficPortQosShaperHistoryAction          CmPmBinAction,
    cmTrafficPortQosShaperHistoryBT              PerfCounter64,
    cmTrafficPortQosShaperHistoryBTD             PerfCounter64,
    cmTrafficPortQosShaperHistoryFD              PerfCounter64,
    cmTrafficPortQosShaperHistoryFTD             PerfCounter64,
    cmTrafficPortQosShaperHistoryABRRL           PerfCounter64,
    cmTrafficPortQosShaperHistoryBREDD           PerfCounter64,
    cmTrafficPortQosShaperHistoryFREDD           PerfCounter64
}


cmTrafficPortQosShaperHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Traffic Port QOS Shaper statistics entry."
    ::= { cmTrafficPortQosShaperHistoryEntry 1 }

cmTrafficPortQosShaperHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { cmTrafficPortQosShaperHistoryEntry 2 }

cmTrafficPortQosShaperHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { cmTrafficPortQosShaperHistoryEntry 3 }

cmTrafficPortQosShaperHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { cmTrafficPortQosShaperHistoryEntry 4 }

cmTrafficPortQosShaperHistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { cmTrafficPortQosShaperHistoryEntry 5 }

cmTrafficPortQosShaperHistoryBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { cmTrafficPortQosShaperHistoryEntry 6 }

cmTrafficPortQosShaperHistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { cmTrafficPortQosShaperHistoryEntry 7 }

cmTrafficPortQosShaperHistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { cmTrafficPortQosShaperHistoryEntry 8 }

cmTrafficPortQosShaperHistoryABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { cmTrafficPortQosShaperHistoryEntry 9 }

cmTrafficPortQosShaperHistoryBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Red Frame Dropped."
     ::= { cmTrafficPortQosShaperHistoryEntry 10 }

cmTrafficPortQosShaperHistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames WRED Dropped."
     ::= { cmTrafficPortQosShaperHistoryEntry 11 }


--
-- Traffic Port QOS Queue Threshold Table
--
cmTrafficPortQosShaperThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmTrafficPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of Traffic Port QOS
             Shaper Thresholds.(Now it is only relevant 
             for LAG ports and that the slotIndex value will 
             be 254 and cmEthernetTrafficPortIndex is the LAG index)"
    ::= { cmPerfObjects 42 }

cmTrafficPortQosShaperThresholdEntry OBJECT-TYPE
    SYNTAX      CmTrafficPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmTrafficPortQosShaperThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetTrafficPortIndex,
            cmTrafficPortQosShaperIndex, 
            cmTrafficPortQosShaperStatsIndex, 
            cmTrafficPortQosShaperThresholdIndex }
    ::= { cmTrafficPortQosShaperThresholdTable 1 }

CmTrafficPortQosShaperThresholdEntry ::= SEQUENCE {
    cmTrafficPortQosShaperThresholdIndex       Integer32,
    cmTrafficPortQosShaperThresholdInterval    CmPmIntervalType,
    cmTrafficPortQosShaperThresholdVariable    VariablePointer,
    cmTrafficPortQosShaperThresholdValueLo     Unsigned32,
    cmTrafficPortQosShaperThresholdValueHi     Unsigned32,
    cmTrafficPortQosShaperThresholdMonValue    Counter64
}

cmTrafficPortQosShaperThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        cmTrafficPortQosShaperThresholdTable."
    ::= { cmTrafficPortQosShaperThresholdEntry 1 }

cmTrafficPortQosShaperThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { cmTrafficPortQosShaperThresholdEntry 2 }

cmTrafficPortQosShaperThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { cmTrafficPortQosShaperThresholdEntry 3 }

cmTrafficPortQosShaperThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { cmTrafficPortQosShaperThresholdEntry 4 }

cmTrafficPortQosShaperThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { cmTrafficPortQosShaperThresholdEntry 5 }

cmTrafficPortQosShaperThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to cmTrafficPortQosShaperThresholdVariable."
    ::= { cmTrafficPortQosShaperThresholdEntry 6 }


--
-- Port Level Quality of Service(QOS) Shaper Current Statistics Table
--
f3NetPortQosShaperStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3NetPortQosShaperStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Network Port Level QOS Shaper statistics.  
             These reflect the current data."
    ::= { cmPerfObjects 43 }

f3NetPortQosShaperStatsEntry OBJECT-TYPE
    SYNTAX      F3NetPortQosShaperStatsEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmNetPortQosShaperStatsTable.
             Entries exist in this table for each QOS Shaper per Network Port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex,
            f3NetPortQosShaperIndex, f3NetPortQosShaperStatsIndex }
    ::= { f3NetPortQosShaperStatsTable 1 }

F3NetPortQosShaperStatsEntry ::= SEQUENCE {
    f3NetPortQosShaperStatsIndex             Integer32,
    f3NetPortQosShaperStatsIntervalType      CmPmIntervalType,
    f3NetPortQosShaperStatsValid             TruthValue,
    f3NetPortQosShaperStatsAction            CmPmBinAction,
    f3NetPortQosShaperStatsBT                PerfCounter64,
    f3NetPortQosShaperStatsBTD               PerfCounter64,
    f3NetPortQosShaperStatsFD                PerfCounter64,
    f3NetPortQosShaperStatsFTD               PerfCounter64,
    f3NetPortQosShaperStatsBR                PerfCounter64,
    f3NetPortQosShaperStatsFR                PerfCounter64,
    f3NetPortQosShaperStatsABRRL             PerfCounter64,
    f3NetPortQosShaperStatsBREDD             PerfCounter64,
    f3NetPortQosShaperStatsFREDD             PerfCounter64
}

f3NetPortQosShaperStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry.
            Index 1 corresponds to 15minute, index 2 to 1 day and index
            3 corresponds to rollover."
    ::= { f3NetPortQosShaperStatsEntry 1 }

f3NetPortQosShaperStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { f3NetPortQosShaperStatsEntry 2 }

f3NetPortQosShaperStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { f3NetPortQosShaperStatsEntry 3 }

f3NetPortQosShaperStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { f3NetPortQosShaperStatsEntry 4 }

f3NetPortQosShaperStatsBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { f3NetPortQosShaperStatsEntry 5 }

f3NetPortQosShaperStatsBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { f3NetPortQosShaperStatsEntry 6 }

f3NetPortQosShaperStatsFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { f3NetPortQosShaperStatsEntry 7 }

f3NetPortQosShaperStatsFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { f3NetPortQosShaperStatsEntry 8 }

f3NetPortQosShaperStatsBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { f3NetPortQosShaperStatsEntry 9 }

f3NetPortQosShaperStatsFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { f3NetPortQosShaperStatsEntry 10 }

f3NetPortQosShaperStatsABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { f3NetPortQosShaperStatsEntry 11 }

f3NetPortQosShaperStatsBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { f3NetPortQosShaperStatsEntry 12 }

f3NetPortQosShaperStatsFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { f3NetPortQosShaperStatsEntry 13 }


--
-- Quality of Service(QOS) Shaper History Statistics Table
--
f3NetPortQosShaperHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3NetPortQosShaperHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of Network Port Level QOS Shaper statistics.
             These reflect the history data."
    ::= { cmPerfObjects 44 }

f3NetPortQosShaperHistoryEntry OBJECT-TYPE
    SYNTAX      F3NetPortQosShaperHistoryEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmNetPortQosShaperHistoryTable.
             Entries exist in this table for each Shaper per Network Port."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex,
            f3NetPortQosShaperIndex, f3NetPortQosShaperStatsIndex, f3NetPortQosShaperHistoryIndex }
    ::= { f3NetPortQosShaperHistoryTable 1 }

F3NetPortQosShaperHistoryEntry ::= SEQUENCE {
    f3NetPortQosShaperHistoryIndex             Integer32,
    f3NetPortQosShaperHistoryTime              DateAndTime,
    f3NetPortQosShaperHistoryValid             TruthValue,
    f3NetPortQosShaperHistoryAction            CmPmBinAction,
    f3NetPortQosShaperHistoryBT                PerfCounter64,
    f3NetPortQosShaperHistoryBTD               PerfCounter64,
    f3NetPortQosShaperHistoryFD                PerfCounter64,
    f3NetPortQosShaperHistoryFTD               PerfCounter64,
    f3NetPortQosShaperHistoryBR                PerfCounter64,
    f3NetPortQosShaperHistoryFR                PerfCounter64,
    f3NetPortQosShaperHistoryABRRL             PerfCounter64,
    f3NetPortQosShaperHistoryBREDD             PerfCounter64,
    f3NetPortQosShaperHistoryFREDD             PerfCounter64
}

f3NetPortQosShaperHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this QOS Shaper statistics entry."
    ::= { f3NetPortQosShaperHistoryEntry 1 }

f3NetPortQosShaperHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation." 
    ::= { f3NetPortQosShaperHistoryEntry 2 }

f3NetPortQosShaperHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { f3NetPortQosShaperHistoryEntry 3 }

f3NetPortQosShaperHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { f3NetPortQosShaperHistoryEntry 4 }

f3NetPortQosShaperHistoryBT OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Transmitted."
     ::= { f3NetPortQosShaperHistoryEntry 5 }

f3NetPortQosShaperHistoryBTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Tail Dropped."
     ::= { f3NetPortQosShaperHistoryEntry 6 }

f3NetPortQosShaperHistoryFD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Dequeued."
     ::= { f3NetPortQosShaperHistoryEntry 7 }

f3NetPortQosShaperHistoryFTD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Tail Dropped."
     ::= { f3NetPortQosShaperHistoryEntry 8 }

f3NetPortQosShaperHistoryBR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Replicated."
     ::= { f3NetPortQosShaperHistoryEntry 9 }

f3NetPortQosShaperHistoryFR OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Replicated."
     ::= { f3NetPortQosShaperHistoryEntry 10 }

f3NetPortQosShaperHistoryABRRL OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Average Bit Rate - Rate Limited."
     ::= { f3NetPortQosShaperHistoryEntry 11 }

f3NetPortQosShaperHistoryBREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Bytes Random Early Discard, Dropped."
     ::= { f3NetPortQosShaperHistoryEntry 12 }

f3NetPortQosShaperHistoryFREDD OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frames Random Early Discard, Dropped."
     ::= { f3NetPortQosShaperHistoryEntry 13 }

--
-- Network Port QOS Shaper Threshold Table
--
f3NetPortQosShaperThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3NetPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of QOS
             Shaper Thresholds."
    ::= { cmPerfObjects 45 }

f3NetPortQosShaperThresholdEntry OBJECT-TYPE
    SYNTAX      F3NetPortQosShaperThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmNetPortQosShaperThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, cmEthernetNetPortIndex,
            f3NetPortQosShaperIndex, f3NetPortQosShaperStatsIndex, 
            f3NetPortQosShaperThresholdIndex }
    ::= { f3NetPortQosShaperThresholdTable 1 }

F3NetPortQosShaperThresholdEntry ::= SEQUENCE {
    f3NetPortQosShaperThresholdIndex       Integer32,
    f3NetPortQosShaperThresholdInterval    CmPmIntervalType,
    f3NetPortQosShaperThresholdVariable    VariablePointer,
    f3NetPortQosShaperThresholdValueLo     Unsigned32,
    f3NetPortQosShaperThresholdValueHi     Unsigned32,
    f3NetPortQosShaperThresholdMonValue    Counter64
}

f3NetPortQosShaperThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        f3NetPortQosShaperThresholdTable."
    ::= { f3NetPortQosShaperThresholdEntry 1 }

f3NetPortQosShaperThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { f3NetPortQosShaperThresholdEntry 2 }

f3NetPortQosShaperThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { f3NetPortQosShaperThresholdEntry 3 }

f3NetPortQosShaperThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { f3NetPortQosShaperThresholdEntry 4 }

f3NetPortQosShaperThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { f3NetPortQosShaperThresholdEntry 5 }

f3NetPortQosShaperThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to f3NetPortQosShaperThresholdVariable."
    ::= { f3NetPortQosShaperThresholdEntry 6 }

--
-- OCn/STM Statistics Table
--
ocnStmStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF OcnStmStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of OCn/STM interface related statistics.
            These provide the current data."
    ::= { cmPerfObjects 46 }

ocnStmStatsEntry OBJECT-TYPE
    SYNTAX      OcnStmStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ocnStmStatsTable. An entry exists
             in this table for each OCn/STM interface."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex, 
            ocnStmStatsIndex }
    ::= { ocnStmStatsTable 1 }

OcnStmStatsEntry ::= SEQUENCE {
    ocnStmStatsIndex                 Integer32,
    ocnStmStatsIntervalType          CmPmIntervalType,
    ocnStmStatsValid                 TruthValue,
    ocnStmStatsAction                CmPmBinAction,
    ocnStmStatsLineLBC               Integer32,
    ocnStmStatsLineOPT               Integer32,
    ocnStmStatsLineOPR               Integer32,
    ocnStmStatsLineTemp              Integer32,
    ocnStmStatsLinePSC               PerfCounter64,
    ocnStmStatsLineESs               PerfCounter64,
    ocnStmStatsLineSESs              PerfCounter64,
    ocnStmStatsLineCVs               PerfCounter64,
    ocnStmStatsLineUASs              PerfCounter64,
    ocnStmStatsLineFCs               PerfCounter64,
    ocnStmStatsLineFarEndESs         PerfCounter64,
    ocnStmStatsLineFarEndSESs        PerfCounter64,
    ocnStmStatsLineFarEndCVs         PerfCounter64,
    ocnStmStatsLineFarEndUASs        PerfCounter64,
    ocnStmStatsSectionESs            PerfCounter64,
    ocnStmStatsSectionSESs           PerfCounter64,
    ocnStmStatsSectionCVs            PerfCounter64,
    ocnStmStatsSectionSEFs           PerfCounter64,
    ocnStmStatsSectionUASs           PerfCounter64
}

ocnStmStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this OCn/STM interface statistics entry."
    ::= { ocnStmStatsEntry 1 }

ocnStmStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { ocnStmStatsEntry 2 }

ocnStmStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { ocnStmStatsEntry 3 }

ocnStmStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { ocnStmStatsEntry 4 }

ocnStmStatsLineLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current retrieved from the SFP."
     ::= { ocnStmStatsEntry 5 }

ocnStmStatsLineOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Transmit Optical Power retrieved from the SFP."
     ::= { ocnStmStatsEntry 6 }

ocnStmStatsLineOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Received Optical Power retrieved from the SFP."
     ::= { ocnStmStatsEntry 7 }

ocnStmStatsLineTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Temperature retrieved from the SFP."
     ::= { ocnStmStatsEntry 8 }

ocnStmStatsLinePSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Protection Switch Counts retrieved from the SFP."
     ::= { ocnStmStatsEntry 9 }

ocnStmStatsLineESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         Line in the current bin."
     ::= { ocnStmStatsEntry 10 }

ocnStmStatsLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH Line in the current bin."
     ::= { ocnStmStatsEntry 11 }

ocnStmStatsLineCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         Line in the current bin."
     ::= { ocnStmStatsEntry 12 }

ocnStmStatsLineUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by a SONET/SDH 
         Line in the current bin."
     ::= { ocnStmStatsEntry 13 }

ocnStmStatsLineFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Failure Count encountered by a SONET/SDH 
         Line in the current bin."
     ::= { ocnStmStatsEntry 14 }

ocnStmStatsLineFarEndESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by a 
         SONET/SDH Section in the current bin."
     ::= { ocnStmStatsEntry 15 }

ocnStmStatsLineFarEndSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by a SONET/SDH Line in the current bin."
     ::= { ocnStmStatsEntry 16 }

ocnStmStatsLineFarEndCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by a 
         SONET/SDH Line in the current bin."
     ::= { ocnStmStatsEntry 17 }

ocnStmStatsLineFarEndUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Unavailable Seconds encountered by 
         a SONET/SDH Line in the current bin."
     ::= { ocnStmStatsEntry 18 }

ocnStmStatsSectionESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         Section in the current bin."
     ::= { ocnStmStatsEntry 19 }

ocnStmStatsSectionSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH Section in the current bin."
     ::= { ocnStmStatsEntry 20 }

ocnStmStatsSectionCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         Section in the current bin."
     ::= { ocnStmStatsEntry 21 }

ocnStmStatsSectionSEFs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Serverely Errored Frames Seconds encountered 
         by a SONET/SDH Section in the current bin."
     ::= { ocnStmStatsEntry 22 }

ocnStmStatsSectionUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered 
         by a SONET/SDH Section in the current bin."
     ::= { ocnStmStatsEntry 23 }


--
-- OCn/STM History Table
--
ocnStmHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF OcnStmHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of OCn/STM interface related statistics. 
            These reflect the history data."
    ::= { cmPerfObjects 47 }

ocnStmHistoryEntry OBJECT-TYPE
    SYNTAX      OcnStmHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ocnStmHistoryTable. An entry exists
             in this table for each OCn/STM interface."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex, 
            ocnStmStatsIndex, ocnStmHistoryIndex }
    ::= { ocnStmHistoryTable 1 }

OcnStmHistoryEntry ::= SEQUENCE {
    ocnStmHistoryIndex           Integer32,
    ocnStmHistoryTime            DateAndTime,
    ocnStmHistoryValid           TruthValue,
    ocnStmHistoryAction          CmPmBinAction,
    ocnStmHistoryLineLBC         Integer32,
    ocnStmHistoryLineOPT         Integer32,
    ocnStmHistoryLineOPR         Integer32,
    ocnStmHistoryLineTemp        Integer32,
    ocnStmHistoryLinePSC         PerfCounter64,
    ocnStmHistoryLineESs         PerfCounter64,
    ocnStmHistoryLineSESs        PerfCounter64,
    ocnStmHistoryLineCVs         PerfCounter64,
    ocnStmHistoryLineUASs        PerfCounter64,
    ocnStmHistoryLineFCs         PerfCounter64,
    ocnStmHistoryLineFarEndESs   PerfCounter64,
    ocnStmHistoryLineFarEndSESs  PerfCounter64,
    ocnStmHistoryLineFarEndCVs   PerfCounter64,
    ocnStmHistoryLineFarEndUASs  PerfCounter64,
    ocnStmHistorySectionESs      PerfCounter64,
    ocnStmHistorySectionSESs     PerfCounter64,
    ocnStmHistorySectionCVs      PerfCounter64,
    ocnStmHistorySectionSEFs     PerfCounter64,
    ocnStmHistorySectionUASs     PerfCounter64
}

ocnStmHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this OCn/STM interface statistics entry."
    ::= { ocnStmHistoryEntry 1 }

ocnStmHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { ocnStmHistoryEntry 2 }

ocnStmHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { ocnStmHistoryEntry 3 }

ocnStmHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { ocnStmHistoryEntry 4 }

ocnStmHistoryLineLBC OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Laser Bias Current retrieved from the SFP."
     ::= { ocnStmHistoryEntry 5 }

ocnStmHistoryLineOPT OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Transmit Optical Power retrieved from the SFP."
     ::= { ocnStmHistoryEntry 6 }

ocnStmHistoryLineOPR OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Received Optical Power retrieved from the SFP."
     ::= { ocnStmHistoryEntry 7 }

ocnStmHistoryLineTemp OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Temperature retrieved from the SFP."
     ::= { ocnStmHistoryEntry 8 }

ocnStmHistoryLinePSC OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Protection Switch Counts retrieved from the SFP."
     ::= { ocnStmHistoryEntry 9 }

ocnStmHistoryLineESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         Line in the history bin."
     ::= { ocnStmHistoryEntry 10 }

ocnStmHistoryLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH Line in the history bin."
     ::= { ocnStmHistoryEntry 11 }

ocnStmHistoryLineCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         Line in the history bin."
     ::= { ocnStmHistoryEntry 12 }

ocnStmHistoryLineUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by a SONET/SDH 
         Line in the history bin."
     ::= { ocnStmHistoryEntry 13 }

ocnStmHistoryLineFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Failure Count encountered by a SONET/SDH 
         Line in the history bin."
     ::= { ocnStmHistoryEntry 14 }

ocnStmHistoryLineFarEndESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by a 
         SONET/SDH Section in the history bin."
     ::= { ocnStmHistoryEntry 15 }

ocnStmHistoryLineFarEndSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by a SONET/SDH Line in the history bin."
     ::= { ocnStmHistoryEntry 16 }

ocnStmHistoryLineFarEndCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by a 
         SONET/SDH Line in the history bin."
     ::= { ocnStmHistoryEntry 17 }

ocnStmHistoryLineFarEndUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Unavailable Seconds encountered by 
         a SONET/SDH Line in the history bin."
     ::= { ocnStmHistoryEntry 18 }

ocnStmHistorySectionESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         Section in the history bin."
     ::= { ocnStmHistoryEntry 19 }

ocnStmHistorySectionSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH Section in the history bin."
     ::= { ocnStmHistoryEntry 20 }

ocnStmHistorySectionCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         Section in the history bin."
     ::= { ocnStmHistoryEntry 21 }

ocnStmHistorySectionSEFs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Serverely Errored Frames Seconds encountered 
         by a SONET/SDH Section in the history bin."
     ::= { ocnStmHistoryEntry 22 }

ocnStmHistorySectionUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered 
         by a SONET/SDH Section in the history bin."
     ::= { ocnStmHistoryEntry 23 }


--
-- OCn Threshold Table
--
ocnStmThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF OcnStmThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of OCn/STM
             Thresholds."
    ::= { cmPerfObjects 48 }

ocnStmThresholdEntry OBJECT-TYPE
    SYNTAX      OcnStmThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ocnStmThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex, 
            ocnStmStatsIndex, ocnStmThresholdIndex }
    ::= { ocnStmThresholdTable 1 }

OcnStmThresholdEntry ::= SEQUENCE {
    ocnStmThresholdIndex       Integer32,
    ocnStmThresholdInterval    CmPmIntervalType,
    ocnStmThresholdVariable    VariablePointer,
    ocnStmThresholdValueLo     Unsigned32,
    ocnStmThresholdValueHi     Unsigned32,
    ocnStmThresholdMonValue    Counter64
}

ocnStmThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        ocnStmThresholdTable."
    ::= { ocnStmThresholdEntry 1 }

ocnStmThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { ocnStmThresholdEntry 2 }

ocnStmThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { ocnStmThresholdEntry 3 }

ocnStmThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { ocnStmThresholdEntry 4 }

ocnStmThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { ocnStmThresholdEntry 5 }

ocnStmThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to ocnStmThresholdVariable."
    ::= { ocnStmThresholdEntry 6 }

--
-- Sts/Vc Path Stats (Current) Table
--
stsVcPathStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF StsVcPathStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The SONET/SDH STS/VC Path Current data."
    ::= { cmPerfObjects 49 }

stsVcPathStatsEntry OBJECT-TYPE
    SYNTAX      StsVcPathStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stsVcPathStatsTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            stsVcPathParentIfIndex, stsVcPathIndex, stsVcPathStatsIndex }
    ::= { stsVcPathStatsTable 1 }

StsVcPathStatsEntry ::= SEQUENCE {
    stsVcPathStatsIndex           Integer32,
    stsVcPathStatsIntervalType    CmPmIntervalType,
    stsVcPathStatsValid           TruthValue,
    stsVcPathStatsAction          CmPmBinAction,
    stsVcPathStatsESs             PerfCounter64,
    stsVcPathStatsSESs            PerfCounter64,
    stsVcPathStatsCVs             PerfCounter64,
    stsVcPathStatsUASs            PerfCounter64,
    stsVcPathFarEndStatsESs       PerfCounter64,
    stsVcPathFarEndStatsSESs      PerfCounter64,
    stsVcPathFarEndStatsCVs       PerfCounter64,
    stsVcPathFarEndStatsUASs      PerfCounter64
}

stsVcPathStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the SONET/SDH sts Path current bin."
    ::= { stsVcPathStatsEntry 1 }

stsVcPathStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { stsVcPathStatsEntry 2 }

stsVcPathStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { stsVcPathStatsEntry 3 }

stsVcPathStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { stsVcPathStatsEntry 4 }

stsVcPathStatsESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
            "The counter associated with the number 
            of Errored Seconds encountered by the 
            SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 5 }

stsVcPathStatsSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by the 
         SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 6 }

stsVcPathStatsCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by the SONET/SDH 
         STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 7 }

stsVcPathStatsUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by the SONET/SDH 
         STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 8 }

stsVcPathFarEndStatsESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by the 
         SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 9 }

stsVcPathFarEndStatsSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by the SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 10 }

stsVcPathFarEndStatsCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by the 
         SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 11 }

stsVcPathFarEndStatsUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "The counter associated with the number of 
          Far End Unavailable Seconds encountered by 
          a SONET/SDH STS/VC Path in the current bin."
     ::= { stsVcPathStatsEntry 12 }

--
-- Sts/Vc Path History Table
--
stsVcPathHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF StsVcPathHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The SONET/SDH STS/VC Path History data."
    ::= { cmPerfObjects 50 }

stsVcPathHistoryEntry OBJECT-TYPE
    SYNTAX      StsVcPathHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stsVcPathHistoryTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            stsVcPathParentIfIndex, stsVcPathIndex, 
            stsVcPathStatsIndex, stsVcPathHistoryIndex }
    ::= { stsVcPathHistoryTable 1 }

StsVcPathHistoryEntry ::= SEQUENCE {
    stsVcPathHistoryIndex         Integer32,
    stsVcPathHistoryTime          DateAndTime,
    stsVcPathHistoryValid         TruthValue,
    stsVcPathHistoryAction        CmPmBinAction,
    stsVcPathHistoryESs           PerfCounter64,
    stsVcPathHistorySESs          PerfCounter64,
    stsVcPathHistoryCVs           PerfCounter64,
    stsVcPathHistoryUASs          PerfCounter64,
    stsVcPathFarEndHistoryESs     PerfCounter64,
    stsVcPathFarEndHistorySESs    PerfCounter64,
    stsVcPathFarEndHistoryCVs     PerfCounter64,
    stsVcPathFarEndHistoryUASs    PerfCounter64
}

stsVcPathHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the SONET/SDH sts Path history bin."
    ::= { stsVcPathHistoryEntry 1 }

stsVcPathHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only 
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { stsVcPathHistoryEntry 2 }

stsVcPathHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { stsVcPathHistoryEntry 3 }

stsVcPathHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { stsVcPathHistoryEntry 4 }

stsVcPathHistoryESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
            "The counter associated with the number 
            of Errored Seconds encountered by the 
            SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 5 }

stsVcPathHistorySESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by the 
         SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 6 }

stsVcPathHistoryCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by the SONET/SDH 
         STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 7 }

stsVcPathHistoryUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by the SONET/SDH 
         STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 8 }

stsVcPathFarEndHistoryESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by the 
         SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 9 }

stsVcPathFarEndHistorySESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by the SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 10 }

stsVcPathFarEndHistoryCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by the 
         SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 11 }

stsVcPathFarEndHistoryUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "The counter associated with the number of 
          Far End Unavailable Seconds encountered by 
          a SONET/SDH STS/VC Path in the history bin."
     ::= { stsVcPathHistoryEntry 12 }

--
-- STS VC Path Threshold Table
--
stsVcPathThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF StsVcPathThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of OCn
             Path Thresholds."
    ::= { cmPerfObjects 51 }

stsVcPathThresholdEntry OBJECT-TYPE
    SYNTAX      StsVcPathThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stsVcPathThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            stsVcPathParentIfIndex, stsVcPathIndex, 
            stsVcPathStatsIndex, stsVcPathThresholdIndex }
    ::= { stsVcPathThresholdTable 1 }

StsVcPathThresholdEntry ::= SEQUENCE {
    stsVcPathThresholdIndex       Integer32,
    stsVcPathThresholdInterval    CmPmIntervalType,
    stsVcPathThresholdVariable    VariablePointer,
    stsVcPathThresholdValueLo     Unsigned32,
    stsVcPathThresholdValueHi     Unsigned32,
    stsVcPathThresholdMonValue    Counter64
}

stsVcPathThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        stsVcPathThresholdTable."
    ::= { stsVcPathThresholdEntry 1 }

stsVcPathThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { stsVcPathThresholdEntry 2 }

stsVcPathThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { stsVcPathThresholdEntry 3 }

stsVcPathThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { stsVcPathThresholdEntry 4 }

stsVcPathThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { stsVcPathThresholdEntry 5 }

stsVcPathThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to stsVcPathThresholdVariable."
    ::= { stsVcPathThresholdEntry 6 }

--
-- Vt/Vc Path Stats Table
--
vtVcPathStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF VtVcPathStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The SONET/SDH vt Path current table." 
    ::= { cmPerfObjects 52 }

vtVcPathStatsEntry OBJECT-TYPE
    SYNTAX      VtVcPathStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the vtVcPathStatsTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            vtVcPathParentIfIndex, vtVcPathIndex, 
            vtVcPathStatsIndex }
    ::= { vtVcPathStatsTable 1 }

VtVcPathStatsEntry ::= SEQUENCE {
    vtVcPathStatsIndex          Integer32,
    vtVcPathStatsIntervalType   CmPmIntervalType,
    vtVcPathStatsValid          TruthValue,
    vtVcPathStatsAction         CmPmBinAction,
    vtVcPathStatsESs            PerfCounter64,
    vtVcPathStatsSESs           PerfCounter64,
    vtVcPathStatsCVs            PerfCounter64,
    vtVcPathStatsUASs           PerfCounter64,
    vtVcPathFarEndStatsESs      PerfCounter64,
    vtVcPathFarEndStatsSESs     PerfCounter64,
    vtVcPathFarEndStatsCVs      PerfCounter64,
    vtVcPathFarEndStatsUASs     PerfCounter64
}

vtVcPathStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the SONET/SDH VT/VC Path current bin."
    ::= { vtVcPathStatsEntry 1 }

vtVcPathStatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { vtVcPathStatsEntry 2 }

vtVcPathStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { vtVcPathStatsEntry 3 }

vtVcPathStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { vtVcPathStatsEntry 4 }

vtVcPathStatsESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         vt Path in the current bin."
     ::= { vtVcPathStatsEntry 5 }

vtVcPathStatsSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH vt Path in the current bin."   
     ::= { vtVcPathStatsEntry 6 }

vtVcPathStatsCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         vt Path in the current bin." 
     ::= { vtVcPathStatsEntry 7 }

vtVcPathStatsUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by a SONET/SDH 
         vt Path in the current bin."    
     ::= { vtVcPathStatsEntry 8 }

vtVcPathFarEndStatsESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by a 
         SONET/SDH vt Path in the current bin."
     ::= { vtVcPathStatsEntry 9 }

vtVcPathFarEndStatsSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by a SONET/SDH vt Path in the current bin."  
     ::= { vtVcPathStatsEntry 10 }

vtVcPathFarEndStatsCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by a 
         SONET/SDH vt Path in the current bin." 
     ::= { vtVcPathStatsEntry 11 }

vtVcPathFarEndStatsUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Unavailable Seconds encountered by 
         a SONET/SDH vt Path in the current bin."         
     ::= { vtVcPathStatsEntry 12 }

--
-- Vt/Vc Path History Table
--
vtVcPathHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF VtVcPathHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The SONET/SDH vt Path history table." 
    ::= { cmPerfObjects 53 }

vtVcPathHistoryEntry OBJECT-TYPE
    SYNTAX      VtVcPathHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the vtVcPathHistoryTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            vtVcPathParentIfIndex, vtVcPathIndex, 
            vtVcPathStatsIndex,  vtVcPathHistoryIndex }
    ::= { vtVcPathHistoryTable 1 }

VtVcPathHistoryEntry ::= SEQUENCE {
    vtVcPathHistoryIndex       Integer32,
    vtVcPathHistoryTime        DateAndTime,
    vtVcPathHistoryValid       TruthValue,
    vtVcPathHistoryAction      CmPmBinAction,
    vtVcPathHistoryESs         PerfCounter64,
    vtVcPathHistorySESs        PerfCounter64,
    vtVcPathHistoryCVs         PerfCounter64,
    vtVcPathHistoryUASs        PerfCounter64,
    vtVcPathFarEndHistoryESs   PerfCounter64,
    vtVcPathFarEndHistorySESs  PerfCounter64,
    vtVcPathFarEndHistoryCVs   PerfCounter64,
    vtVcPathFarEndHistoryUASs  PerfCounter64
}

vtVcPathHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the SONET/SDH vt Path history bin."
    ::= { vtVcPathHistoryEntry 1 }

vtVcPathHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { vtVcPathHistoryEntry 2 }

vtVcPathHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { vtVcPathHistoryEntry 3 }

vtVcPathHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { vtVcPathHistoryEntry 4 }

vtVcPathHistoryESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a SONET/SDH 
         vt Path in the history bin."
     ::= { vtVcPathHistoryEntry 5 }

vtVcPathHistorySESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         SONET/SDH vt Path in the history bin."   
     ::= { vtVcPathHistoryEntry 6 }

vtVcPathHistoryCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a SONET/SDH 
         vt Path in the history bin." 
     ::= { vtVcPathHistoryEntry 7 }

vtVcPathHistoryUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Unavailable Seconds encountered by a SONET/SDH 
         vt Path in the history bin."    
     ::= { vtVcPathHistoryEntry 8 }

vtVcPathFarEndHistoryESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Errored Seconds encountered by a 
         SONET/SDH vt Path in the history bin."
     ::= { vtVcPathHistoryEntry 9 }

vtVcPathFarEndHistorySESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Severely Errored Seconds encountered 
         by a SONET/SDH vt Path in the history bin."  
     ::= { vtVcPathHistoryEntry 10 }

vtVcPathFarEndHistoryCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Coding Violations encountered by a 
         SONET/SDH vt Path in the history bin." 
     ::= { vtVcPathHistoryEntry 11 }

vtVcPathFarEndHistoryUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Far End Unavailable Seconds encountered by 
         a SONET/SDH vt Path in the history bin."         
     ::= { vtVcPathHistoryEntry 12 }

--
-- VtVC Threshold Table
--
vtVcPathThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF VtVcPathThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of 
             OCn VT1.5/VC12 Thresholds."
    ::= { cmPerfObjects 54 }

vtVcPathThresholdEntry OBJECT-TYPE
    SYNTAX      VtVcPathThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the vtVcPathThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex,
            vtVcPathParentIfIndex, vtVcPathIndex, 
            vtVcPathStatsIndex, vtVcPathThresholdIndex }
    ::= { vtVcPathThresholdTable 1 }

VtVcPathThresholdEntry ::= SEQUENCE {
    vtVcPathThresholdIndex       Integer32,
    vtVcPathThresholdInterval    CmPmIntervalType,
    vtVcPathThresholdVariable    VariablePointer,
    vtVcPathThresholdValueLo     Unsigned32,
    vtVcPathThresholdValueHi     Unsigned32,
    vtVcPathThresholdMonValue    Counter64
}

vtVcPathThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        vtVcPathThresholdTable."
    ::= { vtVcPathThresholdEntry 1 }

vtVcPathThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { vtVcPathThresholdEntry 2 }

vtVcPathThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { vtVcPathThresholdEntry 3 }

vtVcPathThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { vtVcPathThresholdEntry 4 }

vtVcPathThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { vtVcPathThresholdEntry 5 }

vtVcPathThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to vtVcPathThresholdVariable."
    ::= { vtVcPathThresholdEntry 6 }


--
-- E1/T1 Stats Table
--
e1t1StatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E1T1StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The E1/T1 Path current table." 
    ::= { cmPerfObjects 55 }

e1t1StatsEntry OBJECT-TYPE
    SYNTAX      E1T1StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e1t1StatsTable."
    INDEX { neIndex, shelfIndex, slotIndex, 
            e1t1ParentIfIndex, e1t1Index, e1t1StatsIndex }
    ::= { e1t1StatsTable 1 }

E1T1StatsEntry ::= SEQUENCE {
    e1t1StatsIndex             Integer32,
    e1t1StatsIntervalType      CmPmIntervalType,
    e1t1StatsValid             TruthValue,
    e1t1StatsAction            CmPmBinAction,
    e1t1StatsLineCVs           PerfCounter64,
    e1t1StatsLineESs           PerfCounter64,
    e1t1StatsLineSESs          PerfCounter64,
    e1t1StatsLineESsFarEnd     PerfCounter64,
    e1t1StatsLineUASs          PerfCounter64,
    e1t1StatsLineLOSSs         PerfCounter64,
    e1t1StatsPathCVs           PerfCounter64,
    e1t1StatsPathESs           PerfCounter64,
    e1t1StatsPathSESs          PerfCounter64,
    e1t1StatsPathUASs          PerfCounter64,
    e1t1StatsPathCVsFarEnd     PerfCounter64,
    e1t1StatsPathESsFarEnd     PerfCounter64,
    e1t1StatsPathSESsFarEnd    PerfCounter64,
    e1t1StatsPathSEFsFarEnd    PerfCounter64,
    e1t1StatsPathUASsFarEnd    PerfCounter64,
    e1t1StatsPathFCs           PerfCounter64,
    e1t1StatsPathFCsFarEnd     PerfCounter64,
    e1t1StatsPathAISs          PerfCounter64,
    e1t1StatsPathSASs          PerfCounter64
}

e1t1StatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the E1/T1 current bin."
    ::= { e1t1StatsEntry 1 }

e1t1StatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { e1t1StatsEntry 2 }

e1t1StatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { e1t1StatsEntry 3 }

e1t1StatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { e1t1StatsEntry 4 }

e1t1StatsLineCVs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a E1/T1 interface 
         in the current bin."
     ::= { e1t1StatsEntry 5 }

e1t1StatsLineESs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a E1/T1 interface 
         in the current bin."
     ::= { e1t1StatsEntry 6 }

e1t1StatsLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 7 }

e1t1StatsLineESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Errored Seconds Far End encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 8 }

e1t1StatsLineUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Unavailable Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 9 }

e1t1StatsLineLOSSs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Loss Of Signal Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 10 }

e1t1StatsPathCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Coding Violations encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 11 }

e1t1StatsPathESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Errored Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 12 }

e1t1StatsPathSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 13 }

e1t1StatsPathUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Unavailable Seconds Seconds encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 14 }

e1t1StatsPathCVsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Coding Violations Far End encountered by a 
         E1/T1 interface in the current bin."   
     ::= { e1t1StatsEntry 15 }

e1t1StatsPathESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Errored Seconds Far End encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 16 }

e1t1StatsPathSESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Seconds Far End encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 17 }

e1t1StatsPathSEFsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Framing Seconds Far End encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 18 }

e1t1StatsPathUASsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Unavailable Seconds Seconds Far End encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 19 }

e1t1StatsPathFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Count encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 20 }

e1t1StatsPathFCsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Count Far End encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 21 }

e1t1StatsPathAISs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path AISs encountered by a 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 22 }

e1t1StatsPathSASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with path SASs 
         E1/T1 interface in the current bin."
     ::= { e1t1StatsEntry 23 }


--
-- E1/T1 History Table
--
e1t1HistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E1T1HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The E1/T1 history table." 
    ::= { cmPerfObjects 56 }

e1t1HistoryEntry OBJECT-TYPE
    SYNTAX      E1T1HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e1t1HistoryTable."
    INDEX { neIndex, shelfIndex, slotIndex,
            e1t1ParentIfIndex, e1t1Index, 
            e1t1StatsIndex, e1t1HistoryIndex }
    ::= { e1t1HistoryTable 1 }

E1T1HistoryEntry ::= SEQUENCE {
    e1t1HistoryIndex            Integer32,
    e1t1HistoryTime             DateAndTime,
    e1t1HistoryValid            TruthValue,
    e1t1HistoryAction           CmPmBinAction,
    e1t1HistoryLineCVs          PerfCounter64,
    e1t1HistoryLineESs          PerfCounter64,
    e1t1HistoryLineSESs         PerfCounter64,
    e1t1HistoryLineESsFarEnd    PerfCounter64,
    e1t1HistoryLineUASs         PerfCounter64,
    e1t1HistoryLineLOSSs        PerfCounter64,
    e1t1HistoryPathCVs          PerfCounter64,
    e1t1HistoryPathESs          PerfCounter64,
    e1t1HistoryPathSESs         PerfCounter64,
    e1t1HistoryPathUASs         PerfCounter64,
    e1t1HistoryPathCVsFarEnd    PerfCounter64,
    e1t1HistoryPathESsFarEnd    PerfCounter64,
    e1t1HistoryPathSESsFarEnd   PerfCounter64,
    e1t1HistoryPathSEFsFarEnd   PerfCounter64,
    e1t1HistoryPathUASsFarEnd   PerfCounter64,
    e1t1HistoryPathFCs          PerfCounter64,
    e1t1HistoryPathFCsFarEnd    PerfCounter64,
    e1t1HistoryPathAISs         PerfCounter64,
    e1t1HistoryPathSASs         PerfCounter64
}

e1t1HistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the E1/T1 history bin."
    ::= { e1t1HistoryEntry 1 }

e1t1HistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { e1t1HistoryEntry 2 }

e1t1HistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { e1t1HistoryEntry 3 }

e1t1HistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { e1t1HistoryEntry 4 }

e1t1HistoryLineCVs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a E1/T1 interface 
         in the history bin."
     ::= { e1t1HistoryEntry 5 }

e1t1HistoryLineESs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a E1/T1 interface 
         in the history bin."
     ::= { e1t1HistoryEntry 6 }

e1t1HistoryLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 7 }

e1t1HistoryLineESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Errored Seconds Far End encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 8 }

e1t1HistoryLineUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Unavailable Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 9 }

e1t1HistoryLineLOSSs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Line Loss Of Signal Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 10 }

e1t1HistoryPathCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Coding Violations encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 11 }

e1t1HistoryPathESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Errored Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 12 }

e1t1HistoryPathSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 13 }

e1t1HistoryPathUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Unavailable Seconds Seconds encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 14 }

e1t1HistoryPathCVsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Coding Violations Far End encountered by a 
         E1/T1 interface in the history bin."   
     ::= { e1t1HistoryEntry 15 }

e1t1HistoryPathESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Errored Seconds Far End encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 16 }

e1t1HistoryPathSESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Seconds Far End encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 17 }

e1t1HistoryPathSEFsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Severely Errored Framing Seconds Far End encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 18 }

e1t1HistoryPathUASsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Unavailable Seconds Seconds Far End encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 19 }

e1t1HistoryPathFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Count encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 20 }

e1t1HistoryPathFCsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Count Far End encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 21 }

e1t1HistoryPathAISs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path AISs encountered by a 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 22 }

e1t1HistoryPathSASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with path SASs 
         E1/T1 interface in the history bin."
     ::= { e1t1HistoryEntry 23 }

--
-- E1T1 Threshold Table
--
e1t1ThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E1T1ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of 
             OCn E1/T1 Thresholds."
    ::= { cmPerfObjects 57 }

e1t1ThresholdEntry OBJECT-TYPE
    SYNTAX      E1T1ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e1t1ThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex,
            e1t1ParentIfIndex, e1t1Index, 
            e1t1StatsIndex, e1t1ThresholdIndex }
    ::= { e1t1ThresholdTable 1 }

E1T1ThresholdEntry ::= SEQUENCE {
    e1t1ThresholdIndex       Integer32,
    e1t1ThresholdInterval    CmPmIntervalType,
    e1t1ThresholdVariable    VariablePointer,
    e1t1ThresholdValueLo     Unsigned32,
    e1t1ThresholdValueHi     Unsigned32,
    e1t1ThresholdMonValue    Counter64
}

e1t1ThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS  not-accessible 
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        e1t1ThresholdTable."
    ::= { e1t1ThresholdEntry 1 }

e1t1ThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { e1t1ThresholdEntry 2 }

e1t1ThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { e1t1ThresholdEntry 3 }

e1t1ThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { e1t1ThresholdEntry 4 }

e1t1ThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { e1t1ThresholdEntry 5 }

e1t1ThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to e1t1ThresholdVariable."
    ::= { e1t1ThresholdEntry 6 }

--
-- E3/T3 Stats Table
--
e3t3StatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E3T3StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The E3/T3 Path current table." 
    ::= { cmPerfObjects 58 }

e3t3StatsEntry OBJECT-TYPE
    SYNTAX      E3T3StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e3t3StatsTable."
    INDEX { neIndex, shelfIndex, slotIndex,
            e3t3ParentIfIndex, e3t3Index, e3t3StatsIndex }
    ::= { e3t3StatsTable 1 }

E3T3StatsEntry ::= SEQUENCE {
    e3t3StatsIndex             Integer32,
    e3t3StatsIntervalType      CmPmIntervalType,
    e3t3StatsValid             TruthValue,
    e3t3StatsAction            CmPmBinAction,
    e3t3StatsLineCVs           PerfCounter64,
    e3t3StatsLineESs           PerfCounter64,
    e3t3StatsLineSESs          PerfCounter64,
    e3t3StatsLineLOSSs         PerfCounter64,
    e3t3StatsPathPCVs          PerfCounter64,
    e3t3StatsPathCCVs          PerfCounter64,
    e3t3StatsPathAISs          PerfCounter64,
    e3t3StatsPathPESs          PerfCounter64,
    e3t3StatsPathCESs          PerfCounter64,
    e3t3StatsPathFCs           PerfCounter64,
    e3t3StatsPathSEFs          PerfCounter64,
    e3t3StatsPathPSESs         PerfCounter64,
    e3t3StatsPathCSESs         PerfCounter64,
    e3t3StatsPathPUASs         PerfCounter64,
    e3t3StatsPathCUASs         PerfCounter64,
    e3t3StatsPathCCVsFarEnd    PerfCounter64,
    e3t3StatsPathCESsFarEnd    PerfCounter64,
    e3t3StatsPathCSESsFarEnd   PerfCounter64,
    e3t3StatsPathCFCsFarEnd    PerfCounter64,
    e3t3StatsPathCUASsFarEnd   PerfCounter64
}

e3t3StatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the E3/T3 current bin."
    ::= { e3t3StatsEntry 1 }

e3t3StatsIntervalType OBJECT-TYPE
    SYNTAX      CmPmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Interval over which Performance Monitoring data is collected."
    ::= { e3t3StatsEntry 2 }

e3t3StatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { e3t3StatsEntry 3 }

e3t3StatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { e3t3StatsEntry 4 }

e3t3StatsLineCVs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a E3/T3 interface 
         in the current bin."
     ::= { e3t3StatsEntry 5 }

e3t3StatsLineESs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a E3/T3 interface 
         in the current bin."
     ::= { e3t3StatsEntry 6 }

e3t3StatsLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         E3/T3 interface in the current bin."   
     ::= { e3t3StatsEntry 7 }

e3t3StatsLineLOSSs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Loss Of Signal Seconds encountered by a 
         E3/T3 interface in the current bin."   
     ::= { e3t3StatsEntry 8 }

e3t3StatsPathPCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Coding Violations 
         encountered by a E3/T3 interface in the current bin."   
     ::= { e3t3StatsEntry 9 }

e3t3StatsPathCCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Coding Violations 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 10 }

e3t3StatsPathAISs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Alarm Indications Signal Seconds  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 11 }

e3t3StatsPathPESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Error Seconds  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 12 }

e3t3StatsPathCESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Error Seconds  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 13 }

e3t3StatsPathFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Counts encountered by a E3/T3 
         interface in the current bin."
     ::= { e3t3StatsEntry 14 }

e3t3StatsPathSEFs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Serverely Errored Frames/AIS Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 15 }

e3t3StatsPathPSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Severely Errored Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 16 }

e3t3StatsPathCSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Severely Errored Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 17 }

e3t3StatsPathPUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 18 }

e3t3StatsPathCUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 19 }

e3t3StatsPathCCVsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Code Violations 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 20 }

e3t3StatsPathCESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Errored Seconds  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 21 }

e3t3StatsPathCSESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Severely Errored Seconds  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 22 }

e3t3StatsPathCFCsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Failure Counts  
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 23 }

e3t3StatsPathCUASsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the current bin."
     ::= { e3t3StatsEntry 24 }

--
-- E3/T3 History Table
--
e3t3HistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E3T3HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The E3/T3 history table." 
    ::= { cmPerfObjects 59 }

e3t3HistoryEntry OBJECT-TYPE
    SYNTAX      E3T3HistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e3t3HistoryTable."
    INDEX { neIndex, shelfIndex, slotIndex,
            e3t3ParentIfIndex, e3t3Index, 
            e3t3StatsIndex, e3t3HistoryIndex }
    ::= { e3t3HistoryTable 1 }

E3T3HistoryEntry ::= SEQUENCE {
    e3t3HistoryIndex            Integer32,
    e3t3HistoryTime             DateAndTime,
    e3t3HistoryValid            TruthValue,
    e3t3HistoryAction           CmPmBinAction,
    e3t3HistoryLineCVs          PerfCounter64,
    e3t3HistoryLineESs          PerfCounter64,
    e3t3HistoryLineSESs         PerfCounter64,
    e3t3HistoryLineLOSSs        PerfCounter64,
    e3t3HistoryPathPCVs         PerfCounter64,
    e3t3HistoryPathCCVs         PerfCounter64,
    e3t3HistoryPathAISs         PerfCounter64,
    e3t3HistoryPathPESs         PerfCounter64,
    e3t3HistoryPathCESs         PerfCounter64,
    e3t3HistoryPathFCs          PerfCounter64,
    e3t3HistoryPathSEFs         PerfCounter64,
    e3t3HistoryPathPSESs        PerfCounter64,
    e3t3HistoryPathCSESs        PerfCounter64,
    e3t3HistoryPathPUASs        PerfCounter64,
    e3t3HistoryPathCUASs        PerfCounter64,
    e3t3HistoryPathCCVsFarEnd   PerfCounter64,
    e3t3HistoryPathCESsFarEnd   PerfCounter64,
    e3t3HistoryPathCSESsFarEnd  PerfCounter64,
    e3t3HistoryPathCFCsFarEnd   PerfCounter64,
    e3t3HistoryPathCUASsFarEnd  PerfCounter64
}

e3t3HistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An index of the E3/T3 history bin."
    ::= { e3t3HistoryEntry 1 }

e3t3HistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the time of history bin creation."
    ::= { e3t3HistoryEntry 2 }

e3t3HistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { e3t3HistoryEntry 3 }

e3t3HistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { e3t3HistoryEntry 4 }

e3t3HistoryLineCVs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Coding Violations encountered by a E3/T3 interface 
         in the history bin."
     ::= { e3t3HistoryEntry 5 }

e3t3HistoryLineESs  OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Errored Seconds encountered by a E3/T3 interface 
         in the history bin."
     ::= { e3t3HistoryEntry 6 }

e3t3HistoryLineSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Severely Errored Seconds encountered by a 
         E3/T3 interface in the history bin."   
     ::= { e3t3HistoryEntry 7 }

e3t3HistoryLineLOSSs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Loss Of Signal Seconds encountered by a 
         E3/T3 interface in the history bin."   
     ::= { e3t3HistoryEntry 8 }

e3t3HistoryPathPCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Coding Violations 
         encountered by a E3/T3 interface in the history bin."   
     ::= { e3t3HistoryEntry 9 }

e3t3HistoryPathCCVs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Coding Violations 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 10 }

e3t3HistoryPathAISs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Alarm Indications Signal Seconds  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 11 }

e3t3HistoryPathPESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Error Seconds  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 12 }

e3t3HistoryPathCESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Error Seconds  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 13 }

e3t3HistoryPathFCs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Failure Counts encountered by a E3/T3 
         interface in the history bin."
     ::= { e3t3HistoryEntry 14 }

e3t3HistoryPathSEFs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path Serverely Errored Frames/AIS Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 15 }

e3t3HistoryPathPSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Severely Errored Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 16 }

e3t3HistoryPathCSESs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Severely Errored Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 17 }

e3t3HistoryPathPUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path P-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 18 }

e3t3HistoryPathCUASs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path C-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 19 }

e3t3HistoryPathCCVsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Code Violations 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 20 }

e3t3HistoryPathCESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Errored Seconds  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 21 }

e3t3HistoryPathCSESsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Severely Errored Seconds  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 22 }

e3t3HistoryPathCFCsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Failure Counts  
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 23 }

e3t3HistoryPathCUASsFarEnd OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The counter associated with the number of 
         Path FarEnd C-bit parity check Unavailable Seconds 
         encountered by a E3/T3 interface in the history bin."
     ::= { e3t3HistoryEntry 24 }

--
-- E3T3 Threshold Table
--
e3t3ThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF E3T3ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of 
             OCn E3/T3 Thresholds."
    ::= { cmPerfObjects 60 }

e3t3ThresholdEntry OBJECT-TYPE
    SYNTAX      E3T3ThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the e3t3ThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex,
            e3t3ParentIfIndex, e3t3Index, 
            e3t3StatsIndex, e3t3ThresholdIndex }
    ::= { e3t3ThresholdTable 1 }

E3T3ThresholdEntry ::= SEQUENCE {
    e3t3ThresholdIndex       Integer32,
    e3t3ThresholdInterval    CmPmIntervalType,
    e3t3ThresholdVariable    VariablePointer,
    e3t3ThresholdValueLo     Unsigned32,
    e3t3ThresholdValueHi     Unsigned32,
    e3t3ThresholdMonValue    Counter64
}

e3t3ThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        e3t3ThresholdTable."
    ::= { e3t3ThresholdEntry 1 }

e3t3ThresholdInterval OBJECT-TYPE
    SYNTAX     CmPmIntervalType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The interval over which monitored value is
        sampled and compared with the specified threshold."
    ::= { e3t3ThresholdEntry 2 }

e3t3ThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled."
    ::= { e3t3ThresholdEntry 3 }

e3t3ThresholdValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the threshold value."
    ::= { e3t3ThresholdEntry 4 }

e3t3ThresholdValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the threshold value."
    ::= { e3t3ThresholdEntry 5 }

e3t3ThresholdMonValue OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to e3t3ThresholdVariable."
    ::= { e3t3ThresholdEntry 6 }

--
-- Flow Bandwidth extenstion table
--
cmFlowBWExtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CmFlowBWExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "This table extends cmFlowTable and  presents Flow's bandwidth configuration 
             paremeters in 64-bit values."
    ::= { cmPerfObjects 61 }

cmFlowBWExtEntry OBJECT-TYPE
    SYNTAX      CmFlowBWExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the cmFlowBWExtTable."
    AUGMENTS { cmFlowEntry }
    ::= { cmFlowBWExtTable 1 }

CmFlowBWExtEntry ::= SEQUENCE {
    cmFlowBWA2NCIR       CounterBasedGauge64,
    cmFlowBWA2NEIR       CounterBasedGauge64,
    cmFlowBWN2ACIR       CounterBasedGauge64,
    cmFlowBWN2AEIR       CounterBasedGauge64,
    cmFlowBWA2NGFB       CounterBasedGauge64,
    cmFlowBWA2NMFB       CounterBasedGauge64 
}

cmFlowBWA2NCIR OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "A2N CIR of a FLOW."
    ::= { cmFlowBWExtEntry 1 }

cmFlowBWA2NEIR OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "A2N EIR of a FLOW."
    ::= { cmFlowBWExtEntry 2 }

cmFlowBWN2ACIR OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "N2A CIR of a FLOW."
    ::= { cmFlowBWExtEntry 3 }

cmFlowBWN2AEIR OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "N2A EIR of a FLOW."
    ::= { cmFlowBWExtEntry 4 }

cmFlowBWA2NGFB OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Guaranteed Flow Bandwidth of a FLOW."
    ::= { cmFlowBWExtEntry 5 }

cmFlowBWA2NMFB OBJECT-TYPE
    SYNTAX    CounterBasedGauge64 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Maximum Flow Bandwidth of a FLOW."
    ::= { cmFlowBWExtEntry 6 }

--
-- OCN STM Threshold Variance Table
--
ocnStmThresholdVarTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF OcnStmThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of ocn stm
             Port Threshold variances."
    ::= { cmPerfObjects 62 }

ocnStmThresholdVarEntry OBJECT-TYPE
    SYNTAX      OcnStmThresholdVarEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ocnStmThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, ocnStmIndex, 
            ocnStmStatsIndex }
    ::= { ocnStmThresholdVarTable 1 }

OcnStmThresholdVarEntry ::= SEQUENCE {
    ocnStmThresholdVarOprVariance    Integer32,
    ocnStmThresholdVarOptVariance    Integer32
}

ocnStmThresholdVarOprVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power received (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { ocnStmThresholdVarEntry 1 }

ocnStmThresholdVarOptVariance OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Variance in the Optical Power transmitted (in dbM), beyond which 
         threshold crossing alert will be generated."
    ::= { ocnStmThresholdVarEntry 2 }

cmPerfScalarObjects           OBJECT IDENTIFIER ::= {cmPerfObjects 63}

--
--cmPerQueryGenControl
--
cmPerQueryGenControl OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "This object describes whether report the cmPerQueryGenTrap. Its default value is enable."
    ::= { cmPerfScalarObjects 1 }

---
---Notifications
---
cmEthernetAccPortThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmEthernetAccPortThresholdIndex,       
                cmEthernetAccPortThresholdInterval,
                cmEthernetAccPortThresholdVariable,
                cmEthernetAccPortThresholdValueLo,
                cmEthernetAccPortThresholdValueHi,
                cmEthernetAccPortThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Ethernet Access Port is crossed."
  ::= { cmPerfNotifications 1 }

cmEthernetNetPortThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmEthernetNetPortThresholdIndex,       
                cmEthernetNetPortThresholdInterval,
                cmEthernetNetPortThresholdVariable,
                cmEthernetNetPortThresholdValueLo,
                cmEthernetNetPortThresholdValueHi,
                cmEthernetNetPortThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Ethernet Network Port is crossed."
  ::= { cmPerfNotifications 2 }

cmFlowThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmFlowThresholdIndex,       
                cmFlowThresholdInterval,
                cmFlowThresholdVariable,
                cmFlowThresholdValueLo,
                cmFlowThresholdValueHi,
                cmFlowThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Ethernet Flow is crossed."
  ::= { cmPerfNotifications 3 }

cmQosShaperThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmQosShaperThresholdIndex,       
                cmQosShaperThresholdInterval,
                cmQosShaperThresholdVariable,
                cmQosShaperThresholdValueLo,
                cmQosShaperThresholdValueHi,
                cmQosShaperThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an QOS Shaper is crossed."
  ::= { cmPerfNotifications 4 }

cmQosFlowPolicerThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmQosFlowPolicerThresholdIndex,       
                cmQosFlowPolicerThresholdInterval,
                cmQosFlowPolicerThresholdVariable,
                cmQosFlowPolicerThresholdValueLo,
                cmQosFlowPolicerThresholdValueHi,
                cmQosFlowPolicerThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an QOS Flow Policer is crossed."
  ::= { cmPerfNotifications 5 }

cmAccPortQosShaperThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmAccPortQosShaperThresholdIndex,       
                cmAccPortQosShaperThresholdInterval,
                cmAccPortQosShaperThresholdVariable,
                cmAccPortQosShaperThresholdValueLo,
                cmAccPortQosShaperThresholdValueHi,
                cmAccPortQosShaperThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Access Port QOS Shaper is crossed."
  ::= { cmPerfNotifications 6 }

cmEthernetTrafficPortThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmEthernetTrafficPortThresholdIndex,       
                cmEthernetTrafficPortThresholdInterval,
                cmEthernetTrafficPortThresholdVariable,
                cmEthernetTrafficPortThresholdValueLo,
                cmEthernetTrafficPortThresholdValueHi,
                cmEthernetTrafficPortThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg Ethernet Port is crossed."
  ::= { cmPerfNotifications 7 }

cmFlowPointThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmFlowPointThresholdIndex,       
                cmFlowPointThresholdInterval,
                cmFlowPointThresholdVariable,
                cmFlowPointThresholdValueLo,
                cmFlowPointThresholdValueHi,
                cmFlowPointThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg FlowPoint is crossed."
  ::= { cmPerfNotifications 8 }
  
cmOAMFlowPointThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmOAMFlowPointThresholdIndex,       
                cmOAMFlowPointThresholdInterval,
                cmOAMFlowPointThresholdVariable,
                cmOAMFlowPointThresholdValueLo,
                cmOAMFlowPointThresholdValueHi,
                cmOAMFlowPointThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg FlowPoint is crossed."
  ::= { cmPerfNotifications 9 }
   
cmQosPolicerV2ThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmQosPolicerV2ThresholdIndex,       
                cmQosPolicerV2ThresholdInterval,
                cmQosPolicerV2ThresholdVariable,
                cmQosPolicerV2ThresholdValueLo,
                cmQosPolicerV2ThresholdValueHi,
                cmQosPolicerV2ThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg QosPolicer is crossed."
  ::= { cmPerfNotifications 10 }
  
cmQosShaperV2ThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmQosShaperV2ThresholdIndex,       
                cmQosShaperV2ThresholdInterval,
                cmQosShaperV2ThresholdVariable,
                cmQosShaperV2ThresholdValueLo,
                cmQosShaperV2ThresholdValueHi,
                cmQosShaperV2ThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg QosQueue is crossed."
  ::= { cmPerfNotifications 11 }
  
cmLagThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmLagThresholdIndex,       
                cmLagThresholdInterval,
                cmLagThresholdVariable,
                cmLagThresholdValueLo,
                cmLagThresholdValueHi,
                cmLagThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg Lag is crossed."
  ::= { cmPerfNotifications 12 }

cmTrafficPortQosShaperThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                cmTrafficPortQosShaperThresholdIndex,       
                cmTrafficPortQosShaperThresholdInterval,
                cmTrafficPortQosShaperThresholdVariable,
                cmTrafficPortQosShaperThresholdValueLo,
                cmTrafficPortQosShaperThresholdValueHi,
                cmTrafficPortQosShaperThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Agg QosQueue is crossed."
  ::= { cmPerfNotifications 13 }

f3NetPortQosShaperThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                f3NetPortQosShaperThresholdIndex,
                f3NetPortQosShaperThresholdInterval,
                f3NetPortQosShaperThresholdVariable,
                f3NetPortQosShaperThresholdValueLo,
                f3NetPortQosShaperThresholdValueHi,
                f3NetPortQosShaperThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an Network Port QOS Shaper is crossed."
  ::= { cmPerfNotifications 14 }

ocnStmThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                ocnStmThresholdIndex,
                ocnStmThresholdInterval,
                ocnStmThresholdVariable,
                ocnStmThresholdValueLo,
                ocnStmThresholdValueHi,
                ocnStmThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an OCN/STM is crossed."
  ::= { cmPerfNotifications 15 }

stsVcPathThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                stsVcPathThresholdIndex,
                stsVcPathThresholdInterval,
                stsVcPathThresholdVariable,
                stsVcPathThresholdValueLo,
                stsVcPathThresholdValueHi,
                stsVcPathThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an STS/VC Path is crossed."
  ::= { cmPerfNotifications 16 }

vtVcPathThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                vtVcPathThresholdIndex,
                vtVcPathThresholdInterval,
                vtVcPathThresholdVariable,
                vtVcPathThresholdValueLo,
                vtVcPathThresholdValueHi,
                vtVcPathThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an VT/VC Path is crossed."
  ::= { cmPerfNotifications 17 }

e1t1ThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                e1t1ThresholdIndex,
                e1t1ThresholdInterval,
                e1t1ThresholdVariable,
                e1t1ThresholdValueLo,
                e1t1ThresholdValueHi,
                e1t1ThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an E1/T1 is crossed."
  ::= { cmPerfNotifications 18 }

e3t3ThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                e3t3ThresholdIndex,
                e3t3ThresholdInterval,
                e3t3ThresholdVariable,
                e3t3ThresholdValueLo,
                e3t3ThresholdValueHi,
                e3t3ThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on an E3/T3 is crossed."
  ::= { cmPerfNotifications 19 }

cmPerQueryGenTrap NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a pm period when the performance generation finished.
            Note: only when cmPerQueryGenControl is enabled, this trap will be reported."
  ::= { cmPerfNotifications 20 }

--
-- Conformance
--
cmPerfCompliances OBJECT IDENTIFIER ::= {cmPerfConformance 1}
cmPerfGroups      OBJECT IDENTIFIER ::= {cmPerfConformance 2}

cmPerfCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "Describes the requirements for conformance to the CM Perf
             group."
    MODULE  -- this module
        MANDATORY-GROUPS {
              cmPerfObjectGroup, cmPerfNotifGroup,
              ethernetAccessPortPMGroup, ethernetNetworkPortPMGroup,
              trafficPMGroup
        }
    ::= { cmPerfCompliances 1 }

cmPerfObjectGroup OBJECT-GROUP
    OBJECTS {
        cmEthernetAccPortStatsIndex, cmEthernetAccPortStatsIntervalType,
        cmEthernetAccPortStatsValid, cmEthernetAccPortStatsAction,
        cmEthernetAccPortStatsESBF, cmEthernetAccPortStatsESBP,
        cmEthernetAccPortStatsESBS, cmEthernetAccPortStatsESC,
        cmEthernetAccPortStatsESCAE,cmEthernetAccPortStatsESDE,
        cmEthernetAccPortStatsESF, cmEthernetAccPortStatsESFS,
        cmEthernetAccPortStatsESJ, cmEthernetAccPortStatsESMF,
        cmEthernetAccPortStatsESMP, cmEthernetAccPortStatsESO,
        cmEthernetAccPortStatsESOF, cmEthernetAccPortStatsESOP,
        cmEthernetAccPortStatsESP, cmEthernetAccPortStatsESP64, 
        cmEthernetAccPortStatsESP65, cmEthernetAccPortStatsESP128,
        cmEthernetAccPortStatsESP256, cmEthernetAccPortStatsESP512,
        cmEthernetAccPortStatsESP1024, cmEthernetAccPortStatsESP1519, 
        cmEthernetAccPortStatsESUF, cmEthernetAccPortStatsESUP,
        cmEthernetAccPortStatsL2CPFD, cmEthernetAccPortStatsL2CPFP,
        cmEthernetAccPortStatsLES, cmEthernetAccPortStatsLBC,
        cmEthernetAccPortStatsOPT, cmEthernetAccPortStatsOPR,
        cmEthernetAccPortStatsAUFD, cmEthernetAccPortStatsAPFD,
        cmEthernetAccPortStatsABRRx, cmEthernetAccPortStatsABRTx,
        cmEthernetAccPortStatsTemp, cmEthernetAccPortStatsUAS,
        cmEthernetAccPortStatsIBRMaxRx, cmEthernetAccPortStatsIBRMaxTx,
        cmEthernetAccPortStatsIBRMinRx, cmEthernetAccPortStatsIBRMinTx,
        cmEthernetAccPortStatsIBRRx, cmEthernetAccPortStatsIBRTx,
        
        cmEthernetAccPortHistoryIndex, cmEthernetAccPortHistoryTime,
        cmEthernetAccPortHistoryValid, cmEthernetAccPortHistoryAction,
        cmEthernetAccPortHistoryESBF, cmEthernetAccPortHistoryESBP,
        cmEthernetAccPortHistoryESBS, cmEthernetAccPortHistoryESC,
        cmEthernetAccPortHistoryESCAE, cmEthernetAccPortHistoryESDE,
        cmEthernetAccPortHistoryESF, cmEthernetAccPortHistoryESFS,
        cmEthernetAccPortHistoryESJ, cmEthernetAccPortHistoryESMF,
        cmEthernetAccPortHistoryESMP, cmEthernetAccPortHistoryESO,
        cmEthernetAccPortHistoryESOF, cmEthernetAccPortHistoryESOP,
        cmEthernetAccPortHistoryESP, cmEthernetAccPortHistoryESP64,
        cmEthernetAccPortHistoryESP65, cmEthernetAccPortHistoryESP128,
        cmEthernetAccPortHistoryESP256, cmEthernetAccPortHistoryESP512,
        cmEthernetAccPortHistoryESP1024, cmEthernetAccPortHistoryESP1519,
        cmEthernetAccPortHistoryESUF, cmEthernetAccPortHistoryESUP,
        cmEthernetAccPortHistoryL2CPFD, cmEthernetAccPortHistoryL2CPFP,
        cmEthernetAccPortHistoryLES, cmEthernetAccPortHistoryLBC,
        cmEthernetAccPortHistoryOPT, cmEthernetAccPortHistoryOPR,
        cmEthernetAccPortHistoryAUFD, cmEthernetAccPortHistoryAPFD,
        cmEthernetAccPortHistoryABRRx, cmEthernetAccPortHistoryABRTx,
        cmEthernetAccPortHistoryTemp, cmEthernetAccPortHistoryUAS,
        cmEthernetAccPortHistoryIBRMaxRx, cmEthernetAccPortHistoryIBRMaxTx,
        cmEthernetAccPortHistoryIBRMinRx, cmEthernetAccPortHistoryIBRMinTx,
        cmEthernetAccPortHistoryIBRRx, cmEthernetAccPortHistoryIBRTx,

        cmEthernetAccPortThresholdIndex, cmEthernetAccPortThresholdInterval,
        cmEthernetAccPortThresholdVariable, cmEthernetAccPortThresholdValueLo,
        cmEthernetAccPortThresholdValueHi, cmEthernetAccPortThresholdMonValue,
        cmEthernetAccPortThresholdVarOprVariance, 
        cmEthernetAccPortThresholdVarOptVariance,

        cmEthernetNetPortStatsIndex, cmEthernetNetPortStatsIntervalType,
        cmEthernetNetPortStatsValid, cmEthernetNetPortStatsAction,
        cmEthernetNetPortStatsESBF, cmEthernetNetPortStatsESBP,
        cmEthernetNetPortStatsESBS, cmEthernetNetPortStatsESC,
        cmEthernetNetPortStatsESCAE, cmEthernetNetPortStatsESDE,
        cmEthernetNetPortStatsESF, cmEthernetNetPortStatsESFS,
        cmEthernetNetPortStatsESJ, cmEthernetNetPortStatsESMF,
        cmEthernetNetPortStatsESMP, cmEthernetNetPortStatsESO,
        cmEthernetNetPortStatsESOF, cmEthernetNetPortStatsESOP,
        cmEthernetNetPortStatsESP, cmEthernetNetPortStatsESP64,
        cmEthernetNetPortStatsESP65, cmEthernetNetPortStatsESP128,
        cmEthernetNetPortStatsESP256, cmEthernetNetPortStatsESP512,
        cmEthernetNetPortStatsESP1024, cmEthernetNetPortStatsESP1519,
        cmEthernetNetPortStatsESUF, cmEthernetNetPortStatsESUP,
        cmEthernetNetPortStatsL2CPFD, cmEthernetNetPortStatsL2CPFP,
        cmEthernetNetPortStatsLES, cmEthernetNetPortStatsLBC,
        cmEthernetNetPortStatsOPT, cmEthernetNetPortStatsOPR,
        cmEthernetNetPortStatsAUFD, cmEthernetNetPortStatsAPFD,
        cmEthernetNetPortStatsABRRx, cmEthernetNetPortStatsABRTx,
        cmEthernetNetPortStatsPSC,
        cmEthernetNetPortStatsTemp, cmEthernetNetPortStatsUAS,
        cmEthernetNetPortStatsIBRMaxRx, cmEthernetNetPortStatsIBRMaxTx,
        cmEthernetNetPortStatsIBRMinRx, cmEthernetNetPortStatsIBRMinTx,
        cmEthernetNetPortStatsIBRRx, cmEthernetNetPortStatsIBRTx,

        cmEthernetNetPortHistoryIndex, cmEthernetNetPortHistoryTime,
        cmEthernetNetPortHistoryValid, cmEthernetNetPortHistoryAction,
        cmEthernetNetPortHistoryESBF, cmEthernetNetPortHistoryESBP,
        cmEthernetNetPortHistoryESBS, cmEthernetNetPortHistoryESC,
        cmEthernetNetPortHistoryESCAE, cmEthernetNetPortHistoryESDE,
        cmEthernetNetPortHistoryESF, cmEthernetNetPortHistoryESFS,
        cmEthernetNetPortHistoryESJ, cmEthernetNetPortHistoryESMF,
        cmEthernetNetPortHistoryESMP, cmEthernetNetPortHistoryESO,
        cmEthernetNetPortHistoryESOF, cmEthernetNetPortHistoryESOP,
        cmEthernetNetPortHistoryESP, cmEthernetNetPortHistoryESP64,
        cmEthernetNetPortHistoryESP65, cmEthernetNetPortHistoryESP128,
        cmEthernetNetPortHistoryESP256, cmEthernetNetPortHistoryESP512,
        cmEthernetNetPortHistoryESP1024, cmEthernetNetPortHistoryESP1519,
        cmEthernetNetPortHistoryESUF, cmEthernetNetPortHistoryESUP,
        cmEthernetNetPortHistoryL2CPFD, cmEthernetNetPortHistoryL2CPFP,
        cmEthernetNetPortHistoryLES, cmEthernetNetPortHistoryLBC,
        cmEthernetNetPortHistoryOPT, cmEthernetNetPortHistoryOPR,
        cmEthernetNetPortHistoryAUFD, cmEthernetNetPortHistoryAPFD,
        cmEthernetNetPortHistoryABRRx, cmEthernetNetPortHistoryABRTx,
        cmEthernetNetPortHistoryPSC,
        cmEthernetNetPortHistoryTemp, cmEthernetNetPortHistoryUAS,
        cmEthernetNetPortHistoryIBRMaxRx, cmEthernetNetPortHistoryIBRMaxTx,
        cmEthernetNetPortHistoryIBRMinRx, cmEthernetNetPortHistoryIBRMinTx,
        cmEthernetNetPortHistoryIBRRx, cmEthernetNetPortHistoryIBRTx,

        cmEthernetNetPortThresholdIndex, cmEthernetNetPortThresholdInterval,
        cmEthernetNetPortThresholdVariable, cmEthernetNetPortThresholdValueLo,
        cmEthernetNetPortThresholdValueHi, cmEthernetNetPortThresholdMonValue,
        cmEthernetNetPortThresholdVarOprVariance, 
        cmEthernetNetPortThresholdVarOptVariance,

        cmFlowStatsIndex, cmFlowStatsIntervalType, cmFlowStatsValid,
        cmFlowStatsAction, cmFlowStatsL2CPFD, cmFlowStatsABRA2N,
        cmFlowStatsABRRLA2N, cmFlowStatsABRRLRA2N, cmFlowStatsABRN2A,
        cmFlowStatsABRRLN2A,
        cmFlowStatsUAS, cmFlowStatsES, cmFlowStatsSES,
        cmFlowStatsFMGA2N, cmFlowStatsFMYA2N, cmFlowStatsFMYDA2N,
        cmFlowStatsFMRDA2N, cmFlowStatsBytesInA2N,
        cmFlowStatsBytesOutA2N, cmFlowStatsFMGN2A,
        cmFlowStatsFMYN2A, cmFlowStatsFMYDN2A,
        cmFlowStatsFMRDN2A, cmFlowStatsBytesInN2A,
        cmFlowStatsBytesOutN2A, cmFlowStatsFTDA2N,
        cmFlowStatsIBRA2NMax, cmFlowStatsIBRRlA2NMax,
        cmFlowStatsIBRA2NMin, cmFlowStatsIBRRlA2NMin,
        cmFlowStatsIBRA2N, cmFlowStatsIBRRlA2N,
        cmFlowStatsIBRN2AMax, cmFlowStatsIBRRlN2AMax,
        cmFlowStatsIBRN2AMin, cmFlowStatsIBRRlN2AMin,
        cmFlowStatsIBRN2A, cmFlowStatsIBRRlN2A,
        cmFlowStatsFMCDA2N, cmFlowStatsFBCDA2N,

        cmFlowHistoryIndex, cmFlowHistoryTime, cmFlowHistoryValid,
        cmFlowHistoryAction, cmFlowHistoryL2CPFD, cmFlowHistoryABRA2N,
        cmFlowHistoryABRRLA2N, cmFlowHistoryABRRLRA2N, cmFlowHistoryABRN2A,
        cmFlowHistoryABRRLN2A,
        cmFlowHistoryUAS, cmFlowHistoryES, cmFlowHistorySES,
        cmFlowHistoryFMGA2N, cmFlowHistoryFMYA2N, cmFlowHistoryFMYDA2N,
        cmFlowHistoryFMRDA2N, cmFlowHistoryBytesInA2N,
        cmFlowHistoryBytesOutA2N, cmFlowHistoryFMGN2A,
        cmFlowHistoryFMYN2A, cmFlowHistoryFMYDN2A,
        cmFlowHistoryFMRDN2A, cmFlowHistoryBytesInN2A,
        cmFlowHistoryBytesOutN2A, cmFlowHistoryFTDA2N,
        cmFlowHistoryIBRA2NMax, cmFlowHistoryIBRRlA2NMax,
        cmFlowHistoryIBRA2NMin, cmFlowHistoryIBRRlA2NMin,
        cmFlowHistoryIBRA2N, cmFlowHistoryIBRRlA2N,
        cmFlowHistoryIBRN2AMax, cmFlowHistoryIBRRlN2AMax,
        cmFlowHistoryIBRN2AMin, cmFlowHistoryIBRRlN2AMin,
        cmFlowHistoryIBRN2A, cmFlowHistoryIBRRlN2A,
        cmFlowHistoryFMCDA2N, cmFlowHistoryFBCDA2N,
        
        cmFlowThresholdIndex, cmFlowThresholdInterval, cmFlowThresholdVariable,
        cmFlowThresholdValueLo, cmFlowThresholdValueHi, cmFlowThresholdMonValue,
    
        cmQosShaperStatsIndex, cmQosShaperStatsIntervalType,
        cmQosShaperStatsValid, cmQosShaperStatsAction,
        cmQosShaperStatsBT, cmQosShaperStatsBTD, cmQosShaperStatsFD,
        cmQosShaperStatsFTD, cmQosShaperStatsBR, cmQosShaperStatsFR,
        cmQosShaperStatsABRRL, cmQosShaperStatsABRRLR,
        cmQosShaperStatsBREDD, cmQosShaperStatsFREDD,
    
        cmQosShaperHistoryIndex, cmQosShaperHistoryTime,
        cmQosShaperHistoryValid, cmQosShaperHistoryAction,
        cmQosShaperHistoryBT, cmQosShaperHistoryBTD, cmQosShaperHistoryFD,
        cmQosShaperHistoryFTD, cmQosShaperHistoryBR, cmQosShaperHistoryFR,
        cmQosShaperHistoryABRRL, cmQosShaperHistoryABRRLR,
        cmQosShaperHistoryBREDD, cmQosShaperHistoryFREDD,
    
        cmQosShaperThresholdIndex, cmQosShaperThresholdInterval,
        cmQosShaperThresholdVariable, cmQosShaperThresholdValueLo,
        cmQosShaperThresholdValueHi, cmQosShaperThresholdMonValue,

        cmQosFlowPolicerStatsIndex, cmQosFlowPolicerStatsIntervalType,
        cmQosFlowPolicerStatsValid, cmQosFlowPolicerStatsAction,
        cmQosFlowPolicerStatsFMG, cmQosFlowPolicerStatsFMY,
        cmQosFlowPolicerStatsFMYD, cmQosFlowPolicerStatsFMRD,
        cmQosFlowPolicerStatsBytesIn, cmQosFlowPolicerStatsBytesOut,
        cmQosFlowPolicerStatsABR, 
 
        cmQosFlowPolicerHistoryIndex, cmQosFlowPolicerHistoryTime, 
        cmQosFlowPolicerHistoryValid, cmQosFlowPolicerHistoryAction, 
        cmQosFlowPolicerHistoryFMG, cmQosFlowPolicerHistoryFMY, 
        cmQosFlowPolicerHistoryFMYD, cmQosFlowPolicerHistoryFMRD, 
        cmQosFlowPolicerHistoryBytesIn, cmQosFlowPolicerHistoryBytesOut, 
        cmQosFlowPolicerHistoryABR,

        cmQosFlowPolicerThresholdIndex, cmQosFlowPolicerThresholdInterval,
        cmQosFlowPolicerThresholdVariable, cmQosFlowPolicerThresholdValueLo,
        cmQosFlowPolicerThresholdValueHi, cmQosFlowPolicerThresholdMonValue,

        cmAccPortQosShaperStatsIndex, cmAccPortQosShaperStatsIntervalType,
        cmAccPortQosShaperStatsValid, cmAccPortQosShaperStatsAction,
        cmAccPortQosShaperStatsBT, cmAccPortQosShaperStatsBTD,
        cmAccPortQosShaperStatsFD, cmAccPortQosShaperStatsFTD,
        cmAccPortQosShaperStatsBR, cmAccPortQosShaperStatsFR,
        cmAccPortQosShaperStatsABRRL,
        cmAccPortQosShaperStatsBREDD, cmAccPortQosShaperStatsFREDD,

        cmAccPortQosShaperHistoryIndex, cmAccPortQosShaperHistoryTime,
        cmAccPortQosShaperHistoryValid, cmAccPortQosShaperHistoryAction,
        cmAccPortQosShaperHistoryBT, cmAccPortQosShaperHistoryBTD,
        cmAccPortQosShaperHistoryFD, cmAccPortQosShaperHistoryFTD,
        cmAccPortQosShaperHistoryBR, cmAccPortQosShaperHistoryFR,
        cmAccPortQosShaperHistoryABRRL,
        cmAccPortQosShaperHistoryBREDD, cmAccPortQosShaperHistoryFREDD,

        cmAccPortQosShaperThresholdIndex, cmAccPortQosShaperThresholdInterval,
        cmAccPortQosShaperThresholdVariable, cmAccPortQosShaperThresholdValueLo,
        cmAccPortQosShaperThresholdValueHi, cmAccPortQosShaperThresholdMonValue,
        
        cmEthernetTrafficPortStatsIndex, cmEthernetTrafficPortStatsIntervalType,
        cmEthernetTrafficPortStatsValid, cmEthernetTrafficPortStatsAction,
        cmEthernetTrafficPortStatsESBF, cmEthernetTrafficPortStatsESBP,
        cmEthernetTrafficPortStatsESBS, cmEthernetTrafficPortStatsESC,
        cmEthernetTrafficPortStatsESCAE, cmEthernetTrafficPortStatsESDE,
        cmEthernetTrafficPortStatsESF, cmEthernetTrafficPortStatsESFS,
        cmEthernetTrafficPortStatsESJ, cmEthernetTrafficPortStatsESMF,
        cmEthernetTrafficPortStatsESMP, cmEthernetTrafficPortStatsESO,
        cmEthernetTrafficPortStatsESOF, cmEthernetTrafficPortStatsESOP,
        cmEthernetTrafficPortStatsESP, cmEthernetTrafficPortStatsESP64,
        cmEthernetTrafficPortStatsESP65, cmEthernetTrafficPortStatsESP128,
        cmEthernetTrafficPortStatsESP256, cmEthernetTrafficPortStatsESP512,
        cmEthernetTrafficPortStatsESP1024, cmEthernetTrafficPortStatsESP1519,
        cmEthernetTrafficPortStatsESUF, cmEthernetTrafficPortStatsESUP,
        cmEthernetTrafficPortStatsL2CPFD,
        cmEthernetTrafficPortStatsL2CPFP, cmEthernetTrafficPortStatsLES,
        cmEthernetTrafficPortStatsLBC, cmEthernetTrafficPortStatsOPT,
        cmEthernetTrafficPortStatsOPR, cmEthernetTrafficPortStatsAUFD,
        cmEthernetTrafficPortStatsAPFD, cmEthernetTrafficPortStatsABRRx,
        cmEthernetTrafficPortStatsABRTx,cmEthernetTrafficPortStatsATFD,
        cmEthernetTrafficPortStatsUAS,
        cmEthernetTrafficPortStatsTemp,cmEthernetTrafficPortStatsLkupFails,
        
        cmEthernetTrafficPortHistoryIndex,cmEthernetTrafficPortHistoryTime,
        cmEthernetTrafficPortHistoryValid,cmEthernetTrafficPortHistoryAction,
        cmEthernetTrafficPortHistoryESBF,cmEthernetTrafficPortHistoryESBP,
        cmEthernetTrafficPortHistoryESBS,cmEthernetTrafficPortHistoryESC,
        cmEthernetTrafficPortHistoryESCAE,cmEthernetTrafficPortHistoryESDE,
        cmEthernetTrafficPortHistoryESF,cmEthernetTrafficPortHistoryESFS,
        cmEthernetTrafficPortHistoryESJ,cmEthernetTrafficPortHistoryESMF,
        cmEthernetTrafficPortHistoryESMP,cmEthernetTrafficPortHistoryESO,
        cmEthernetTrafficPortHistoryESOF,cmEthernetTrafficPortHistoryESOP,
        cmEthernetTrafficPortHistoryESP,cmEthernetTrafficPortHistoryESP64,
        cmEthernetTrafficPortHistoryESP65,cmEthernetTrafficPortHistoryESP128,
        cmEthernetTrafficPortHistoryESP256,cmEthernetTrafficPortHistoryESP512,
        cmEthernetTrafficPortHistoryESP1024,cmEthernetTrafficPortHistoryESP1519,
        cmEthernetTrafficPortHistoryESUF,cmEthernetTrafficPortHistoryESUP,
        cmEthernetTrafficPortHistoryL2CPFD,
        cmEthernetTrafficPortHistoryL2CPFP,cmEthernetTrafficPortHistoryLES,
        cmEthernetTrafficPortHistoryLBC,cmEthernetTrafficPortHistoryOPT,
        cmEthernetTrafficPortHistoryOPR,cmEthernetTrafficPortHistoryAUFD,
        cmEthernetTrafficPortHistoryAPFD,cmEthernetTrafficPortHistoryABRRx,
        cmEthernetTrafficPortHistoryABRTx,cmEthernetTrafficPortHistoryATFD,
        cmEthernetTrafficPortHistoryUAS,
        cmEthernetTrafficPortHistoryTemp,cmEthernetTrafficPortHistoryLkupFails,
        
        cmEthernetTrafficPortThresholdIndex,cmEthernetTrafficPortThresholdInterval,
        cmEthernetTrafficPortThresholdVariable,cmEthernetTrafficPortThresholdValueLo,
        cmEthernetTrafficPortThresholdValueHi,cmEthernetTrafficPortThresholdMonValue,
        
        cmEthernetTrafficPortThresholdVarOprVariance, 
        cmEthernetTrafficPortThresholdVarOptVariance,
        
        cmFlowPointStatsIndex,cmFlowPointStatsIntervalType,
        cmFlowPointStatsValid,cmFlowPointStatsAction,
        cmFlowPointStatsL2CPFD,cmFlowPointStatsABRRx,
        cmFlowPointStatsABRRLRx,cmFlowPointStatsUAS,
        cmFlowPointStatsSES,
        cmFlowPointStatsFMG,cmFlowPointStatsFMY,
        cmFlowPointStatsFMRD,
        cmFlowPointStatsFTD,cmFlowPointStatsBytesIn,
        cmFlowPointStatsBytesOut,cmFlowPointStatsFREDD,
        cmFlowPointStatsFACLD,
        
        cmFlowPointHistoryIndex,cmFlowPointHistoryTime,
        cmFlowPointHistoryValid,cmFlowPointHistoryAction,
        cmFlowPointHistoryL2CPFD,cmFlowPointHistoryABRRx,
        cmFlowPointHistoryABRRLRx,cmFlowPointHistoryUAS,
        cmFlowPointHistorySES,
        cmFlowPointHistoryFMG,cmFlowPointHistoryFMY,
        cmFlowPointHistoryFMRD,
        cmFlowPointHistoryFTD,cmFlowPointHistoryBytesIn,
        cmFlowPointHistoryBytesOut,cmFlowPointHistoryFREDD,
        cmFlowPointHistoryFACLD,
        
        cmFlowPointThresholdIndex,cmFlowPointThresholdInterval,
        cmFlowPointThresholdVariable,cmFlowPointThresholdValueLo,
        cmFlowPointThresholdValueHi,cmFlowPointThresholdMonValue,
        
        cmOAMFlowPointStatsIndex,cmOAMFlowPointStatsIntervalType,
        cmOAMFlowPointStatsValid,cmOAMFlowPointStatsAction,
        cmOAMFlowPointStatsUAS,cmOAMFlowPointStatsSES,
        
        cmOAMFlowPointHistoryIndex,cmOAMFlowPointHistoryTime,
        cmOAMFlowPointHistoryValid,cmOAMFlowPointHistoryAction,
        cmOAMFlowPointHistoryUAS,cmOAMFlowPointHistorySES,
        
        cmOAMFlowPointThresholdIndex,cmOAMFlowPointThresholdInterval,
        cmOAMFlowPointThresholdVariable,cmOAMFlowPointThresholdValueLo,
        cmOAMFlowPointThresholdValueHi,cmOAMFlowPointThresholdMonValue,
                
        cmQosPolicerV2StatsIndex,cmQosPolicerV2StatsIntervalType,
        cmQosPolicerV2StatsValid,cmQosPolicerV2StatsAction,
        cmQosPolicerV2StatsFMG,cmQosPolicerV2StatsFMY,
        cmQosPolicerV2StatsFMYD,cmQosPolicerV2StatsFMRD,
        cmQosPolicerV2StatsBytesIn,cmQosPolicerV2StatsBytesOut,
        cmQosPolicerV2StatsABR,

        cmQosPolicerV2HistoryIndex,cmQosPolicerV2HistoryTime,
        cmQosPolicerV2HistoryValid,cmQosPolicerV2HistoryAction,
        cmQosPolicerV2HistoryFMG,cmQosPolicerV2HistoryFMY,
        cmQosPolicerV2HistoryFMYD,cmQosPolicerV2HistoryFMRD,
        cmQosPolicerV2HistoryBytesIn,cmQosPolicerV2HistoryBytesOut,
        cmQosPolicerV2HistoryABR,
        
        cmQosPolicerV2ThresholdIndex, cmQosPolicerV2ThresholdInterval,
        cmQosPolicerV2ThresholdVariable, cmQosPolicerV2ThresholdValueLo,
        cmQosPolicerV2ThresholdValueHi, cmQosPolicerV2ThresholdMonValue,
        
        cmQosShaperV2StatsIndex, cmQosShaperV2StatsIntervalType,
        cmQosShaperV2StatsValid, cmQosShaperV2StatsAction,
        cmQosShaperV2StatsBT, cmQosShaperV2StatsBTD,
        cmQosShaperV2StatsFD, cmQosShaperV2StatsFTD,
        cmQosShaperV2StatsABRRL,cmQosShaperV2StatsBREDD,
        cmQosShaperV2StatsFREDD,
        
        cmQosShaperV2HistoryIndex, cmQosShaperV2HistoryTime,
        cmQosShaperV2HistoryValid, cmQosShaperV2HistoryAction,
        cmQosShaperV2HistoryBT, cmQosShaperV2HistoryBTD,
        cmQosShaperV2HistoryFD, cmQosShaperV2HistoryFTD,
        cmQosShaperV2HistoryABRRL,cmQosShaperV2HistoryBREDD,
        cmQosShaperV2HistoryFREDD,
        
        cmQosShaperV2ThresholdIndex, cmQosShaperV2ThresholdInterval,
        cmQosShaperV2ThresholdVariable,cmQosShaperV2ThresholdValueLo,
        cmQosShaperV2ThresholdValueHi,cmQosShaperV2ThresholdMonValue,
        
        cmLagStatsIndex, cmLagStatsIntervalType,
        cmLagStatsValid, cmLagStatsAction,
        cmLagStatsESBF, cmLagStatsESBP,
        cmLagStatsESBS, cmLagStatsESC,
        cmLagStatsESCAE, cmLagStatsESDE,
        cmLagStatsESF, cmLagStatsESFS,
        cmLagStatsESJ, cmLagStatsESMF,
        cmLagStatsESMP, cmLagStatsESO,
        cmLagStatsESOF, cmLagStatsESOP,
        cmLagStatsESP, cmLagStatsESP64,
        cmLagStatsESP65, cmLagStatsESP128,
        cmLagStatsESP256, cmLagStatsESP512,
        cmLagStatsESP1024, cmLagStatsESP1519,
        cmLagStatsESUF, cmLagStatsESUP,
        cmLagStatsL2CPFD,
        cmLagStatsL2CPFP,cmLagStatsAUFD,
        cmLagStatsAPFD, cmLagStatsABRRx,
        cmLagStatsABRTx,cmLagStatsATFD,
        cmLagStatsLkupFails,
        
        cmLagHistoryIndex,cmLagHistoryTime,
        cmLagHistoryValid,cmLagHistoryAction,
        cmLagHistoryESBF,cmLagHistoryESBP,
        cmLagHistoryESBS,cmLagHistoryESC,
        cmLagHistoryESCAE,cmLagHistoryESDE,
        cmLagHistoryESF,cmLagHistoryESFS,
        cmLagHistoryESJ,cmLagHistoryESMF,
        cmLagHistoryESMP,cmLagHistoryESO,
        cmLagHistoryESOF,cmLagHistoryESOP,
        cmLagHistoryESP,cmLagHistoryESP64,
        cmLagHistoryESP65,cmLagHistoryESP128,
        cmLagHistoryESP256,cmLagHistoryESP512,
        cmLagHistoryESP1024,cmLagHistoryESP1519,
        cmLagHistoryESUF,cmLagHistoryESUP,
        cmLagHistoryL2CPFD,
        cmLagHistoryL2CPFP,cmLagHistoryAUFD,
        cmLagHistoryAPFD,cmLagHistoryABRRx,
        cmLagHistoryABRTx,cmLagHistoryATFD,
        cmLagHistoryLkupFails,

        cmLagThresholdIndex,cmLagThresholdInterval,
        cmLagThresholdVariable,cmLagThresholdValueLo,
        cmLagThresholdValueHi,cmLagThresholdMonValue,

        cmTrafficPortQosShaperStatsIndex, 
        cmTrafficPortQosShaperStatsIntervalType,
        cmTrafficPortQosShaperStatsValid, cmTrafficPortQosShaperStatsAction,
        cmTrafficPortQosShaperStatsBT, cmTrafficPortQosShaperStatsBTD,
        cmTrafficPortQosShaperStatsFD, cmTrafficPortQosShaperStatsFTD, 
        cmTrafficPortQosShaperStatsABRRL, cmTrafficPortQosShaperStatsBREDD,
        cmTrafficPortQosShaperStatsFREDD,
        
        cmTrafficPortQosShaperHistoryIndex, cmTrafficPortQosShaperHistoryTime,
        cmTrafficPortQosShaperHistoryValid, cmTrafficPortQosShaperHistoryAction,
        cmTrafficPortQosShaperHistoryBT, cmTrafficPortQosShaperHistoryBTD,
        cmTrafficPortQosShaperHistoryFD, cmTrafficPortQosShaperHistoryFTD,
        cmTrafficPortQosShaperHistoryABRRL, cmTrafficPortQosShaperHistoryBREDD,
        cmTrafficPortQosShaperHistoryFREDD,
        
        cmTrafficPortQosShaperThresholdIndex, 
        cmTrafficPortQosShaperThresholdInterval,
        cmTrafficPortQosShaperThresholdVariable, 
        cmTrafficPortQosShaperThresholdValueLo,
        cmTrafficPortQosShaperThresholdValueHi, 
        cmTrafficPortQosShaperThresholdMonValue,
        
        f3NetPortQosShaperStatsIndex, f3NetPortQosShaperStatsIntervalType,
        f3NetPortQosShaperStatsValid, f3NetPortQosShaperStatsAction,
        f3NetPortQosShaperStatsBT, f3NetPortQosShaperStatsBTD,
        f3NetPortQosShaperStatsFD, f3NetPortQosShaperStatsFTD,
        f3NetPortQosShaperStatsBR, f3NetPortQosShaperStatsFR,
        f3NetPortQosShaperStatsABRRL, f3NetPortQosShaperStatsBREDD,
        f3NetPortQosShaperStatsFREDD,
        
        f3NetPortQosShaperHistoryIndex, f3NetPortQosShaperHistoryTime,
        f3NetPortQosShaperHistoryValid, f3NetPortQosShaperHistoryAction,
        f3NetPortQosShaperHistoryBT, f3NetPortQosShaperHistoryBTD,
        f3NetPortQosShaperHistoryFD, f3NetPortQosShaperHistoryFTD,
        f3NetPortQosShaperHistoryBR, f3NetPortQosShaperHistoryFR,
        f3NetPortQosShaperHistoryABRRL, f3NetPortQosShaperHistoryBREDD,
        f3NetPortQosShaperHistoryFREDD,
        
        f3NetPortQosShaperThresholdIndex, f3NetPortQosShaperThresholdInterval,
        f3NetPortQosShaperThresholdVariable, f3NetPortQosShaperThresholdValueLo,
        f3NetPortQosShaperThresholdValueHi, f3NetPortQosShaperThresholdMonValue,
        
        ocnStmStatsIndex, ocnStmStatsIntervalType,
        ocnStmStatsValid, ocnStmStatsAction,
        ocnStmStatsLineLBC, ocnStmStatsLineOPT,
        ocnStmStatsLineOPR, ocnStmStatsLineTemp,
        ocnStmStatsLinePSC, ocnStmStatsLineESs,
        ocnStmStatsLineSESs, ocnStmStatsLineCVs,
        ocnStmStatsLineUASs, ocnStmStatsLineFCs,
        ocnStmStatsLineFarEndESs, ocnStmStatsLineFarEndSESs, 
        ocnStmStatsLineFarEndCVs, ocnStmStatsLineFarEndUASs, 
        ocnStmStatsSectionESs, ocnStmStatsSectionSESs, 
        ocnStmStatsSectionCVs, ocnStmStatsSectionSEFs, 
        ocnStmStatsSectionUASs,
        
        ocnStmHistoryIndex, ocnStmHistoryTime,
        ocnStmHistoryValid, ocnStmHistoryAction,
        ocnStmHistoryLineLBC, ocnStmHistoryLineOPT,
        ocnStmHistoryLineOPR, ocnStmHistoryLineTemp,
        ocnStmHistoryLinePSC, ocnStmHistoryLineESs,
        ocnStmHistoryLineSESs, ocnStmHistoryLineCVs,
        ocnStmHistoryLineUASs, ocnStmHistoryLineFCs,
        ocnStmHistoryLineFarEndESs, ocnStmHistoryLineFarEndSESs, 
        ocnStmHistoryLineFarEndCVs, ocnStmHistoryLineFarEndUASs, 
        ocnStmHistorySectionESs, ocnStmHistorySectionSESs, 
        ocnStmHistorySectionCVs, ocnStmHistorySectionSEFs, 
        ocnStmHistorySectionUASs,
        
        ocnStmThresholdIndex, ocnStmThresholdInterval,
        ocnStmThresholdVariable, ocnStmThresholdValueLo,
        ocnStmThresholdValueHi, ocnStmThresholdMonValue,
        
        stsVcPathStatsIndex, stsVcPathStatsIntervalType,
        stsVcPathStatsValid, stsVcPathStatsAction,
        stsVcPathStatsESs, stsVcPathStatsSESs,
        stsVcPathStatsCVs, stsVcPathStatsUASs,
        stsVcPathFarEndStatsESs, stsVcPathFarEndStatsSESs,
        stsVcPathFarEndStatsCVs, stsVcPathFarEndStatsUASs,
        
        stsVcPathHistoryIndex, stsVcPathHistoryTime,
        stsVcPathHistoryValid, stsVcPathHistoryAction,
        stsVcPathHistoryESs, stsVcPathHistorySESs,
        stsVcPathHistoryCVs, stsVcPathHistoryUASs,
        stsVcPathFarEndHistoryESs, stsVcPathFarEndHistorySESs,
        stsVcPathFarEndHistoryCVs, stsVcPathFarEndHistoryUASs,
        
        stsVcPathThresholdIndex, stsVcPathThresholdInterval,
        stsVcPathThresholdVariable, stsVcPathThresholdValueLo,
        stsVcPathThresholdValueHi, stsVcPathThresholdMonValue,
        
        vtVcPathStatsIndex, vtVcPathStatsIntervalType,
        vtVcPathStatsValid, vtVcPathStatsAction,
        vtVcPathStatsESs, vtVcPathStatsSESs,
        vtVcPathStatsCVs, vtVcPathStatsUASs,
        vtVcPathFarEndStatsESs, vtVcPathFarEndStatsSESs,
        vtVcPathFarEndStatsCVs, vtVcPathFarEndStatsUASs,
        
        vtVcPathHistoryIndex, vtVcPathHistoryTime,
        vtVcPathHistoryValid, vtVcPathHistoryAction,
        vtVcPathHistoryESs, vtVcPathHistorySESs,
        vtVcPathHistoryCVs, vtVcPathHistoryUASs,
        vtVcPathFarEndHistoryESs, vtVcPathFarEndHistorySESs,
        vtVcPathFarEndHistoryCVs, vtVcPathFarEndHistoryUASs,
        
        vtVcPathThresholdIndex, vtVcPathThresholdInterval,
        vtVcPathThresholdVariable, vtVcPathThresholdValueLo,
        vtVcPathThresholdValueHi, vtVcPathThresholdMonValue,
        
        e1t1StatsIndex, e1t1StatsIntervalType,
        e1t1StatsValid, e1t1StatsAction, 
        e1t1StatsLineCVs, e1t1StatsLineESs,
        e1t1StatsLineSESs, e1t1StatsLineESsFarEnd,
        e1t1StatsLineUASs, e1t1StatsLineLOSSs,
        e1t1StatsPathCVs, e1t1StatsPathESs, 
        e1t1StatsPathSESs, e1t1StatsPathUASs, 
        e1t1StatsPathCVsFarEnd, e1t1StatsPathESsFarEnd, 
        e1t1StatsPathSESsFarEnd, e1t1StatsPathSEFsFarEnd, 
        e1t1StatsPathUASsFarEnd, e1t1StatsPathFCs, 
        e1t1StatsPathFCsFarEnd, e1t1StatsPathAISs, 
        e1t1StatsPathSASs,
        
        e1t1HistoryIndex, e1t1HistoryTime,
        e1t1HistoryValid, e1t1HistoryAction,
        e1t1HistoryLineCVs, e1t1HistoryLineESs,
        e1t1HistoryLineSESs, e1t1HistoryLineESsFarEnd,
        e1t1HistoryLineUASs, e1t1HistoryLineLOSSs,
        e1t1HistoryPathCVs, e1t1HistoryPathESs, 
        e1t1HistoryPathSESs, e1t1HistoryPathUASs, 
        e1t1HistoryPathCVsFarEnd, e1t1HistoryPathESsFarEnd, 
        e1t1HistoryPathSESsFarEnd, e1t1HistoryPathSEFsFarEnd, 
        e1t1HistoryPathUASsFarEnd, e1t1HistoryPathFCs, 
        e1t1HistoryPathFCsFarEnd, e1t1HistoryPathAISs, 
        e1t1HistoryPathSASs,
        
        e1t1ThresholdIndex, e1t1ThresholdInterval,
        e1t1ThresholdVariable, e1t1ThresholdValueLo,
        e1t1ThresholdValueHi, e1t1ThresholdMonValue,

        e3t3StatsIndex, e3t3StatsIntervalType,
        e3t3StatsValid, e3t3StatsAction,
        e3t3StatsLineCVs, e3t3StatsLineESs,
        e3t3StatsLineSESs, e3t3StatsLineLOSSs,
        e3t3StatsPathPCVs, e3t3StatsPathCCVs,
        e3t3StatsPathAISs, e3t3StatsPathPESs,
        e3t3StatsPathCESs, e3t3StatsPathFCs,
        e3t3StatsPathSEFs, e3t3StatsPathPSESs,
        e3t3StatsPathCSESs, e3t3StatsPathPUASs,
        e3t3StatsPathCUASs, e3t3StatsPathCCVsFarEnd,
        e3t3StatsPathCESsFarEnd, e3t3StatsPathCSESsFarEnd,
        e3t3StatsPathCFCsFarEnd, e3t3StatsPathCUASsFarEnd,
        
        e3t3HistoryIndex, e3t3HistoryTime,
        e3t3HistoryValid, e3t3HistoryAction,
        e3t3HistoryLineCVs, e3t3HistoryLineESs,
        e3t3HistoryLineSESs, e3t3HistoryLineLOSSs,
        e3t3HistoryPathPCVs, e3t3HistoryPathCCVs,
        e3t3HistoryPathAISs, e3t3HistoryPathPESs,
        e3t3HistoryPathCESs, e3t3HistoryPathFCs,
        e3t3HistoryPathSEFs, e3t3HistoryPathPSESs,
        e3t3HistoryPathCSESs, e3t3HistoryPathPUASs,
        e3t3HistoryPathCUASs, e3t3HistoryPathCCVsFarEnd,
        e3t3HistoryPathCESsFarEnd, e3t3HistoryPathCSESsFarEnd,
        e3t3HistoryPathCFCsFarEnd, e3t3HistoryPathCUASsFarEnd,
        
        e3t3ThresholdIndex, e3t3ThresholdInterval,
        e3t3ThresholdVariable, e3t3ThresholdValueLo,
        e3t3ThresholdValueHi, e3t3ThresholdMonValue
    }
    STATUS  deprecated
    DESCRIPTION
            "********************THIS GROUP IS NOW DEPRECATED*********************
            A collection of objects used to manage the CM Perf
             group."
    ::= { cmPerfGroups 1 }

cmPerfNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        cmEthernetAccPortThresholdCrossingAlert,
        cmEthernetNetPortThresholdCrossingAlert,
        cmFlowThresholdCrossingAlert, 
        cmQosShaperThresholdCrossingAlert,
        cmQosFlowPolicerThresholdCrossingAlert,
        cmAccPortQosShaperThresholdCrossingAlert,
        f3NetPortQosShaperThresholdCrossingAlert,
        ocnStmThresholdCrossingAlert,
        stsVcPathThresholdCrossingAlert,
        vtVcPathThresholdCrossingAlert,
        e1t1ThresholdCrossingAlert,
        e3t3ThresholdCrossingAlert
    }
    STATUS  current
    DESCRIPTION
            "A collection of notifications related to FSP150CM, FSP150CC
             Threshold Crossing Alerts."
    ::= { cmPerfGroups 2 }

ethernetAccessPortPMGroup OBJECT-GROUP
    OBJECTS {
        cmEthernetAccPortStatsIndex, cmEthernetAccPortStatsIntervalType,
        cmEthernetAccPortStatsValid, cmEthernetAccPortStatsAction,
        cmEthernetAccPortStatsESBF, cmEthernetAccPortStatsESBP,
        cmEthernetAccPortStatsESBS, cmEthernetAccPortStatsESC,
        cmEthernetAccPortStatsESCAE,cmEthernetAccPortStatsESDE,
        cmEthernetAccPortStatsESF, cmEthernetAccPortStatsESFS,
        cmEthernetAccPortStatsESJ, cmEthernetAccPortStatsESMF,
        cmEthernetAccPortStatsESMP, cmEthernetAccPortStatsESO,
        cmEthernetAccPortStatsESOF, cmEthernetAccPortStatsESOP,
        cmEthernetAccPortStatsESP, cmEthernetAccPortStatsESP64, 
        cmEthernetAccPortStatsESP65, cmEthernetAccPortStatsESP128,
        cmEthernetAccPortStatsESP256, cmEthernetAccPortStatsESP512,
        cmEthernetAccPortStatsESP1024, cmEthernetAccPortStatsESP1519, 
        cmEthernetAccPortStatsESUF, cmEthernetAccPortStatsESUP,
        cmEthernetAccPortStatsL2CPFD, cmEthernetAccPortStatsL2CPFP,
        cmEthernetAccPortStatsLES, cmEthernetAccPortStatsLBC,
        cmEthernetAccPortStatsOPT, cmEthernetAccPortStatsOPR,
        cmEthernetAccPortStatsAUFD, cmEthernetAccPortStatsAPFD,
        cmEthernetAccPortStatsABRRx, cmEthernetAccPortStatsABRTx,
        cmEthernetAccPortStatsTemp, cmEthernetAccPortStatsUAS,
        cmEthernetAccPortStatsL2PTRxFramesEncap,
        cmEthernetAccPortStatsL2PTTxFramesDecap,
        cmEthernetAccPortStatsIBRMaxRx, cmEthernetAccPortStatsIBRMaxTx,
        cmEthernetAccPortStatsIBRMinRx, cmEthernetAccPortStatsIBRMinTx,
        cmEthernetAccPortStatsIBRRx, cmEthernetAccPortStatsIBRTx,   
        cmEthernetAccPortStatsLkupFails,     

        cmEthernetAccPortHistoryIndex, cmEthernetAccPortHistoryTime,
        cmEthernetAccPortHistoryValid, cmEthernetAccPortHistoryAction,
        cmEthernetAccPortHistoryESBF, cmEthernetAccPortHistoryESBP,
        cmEthernetAccPortHistoryESBS, cmEthernetAccPortHistoryESC,
        cmEthernetAccPortHistoryESCAE, cmEthernetAccPortHistoryESDE,
        cmEthernetAccPortHistoryESF, cmEthernetAccPortHistoryESFS,
        cmEthernetAccPortHistoryESJ, cmEthernetAccPortHistoryESMF,
        cmEthernetAccPortHistoryESMP, cmEthernetAccPortHistoryESO,
        cmEthernetAccPortHistoryESOF, cmEthernetAccPortHistoryESOP,
        cmEthernetAccPortHistoryESP, cmEthernetAccPortHistoryESP64,
        cmEthernetAccPortHistoryESP65, cmEthernetAccPortHistoryESP128,
        cmEthernetAccPortHistoryESP256, cmEthernetAccPortHistoryESP512,
        cmEthernetAccPortHistoryESP1024, cmEthernetAccPortHistoryESP1519,
        cmEthernetAccPortHistoryESUF, cmEthernetAccPortHistoryESUP,
        cmEthernetAccPortHistoryL2CPFD, cmEthernetAccPortHistoryL2CPFP,
        cmEthernetAccPortHistoryLES, cmEthernetAccPortHistoryLBC,
        cmEthernetAccPortHistoryOPT, cmEthernetAccPortHistoryOPR,
        cmEthernetAccPortHistoryAUFD, cmEthernetAccPortHistoryAPFD,
        cmEthernetAccPortHistoryABRRx, cmEthernetAccPortHistoryABRTx,
        cmEthernetAccPortHistoryTemp, cmEthernetAccPortHistoryUAS,
        cmEthernetAccPortHistoryL2PTRxFramesEncap,
        cmEthernetAccPortHistoryL2PTTxFramesDecap,
        cmEthernetAccPortHistoryIBRMaxRx, cmEthernetAccPortHistoryIBRMaxTx,
        cmEthernetAccPortHistoryIBRMinRx, cmEthernetAccPortHistoryIBRMinTx,
        cmEthernetAccPortHistoryIBRRx, cmEthernetAccPortHistoryIBRTx,
        cmEthernetAccPortHistoryLkupFails,

        cmEthernetAccPortThresholdIndex, cmEthernetAccPortThresholdInterval,
        cmEthernetAccPortThresholdVariable, cmEthernetAccPortThresholdValueLo,
        cmEthernetAccPortThresholdValueHi, cmEthernetAccPortThresholdMonValue,
        cmEthernetAccPortThresholdVarOprVariance, 
        cmEthernetAccPortThresholdVarOptVariance
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Ethernet Access
             Port PM function." 
    ::= { cmPerfGroups 3 }

ethernetNetworkPortPMGroup OBJECT-GROUP
    OBJECTS {
        cmEthernetNetPortStatsIndex, cmEthernetNetPortStatsIntervalType,
        cmEthernetNetPortStatsValid, cmEthernetNetPortStatsAction,
        cmEthernetNetPortStatsESBF, cmEthernetNetPortStatsESBP,
        cmEthernetNetPortStatsESBS, cmEthernetNetPortStatsESC,
        cmEthernetNetPortStatsESCAE, cmEthernetNetPortStatsESDE,
        cmEthernetNetPortStatsESF, cmEthernetNetPortStatsESFS,
        cmEthernetNetPortStatsESJ, cmEthernetNetPortStatsESMF,
        cmEthernetNetPortStatsESMP, cmEthernetNetPortStatsESO,
        cmEthernetNetPortStatsESOF, cmEthernetNetPortStatsESOP,
        cmEthernetNetPortStatsESP, cmEthernetNetPortStatsESP64,
        cmEthernetNetPortStatsESP65, cmEthernetNetPortStatsESP128,
        cmEthernetNetPortStatsESP256, cmEthernetNetPortStatsESP512,
        cmEthernetNetPortStatsESP1024, cmEthernetNetPortStatsESP1519,
        cmEthernetNetPortStatsESUF, cmEthernetNetPortStatsESUP,
        cmEthernetNetPortStatsL2CPFD, cmEthernetNetPortStatsL2CPFP,
        cmEthernetNetPortStatsLES, cmEthernetNetPortStatsLBC,
        cmEthernetNetPortStatsOPT, cmEthernetNetPortStatsOPR,
        cmEthernetNetPortStatsAUFD, cmEthernetNetPortStatsAPFD,
        cmEthernetNetPortStatsABRRx, cmEthernetNetPortStatsABRTx,
        cmEthernetNetPortStatsPSC,
        cmEthernetNetPortStatsTemp, cmEthernetNetPortStatsUAS,
        cmEthernetNetPortStatsL2PTRxFramesEncap,
        cmEthernetNetPortStatsL2PTTxFramesDecap,
        cmEthernetNetPortStatsIBRMaxRx, cmEthernetNetPortStatsIBRMaxTx,
        cmEthernetNetPortStatsIBRMinRx, cmEthernetNetPortStatsIBRMinTx,
        cmEthernetNetPortStatsIBRRx, cmEthernetNetPortStatsIBRTx, 
        cmEthernetNetPortStatsLkupFails,       

        cmEthernetNetPortHistoryIndex, cmEthernetNetPortHistoryTime,
        cmEthernetNetPortHistoryValid, cmEthernetNetPortHistoryAction,
        cmEthernetNetPortHistoryESBF, cmEthernetNetPortHistoryESBP,
        cmEthernetNetPortHistoryESBS, cmEthernetNetPortHistoryESC,
        cmEthernetNetPortHistoryESCAE, cmEthernetNetPortHistoryESDE,
        cmEthernetNetPortHistoryESF, cmEthernetNetPortHistoryESFS,
        cmEthernetNetPortHistoryESJ, cmEthernetNetPortHistoryESMF,
        cmEthernetNetPortHistoryESMP, cmEthernetNetPortHistoryESO,
        cmEthernetNetPortHistoryESOF, cmEthernetNetPortHistoryESOP,
        cmEthernetNetPortHistoryESP, cmEthernetNetPortHistoryESP64,
        cmEthernetNetPortHistoryESP65, cmEthernetNetPortHistoryESP128,
        cmEthernetNetPortHistoryESP256, cmEthernetNetPortHistoryESP512,
        cmEthernetNetPortHistoryESP1024, cmEthernetNetPortHistoryESP1519,
        cmEthernetNetPortHistoryESUF, cmEthernetNetPortHistoryESUP,
        cmEthernetNetPortHistoryL2CPFD, cmEthernetNetPortHistoryL2CPFP,
        cmEthernetNetPortHistoryLES, cmEthernetNetPortHistoryLBC,
        cmEthernetNetPortHistoryOPT, cmEthernetNetPortHistoryOPR,
        cmEthernetNetPortHistoryAUFD, cmEthernetNetPortHistoryAPFD,
        cmEthernetNetPortHistoryABRRx, cmEthernetNetPortHistoryABRTx,
        cmEthernetNetPortHistoryPSC,
        cmEthernetNetPortHistoryTemp, cmEthernetNetPortHistoryUAS,
        cmEthernetNetPortHistoryL2PTRxFramesEncap,
        cmEthernetNetPortHistoryL2PTTxFramesDecap,
        cmEthernetNetPortHistoryIBRMaxRx, cmEthernetNetPortHistoryIBRMaxTx,
        cmEthernetNetPortHistoryIBRMinRx, cmEthernetNetPortHistoryIBRMinTx,
        cmEthernetNetPortHistoryIBRRx, cmEthernetNetPortHistoryIBRTx,      
        cmEthernetNetPortHistoryLkupFails,  

        cmEthernetNetPortThresholdIndex, cmEthernetNetPortThresholdInterval,
        cmEthernetNetPortThresholdVariable, cmEthernetNetPortThresholdValueLo,
        cmEthernetNetPortThresholdValueHi, cmEthernetNetPortThresholdMonValue,
        cmEthernetNetPortThresholdVarOprVariance, 
        cmEthernetNetPortThresholdVarOptVariance
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Network Port PM
             function."
    ::= { cmPerfGroups 4 }

trafficPMGroup OBJECT-GROUP
    OBJECTS {
        cmFlowStatsIndex, cmFlowStatsIntervalType, cmFlowStatsValid,
        cmFlowStatsAction, cmFlowStatsL2CPFD, cmFlowStatsABRA2N,
        cmFlowStatsABRRLA2N, cmFlowStatsABRRLRA2N, cmFlowStatsABRN2A,
        cmFlowStatsABRRLN2A,
        cmFlowStatsUAS, cmFlowStatsES, cmFlowStatsSES,
        cmFlowStatsFMGA2N, cmFlowStatsFMYA2N, cmFlowStatsFMYDA2N,
        cmFlowStatsFMRDA2N, cmFlowStatsBytesInA2N,
        cmFlowStatsBytesOutA2N, cmFlowStatsFMGN2A,
        cmFlowStatsFMYN2A, cmFlowStatsFMYDN2A,
        cmFlowStatsFMRDN2A, cmFlowStatsBytesInN2A,
        cmFlowStatsBytesOutN2A, cmFlowStatsFTDA2N,
        cmFlowStatsIBRA2NMax, cmFlowStatsIBRRlA2NMax,
        cmFlowStatsIBRA2NMin, cmFlowStatsIBRRlA2NMin,
        cmFlowStatsIBRA2N, cmFlowStatsIBRRlA2N,
        cmFlowStatsIBRN2AMax, cmFlowStatsIBRRlN2AMax,
        cmFlowStatsIBRN2AMin, cmFlowStatsIBRRlN2AMin,
        cmFlowStatsIBRN2A, cmFlowStatsIBRRlN2A,        
        cmFlowStatsFMCDA2N, cmFlowStatsFBCDA2N,

        cmFlowHistoryIndex, cmFlowHistoryTime, cmFlowHistoryValid,
        cmFlowHistoryAction, cmFlowHistoryL2CPFD, cmFlowHistoryABRA2N,
        cmFlowHistoryABRRLA2N, cmFlowHistoryABRRLRA2N, cmFlowHistoryABRN2A,
        cmFlowHistoryABRRLN2A,
        cmFlowHistoryUAS, cmFlowHistoryES, cmFlowHistorySES,
        cmFlowHistoryFMGA2N, cmFlowHistoryFMYA2N, cmFlowHistoryFMYDA2N,
        cmFlowHistoryFMRDA2N, cmFlowHistoryBytesInA2N,
        cmFlowHistoryBytesOutA2N, cmFlowHistoryFMGN2A,
        cmFlowHistoryFMYN2A, cmFlowHistoryFMYDN2A,
        cmFlowHistoryFMRDN2A, cmFlowHistoryBytesInN2A,
        cmFlowHistoryBytesOutN2A, cmFlowHistoryFTDA2N,
        cmFlowHistoryIBRA2NMax, cmFlowHistoryIBRRlA2NMax,
        cmFlowHistoryIBRA2NMin, cmFlowHistoryIBRRlA2NMin,
        cmFlowHistoryIBRA2N, cmFlowHistoryIBRRlA2N,
        cmFlowHistoryIBRN2AMax, cmFlowHistoryIBRRlN2AMax,
        cmFlowHistoryIBRN2AMin, cmFlowHistoryIBRRlN2AMin,
        cmFlowHistoryIBRN2A, cmFlowHistoryIBRRlN2A,
        cmFlowHistoryFMCDA2N, cmFlowHistoryFBCDA2N,
            
        cmFlowThresholdIndex, cmFlowThresholdInterval, cmFlowThresholdVariable,
        cmFlowThresholdValueLo, cmFlowThresholdValueHi, cmFlowThresholdMonValue,
    
        cmQosShaperStatsIndex, cmQosShaperStatsIntervalType,
        cmQosShaperStatsValid, cmQosShaperStatsAction,
        cmQosShaperStatsBT, cmQosShaperStatsBTD, cmQosShaperStatsFD,
        cmQosShaperStatsFTD, cmQosShaperStatsBR, cmQosShaperStatsFR,
        cmQosShaperStatsABRRL, cmQosShaperStatsABRRLR,
        cmQosShaperStatsBREDD, cmQosShaperStatsFREDD,
    
        cmQosShaperHistoryIndex, cmQosShaperHistoryTime,
        cmQosShaperHistoryValid, cmQosShaperHistoryAction,
        cmQosShaperHistoryBT, cmQosShaperHistoryBTD, cmQosShaperHistoryFD,
        cmQosShaperHistoryFTD, cmQosShaperHistoryBR, cmQosShaperHistoryFR,
        cmQosShaperHistoryABRRL, cmQosShaperHistoryABRRLR,
        cmQosShaperHistoryBREDD, cmQosShaperHistoryFREDD,
    
        cmQosShaperThresholdIndex, cmQosShaperThresholdInterval,
        cmQosShaperThresholdVariable, cmQosShaperThresholdValueLo,
        cmQosShaperThresholdValueHi, cmQosShaperThresholdMonValue,

        cmQosFlowPolicerStatsIndex, cmQosFlowPolicerStatsIntervalType,
        cmQosFlowPolicerStatsValid, cmQosFlowPolicerStatsAction,
        cmQosFlowPolicerStatsFMG, cmQosFlowPolicerStatsFMY,
        cmQosFlowPolicerStatsFMYD, cmQosFlowPolicerStatsFMRD,
        cmQosFlowPolicerStatsBytesIn, cmQosFlowPolicerStatsBytesOut,
        cmQosFlowPolicerStatsABR, 
 
        cmQosFlowPolicerHistoryIndex, cmQosFlowPolicerHistoryTime, 
        cmQosFlowPolicerHistoryValid, cmQosFlowPolicerHistoryAction, 
        cmQosFlowPolicerHistoryFMG, cmQosFlowPolicerHistoryFMY, 
        cmQosFlowPolicerHistoryFMYD, cmQosFlowPolicerHistoryFMRD, 
        cmQosFlowPolicerHistoryBytesIn, cmQosFlowPolicerHistoryBytesOut, 
        cmQosFlowPolicerHistoryABR,

        cmQosFlowPolicerThresholdIndex, cmQosFlowPolicerThresholdInterval,
        cmQosFlowPolicerThresholdVariable, cmQosFlowPolicerThresholdValueLo,
        cmQosFlowPolicerThresholdValueHi, cmQosFlowPolicerThresholdMonValue,

        cmAccPortQosShaperStatsIndex, cmAccPortQosShaperStatsIntervalType,
        cmAccPortQosShaperStatsValid, cmAccPortQosShaperStatsAction,
        cmAccPortQosShaperStatsBT, cmAccPortQosShaperStatsBTD,
        cmAccPortQosShaperStatsFD, cmAccPortQosShaperStatsFTD,
        cmAccPortQosShaperStatsBR, cmAccPortQosShaperStatsFR,
        cmAccPortQosShaperStatsABRRL,
        cmAccPortQosShaperStatsBREDD, cmAccPortQosShaperStatsFREDD,

        cmAccPortQosShaperHistoryIndex, cmAccPortQosShaperHistoryTime,
        cmAccPortQosShaperHistoryValid, cmAccPortQosShaperHistoryAction,
        cmAccPortQosShaperHistoryBT, cmAccPortQosShaperHistoryBTD,
        cmAccPortQosShaperHistoryFD, cmAccPortQosShaperHistoryFTD,
        cmAccPortQosShaperHistoryBR, cmAccPortQosShaperHistoryFR,
        cmAccPortQosShaperHistoryABRRL,
        cmAccPortQosShaperHistoryBREDD, cmAccPortQosShaperHistoryFREDD,

        cmAccPortQosShaperThresholdIndex, cmAccPortQosShaperThresholdInterval,
        cmAccPortQosShaperThresholdVariable, cmAccPortQosShaperThresholdValueLo,
        cmAccPortQosShaperThresholdValueHi, cmAccPortQosShaperThresholdMonValue,

        f3NetPortQosShaperStatsIndex, f3NetPortQosShaperStatsIntervalType,
        f3NetPortQosShaperStatsValid, f3NetPortQosShaperStatsAction,
        f3NetPortQosShaperStatsBT, f3NetPortQosShaperStatsBTD,
        f3NetPortQosShaperStatsFD, f3NetPortQosShaperStatsFTD,
        f3NetPortQosShaperStatsBR, f3NetPortQosShaperStatsFR,
        f3NetPortQosShaperStatsABRRL, f3NetPortQosShaperStatsBREDD,
        f3NetPortQosShaperStatsFREDD,
        
        f3NetPortQosShaperHistoryIndex, f3NetPortQosShaperHistoryTime,
        f3NetPortQosShaperHistoryValid, f3NetPortQosShaperHistoryAction,
        f3NetPortQosShaperHistoryBT, f3NetPortQosShaperHistoryBTD,
        f3NetPortQosShaperHistoryFD, f3NetPortQosShaperHistoryFTD,
        f3NetPortQosShaperHistoryBR, f3NetPortQosShaperHistoryFR,
        f3NetPortQosShaperHistoryABRRL, f3NetPortQosShaperHistoryBREDD,
        f3NetPortQosShaperHistoryFREDD,
        
        f3NetPortQosShaperThresholdIndex, f3NetPortQosShaperThresholdInterval,
        f3NetPortQosShaperThresholdVariable, f3NetPortQosShaperThresholdValueLo,
        f3NetPortQosShaperThresholdValueHi, f3NetPortQosShaperThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Traffic PM  
             function."
    ::= { cmPerfGroups 5 }

cmEGXPerfNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        cmEthernetTrafficPortThresholdCrossingAlert,
        cmFlowPointThresholdCrossingAlert,
        cmQosPolicerV2ThresholdCrossingAlert,
        cmQosShaperV2ThresholdCrossingAlert,
        cmLagThresholdCrossingAlert,
        cmOAMFlowPointThresholdCrossingAlert,
        cmTrafficPortQosShaperThresholdCrossingAlert,
        ocnStmThresholdCrossingAlert,
        stsVcPathThresholdCrossingAlert,
        vtVcPathThresholdCrossingAlert,
        e1t1ThresholdCrossingAlert,
        e3t3ThresholdCrossingAlert,
        cmPerQueryGenControl,
        cmPerQueryGenTrap
    }
    STATUS  current
    DESCRIPTION
            "A collection of notifications related to EG-X Threshold Crossing 
             Alerts."
    ::= { cmPerfGroups 6 }

trafficPMGroupCmHub OBJECT-GROUP
    OBJECTS {
        cmFlowStatsIndex, cmFlowStatsIntervalType, cmFlowStatsValid,
        cmFlowStatsAction, cmFlowStatsL2CPFD, cmFlowStatsABRA2N,
        cmFlowStatsABRRLA2N, cmFlowStatsABRRLRA2N, cmFlowStatsABRN2A,
        cmFlowStatsABRRLN2A,
        cmFlowStatsUAS, cmFlowStatsES, cmFlowStatsSES,
        cmFlowStatsFMGA2N, cmFlowStatsFMYA2N, cmFlowStatsFMYDA2N,
        cmFlowStatsFMRDA2N, cmFlowStatsBytesInA2N,
        cmFlowStatsBytesOutA2N, cmFlowStatsFMGN2A,
        cmFlowStatsFMYN2A, cmFlowStatsFMYDN2A,
        cmFlowStatsFMRDN2A, cmFlowStatsBytesInN2A,
        cmFlowStatsBytesOutN2A, cmFlowStatsFTDA2N,
        cmFlowStatsIBRA2NMax, cmFlowStatsIBRRlA2NMax,
        cmFlowStatsIBRA2NMin, cmFlowStatsIBRRlA2NMin,
        cmFlowStatsIBRA2N, cmFlowStatsIBRRlA2N,
        cmFlowStatsIBRN2AMax, cmFlowStatsIBRRlN2AMax,
        cmFlowStatsIBRN2AMin, cmFlowStatsIBRRlN2AMin,
        cmFlowStatsIBRN2A, cmFlowStatsIBRRlN2A,

        cmFlowHistoryIndex, cmFlowHistoryTime, cmFlowHistoryValid,
        cmFlowHistoryAction, cmFlowHistoryL2CPFD, cmFlowHistoryABRA2N,
        cmFlowHistoryABRRLA2N, cmFlowHistoryABRRLRA2N, cmFlowHistoryABRN2A,
        cmFlowHistoryABRRLN2A,
        cmFlowHistoryUAS, cmFlowHistoryES, cmFlowHistorySES,
        cmFlowHistoryFMGA2N, cmFlowHistoryFMYA2N, cmFlowHistoryFMYDA2N,
        cmFlowHistoryFMRDA2N, cmFlowHistoryBytesInA2N,
        cmFlowHistoryBytesOutA2N, cmFlowHistoryFMGN2A,
        cmFlowHistoryFMYN2A, cmFlowHistoryFMYDN2A,
        cmFlowHistoryFMRDN2A, cmFlowHistoryBytesInN2A,
        cmFlowHistoryBytesOutN2A, cmFlowHistoryFTDA2N,
        cmFlowHistoryIBRA2NMax, cmFlowHistoryIBRRlA2NMax,
        cmFlowHistoryIBRA2NMin, cmFlowHistoryIBRRlA2NMin,
        cmFlowHistoryIBRA2N, cmFlowHistoryIBRRlA2N,
        cmFlowHistoryIBRN2AMax, cmFlowHistoryIBRRlN2AMax,
        cmFlowHistoryIBRN2AMin, cmFlowHistoryIBRRlN2AMin,
        cmFlowHistoryIBRN2A, cmFlowHistoryIBRRlN2A,
    
        cmFlowThresholdIndex, cmFlowThresholdInterval, cmFlowThresholdVariable,
        cmFlowThresholdValueLo, cmFlowThresholdValueHi, cmFlowThresholdMonValue,
    
        cmQosShaperStatsIndex, cmQosShaperStatsIntervalType,
        cmQosShaperStatsValid, cmQosShaperStatsAction,
        cmQosShaperStatsBT, cmQosShaperStatsBTD, cmQosShaperStatsFD,
        cmQosShaperStatsFTD, cmQosShaperStatsBR, cmQosShaperStatsFR,
        cmQosShaperStatsABRRL, cmQosShaperStatsABRRLR,
        cmQosShaperStatsBREDD, cmQosShaperStatsFREDD,
    
        cmQosShaperHistoryIndex, cmQosShaperHistoryTime,
        cmQosShaperHistoryValid, cmQosShaperHistoryAction,
        cmQosShaperHistoryBT, cmQosShaperHistoryBTD, cmQosShaperHistoryFD,
        cmQosShaperHistoryFTD, cmQosShaperHistoryBR, cmQosShaperHistoryFR,
        cmQosShaperHistoryABRRL, cmQosShaperHistoryABRRLR,
        cmQosShaperHistoryBREDD, cmQosShaperHistoryFREDD,
    
        cmQosShaperThresholdIndex, cmQosShaperThresholdInterval,
        cmQosShaperThresholdVariable, cmQosShaperThresholdValueLo,
        cmQosShaperThresholdValueHi, cmQosShaperThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Traffic PM  
             function."
    ::= { cmPerfGroups 7 }
    
    cmPerfNotifGroupCmHub NOTIFICATION-GROUP
    NOTIFICATIONS {
        cmEthernetAccPortThresholdCrossingAlert,
        cmEthernetNetPortThresholdCrossingAlert,
        cmFlowThresholdCrossingAlert, 
        cmQosShaperThresholdCrossingAlert
    }
    STATUS  current
    DESCRIPTION
            "A collection of notifications related to FSP150CM, FSP150CC
             Threshold Crossing Alerts."
    ::= { cmPerfGroups 8 }

ocnStmPortPerfGroup OBJECT-GROUP
    OBJECTS {
       ocnStmStatsIndex, ocnStmStatsIntervalType,
       ocnStmStatsValid, ocnStmStatsAction,
       ocnStmStatsLineLBC, ocnStmStatsLineOPT,
       ocnStmStatsLineOPR, ocnStmStatsLineTemp,
       ocnStmStatsLinePSC, ocnStmStatsLineESs,
       ocnStmStatsLineSESs, ocnStmStatsLineCVs,
       ocnStmStatsLineUASs, ocnStmStatsLineFCs,
       ocnStmStatsLineFarEndESs, ocnStmStatsLineFarEndSESs,
       ocnStmStatsLineFarEndCVs, ocnStmStatsLineFarEndUASs,
       ocnStmStatsSectionESs, ocnStmStatsSectionSESs,
       ocnStmStatsSectionCVs, ocnStmStatsSectionSEFs,
       ocnStmStatsSectionUASs,

       ocnStmHistoryIndex, ocnStmHistoryTime,
       ocnStmHistoryValid, ocnStmHistoryAction,
       ocnStmHistoryLineLBC, ocnStmHistoryLineOPT,
       ocnStmHistoryLineOPR, ocnStmHistoryLineTemp,
       ocnStmHistoryLinePSC, ocnStmHistoryLineESs,
       ocnStmHistoryLineSESs, ocnStmHistoryLineCVs,
       ocnStmHistoryLineUASs, ocnStmHistoryLineFCs,
       ocnStmHistoryLineFarEndESs, ocnStmHistoryLineFarEndSESs,
       ocnStmHistoryLineFarEndCVs, ocnStmHistoryLineFarEndUASs,
       ocnStmHistorySectionESs, ocnStmHistorySectionSESs,
       ocnStmHistorySectionCVs, ocnStmHistorySectionSEFs,
       ocnStmHistorySectionUASs,

       ocnStmThresholdIndex, ocnStmThresholdInterval,
       ocnStmThresholdVariable, ocnStmThresholdValueLo,
       ocnStmThresholdValueHi, ocnStmThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
		"A collection of objects used to manage the F3 Ocn-Stm Port PM function."
    ::= { cmPerfGroups 9 }

stsVcPathPerfGroup OBJECT-GROUP
    OBJECTS {
       stsVcPathStatsIndex, stsVcPathStatsIntervalType,
       stsVcPathStatsValid, stsVcPathStatsAction,
       stsVcPathStatsESs, stsVcPathStatsSESs,
       stsVcPathStatsCVs, stsVcPathStatsUASs,
       stsVcPathFarEndStatsESs, stsVcPathFarEndStatsSESs,
       stsVcPathFarEndStatsCVs, stsVcPathFarEndStatsUASs,

       stsVcPathHistoryIndex, stsVcPathHistoryTime,
       stsVcPathHistoryValid, stsVcPathHistoryAction,
       stsVcPathHistoryESs, stsVcPathHistorySESs,
       stsVcPathHistoryCVs, stsVcPathHistoryUASs,
       stsVcPathFarEndHistoryESs, stsVcPathFarEndHistorySESs,
       stsVcPathFarEndHistoryCVs, stsVcPathFarEndHistoryUASs,

       stsVcPathThresholdIndex, stsVcPathThresholdInterval,
       stsVcPathThresholdVariable, stsVcPathThresholdValueLo,
       stsVcPathThresholdValueHi, stsVcPathThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage the F3 High Order Path PM function."
    ::= { cmPerfGroups 10 }

vtVcPathPerfGroup OBJECT-GROUP
    OBJECTS {
       vtVcPathStatsIndex, vtVcPathStatsIntervalType,
       vtVcPathStatsValid, vtVcPathStatsAction,
       vtVcPathStatsESs, vtVcPathStatsSESs,
       vtVcPathStatsCVs, vtVcPathStatsUASs,
       vtVcPathFarEndStatsESs, vtVcPathFarEndStatsSESs,
       vtVcPathFarEndStatsCVs, vtVcPathFarEndStatsUASs,

       vtVcPathHistoryIndex, vtVcPathHistoryTime,
       vtVcPathHistoryValid, vtVcPathHistoryAction,
       vtVcPathHistoryESs, vtVcPathHistorySESs,
       vtVcPathHistoryCVs, vtVcPathHistoryUASs,
       vtVcPathFarEndHistoryESs, vtVcPathFarEndHistorySESs,
       vtVcPathFarEndHistoryCVs, vtVcPathFarEndHistoryUASs,

       vtVcPathThresholdIndex, vtVcPathThresholdInterval,
       vtVcPathThresholdVariable, vtVcPathThresholdValueLo,
       vtVcPathThresholdValueHi, vtVcPathThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage the F3 Low Order Path PM function."
    ::= { cmPerfGroups 11 }

e1T1PerfGroup OBJECT-GROUP
    OBJECTS {
       e1t1StatsIndex, e1t1StatsIntervalType,
       e1t1StatsValid, e1t1StatsAction,
       e1t1StatsLineCVs, e1t1StatsLineESs,
       e1t1StatsLineSESs, e1t1StatsLineESsFarEnd,
       e1t1StatsLineUASs, e1t1StatsLineLOSSs,
       e1t1StatsPathCVs, e1t1StatsPathESs,
       e1t1StatsPathSESs, e1t1StatsPathUASs,
       e1t1StatsPathCVsFarEnd, e1t1StatsPathESsFarEnd,
       e1t1StatsPathSESsFarEnd, e1t1StatsPathSEFsFarEnd,
       e1t1StatsPathUASsFarEnd, e1t1StatsPathFCs,
       e1t1StatsPathFCsFarEnd, e1t1StatsPathAISs,
       e1t1StatsPathSASs,

       e1t1HistoryIndex, e1t1HistoryTime,
       e1t1HistoryValid, e1t1HistoryAction,
       e1t1HistoryLineCVs, e1t1HistoryLineESs,
       e1t1HistoryLineSESs, e1t1HistoryLineESsFarEnd,
       e1t1HistoryLineUASs, e1t1HistoryLineLOSSs,
       e1t1HistoryPathCVs, e1t1HistoryPathESs,
       e1t1HistoryPathSESs, e1t1HistoryPathUASs,
       e1t1HistoryPathCVsFarEnd, e1t1HistoryPathESsFarEnd,
       e1t1HistoryPathSESsFarEnd, e1t1HistoryPathSEFsFarEnd,
       e1t1HistoryPathUASsFarEnd, e1t1HistoryPathFCs,
       e1t1HistoryPathFCsFarEnd, e1t1HistoryPathAISs,
       e1t1HistoryPathSASs,

       e1t1ThresholdIndex, e1t1ThresholdInterval,
       e1t1ThresholdVariable, e1t1ThresholdValueLo,
       e1t1ThresholdValueHi, e1t1ThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage the F3 E1-T1 Ports
         and Facility Ports PM function."
    ::= { cmPerfGroups 12 }

flowPointPmGroup OBJECT-GROUP
    OBJECTS {        
        cmFlowPointStatsIndex,cmFlowPointStatsIntervalType,
        cmFlowPointStatsValid,cmFlowPointStatsAction,
        cmFlowPointStatsL2CPFD,cmFlowPointStatsABRRx,
        cmFlowPointStatsABRRLRx,cmFlowPointStatsUAS,
        cmFlowPointStatsSES,
        cmFlowPointStatsFMG,cmFlowPointStatsFMY,
        cmFlowPointStatsFMRD,
        cmFlowPointStatsFTD,cmFlowPointStatsBytesIn,
        cmFlowPointStatsBytesOut,cmFlowPointStatsFREDD,
        cmFlowPointStatsFACLD,
        cmFlowPointStatsFMYD,
        cmFlowPointStatsFMGD,
        cmFlowPointStatsFD,
        cmFlowPointStatsFMCD,
        cmFlowPointStatsFBCD,
        cmFlowPointStatsBT,
        cmFlowPointStatsFLD,
        
        cmFlowPointHistoryIndex,cmFlowPointHistoryTime,
        cmFlowPointHistoryValid,cmFlowPointHistoryAction,
        cmFlowPointHistoryL2CPFD,cmFlowPointHistoryABRRx,
        cmFlowPointHistoryABRRLRx,cmFlowPointHistoryUAS,
        cmFlowPointHistorySES,
        cmFlowPointHistoryFMG,cmFlowPointHistoryFMY,
        cmFlowPointHistoryFMRD,
        cmFlowPointHistoryFTD,cmFlowPointHistoryBytesIn,
        cmFlowPointHistoryBytesOut,cmFlowPointHistoryFREDD,
        cmFlowPointHistoryFACLD,
        cmFlowPointHistoryFMYD,
        cmFlowPointHistoryFMGD,
        cmFlowPointHistoryFD,
        cmFlowPointHistoryFMCD,
        cmFlowPointHistoryFBCD,
        cmFlowPointHistoryBT,
        cmFlowPointHistoryFLD,
        
        cmFlowPointThresholdIndex,cmFlowPointThresholdInterval,
        cmFlowPointThresholdVariable,cmFlowPointThresholdValueLo,
        cmFlowPointThresholdValueHi,cmFlowPointThresholdMonValue
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage the flow point pm."
    ::= { cmPerfGroups 13 }
 
 cmFlowBWPerfGroup OBJECT-GROUP
    OBJECTS {
       cmFlowBWA2NCIR,
       cmFlowBWA2NEIR,
       cmFlowBWN2ACIR,
       cmFlowBWN2AEIR,
       cmFlowBWA2NGFB,
       cmFlowBWA2NMFB
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage the Flow bandwidth statistics.
         Non-mandatory group."
    ::= { cmPerfGroups 14 }
 
 ocnStmThresholdVarGroup OBJECT-GROUP
    OBJECTS {
    ocnStmThresholdVarOprVariance, ocnStmThresholdVarOptVariance
    }
    STATUS  current
    DESCRIPTION
        "A collection of objects used to manage ocn stm threshold variable."
    ::= { cmPerfGroups 15 }

END
