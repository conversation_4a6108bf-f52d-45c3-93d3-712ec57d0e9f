CM-SA-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-COMPLIANCE, OBJECT-G<PERSON><PERSON>, NOTIFICATION-GROUP
             FROM SNMPv2-CONF
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-<PERSON><PERSON><PERSON>,
    <PERSON>teger32, <PERSON><PERSON><PERSON><PERSON><PERSON>, Un<PERSON><PERSON>, Counter64
             FROM SNMPv2-SMI
    DateAndTime, Di<PERSON>layString, TruthValue, RowStatus, StorageType, 
    Mac<PERSON>ddress, VariablePointer, TEXTUAL-CONVENTION
             FROM SNMPv2-TC
    Ipv6Address 
             FROM IPV6-TC
    fsp150cm
             FROM  ADVA-MIB 
    TrafficDirection, VlanId, VlanPriority, IpVersion, 
    IpPriorityMapMode, ScheduleType, ClassOfServiceType, CmPmBinAction
             FROM  CM-COMMON-MIB
    neIndex, shelfIndex, slotIndex
             FROM  CM-ENTITY-MIB
    BitErrRate, PolicerColorMode
             FROM  CM-FACILITY-MIB
    AdminState, OperationalState, SecondaryState,
    <PERSON><PERSON><PERSON>ounter64, <PERSON>fCounter32, ScheduleType, Sched<PERSON>ctivityStatus, 
    MepDestinationType, ClassOfServiceType
             FROM  CM-COMMON-MIB
    Dot1agCfmMepIdOrZero
             FROM IEEE8021-CFM-MIB;


cmServiceAssuranceMIB MODULE-IDENTITY
    LAST-UPDATED    "201111220000Z"
    ORGANIZATION    "ADVA Optical Networking"
    CONTACT-INFO
            "        Raghav Trivedi
                     ADVA Optical Networking, Inc.
                Tel: ****** 759-1239
             E-mail: <EMAIL>
             Postal: 2301 N. Greenville Ave. #300
                     Richardson, TX USA 75082"
    DESCRIPTION
            "This module defines the Service Assurance MIB definitions used by 
             the F3 (FSP150CM/CC) product lines.
             Copyright (C) ADVA Optical Networking."
    REVISION        "201111220000Z"
    DESCRIPTION
        "
         Notes from release 201607310000Z
         (i)Added the following objects for EsaProbeCOSConfigEntry:
            - esaProbeCOSConfigSoamPmExtAvailFlrThreshold
            - esaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus
            - esaProbeCOSConfigSoamPmExtConDeltaTsForAvail

         Notes from release 201302130000Z
         (i)Added the following objects:
            - esaProbeAlias                     
            - esaReflectorAlias

         Notes from release 201111220000Z
         (i)Added the following objects to support ESA Probes and Reflectors on
            pluggable cards: 
              -esaProbeAdminState, esaProbeOperationalState, esaProbeSecondaryState
              -esaReflectorAdminState, esaReflectorOperationalState, 
                                       esaReflectorSecondaryState

         Notes from release 201105260000Z
         (i)Formalized the compliance groups in the MIBs

         Notes from release 201006220000Z
         (i)Updated range of ecpaControlNumFrames to (0..2147483647),
         (ii)Removed redundant column ecpaTestStreamRxBitRate

         Notes from release 201006100000Z
         (i)Updated range of esaProbeHistoryBins and esaProbeDistHistoryBins to 
              include 0 - since it is possible that some ESA Probes will not have history bins
         (ii)Changed ecpaControlStatus occurrences to ecpaControlTestStatus
         (iii)Changed EcpaTestType TC literal continous to continuous 

         Notes from release 201003250000Z,
        (1)Added new objects for ecpaTestStreamRxBitRate, ecpaConfigStreamUsePortSourceMAC
        (1)ecpaControlDuration now has a max range of 72 hours (259200 seconds) 
           instead of 1 hour (3600 seconds)

         Notes from release 201002110000Z
         This release is applicable to the FSP150CC Release 4.3 device GE201, GE201-SE and
         FSP150CM Release 4.1.
        (1)Added new objects to ecpaConfigStreamTable
               ecpaConfigStreamInnerVlan2Enabled, ecpaConfigStreamInnerVlan2Id, 
               ecpaConfigStreamInnerVlan2Prio, ecpaConfigStreamInnerVlan2EtherType, 
               ecpaConfigStreamDestIpV4Address, ecpaConfigStreamDestIpV6Address
        (2)Added new objects to ecpaTestStreamTable
               ecpaTestStreamInnerVlan2Enabled, ecpaTestStreamInnerVlan2Id, 
               ecpaTestStreamInnerVlan2Prio, ecpaTestStreamInnerVlan2EtherType, 
               ecpaTestStreamDestIpV4Address, ecpaTestStreamDestIpV6Address, ecpaTestStreamChanged
        (3)Added new objects to esaProbeTable
               esaProbeMultiCOSEnabled, esaProbeSLAMonitorType, esaProbeCOSType, 
               esaProbeSLMMulticastMACEnabled, esaProbeSOAMInterval, esaProbeSOAMPktSize
        (4)Added new tables : esaProbeCOSConfigTable, esaProbeMultiDestinationTable
       

         Notes from release 200904150000Z
         This release is applicable to the FSP150CC Release 4.1 
         devices GE101 and GE206.
         Following new tables are added,
           esaProbeTable,  esaProbeScheduleGroupTable,
           esaReflectorTable, esaProbeStatsTable, esaProbeHistoryTable,
           esaProbeDistStatsConfigTable, esaProbeDistStatsTable,
           esaProbeDistStatsBinTable, esaProbeDistHistoryTable,
           esaProbeDistHistoryBinTable, esaProbeStatsThresholdTable
         
         Following notification is added,
           esaProbeThresholdCrossingAlert

         Notes from release 200803030000Z,
         (1)MIB version ready for release FSP150CM 3.1.
         
         Notes from release 201603150000Z
         This release is applicable to the FSP150CC Release 7.1.1 device GE206V, XG210
         (1) Following new extension tables are added,
           f3EsaProbeStatsSoamPmExtTable, f3EsaProbeHistorySoamPmExtTable, f3EsaProbeCOSConfigSoamPmExtTable."
    ::= {fsp150cm 8}    

-- 
-- OID definitions
-- 
cmServAssuranceObjects        OBJECT IDENTIFIER ::= {cmServiceAssuranceMIB 1}
cmServAssuranceNotifications  OBJECT IDENTIFIER ::= {cmServiceAssuranceMIB 2}
cmServAssuranceConformance    OBJECT IDENTIFIER ::= {cmServiceAssuranceMIB 3}

-- 
-- Textual Conventions 
-- 
EcpaTestStatus ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ECPA Test Status."
    SYNTAX       INTEGER {
                   initial (1),
                   in-progress (2),
                   stopped (3),
                   completed (4),
                   aborted (5)
                 }

EcpaPayloadType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ECPA Payload Type."
    SYNTAX       INTEGER {
                   fixed (1),
                   random (2)
                 }

EcpaTestType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ECPA Control Test Type."
    SYNTAX       INTEGER {
                  duration(1),
                  numframes(2),
                  continuous(3)
                 }

EcpaControlAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ECPA Control Action."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  start(1),
                  stop(2)
                 }

EsaProbeProtocol ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Probe Protocol Types."
    SYNTAX       INTEGER {
                  icmpEcho(1),
                  udpEcho(2),
                  icmpTimestamp(3),
                  y1731(4), -- this is the Y.1731 LM/DM protocol
                  y1731-slm-slr(5), -- this is the Y.1731 SLM/SLR protocol
                  y1731-slm-dmm(6)
                 }

EsaProbeDirection ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Probe Direction."
    SYNTAX       INTEGER {
                  up(1),   -- towards the bridge
                  down(2)  -- away from the bridge
                 }

EsaReflectorDirection ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Reflector Direction."
    SYNTAX       INTEGER {
                  up(1),   -- towards the bridge
                  down(2)  -- away from the bridge
                 }
                 
EsaProbePmIntervalType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Probe PM Interval Types."
    SYNTAX       INTEGER {
                  current(1),   -- current pm 
                  rollover(2)   -- rollover
                 }

EsaProbeHistoryIntervalType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Probe Y1731 History Interval Types."
    SYNTAX       INTEGER {
                  interval-1min(1),   -- 1 min
                  interval-5min(2),   -- 5 mins 
                  interval-10min(3),  -- 10 mins
                  interval-15min(4),  -- 15 mins 
                  interval-60min(5)    -- 60 mins
                 }

EsaProbeDistStatsType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Indicates the ESA Probe Statistics Distribution Type."
    SYNTAX       INTEGER {
                   roundtrip-delay(1),
                   oneway-p2r-delay(2),
                   oneway-r2p-delay(3),
                   oneway-p2r-jitter(4),
                   oneway-r2p-jitter(5),
                   oneway-p2r-absjitter(6),
                   oneway-r2p-absjitter(7),
                   roundtrip-absjitter(8),
                   oneway-p2r-fdr(9),
                   oneway-r2p-fdr(10),
                   roundtrip-fdr(11)
                 }

EsaAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Scheduled Group/Reflector Action."
    SYNTAX       INTEGER {
                   not-applicable(0),
                   suspend(1),
                   resume(2),
                   addEsaProbe(3),
                   removeEsaProbe(4)
                 }


EsaProbePktIntervalType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Interval Type used for LMM and DMM messages."
    SYNTAX       INTEGER {
                   not-applicable(0),
                   interval-10ms(1),--applicable for DMM
                   interval-100ms(2),--applicable for DMM
                   interval-1sec(3), -- applicable for LMM and DMM
                   interval-10sec(4), -- applicable for LMM and DMM
                   interval-1min(5) -- applicable for LMM and DMM
                 }

EsaProbeSLAMonitorType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "ESA Probe SLA Monitor Type."
    SYNTAX       INTEGER {
                   not-applicable(0),
                   point-to-point(1),-- point2point
                   point-to-multipoint(2)--point2multipoint
                 }

BerTestStatus ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BER Test Status."
    SYNTAX       INTEGER {
                   not-applicable(0),
                   running (1),
                   not-running (2)
                 }

BerTestMode ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BER Test Control Test Mode."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  generator(1),
                  monitor(2),
                  singleend(3)
                 }

BertControlAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BER Test Control Action."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  start(1),
                  stop(2)
                 }

BertPattern ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BERT Tx Pattern."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  all-0(1),
                  all-1(2),
                  alt-1100(3),
                  bit-1in8(4),
                  bit-3in24(5),
                  bit-2exp20-qrss(6),
                  bit-2exp11-prbs(7),
                  bit-2exp15-prbs(8),
                  bit-2exp23-prbs(9),
                  userdefined(10)
                 }

BertUserPatternLength ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BERT Error Rate."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  length-1byte(1),
                  length-2byte(2),
                  length-3byte(3),
                  length-4byte(4)
                 }

BertSyncState ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "BERT Sync State."
    SYNTAX       INTEGER {
                  not-applicable(0),
                  sync(1),
                  outofsync(2)
                 }

--
--  Etherjack Connection Performance Analyzer Control Table
--
ecpaControlTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EcpaControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries for the control of Etherjack Connection
             Performance Analyzer Streams. Upto 12 ECPA streams can
             be configured per test head; 3 ECPA streams can be 
             simultaneously executed."
    ::= { cmServAssuranceObjects 1 }

ecpaControlEntry  OBJECT-TYPE
    SYNTAX      EcpaControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ecpaControlTable."
    INDEX { neIndex, shelfIndex, slotIndex, ecpaControlIndex }
    ::= { ecpaControlTable 1 }

EcpaControlEntry ::= SEQUENCE {
    ecpaControlIndex                Integer32,
    ecpaControlSourcePort           VariablePointer,
    ecpaControlTestType             EcpaTestType,
    ecpaControlNumFrames            Integer32,
    ecpaControlDuration             Integer32,
    ecpaControlInjectorDirection    TrafficDirection,
    ecpaControlMonitorDirection     TrafficDirection,
    ecpaControlStream1              Integer32,
    ecpaControlStream2              Integer32,
    ecpaControlStream3              Integer32,
    ecpaControlAction               EcpaControlAction,
    ecpaControlTestStatus           EcpaTestStatus,
    ecpaControlStorageType          StorageType,
    ecpaControlRowStatus            RowStatus
}

ecpaControlIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "An arbitrary integer index value used to uniquely identify
            this Etherjack Connection Performance Analyzer Control Test head.
            There is only one test head per card."
    ::= { ecpaControlEntry 1 }

ecpaControlSourcePort OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Pointer to the Access or Network Port on which the ECPA test 
             will be started within a card."
    ::= { ecpaControlEntry 2 }

ecpaControlTestType OBJECT-TYPE
    SYNTAX      EcpaTestType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The type of ECPA test to be performed.  When 'duration' test is
             specified, ECPA test is run for 'ecpaControlDuration' time.
             When 'numFrames' test is specified, ECPA test is run for  
             'ecpaControlNumFrames', when 'continous' test is specified,
             ECPA test is run until an explicit 'ecpaControlAction' of 'stop'
             of received."
    ::= { ecpaControlEntry 3 }

ecpaControlNumFrames OBJECT-TYPE
    SYNTAX     Integer32 (0..2147483647)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the number of frames
          for traffic generation. Traffic Generation will be completed
          after the specified number of frames are transmitted."
     ::= { ecpaControlEntry 4 }

ecpaControlDuration OBJECT-TYPE
    SYNTAX     Integer32 (0..259200)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the time in seconds
          for traffic generation. Traffic Generation will be stopped
          after the specified time interval is exhausted."
     ::= { ecpaControlEntry 5 }

ecpaControlInjectorDirection OBJECT-TYPE
    SYNTAX     TrafficDirection
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the direction of
          traffic generation. This value must be specified if the
          interface is used for traffic generation. Both of
          ecpaControlInjectorDirection and ecpaControlMonitorDirection must be
          specified."
     ::= { ecpaControlEntry 6 }

ecpaControlMonitorDirection OBJECT-TYPE
    SYNTAX     TrafficDirection
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the direction of
          receival of generated traffic. This value must be specified if the
          interface is used for receiving/monitoring generated traffic.
          Both of ecpaControlInjectorDirection and ecpaControlMonitorDirection 
          must be specified."
     ::= { ecpaControlEntry 7 }

ecpaControlStream1 OBJECT-TYPE
    SYNTAX     Integer32 (0..48)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object specifies which stream should be included in the
          ECPA execution. Value of 0 indicates that no streams is selected
          by ecpaControlStream1. Non 0 value indicates the 
          selected ECPA stream."
     ::= { ecpaControlEntry 8 }

ecpaControlStream2 OBJECT-TYPE
    SYNTAX     Integer32 (0..48)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object specifies which stream should be included in the
          ECPA execution. Value of 0 indicates that no streams is selected
          by ecpaControlStream2. Non 0 value indicates the 
          selected ECPA stream."
     ::= { ecpaControlEntry 9 }

ecpaControlStream3  OBJECT-TYPE
    SYNTAX     Integer32 (0..48)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object specifies which stream should be included in the
          ECPA execution. Value of 0 indicates that no streams is selected
          by ecpaControlStream3. Non 0 value indicates the 
          selected ECPA stream."
     ::= { ecpaControlEntry 10 }

ecpaControlAction     OBJECT-TYPE
    SYNTAX      EcpaControlAction 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This object enables starting/stopping of traffic generation
          as well as resetting of monitored streams. This object may
          not be specified at configuration creation time - in this
          case it will take the value of `none'.  This allows manager
          to explicitly control the start and stop of traffic
          generation/monitoring activity.
          Value of `stop' is invalid if the activity is specified but
          not started. Value of `start' is valid during creation time,
          as well as when activity is already stopped.  Specification
          of `reset'  resets the specified streams to default values,
          as well as clears the control specification. Get on this
          variable gives the most recent SNMP set specification."
     ::= { ecpaControlEntry 11 }

ecpaControlTestStatus     OBJECT-TYPE
    SYNTAX     EcpaTestStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "This object reflects the current status of test set configuration
          entry. Upon creation, the status has a value of `initial'; the
          status changes to `in-progress' upon ecpaControlAction
          indicating `start'; the status changes to `completed' upon
          ecpaControlAction indicating stop, as well as when
          the specified test generation completes
          autonomously upon completion of number of frames or
          completion of specified time interval."
     ::= { ecpaControlEntry 12 }

ecpaControlStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ecpaControlEntry 13 }

ecpaControlRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of ecpaControlRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ecpaControlRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ecpaControlRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ecpaControlEntry 14 }

--
--  Etherjack Connection Performance Analyzer Stream Table
--
ecpaConfigStreamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EcpaConfigStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries for the configuration of Etherjack Connection
             Performance Analyzer Stream Specifications."
    ::= { cmServAssuranceObjects 2 }

ecpaConfigStreamEntry OBJECT-TYPE
    SYNTAX      EcpaConfigStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ecpaConfigStreamTable."
    INDEX { ecpaConfigStreamIndex }
    ::= { ecpaConfigStreamTable 1 }

EcpaConfigStreamEntry ::= SEQUENCE {
    ecpaConfigStreamIndex                    Integer32,
    ecpaConfigStreamName                     DisplayString,
    ecpaConfigStreamFrameSize                Integer32,
    ecpaConfigStreamRate                     Unsigned32,
    ecpaConfigStreamPayloadType              EcpaPayloadType,
    ecpaConfigStreamSignature                DisplayString,
    ecpaConfigStreamDestinationMAC           MacAddress,
    ecpaConfigStreamSourceMAC                MacAddress,
    ecpaConfigStreamOuterVlanEnabled         TruthValue,
    ecpaConfigStreamOuterVlanId              VlanId,
    ecpaConfigStreamOuterVlanPrio            VlanPriority,
    ecpaConfigStreamOuterVlanEtherType       Integer32,
    ecpaConfigStreamInnerVlanEnabled         TruthValue,
    ecpaConfigStreamInnerVlanId              VlanId,
    ecpaConfigStreamInnerVlanPrio            VlanPriority,
    ecpaConfigStreamInnerVlanEtherType       Integer32,
    ecpaConfigStreamIpVersion                IpVersion,
    ecpaConfigStreamIpV4Address              IpAddress,
    ecpaConfigStreamIpV6Address              Ipv6Address,
    ecpaConfigStreamPrioMapMode              IpPriorityMapMode,
    ecpaConfigStreamPrioVal                  Integer32,
    ecpaConfigStreamInnerVlan2Enabled        TruthValue,
    ecpaConfigStreamInnerVlan2Id             VlanId,
    ecpaConfigStreamInnerVlan2Prio           VlanPriority,
    ecpaConfigStreamInnerVlan2EtherType      Integer32,
    ecpaConfigStreamDestIpV4Address          IpAddress,
    ecpaConfigStreamDestIpV6Address          Ipv6Address,
    ecpaConfigStreamUsePortSourceMAC         TruthValue,
    ecpaConfigStreamRateHi                   Unsigned32
}

ecpaConfigStreamIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..48)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this Etherjack Connection Performance Analyzer."
    ::= { ecpaConfigStreamEntry 1 }

ecpaConfigStreamName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Symbolic Stream Name that can be associated with an ECPA stream."
    ::= { ecpaConfigStreamEntry 2 }

ecpaConfigStreamFrameSize  OBJECT-TYPE
    SYNTAX     Integer32 (64..9612)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of fixed length frame size
          in bytes for traffic generation. If not specified, default value
          of 64 bytes is used.
          CM Aggregation Product limits the upper range value to 9600."
     ::= { ecpaConfigStreamEntry 3 }

ecpaConfigStreamRate OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The object enables specification of the traffic generation rate."
    ::= { ecpaConfigStreamEntry 4 }

ecpaConfigStreamPayloadType  OBJECT-TYPE
    SYNTAX     EcpaPayloadType 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of type of payload to generate
          `fixed' type indicates a generation pattern of 5A, A5, 5A, A5 bytes.
          `random' type indicates traffic generation with pseudo-random 
           pattern."
     ::= { ecpaConfigStreamEntry 5 }

ecpaConfigStreamSignature  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..10))
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "Indicates an 8-byte signature specified by the user in 
          hexadecimal or ASCII format. This signature is currently 
          used by the FSP150Mx products for test frame recognition and 
          will be appended directly after the ECPA Signature in the test 
          frame and be 4-byte aligned. 
          This will allow the F3 ECPA feature to interoperate with the 
          existing FSP150Mx products.
          Hexadecimal signature must be prefixed with 0x/0X."
     ::= { ecpaConfigStreamEntry 6 }

ecpaConfigStreamDestinationMAC    OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of destination MAC in the generated
             traffic."
    ::= { ecpaConfigStreamEntry 7 }

ecpaConfigStreamSourceMAC    OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of source MAC in the generated
             traffic."
    ::= { ecpaConfigStreamEntry 8 }

ecpaConfigStreamOuterVlanEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of a VLAN tagged ECPA stream. This variable
             enables the outer VLAN specification, in the case of a
             doubly tagged ECPA stream.   If a singly tagged ECPA stream is
             desired, this specification is mandatory." 
    ::= { ecpaConfigStreamEntry 9 }

ecpaConfigStreamOuterVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Outer VLAN Id associated with the ECPA stream.  Valid only
             if ecpaConfigStreamOuterVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 10 }

ecpaConfigStreamOuterVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Outer VLAN Priority associated with the ECPA stream.  Valid only
             if ecpaConfigStreamOuterVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 11 }

ecpaConfigStreamOuterVlanEtherType OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "EtherType associated with the Outer VLAN Tag.  Valid only
             if ecpaConfigStreamOuterVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 12 }

ecpaConfigStreamInnerVlanEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of a VLAN tagged ECPA stream. This variable
             enables the outer VLAN specification, in the case of a
             doubly tagged ECPA stream.   If a singly tagged ECPA stream is
             desired, this specification is mandatory." 
    ::= { ecpaConfigStreamEntry 13 }

ecpaConfigStreamInnerVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner VLAN Id associated with the ECPA stream.  Valid only
             if ecpaConfigStreamInnerVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 14 }

ecpaConfigStreamInnerVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner VLAN Priority associated with the ECPA stream.  Valid only
             if ecpaConfigStreamInnerVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 15 }

ecpaConfigStreamInnerVlanEtherType OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "EtherType associated with the Inner VLAN Tag.  Valid only
             if ecpaConfigStreamInnerVlanEnabled is enabled."
    ::= { ecpaConfigStreamEntry 16 }


ecpaConfigStreamIpVersion  OBJECT-TYPE
    SYNTAX     IpVersion 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of IP data in the generated
          test traffic.  Value of `ipv4' allows test data to be 
          IP v4  compatible.
          Value of `ipv6' allows test data to be IP v6 compatible."
     ::= { ecpaConfigStreamEntry 17 }

ecpaConfigStreamIpV4Address  OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of ipv4 IP Address for 
          the test stream.This is used when ecpaConfigStreamIpVersion is ipv4."
     ::= { ecpaConfigStreamEntry 18 }

ecpaConfigStreamIpV6Address  OBJECT-TYPE
    SYNTAX        Ipv6Address
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of ipv6 IP Address for 
          the test stream.This is used when ecpaConfigStreamIpVersion is ipv6."
     ::= { ecpaConfigStreamEntry 19 }


ecpaConfigStreamPrioMapMode  OBJECT-TYPE
    SYNTAX     IpPriorityMapMode 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Mapping Mode for
          the test stream."
     ::= { ecpaConfigStreamEntry 20 }


ecpaConfigStreamPrioVal  OBJECT-TYPE
    SYNTAX     Integer32 (0..63)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Value for
          ecpaConfigStreamPrioMapMode as TOS/DSCP."
     ::= { ecpaConfigStreamEntry 21 }

ecpaConfigStreamInnerVlan2Enabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of a VLAN tagged ECPA stream. This variable
             enables the second(inner) VLAN specification, in the case of a
             3-tagged ECPA stream.   If a three tagged ECPA stream is
             desired, this specification is mandatory." 
    ::= { ecpaConfigStreamEntry 22 }

ecpaConfigStreamInnerVlan2Id OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner (second) VLAN Id associated with the ECPA stream.  Valid only
             if ecpaConfigStreamInnerVlan2Enabled is enabled."
    ::= { ecpaConfigStreamEntry 23 }

ecpaConfigStreamInnerVlan2Prio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner (second) VLAN Priority associated with the ECPA stream.  Valid only
             if ecpaConfigStreamInnerVlan2Enabled is enabled."
    ::= { ecpaConfigStreamEntry 24 }

ecpaConfigStreamInnerVlan2EtherType OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "EtherType associated with the Inner (second) VLAN Tag.  Valid only
             if ecpaConfigStreamInnerVlan2Enabled is enabled."
    ::= { ecpaConfigStreamEntry 25 }
ecpaConfigStreamDestIpV4Address  OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of destination ipv4 IP Address for 
          the test stream.This is used when ecpaConfigStreamIpVersion is ipv4."
     ::= { ecpaConfigStreamEntry 26 }

ecpaConfigStreamDestIpV6Address  OBJECT-TYPE
    SYNTAX        Ipv6Address
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of destination ipv6 IP Address for 
          the test stream.This is used when ecpaConfigStreamIpVersion is ipv6."
     ::= { ecpaConfigStreamEntry 27 }

ecpaConfigStreamUsePortSourceMAC  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object when not enabled indicates that
          MAC Address specified by ecpaConfigStreamSourceMAC as the source MAC
          Address of the ECPA test stream.
          For EGX, if enabled indicates that the corresponding ECPA test stream
          will use the associated Source Port's MAC Address.
          For GE110 products and CC products, if enabled indicates that the corresponding ECPA test stream
          will use the associated Source Port's Diagnostic MAC Address."
     ::= { ecpaConfigStreamEntry 28 }

ecpaConfigStreamRateHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The object enables specification of the higher 32 bits of the traffic generation rate."
    ::= { ecpaConfigStreamEntry 29 }

--- ECPA Test Streams Table
ecpaTestStreamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EcpaTestStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries containing results of a previous test."
    ::= { cmServAssuranceObjects 3 }

ecpaTestStreamEntry OBJECT-TYPE
    SYNTAX      EcpaTestStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ecpaTestStreamTable. Entries in this
            table show results of the current ECPA test."
    INDEX { neIndex, shelfIndex, slotIndex, ecpaControlIndex, 
            ecpaTestStreamIndex }
    ::= { ecpaTestStreamTable 1 }

EcpaTestStreamEntry ::= SEQUENCE {
    ecpaTestStreamIndex                          Integer32,
    ecpaTestStreamSourcePort                     VariablePointer,
    ecpaTestStreamName                           DisplayString,
    ecpaTestStreamFrameSize                      Integer32,
    ecpaTestStreamRate                           Unsigned32,
    ecpaTestStreamPayloadType                    EcpaPayloadType,
    ecpaTestStreamSignature                      DisplayString,
    ecpaTestStreamDestinationMAC                 MacAddress,
    ecpaTestStreamSourceMAC                      MacAddress,
    ecpaTestStreamOuterVlanEnabled               TruthValue,
    ecpaTestStreamOuterVlanId                    VlanId,
    ecpaTestStreamOuterVlanPrio                  VlanPriority,
    ecpaTestStreamOuterVlanEtherType             Integer32,
    ecpaTestStreamInnerVlanEnabled               TruthValue,
    ecpaTestStreamInnerVlanId                    VlanId,
    ecpaTestStreamInnerVlanPrio                  VlanPriority,
    ecpaTestStreamInnerVlanEtherType             Integer32,
    ecpaTestStreamIpVersion                      IpVersion,
    ecpaTestStreamIpV4Address                    IpAddress,
    ecpaTestStreamIpV6Address                    Ipv6Address,
    ecpaTestStreamPrioMapMode                    IpPriorityMapMode,
    ecpaTestStreamPrioVal                        Integer32,
    ecpaTestStreamMonStartTime                   DateAndTime,
    ecpaTestStreamMonEndTime                     DateAndTime,
    ecpaTestStreamMonElapsedTime                 Integer32,
    ecpaTestStreamMonTxFrames                    PerfCounter64,
    ecpaTestStreamMonRxFrames                    PerfCounter64,
    ecpaTestStreamMonRxPercentSuccess            Integer32,
    ecpaTestStreamMonRxOutOfSeqErrs              PerfCounter64,
    ecpaTestStreamMonRxSeqGaps                   PerfCounter64,
    ecpaTestStreamMonRxNonEcpaFrames             PerfCounter64,
    ecpaTestStreamMonRxMinDelay                  Unsigned32,
    ecpaTestStreamMonRxMaxDelay                  Unsigned32,
    ecpaTestStreamMonRxAvgDelay                  Unsigned32,
    ecpaTestStreamMonRx1stFrameSize              Integer32,
    ecpaTestStreamMonRx1stFrame1Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame2Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame3Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame4Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame5Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame6Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame7Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame8Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame9Octets           DisplayString,
    ecpaTestStreamMonRx1stFrame10Octets          DisplayString,
    ecpaTestStreamMonRxBitRate                   PerfCounter64,
    --added for EG-X product 
    ecpaTestStreamInnerVlan2Enabled              TruthValue,
    ecpaTestStreamInnerVlan2Id                   VlanId,
    ecpaTestStreamInnerVlan2Prio                 VlanPriority,
    ecpaTestStreamInnerVlan2EtherType            Integer32,
    ecpaTestStreamDestIpV4Address                IpAddress,
    ecpaTestStreamDestIpV6Address                Ipv6Address,
    ecpaTestStreamConfigChanged                  TruthValue,
    ecpaTestStreamRateHi                         Unsigned32
}

ecpaTestStreamIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..3) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value corresponding to the stream index 
             for which the ECPA test was initiated."
    ::= { ecpaTestStreamEntry 1 }


ecpaTestStreamSourcePort OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value corresponding to the ifIndex
             for which the ECPA test was initiated."
    ::= { ecpaTestStreamEntry 2 }

ecpaTestStreamName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Symbolic Stream Name that can be associated with an ECPA stream."
    ::= { ecpaTestStreamEntry 3 }

ecpaTestStreamFrameSize  OBJECT-TYPE
    SYNTAX     Integer32 (64..9612)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of fixed length frame size
          in bytes for traffic generation. If not specified, default value
          of 64 bytes is used."
     ::= { ecpaTestStreamEntry 4 }

ecpaTestStreamRate OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The object enables specification of the traffic generation rate."
    ::= { ecpaTestStreamEntry 5 }

ecpaTestStreamPayloadType  OBJECT-TYPE
    SYNTAX     EcpaPayloadType 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of type of payload to generate
          `fixed' type indicates a generation pattern of 5A, A5, 5A, A5 bytes.
          `random' type indicates traffic generation with pseudo-random 
           pattern."
     ::= { ecpaTestStreamEntry 6 }

ecpaTestStreamSignature  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (10))
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "Indicates an 8-byte signature specified by the user in 
          hexadecimal or ASCII format. This signature is currently 
          used by the FSP150Mx products for test frame recognition and 
          will be appended directly after the ECPA Signature in the test 
          frame and be 4-byte aligned. 
          This will allow the F3 ECPA feature to interoperate with the 
          existing FSP150Mx products.
          Hexadecimal signature must be prefixed with 0x/0X."
     ::= { ecpaTestStreamEntry 7 }

ecpaTestStreamDestinationMAC    OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of destination MAC in the generated
             traffic."
    ::= { ecpaTestStreamEntry 8 }

ecpaTestStreamSourceMAC    OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of source MAC in the generated
             traffic."
    ::= { ecpaTestStreamEntry 9 }

ecpaTestStreamOuterVlanEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of a VLAN tagged ECPA stream. This variable
             enables the outer VLAN specification, in the case of a
             doubly tagged ECPA stream.   If a singly tagged ECPA stream is
             desired, this specification is mandatory." 
    ::= { ecpaTestStreamEntry 10 }

ecpaTestStreamOuterVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Outer VLAN Id associated with the ECPA stream.  Valid only
             if ecpaTestStreamOuterVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 11 }

ecpaTestStreamOuterVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Outer VLAN Priority associated with the ECPA stream.  Valid only
             if ecpaTestStreamOuterVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 12 }

ecpaTestStreamOuterVlanEtherType OBJECT-TYPE
    SYNTAX      Integer32(0..65535)
    MAX-ACCESS  read-write 
    STATUS      current
    DESCRIPTION
            "EtherType associated with the Outer VLAN Tag.  Valid only
             if ecpaTestStreamOuterVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 13 }

ecpaTestStreamInnerVlanEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows specification of a VLAN tagged ECPA stream. This variable
             enables the outer VLAN specification, in the case of a
             doubly tagged ECPA stream.   If a singly tagged ECPA stream is
             desired, this specification is mandatory." 
    ::= { ecpaTestStreamEntry 14 }

ecpaTestStreamInnerVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner VLAN Id associated with the ECPA stream.  Valid only
             if ecpaTestStreamInnerVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 15 }

ecpaTestStreamInnerVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Inner VLAN Priority associated with the ECPA stream.  Valid only
             if ecpaTestStreamInnerVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 16 }

ecpaTestStreamInnerVlanEtherType OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "EtherType associated with the Inner VLAN Tag.  Valid only
             if ecpaTestStreamInnerVlanEnabled is enabled."
    ::= { ecpaTestStreamEntry 17 }


ecpaTestStreamIpVersion  OBJECT-TYPE
    SYNTAX     IpVersion 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of IP data in the generated
          test traffic.  Value of `ipv4' allows test data to be 
          IP v4  compatible.
          Value of `ipv6' allows test data to be IP v6 compatible."
     ::= { ecpaTestStreamEntry 18 }

ecpaTestStreamIpV4Address  OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of ipv4 IP Address for 
          the test stream.This is used when ecpaTestStreamIpVersion is ipv4."
     ::= { ecpaTestStreamEntry 19 }

ecpaTestStreamIpV6Address  OBJECT-TYPE
    SYNTAX        Ipv6Address
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
         "This object enables specification of ipv6 IP Address for 
          the test stream.This is used when ecpaTestStreamIpVersion is ipv6."
     ::= { ecpaTestStreamEntry 20 }

ecpaTestStreamPrioMapMode  OBJECT-TYPE
    SYNTAX     IpPriorityMapMode 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Mapping Mode for
          the test stream."
     ::= { ecpaTestStreamEntry 21 }

ecpaTestStreamPrioVal  OBJECT-TYPE
    SYNTAX     Integer32 (0..63)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Value for
          ecpaTestStreamPrioMapMode as TOS/DSCP."
     ::= { ecpaTestStreamEntry 22 }

ecpaTestStreamMonStartTime OBJECT-TYPE
     SYNTAX     DateAndTime
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The start time of the ECPA test for this stream." 
     ::= { ecpaTestStreamEntry 23 }

ecpaTestStreamMonEndTime OBJECT-TYPE
     SYNTAX     DateAndTime
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The end time of the ECPA test for this stream." 
     ::= { ecpaTestStreamEntry 24 }

ecpaTestStreamMonElapsedTime OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The elapsed time of ECPA test for this stream in seconds."
     ::= { ecpaTestStreamEntry 25 }

ecpaTestStreamMonTxFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of frames transmitted(generated) so far.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 26 }

ecpaTestStreamMonRxFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of frames received so far.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 27 }

ecpaTestStreamMonRxPercentSuccess OBJECT-TYPE
     SYNTAX     Integer32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Received frames as a percentage of transmitted frames. 
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 28 }

ecpaTestStreamMonRxOutOfSeqErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of out of sequence errors received so far.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 29 }

ecpaTestStreamMonRxSeqGaps OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of sequence gaps received so far.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 30 }

ecpaTestStreamMonRxNonEcpaFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of non ECPA frames received during the execution."
     ::= { ecpaTestStreamEntry 31 }

ecpaTestStreamMonRxMinDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum delay (in microseconds) across all frames
          received so far. This object does not exist if the
          ecpaControlTestStatus indicates initial state."
     ::= { ecpaTestStreamEntry 32 }

ecpaTestStreamMonRxMaxDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum delay (in microseconds) across all frames
          received so far. This object does not exist if the
          ecpaControlTestStatus indicates initial state."
     ::= { ecpaTestStreamEntry 33 }

ecpaTestStreamMonRxAvgDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average delay (in microseconds) across all frames
          received so far.  This object does not exist if the
          ecpaControlTestStatus indicates initial state."
     ::= { ecpaTestStreamEntry 34 }

ecpaTestStreamMonRx1stFrameSize OBJECT-TYPE
     SYNTAX      Integer32 (64..9612)
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "Frame size of the Monitored First Frame."
     ::= { ecpaTestStreamEntry 35 }

ecpaTestStreamMonRx1stFrame1Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - first 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 36 }

ecpaTestStreamMonRx1stFrame2Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 37 }

ecpaTestStreamMonRx1stFrame3Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 38 }

ecpaTestStreamMonRx1stFrame4Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 39 }

ecpaTestStreamMonRx1stFrame5Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 40 }

ecpaTestStreamMonRx1stFrame6Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 41 }

ecpaTestStreamMonRx1stFrame7Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 42 }

ecpaTestStreamMonRx1stFrame8Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 43 }

ecpaTestStreamMonRx1stFrame9Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 44 }

ecpaTestStreamMonRx1stFrame10Octets OBJECT-TYPE
     SYNTAX      DisplayString (SIZE (1..2048))
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The frame contents of the first received frame - next 1024 bytes.
          This object does not exist if the ecpaControlTestStatus indicates
          initial state."
     ::= { ecpaTestStreamEntry 45 }

ecpaTestStreamMonRxBitRate OBJECT-TYPE
     SYNTAX      PerfCounter64 
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The object provides the average bit rate on the ECPA test stream." 
     ::= { ecpaTestStreamEntry 46 }

ecpaTestStreamInnerVlan2Enabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This variable indicates the second(inner) VLAN specification, in the case of a
             3-tagged ECPA stream.   If a three tagged ECPA stream is
             desired, this indicates true."
    ::= { ecpaTestStreamEntry 47 }

ecpaTestStreamInnerVlan2Id OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This variable provides the Inner (second) VLAN Id associated with the ECPA stream.  
             Valid only if ecpaTestStreamInnerVlan2Enabled is enabled."
    ::= { ecpaTestStreamEntry 48 }

ecpaTestStreamInnerVlan2Prio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This variable provides the Inner (second) VLAN Priority associated with the ECPA stream.  
             Valid only if ecpaTestStreamInnerVlan2Enabled is enabled."
    ::= { ecpaTestStreamEntry 49 }

ecpaTestStreamInnerVlan2EtherType OBJECT-TYPE
    SYNTAX      Integer32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This variable provides the EtherType associated with the Inner (second) VLAN Tag.  
             Valid only if ecpaTestStreamInnerVlan2Enabled is enabled."
    ::= { ecpaTestStreamEntry 50 }
ecpaTestStreamDestIpV4Address  OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
         "This object provides the destination ipv4 IP Address for 
          the test stream. This is valid when ecpaTestStreamIpVersion is ipv4."
     ::= { ecpaTestStreamEntry 51 }

ecpaTestStreamDestIpV6Address  OBJECT-TYPE
    SYNTAX        Ipv6Address
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
         "This object provides the destination ipv6 IP Address for 
          the test stream. This is valid when ecpaTestStreamIpVersion is ipv6."
     ::= { ecpaTestStreamEntry 52 }

ecpaTestStreamConfigChanged  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
         "This object provides information on whether the associated ecpaConfigStream is changed."
     ::= { ecpaTestStreamEntry 53 }

ecpaTestStreamRateHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The object enables specification of the higher 32 bits of the traffic generation rate."
    ::= { ecpaTestStreamEntry 54 }

--
-- ESA Probe Table
--
esaProbeTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to ESA Probes."
    ::= { cmServAssuranceObjects 4 }

esaProbeEntry  OBJECT-TYPE
    SYNTAX      EsaProbeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeTable. Entries can
            be created in this table by management application action.
            The slotIndex should be 255 if the product is cm5.1."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex }
    ::= { esaProbeTable 1 }

EsaProbeEntry ::= SEQUENCE {
    -- identification
    esaProbeIndex                    Integer32,
    esaProbeName                     DisplayString,
    esaProbeSourcePort               VariablePointer,
    esaProbeAssocSchedGroup          VariablePointer,
    esaProbeDirection                EsaProbeDirection,

    -- protocol, end-points
    esaProbeProtocol                 EsaProbeProtocol,
    esaProbeSrcIpAddress             IpAddress,
    esaProbeSrcSubnetMask            IpAddress,
    esaProbeDestIpAddress            IpAddress,
    esaProbeSrcMep                   VariablePointer,
    esaProbeDestMepType              MepDestinationType,
    esaProbeDestMepMacAddr           MacAddress,
    esaProbeDestMepId                Dot1agCfmMepIdOrZero,

    -- service params
    esaProbeVlanTagEnabled           TruthValue,
    esaProbeVlanTagEtherType         Unsigned32,
    esaProbeVlanId                   VlanId,
    esaProbeVlanPrio                 VlanPriority,
    esaProbeInnerVlanTagEnabled      TruthValue,
    esaProbeInnerVlanTagEtherType    Unsigned32,
    esaProbeInnerVlanId              VlanId,
    esaProbeInnerVlanPrio            VlanPriority,
    esaProbeIpPrioMapMode            IpPriorityMapMode,
    esaProbeIpPriority               Integer32,

    --sched params
    esaProbePktsPerSample            Integer32,
    esaProbePktSize                  Integer32,
    esaProbeInterPktGap              Integer32,
    esaProbePktDeadInterval          Integer32,
    esaProbeResponseTimeout          Integer32,

    esaProbeY1731DmmPktSize          Integer32,
    esaProbeY1731LmmInterval         EsaProbePktIntervalType,
    esaProbeY1731DmmInterval         EsaProbePktIntervalType,

    --history config
    esaProbeHistoryBins              Integer32,
    esaProbeHistoryInterval          EsaProbeHistoryIntervalType,
    esaProbeDistHistoryBins          Integer32,
    esaProbeDistHistoryInterval      EsaProbeHistoryIntervalType,

    --general params
    esaProbeCreationTime             DateAndTime,

    -- probe creation params
    esaProbeStorageType              StorageType,
    esaProbeRowStatus                RowStatus,


    -- multi-cos, multi-dest params
    esaProbeMultiCOSEnabled          TruthValue,
    esaProbeSLAMonitorType           EsaProbeSLAMonitorType,
    esaProbeCOSType                  ClassOfServiceType, 
    esaProbeSLMMulticastMACEnabled   TruthValue,
    esaProbeSOAMInterval             EsaProbePktIntervalType,
    esaProbeSOAMPktSize              Integer32,

    --added for cm5.1
    esaProbeInner2VlanTagEnabled      TruthValue,
    esaProbeInner2VlanTagEtherType    Unsigned32,
    esaProbeInner2VlanId              VlanId,
    esaProbeInner2VlanPrio            VlanPriority,

    -- State Management params
    esaProbeAdminState                AdminState,
    esaProbeOperationalState          OperationalState,
    esaProbeSecondaryState            SecondaryState,
    esaProbeMacAddress                MacAddress,
    esaProbeAlias                     DisplayString

}


esaProbeIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "An integer index value used to uniquely identify
            this ESA Probe within a Card."
    ::= { esaProbeEntry 1 }

esaProbeName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..15))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "A unique identifier used by the Manager to distinguish 
             the ESA Probe."
    ::= { esaProbeEntry 2 }

esaProbeSourcePort OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "This object points to the Port to which the ESA Probe
             is attached, i.e.on which the service to be monitored resides."
    ::= { esaProbeEntry 3 }

esaProbeAssocSchedGroup OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object points to the probe's associated Scheduled Group.
             Initially, when the probe is not associated with a Group,
             this object's value is set to {0.0}."
    ::= { esaProbeEntry 4 }

esaProbeDirection OBJECT-TYPE
    SYNTAX      EsaProbeDirection 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "This object allows to specify the direction of Probe
             traffic generation." 
    ::= { esaProbeEntry 5 }

esaProbeProtocol OBJECT-TYPE
    SYNTAX      EsaProbeProtocol
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "Protocol to be used in ESA Probe messages. 
             'icmpTimestamp', 'icmpEcho', 'udpEcho' protocols support 
             layer-3 ethernet service assurance. If the remote reflector
             is an ADVA equipment based on CM family of products,
             one-way loss measurement, one-way delay measurement,
             round-trip delay measurement and one-way jitter measurements
             are computed. Else, only round-trip delay measurements are
             applicable.
             'y1731' protocol can be used for layer-2 ethernet service
             assurance. One-way loss measurement, one-way and round-trip
             delay measurement, and one-way jitter measurements are
             available for this protocol.
             'y1731-slm-slr' protocol can be used for layer-2 ethernet service
             assurance using Synthetic Loss Measurement messages.  One-way
             loss measurement, one-way and round-trip delay measurement,
             and one-way jitter measurements are available for this protocol."
    ::= { esaProbeEntry 6 }

esaProbeSrcIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "Variable that allows specification of the Probe's source
             IP Address.  This IP Address is used in the IP header of
             the ESA Probe generated packets, as source address.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 7 }

esaProbeSrcSubnetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "Variable that allows specification of the Probe's source
             IP Subnet Mask.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 8 }

esaProbeDestIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "Variable that allows specification of the Reflector's
             destination IP Address.  This IP Address is used in the IP header
             of the ESA Probe generated packets, as destination address.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 9 }

esaProbeSrcMep OBJECT-TYPE
    SYNTAX      VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. It identifies
            the source MEP that initiates the Y.1731 LMMs and DMMs."
    ::= { esaProbeEntry 10 }

esaProbeDestMepType OBJECT-TYPE
    SYNTAX      MepDestinationType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. This indicates
            if the destination MEP is specified using the MEP ID object
            esaProbeDestMepId."
    ::= { esaProbeEntry 11 }

esaProbeDestMepMacAddr OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. It identifies
            the destination MEP that responds to the LMMs and DMMs. This
            MAC address will be the destination MAC address for LMM and DMM
            frames if esaProbeY1731DestMepType is 'macaddress'. If CC protocol is
            not enabled between source and destination MEP then this is the
            only choice to identify the remote MEP."
    ::= { esaProbeEntry 12 }

esaProbeDestMepId OBJECT-TYPE
    SYNTAX      Dot1agCfmMepIdOrZero
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. It identifies
            the destination MEP that responds to the LMMs and DMMs. The MAC
            address of this RMEP ID from RMEP database will be the destination
            MAC address for LMM and DMM. This value is used only if the
            esaProbeY1731DestMepType is 'mepid'. CC protocol must be
            enabled between source and destination MEP if the RMEP ID
            is used to identify the Remote MEP."
    ::= { esaProbeEntry 13 }

esaProbeVlanTagEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Ability to specify whether or not to use VLAN tagged or untagged
             ESA traffic generation on the Probe.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 14 }

esaProbeVlanTagEtherType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's VLAN tag Ether Type.  
             Valid if 'esaProbeVlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 15 }

esaProbeVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's VLAN tag.  Valid if 'esaProbeVlanTagEnabled' 
             is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 16 }

esaProbeVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's VLAN priority. Valid if
             'esaProbeVlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 17 }

esaProbeInnerVlanTagEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Ability to specify whether or not to use Inner VLAN tag. 
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 18 }

esaProbeInnerVlanTagEtherType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner VLAN tag Ether Type.  
             Valid if 'esaProbeInnerVlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 19 }

esaProbeInnerVlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner VLAN tag.  Valid if 'esaProbeInnerVlanTagEnabled' 
             is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 20 }

esaProbeInnerVlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner VLAN priority. Valid if
             'esaProbeInnerVlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 21 }

esaProbeIpPrioMapMode  OBJECT-TYPE
    SYNTAX     IpPriorityMapMode 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Mapping Mode for
          the ESA Probe. 
          NOTE: This attribute is not applicable to Y.1731 probes."
     ::= { esaProbeEntry 22 }

esaProbeIpPriority  OBJECT-TYPE
    SYNTAX     Integer32 (0..63)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Value for
          esaProbePrioMapMode as priomap-tos,priomap-dscp.
          NOTE: This attribute is not applicable to Y.1731 probes."
     ::= { esaProbeEntry 23 }

esaProbePktsPerSample OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of number of packets within
          a sample run for ESA traffic generation. A non-zero value
          must be specified.
          NOTE: This attribute is not applicable to Y.1731 probes."
     ::= { esaProbeEntry 24 }

esaProbePktSize OBJECT-TYPE
    SYNTAX     Integer32 (0|64..2000)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of fixed length frame size
          in bytes for ESA traffic generation. If not specified, default value
          of 104 bytes is used. 
          NOTE: This attribute is not available to Y.1731 probes." 
     ::= { esaProbeEntry 25 }

esaProbeInterPktGap OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of interval in milliseconds between
          packet generation. A non-zero value must be specified.
          NOTE: This attribute is not applicable to Y.1731 probes."
     ::= { esaProbeEntry 26 }

esaProbePktDeadInterval OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the dead interval in seconds
          after 'esaProbePktsPerSample' have been generated within the sample.
          This allows for settling time of the ESA traffic.
          A non-zero value must be specified.
          NOTE: This attribute is not applicable to Y.1731 probes."
     ::= { esaProbeEntry 27 }

esaProbeResponseTimeout OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows specification of the timeout value to
          determine lost packets. Time specified in seconds.
          NOTE: This attribute is not applicable to Y.1731 probes.
          
          This object is applicable to Layer 3 and SLM/SLR Probes."
     ::= { esaProbeEntry 28 }

esaProbeY1731DmmPktSize OBJECT-TYPE
    SYNTAX     Integer32 (0|64..9600)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object is only applicable to Y.1731 probes.
          This object enables specification of the TLV size for
          Delay Measurement Frames (DMMs).

          Value of 0 is a read-only value when the Probe protocol is not y1731.
          GE206 Release 4.4.x supports 2000 bytes"
     ::= { esaProbeEntry 29 }

esaProbeY1731LmmInterval OBJECT-TYPE
    SYNTAX      EsaProbePktIntervalType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "LMM frame transmission period. 
            For Loss Measurement Frames, only interval-1sec is supported. 
            This attribute is only applicable to Y.1731 probes."
    DEFVAL { interval-1sec }
    ::= { esaProbeEntry 30 }

esaProbeY1731DmmInterval OBJECT-TYPE
    SYNTAX      EsaProbePktIntervalType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "DMM frame transmission period. 
            This attribute is only applicable to Y.1731 probes."
    DEFVAL { interval-100ms }
    ::= { esaProbeEntry 31 }

esaProbeHistoryBins OBJECT-TYPE
    SYNTAX     Integer32 (0..32)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows specification of number of history bins.
          This is the total history data that will be maintained
          for an ESA Probe run, at any instance."
     ::= { esaProbeEntry 32 }

esaProbeHistoryInterval OBJECT-TYPE
    SYNTAX     EsaProbeHistoryIntervalType 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows specification of the history interval
          period."
     ::= { esaProbeEntry 33 }

esaProbeDistHistoryBins OBJECT-TYPE
    SYNTAX     Integer32 (0..32)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows specification of number of distribution
          history bins. This is the total history distribution data that
          will be maintained for an ESA Probe run, at any instance."
     ::= { esaProbeEntry 34 }

esaProbeDistHistoryInterval OBJECT-TYPE
    SYNTAX     EsaProbeHistoryIntervalType  
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows specification of the history distribution
          interval period."
     ::= { esaProbeEntry 35 }

esaProbeCreationTime OBJECT-TYPE
    SYNTAX     DateAndTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "This object provides the creation time of the ESA Probe."
     ::= { esaProbeEntry 36 }

esaProbeStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { esaProbeEntry 37 }

esaProbeRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of esaProbeRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            esaProbeRowStatus value as createAndGo(4).
            Upon successful row creation, this variable has a
            value of active(1).

            The esaProbeRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { esaProbeEntry 38 }

esaProbeMultiCOSEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "This object is introduced for FSP150CC Release 4.3. 
         This object is only applicable for Y.1731 and 
         SLM-SLR Probe Protocol Types.  This object can be used
         to specify the multiple class of services (COS) for each
         ESA Probe."
    ::= { esaProbeEntry 39 }

esaProbeSLAMonitorType OBJECT-TYPE          
    SYNTAX      EsaProbeSLAMonitorType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
         "This object indicates whether the Probe is a point-to-point type
          or a point-to-multipoint type. Only SLM-SLR probes can be
          point-to-multipoint." 
    ::= { esaProbeEntry 40  }

esaProbeCOSType OBJECT-TYPE
    SYNTAX      ClassOfServiceType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
          "This object can be used to specify the first Class of Service associated with
           the Y.1731/SLM-SLR ESA Probe.  In the case of a multiCOS Probe, this object
           has no semantics when multiple COS levels exist.  This object is only valid for
           Y.1731/SLM-SLR ESA Probes."
    ::= { esaProbeEntry 41 }

esaProbeSLMMulticastMACEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
          "This object can be used to specify whether the SLM/SLR protocol Probe will use
           multicast MAC address in SLM (Synthetic Loss Measurement) messages. 
           This object is only valid for SLM-SLR ESA Probes."
    ::= { esaProbeEntry 42 }

esaProbeSOAMInterval OBJECT-TYPE
    SYNTAX      EsaProbePktIntervalType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This object can be used to specify the packet interval type for Y.1731 LM/DM
            and SLM/SLR based ESA Probes, for the first Class of Service.  
            In the case of a multiCOS Probe, this object has no semantics 
            when multiple COS levels exist. 
            This attribute is applicable for Y.1731 LM/DM and SLM/SLR based probes."
    ::= { esaProbeEntry 43 }

esaProbeSOAMPktSize OBJECT-TYPE
    SYNTAX     Integer32 (0|64..9612)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object can be used to specify the packet size for SLM-SLR based ESA Probes,
          for the first Class of Service.  In the case of multiCOS Probe, this object has
          no semantics when multiple COS levels exist. In such cases this will return
          a value of 0."
     ::= { esaProbeEntry 44 }

esaProbeInner2VlanTagEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Ability to specify whether or not to use Inner2 VLAN tag. 
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 45 }

esaProbeInner2VlanTagEtherType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner2 VLAN tag Ether Type.  
             Valid if 'esaProbeInner2VlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 46 }

esaProbeInner2VlanId OBJECT-TYPE
    SYNTAX      VlanId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner2 VLAN tag.  Valid if 'esaProbeInner2VlanTagEnabled' 
             is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 47 }

esaProbeInner2VlanPrio OBJECT-TYPE
    SYNTAX      VlanPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This variable allows the user to specify the
             monitored service's Inner2 VLAN priority. Valid if
             'esaProbeInner2VlanTagEnabled' is specified as TRUE.
             NOTE: This attribute is not applicable to Y.1731 probes."
    ::= { esaProbeEntry 48 }

-- State Management params
esaProbeAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the ESA Probe.
             This is not externally managed."
    ::= { esaProbeEntry 49 }

esaProbeOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the ESA Probe." 
    ::= { esaProbeEntry 50 }

esaProbeSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the ESA Probe." 
    ::= { esaProbeEntry 51 }
    
esaProbeMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. It identifies
            the destination MacAddress which esa using "
    ::= { esaProbeEntry 52 }
    
esaProbeAlias    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
         "This object allows SNMP management entities to provide an
          alias for an ESA Probe."
     ::= { esaProbeEntry 53 }

--
-- ESA Probe Schedule Group Table
--
esaProbeScheduleGroupTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeScheduleGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that allow groups of ESA Probes
             to be scheduled."
    ::= { cmServAssuranceObjects 5 }

esaProbeScheduleGroupEntry  OBJECT-TYPE
    SYNTAX      EsaProbeScheduleGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in esaProbeScheduleGroupTable. Entries are created
             in this table by management application action."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeScheduleGroupIndex }
    ::= { esaProbeScheduleGroupTable 1 }


EsaProbeScheduleGroupEntry ::= SEQUENCE {
    esaProbeScheduleGroupIndex          Integer32,
    esaProbeScheduleGroupDescr          DisplayString,
    esaProbeScheduleGroupProbeList      DisplayString,
    esaProbeScheduleGroupType           ScheduleType,
    esaProbeScheduleGroupStartTime      DateAndTime,
    esaProbeScheduleGroupDuration       Unsigned32,
    esaProbeScheduleGroupInterval       Unsigned32,
    esaProbeScheduleGroupAction         EsaAction,
    esaProbeScheduleGroupStatus         SchedActivityStatus,
    esaProbeScheduleGroupStorageType    StorageType,
    esaProbeScheduleGroupRowStatus      RowStatus,
    esaProbeScheduleGroupActionProbeList      DisplayString
}

esaProbeScheduleGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "A unique index to distinguish the ESA Probe Schedule Group."
    ::= { esaProbeScheduleGroupEntry 1 }

esaProbeScheduleGroupDescr OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..256))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "Description of the probe schedule group." 
    ::= { esaProbeScheduleGroupEntry 2 }

esaProbeScheduleGroupProbeList OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..512))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "A comma separated list of esaProbeName's, that will have the
             same schedule."
    ::= { esaProbeScheduleGroupEntry 3 }

esaProbeScheduleGroupType OBJECT-TYPE
    SYNTAX     ScheduleType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "This object allows to specify whether the ESA probe group
          schedule is 'one-shot' or 'periodic'. A 'one-shot' schedule executes only
          once and then stops. A 'periodic' schedule reoccurs
          every 'esaProbeScheduleGroupInterval' seconds and continues
          until explicitly stopped."
     ::= { esaProbeScheduleGroupEntry 4 }

esaProbeScheduleGroupStartTime OBJECT-TYPE
    SYNTAX     DateAndTime
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "This object allows specification of the start time
          of the ESA Probe Group. Value of time less than current system
          time will equate to current time. When the system time reaches
          this configured time, the ESA Probe Group will be activated."
     ::= { esaProbeScheduleGroupEntry 5 }

esaProbeScheduleGroupDuration OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "This object allows specification of the duration in
          'seconds' for ESA Probe Group run. Note that a value of
          4294967295 means Duration as FOREVER.
          FOREVER value is only applicable to schedule type oneshot. 
          The value 4294967295 cannot be used if schedule type is periodic."
     ::= { esaProbeScheduleGroupEntry 6 }

esaProbeScheduleGroupInterval OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "This object allows specification of the interval in
          'seconds' between successive runs for ESA Probe Group.  This
          variable is only valid if the 'esaProbeScheduleGroupType' is
          'periodic'."
     ::= { esaProbeScheduleGroupEntry 7 }

esaProbeScheduleGroupAction OBJECT-TYPE
    SYNTAX     EsaAction
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows to suspend/resume the ESA scheduled group."
     ::= { esaProbeScheduleGroupEntry 8 }

esaProbeScheduleGroupStatus OBJECT-TYPE
    SYNTAX     SchedActivityStatus 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "This object indicates the current status of the ESA Probe Group."
     ::= { esaProbeScheduleGroupEntry 9 }

esaProbeScheduleGroupStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { esaProbeScheduleGroupEntry 10 }

esaProbeScheduleGroupRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of esaProbeScheduleGroupRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            esaProbeRowStatus value as createAndGo(4).
            Upon successful row creation, this variable has a
            value of active(1).

            The esaProbeScheduleGroupRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { esaProbeScheduleGroupEntry 11 }

esaProbeScheduleGroupActionProbeList OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..512))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "A comma separated list of esaProbeName's, if set esaProbeScheduleGroupAction as add or remove,
            they will be add or remove from esaProbeScheduleGroupProbeList."
    ::= { esaProbeScheduleGroupEntry 12 }

--
--  ESA Reflector Table
--
esaReflectorTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaReflectorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to ESA Reflectors.
             NOTE: There is no need to create reflectors for Y.1731 probes as
             the MEPs will respond by default."
    ::= { cmServAssuranceObjects 6 }

esaReflectorEntry  OBJECT-TYPE
    SYNTAX      EsaReflectorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaReflectorTable. Entries can
            be created in this table by management application action."
    INDEX { neIndex, shelfIndex, slotIndex, esaReflectorIndex }
    ::= { esaReflectorTable 1 }

EsaReflectorEntry ::= SEQUENCE {
    esaReflectorIndex                Integer32,
    esaReflectorName                 DisplayString,
    esaReflectorIpAddress            IpAddress,
    esaReflectorSubnetMask           IpAddress,
    esaReflectorSourcePort           VariablePointer,
    esaReflectorIpPrioMapMode        IpPriorityMapMode,
    esaReflectorIpPriority           Integer32,
    esaReflectorAction               EsaAction,
    esaReflectorSuspended            TruthValue,
    esaReflectorCreationTime         DateAndTime,
    esaReflectorStorageType          StorageType,
    esaReflectorRowStatus            RowStatus,
    esaReflectorDirection            EsaReflectorDirection,
    esaReflectorAdminState           AdminState,
    esaReflectorOperationalState     OperationalState,
    esaReflectorSecondaryState       SecondaryState,
    esaReflectorMacAddress           MacAddress,
    esaReflectorAlias                DisplayString
}

esaReflectorIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "A unique index to distinguish the ESA Reflector."
    ::= { esaReflectorEntry 1 }

esaReflectorName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..15))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "A unique identifier to distinguish the ESA Reflector."
    ::= { esaReflectorEntry 2 }

esaReflectorIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "This object allows specification of the Reflector's IP Address.
             Reflector listens for packets destined at this IP Address."
    ::= { esaReflectorEntry 3 }

esaReflectorSubnetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "This object allows specification of the
             Reflector's IP Subnet mask."
    ::= { esaReflectorEntry 4 }

esaReflectorSourcePort OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "This object points to the Port to which the ESA Reflector
             is attached, i.e.on which the service to be monitored resides."
    ::= { esaReflectorEntry 5 }

esaReflectorIpPrioMapMode  OBJECT-TYPE
    SYNTAX     IpPriorityMapMode 
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Mapping Mode for
          the ESA Reflector."
     ::= { esaReflectorEntry 6 }

esaReflectorIpPriority  OBJECT-TYPE
    SYNTAX     Integer32 (0..63)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of Priority Value for
          esaReflectorPrioMapMode as priomap-tos,priomap-dscp."
     ::= { esaReflectorEntry 7 }

esaReflectorAction OBJECT-TYPE
    SYNTAX     EsaAction
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object allows to suspend/resume the ESA reflector."
     ::= { esaReflectorEntry 8 }

esaReflectorSuspended OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "This object indicates whether the ESA Reflector is active or suspended."
     ::= { esaReflectorEntry 9 }

esaReflectorCreationTime OBJECT-TYPE
    SYNTAX     DateAndTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "This object provides the creation time of the ESA Reflector."
     ::= { esaReflectorEntry 10 }

esaReflectorStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { esaReflectorEntry 11 }

esaReflectorRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of esaReflectorRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            esaReflectorRowStatus value as createAndGo(4).
            Upon successful row creation, this variable has a
            value of active(1).

            The esaReflectorRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { esaReflectorEntry 12 }

esaReflectorDirection OBJECT-TYPE
    SYNTAX     EsaReflectorDirection
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "This object provides the direction of the ESA Reflector."
    ::= { esaReflectorEntry 13 }

-- State Management params
esaReflectorAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the ESA Reflector.
             This is not externally managed."
    ::= { esaReflectorEntry 14 }

esaReflectorOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the ESA Reflector." 
    ::= { esaReflectorEntry 15 }

esaReflectorSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the ESA Reflector." 
    ::= { esaReflectorEntry 16 }
    
esaReflectorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for Y.1731 probes. It identifies
            the destination MacAddress which esa using "
    ::= { esaReflectorEntry 17 }

esaReflectorAlias    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
         "This object allows SNMP management entities to provide an
          alias for an ESA Reflector."
     ::= { esaReflectorEntry 18 }
    
--
-- ESA Probe Current Statistics Table
--
esaProbeStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of ESA Probe related statistics kept for
             a particular ESA Probe entity. These reflect the
             current data."
    ::= { cmServAssuranceObjects 7 }

esaProbeStatsEntry OBJECT-TYPE
    SYNTAX      EsaProbeStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the esaProbeStatsTable. An entry exists
             in this table for each ESA Probe."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, 
            esaProbeStatsDestinationIndex, esaProbeStatsCOSIndex, esaProbeStatsIndex }
    ::= { esaProbeStatsTable 1 }

EsaProbeStatsEntry ::= SEQUENCE {
    esaProbeStatsDestinationIndex             Integer32,
    esaProbeStatsCOSIndex                     Integer32,
    esaProbeStatsIndex                        Integer32,
    esaProbeStatsIntervalType                 EsaProbePmIntervalType,
    esaProbeStatsCOS                          ClassOfServiceType,
    esaProbeStatsValid                        TruthValue,
    esaProbeStatsAction                       CmPmBinAction,
    esaProbeStatsP2RPkts                      PerfCounter64,
    esaProbeStatsP2RErredPkts                 PerfCounter64,
    esaProbeStatsP2RSyncErrs                  PerfCounter64,
    esaProbeStatsP2RLostPkts                  PerfCounter64,
    esaProbeStatsR2PPkts                      PerfCounter64,
    esaProbeStatsR2PErredPkts                 PerfCounter64,
    esaProbeStatsR2PSyncErrs                  PerfCounter64,
    esaProbeStatsR2PLostPkts                  PerfCounter64,
    esaProbeStatsLostPkts                     PerfCounter64,
    esaProbeStatsSeqGaps                      PerfCounter64,
    esaProbeStatsOutOfSeqErrs                 PerfCounter64,
    esaProbeStatsMinRoundTripDelay            Unsigned32,
    esaProbeStatsMaxRoundTripDelay            Unsigned32,
    esaProbeStatsAvgRoundTripDelay            Unsigned32,
    esaProbeStatsSumRoundTripDelay            PerfCounter64,
    esaProbeStatsSumOfSqRoundTripDelay        PerfCounter64,
    esaProbeStatsMinOnewayP2RDelay            Unsigned32,
    esaProbeStatsMaxOnewayP2RDelay            Unsigned32,
    esaProbeStatsAvgOnewayP2RDelay            Unsigned32,
    esaProbeStatsSumOnewayP2RDelay            PerfCounter64,
    esaProbeStatsSumOfSqOnewayP2RDelay        PerfCounter64,
    esaProbeStatsMinOnewayR2PDelay            Unsigned32,
    esaProbeStatsMaxOnewayR2PDelay            Unsigned32,
    esaProbeStatsAvgOnewayR2PDelay            Unsigned32,
    esaProbeStatsSumOnewayR2PDelay            PerfCounter64,
    esaProbeStatsSumOfSqOnewayR2PDelay        PerfCounter64,
    esaProbeStatsMinPosP2RJitter              Unsigned32,
    esaProbeStatsMaxPosP2RJitter              Unsigned32,
    esaProbeStatsNumPosP2RJitter              PerfCounter64,
    esaProbeStatsSumPosP2RJitter              PerfCounter64,
    esaProbeStatsSumOfSqPosP2RJitter          PerfCounter64,
    esaProbeStatsMinNegP2RJitter              Unsigned32,
    esaProbeStatsMaxNegP2RJitter              Unsigned32,
    esaProbeStatsNumNegP2RJitter              PerfCounter64,
    esaProbeStatsSumNegP2RJitter              PerfCounter64,
    esaProbeStatsSumOfSqNegP2RJitter          PerfCounter64,
    esaProbeStatsMinPosR2PJitter              Unsigned32,
    esaProbeStatsMaxPosR2PJitter              Unsigned32,
    esaProbeStatsNumPosR2PJitter              PerfCounter64,
    esaProbeStatsSumPosR2PJitter              PerfCounter64,
    esaProbeStatsSumOfSqPosR2PJitter          PerfCounter64,
    esaProbeStatsMinNegR2PJitter              Unsigned32,
    esaProbeStatsMaxNegR2PJitter              Unsigned32,
    esaProbeStatsNumNegR2PJitter              PerfCounter64,
    esaProbeStatsSumNegR2PJitter              PerfCounter64,
    esaProbeStatsSumOfSqNegR2PJitter          PerfCounter64,
    esaProbeStatsY1731P2RNegLossOccurrences   PerfCounter64,
    esaProbeStatsY1731R2PNegLossOccurrences   PerfCounter64,
    esaProbeStatsY1731RxLmSamples             TruthValue,
    esaProbeStatsY1731RxDmSamples             TruthValue,
    esaProbeStatsY1731P2RFrames               PerfCounter64,
    esaProbeStatsY1731R2PFrames               PerfCounter64,
    -- following introduced in R4.4CC
    esaProbeStatsAvgAbsP2RJitter              Unsigned32,
    esaProbeStatsAvgAbsR2PJitter              Unsigned32,
    esaProbeStatsMaxAbsP2RJitter              Unsigned32,
    esaProbeStatsMaxAbsR2PJitter              Unsigned32,
    esaProbeStatsMinAbsP2RJitter              Unsigned32,
    esaProbeStatsMinAbsR2PJitter              Unsigned32,
    esaProbeStatsNumAbsP2RJitter              PerfCounter64,
    esaProbeStatsNumAbsR2PJitter              PerfCounter64,
    esaProbeStatsSumAbsP2RJitter              PerfCounter64,
    esaProbeStatsSumAbsR2PJitter              PerfCounter64,
    esaProbeStatsSumOfSqAbsP2RJitter          PerfCounter64,
    esaProbeStatsSumOfSqAbsR2PJitter          PerfCounter64
}

esaProbeStatsDestinationIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             a destination (Reflector) Index, in a point-to-multi point SLA. 
             For Release 4.1 GE206, this value is 1, since only point-to-point SLA
             is supported."
    ::= { esaProbeStatsEntry 1 }

esaProbeStatsCOSIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             SLA for a specific Class of Service. 
             For Release 4.1 GE206, this value is 1, since the COS
             is explicitly specified in the Probe parameters." 
    ::= { esaProbeStatsEntry 2 }

esaProbeStatsIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..2)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this ESA Probe statistics entry. 
            Index 1 corresponds to current and index 2 to rollover."
    ::= { esaProbeStatsEntry 3 }

esaProbeStatsIntervalType OBJECT-TYPE
    SYNTAX      EsaProbePmIntervalType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the type of collection, i.e. whether it is
             current, or rollover."
    ::= { esaProbeStatsEntry 4 }

esaProbeStatsCOS   OBJECT-TYPE
    SYNTAX      ClassOfServiceType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The Class of Service.  
             For Release 4.1 GE206, this value is cos-not-applicable, since 
             multi COS SLA is not supported." 
    ::= { esaProbeStatsEntry 5 }

esaProbeStatsValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { esaProbeStatsEntry 6 }

esaProbeStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { esaProbeStatsEntry 7 }

esaProbeStatsP2RPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Transmitted Packets in the current interval.
          In the case of Y.1731 probes, this reflects the number of DMRs
          (Delay Measurement Replies) received and this will always be the
          same as the esaProbeStatsR2PPkts."
     ::= { esaProbeStatsEntry 8 }

esaProbeStatsP2RErredPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Packets in the current interval, for which
          there was a problem in transmission.  This error is counted
          when there is a failure in packet transmission due to resource
          limitations (buffers).
          This can happen if the Probe Configuration was changed to
          send in a large number of packets in a time interval.

          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeStatsEntry 9 }

esaProbeStatsP2RSyncErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of (time) Synchronization Errors from probe to reflector
          in the current interval."
     ::= { esaProbeStatsEntry 10 }

esaProbeStatsP2RLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Lost Pkts from probe to reflector in the current interval.
          For Y.1731 probes, these are the actual data frames lost."
     ::= { esaProbeStatsEntry 11 }

esaProbeStatsR2PPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Received Packets in the current interval.
          In the case of Y.1731 probes, this reflects the number of DMRs
          (Delay Measurement Replies) received and this will always be the
          same as the esaProbeStatsP2RPkts."
     ::= { esaProbeStatsEntry 12 }

esaProbeStatsR2PErredPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Received Packets in the current interval,
          which had errors such as length of received packet is less than
          the protocol header length, packet was received after it was
          marked as lost (i.e.beyond timeout).
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeStatsEntry 13 }

esaProbeStatsR2PSyncErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of (time) Synchronization Errors from reflector to probe
          in the current interval."
     ::= { esaProbeStatsEntry 14 }

esaProbeStatsR2PLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Lost Pkts from reflector to probe in the current interval.
          For Y.1731 probes, these are the actual data frames lost."
     ::= { esaProbeStatsEntry 15 }

esaProbeStatsLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Lost Packets in the current interval.
          Packets which are not received from the reflector end beyond
          the configured timeout value are termed as lost.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeStatsEntry 16 }

esaProbeStatsSeqGaps OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of sequence gaps across all
          packets received in the current interval. If (Received Seq Number)
          is greater than or equal to (Last Received Seq Number + 2))
          it is termed as a sequence gap.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeStatsEntry 17 }

esaProbeStatsOutOfSeqErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of out of sequence errors across all
          packets received in the current interval. If (Received Seq Number)
          is less than or equal to (Last Received Seq Number),
          it is termed as out of sequence error.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeStatsEntry 18 }

esaProbeStatsMinRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeStatsEntry 19 }

esaProbeStatsMaxRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeStatsEntry 20 }

esaProbeStatsAvgRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeStatsEntry 21 }

esaProbeStatsSumRoundTripDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  round trip delay(in microseconds)
          in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeStatsEntry 22 }

esaProbeStatsSumOfSqRoundTripDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  round trip delays in the current interval."
     ::= { esaProbeStatsEntry 23 }

esaProbeStatsMinOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 24 }

esaProbeStatsMaxOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 25 }

esaProbeStatsAvgOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 26 }

esaProbeStatsSumOnewayP2RDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of one-way delay from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 27 }

esaProbeStatsSumOfSqOnewayP2RDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of one-way delay from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 28 }

esaProbeStatsMinOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 29 }

esaProbeStatsMaxOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 30 }

esaProbeStatsAvgOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeStatsEntry 31 }

esaProbeStatsSumOnewayR2PDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of one-way delay from destination to source
          in the current interval."
     ::= { esaProbeStatsEntry 32 }

esaProbeStatsSumOfSqOnewayR2PDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of one-way delay from destination to source
          in the current interval."
     ::= { esaProbeStatsEntry 33 }

esaProbeStatsMinPosP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum positive one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 34 }

esaProbeStatsMaxPosP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum positive one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 35 }

esaProbeStatsNumPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 36 }

esaProbeStatsSumPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 37 }

esaProbeStatsSumOfSqPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 38 }

esaProbeStatsMinNegP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum negative one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 39 }

esaProbeStatsMaxNegP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum negative one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 40 }

esaProbeStatsNumNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 41 }

esaProbeStatsSumNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 42 }

esaProbeStatsSumOfSqNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeStatsEntry 43 }

esaProbeStatsMinPosR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum positive one-way jitter from destination to source
          in the current interval."
     ::= { esaProbeStatsEntry 44 }

esaProbeStatsMaxPosR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum positive one-way jitter from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 45 }

esaProbeStatsNumPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 46 }

esaProbeStatsSumPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 47 }

esaProbeStatsSumOfSqPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 48 }

esaProbeStatsMinNegR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum negative one-way jitter from destination to source
          in the current interval."
     ::= { esaProbeStatsEntry 49 }

esaProbeStatsMaxNegR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum negative one-way jitter from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 50 }

esaProbeStatsNumNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 51 }

esaProbeStatsSumNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 52 }

esaProbeStatsSumOfSqNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeStatsEntry 53 }

esaProbeStatsY1731P2RNegLossOccurrences OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This is the
          number of occurences of negative frame loss from Source MEP (Probe)
          to Destination MEP (Reflector). If these counts are non-zero then
          there could be some kind of provisioning mismatch between the Probe
          MEP and Reflector MEP. Here are some scenarios this can happen:
          - Probe MEP is configured to count in-profile frames only and the
            Reflector MEP is configured to count all frames with a mismatch
            in value for the attribute cfmMepLmCountInProfileFrames.
          - Probe MEP is configured to count data frames for specific VLAN
            priority and the Reflector MEP is configured to count data frames
            for all the priorities with a mismatch in values for the attributes
            cfmMepLmTxCountAllPrios or cfmMepLmRxCountAllPrios.
          NOTE: This could possibly happen due to reasons not related to
                configuration such as frame reordering in the network."
     ::= { esaProbeStatsEntry 54 }

esaProbeStatsY1731R2PNegLossOccurrences OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This is the
          number of occurences of negative frame loss from to Destination MEP
          (Reflector) to Source MEP (Probe). If these counts are non-zero then
          there could be some kind of provisioning mismatch between the Probe
          MEP and Reflector MEP. Here are some scenarios this can happen:
          - Probe MEP is configured to count in-profile frames only and the
            Reflector MEP is configured to count all frames with a mismatch
            in value for the attribute cfmMepLmCountInProfileFrames.
          - Probe MEP is configured to count data frames for specific VLAN
            priority and the Reflector MEP is configured to count data frames
            for all the priorities with a mismatch in values for the attributes
            cfmMepLmTxCountAllPrios or cfmMepLmRxCountAllPrios.
          NOTE: This could possibly happen due to reasons not related to
                configuration such as frame reordering in the network."
     ::= { esaProbeStatsEntry 55 }

esaProbeStatsY1731RxLmSamples OBJECT-TYPE
     SYNTAX     TruthValue
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This flag
          indicates that at least one loss measurement sample was received
          for this bin period (LMRs received). This is used by the user
          to differentiate between valid zero frame loss and not receiving
          any LMRs."
     ::= { esaProbeStatsEntry 56 }

esaProbeStatsY1731RxDmSamples OBJECT-TYPE
     SYNTAX     TruthValue
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This flag
          indicates that at least one delay measurement sample was received
          for this bin period (DMRs received)."
     ::= { esaProbeStatsEntry 57 }

esaProbeStatsY1731P2RFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Y.1731 probes. This is the
           number of data frames counted by the Source MEP (Probe) that
           are transmitted towards the Destination MEP (Reflector). Based
           on the configuration the MEP counts the data frames with:
           - MEP's LM TX priority or all the priorities
           - All the VLAN IDs belonging the MEP's MA VID list."
     ::= { esaProbeStatsEntry 58 }

esaProbeStatsY1731R2PFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Y.1731 probes. This is the
           number of data frames counted by the Destination MEP (Reflector)
           that are transmitted towards the Source MEP (Probe). Based
           on the configuration the MEP counts the data frames with:
           - MEP's LM RX priority or all the priorities
           - All the VLAN IDs belonging the MEP's MA VID list."
     ::= { esaProbeStatsEntry 59 }

esaProbeStatsAvgAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the average absolute P2R Jitter value.
           ."
     ::= { esaProbeStatsEntry 60 }

esaProbeStatsAvgAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the average absolute R2P Jitter value.
           ."
     ::= { esaProbeStatsEntry 61 }

esaProbeStatsMinAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the minimum absolute P2R Jitter value.
           ."
     ::= { esaProbeStatsEntry 62 }

esaProbeStatsMinAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the minimum absolute R2P Jitter value.
           ."
     ::= { esaProbeStatsEntry 63 }

esaProbeStatsMaxAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the maximum absolute P2R Jitter value.
           ."
     ::= { esaProbeStatsEntry 64 }

esaProbeStatsMaxAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the maximum absolute R2P Jitter value.
           ."
     ::= { esaProbeStatsEntry 65 }

esaProbeStatsNumAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the number of absolute P2R Jitters."
     ::= { esaProbeStatsEntry 66 }

esaProbeStatsNumAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the number of absolute R2P Jitters."
     ::= { esaProbeStatsEntry 67 }

esaProbeStatsSumAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of absolute P2R Jitter values."
     ::= { esaProbeStatsEntry 68 }

esaProbeStatsSumAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of absolute R2P Jitter values."
     ::= { esaProbeStatsEntry 69 }

esaProbeStatsSumOfSqAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of squares of absolute P2R Jitter values."
     ::= { esaProbeStatsEntry 70 }

esaProbeStatsSumOfSqAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of squares of absolute R2P Jitter values."
     ::= { esaProbeStatsEntry 71 }

--
-- ESA Probe Current Statistics Table
--
esaProbeHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of ESA Probe related history kept for
             a particular ESA Probe entity. These reflect the
             current data."
    ::= { cmServAssuranceObjects 8 }

esaProbeHistoryEntry OBJECT-TYPE
    SYNTAX      EsaProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the esaProbeHistoryTable. An entry exists
             in this table for each ESA Probe."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, 
            esaProbeHistoryDestinationIndex, esaProbeHistoryCOSIndex, 
            esaProbeHistoryIndex }
    ::= { esaProbeHistoryTable 1 }

EsaProbeHistoryEntry ::= SEQUENCE {
    esaProbeHistoryDestinationIndex          Integer32,
    esaProbeHistoryCOSIndex                  Integer32,
    esaProbeHistoryIndex                     Integer32,
    esaProbeHistoryTime                      DateAndTime,
    esaProbeHistoryValid                     TruthValue,
    esaProbeHistoryAction                    CmPmBinAction,
    esaProbeHistoryCOS                       ClassOfServiceType,
    esaProbeHistoryP2RPkts                   PerfCounter64,
    esaProbeHistoryP2RErredPkts              PerfCounter64,
    esaProbeHistoryP2RSyncErrs               PerfCounter64,
    esaProbeHistoryP2RLostPkts               PerfCounter64,
    esaProbeHistoryR2PPkts                   PerfCounter64,
    esaProbeHistoryR2PErredPkts              PerfCounter64,
    esaProbeHistoryR2PSyncErrs               PerfCounter64,
    esaProbeHistoryR2PLostPkts               PerfCounter64,
    esaProbeHistoryLostPkts                  PerfCounter64,
    esaProbeHistorySeqGaps                   PerfCounter64,
    esaProbeHistoryOutOfSeqErrs              PerfCounter64,
    esaProbeHistoryMinRoundTripDelay         Unsigned32,
    esaProbeHistoryMaxRoundTripDelay         Unsigned32,
    esaProbeHistoryAvgRoundTripDelay         Unsigned32,
    esaProbeHistorySumRoundTripDelay         PerfCounter64,
    esaProbeHistorySumOfSqRoundTripDelay     PerfCounter64,
    esaProbeHistoryMinOnewayP2RDelay         Unsigned32,
    esaProbeHistoryMaxOnewayP2RDelay         Unsigned32,
    esaProbeHistoryAvgOnewayP2RDelay         Unsigned32,
    esaProbeHistorySumOnewayP2RDelay         PerfCounter64,
    esaProbeHistorySumOfSqOnewayP2RDelay     PerfCounter64,
    esaProbeHistoryMinOnewayR2PDelay         Unsigned32,
    esaProbeHistoryMaxOnewayR2PDelay         Unsigned32,
    esaProbeHistoryAvgOnewayR2PDelay         Unsigned32,
    esaProbeHistorySumOnewayR2PDelay         PerfCounter64,
    esaProbeHistorySumOfSqOnewayR2PDelay     PerfCounter64,
    esaProbeHistoryMinPosP2RJitter           Unsigned32,
    esaProbeHistoryMaxPosP2RJitter           Unsigned32,
    esaProbeHistoryNumPosP2RJitter           PerfCounter64,
    esaProbeHistorySumPosP2RJitter           PerfCounter64,
    esaProbeHistorySumOfSqPosP2RJitter       PerfCounter64,
    esaProbeHistoryMinNegP2RJitter           Unsigned32,
    esaProbeHistoryMaxNegP2RJitter           Unsigned32,
    esaProbeHistoryNumNegP2RJitter           PerfCounter64,
    esaProbeHistorySumNegP2RJitter           PerfCounter64,
    esaProbeHistorySumOfSqNegP2RJitter       PerfCounter64,
    esaProbeHistoryMinPosR2PJitter           Unsigned32,
    esaProbeHistoryMaxPosR2PJitter           Unsigned32,
    esaProbeHistoryNumPosR2PJitter           PerfCounter64,
    esaProbeHistorySumPosR2PJitter           PerfCounter64,
    esaProbeHistorySumOfSqPosR2PJitter       PerfCounter64,
    esaProbeHistoryMinNegR2PJitter           Unsigned32,
    esaProbeHistoryMaxNegR2PJitter           Unsigned32,
    esaProbeHistoryNumNegR2PJitter           PerfCounter64,
    esaProbeHistorySumNegR2PJitter           PerfCounter64,
    esaProbeHistorySumOfSqNegR2PJitter       PerfCounter64,
    esaProbeHistoryY1731P2RNegLossOccurrences PerfCounter64,
    esaProbeHistoryY1731R2PNegLossOccurrences PerfCounter64,
    esaProbeHistoryY1731RxLmSamples          TruthValue,
    esaProbeHistoryY1731RxDmSamples          TruthValue,
    esaProbeHistoryY1731P2RFrames            PerfCounter64,
    esaProbeHistoryY1731R2PFrames            PerfCounter64,
    esaProbeHistoryAvgAbsP2RJitter           Unsigned32,
    esaProbeHistoryAvgAbsR2PJitter           Unsigned32,
    esaProbeHistoryMaxAbsP2RJitter           Unsigned32,
    esaProbeHistoryMaxAbsR2PJitter           Unsigned32,
    esaProbeHistoryMinAbsP2RJitter           Unsigned32,
    esaProbeHistoryMinAbsR2PJitter           Unsigned32,
    esaProbeHistoryNumAbsP2RJitter           PerfCounter64,
    esaProbeHistoryNumAbsR2PJitter           PerfCounter64,
    esaProbeHistorySumAbsP2RJitter           PerfCounter64,
    esaProbeHistorySumAbsR2PJitter           PerfCounter64,
    esaProbeHistorySumOfSqAbsP2RJitter       PerfCounter64,
    esaProbeHistorySumOfSqAbsR2PJitter       PerfCounter64
}

esaProbeHistoryDestinationIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             a destination (Reflector) Index, in a point-to-multi point SLA. 
             For Release 4.1 GE206, this value is 1, since only point-to-point SLA
             is supported."
    ::= { esaProbeHistoryEntry 1 }

esaProbeHistoryCOSIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             SLA for a specific Class of Service. 
             For Release 4.1 GE206, this value is 1, since the COS
             is explicitly specified in the Probe parameters." 
    ::= { esaProbeHistoryEntry 2 }

esaProbeHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this ESA Probe statistics history entry."
    ::= { esaProbeHistoryEntry 3 }

esaProbeHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Indicates the time of history bin creation."
    ::= { esaProbeHistoryEntry 4 }

esaProbeHistoryValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates the validity of the bin."
    ::= { esaProbeHistoryEntry 5 }

esaProbeHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { esaProbeHistoryEntry 6 }

esaProbeHistoryCOS   OBJECT-TYPE
    SYNTAX      ClassOfServiceType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The Class of Service.  
             For Release 4.1 GE206, this value is cos-not-applicable, since 
             multi COS SLA is not supported." 
    ::= { esaProbeHistoryEntry 7 }

esaProbeHistoryP2RPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Transmitted Packets in the current interval.
          In the case of Y.1731 probes, this reflects the number of DMRs
          (Delay Measurement Replies) received and this will always be the
          same as the esaProbeHistoryR2PPkts."
     ::= { esaProbeHistoryEntry 8 }

esaProbeHistoryP2RErredPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Packets in the current interval, for which
          there was a problem in transmission.  This error is counted
          when there is a failure in packet transmission due to resource
          limitations (buffers).
          This can happen if the Probe Configuration was changed to
          send in a large number of packets in a time interval.

          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeHistoryEntry 9 }


esaProbeHistoryP2RSyncErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of (time) Synchronization Errors from probe to reflector
          in the current interval."
     ::= { esaProbeHistoryEntry 10 }


esaProbeHistoryP2RLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Lost Pkts from probe to reflector in the current interval.
          For Y.1731 probes, these are the actual data frames lost."
     ::= { esaProbeHistoryEntry 11 }


esaProbeHistoryR2PPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Received Packets in the current interval.
          In the case of Y.1731 probes, this reflects the number of DMRs
          (Delay Measurement Replies) received and this will always be the
          same as the esaProbeHistoryP2RPkts."
     ::= { esaProbeHistoryEntry 12 }

esaProbeHistoryR2PErredPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Received Packets in the current interval,
          which had errors such as length of received packet is less than
          the protocol header length, packet was received after it was
          marked as lost (i.e.beyond timeout).
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeHistoryEntry 13 }


esaProbeHistoryR2PSyncErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of (time) Synchronization Errors from reflector to probe
          in the current interval."
     ::= { esaProbeHistoryEntry 14 }

esaProbeHistoryR2PLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The number of Lost Pkts from reflector to probe in the current interval.
          For Y.1731 probes, these are the actual data frames lost."
     ::= { esaProbeHistoryEntry 15 }

esaProbeHistoryLostPkts OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of Lost Packets in the current interval.
          Packets which are not received from the reflector end beyond
          the configured timeout value are termed as lost.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeHistoryEntry 16 }

esaProbeHistorySeqGaps OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of sequence gaps across all
          packets received in the current interval. If (Received Seq Number)
          is greater than or equal to (Last Received Seq Number + 2))
          it is termed as a sequence gap.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeHistoryEntry 17 }

esaProbeHistoryOutOfSeqErrs OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of out of sequence errors across all
          packets received in the current interval. If (Received Seq Number)
          is less than or equal to (Last Received Seq Number),
          it is termed as out of sequence error.
          NOTE: This is not applicable for Y.1731 probes."
     ::= { esaProbeHistoryEntry 18 }

esaProbeHistoryMinRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeHistoryEntry 19 }

esaProbeHistoryMaxRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeHistoryEntry 20 }

esaProbeHistoryAvgRoundTripDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average round-trip delay(in microseconds) across all
          packets received in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeHistoryEntry 21 }

esaProbeHistorySumRoundTripDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  round trip delay(in microseconds)
          in the current interval.
          The reflector's processing time is excluded from the value."
     ::= { esaProbeHistoryEntry 22 }

esaProbeHistorySumOfSqRoundTripDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  round trip delays in the current interval."
     ::= { esaProbeHistoryEntry 23 }

esaProbeHistoryMinOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 24 }

esaProbeHistoryMaxOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 25 }

esaProbeHistoryAvgOnewayP2RDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average one-way source to destination delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 26 }

esaProbeHistorySumOnewayP2RDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of one-way delay from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 27 }

esaProbeHistorySumOfSqOnewayP2RDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of one-way delay from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 28 }

esaProbeHistoryMinOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 29 }

esaProbeHistoryMaxOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 30 }

esaProbeHistoryAvgOnewayR2PDelay OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The average one-way destination to source delay(in microseconds)
          across all packets received in the current interval."
     ::= { esaProbeHistoryEntry 31 }

esaProbeHistorySumOnewayR2PDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of one-way delay from destination to source
          in the current interval."
     ::= { esaProbeHistoryEntry 32 }

esaProbeHistorySumOfSqOnewayR2PDelay OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of one-way delay from destination to source
          in the current interval."
     ::= { esaProbeHistoryEntry 33 }

esaProbeHistoryMinPosP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum positive one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 34 }

esaProbeHistoryMaxPosP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum positive one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 35 }

esaProbeHistoryNumPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 36 }

esaProbeHistorySumPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 37 }

esaProbeHistorySumOfSqPosP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  positive one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 38 }

esaProbeHistoryMinNegP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum negative one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 39 }

esaProbeHistoryMaxNegP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum negative one-way jitter from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 40 }

esaProbeHistoryNumNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 41 }

esaProbeHistorySumNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 42 }

esaProbeHistorySumOfSqNegP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  negative one-way jitters from source to
          destination in the current interval."
     ::= { esaProbeHistoryEntry 43 }

esaProbeHistoryMinPosR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum positive one-way jitter from destination to source
          in the current interval."
     ::= { esaProbeHistoryEntry 44 }

esaProbeHistoryMaxPosR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum positive one-way jitter from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 45 }

esaProbeHistoryNumPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 46 }

esaProbeHistorySumPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 47 }

esaProbeHistorySumOfSqPosR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  positive one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 48 }

esaProbeHistoryMinNegR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The minimum negative one-way jitter from destination to source
          in the current interval."
     ::= { esaProbeHistoryEntry 49 }

esaProbeHistoryMaxNegR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The maximum negative one-way jitter from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 50 }

esaProbeHistoryNumNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total number of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 51 }


esaProbeHistorySumNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The total sum of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 52 }

esaProbeHistorySumOfSqNegR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "The sum of square of  negative one-way jitters from destination to
          source in the current interval."
     ::= { esaProbeHistoryEntry 53 }


esaProbeHistoryY1731P2RNegLossOccurrences OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This is the
          number of occurences of negative frame loss from Source MEP (Probe)
          to Destination MEP (Reflector). If these counts are non-zero then
          there could be some kind of provisioning mismatch between the Probe
          MEP and Reflector MEP. Here are some scenarios this can happen:
          - Probe MEP is configured to count in-profile frames only and the
            Reflector MEP is configured to count all frames with a mismatch
            in value for the attribute cfmMepLmCountInProfileFrames.
          - Probe MEP is configured to count data frames for specific VLAN
            priority and the Reflector MEP is configured to count data frames
            for all the priorities with a mismatch in values for the attributes
            cfmMepLmTxCountAllPrios or cfmMepLmRxCountAllPrios.
          NOTE: This could possibly happen due to reasons not related to
                configuration such as frame reordering in the network."
     ::= { esaProbeHistoryEntry 54 }

esaProbeHistoryY1731R2PNegLossOccurrences OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This is the
          number of occurences of negative frame loss from to Destination MEP
          (Reflector) to Source MEP (Probe). If these counts are non-zero then
          there could be some kind of provisioning mismatch between the Probe
          MEP and Reflector MEP. Here are some scenarios this can happen:
          - Probe MEP is configured to count in-profile frames only and the
            Reflector MEP is configured to count all frames with a mismatch
            in value for the attribute cfmMepLmCountInProfileFrames.
          - Probe MEP is configured to count data frames for specific VLAN
            priority and the Reflector MEP is configured to count data frames
            for all the priorities with a mismatch in values for the attributes
            cfmMepLmTxCountAllPrios or cfmMepLmRxCountAllPrios.
          NOTE: This could possibly happen due to reasons not related to
                configuration such as frame reordering in the network."
     ::= { esaProbeHistoryEntry 55 }

esaProbeHistoryY1731RxLmSamples OBJECT-TYPE
     SYNTAX     TruthValue
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This flag
          indicates that at least one loss measurement sample was received
          for this bin period (LMRs received). This is used by the user
          to differentiate between valid zero frame loss and not receiving
          any LMRs."
     ::= { esaProbeHistoryEntry 56 }

esaProbeHistoryY1731RxDmSamples OBJECT-TYPE
     SYNTAX     TruthValue
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
         "This attribute is only applicable to Y.1731 probes. This flag
          indicates that at least one delay measurement sample was received
          for this bin period (DMRs received)."
     ::= { esaProbeHistoryEntry 57 }

esaProbeHistoryY1731P2RFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Y.1731 probes. This is the
           number of data frames counted by the Source MEP (Probe) that
           are transmitted towards the Destination MEP (Reflector). Based
           on the configuration the MEP counts the data frames with:
           - MEP's LM TX priority or all the priorities
           - All the VLAN IDs belonging the MEP's MA VID list."
     ::= { esaProbeHistoryEntry 58 }

esaProbeHistoryY1731R2PFrames OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Y.1731 probes. This is the
           number of data frames counted by the Destination MEP (Reflector)
           that are transmitted towards the Source MEP (Probe). Based
           on the configuration the MEP counts the data frames with:
           - MEP's LM RX priority or all the priorities
           - All the VLAN IDs belonging the MEP's MA VID list."
     ::= { esaProbeHistoryEntry 59 }

esaProbeHistoryAvgAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the average absolute P2R Jitter value.
           ."
     ::= { esaProbeHistoryEntry 60 }

esaProbeHistoryAvgAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the average absolute R2P Jitter value.
           ."
     ::= { esaProbeHistoryEntry 61 }

esaProbeHistoryMinAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the minimum absolute P2R Jitter value.
           ."
     ::= { esaProbeHistoryEntry 62 }

esaProbeHistoryMinAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the minimum absolute R2P Jitter value.
           ."
     ::= { esaProbeHistoryEntry 63 }

esaProbeHistoryMaxAbsP2RJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the maximum absolute P2R Jitter value.
           ."
     ::= { esaProbeHistoryEntry 64 }

esaProbeHistoryMaxAbsR2PJitter OBJECT-TYPE
     SYNTAX     Unsigned32
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the maximum absolute R2P Jitter value.
           ."
     ::= { esaProbeHistoryEntry 65 }

esaProbeHistoryNumAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the number of absolute P2R Jitters."
     ::= { esaProbeHistoryEntry 66 }

esaProbeHistoryNumAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the number of absolute R2P Jitters."
     ::= { esaProbeHistoryEntry 67 }

esaProbeHistorySumAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of absolute P2R Jitter values."
     ::= { esaProbeHistoryEntry 68 }

esaProbeHistorySumAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of absolute R2P Jitter values."
     ::= { esaProbeHistoryEntry 69 }

esaProbeHistorySumOfSqAbsP2RJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of squares of absolute P2R Jitter values."
     ::= { esaProbeHistoryEntry 70 }

esaProbeHistorySumOfSqAbsR2PJitter OBJECT-TYPE
     SYNTAX     PerfCounter64
     MAX-ACCESS read-only
     STATUS     current
     DESCRIPTION
          "This attribute is only applicable to Layer 3 ICMP Timestamp and Layer 2 Y.1731 probes. 
           This is the sum of squares of absolute R2P Jitter values."
     ::= { esaProbeHistoryEntry 71 }
--
-- ESA Probe Statistics Distribution Config Table
--
esaProbeDistStatsConfigTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeDistStatsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that need to be configured
             in order to obtain statistical distribution
             of ESA Probe collected data."
    ::= { cmServAssuranceObjects 9 }

esaProbeDistStatsConfigEntry  OBJECT-TYPE
    SYNTAX      EsaProbeDistStatsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeDistStatsConfigTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeDistStatsConfigIndex }
    ::= { esaProbeDistStatsConfigTable 1 }

EsaProbeDistStatsConfigEntry ::= SEQUENCE {
    esaProbeDistStatsConfigIndex           Integer32,
    esaProbeDistStatsConfigType            EsaProbeDistStatsType,
    esaProbeDistStatsConfigMinVal          Integer32,
    esaProbeDistStatsConfigMaxVal          Integer32,
    esaProbeDistStatsConfigNumBins         Unsigned32,
    esaProbeDistStatsConfigLowBoundOfBin1  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin2  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin3  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin4  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin5  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin6  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin7  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin8  Integer32,
    esaProbeDistStatsConfigLowBoundOfBin9  Integer32
}

esaProbeDistStatsConfigIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..11)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "A unique index for each row.  Each row corresponds to each enumeration
             type specified in EsaProbeDistStatsType."
    ::= { esaProbeDistStatsConfigEntry 1 }

esaProbeDistStatsConfigType OBJECT-TYPE
    SYNTAX      EsaProbeDistStatsType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicates whether the statistical distribution
             is for round-trip delay (rt-delay), one-way probe to reflector
             delay (oneway-p2rdelay), one-way reflector to probe delay(oneway-r2pdelay),
             probe to reflector jitter (oneway-p2rjitter) or
             one-way reflector to probe jitter (oneway-r2pjitter)."
    ::= { esaProbeDistStatsConfigEntry 2 }

esaProbeDistStatsConfigMinVal OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Minimum sample value in distribution."
    ::= { esaProbeDistStatsConfigEntry 3 }

esaProbeDistStatsConfigMaxVal OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Maximum sample value in distribution."
    ::= { esaProbeDistStatsConfigEntry 4 }

esaProbeDistStatsConfigNumBins OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Number of distribution bins."
    ::= { esaProbeDistStatsConfigEntry 5 }

esaProbeDistStatsConfigLowBoundOfBin1 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 1st bin."
    ::= { esaProbeDistStatsConfigEntry 6 }

esaProbeDistStatsConfigLowBoundOfBin2 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 2nd bin."
    ::= { esaProbeDistStatsConfigEntry 7 }

esaProbeDistStatsConfigLowBoundOfBin3 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 3rd bin."
    ::= { esaProbeDistStatsConfigEntry 8 }

esaProbeDistStatsConfigLowBoundOfBin4 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 4th bin."
    ::= { esaProbeDistStatsConfigEntry 9 }

esaProbeDistStatsConfigLowBoundOfBin5 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 5th bin."
    ::= { esaProbeDistStatsConfigEntry 10 }

esaProbeDistStatsConfigLowBoundOfBin6 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 6th bin."
    ::= { esaProbeDistStatsConfigEntry 11 }

esaProbeDistStatsConfigLowBoundOfBin7 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 7th bin."
    ::= { esaProbeDistStatsConfigEntry 12 }

esaProbeDistStatsConfigLowBoundOfBin8 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 8th bin."
    ::= { esaProbeDistStatsConfigEntry 13 }

esaProbeDistStatsConfigLowBoundOfBin9 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The lower bound of the 9th bin."
    ::= { esaProbeDistStatsConfigEntry 14 }

--
-- ESA Probe Statistics Current Distribution Results Table
--
esaProbeDistStatsTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeDistStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that provide statistical distribution
             of ESA Probe collected data."
    ::= { cmServAssuranceObjects 10 }

esaProbeDistStatsEntry  OBJECT-TYPE
    SYNTAX      EsaProbeDistStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeDistStatsTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeDistStatsConfigIndex,
            esaProbeDistStatsDestinationIndex, esaProbeDistStatsCOSIndex }
    ::= { esaProbeDistStatsTable 1 }

EsaProbeDistStatsEntry ::= SEQUENCE {
    esaProbeDistStatsDestinationIndex  Integer32,
    esaProbeDistStatsCOSIndex          Integer32,
    esaProbeDistStatsAction            CmPmBinAction,
    esaProbeDistStatsCOS               ClassOfServiceType,
    esaProbeDistStatsNumBins           Integer32,
    esaProbeDistStatsLTMin             PerfCounter64,
    esaProbeDistStatsGTMax             PerfCounter64
}

esaProbeDistStatsDestinationIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             a destination (Reflector) Index, in a point-to-multi point SLA. 
             For Release 4.1 GE206, this value is 1, since only point-to-point SLA
             is supported."
    ::= { esaProbeDistStatsEntry 1 }

esaProbeDistStatsCOSIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             SLA for a specific Class of Service. 
             For Release 4.1 GE206, this value is 1, since the COS
             is explicitly specified in the Probe parameters." 
    ::= { esaProbeDistStatsEntry 2 }

esaProbeDistStatsAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { esaProbeDistStatsEntry 3 }

esaProbeDistStatsCOS   OBJECT-TYPE
    SYNTAX      ClassOfServiceType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The Class of Service.  
             For Release 4.1 GE206, this value is cos-not-applicable, since 
             multi COS SLA is not supported." 
    ::= { esaProbeDistStatsEntry 4 }

esaProbeDistStatsNumBins OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of bins over which the statistical
             data is distributed."
    ::= { esaProbeDistStatsEntry 5 }

esaProbeDistStatsLTMin OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which are
             less than the minimum value specified."
    ::= { esaProbeDistStatsEntry 6 }

esaProbeDistStatsGTMax OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which are
             greater than the maximum value specified."
    ::= { esaProbeDistStatsEntry 7 }

--
-- ESA Probe Statistics Current Distribution Results BIN Table
--
esaProbeDistStatsBinTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeDistStatsBinEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that provide statistical distribution
             of ESA Probe collected data for specific BINs."
    ::= { cmServAssuranceObjects 11 }

esaProbeDistStatsBinEntry  OBJECT-TYPE
    SYNTAX      EsaProbeDistStatsBinEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeDistStatsBinTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, 
            esaProbeDistStatsConfigIndex,
            esaProbeDistStatsDestinationIndex, 
            esaProbeDistStatsCOSIndex,
            esaProbeDistStatsBinIndex }
    ::= { esaProbeDistStatsBinTable 1 }

EsaProbeDistStatsBinEntry ::= SEQUENCE {
    esaProbeDistStatsBinIndex        Integer32,
    esaProbeDistStatsBinLower        Integer32,
    esaProbeDistStatsBinUpper        Integer32,
    esaProbeDistStatsBinNumSamples   PerfCounter64
}

esaProbeDistStatsBinIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An index for each row that uniquely provides statistical
             data for a range."
    ::= { esaProbeDistStatsBinEntry 1 }

esaProbeDistStatsBinLower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The lower limit of values collected in this bin."
    ::= { esaProbeDistStatsBinEntry 2 }

esaProbeDistStatsBinUpper OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The upper limit of values collected in this bin."
    ::= { esaProbeDistStatsBinEntry 3 }

esaProbeDistStatsBinNumSamples OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which fall between
             the lower and upper limits."
    ::= { esaProbeDistStatsBinEntry 4 }

--
-- ESA Probe Statistics History Distribution Results Table
--
esaProbeDistHistoryTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeDistHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that provide statistical distribution
             history of ESA Probe collected data."
    ::= { cmServAssuranceObjects 12 }

esaProbeDistHistoryEntry  OBJECT-TYPE
    SYNTAX      EsaProbeDistHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeDistHistoryTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, 
            esaProbeDistStatsConfigIndex, 
            esaProbeDistHistoryDestinationIndex, 
            esaProbeDistHistoryCOSIndex, 
            esaProbeDistHistoryIndex }
    ::= { esaProbeDistHistoryTable 1 }

EsaProbeDistHistoryEntry ::= SEQUENCE {
    esaProbeDistHistoryDestinationIndex   Integer32,
    esaProbeDistHistoryCOSIndex           Integer32,
    esaProbeDistHistoryIndex              Integer32,
    esaProbeDistHistoryTime               DateAndTime,
    esaProbeDistHistoryAction             CmPmBinAction,
    esaProbeDistHistoryCOS                ClassOfServiceType,
    esaProbeDistHistoryNumBins            Integer32,
    esaProbeDistHistoryLTMin              PerfCounter64,
    esaProbeDistHistoryGTMax              PerfCounter64
}

esaProbeDistHistoryDestinationIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             a destination (Reflector) Index, in a point-to-multi point SLA. 
             For Release 4.1 GE206, this value is 1, since only point-to-point SLA
             is supported."
    ::= { esaProbeDistHistoryEntry 1 }

esaProbeDistHistoryCOSIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             SLA for a specific Class of Service. 
             For Release 4.1 GE206, this value is 1, since the COS
             is explicitly specified in the Probe parameters." 
    ::= { esaProbeDistHistoryEntry 2 }

esaProbeDistHistoryIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this ESA Probe Distribution History entry."
    ::= { esaProbeDistHistoryEntry 3 }

esaProbeDistHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Indicates the time of history bin creation."
    ::= { esaProbeDistHistoryEntry 4 }

esaProbeDistHistoryAction OBJECT-TYPE
    SYNTAX      CmPmBinAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Allows the Manager to clear the bin."
    ::= { esaProbeDistHistoryEntry 5 }

esaProbeDistHistoryCOS   OBJECT-TYPE
    SYNTAX      ClassOfServiceType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The Class of Service.  
             For Release 4.1 GE206, this value is cos-not-applicable, since 
             multi COS SLA is not supported." 
    ::= { esaProbeDistHistoryEntry 6 }

esaProbeDistHistoryNumBins OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of bins over which the statistical
             data is distributed."
    ::= { esaProbeDistHistoryEntry 7 }

esaProbeDistHistoryLTMin OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which are
             less than the minimum value specified."
    ::= { esaProbeDistHistoryEntry 8 }

esaProbeDistHistoryGTMax OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which are
             greater than the maximum value specified."
    ::= { esaProbeDistHistoryEntry 9 }

--
-- ESA Probe Statistics History Distribution Results BIN Table
--
esaProbeDistHistoryBinTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeDistHistoryBinEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries that provide historical statistical distribution
             of ESA Probe collected data for specific BINs."
    ::= { cmServAssuranceObjects 13 }

esaProbeDistHistoryBinEntry  OBJECT-TYPE
    SYNTAX      EsaProbeDistHistoryBinEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeDistHistoryBinTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeDistStatsConfigIndex,
            esaProbeDistHistoryDestinationIndex, 
            esaProbeDistHistoryCOSIndex, 
            esaProbeDistHistoryIndex, esaProbeDistHistoryBinIndex }
    ::= { esaProbeDistHistoryBinTable 1 }

EsaProbeDistHistoryBinEntry ::= SEQUENCE {
    esaProbeDistHistoryBinIndex        Integer32,
    esaProbeDistHistoryBinLower        Integer32,
    esaProbeDistHistoryBinUpper        Integer32,
    esaProbeDistHistoryBinNumSamples   PerfCounter64
}

esaProbeDistHistoryBinIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "An index for each row that uniquely provides statistical
             data for a range."
    ::= { esaProbeDistHistoryBinEntry 1 }

esaProbeDistHistoryBinLower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The lower limit of values collected in this bin."
    ::= { esaProbeDistHistoryBinEntry 2 }

esaProbeDistHistoryBinUpper OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The upper limit of values collected in this bin."
    ::= { esaProbeDistHistoryBinEntry 3 }

esaProbeDistHistoryBinNumSamples OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Number of sample data points which fall between
             the lower and upper limits."
    ::= { esaProbeDistHistoryBinEntry 4 }


---ESA Probe Threshold Table
esaProbeStatsThresholdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeStatsThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of entries that allow manageability of ESA
             Probe Thresholds."
    ::= { cmServAssuranceObjects 14 }

esaProbeStatsThresholdEntry OBJECT-TYPE
    SYNTAX      EsaProbeStatsThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the esaProbeStatsThresholdTable."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeStatsThresholdIndex }
    ::= { esaProbeStatsThresholdTable 1 }

EsaProbeStatsThresholdEntry ::= SEQUENCE {
    esaProbeStatsThresholdIndex       Integer32,
    esaProbeStatsThresholdVariable    VariablePointer,
    esaProbeStatsThresholdAbsValueLo  Unsigned32,
    esaProbeStatsThresholdAbsValueHi  Unsigned32,
    esaProbeStatsThresholdMonValue    PerfCounter64
}

esaProbeStatsThresholdIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        esaProbeStatsThresholdTable."
    ::= { esaProbeStatsThresholdEntry 1 }

esaProbeStatsThresholdVariable OBJECT-TYPE
    SYNTAX     VariablePointer
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The object identifier of the particular variable to be
        sampled.  Objects of the esaProbeStatsTable table are
        monitored. Only variables that resolve to an ASN.1 primitive
        type of INTEGER (INTEGER, Integer32, Counter32, PerfCounter64,
        Gauge, or TimeTicks) may be sampled."
    ::= { esaProbeStatsThresholdEntry 2 }

esaProbeStatsThresholdAbsValueLo OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Lower 32 bits of the absolute value of the threshold."
    ::= { esaProbeStatsThresholdEntry 3 }

esaProbeStatsThresholdAbsValueHi OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Higher 32 bits of the absolute value of the threshold."
    ::= { esaProbeStatsThresholdEntry 4 }

esaProbeStatsThresholdMonValue OBJECT-TYPE
    SYNTAX     PerfCounter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "Monitored value corresponding to esaProbeStatsThresholdVariable."
    ::= { esaProbeStatsThresholdEntry 5 }

--
-- ESA Probe COS Configuration Table
--
esaProbeCOSConfigTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeCOSConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to ESA Probes COS Configuration Levels."
    ::= { cmServAssuranceObjects 15 }

esaProbeCOSConfigEntry  OBJECT-TYPE
    SYNTAX      EsaProbeCOSConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeCOSConfigTable. Entries can
            be created in this table by management application action."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeCOSConfigIndex }
    ::= { esaProbeCOSConfigTable 1 }

EsaProbeCOSConfigEntry ::= SEQUENCE {
    -- identification
    esaProbeCOSConfigIndex       Integer32,
    esaProbeCOSConfigType        ClassOfServiceType,
    esaProbeCOSConfigInterval    EsaProbePktIntervalType,
    esaProbeCOSConfigPktSize     Integer32,
    esaProbeCOSConfigStorageType StorageType,
    esaProbeCOSConfigRowStatus   RowStatus,    
    esaProbeCOSConfigslmInterval EsaProbePktIntervalType,
    esaProbeCOSConfigslmPktSize  Integer32,

    esaProbeCOSConfigSoamPmExtAvailFlrThreshold     Unsigned32,
    esaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus    Unsigned32,
    esaProbeCOSConfigSoamPmExtConDeltaTsForAvail    Unsigned32
}

esaProbeCOSConfigIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..8)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             SLA (Service Level Agreement) for a specific Class of Service.
             Index 1 is associated with cos-0, index 2 with cos-1, index 3 with cos-2 and so on." 
    ::= { esaProbeCOSConfigEntry 1 }

esaProbeCOSConfigType OBJECT-TYPE
    SYNTAX      ClassOfServiceType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This is the Class of Service Type for Y.1731/SLM-SLR Probes, associated with the row."
    ::= { esaProbeCOSConfigEntry 2 }

esaProbeCOSConfigInterval OBJECT-TYPE
    SYNTAX      EsaProbePktIntervalType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This is the Class of Service Y.1731/SLM-SLR packet interval type."
    ::= { esaProbeCOSConfigEntry 3 }

esaProbeCOSConfigPktSize OBJECT-TYPE
    SYNTAX     Integer32 (64..9612)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "This is the Class of Service Y.1731/SLM-SLR Packet Size.
          This object enables specification of the TLV size for
          Delay Measurement Measures(DMMs) as well as Synthetic Loss Measurement Frames (SLMs)." 
     ::= { esaProbeCOSConfigEntry 4 }

esaProbeCOSConfigStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { esaProbeCOSConfigEntry 5 }

esaProbeCOSConfigRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of esaProbeCOSConfigRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            esaProbeCOSConfigRowStatus value as createAndGo(4).
            Upon successful row creation, this variable has a
            value of active(1).

            The esaProbeCOSConfigRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { esaProbeCOSConfigEntry 6 }

esaProbeCOSConfigslmInterval OBJECT-TYPE
    SYNTAX     EsaProbePktIntervalType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "This allows specification of the interval between subsequent SLM packets."
    ::= { esaProbeCOSConfigEntry 7 }

esaProbeCOSConfigslmPktSize OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "This allows specification of the SLM Packet Size."
    ::= { esaProbeCOSConfigEntry 8 }

esaProbeCOSConfigSoamPmExtAvailFlrThreshold OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Frame loss ratio threshold for Availability (m%)."
    ::= { esaProbeCOSConfigEntry 9 }

esaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus OBJECT-TYPE
    SYNTAX      Unsigned32 (1..1000000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This is the number of loss measurement PDUs. 
             delta_t is this attribute multiplied by the 
             loss measurement message period. 
             For example, if SLM message period is 
             100ms and this attribute is 10, 
             then the delta_t is 1second."
    ::= { esaProbeCOSConfigEntry 10 }

esaProbeCOSConfigSoamPmExtConDeltaTsForAvail OBJECT-TYPE
    SYNTAX      Unsigned32 (1..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This is the number of consecutive delta_ts used 
             in the computation of availability/unavailability (n)."
    ::= { esaProbeCOSConfigEntry 11 }


--
-- ESA Probe Destination Configuration Table
--
esaProbeMultiDestinationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF EsaProbeMultiDestinationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to ESA Probes Multi Destinations."
    ::= { cmServAssuranceObjects 16 }

esaProbeMultiDestinationEntry OBJECT-TYPE
    SYNTAX      EsaProbeMultiDestinationEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeMultiDestinationTable. Entries can
            be created in this table by management application action. Entries in this table
            represent destinations in a point-to-multipoint ESA Probe."
    INDEX { neIndex, shelfIndex, slotIndex, esaProbeIndex, esaProbeDestinationIndex }
    ::= { esaProbeMultiDestinationTable 1 }

EsaProbeMultiDestinationEntry ::= SEQUENCE {
    -- identification
    esaProbeDestinationIndex        Integer32,
    esaProbeDestinationMepType      MepDestinationType,
    esaProbeDestinationMepMacAddr   MacAddress,
    esaProbeDestinationMepId        Dot1agCfmMepIdOrZero,
    esaProbeDestinationStorageType  StorageType,
    esaProbeDestinationRowStatus    RowStatus
}

esaProbeDestinationIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "An integer index value used to uniquely identify
             a destination." 
    ::= { esaProbeMultiDestinationEntry 1 }

esaProbeDestinationMepType OBJECT-TYPE
    SYNTAX      MepDestinationType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for point-to-multipoint probes. 
            This indicates if the destination MEP is specified using the MEP ID object
            esaProbeDestinationMepId."
    ::= { esaProbeMultiDestinationEntry 2 }

esaProbeDestinationMepMacAddr OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for point-to-multipoint probes. 
            It identifies the destination MEP. This MAC address will be the 
            destination MAC address for  frames if esaProbeDestinationMepType is 'macaddress'. 
            If CC protocol is not enabled between source and destination MEP then this is the
            only choice to identify the remote MEP."
    ::= { esaProbeMultiDestinationEntry 3 }

esaProbeDestinationMepId OBJECT-TYPE
    SYNTAX      Dot1agCfmMepIdOrZero
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "This attribute is applicable only for point-to-multipoint probes. 
            It identifies the destination MEP. The MAC
            address of this RMEP ID from RMEP database will be the destination
            MAC address. This value is used only if the
            esaProbeDestinationMepType is 'mepid'. CC protocol must be
            enabled between source and destination MEP if the RMEP ID
            is used to identify the Remote MEP."
    ::= { esaProbeMultiDestinationEntry 4 }

esaProbeDestinationStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { esaProbeMultiDestinationEntry 5 }

esaProbeDestinationRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The status of this row.
            An entry MUST NOT exist in the active state unless all
            objects in the entry have an appropriate value, as described
            in the description clause for each writable object.

            The values of esaProbeDestinationRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            esaProbeDestinationRowStatus value as createAndGo(4).
            Upon successful row creation, this variable has a
            value of active(1).

            The esaProbeDestinationRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { esaProbeMultiDestinationEntry 6 }


--
--  BERT Control Table
--
bertControlTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF BertControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries for the control of BERT Streams."
    ::= { cmServAssuranceObjects 17 }

bertControlEntry  OBJECT-TYPE
    SYNTAX      BertControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the bertControlTable."
    INDEX { neIndex, shelfIndex, slotIndex, ecpaControlIndex }
    ::= { bertControlTable 1 }

BertControlEntry ::= SEQUENCE {
    bertControlIndex                Integer32,
    bertControlSourceEntity         VariablePointer,
    bertControlTestMode             BerTestMode,
    bertControlDuration             Integer32,
    bertControlStream               Integer32,
    bertControlAction               BertControlAction,
    bertControlTestStatus           BerTestStatus
}

bertControlIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
           "An arbitrary integer index value used to uniquely identify
            this BERT Control."
    ::= { bertControlEntry 1 }

bertControlSourceEntity OBJECT-TYPE
    SYNTAX      VariablePointer 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Pointer to the entity on which the BERT 
             will be started within a card."
    ::= { bertControlEntry 2 }

bertControlTestMode OBJECT-TYPE
    SYNTAX      BerTestMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The mode of BERT to be performed."
    ::= { bertControlEntry 3 }

bertControlDuration OBJECT-TYPE
    SYNTAX     Integer32 (0..259200)
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object enables specification of the time in seconds
          for BERT. BERT will be stopped
          after the specified time interval is exhausted."
    ::= { bertControlEntry 4 }

bertControlStream OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object specifies which stream should be included in the
          BERT execution. Value of 0 indicates that no streams is selected
          by bertControlStream. Non 0 value indicates the 
          selected BERT stream."
    ::= { bertControlEntry 5 }

bertControlAction     OBJECT-TYPE
    SYNTAX      BertControlAction 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This object enables starting/stopping of traffic generation
          as well as resetting of monitored streams. This object may
          not be specified at configuration creation time - in this
          case it will take the value of `none'.  This allows manager
          to explicitly control the start and stop of traffic
          generation/monitoring activity.
          Value of `stop' is invalid if the activity is specified but
          not started. Value of `start' is valid during creation time,
          as well as when activity is already stopped.  Specification
          of `reset'  resets the specified streams to default values,
          as well as clears the control specification. Get on this
          variable gives the most recent SNMP set specification."
    ::= { bertControlEntry 6 }

bertControlTestStatus     OBJECT-TYPE
    SYNTAX     BerTestStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "This object reflects the current status of test set configuration
          entry. Upon creation, the status has a value of `initial'; the
          status changes to `in-progress' upon ecpaControlAction
          indicating `start'; the status changes to `completed' upon
          ecpaControlAction indicating stop, as well as when
          the specified test generation completes
          autonomously upon completion of number of frames or
          completion of specified time interval."
    ::= { bertControlEntry 7 }


--
--  BERT Config Stream Table
--
bertConfigStreamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF BertConfigStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries for the configuration of BERT Config Stream Specifications."
    ::= { cmServAssuranceObjects 18 }

bertConfigStreamEntry OBJECT-TYPE
    SYNTAX      BertConfigStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the bertConfigStreamTable."
    INDEX { bertConfigStreamIndex }
    ::= { bertConfigStreamTable 1 }

BertConfigStreamEntry ::= SEQUENCE {
    bertConfigStreamIndex                      Integer32,
    bertConfigStreamName                       DisplayString,
    bertConfigStreamTxPattern                  BertPattern,
    bertConfigStreamErrInjectEnabled           TruthValue,
    bertConfigStreamErrInjectRate              BitErrRate,
    bertConfigStreamErrInjectRateMultiplier    Integer32,
    bertConfigStreamUserPatternLength          BertUserPatternLength,
    bertConfigStreamUserPattern                DisplayString
}

bertConfigStreamIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An arbitrary integer index value used to uniquely identify
            this BERT configStream."
    ::= { bertConfigStreamEntry 1 }

bertConfigStreamName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Symbolic Stream Name that can be associated with an BERT Config stream."
    ::= { bertConfigStreamEntry 2 }

bertConfigStreamTxPattern  OBJECT-TYPE
    SYNTAX     BertPattern
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "Allows specification of the Tx pattern of this BERT Config stream."
    ::= { bertConfigStreamEntry 3 }

bertConfigStreamErrInjectEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "This object allows specification of whether Bit Error be injected."
    ::= { bertConfigStreamEntry 4 }

bertConfigStreamErrInjectRate OBJECT-TYPE
    SYNTAX     BitErrRate
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "Bit Error Rate, applicable when Error Injection is enabled."
    ::= { bertConfigStreamEntry 5 }

bertConfigStreamErrInjectRateMultiplier OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "BER value with 0.1 Step, in this release is fixed 10, 
         only applicable when Error Injection is enabled."
    ::= { bertConfigStreamEntry 6 }

bertConfigStreamUserPatternLength OBJECT-TYPE
    SYNTAX      BertUserPatternLength
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "User defined testing pattern length."
    ::= { bertConfigStreamEntry 7 }

bertConfigStreamUserPattern OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "User defined testing pattern in a HEX format display string, 
            applicable when test pattern is user defined."
    ::= { bertConfigStreamEntry 8 }


--- BERT Test Streams Table
bertTestStreamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF BertTestStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries containing results of a previous test."
    ::= { cmServAssuranceObjects 19 }

bertTestStreamEntry OBJECT-TYPE
    SYNTAX      BertTestStreamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the bertTestStreamTable. Entries in this
            table show results of the current BERT."
    INDEX { neIndex, shelfIndex, slotIndex, bertControlIndex, 
            bertTestStreamIndex }
    ::= { bertTestStreamTable 1 }

BertTestStreamEntry ::= SEQUENCE {
    bertTestStreamIndex                                  Integer32,
    bertTestStreamName                                   DisplayString,
    bertTestStreamTxPattern                              BertPattern,
    bertTestStreamErrInjectEnabled                       TruthValue,
    bertTestStreamErrInjectRate                          BitErrRate,
    bertTestStreamErrInjectRateMultiplier                Integer32,
    bertTestStreamUserPatternLength                      BertUserPatternLength,
    bertTestStreamUserPattern                            DisplayString,
    bertTestStreamMonStartTime                           DateAndTime,
    bertTestStreamMonEndTime                             DateAndTime,
    bertTestStreamMonElapsedTime                         Integer32,
    bertTestStreamMonSyncState                           BertSyncState,
    bertTestStreamMonRxPattern                           BertPattern,
    bertTestStreamMonSyncCounts                          Unsigned32,
    bertTestStreamMonRxBitErrsSinceStart                 PerfCounter64,
    bertTestStreamMonRxBitsSinceStart                    PerfCounter64,
    bertTestStreamMonRxESsSinceStart                     Unsigned32,
    bertTestStreamMonRxErrRateSinceStart                 BitErrRate,
    bertTestStreamMonRxErrRateMultiplierSinceStart       Integer32,
    bertTestStreamMonRxBitErrsSinceLastSync              PerfCounter64,
    bertTestStreamMonRxBitsSinceLastSync                 PerfCounter64,
    bertTestStreamMonRxESsSinceLastSync                  Unsigned32,
    bertTestStreamMonRxErrRateSinceLastSync              BitErrRate,
    bertTestStreamMonRxErrRateMultiplierSinceLastSync    Unsigned32,
    bertTestStreamConfigChangedFlag                      TruthValue,
    bertTestStreamMonOOSSsSinceStart                     Unsigned32
}

bertTestStreamIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An integer index value corresponding to the stream index 
             for which the BERT was initiated."
    ::= { bertTestStreamEntry 1 }

bertTestStreamName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Symbolic Stream Name that can be associated with an BERT stream."
    ::= { bertTestStreamEntry 2 }

bertTestStreamTxPattern  OBJECT-TYPE
    SYNTAX     BertPattern
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Tx pattern of this BERT Test stream."
    ::= { bertTestStreamEntry 3 }

bertTestStreamErrInjectEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "This object allows specification of whether Bit Error be injected."
    ::= { bertTestStreamEntry 4 }

bertTestStreamErrInjectRate OBJECT-TYPE
    SYNTAX     BitErrRate
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Bit Error Rate, applicable when Error Injection is enabled."
    ::= { bertTestStreamEntry 5 }

bertTestStreamErrInjectRateMultiplier OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "BER value with 0.1 Step, in this release is fixed 10, 
         only applicable when Error Injection is enabled."
    ::= { bertTestStreamEntry 6 }

bertTestStreamUserPatternLength OBJECT-TYPE
    SYNTAX      BertUserPatternLength
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "User defined testing pattern length."
    ::= { bertTestStreamEntry 7 }

bertTestStreamUserPattern OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "User defined testing pattern in a HEX format display string, 
            applicable when test pattern is user defined."
    ::= { bertTestStreamEntry 8 }

bertTestStreamMonStartTime OBJECT-TYPE
    SYNTAX     DateAndTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "The start time of the BERT for this stream." 
    ::= { bertTestStreamEntry 9 }

bertTestStreamMonEndTime OBJECT-TYPE
    SYNTAX     DateAndTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "The end time of the BERT for this stream." 
    ::= { bertTestStreamEntry 10 }

bertTestStreamMonElapsedTime OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "The elapsed time of BERT for this stream in seconds."
    ::= { bertTestStreamEntry 11 }

bertTestStreamMonSyncState OBJECT-TYPE
    SYNTAX     BertSyncState
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "The Sync State of the BERT."
    ::= { bertTestStreamEntry 12 }

bertTestStreamMonRxPattern OBJECT-TYPE
    SYNTAX     BertPattern
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Rx pattern of this BERT Test stream."
    ::= { bertTestStreamEntry 13 }

bertTestStreamMonSyncCounts OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Sync counts from test start."
    ::= { bertTestStreamEntry 14 }

bertTestStreamMonRxBitErrsSinceStart OBJECT-TYPE
    SYNTAX     PerfCounter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received Errors since test start."
    ::= { bertTestStreamEntry 15 }

bertTestStreamMonRxBitsSinceStart OBJECT-TYPE
    SYNTAX     PerfCounter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received Bits since test start."
    ::= { bertTestStreamEntry 16 }

bertTestStreamMonRxESsSinceStart OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Error seconds since test start."
    ::= { bertTestStreamEntry 17 }

bertTestStreamMonRxErrRateSinceStart OBJECT-TYPE
    SYNTAX     BitErrRate
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received BER Unit since test start."
    ::= { bertTestStreamEntry 18 }

bertTestStreamMonRxErrRateMultiplierSinceStart OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received BER value with 0.1 Step since test start."
    ::= { bertTestStreamEntry 19 }

bertTestStreamMonRxBitErrsSinceLastSync OBJECT-TYPE
    SYNTAX     PerfCounter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received Errors since last sync."
    ::= { bertTestStreamEntry 20 }

bertTestStreamMonRxBitsSinceLastSync OBJECT-TYPE
    SYNTAX     PerfCounter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received Bits since last sync."
    ::= { bertTestStreamEntry 21 }

bertTestStreamMonRxESsSinceLastSync OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received Error seconds since last sync."
    ::= { bertTestStreamEntry 22 }

bertTestStreamMonRxErrRateSinceLastSync OBJECT-TYPE
    SYNTAX     BitErrRate
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received BER Unit since last sync."
    ::= { bertTestStreamEntry 23 }

bertTestStreamMonRxErrRateMultiplierSinceLastSync OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "Allows specification of the Received BER value with 0.1 Step since last sync."
    ::= { bertTestStreamEntry 24 }

bertTestStreamConfigChangedFlag  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
         "This object provides information on whether the associated bertConfigStream is changed."
     ::= { bertTestStreamEntry 25 }
 
bertTestStreamMonOOSSsSinceStart OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
         "This object means the Out of Sync Seconds since start."
    ::= { bertTestStreamEntry 26 }

--
--  Esa Probe Cos Configuration Soam Pm Ext Table
--

f3EsaProbeCOSConfigSoamPmExtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3EsaProbeCOSConfigSoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to ESA Probe Cos Configurations MEF 35."
    ::= { cmServAssuranceObjects 20 }

f3EsaProbeCOSConfigSoamPmExtEntry  OBJECT-TYPE
    SYNTAX      F3EsaProbeCOSConfigSoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A row in the esaProbeCOSConfigTable MEF 35. Entries can
             be created in this table by management application action."
    AUGMENTS { esaProbeCOSConfigEntry }
    ::= { f3EsaProbeCOSConfigSoamPmExtTable 1 }

F3EsaProbeCOSConfigSoamPmExtEntry ::= SEQUENCE {
    f3EsaProbeCOSConfigSoamPmExtAvailFlrThreshold     Unsigned32,
    f3EsaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus    Unsigned32,
    f3EsaProbeCOSConfigSoamPmExtConDeltaTsForAvail    Unsigned32
}

f3EsaProbeCOSConfigSoamPmExtAvailFlrThreshold OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Frame loss ratio threshold for Availability (m%)."
    ::= { f3EsaProbeCOSConfigSoamPmExtEntry 1 }

f3EsaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus OBJECT-TYPE
    SYNTAX      Unsigned32 (1..1000000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This is the number of loss measurement PDUs. 
             delta_t is this attribute multiplied by the 
             loss measurement message period. 
             For example, if SLM message period is 
             100ms and this attribute is 10, 
             then the delta_t is 1second."
    ::= { f3EsaProbeCOSConfigSoamPmExtEntry 2 }

f3EsaProbeCOSConfigSoamPmExtConDeltaTsForAvail OBJECT-TYPE
    SYNTAX      Unsigned32 (1..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This is the number of consecutive delta_ts used 
             in the computation of availability/unavailability (n)."
    ::= { f3EsaProbeCOSConfigSoamPmExtEntry 3 }

--
--  Esa Probe Stats Soam Pm Ext Table
--

f3EsaProbeStatsSoamPmExtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3EsaProbeStatsSoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of ESA Probe MEF 35 related statistics kept for
             a particular ESA Probe entity. These reflect the
             current data."
    ::= { cmServAssuranceObjects 21 }

f3EsaProbeStatsSoamPmExtEntry OBJECT-TYPE
    SYNTAX      F3EsaProbeStatsSoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the f3EsaProbeStatsSoamPmExtTable."
    AUGMENTS { esaProbeStatsEntry }
    ::= { f3EsaProbeStatsSoamPmExtTable 1 }

F3EsaProbeStatsSoamPmExtEntry ::= SEQUENCE {
    f3EsaProbeStatsSoamPmExtMinP2RFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxP2RFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgP2RFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtMinR2PFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxR2PFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgR2PFlr                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtP2rSeverelyErroredDeltaTs   PerfCounter64,
    f3EsaProbeStatsSoamPmExtR2PSeverelyErroredDeltaTs   PerfCounter64,
    f3EsaProbeStatsSoamPmExtP2rAvailableTime            PerfCounter64,
    f3EsaProbeStatsSoamPmExtR2PAvailableTime            PerfCounter64,
    f3EsaProbeStatsSoamPmExtP2rUnavailableTime          PerfCounter64,
    f3EsaProbeStatsSoamPmExtR2PUnavailableTime          PerfCounter64,
    f3EsaProbeStatsSoamPmExtMinAbsRTJitter              PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxAbsRTJitter              PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgAbsRTJitter              PerfCounter64,
    f3EsaProbeStatsSoamPmExtNumAbsRTJitter              PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumAbsRTJitter              PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumOfSqAbsRTJitter          PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxP2RFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgP2RFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtNumP2RFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumP2RFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumOfSqP2RFDR               PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxR2PFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgR2PFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtNumR2PFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumR2PFDR                   PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumOfSqR2PFDR               PerfCounter64,
    f3EsaProbeStatsSoamPmExtMaxRTFDR                    PerfCounter64,
    f3EsaProbeStatsSoamPmExtAvgRTFDR                    PerfCounter64,
    f3EsaProbeStatsSoamPmExtNumRTFDR                    PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumRTFDR                    PerfCounter64,
    f3EsaProbeStatsSoamPmExtSumOfSqRTFDR                PerfCounter64,
    f3EsaProbeStatsSoamPmExtP2rAvailableDeltaTs         PerfCounter64,
    f3EsaProbeStatsSoamPmExtR2pAvailableDeltaTs         PerfCounter64,
    f3EsaProbeStatsSoamPmExtP2rUnavailableDeltaTs       PerfCounter64,
    f3EsaProbeStatsSoamPmExtR2pUnavailableDeltaTs       PerfCounter64,
    f3EsaProbeStatsSoamPmExtElapsedTime                 PerfCounter64
}

f3EsaProbeStatsSoamPmExtMinP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64  
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the minimum  probe to reflector FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 1 }

f3EsaProbeStatsSoamPmExtMaxP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the maximum  probe to reflector FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 2 }

f3EsaProbeStatsSoamPmExtAvgP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the average  probe to reflector FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 3 }

f3EsaProbeStatsSoamPmExtMinR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the minimum  reflector to probe FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 4 }

f3EsaProbeStatsSoamPmExtMaxR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the maximum  reflector to probe FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 5 }

f3EsaProbeStatsSoamPmExtAvgR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the average  reflector to probe FLR (m%)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 6 }

f3EsaProbeStatsSoamPmExtP2rSeverelyErroredDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the number of delta_ts in which the FLR is greater 
         than availability threshold in the probe to reflector direction."
    ::= { f3EsaProbeStatsSoamPmExtEntry 7 }

f3EsaProbeStatsSoamPmExtR2PSeverelyErroredDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the number of delta_ts in which the FLR is greater 
         than availability threshold in the reflector to probe direction."
    ::= { f3EsaProbeStatsSoamPmExtEntry 8 }

f3EsaProbeStatsSoamPmExtP2rAvailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the probe to reflector available delta_ts which begins to increment 
         when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts.
         It is incremented until FLR is greater than availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 9 }

f3EsaProbeStatsSoamPmExtR2PAvailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the reflector to probe available delta_ts which begins to increment 
         when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts.
         It is incremented until FLR is greater than availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 10 }

f3EsaProbeStatsSoamPmExtP2rUnavailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the probe to reflector unavailable delta_ts which begins to increment 
         when FLR is greater than availability threshold for 'n' consecutive delta_ts. 
         It is incremented until FLR is less than or equal to availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 11 }

f3EsaProbeStatsSoamPmExtR2PUnavailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the reflector to probe unavailable delta_ts which begins to increment 
         when FLR is greater than availability threshold for 'n' consecutive delta_ts. 
         It is incremented until FLR is less than or equal to availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 12 }

f3EsaProbeStatsSoamPmExtMinAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum absolute round-trip (two-way) jitter in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 13 }

f3EsaProbeStatsSoamPmExtMaxAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum absolute round-trip (two-way) jitter in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 14 }

f3EsaProbeStatsSoamPmExtAvgAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average absolute round-trip (two-way) jitter in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 15 }

f3EsaProbeStatsSoamPmExtNumAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of absolute round-trip (two-way) jitter samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 16 }

f3EsaProbeStatsSoamPmExtSumAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of absolute round-trip (two-way) jitter samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 17 }

f3EsaProbeStatsSoamPmExtSumOfSqAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of absolute round-trip (two-way) jitter samples (microseconds^2)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 18 }

f3EsaProbeStatsSoamPmExtMaxP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum P2R (Forward) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 19 }

f3EsaProbeStatsSoamPmExtAvgP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average P2R (Forward) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 20 }

f3EsaProbeStatsSoamPmExtNumP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of P2R (Forward) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 21 }

f3EsaProbeStatsSoamPmExtSumP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of P2R (Forward) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 22 }

f3EsaProbeStatsSoamPmExtSumOfSqP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of P2R (Forward) FDR samples (microseconds^2)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 23 }

f3EsaProbeStatsSoamPmExtMaxR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum R2P (Backward) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 24 }

f3EsaProbeStatsSoamPmExtAvgR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average R2P (Backward) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 25 }

f3EsaProbeStatsSoamPmExtNumR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of R2P (Backward) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 26 }

f3EsaProbeStatsSoamPmExtSumR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of R2P (Backward) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 27 }

f3EsaProbeStatsSoamPmExtSumOfSqR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of R2P (Backward) FDR samples (microseconds^2)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 28 }

f3EsaProbeStatsSoamPmExtMaxRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum round-trip (two-way) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 29 }

f3EsaProbeStatsSoamPmExtAvgRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average round-trip (two-way) FDR in a measurement interval (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 30 }

f3EsaProbeStatsSoamPmExtNumRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of round-trip (two-way) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 31 }

f3EsaProbeStatsSoamPmExtSumRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of round-trip (two-way) FDR samples (microseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 32 }

f3EsaProbeStatsSoamPmExtSumOfSqRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of round-trip (two-way) FDR samples (microseconds^2)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 33 }

f3EsaProbeStatsSoamPmExtP2rAvailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Probe to reflector available delta_ts which begins to increment when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 34 }

f3EsaProbeStatsSoamPmExtR2pAvailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflector to probe available delta_ts which begins to increment when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 35 }

f3EsaProbeStatsSoamPmExtP2rUnavailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Probe to reflector unavailable delta_ts which begins to increment when FLR is greater than availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 36 }

f3EsaProbeStatsSoamPmExtR2pUnavailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflector to probe unavailable delta_ts which begins to increment when FLR is greater than availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeStatsSoamPmExtEntry 37 }

f3EsaProbeStatsSoamPmExtElapsedTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The elapsed time (monotonic time) for the interval which could be less or more than measurement interval based on which the Time of Day was moved forward or backwards respectively (miliseconds)."
    ::= { f3EsaProbeStatsSoamPmExtEntry 38 }

--
--  Esa Probe History Soam Pm Ext Table
--

f3EsaProbeHistorySoamPmExtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3EsaProbeHistorySoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A collection of ESA Probe MEF 35 related history kept for
             a particular ESA Probe entity. These reflect the
             history data."
    ::= { cmServAssuranceObjects 22 }

f3EsaProbeHistorySoamPmExtEntry OBJECT-TYPE
    SYNTAX      F3EsaProbeHistorySoamPmExtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the f3EsaProbeHistorySoamPmExtTable."
    AUGMENTS { esaProbeHistoryEntry }
    ::= { f3EsaProbeHistorySoamPmExtTable 1 }

F3EsaProbeHistorySoamPmExtEntry ::= SEQUENCE {
    f3EsaProbeHistorySoamPmExtMinP2RFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxP2RFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgP2RFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtMinR2PFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxR2PFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgR2PFlr                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtP2rSeverelyErroredDeltaTs   PerfCounter64,
    f3EsaProbeHistorySoamPmExtR2PSeverelyErroredDeltaTs   PerfCounter64,
    f3EsaProbeHistorySoamPmExtP2rAvailableTime            PerfCounter64,
    f3EsaProbeHistorySoamPmExtR2PAvailableTime            PerfCounter64,
    f3EsaProbeHistorySoamPmExtP2rUnavailableTime          PerfCounter64,
    f3EsaProbeHistorySoamPmExtR2PUnavailableTime          PerfCounter64,
    f3EsaProbeHistorySoamPmExtMinAbsRTJitter              PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxAbsRTJitter              PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgAbsRTJitter              PerfCounter64,
    f3EsaProbeHistorySoamPmExtNumAbsRTJitter              PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumAbsRTJitter              PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumOfSqAbsRTJitter          PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxP2RFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgP2RFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtNumP2RFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumP2RFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumOfSqP2RFDR               PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxR2PFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgR2PFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtNumR2PFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumR2PFDR                   PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumOfSqR2PFDR               PerfCounter64,
    f3EsaProbeHistorySoamPmExtMaxRTFDR                    PerfCounter64,
    f3EsaProbeHistorySoamPmExtAvgRTFDR                    PerfCounter64,
    f3EsaProbeHistorySoamPmExtNumRTFDR                    PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumRTFDR                    PerfCounter64,
    f3EsaProbeHistorySoamPmExtSumOfSqRTFDR                PerfCounter64,
    f3EsaProbeHistorySoamPmExtP2rAvailableDeltaTs         PerfCounter64,
    f3EsaProbeHistorySoamPmExtR2pAvailableDeltaTs         PerfCounter64,
    f3EsaProbeHistorySoamPmExtP2rUnavailableDeltaTs       PerfCounter64,
    f3EsaProbeHistorySoamPmExtR2pUnavailableDeltaTs       PerfCounter64,
    f3EsaProbeHistorySoamPmExtElapsedTime                 PerfCounter64
}

f3EsaProbeHistorySoamPmExtMinP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64  
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the minimum  probe to reflector FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 1 }

f3EsaProbeHistorySoamPmExtMaxP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the maximum  probe to reflector FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 2 }

f3EsaProbeHistorySoamPmExtAvgP2RFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the average  probe to reflector FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 3 }

f3EsaProbeHistorySoamPmExtMinR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the minimum  reflector to probe FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 4 }

f3EsaProbeHistorySoamPmExtMaxR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the maximum  reflector to probe FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 5 }

f3EsaProbeHistorySoamPmExtAvgR2PFlr OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the average  reflector to probe FLR."
    ::= { f3EsaProbeHistorySoamPmExtEntry 6 }

f3EsaProbeHistorySoamPmExtP2rSeverelyErroredDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the number of delta_ts in which the FLR is greater 
         than availability threshold in the probe to reflector direction."
    ::= { f3EsaProbeHistorySoamPmExtEntry 7 }

f3EsaProbeHistorySoamPmExtR2PSeverelyErroredDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the number of delta_ts in which the FLR is greater 
         than availability threshold in the reflector to probe direction."
    ::= { f3EsaProbeHistorySoamPmExtEntry 8 }

f3EsaProbeHistorySoamPmExtP2rAvailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the probe to reflector available delta_ts which begins to increment 
         when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts.
         It is incremented until FLR is greater than availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 9 }

f3EsaProbeHistorySoamPmExtR2PAvailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the reflector to probe available delta_ts which begins to increment 
         when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts.
         It is incremented until FLR is greater than availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 10 }

f3EsaProbeHistorySoamPmExtP2rUnavailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the probe to reflector unavailable delta_ts which begins to increment 
         when FLR is greater than availability threshold for 'n' consecutive delta_ts. 
         It is incremented until FLR is less than or equal to availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 11 }

f3EsaProbeHistorySoamPmExtR2PUnavailableTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the reflector to probe unavailable delta_ts which begins to increment 
         when FLR is greater than availability threshold for 'n' consecutive delta_ts. 
         It is incremented until FLR is less than or equal to availability threshold for 
         'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 12 }

f3EsaProbeHistorySoamPmExtMinAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum absolute round-trip (two-way) jitter in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 13 }

f3EsaProbeHistorySoamPmExtMaxAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum absolute round-trip (two-way) jitter in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 14 }

f3EsaProbeHistorySoamPmExtAvgAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average absolute round-trip (two-way) jitter in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 15 }

f3EsaProbeHistorySoamPmExtNumAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of absolute round-trip (two-way) jitter samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 16 }

f3EsaProbeHistorySoamPmExtSumAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of absolute round-trip (two-way) jitter samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 17 }

f3EsaProbeHistorySoamPmExtSumOfSqAbsRTJitter OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of absolute round-trip (two-way) jitter samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 18 }

f3EsaProbeHistorySoamPmExtMaxP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum P2R (Forward) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 19 }

f3EsaProbeHistorySoamPmExtAvgP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average P2R (Forward) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 20 }

f3EsaProbeHistorySoamPmExtNumP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of P2R (Forward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 21 }

f3EsaProbeHistorySoamPmExtSumP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of P2R (Forward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 22 }

f3EsaProbeHistorySoamPmExtSumOfSqP2RFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of P2R (Forward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 23 }

f3EsaProbeHistorySoamPmExtMaxR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum R2P (Backward) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 24 }

f3EsaProbeHistorySoamPmExtAvgR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average R2P (Backward) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 25 }

f3EsaProbeHistorySoamPmExtNumR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of R2P (Backward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 26 }

f3EsaProbeHistorySoamPmExtSumR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of R2P (Backward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 27 }

f3EsaProbeHistorySoamPmExtSumOfSqR2PFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of R2P (Backward) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 28 }

f3EsaProbeHistorySoamPmExtMaxRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum round-trip (two-way) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 29 }

f3EsaProbeHistorySoamPmExtAvgRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average round-trip (two-way) FDR in a measurement interval."
    ::= { f3EsaProbeHistorySoamPmExtEntry 30 }

f3EsaProbeHistorySoamPmExtNumRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of round-trip (two-way) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 31 }

f3EsaProbeHistorySoamPmExtSumRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of round-trip (two-way) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 32 }

f3EsaProbeHistorySoamPmExtSumOfSqRTFDR OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sum of squares of round-trip (two-way) FDR samples."
    ::= { f3EsaProbeHistorySoamPmExtEntry 33 }

f3EsaProbeHistorySoamPmExtP2rAvailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Probe to reflector available delta_ts which begins to increment when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 34 }

f3EsaProbeHistorySoamPmExtR2pAvailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflector to probe available delta_ts which begins to increment when FLR is less than or equal to availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 35 }

f3EsaProbeHistorySoamPmExtP2rUnavailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Probe to reflector unavailable delta_ts which begins to increment when FLR is greater than availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 36 }

f3EsaProbeHistorySoamPmExtR2pUnavailableDeltaTs OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflector to probe unavailable delta_ts which begins to increment when FLR is greater than availability threshold for 'n' consecutive delta_ts."
    ::= { f3EsaProbeHistorySoamPmExtEntry 37 }

f3EsaProbeHistorySoamPmExtElapsedTime OBJECT-TYPE
    SYNTAX      PerfCounter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The elapsed time (monotonic time) for the interval which could be less or more than measurement interval based on which the Time of Day was moved forward or backwards respectively (miliseconds)."
    ::= { f3EsaProbeHistorySoamPmExtEntry 38 }


--
-- Notifications, i.e., traps.
--
--
-- Operate Loopback event
--
cmOperateLoopbackTrap NOTIFICATION-TYPE 
    STATUS  current
    DESCRIPTION
       "This is the Operate Loopback Notification sent by the agent. 
         The actual attribute value is sent by the agent in the form of
         a varbind list, as additional objects, as per SMIv2 (RFC2578, Section 8.1)."
  ::= { cmServAssuranceNotifications 1 }

--
-- Release Loopback event
--
cmReleaseLoopbackTrap NOTIFICATION-TYPE 
    STATUS  current
    DESCRIPTION
       "This is the Release Loopback Notification sent by the agent. 
         The actual attribute value is sent by the agent in the form of
         a varbind list, as additional objects, as per SMIv2 (RFC2578, Section 8.1)."
  ::= { cmServAssuranceNotifications 2 }

--
-- ESA Threshold Crossing Alerts
--
esaProbeThresholdCrossingAlert NOTIFICATION-TYPE
    OBJECTS {
                esaProbeStatsThresholdIndex,
                esaProbeStatsThresholdVariable,
                esaProbeStatsThresholdAbsValueLo,
                esaProbeStatsThresholdAbsValueHi,
                esaProbeStatsThresholdMonValue
            }
    STATUS  current
    DESCRIPTION
            "This trap is sent each time a threshold on a PM condition
             on ESA Probe is crossed."
  ::= { cmServAssuranceNotifications 3 }
--
-- Conformance
--
cmServAssuranceCompliances OBJECT IDENTIFIER ::= {cmServAssuranceConformance 1}
cmServAssuranceGroups      OBJECT IDENTIFIER ::= {cmServAssuranceConformance 2}

cmServAssuranceCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "Describes the requirements for conformance to the CM ServAssurance
             group."
    MODULE  -- this module
        MANDATORY-GROUPS {
              cmServAssuranceObjectGroup, cmServAssuranceNotifGroup,
              cmEcpaGroup, cmEsaGroup, cmServAssuranceGenNotifGroup, 
              cmServAssuranceEsaNotifGroup
        }
    ::= { cmServAssuranceCompliances 1 }

cmServAssuranceObjectGroup OBJECT-GROUP
    OBJECTS {
        ecpaControlIndex, ecpaControlSourcePort, ecpaControlTestType,
        ecpaControlNumFrames, ecpaControlDuration, 
        ecpaControlInjectorDirection, ecpaControlMonitorDirection,
        ecpaControlStream1, ecpaControlStream2, ecpaControlStream3,
        ecpaControlAction, ecpaControlTestStatus,
    
        ecpaConfigStreamIndex, ecpaConfigStreamName, ecpaConfigStreamFrameSize,
        ecpaConfigStreamRate, ecpaConfigStreamPayloadType,
        ecpaConfigStreamSignature, ecpaConfigStreamDestinationMAC,
        ecpaConfigStreamSourceMAC, ecpaConfigStreamOuterVlanEnabled,
        ecpaConfigStreamOuterVlanId, ecpaConfigStreamOuterVlanPrio,
        ecpaConfigStreamOuterVlanEtherType, ecpaConfigStreamInnerVlanEnabled,
        ecpaConfigStreamInnerVlanId, ecpaConfigStreamInnerVlanPrio,
        ecpaConfigStreamInnerVlanEtherType, ecpaConfigStreamIpVersion,
        ecpaConfigStreamIpV4Address, ecpaConfigStreamIpV6Address,
        ecpaConfigStreamPrioMapMode, ecpaConfigStreamPrioVal,
        ecpaConfigStreamInnerVlan2Enabled, ecpaConfigStreamInnerVlan2Id,
        ecpaConfigStreamInnerVlan2Prio, ecpaConfigStreamInnerVlan2EtherType,
        ecpaConfigStreamDestIpV4Address, ecpaConfigStreamDestIpV6Address,
        ecpaConfigStreamUsePortSourceMAC, ecpaConfigStreamRateHi,
    
        ecpaTestStreamIndex, ecpaTestStreamSourcePort,
        ecpaTestStreamName, ecpaTestStreamFrameSize, ecpaTestStreamRate,
        ecpaTestStreamPayloadType, ecpaTestStreamSignature,
        ecpaTestStreamDestinationMAC, ecpaTestStreamSourceMAC,
        ecpaTestStreamOuterVlanEnabled, ecpaTestStreamOuterVlanId,
        ecpaTestStreamOuterVlanPrio, ecpaTestStreamOuterVlanEtherType,
        ecpaTestStreamInnerVlanEnabled, ecpaTestStreamInnerVlanId,
        ecpaTestStreamInnerVlanPrio, ecpaTestStreamInnerVlanEtherType,
        ecpaTestStreamIpVersion, ecpaTestStreamIpV4Address,
        ecpaTestStreamIpV6Address, ecpaTestStreamPrioMapMode,
        ecpaTestStreamPrioVal, ecpaTestStreamMonStartTime,
        ecpaTestStreamMonEndTime, ecpaTestStreamMonElapsedTime,
        ecpaTestStreamMonTxFrames, ecpaTestStreamMonRxFrames,
        ecpaTestStreamMonRxPercentSuccess, ecpaTestStreamMonRxOutOfSeqErrs,
        ecpaTestStreamMonRxSeqGaps, ecpaTestStreamMonRxNonEcpaFrames,
        ecpaTestStreamMonRxMinDelay, ecpaTestStreamMonRxMaxDelay,
        ecpaTestStreamMonRxAvgDelay, ecpaTestStreamMonRx1stFrameSize,
        ecpaTestStreamMonRx1stFrame1Octets, ecpaTestStreamMonRx1stFrame2Octets,
        ecpaTestStreamMonRx1stFrame3Octets, ecpaTestStreamMonRx1stFrame4Octets,
        ecpaTestStreamMonRx1stFrame5Octets, ecpaTestStreamMonRx1stFrame6Octets,
        ecpaTestStreamMonRx1stFrame7Octets, ecpaTestStreamMonRx1stFrame8Octets,
        ecpaTestStreamMonRx1stFrame9Octets, ecpaTestStreamMonRx1stFrame10Octets,
        ecpaTestStreamMonRxBitRate, ecpaTestStreamInnerVlan2Enabled,
        ecpaTestStreamInnerVlan2Id, ecpaTestStreamInnerVlan2Prio,
        ecpaTestStreamInnerVlan2EtherType, ecpaTestStreamDestIpV4Address,
        ecpaTestStreamDestIpV6Address, ecpaTestStreamConfigChanged, ecpaTestStreamRateHi,

        esaProbeIndex, esaProbeName, esaProbeSourcePort, 
        esaProbeAssocSchedGroup, esaProbeDirection,
        esaProbeProtocol, esaProbeSrcIpAddress, esaProbeSrcSubnetMask,
        esaProbeDestIpAddress, esaProbeSrcMep, esaProbeDestMepType,
        esaProbeDestMepMacAddr, esaProbeDestMepId,
        esaProbeVlanTagEnabled, esaProbeVlanTagEtherType, esaProbeVlanId, esaProbeVlanPrio,
        esaProbeInnerVlanTagEnabled, esaProbeInnerVlanTagEtherType, 
        esaProbeInnerVlanId, esaProbeInnerVlanPrio,
        esaProbeIpPrioMapMode, esaProbeIpPriority,
        esaProbePktsPerSample, esaProbePktSize, esaProbeInterPktGap,
        esaProbePktDeadInterval, esaProbeResponseTimeout,
        esaProbeY1731DmmPktSize, esaProbeY1731LmmInterval,
        esaProbeY1731DmmInterval,
        esaProbeHistoryBins, esaProbeHistoryInterval,
        esaProbeDistHistoryBins, esaProbeDistHistoryInterval,
        esaProbeCreationTime, esaProbeStorageType,
        esaProbeRowStatus,
        esaProbeInner2VlanTagEnabled, esaProbeInner2VlanTagEtherType, 
        esaProbeInner2VlanId, esaProbeInner2VlanPrio,
        esaProbeAdminState, esaProbeOperationalState, esaProbeSecondaryState,
        
        esaProbeScheduleGroupIndex, esaProbeScheduleGroupDescr,
        esaProbeScheduleGroupProbeList,
        esaProbeScheduleGroupType, esaProbeScheduleGroupStartTime,
        esaProbeScheduleGroupDuration, esaProbeScheduleGroupInterval,
        esaProbeScheduleGroupAction, esaProbeScheduleGroupStatus,
        esaProbeScheduleGroupStorageType, esaProbeScheduleGroupRowStatus,
        esaProbeScheduleGroupActionProbeList,
        esaReflectorIndex, esaReflectorName, esaReflectorIpAddress,
        esaReflectorSubnetMask, esaReflectorSourcePort, 
        esaReflectorIpPrioMapMode,  esaReflectorIpPriority, esaReflectorAction,
        esaReflectorSuspended, esaReflectorCreationTime, 
        esaReflectorStorageType,
        esaReflectorRowStatus,esaReflectorDirection,
        esaReflectorAdminState, esaReflectorOperationalState, esaReflectorSecondaryState,

        esaProbeStatsDestinationIndex, esaProbeStatsCOSIndex,
        esaProbeStatsIndex, esaProbeStatsIntervalType,
        esaProbeStatsCOS, esaProbeStatsValid,
        esaProbeStatsAction, esaProbeStatsP2RPkts,
        esaProbeStatsP2RErredPkts, esaProbeStatsP2RSyncErrs,
        esaProbeStatsP2RLostPkts, esaProbeStatsR2PPkts,
        esaProbeStatsR2PErredPkts, esaProbeStatsR2PSyncErrs,
        esaProbeStatsR2PLostPkts, esaProbeStatsLostPkts,
        esaProbeStatsSeqGaps, esaProbeStatsOutOfSeqErrs,
        esaProbeStatsMinRoundTripDelay, esaProbeStatsMaxRoundTripDelay,
        esaProbeStatsAvgRoundTripDelay, esaProbeStatsSumRoundTripDelay,
        esaProbeStatsSumOfSqRoundTripDelay, esaProbeStatsMinOnewayP2RDelay,
        esaProbeStatsMaxOnewayP2RDelay, esaProbeStatsAvgOnewayP2RDelay,
        esaProbeStatsSumOnewayP2RDelay, esaProbeStatsSumOfSqOnewayP2RDelay,
        esaProbeStatsMinOnewayR2PDelay, esaProbeStatsMaxOnewayR2PDelay,
        esaProbeStatsAvgOnewayR2PDelay, esaProbeStatsSumOnewayR2PDelay,
        esaProbeStatsSumOfSqOnewayR2PDelay, esaProbeStatsMinPosP2RJitter,
        esaProbeStatsMaxPosP2RJitter, esaProbeStatsNumPosP2RJitter,
        esaProbeStatsSumPosP2RJitter, esaProbeStatsSumOfSqPosP2RJitter,
        esaProbeStatsMinNegP2RJitter, esaProbeStatsMaxNegP2RJitter,
        esaProbeStatsNumNegP2RJitter, esaProbeStatsSumNegP2RJitter,
        esaProbeStatsSumOfSqNegP2RJitter, esaProbeStatsMinPosR2PJitter,
        esaProbeStatsMaxPosR2PJitter, esaProbeStatsNumPosR2PJitter,
        esaProbeStatsSumPosR2PJitter, esaProbeStatsSumOfSqPosR2PJitter,
        esaProbeStatsMinNegR2PJitter, esaProbeStatsMaxNegR2PJitter,
        esaProbeStatsNumNegR2PJitter, esaProbeStatsSumNegR2PJitter,
        esaProbeStatsSumOfSqNegR2PJitter, esaProbeStatsY1731P2RNegLossOccurrences,
        esaProbeStatsY1731R2PNegLossOccurrences, esaProbeStatsY1731RxLmSamples,
        esaProbeStatsY1731RxDmSamples, esaProbeStatsY1731P2RFrames,
        esaProbeStatsY1731R2PFrames, esaProbeStatsAvgAbsP2RJitter,
        esaProbeStatsAvgAbsR2PJitter, esaProbeStatsMaxAbsP2RJitter,
        esaProbeStatsMaxAbsR2PJitter, esaProbeStatsMinAbsP2RJitter,
        esaProbeStatsMinAbsR2PJitter, esaProbeStatsNumAbsP2RJitter,
        esaProbeStatsNumAbsR2PJitter, esaProbeStatsSumAbsP2RJitter,
        esaProbeStatsSumAbsR2PJitter, esaProbeStatsSumOfSqAbsP2RJitter,
        esaProbeStatsSumOfSqAbsR2PJitter,

        esaProbeHistoryDestinationIndex, esaProbeHistoryCOSIndex, esaProbeHistoryIndex,
        esaProbeHistoryTime, esaProbeHistoryValid, esaProbeHistoryAction, 
        esaProbeHistoryCOS, esaProbeHistoryP2RPkts, esaProbeHistoryP2RErredPkts, 
        esaProbeHistoryP2RSyncErrs, esaProbeHistoryP2RLostPkts, 
        esaProbeHistoryR2PPkts, esaProbeHistoryR2PErredPkts,
        esaProbeHistoryR2PSyncErrs, esaProbeHistoryR2PLostPkts,
        esaProbeHistoryLostPkts, esaProbeHistorySeqGaps, esaProbeHistoryOutOfSeqErrs,
        esaProbeHistoryMinRoundTripDelay, esaProbeHistoryMaxRoundTripDelay,
        esaProbeHistoryAvgRoundTripDelay, esaProbeHistorySumRoundTripDelay,
        esaProbeHistorySumOfSqRoundTripDelay, esaProbeHistoryMinOnewayP2RDelay,
        esaProbeHistoryMaxOnewayP2RDelay, esaProbeHistoryAvgOnewayP2RDelay,
        esaProbeHistorySumOnewayP2RDelay, esaProbeHistorySumOfSqOnewayP2RDelay,
        esaProbeHistoryMinOnewayR2PDelay, esaProbeHistoryMaxOnewayR2PDelay,
        esaProbeHistoryAvgOnewayR2PDelay, esaProbeHistorySumOnewayR2PDelay,
        esaProbeHistorySumOfSqOnewayR2PDelay, esaProbeHistoryMinPosP2RJitter,
        esaProbeHistoryMaxPosP2RJitter, esaProbeHistoryNumPosP2RJitter,
        esaProbeHistorySumPosP2RJitter, esaProbeHistorySumOfSqPosP2RJitter,
        esaProbeHistoryMinNegP2RJitter, esaProbeHistoryMaxNegP2RJitter,
        esaProbeHistoryNumNegP2RJitter, esaProbeHistorySumNegP2RJitter,
        esaProbeHistorySumOfSqNegP2RJitter, esaProbeHistoryMinPosR2PJitter,
        esaProbeHistoryMaxPosR2PJitter, esaProbeHistoryNumPosR2PJitter,
        esaProbeHistorySumPosR2PJitter, esaProbeHistorySumOfSqPosR2PJitter,
        esaProbeHistoryMinNegR2PJitter, esaProbeHistoryMaxNegR2PJitter,
        esaProbeHistoryNumNegR2PJitter, esaProbeHistorySumNegR2PJitter,
        esaProbeHistorySumOfSqNegR2PJitter, esaProbeHistoryY1731P2RNegLossOccurrences,
        esaProbeHistoryY1731R2PNegLossOccurrences, esaProbeHistoryY1731RxLmSamples,
        esaProbeHistoryY1731RxDmSamples, esaProbeHistoryY1731P2RFrames,
        esaProbeHistoryY1731R2PFrames, esaProbeHistoryAvgAbsP2RJitter,
        esaProbeHistoryAvgAbsR2PJitter, esaProbeHistoryMaxAbsP2RJitter,
        esaProbeHistoryMaxAbsR2PJitter, esaProbeHistoryMinAbsP2RJitter,
        esaProbeHistoryMinAbsR2PJitter, esaProbeHistoryNumAbsP2RJitter,
        esaProbeHistoryNumAbsR2PJitter, esaProbeHistorySumAbsP2RJitter,
        esaProbeHistorySumAbsR2PJitter, esaProbeHistorySumOfSqAbsP2RJitter,
        esaProbeHistorySumOfSqAbsR2PJitter,

        esaProbeDistStatsConfigIndex, esaProbeDistStatsConfigType,
        esaProbeDistStatsConfigMinVal, esaProbeDistStatsConfigMaxVal,
        esaProbeDistStatsConfigNumBins, 

        esaProbeDistStatsDestinationIndex, esaProbeDistStatsCOSIndex,
        esaProbeDistStatsCOS, esaProbeDistStatsNumBins, esaProbeDistStatsLTMin, 
        esaProbeDistStatsGTMax,

        esaProbeDistStatsBinIndex, esaProbeDistStatsBinLower, 
        esaProbeDistStatsBinUpper,
        esaProbeDistStatsBinNumSamples,

        esaProbeDistHistoryDestinationIndex, esaProbeDistHistoryCOSIndex,
        esaProbeDistHistoryCOS, esaProbeDistHistoryIndex, esaProbeDistHistoryTime, 
        esaProbeDistHistoryAction, esaProbeDistHistoryNumBins,
        esaProbeDistHistoryLTMin, esaProbeDistHistoryGTMax,

        esaProbeDistHistoryBinIndex, esaProbeDistHistoryBinLower, 
        esaProbeDistHistoryBinUpper,
        esaProbeDistHistoryBinNumSamples,

        esaProbeStatsThresholdIndex, esaProbeStatsThresholdVariable,
        esaProbeStatsThresholdAbsValueLo, esaProbeStatsThresholdAbsValueHi,
        esaProbeStatsThresholdMonValue,

        esaProbeCOSConfigIndex, esaProbeCOSConfigType, esaProbeCOSConfigInterval,
        esaProbeCOSConfigPktSize, esaProbeCOSConfigStorageType, esaProbeCOSConfigRowStatus,

        esaProbeDestinationIndex, esaProbeDestinationMepType, esaProbeDestinationMepMacAddr,
        esaProbeDestinationMepId, esaProbeDestinationStorageType, esaProbeDestinationRowStatus
    }
    STATUS  deprecated
    DESCRIPTION
            "***************** THIS OBJECT GROUP IS NOW DEPRECATED ***************
             A collection of objects used to manage the CM ServAssurance group."
    ::= { cmServAssuranceGroups 1 }

cmServAssuranceNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        cmOperateLoopbackTrap, cmReleaseLoopbackTrap, 
        esaProbeThresholdCrossingAlert
    }
    STATUS  deprecated
    DESCRIPTION
            "***************** THIS OBJECT GROUP IS NOW DEPRECATED ******************
             A collection of notifications used in the CM ServAssurance
             group."
    ::= { cmServAssuranceGroups 2 }

cmEcpaGroup OBJECT-GROUP
    OBJECTS {
        ecpaControlIndex, ecpaControlSourcePort, ecpaControlTestType,
        ecpaControlNumFrames, ecpaControlDuration, 
        ecpaControlInjectorDirection, ecpaControlMonitorDirection,
        ecpaControlStream1, ecpaControlStream2, ecpaControlStream3,
        ecpaControlAction, ecpaControlTestStatus, ecpaControlStorageType,
        ecpaControlRowStatus,
    
        ecpaConfigStreamIndex, ecpaConfigStreamName, ecpaConfigStreamFrameSize,
        ecpaConfigStreamRate, ecpaConfigStreamPayloadType,
        ecpaConfigStreamSignature, ecpaConfigStreamDestinationMAC,
        ecpaConfigStreamSourceMAC, ecpaConfigStreamOuterVlanEnabled,
        ecpaConfigStreamOuterVlanId, ecpaConfigStreamOuterVlanPrio,
        ecpaConfigStreamOuterVlanEtherType, ecpaConfigStreamInnerVlanEnabled,
        ecpaConfigStreamInnerVlanId, ecpaConfigStreamInnerVlanPrio,
        ecpaConfigStreamInnerVlanEtherType, ecpaConfigStreamIpVersion,
        ecpaConfigStreamIpV4Address, ecpaConfigStreamIpV6Address,
        ecpaConfigStreamPrioMapMode, ecpaConfigStreamPrioVal,
        ecpaConfigStreamInnerVlan2Enabled, ecpaConfigStreamInnerVlan2Id,
        ecpaConfigStreamInnerVlan2Prio, ecpaConfigStreamInnerVlan2EtherType,
        ecpaConfigStreamDestIpV4Address, ecpaConfigStreamDestIpV6Address,
        ecpaConfigStreamUsePortSourceMAC, ecpaConfigStreamRateHi,
    
        ecpaTestStreamIndex, ecpaTestStreamSourcePort,
        ecpaTestStreamName, ecpaTestStreamFrameSize, ecpaTestStreamRate,
        ecpaTestStreamPayloadType, ecpaTestStreamSignature,
        ecpaTestStreamDestinationMAC, ecpaTestStreamSourceMAC,
        ecpaTestStreamOuterVlanEnabled, ecpaTestStreamOuterVlanId,
        ecpaTestStreamOuterVlanPrio, ecpaTestStreamOuterVlanEtherType,
        ecpaTestStreamInnerVlanEnabled, ecpaTestStreamInnerVlanId,
        ecpaTestStreamInnerVlanPrio, ecpaTestStreamInnerVlanEtherType,
        ecpaTestStreamIpVersion, ecpaTestStreamIpV4Address,
        ecpaTestStreamIpV6Address, ecpaTestStreamPrioMapMode,
        ecpaTestStreamPrioVal, ecpaTestStreamMonStartTime,
        ecpaTestStreamMonEndTime, ecpaTestStreamMonElapsedTime,
        ecpaTestStreamMonTxFrames, ecpaTestStreamMonRxFrames,
        ecpaTestStreamMonRxPercentSuccess, ecpaTestStreamMonRxOutOfSeqErrs,
        ecpaTestStreamMonRxSeqGaps, ecpaTestStreamMonRxNonEcpaFrames,
        ecpaTestStreamMonRxMinDelay, ecpaTestStreamMonRxMaxDelay,
        ecpaTestStreamMonRxAvgDelay, ecpaTestStreamMonRx1stFrameSize,
        ecpaTestStreamMonRx1stFrame1Octets, ecpaTestStreamMonRx1stFrame2Octets,
        ecpaTestStreamMonRx1stFrame3Octets, ecpaTestStreamMonRx1stFrame4Octets,
        ecpaTestStreamMonRx1stFrame5Octets, ecpaTestStreamMonRx1stFrame6Octets,
        ecpaTestStreamMonRx1stFrame7Octets, ecpaTestStreamMonRx1stFrame8Octets,
        ecpaTestStreamMonRx1stFrame9Octets, ecpaTestStreamMonRx1stFrame10Octets,
        ecpaTestStreamMonRxBitRate, ecpaTestStreamInnerVlan2Enabled,
        ecpaTestStreamInnerVlan2Id, ecpaTestStreamInnerVlan2Prio,
        ecpaTestStreamInnerVlan2EtherType, ecpaTestStreamDestIpV4Address,
        ecpaTestStreamDestIpV6Address, ecpaTestStreamConfigChanged, 
        ecpaTestStreamRateHi
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CM ServAssurance
             ECPA functionality."
    ::= { cmServAssuranceGroups 3 }

cmEsaGroup OBJECT-GROUP
    OBJECTS {
        esaProbeIndex, esaProbeName, esaProbeSourcePort, 
        esaProbeAssocSchedGroup, esaProbeDirection,
        esaProbeProtocol, esaProbeSrcIpAddress, esaProbeSrcSubnetMask,
        esaProbeDestIpAddress, esaProbeSrcMep, esaProbeDestMepType,
        esaProbeDestMepMacAddr, esaProbeDestMepId,
        esaProbeVlanTagEnabled, esaProbeVlanTagEtherType, esaProbeVlanId, esaProbeVlanPrio,
        esaProbeInnerVlanTagEnabled, esaProbeInnerVlanTagEtherType, 
        esaProbeInnerVlanId, esaProbeInnerVlanPrio,
        esaProbeIpPrioMapMode, esaProbeIpPriority,
        esaProbePktsPerSample, esaProbePktSize, esaProbeInterPktGap,
        esaProbePktDeadInterval, esaProbeResponseTimeout,
        esaProbeY1731DmmPktSize, esaProbeY1731LmmInterval,
        esaProbeY1731DmmInterval,
        esaProbeHistoryBins, esaProbeHistoryInterval,
        esaProbeDistHistoryBins, esaProbeDistHistoryInterval,
        esaProbeCreationTime, esaProbeStorageType,
        esaProbeRowStatus, esaProbeMultiCOSEnabled, 
        esaProbeSLAMonitorType, esaProbeCOSType, 
        esaProbeSLMMulticastMACEnabled,
        esaProbeSOAMInterval, esaProbeSOAMPktSize,
        esaProbeAdminState, esaProbeOperationalState,
        esaProbeSecondaryState, esaProbeAlias,
        
        esaProbeScheduleGroupIndex, esaProbeScheduleGroupDescr,
        esaProbeScheduleGroupProbeList,
        esaProbeScheduleGroupType, esaProbeScheduleGroupStartTime,
        esaProbeScheduleGroupDuration, esaProbeScheduleGroupInterval,
        esaProbeScheduleGroupAction, esaProbeScheduleGroupStatus,
        esaProbeScheduleGroupStorageType, esaProbeScheduleGroupRowStatus,
        esaReflectorIndex, esaReflectorName, esaReflectorIpAddress,
        esaReflectorSubnetMask, esaReflectorSourcePort, 
        esaReflectorIpPrioMapMode,  esaReflectorIpPriority, esaReflectorAction,
        esaReflectorSuspended, esaReflectorCreationTime, 
        esaReflectorStorageType, esaReflectorRowStatus,
        esaReflectorDirection, esaReflectorAdminState,
        esaReflectorOperationalState, esaReflectorSecondaryState,
        esaReflectorAlias,

        esaProbeStatsDestinationIndex, esaProbeStatsCOSIndex,
        esaProbeStatsIndex, esaProbeStatsIntervalType,
        esaProbeStatsCOS, esaProbeStatsValid,
        esaProbeStatsAction, esaProbeStatsP2RPkts,
        esaProbeStatsP2RErredPkts, esaProbeStatsP2RSyncErrs,
        esaProbeStatsP2RLostPkts, esaProbeStatsR2PPkts,
        esaProbeStatsR2PErredPkts, esaProbeStatsR2PSyncErrs,
        esaProbeStatsR2PLostPkts, esaProbeStatsLostPkts,
        esaProbeStatsSeqGaps, esaProbeStatsOutOfSeqErrs,
        esaProbeStatsMinRoundTripDelay, esaProbeStatsMaxRoundTripDelay,
        esaProbeStatsAvgRoundTripDelay, esaProbeStatsSumRoundTripDelay,
        esaProbeStatsSumOfSqRoundTripDelay, esaProbeStatsMinOnewayP2RDelay,
        esaProbeStatsMaxOnewayP2RDelay, esaProbeStatsAvgOnewayP2RDelay,
        esaProbeStatsSumOnewayP2RDelay, esaProbeStatsSumOfSqOnewayP2RDelay,
        esaProbeStatsMinOnewayR2PDelay, esaProbeStatsMaxOnewayR2PDelay,
        esaProbeStatsAvgOnewayR2PDelay, esaProbeStatsSumOnewayR2PDelay,
        esaProbeStatsSumOfSqOnewayR2PDelay, esaProbeStatsMinPosP2RJitter,
        esaProbeStatsMaxPosP2RJitter, esaProbeStatsNumPosP2RJitter,
        esaProbeStatsSumPosP2RJitter, esaProbeStatsSumOfSqPosP2RJitter,
        esaProbeStatsMinNegP2RJitter, esaProbeStatsMaxNegP2RJitter,
        esaProbeStatsNumNegP2RJitter, esaProbeStatsSumNegP2RJitter,
        esaProbeStatsSumOfSqNegP2RJitter, esaProbeStatsMinPosR2PJitter,
        esaProbeStatsMaxPosR2PJitter, esaProbeStatsNumPosR2PJitter,
        esaProbeStatsSumPosR2PJitter, esaProbeStatsSumOfSqPosR2PJitter,
        esaProbeStatsMinNegR2PJitter, esaProbeStatsMaxNegR2PJitter,
        esaProbeStatsNumNegR2PJitter, esaProbeStatsSumNegR2PJitter,
        esaProbeStatsSumOfSqNegR2PJitter, esaProbeStatsY1731P2RNegLossOccurrences,
        esaProbeStatsY1731R2PNegLossOccurrences, esaProbeStatsY1731RxLmSamples,
        esaProbeStatsY1731RxDmSamples, esaProbeStatsY1731P2RFrames,
        esaProbeStatsY1731R2PFrames, esaProbeStatsAvgAbsP2RJitter,
        esaProbeStatsAvgAbsR2PJitter, esaProbeStatsMaxAbsP2RJitter,
        esaProbeStatsMaxAbsR2PJitter, esaProbeStatsMinAbsP2RJitter,
        esaProbeStatsMinAbsR2PJitter, esaProbeStatsNumAbsP2RJitter,
        esaProbeStatsNumAbsR2PJitter, esaProbeStatsSumAbsP2RJitter,
        esaProbeStatsSumAbsR2PJitter, esaProbeStatsSumOfSqAbsP2RJitter,
        esaProbeStatsSumOfSqAbsR2PJitter,

        esaProbeHistoryDestinationIndex, esaProbeHistoryCOSIndex, esaProbeHistoryIndex,
        esaProbeHistoryTime, esaProbeHistoryValid, esaProbeHistoryAction, 
        esaProbeHistoryCOS, esaProbeHistoryP2RPkts, esaProbeHistoryP2RErredPkts, 
        esaProbeHistoryP2RSyncErrs, esaProbeHistoryP2RLostPkts, 
        esaProbeHistoryR2PPkts, esaProbeHistoryR2PErredPkts,
        esaProbeHistoryR2PSyncErrs, esaProbeHistoryR2PLostPkts,
        esaProbeHistoryLostPkts, esaProbeHistorySeqGaps, esaProbeHistoryOutOfSeqErrs,
        esaProbeHistoryMinRoundTripDelay, esaProbeHistoryMaxRoundTripDelay,
        esaProbeHistoryAvgRoundTripDelay, esaProbeHistorySumRoundTripDelay,
        esaProbeHistorySumOfSqRoundTripDelay, esaProbeHistoryMinOnewayP2RDelay,
        esaProbeHistoryMaxOnewayP2RDelay, esaProbeHistoryAvgOnewayP2RDelay,
        esaProbeHistorySumOnewayP2RDelay, esaProbeHistorySumOfSqOnewayP2RDelay,
        esaProbeHistoryMinOnewayR2PDelay, esaProbeHistoryMaxOnewayR2PDelay,
        esaProbeHistoryAvgOnewayR2PDelay, esaProbeHistorySumOnewayR2PDelay,
        esaProbeHistorySumOfSqOnewayR2PDelay, esaProbeHistoryMinPosP2RJitter,
        esaProbeHistoryMaxPosP2RJitter, esaProbeHistoryNumPosP2RJitter,
        esaProbeHistorySumPosP2RJitter, esaProbeHistorySumOfSqPosP2RJitter,
        esaProbeHistoryMinNegP2RJitter, esaProbeHistoryMaxNegP2RJitter,
        esaProbeHistoryNumNegP2RJitter, esaProbeHistorySumNegP2RJitter,
        esaProbeHistorySumOfSqNegP2RJitter, esaProbeHistoryMinPosR2PJitter,
        esaProbeHistoryMaxPosR2PJitter, esaProbeHistoryNumPosR2PJitter,
        esaProbeHistorySumPosR2PJitter, esaProbeHistorySumOfSqPosR2PJitter,
        esaProbeHistoryMinNegR2PJitter, esaProbeHistoryMaxNegR2PJitter,
        esaProbeHistoryNumNegR2PJitter, esaProbeHistorySumNegR2PJitter,
        esaProbeHistorySumOfSqNegR2PJitter, esaProbeHistoryY1731P2RNegLossOccurrences,
        esaProbeHistoryY1731R2PNegLossOccurrences, esaProbeHistoryY1731RxLmSamples,
        esaProbeHistoryY1731RxDmSamples, esaProbeHistoryY1731P2RFrames,
        esaProbeHistoryY1731R2PFrames, esaProbeHistoryAvgAbsP2RJitter,
        esaProbeHistoryAvgAbsR2PJitter, esaProbeHistoryMaxAbsP2RJitter,
        esaProbeHistoryMaxAbsR2PJitter, esaProbeHistoryMinAbsP2RJitter,
        esaProbeHistoryMinAbsR2PJitter, esaProbeHistoryNumAbsP2RJitter,
        esaProbeHistoryNumAbsR2PJitter, esaProbeHistorySumAbsP2RJitter,
        esaProbeHistorySumAbsR2PJitter, esaProbeHistorySumOfSqAbsP2RJitter,
        esaProbeHistorySumOfSqAbsR2PJitter,

        esaProbeDistStatsConfigIndex, esaProbeDistStatsConfigType,
        esaProbeDistStatsConfigMinVal, esaProbeDistStatsConfigMaxVal,
        esaProbeDistStatsConfigNumBins, 

        esaProbeDistStatsDestinationIndex, esaProbeDistStatsCOSIndex,
        esaProbeDistStatsCOS, esaProbeDistStatsNumBins, esaProbeDistStatsLTMin, 
        esaProbeDistStatsGTMax,

        esaProbeDistStatsBinIndex, esaProbeDistStatsBinLower, 
        esaProbeDistStatsBinUpper,
        esaProbeDistStatsBinNumSamples,

        esaProbeDistHistoryDestinationIndex, esaProbeDistHistoryCOSIndex,
        esaProbeDistHistoryCOS, esaProbeDistHistoryIndex, esaProbeDistHistoryTime, 
        esaProbeDistHistoryAction, esaProbeDistHistoryNumBins,
        esaProbeDistHistoryLTMin, esaProbeDistHistoryGTMax,

        esaProbeDistHistoryBinIndex, esaProbeDistHistoryBinLower, 
        esaProbeDistHistoryBinUpper,
        esaProbeDistHistoryBinNumSamples,

        esaProbeStatsThresholdIndex, esaProbeStatsThresholdVariable,
        esaProbeStatsThresholdAbsValueLo, esaProbeStatsThresholdAbsValueHi,
        esaProbeStatsThresholdMonValue,

        esaProbeCOSConfigIndex, esaProbeCOSConfigType, esaProbeCOSConfigInterval,
        esaProbeCOSConfigPktSize, esaProbeCOSConfigStorageType, esaProbeCOSConfigRowStatus,
        esaProbeCOSConfigslmInterval, esaProbeCOSConfigslmPktSize,
        esaProbeCOSConfigSoamPmExtAvailFlrThreshold,
        esaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus,
        esaProbeCOSConfigSoamPmExtConDeltaTsForAvail,

        esaProbeDestinationIndex,esaProbeDestinationMepType, esaProbeDestinationMepMacAddr,
        esaProbeDestinationMepId,esaProbeDestinationStorageType,esaProbeDestinationRowStatus,
        
        f3EsaProbeCOSConfigSoamPmExtAvailFlrThreshold,
        f3EsaProbeCOSConfigSoamPmExtFlrDeltaTNumLmPdus,
        f3EsaProbeCOSConfigSoamPmExtConDeltaTsForAvail,
    
        f3EsaProbeStatsSoamPmExtMinP2RFlr, f3EsaProbeStatsSoamPmExtMaxP2RFlr, f3EsaProbeStatsSoamPmExtAvgP2RFlr,
        f3EsaProbeStatsSoamPmExtMinR2PFlr, f3EsaProbeStatsSoamPmExtMaxR2PFlr, f3EsaProbeStatsSoamPmExtAvgR2PFlr,
        f3EsaProbeStatsSoamPmExtP2rSeverelyErroredDeltaTs, f3EsaProbeStatsSoamPmExtR2PSeverelyErroredDeltaTs,
        f3EsaProbeStatsSoamPmExtP2rAvailableTime, f3EsaProbeStatsSoamPmExtR2PAvailableTime,
        f3EsaProbeStatsSoamPmExtP2rUnavailableTime, f3EsaProbeStatsSoamPmExtR2PUnavailableTime,
        f3EsaProbeStatsSoamPmExtMinAbsRTJitter, f3EsaProbeStatsSoamPmExtMaxAbsRTJitter, f3EsaProbeStatsSoamPmExtAvgAbsRTJitter,
        f3EsaProbeStatsSoamPmExtNumAbsRTJitter, f3EsaProbeStatsSoamPmExtSumAbsRTJitter, f3EsaProbeStatsSoamPmExtSumOfSqAbsRTJitter,
        f3EsaProbeStatsSoamPmExtMaxP2RFDR, f3EsaProbeStatsSoamPmExtAvgP2RFDR, f3EsaProbeStatsSoamPmExtNumP2RFDR,
        f3EsaProbeStatsSoamPmExtSumP2RFDR, f3EsaProbeStatsSoamPmExtSumOfSqP2RFDR,
        f3EsaProbeStatsSoamPmExtMaxR2PFDR, f3EsaProbeStatsSoamPmExtAvgR2PFDR, f3EsaProbeStatsSoamPmExtNumR2PFDR,
        f3EsaProbeStatsSoamPmExtSumR2PFDR, f3EsaProbeStatsSoamPmExtSumOfSqR2PFDR,
        f3EsaProbeStatsSoamPmExtMaxRTFDR, f3EsaProbeStatsSoamPmExtAvgRTFDR, f3EsaProbeStatsSoamPmExtNumRTFDR,
        f3EsaProbeStatsSoamPmExtSumRTFDR, f3EsaProbeStatsSoamPmExtSumOfSqRTFDR,
        f3EsaProbeStatsSoamPmExtP2rAvailableDeltaTs, f3EsaProbeStatsSoamPmExtR2pAvailableDeltaTs,
        f3EsaProbeStatsSoamPmExtP2rUnavailableDeltaTs, f3EsaProbeStatsSoamPmExtR2pUnavailableDeltaTs,
        f3EsaProbeStatsSoamPmExtElapsedTime,

        f3EsaProbeHistorySoamPmExtMinP2RFlr, f3EsaProbeHistorySoamPmExtMaxP2RFlr, f3EsaProbeHistorySoamPmExtAvgP2RFlr,
        f3EsaProbeHistorySoamPmExtMinR2PFlr, f3EsaProbeHistorySoamPmExtMaxR2PFlr, f3EsaProbeHistorySoamPmExtAvgR2PFlr,
        f3EsaProbeHistorySoamPmExtP2rSeverelyErroredDeltaTs, f3EsaProbeHistorySoamPmExtR2PSeverelyErroredDeltaTs,
        f3EsaProbeHistorySoamPmExtP2rAvailableTime, f3EsaProbeHistorySoamPmExtR2PAvailableTime,
        f3EsaProbeHistorySoamPmExtP2rUnavailableTime, f3EsaProbeHistorySoamPmExtR2PUnavailableTime,
        f3EsaProbeHistorySoamPmExtMinAbsRTJitter, f3EsaProbeHistorySoamPmExtMaxAbsRTJitter, f3EsaProbeHistorySoamPmExtAvgAbsRTJitter,
        f3EsaProbeHistorySoamPmExtNumAbsRTJitter, f3EsaProbeHistorySoamPmExtSumAbsRTJitter, f3EsaProbeHistorySoamPmExtSumOfSqAbsRTJitter,
        f3EsaProbeHistorySoamPmExtMaxP2RFDR, f3EsaProbeHistorySoamPmExtAvgP2RFDR, f3EsaProbeHistorySoamPmExtNumP2RFDR,
        f3EsaProbeHistorySoamPmExtSumP2RFDR, f3EsaProbeHistorySoamPmExtSumOfSqP2RFDR,
        f3EsaProbeHistorySoamPmExtMaxR2PFDR, f3EsaProbeHistorySoamPmExtAvgR2PFDR, f3EsaProbeHistorySoamPmExtNumR2PFDR,
        f3EsaProbeHistorySoamPmExtSumR2PFDR, f3EsaProbeHistorySoamPmExtSumOfSqR2PFDR,
        f3EsaProbeHistorySoamPmExtMaxRTFDR, f3EsaProbeHistorySoamPmExtAvgRTFDR, f3EsaProbeHistorySoamPmExtNumRTFDR,
        f3EsaProbeHistorySoamPmExtSumRTFDR, f3EsaProbeHistorySoamPmExtSumOfSqRTFDR,
        f3EsaProbeHistorySoamPmExtP2rAvailableDeltaTs, f3EsaProbeHistorySoamPmExtR2pAvailableDeltaTs,
        f3EsaProbeHistorySoamPmExtP2rUnavailableDeltaTs, f3EsaProbeHistorySoamPmExtR2pUnavailableDeltaTs,
        f3EsaProbeHistorySoamPmExtElapsedTime
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CM ServAssurance
             ESA functionality."
    ::= { cmServAssuranceGroups 4 }

cmServAssuranceGenNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        cmOperateLoopbackTrap, cmReleaseLoopbackTrap 
    }
    STATUS  current
    DESCRIPTION
            "A collection of notifications used in the generic CM ServAssurance
             functionality."
    ::= { cmServAssuranceGroups 5 }

cmServAssuranceEsaNotifGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        esaProbeThresholdCrossingAlert
    }
    STATUS  current
    DESCRIPTION
            "A collection of notifications used in the CM ServAssurance
             ESA functionality."
    ::= { cmServAssuranceGroups 6 }

cmBertGroup OBJECT-GROUP
    OBJECTS {
        bertControlIndex, bertControlSourceEntity, bertControlTestMode, bertControlDuration,
        bertControlStream, bertControlAction, bertControlTestStatus,
        
        bertConfigStreamIndex, bertConfigStreamName, bertConfigStreamTxPattern,
        bertConfigStreamErrInjectEnabled, bertConfigStreamErrInjectRate, bertConfigStreamErrInjectRateMultiplier,
        bertConfigStreamUserPatternLength, bertConfigStreamUserPattern,
           
        bertTestStreamIndex, bertTestStreamName, bertTestStreamTxPattern, bertTestStreamErrInjectEnabled,
        bertTestStreamErrInjectRate, bertTestStreamErrInjectRateMultiplier, bertTestStreamUserPatternLength,
        bertTestStreamUserPattern, bertTestStreamMonStartTime, bertTestStreamMonEndTime,
        bertTestStreamMonElapsedTime, bertTestStreamMonSyncState, bertTestStreamMonRxPattern,
        bertTestStreamMonSyncCounts, bertTestStreamMonRxBitErrsSinceStart, bertTestStreamMonRxBitsSinceStart,
        bertTestStreamMonRxESsSinceStart, bertTestStreamMonRxErrRateSinceStart,
        bertTestStreamMonRxErrRateMultiplierSinceStart,
        bertTestStreamMonRxBitErrsSinceLastSync, bertTestStreamMonRxBitsSinceLastSync,
        bertTestStreamMonRxESsSinceLastSync, bertTestStreamMonRxErrRateSinceLastSync,
        bertTestStreamMonRxErrRateMultiplierSinceLastSync, bertTestStreamConfigChangedFlag,
        bertTestStreamMonOOSSsSinceStart
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CM ServAssurance
             BERT functionality."
    ::= { cmServAssuranceGroups 7 }

END
