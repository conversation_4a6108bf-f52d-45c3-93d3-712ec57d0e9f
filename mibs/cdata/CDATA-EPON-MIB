--
-- CDATA-EPON-MIB_170929.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Friday, September 29, 2017 at 13:30:00
--

	CDATA-EPON-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			mediaConverter, switch, epon, eoc, gpon			
				FROM CDATA-COMMON-SMI			
			Integer32, Unsigned32, OB<PERSON>ECT-TYP<PERSON>, MODULE-IDENTITY, OBJECT-IDENTITY			
				FROM SNMPv2-SMI			
			RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.34592.1.3.1
		eponMIB MODULE-IDENTITY 
			LAST-UPDATED "201606211415Z"		-- June 21, 2016 at 14:15 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { epon 1 }

		
	
--
-- Textual conventions
--
	
		EponProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (0..16))

		EponAlarmProfileThreshold ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Unsigned32 (0..4294967294)

		EponDbaProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Dba profile index,
				1-128:user dba profile
				65535:default dba profile"
			SYNTAX Integer32 (0..128)

		EponDbaProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING

		EponLinePorfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (0..512)

		EponLineProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..32))

		EponLineProfileLlId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

		EponSrvProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (0..512)

		EponSrvProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		EponTrafficProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..256)

		EponTrafficProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		EponSwitch ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}

		EponVlanId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..4094)

		EponVlanPriority ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (0..7)

		EponOltPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..16)

		EponOnuId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..128)

		EponOnuEthPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

		EponOnuCatvPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..24)

		EponMacAddress ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (6))

	
--
-- Node definitions
--
	
		-- *******.4.1.34592.*******
		eponObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponMIB 1 }

		
		-- *******.4.1.34592.*******.1
		eponProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponObjects 1 }

		
		-- *******.4.1.34592.*******.1.1
		eponDbaProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 1 }

		
		-- *******.4.1.34592.*******.1.1.1
		eponDbaProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponDbaProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponDbaProfileInfoEntry OBJECT-TYPE
			SYNTAX EponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponDbaProfileId }
			::= { eponDbaProfileInfoTable 1 }

		
		EponDbaProfileInfoEntry ::=
			SEQUENCE { 
				eponDbaProfileId
					EponDbaProfileId,
				eponDbaProfileName
					EponDbaProfileName,
				eponDbaProfileType
					INTEGER,
				eponDbaProfileFixRate
					Integer32,
				eponDbaProfileAssureRate
					Integer32,
				eponDbaProfileMaxRate
					Integer32,
				eponDbaProfileBindNum
					Integer32,
				eponDbaProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponDbaProfileId OBJECT-TYPE
			SYNTAX EponDbaProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Dba profile index,
				1-128:user dba profile
				65535:default dba profile"
			::= { eponDbaProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponDbaProfileName OBJECT-TYPE
			SYNTAX EponDbaProfileName (SIZE (1..16))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponDbaProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponDbaProfileType OBJECT-TYPE
			SYNTAX INTEGER
				{
				fix(1),
				assure(2),
				assureAndMax(3),
				max(4),
				fixAndAssureAndMax(5)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The DBA type index."
			::= { eponDbaProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponDbaProfileFixRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..1000000)
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Fixed bandwidth of the profile."
			::= { eponDbaProfileInfoEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponDbaProfileAssureRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 1..1000000)
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Assured bandwidth of the profile."
			::= { eponDbaProfileInfoEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponDbaProfileMaxRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 512..1000000)
			UNITS "kbps"
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Max bandwidth of the profile."
			::= { eponDbaProfileInfoEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponDbaProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponDbaProfileInfoEntry 7 }

		
		-- *******.4.1.34592.*******.*******.8
		eponDbaProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponDbaProfileInfoEntry 8 }

		
		-- *******.4.1.34592.*******.1.2
		eponLineProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 2 }

		
		-- *******.4.1.34592.*******.1.2.1
		eponLineProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponLineProfileInfoEntry OBJECT-TYPE
			SYNTAX EponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponLineProfileId }
			::= { eponLineProfileInfoTable 1 }

		
		EponLineProfileInfoEntry ::=
			SEQUENCE { 
				eponLineProfileId
					EponLinePorfileId,
				eponLineProfileName
					EponLineProfileName,
				eponLineProfileUpstreamFECMode
					EponSwitch,
				eponLineProfileLlidNum
					Integer32,
				eponLineProfileBindNum
					Integer32,
				eponLineProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.1.*******
		eponLineProfileId OBJECT-TYPE
			SYNTAX EponLinePorfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Line profile index,
				1-512:user line profile
				65535:default line profile, it is reserve."
			::= { eponLineProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.1.*******
		eponLineProfileName OBJECT-TYPE
			SYNTAX EponLineProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.1.*******
		eponLineProfileUpstreamFECMode OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.1.*******
		eponLineProfileLlidNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileInfoEntry 4 }

		
		-- *******.4.1.34592.*******.*******.7
		eponLineProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileInfoEntry 7 }

		
		-- *******.4.1.34592.*******.*******.8
		eponLineProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileInfoEntry 8 }

		
		-- *******.4.1.34592.*******.1.2.3
		eponLineProfileLlidTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponLineProfileLlidEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileObjects 3 }

		
		-- *******.4.1.34592.*******.*******
		eponLineProfileLlidEntry OBJECT-TYPE
			SYNTAX EponLineProfileLlidEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponLineProfileId, eponLineProfileLlId }
			::= { eponLineProfileLlidTable 1 }

		
		EponLineProfileLlidEntry ::=
			SEQUENCE { 
				eponLineProfileLlId
					EponLineProfileLlId,
				eponLineProfileLlidDbaProfileId
					EponDbaProfileId,
				eponLineProfileLlidEncrypt
					INTEGER,
				eponLineProfileLlidOntCar
					EponTrafficProfileId,
				eponLineProfileLlidRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.2
		eponLineProfileLlId OBJECT-TYPE
			SYNTAX EponLineProfileLlId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileLlidEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponLineProfileLlidDbaProfileId OBJECT-TYPE
			SYNTAX EponDbaProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileLlidEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponLineProfileLlidEncrypt OBJECT-TYPE
			SYNTAX INTEGER
				{
				triple-churning(1),
				off(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileLlidEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponLineProfileLlidOntCar OBJECT-TYPE
			SYNTAX EponTrafficProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileLlidEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponLineProfileLlidRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileLlidEntry 6 }

		
		-- *******.4.1.34592.*******.1.2.4
		eponLineProfileDbaThresholdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponLineProfileDbaThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileObjects 4 }

		
		-- *******.4.1.34592.*******.*******
		eponLineProfileDbaThresholdEntry OBJECT-TYPE
			SYNTAX EponLineProfileDbaThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponLineProfileId, eponLineProfileQueueSetId, eponLineProfileQueueId }
			::= { eponLineProfileDbaThresholdTable 1 }

		
		EponLineProfileDbaThresholdEntry ::=
			SEQUENCE { 
				eponLineProfileQueueSetId
					Integer32,
				eponLineProfileQueueId
					Integer32,
				eponLineProfileThreshold
					Integer32,
				eponLineProfileDbaThresholdRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponLineProfileQueueSetId OBJECT-TYPE
			SYNTAX Integer32 (1..4)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileDbaThresholdEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponLineProfileQueueId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileDbaThresholdEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponLineProfileThreshold OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileDbaThresholdEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponLineProfileDbaThresholdRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponLineProfileDbaThresholdEntry 4 }

		
		-- *******.4.1.34592.*******.1.3
		eponSrvProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 3 }

		
		-- *******.4.1.34592.*******.1.3.1
		eponSrvProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfileInfoEntry OBJECT-TYPE
			SYNTAX EponSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId }
			::= { eponSrvProfileInfoTable 1 }

		
		EponSrvProfileInfoEntry ::=
			SEQUENCE { 
				eponSrvProfileId
					EponSrvProfileId,
				eponSrvProfileName
					EponSrvProfileName,
				eponSrvProfileBindNum
					Integer32,
				eponSrvProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfileId OBJECT-TYPE
			SYNTAX EponSrvProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfileName OBJECT-TYPE
			SYNTAX EponSrvProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSrvProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileInfoEntry 4 }

		
		-- *******.4.1.34592.*******.1.3.2
		eponSrvProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 2 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfileCfgEntry OBJECT-TYPE
			SYNTAX EponSrvProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId }
			::= { eponSrvProfileCfgTable 1 }

		
		EponSrvProfileCfgEntry ::=
			SEQUENCE { 
				eponSrvProfileMcFastLeave
					EponSwitch,
				eponSrvProfileMacLearning
					EponSwitch,
				eponSrvProfileMacAgeSeconds
					Integer32,
				eponSrvProfileLoopbackDetectCheck
					EponSwitch
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfileMcFastLeave OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfileMacLearning OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfileMacAgeSeconds OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSrvProfileLoopbackDetectCheck OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileCfgEntry 4 }

		
		-- *******.4.1.34592.*******.1.3.3
		eponSrvProfilePortNumTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfilePortNumEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 3 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfilePortNumEntry OBJECT-TYPE
			SYNTAX EponSrvProfilePortNumEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId }
			::= { eponSrvProfilePortNumTable 1 }

		
		EponSrvProfilePortNumEntry ::=
			SEQUENCE { 
				eponSrvProfileEthNum
					Integer32,
				eponSrvProfilePotsNum
					Integer32,
				eponSrvProfileCatvNum
					Integer32
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfileEthNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortNumEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfilePotsNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortNumEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfileCatvNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortNumEntry 3 }

		
		-- *******.4.1.34592.*******.1.3.4
		eponSrvProfileEthPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfileEthPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 4 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfileEthPortCfgEntry OBJECT-TYPE
			SYNTAX EponSrvProfileEthPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId, eponSrvProfilePortId }
			::= { eponSrvProfileEthPortCfgTable 1 }

		
		EponSrvProfileEthPortCfgEntry ::=
			SEQUENCE { 
				eponSrvProfileEthPortMacLimited
					Integer32,
				eponSrvProfileEthPortMtu
					Integer32,
				eponSrvProfileEthPortFlowCtrl
					EponSwitch,
				eponSrvProfileEthPortInTrafficProfileId
					EponTrafficProfileId,
				eponSrvProfileEthPortOutTrafficProfileId
					EponTrafficProfileId,
				eponSrvProfileEthPortNativeVlanId
					EponVlanId,
				eponSrvProfileEthPortNativeVlanPriority
					EponVlanPriority
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfileEthPortMacLimited OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfileEthPortMtu OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfileEthPortFlowCtrl OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSrvProfileEthPortInTrafficProfileId OBJECT-TYPE
			SYNTAX EponTrafficProfileId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponSrvProfileEthPortOutTrafficProfileId OBJECT-TYPE
			SYNTAX EponTrafficProfileId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponSrvProfileEthPortNativeVlanId OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponSrvProfileEthPortNativeVlanPriority OBJECT-TYPE
			SYNTAX EponVlanPriority
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileEthPortCfgEntry 7 }

		
		-- *******.4.1.34592.*******.1.3.6
		eponSrvProfilePortVlanCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfilePortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 6 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfilePortVlanCfgEntry OBJECT-TYPE
			SYNTAX EponSrvProfilePortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId, eponSrvProfilePortType, eponSrvProfilePortId, eponSrvProfilePortVlanEntryId }
			::= { eponSrvProfilePortVlanCfgTable 1 }

		
		EponSrvProfilePortVlanCfgEntry ::=
			SEQUENCE { 
				eponSrvProfilePortType
					INTEGER,
				eponSrvProfilePortId
					Integer32,
				eponSrvProfilePortVlanEntryId
					Integer32,
				eponSrvProfilePortVlanMode
					INTEGER,
				eponSrvProfilePortVlanSvlan
					EponVlanId,
				eponSrvProfilePortVlanSpri
					Integer32,
				eponSrvProfilePortVlanCvlan
					EponVlanId,
				eponSrvProfilePortVlanCpri
					Integer32,
				eponSrvProfilePortVlanRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfilePortType OBJECT-TYPE
			SYNTAX INTEGER { eth(1) }
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfilePortId OBJECT-TYPE
			SYNTAX Integer32 (1..9)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfilePortVlanEntryId OBJECT-TYPE
			SYNTAX Integer32 (1..9)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSrvProfilePortVlanMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				transparent(1),
				qinq(2),
				translation(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponSrvProfilePortVlanSvlan OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponSrvProfilePortVlanSpri OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponSrvProfilePortVlanCvlan OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 7 }

		
		-- *******.4.1.34592.*******.*******.8
		eponSrvProfilePortVlanCpri OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 8 }

		
		-- *******.4.1.34592.*******.*******.9
		eponSrvProfilePortVlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortVlanCfgEntry 9 }

		
		-- *******.4.1.34592.*******.1.3.8
		eponSrvProfilePortMcCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfilePortMcCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 8 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfilePortMcCfgEntry OBJECT-TYPE
			SYNTAX EponSrvProfilePortMcCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId, eponSrvProfilePortType, eponSrvProfilePortId, eponSrvProfilePortMcEntryId }
			::= { eponSrvProfilePortMcCfgTable 1 }

		
		EponSrvProfilePortMcCfgEntry ::=
			SEQUENCE { 
				eponSrvProfilePortMcEntryId
					Integer32,
				eponSrvProfilePortMcMaxGroupNum
					Integer32,
				eponSrvProfilePortMcTagStripMode
					INTEGER,
				eponSrvProfilePortMcTagStripSvlan
					EponVlanId,
				eponSrvProfilePortMcTagStripCvlan
					EponVlanId,
				eponSrvProfilePortMcRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfilePortMcEntryId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfilePortMcMaxGroupNum OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfilePortMcTagStripMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				untag(1),
				tag(2),
				translation(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSrvProfilePortMcTagStripSvlan OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponSrvProfilePortMcTagStripCvlan OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponSrvProfilePortMcRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcCfgEntry 6 }

		
		-- *******.4.1.34592.*******.1.3.9
		eponSrvProfilePortMcVlanCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSrvProfilePortMcVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfileObjects 9 }

		
		-- *******.4.1.34592.*******.*******
		eponSrvProfilePortMcVlanCfgEntry OBJECT-TYPE
			SYNTAX EponSrvProfilePortMcVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSrvProfileId, eponSrvProfilePortType, eponSrvProfilePortId, eponSrvProfilePortMcVlanEntryId }
			::= { eponSrvProfilePortMcVlanCfgTable 1 }

		
		EponSrvProfilePortMcVlanCfgEntry ::=
			SEQUENCE { 
				eponSrvProfilePortMcVlanEntryId
					Integer32,
				eponSrvProfilePortMcVlan
					EponVlanId,
				eponSrvProfilePortMcVlanRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSrvProfilePortMcVlanEntryId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcVlanCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSrvProfilePortMcVlan OBJECT-TYPE
			SYNTAX EponVlanId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcVlanCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSrvProfilePortMcVlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSrvProfilePortMcVlanCfgEntry 3 }

		
		-- *******.4.1.34592.*******.1.4
		eponTrafficProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 4 }

		
		-- *******.4.1.34592.*******.1.4.1
		eponTrafficProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponTrafficProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponTrafficProfileInfoEntry OBJECT-TYPE
			SYNTAX EponTrafficProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponTrafficProfileId }
			::= { eponTrafficProfileInfoTable 1 }

		
		EponTrafficProfileInfoEntry ::=
			SEQUENCE { 
				eponTrafficProfileId
					EponTrafficProfileId,
				eponTrafficProfileName
					EponTrafficProfileName,
				eponTrafficProfileCfgCir
					Integer32,
				eponTrafficProfileCfgPir
					Integer32,
				eponTrafficProfileCfgCbs
					Integer32,
				eponTrafficProfileCfgPbs
					Integer32,
				eponTrafficProfileCfgPriority
					Integer32,
				eponTrafficProfileBindNum
					Integer32,
				eponTrafficProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponTrafficProfileId OBJECT-TYPE
			SYNTAX EponTrafficProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponTrafficProfileName OBJECT-TYPE
			SYNTAX EponTrafficProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponTrafficProfileCfgCir OBJECT-TYPE
			SYNTAX Integer32 (64..10240000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponTrafficProfileCfgPir OBJECT-TYPE
			SYNTAX Integer32 (64..10240000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponTrafficProfileCfgCbs OBJECT-TYPE
			SYNTAX Integer32 (2000..10240000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponTrafficProfileCfgPbs OBJECT-TYPE
			SYNTAX Integer32 (2000..10240000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponTrafficProfileCfgPriority OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 7 }

		
		-- *******.4.1.34592.*******.*******.8
		eponTrafficProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 8 }

		
		-- *******.4.1.34592.*******.*******.9
		eponTrafficProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponTrafficProfileInfoEntry 9 }

		
		-- *******.4.1.34592.*******.1.5
		eponAlarmProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 5 }

		
		-- *******.4.1.34592.*******.1.5.1
		eponAlarmProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponAlarmProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponAlarmProfileInfoEntry OBJECT-TYPE
			SYNTAX EponAlarmProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponAlarmProfileId, eponAlarmProfileTypeId }
			::= { eponAlarmProfileInfoTable 1 }

		
		EponAlarmProfileInfoEntry ::=
			SEQUENCE { 
				eponAlarmProfileId
					Integer32,
				eponAlarmProfileTypeId
					INTEGER,
				eponAlarmProfileName
					EponProfileName,
				eponAlarmProfileThreshold
					EponAlarmProfileThreshold,
				eponAlarmProfileRestoreThreshold
					EponAlarmProfileThreshold,
				eponAlarmProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponAlarmProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..50)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponAlarmProfileTypeId OBJECT-TYPE
			SYNTAX INTEGER
				{
				rx-dropevents-alarm(1),
				tx-dropevents-alarm(2),
				rx-crcerrors-alarm(3),
				tx-crcerrors-alarm(4),
				rx-undersizes-alarm(5),
				tx-undersizes-alarm(6),
				rx-oversizes-alarm(7),
				tx-oversizes-alarm(8),
				rx-fragments-alarm(9),
				tx-fragments-alarm(10),
				rx-jabbers-alarm(11),
				tx-jabbers-alarm(12),
				rx-discards-alarm(13),
				tx-discards-alarm(14),
				rx-errors-alarm(15),
				tx-errors-alarm(16),
				rx-dropevents-warning(17),
				tx-dropevents-warning(18),
				rx-crcerrors-warning(19),
				tx-crcerrors-warning(20),
				rx-undersizes-warning(21),
				tx-undersizes-warning(22),
				rx-oversizes-warning(23),
				tx-oversizes-warning(24),
				rx-fragments-warning(25),
				tx-fragments-warning(26),
				rx-jabbers-warning(27),
				tx-jabbers-warning(28),
				rx-discards-warning(29),
				tx-discards-warning(30),
				rx-errors-warning(31),
				tx-errors-warning(32)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponAlarmProfileName OBJECT-TYPE
			SYNTAX EponProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.5
		eponAlarmProfileThreshold OBJECT-TYPE
			SYNTAX EponAlarmProfileThreshold
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponAlarmProfileRestoreThreshold OBJECT-TYPE
			SYNTAX EponAlarmProfileThreshold
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponAlarmProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponAlarmProfileInfoEntry 7 }

		
		-- *******.4.1.34592.*******.1.6
		eponOpticalAlarmProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 6 }

		
		-- *******.4.1.34592.*******.1.6.1
		eponOpticalAlarmProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOpticalAlarmProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponOpticalAlarmProfileInfoEntry OBJECT-TYPE
			SYNTAX EponOpticalAlarmProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOpticalAlarmProfileId, eponOpticalAlarmProfileTypeId }
			::= { eponOpticalAlarmProfileInfoTable 1 }

		
		EponOpticalAlarmProfileInfoEntry ::=
			SEQUENCE { 
				eponOpticalAlarmProfileId
					Integer32,
				eponOpticalAlarmProfileTypeId
					INTEGER,
				eponOpticalAlarmProfileName
					EponProfileName,
				eponOpticalAlarmProfileUpperThreshold
					OCTET STRING,
				eponOpticalAlarmProfileLowerThreshold
					OCTET STRING,
				eponOpticalAlarmProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponOpticalAlarmProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..50)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponOpticalAlarmProfileTypeId OBJECT-TYPE
			SYNTAX INTEGER
				{
				rx-power-high-alarm(1),
				rx-power-low-alarm(2),
				tx-power-high-alarm(3),
				tx-power-low-alarm(4),
				bias-high-alarm(5),
				bias-low-alarm(6),
				voltage-high-alarm(7),
				voltage-low-alarm(8),
				temperature-high-alarm(9),
				temperature-low-alarm(10),
				rx-power-high-warning(11),
				rx-power-low-warning(12),
				tx-power-high-warning(13),
				tx-power-low-warning(14),
				bias-high-warning(15),
				bias-low-warning(16),
				voltage-high-warning(17),
				voltage-low-warning(18),
				temperature-high-warning(19),
				temperature-low-warning(20)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponOpticalAlarmProfileName OBJECT-TYPE
			SYNTAX EponProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.5
		eponOpticalAlarmProfileUpperThreshold OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponOpticalAlarmProfileLowerThreshold OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 6 }

		
		-- *******.4.1.34592.*******.*******.7
		eponOpticalAlarmProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOpticalAlarmProfileInfoEntry 7 }

		
		-- *******.4.1.34592.*******.1.7
		eponSlaProfileObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponProfileObjects 7 }

		
		-- *******.4.1.34592.*******.1.7.1
		eponSlaProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSlaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponSlaProfileInfoEntry OBJECT-TYPE
			SYNTAX EponSlaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSlaProfileId }
			::= { eponSlaProfileInfoTable 1 }

		
		EponSlaProfileInfoEntry ::=
			SEQUENCE { 
				eponSlaProfileId
					Integer32,
				eponSlaProfileName
					EponProfileName,
				eponSlaProfileCycleLength
					Unsigned32,
				eponSlaProfileServicesNumber
					Unsigned32,
				eponSlaProfileRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSlaProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileInfoEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSlaProfileName OBJECT-TYPE
			SYNTAX EponProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileInfoEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSlaProfileCycleLength OBJECT-TYPE
			SYNTAX Unsigned32 (200..16777215)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileInfoEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSlaProfileServicesNumber OBJECT-TYPE
			SYNTAX Unsigned32 (1..8)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileInfoEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponSlaProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileInfoEntry 5 }

		
		-- *******.4.1.34592.*******.1.7.2
		eponSlaProfileServiceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponSlaProfileServiceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileObjects 2 }

		
		-- *******.4.1.34592.*******.*******
		eponSlaProfileServiceEntry OBJECT-TYPE
			SYNTAX EponSlaProfileServiceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponSlaProfileId, eponSlaProfileServiceId }
			::= { eponSlaProfileServiceTable 1 }

		
		EponSlaProfileServiceEntry ::=
			SEQUENCE { 
				eponSlaProfileServiceId
					Integer32,
				eponSlaProfileServiceFixPacketSize
					Integer32,
				eponSlaProfileServiceFixBandwidth
					Integer32,
				eponSlaProfileServiceGuaranteBandwidth
					Integer32,
				eponSlaProfileServiceBestEffortBandwidth
					Integer32,
				eponSlaProfileServiceWrrWeight
					Integer32
			 }

		-- *******.4.1.34592.*******.*******.1
		eponSlaProfileServiceId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2
		eponSlaProfileServiceFixPacketSize OBJECT-TYPE
			SYNTAX Integer32 (64..2000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 2 }

		
		-- *******.4.1.34592.*******.*******.3
		eponSlaProfileServiceFixBandwidth OBJECT-TYPE
			SYNTAX Integer32 (0..999936)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 3 }

		
		-- *******.4.1.34592.*******.*******.4
		eponSlaProfileServiceGuaranteBandwidth OBJECT-TYPE
			SYNTAX Integer32 (0..999936)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 4 }

		
		-- *******.4.1.34592.*******.*******.5
		eponSlaProfileServiceBestEffortBandwidth OBJECT-TYPE
			SYNTAX Integer32 (0..999936)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 5 }

		
		-- *******.4.1.34592.*******.*******.6
		eponSlaProfileServiceWrrWeight OBJECT-TYPE
			SYNTAX Integer32 (0..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponSlaProfileServiceEntry 6 }

		
		-- *******.4.1.34592.*******.2
		eponControlObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponObjects 2 }

		
		-- *******.4.1.34592.*******.2.1
		eponOnuObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponControlObjects 1 }

		
		-- *******.4.1.34592.*******.2.1.1
		eponOnuAuthenticationManagement OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponOnuObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuAuthenticationModeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuAuthenticationModeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationManagement 1 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuAuthenticationModeEntry OBJECT-TYPE
			SYNTAX EponOnuAuthenticationModeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuAuthenticationModeDeviceId, eponOnuAuthenticationModeSlotId, eponOnuAuthenticationModePortId }
			::= { eponOnuAuthenticationModeTable 1 }

		
		EponOnuAuthenticationModeEntry ::=
			SEQUENCE { 
				eponOnuAuthenticationModeDeviceId
					Integer32,
				eponOnuAuthenticationModeSlotId
					Integer32,
				eponOnuAuthenticationModePortId
					Integer32,
				eponOnuAuthenticationModeAdaptive
					EponSwitch
			 }

		-- *******.4.1.34592.*******.*******.1.1
		eponOnuAuthenticationModeDeviceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationModeEntry 1 }

		
		-- *******.4.1.34592.*******.*******.1.2
		eponOnuAuthenticationModeSlotId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationModeEntry 2 }

		
		-- *******.4.1.34592.*******.*******.1.3
		eponOnuAuthenticationModePortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationModeEntry 3 }

		
		-- *******.4.1.34592.*******.*******.1.4
		eponOnuAuthenticationModeAdaptive OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationModeEntry 4 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuAuthenticationConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuAuthenticationConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationManagement 2 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuAuthenticationConfigEntry OBJECT-TYPE
			SYNTAX EponOnuAuthenticationConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuAuthenOnuId }
			::= { eponOnuAuthenticationConfigTable 1 }

		
		EponOnuAuthenticationConfigEntry ::=
			SEQUENCE { 
				eponOnuAuthenOnuId
					Unsigned32,
				eponOnuAuthenMacAddress
					EponMacAddress,
				eponOnuAuthenLineProfileId
					EponLinePorfileId,
				eponOnuAuthenServiceProfileId
					EponSrvProfileId,
				eponOnuAuthenTimeMode
					INTEGER,
				eponOnuAuthenExpireAt
					Integer32,
				eponOnuAuthenRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.1.1
		eponOnuAuthenOnuId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 1 }

		
		-- *******.4.1.34592.*******.*******.1.2
		eponOnuAuthenMacAddress OBJECT-TYPE
			SYNTAX EponMacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 2 }

		
		-- *******.4.1.34592.*******.*******.1.3
		eponOnuAuthenLineProfileId OBJECT-TYPE
			SYNTAX EponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 3 }

		
		-- *******.4.1.34592.*******.*******.1.4
		eponOnuAuthenServiceProfileId OBJECT-TYPE
			SYNTAX EponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 4 }

		
		-- *******.4.1.34592.*******.*******.1.5
		eponOnuAuthenTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceAging(1),
				onceNoAging(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 5 }

		
		-- *******.4.1.34592.*******.*******.1.6
		eponOnuAuthenExpireAt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 6 }

		
		-- *******.4.1.34592.*******.*******.1.7
		eponOnuAuthenRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfigEntry 7 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuLoidAuthenticationConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuLoidAuthenticationConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationManagement 3 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuLoidAuthenticationConfigEntry OBJECT-TYPE
			SYNTAX EponOnuLoidAuthenticationConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuLoidAuthenOnuId }
			::= { eponOnuLoidAuthenticationConfigTable 1 }

		
		EponOnuLoidAuthenticationConfigEntry ::=
			SEQUENCE { 
				eponOnuLoidAuthenOnuId
					Unsigned32,
				eponOnuLoidAuthenLoid
					OCTET STRING,
				eponOnuLoidAuthenPassword
					OCTET STRING,
				eponOnuLoidAuthenLineProfileId
					EponLinePorfileId,
				eponOnuLoidAuthenServiceProfileId
					EponSrvProfileId,
				eponOnuLoidAuthenTimeMode
					INTEGER,
				eponOnuLoidAuthenExpireAt
					Integer32,
				eponOnuLoidAuthenRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.2.1.*******
		eponOnuLoidAuthenOnuId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 1 }

		
		-- *******.4.1.34592.*******.*******.1.2
		eponOnuLoidAuthenLoid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 2 }

		
		-- *******.4.1.34592.*******.*******.1.3
		eponOnuLoidAuthenPassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..12))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 3 }

		
		-- *******.4.1.34592.*******.*******.1.4
		eponOnuLoidAuthenLineProfileId OBJECT-TYPE
			SYNTAX EponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 4 }

		
		-- *******.4.1.34592.*******.*******.1.5
		eponOnuLoidAuthenServiceProfileId OBJECT-TYPE
			SYNTAX EponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 5 }

		
		-- *******.4.1.34592.*******.*******.1.6
		eponOnuLoidAuthenTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceAging(1),
				onceNoAging(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 6 }

		
		-- *******.4.1.34592.*******.*******.1.7
		eponOnuLoidAuthenExpireAt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 7 }

		
		-- *******.4.1.34592.*******.*******.1.8
		eponOnuLoidAuthenRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuLoidAuthenticationConfigEntry 8 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuAuthenticationConfirmTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuAuthenticationConfirmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationManagement 4 }

		
		-- *******.4.1.34592.*******.2.*******
		eponOnuAuthenticationConfirmEntry OBJECT-TYPE
			SYNTAX EponOnuAuthenticationConfirmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuAuthenticationConfirmDeviceId, eponOnuAuthenticationConfirmSlotId, eponOnuAuthenticationConfirmPortId }
			::= { eponOnuAuthenticationConfirmTable 1 }

		
		EponOnuAuthenticationConfirmEntry ::=
			SEQUENCE { 
				eponOnuAuthenticationConfirmDeviceId
					Integer32,
				eponOnuAuthenticationConfirmSlotId
					Integer32,
				eponOnuAuthenticationConfirmPortId
					Integer32,
				eponOnuAuthenConfirmType
					INTEGER,
				eponOnuAuthenConfirmMacAddress
					EponMacAddress,
				eponOnuAuthenConfirmLoid
					OCTET STRING,
				eponOnuAuthenConfirmLoidPassword
					OCTET STRING,
				eponOnuAuthenConfirmLineProfileId
					EponLinePorfileId,
				eponOnuAuthenConfirmServiceProfileId
					EponSrvProfileId,
				eponOnuAuthenConfirmTimeMode
					INTEGER,
				eponOnuAuthenConfirmAgingDuration
					Integer32,
				eponOnuAuthenticationConfirmRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.2.*******.1
		eponOnuAuthenticationConfirmDeviceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 1 }

		
		-- *******.4.1.34592.*******.2.*******.2
		eponOnuAuthenticationConfirmSlotId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 2 }

		
		-- *******.4.1.34592.*******.2.*******.3
		eponOnuAuthenticationConfirmPortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 3 }

		
		-- *******.4.1.34592.*******.2.*******.4
		eponOnuAuthenConfirmType OBJECT-TYPE
			SYNTAX INTEGER
				{
				macAuth(1),
				loidAuth(2),
				loidPwdAuth(3),
				macAuthAll(4),
				loidAuthAll(5),
				loidPwdAuthAll(6)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 4 }

		
		-- *******.4.1.34592.*******.2.*******.5
		eponOnuAuthenConfirmMacAddress OBJECT-TYPE
			SYNTAX EponMacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 5 }

		
		-- *******.4.1.34592.*******.2.*******.6
		eponOnuAuthenConfirmLoid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 6 }

		
		-- *******.4.1.34592.*******.2.*******.7
		eponOnuAuthenConfirmLoidPassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..12))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 7 }

		
		-- *******.4.1.34592.*******.2.*******.8
		eponOnuAuthenConfirmLineProfileId OBJECT-TYPE
			SYNTAX EponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 8 }

		
		-- *******.4.1.34592.*******.2.*******.9
		eponOnuAuthenConfirmServiceProfileId OBJECT-TYPE
			SYNTAX EponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 9 }

		
		-- *******.4.1.34592.*******.2.*******.10
		eponOnuAuthenConfirmTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceAging(1),
				onceNoAging(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 10 }

		
		-- *******.4.1.34592.*******.2.*******.11
		eponOnuAuthenConfirmAgingDuration OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 11 }

		
		-- *******.4.1.34592.*******.2.*******.12
		eponOnuAuthenticationConfirmRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuAuthenticationConfirmEntry 12 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuPolicyAuthTable OBJECT IDENTIFIER ::= { eponOnuAuthenticationManagement 5 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuPolicyAuthControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuPolicyAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthTable 1 }

		
		-- *******.4.1.34592.*******.2.1.*******
		eponOnuPolicyAuthControlEntry OBJECT-TYPE
			SYNTAX EponOnuPolicyAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuPolicyAuthControlDeviceId }
			::= { eponOnuPolicyAuthControlTable 1 }

		
		EponOnuPolicyAuthControlEntry ::=
			SEQUENCE { 
				eponOnuPolicyAuthControlDeviceId
					Integer32,
				eponOnuPolicyAuthSwitch
					EponSwitch,
				eponOnuPolicyAuthPortSwtichBitMap
					OCTET STRING,
				eponOnuPolicyAuthMode
					INTEGER,
				eponOnuPolicyAuthTargetMode
					INTEGER,
				eponOnuPolicyAuthTimeMode
					INTEGER
			 }

		-- *******.4.1.34592.*******.2.1.*******.1
		eponOnuPolicyAuthControlDeviceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 1 }

		
		-- *******.4.1.34592.*******.2.1.*******.3
		eponOnuPolicyAuthSwitch OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 3 }

		
		-- *******.4.1.34592.*******.2.1.*******.4
		eponOnuPolicyAuthPortSwtichBitMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 4 }

		
		-- *******.4.1.34592.*******.2.1.*******.5
		eponOnuPolicyAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(1),
				modelId(2),
				vendorId(3),
				modelIdAndSwver(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 5 }

		
		-- *******.4.1.34592.*******.2.1.*******.6
		eponOnuPolicyAuthTargetMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				macAuth(0),
				loidAuth(1),
				loidPasswordAuth(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 6 }

		
		-- *******.4.1.34592.*******.2.1.*******.7
		eponOnuPolicyAuthTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceNoAging(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthControlEntry 7 }

		
		-- *******.4.1.34592.*******.*******.2
		eponOnuPolicyAuthRuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuPolicyAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthTable 2 }

		
		-- *******.4.1.34592.*******.*******.2.1
		eponOnuPolicyAuthRuleEntry OBJECT-TYPE
			SYNTAX EponOnuPolicyAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuPolicyAuthRuleId }
			::= { eponOnuPolicyAuthRuleTable 1 }

		
		EponOnuPolicyAuthRuleEntry ::=
			SEQUENCE { 
				eponOnuPolicyAuthRuleId
					Integer32,
				eponOnuPolicyAuthRuleMatchMode
					INTEGER,
				eponOnuPolicyAuthRuleModelId
					OCTET STRING,
				eponOnuPolicyAuthRuleVendorId
					OCTET STRING,
				eponOnuPolicyAuthRuleSoftwareVersion
					OCTET STRING,
				eponOnuPolicyAuthRuleLineProfileId
					EponLinePorfileId,
				eponOnuPolicyAuthRuleSrvProfileId
					EponSrvProfileId,
				eponOnuPolicyAuthRuleRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.*******.2.1.1
		eponOnuPolicyAuthRuleId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 1 }

		
		-- *******.4.1.34592.*******.*******.2.1.2
		eponOnuPolicyAuthRuleMatchMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(1),
				modelId(2),
				vendorId(3),
				modelIdAndSwver(4)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 2 }

		
		-- *******.4.1.34592.*******.*******.2.1.3
		eponOnuPolicyAuthRuleModelId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (10))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 3 }

		
		-- *******.4.1.34592.*******.*******.2.1.4
		eponOnuPolicyAuthRuleVendorId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..4))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 4 }

		
		-- *******.4.1.34592.*******.*******.2.1.5
		eponOnuPolicyAuthRuleSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 5 }

		
		-- *******.4.1.34592.*******.*******.2.1.6
		eponOnuPolicyAuthRuleLineProfileId OBJECT-TYPE
			SYNTAX EponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 6 }

		
		-- *******.4.1.34592.*******.*******.2.1.7
		eponOnuPolicyAuthRuleSrvProfileId OBJECT-TYPE
			SYNTAX EponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 7 }

		
		-- *******.4.1.34592.*******.*******.2.1.8
		eponOnuPolicyAuthRuleRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPolicyAuthRuleEntry 8 }

		
		-- *******.4.1.34592.*******.2.1.2
		eponOnuProfileConfigManagement OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponOnuObjects 2 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuProfileConfigManagement 1 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuProfileCfgEntry OBJECT-TYPE
			SYNTAX EponOnuProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuProfileCfgOnuId }
			::= { eponOnuProfileCfgTable 1 }

		
		EponOnuProfileCfgEntry ::=
			SEQUENCE { 
				eponOnuProfileCfgOnuId
					Integer32,
				eponOnuAlarmProfileId
					Integer32,
				eponOnuOpticalAlarmProfileId
					Integer32,
				eponOnuSlaProfileId
					Integer32
			 }

		-- *******.4.1.34592.*******.*******.1.1
		eponOnuProfileCfgOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuProfileCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.1.2
		eponOnuAlarmProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..50 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuProfileCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.1.3
		eponOnuOpticalAlarmProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..50 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuProfileCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.1.4
		eponOnuSlaProfileId OBJECT-TYPE
			SYNTAX Integer32 (0..256 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuProfileCfgEntry 4 }

		
		-- *******.4.1.34592.*******.2.2
		eponOnuPortObjects OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponControlObjects 2 }

		
		-- *******.4.1.34592.*******.2.2.1
		eponOnuPortConfigManagement OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Description."
			::= { eponOnuPortObjects 1 }

		
		-- *******.4.1.34592.*******.*******
		eponOnuCatvPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EponOnuCatvPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuPortConfigManagement 1 }

		
		-- *******.4.1.34592.*******.*******.1
		eponOnuCatvPortCfgEntry OBJECT-TYPE
			SYNTAX EponOnuCatvPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { eponOnuCatvPortCfgDeviceId, eponOnuCatvPortCfgSlotId, eponOnuCatvPortCfgPortId }
			::= { eponOnuCatvPortCfgTable 1 }

		
		EponOnuCatvPortCfgEntry ::=
			SEQUENCE { 
				eponOnuCatvPortCfgDeviceId
					Integer32,
				eponOnuCatvPortCfgSlotId
					Integer32,
				eponOnuCatvPortCfgPortId
					Integer32,
				eponOnuCatvPortOperationlState
					EponSwitch,
				eponOnuCatvPortRxPower
					OCTET STRING
			 }

		-- *******.4.1.34592.*******.*******.1.1
		eponOnuCatvPortCfgDeviceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuCatvPortCfgEntry 1 }

		
		-- *******.4.1.34592.*******.*******.1.2
		eponOnuCatvPortCfgSlotId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuCatvPortCfgEntry 2 }

		
		-- *******.4.1.34592.*******.*******.1.3
		eponOnuCatvPortCfgPortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuCatvPortCfgEntry 3 }

		
		-- *******.4.1.34592.*******.*******.1.4
		eponOnuCatvPortOperationlState OBJECT-TYPE
			SYNTAX EponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuCatvPortCfgEntry 4 }

		
		-- *******.4.1.34592.*******.*******.1.5
		eponOnuCatvPortRxPower OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eponOnuCatvPortCfgEntry 5 }

		
	
	END

--
-- CDATA-EPON-MIB_170929.my
--
