--
-- FD-SYSTEM-MIB.my
-- MIB generated by MG-<PERSON><PERSON><PERSON> Visual MIB Builder Version 6.0  Build 88
-- Wednesday, January 18, 2017 at 17:53:13
--

--  FD-SYSTEM-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Monday, August 08, 2016 at 15:43:25
-- 
--  FD-SYSTEM-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, August 08, 2016 at 10:40:35
-- 
--  FD-SYSTEM-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, July 14, 2015 at 17:41:47
-- 
--  FD-SYSTEM-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, June 01, 2015 at 16:14:14
-- 

	FD-SYSTEM-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			epon, DeviceType, LedStat<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Device<PERSON>tatus			
				FROM EPON-EOC-MIB			
			OBJECT-GROUP, MODULE-<PERSON><PERSON><PERSON><PERSON><PERSON>E			
				FROM SNMPv2-<PERSON><PERSON>			
			TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Unsigned32, OBJECT-TYPE, 
			<PERSON><PERSON>ULE-IDEN<PERSON>TY			
				FROM SNMPv2-SMI			
			DisplayString, MacAddress, RowStatus			
				FROM SNMPv2-TC;
	
	
--     *******.4.1.345********
-- May 25, 2015 at 19:26 GMT
-- *******.4.1.345********
-- May 25, 2015 at 19:26 GMT
-- *******.4.1.345********
-- August 08, 2016 at 13:48 GMT
		-- *******.4.1.345********
		systemInfo MODULE-IDENTITY 
			LAST-UPDATED "201608081348Z"		-- August 08, 2016 at 13:48 GMT
			ORGANIZATION 
				"epon eoc factory."
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"system mib module"
			::= { epon 1 }

		
	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- Node definitions
-- 
-- Node definitions
-- 
-- *******.4.1.34592.*******
-- *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		sysBaseInfo OBJECT IDENTIFIER ::= { systemInfo 1 }

		
--     *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		sysModel OBJECT-TYPE
			SYNTAX DeviceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"OLT platform system model"
			::= { sysBaseInfo 1 }

		
--     *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		sysDesc OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Used for system manager to give a brief system description information"
			::= { sysBaseInfo 2 }

		
--     *******.4.1.34592.*******.3
-- *******.4.1.34592.*******.3
-- *******.4.1.34592.*******.3
		-- *******.4.1.34592.*******.3
		sysLocation OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Used for system manager to input the device(olt platform) location information"
			::= { sysBaseInfo 3 }

		
--     *******.4.1.34592.*******.4
-- *******.4.1.34592.*******.4
-- *******.4.1.34592.*******.4
		-- *******.4.1.34592.*******.4
		sysContact OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"system manager contact information"
			::= { sysBaseInfo 4 }

		
--     *******.4.1.34592.*******.5
-- *******.4.1.34592.*******.5
-- *******.4.1.34592.*******.5
		-- *******.4.1.34592.*******.5
		sysMajAlarmLed OBJECT-TYPE
			SYNTAX LedStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Status of main card MAJ led"
			::= { sysBaseInfo 5 }

		
--     *******.4.1.34592.*******.6
-- *******.4.1.34592.*******.6
-- *******.4.1.34592.*******.6
		-- *******.4.1.34592.*******.6
		sysCriAlarmLed OBJECT-TYPE
			SYNTAX LedStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Status of main card CRJ led"
			::= { sysBaseInfo 6 }

		
--     *******.4.1.34592.*******.7
-- *******.4.1.34592.*******.7
-- *******.4.1.34592.*******.7
		-- *******.4.1.34592.*******.7
		sysAlarmDesc OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Meaning of MAJ and CRI alarm leds. Normally, you can get alarm meaning
				from this object when one of or both the MAJ and CRI alarm leds are lip"
			::= { sysBaseInfo 7 }

		
--     *******.4.1.34592.*******.8
-- *******.4.1.34592.*******.8
-- *******.4.1.34592.*******.8
		-- *******.4.1.34592.*******.8
		sysCpuUtilization OBJECT-TYPE
			SYNTAX Integer32 (0..100)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysBaseInfo 8 }

		
--     *******.4.1.34592.*******.9
-- *******.4.1.34592.*******.9
-- *******.4.1.34592.*******.9
		-- *******.4.1.34592.*******.9
		sysMTU OBJECT-TYPE
			SYNTAX Integer32 (1518..2047)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysBaseInfo 9 }

		
--     *******.4.1.34592.*******
-- *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		sysConfig OBJECT IDENTIFIER ::= { systemInfo 2 }

		
--     *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		consolePortSpd OBJECT-TYPE
			SYNTAX INTEGER
				{
				bps2400(1),
				bps4800(2),
				bps9600(3),
				bps19200(4),
				bps38400(5),
				bps57600(6),
				bps115200(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Main card CONSOLE port access speed"
			::= { sysConfig 1 }

		
--     *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		manageIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The IP Address of outband network management port"
			::= { sysConfig 2 }

		
--     *******.4.1.34592.*******.3
-- *******.4.1.34592.*******.3
-- *******.4.1.34592.*******.3
		-- *******.4.1.34592.*******.3
		manageNetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The network mask of outband network management port"
			::= { sysConfig 3 }

		
--     *******.4.1.34592.*******.4
-- *******.4.1.34592.*******.4
-- *******.4.1.34592.*******.4
		-- *******.4.1.34592.*******.4
		manageGateway OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Default Gateway Address of outband network management port"
			::= { sysConfig 4 }

		
--     *******.4.1.34592.*******.5
-- *******.4.1.34592.*******.5
-- *******.4.1.34592.*******.5
		-- *******.4.1.34592.*******.5
		snmpReadCommunity OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The read-only community used on the embedded snmp agent"
			::= { sysConfig 5 }

		
--     *******.4.1.34592.*******.6
-- *******.4.1.34592.*******.6
-- *******.4.1.34592.*******.6
		-- *******.4.1.34592.*******.6
		snmpRWCommunity OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The read-write community used on the embedded snmp agent"
			::= { sysConfig 6 }

		
--     *******.4.1.34592.*******.8
-- *******.4.1.34592.*******.8
-- *******.4.1.34592.*******.8
		-- *******.4.1.34592.*******.8
		trapDstIpAddr1 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The first destination IP Address to which the system trap will send"
			::= { sysConfig 8 }

		
--     *******.4.1.34592.*******.9
-- *******.4.1.34592.*******.9
-- *******.4.1.34592.*******.9
		-- *******.4.1.34592.*******.9
		trapDstIpAddr2 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The second destination IP Address to which the system trap will send"
			::= { sysConfig 9 }

		
--     *******.4.1.34592.*******.10
-- *******.4.1.34592.*******.10
-- *******.4.1.34592.*******.10
		-- *******.4.1.34592.*******.10
		trapDstIpAddr3 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The third destination IP Address to which the system trap will send"
			::= { sysConfig 10 }

		
--     *******.4.1.34592.*******.11
-- *******.4.1.34592.*******.11
-- *******.4.1.34592.*******.11
		-- *******.4.1.34592.*******.11
		trapDstIpAddr4 OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The fourth destination IP Address to which the system trap will send"
			::= { sysConfig 11 }

		
--     *******.4.1.34592.*******.12
-- *******.4.1.34592.*******.12
-- *******.4.1.34592.*******.12
		-- *******.4.1.34592.*******.12
		sysOperate OBJECT-TYPE
			SYNTAX DeviceOperation
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set the value of this object to 'save(1)' to save all system configurations.
				Return 'noOperation(2)' when read. Set the value to 'noOperation(2)'will take
				nothing effect"
			::= { sysConfig 12 }

		
		-- *******.4.1.34592.*******.13
		outbandIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysConfig 13 }

		
		-- *******.4.1.34592.*******.14
		outbandNetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysConfig 14 }

		
--   *******.4.1.34592.*******.14
-- *******.4.1.34592.*******.14
		-- *******.4.1.34592.*******.15
		sysConfigurations OBJECT IDENTIFIER ::= { sysConfig 15 }

		
--     *******.4.1.34592.*******.14
-- *******.4.1.34592.*******.14.1
-- *******.4.1.34592.*******.14.1
		-- *******.4.1.34592.*******.15.1
		cfgAutoBackup OBJECT IDENTIFIER ::= { sysConfigurations 1 }

		
--     *******.4.1.34592.*******.14.1
-- *******.4.1.34592.*******.14.1.1
-- *******.4.1.34592.*******.14.1.1
		-- *******.4.1.34592.*******.15.1.1
		sysAutoBackupEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgAutoBackup 1 }

		
--     *******.4.1.34592.*******.14.2
-- *******.4.1.34592.*******.14.1.2
-- *******.4.1.34592.*******.14.1.2
		-- *******.4.1.34592.*******.15.1.2
		sysAutoBackupType OBJECT-TYPE
			SYNTAX INTEGER
				{
				olt(1),
				onu(2),
				all(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgAutoBackup 2 }

		
--     *******.4.1.34592.*******.14.3
-- *******.4.1.34592.*******.14.1.3
-- *******.4.1.34592.*******.14.1.3
		-- *******.4.1.34592.*******.15.1.3
		sysAutoBackupInterval OBJECT-TYPE
			SYNTAX INTEGER (1..365)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgAutoBackup 3 }

		
--     *******.4.1.34592.*******.14.4
-- *******.4.1.34592.*******.14.1.4
-- *******.4.1.34592.*******.14.1.4
		-- *******.4.1.34592.*******.15.1.4
		sysAutoBackupServer OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgAutoBackup 4 }

		
--   *******.4.1.34592.*******.14.2
-- *******.4.1.34592.*******.14.2
		-- *******.4.1.34592.*******.15.2
		cfgBackup OBJECT IDENTIFIER ::= { sysConfigurations 2 }

		
--   *******.4.1.34592.*******.14.2.1
-- *******.4.1.34592.*******.14.2.1
		-- *******.4.1.34592.*******.15.2.1
		sysCfgBackupType OBJECT-TYPE
			SYNTAX INTEGER
				{
				olt(1),
				onu(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgBackup 1 }

		
--   *******.4.1.34592.*******.14.2.2
-- *******.4.1.34592.*******.14.2.2
		-- *******.4.1.34592.*******.15.2.2
		sysCfgBackupServer OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgBackup 2 }

		
--   *******.4.1.34592.*******.14.3
-- *******.4.1.34592.*******.14.3
		-- *******.4.1.34592.*******.15.3
		cfgRestore OBJECT IDENTIFIER ::= { sysConfigurations 3 }

		
--   *******.4.1.34592.*******.14.3.1
-- *******.4.1.34592.*******.14.3.1
		-- *******.4.1.34592.*******.15.3.1
		sysCfgRestoreType OBJECT-TYPE
			SYNTAX INTEGER
				{
				olt(1),
				onu(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgRestore 1 }

		
--   *******.4.1.34592.*******.14.3.2
-- *******.4.1.34592.*******.14.3.2
		-- *******.4.1.34592.*******.15.3.2
		sysCfgRestoreServer OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgRestore 2 }

		
--  *******.4.1.34592.*******.14.3.3
		-- *******.4.1.34592.*******.15.3.3
		sysCfgRestoreFilename OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { cfgRestore 3 }

		
--     *******.4.1.34592.*******.15
-- *******.4.1.34592.*******.15
-- *******.4.1.34592.*******.15
		-- *******.4.1.34592.*******.16
		sysLog OBJECT IDENTIFIER ::= { sysConfig 16 }

		
--     *******.4.1.34592.*******.15.1
-- *******.4.1.34592.*******.15.1
-- *******.4.1.34592.*******.15.1
		-- *******.4.1.34592.*******.16.1
		sysLogLevel OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysLog 1 }

		
--     *******.4.1.34592.*******.15.2
-- *******.4.1.34592.*******.15.2
-- *******.4.1.34592.*******.15.2
		-- *******.4.1.34592.*******.16.2
		sysLogTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SysLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysLog 2 }

		
--     *******.4.1.34592.*******.15.2.1
-- *******.4.1.34592.*******.15.2.1
-- *******.4.1.34592.*******.15.2.1
		-- *******.4.1.34592.*******.16.2.1
		sysLogEntry OBJECT-TYPE
			SYNTAX SysLogEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { sysLogEntryIndex }
			::= { sysLogTable 1 }

		
		SysLogEntry ::=
			SEQUENCE { 
				sysLogEntryIndex
					INTEGER,
				sysLogEntryEnable
					INTEGER
			 }

--     *******.4.1.34592.*******.1*******
-- *******.4.1.34592.*******.1*******
-- *******.4.1.34592.*******.1*******
		-- *******.4.1.34592.*******.********
		sysLogEntryIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				onuOnOffLine(1),
				onuDyingGaspAlarm(2),
				onuUniLoopBackAlarm(3),
				all(127)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysLogEntry 1 }

		
--     *******.4.1.34592.*******.15.2.1.2
-- *******.4.1.34592.*******.15.2.1.2
-- *******.4.1.34592.*******.15.2.1.2
		-- *******.4.1.34592.*******.16.2.1.2
		sysLogEntryEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { sysLogEntry 2 }

		
--   *******.4.1.34592.*******.16
-- *******.4.1.34592.*******.16
		-- *******.4.1.34592.*******.17
		date OBJECT IDENTIFIER ::= { sysConfig 17 }

		
--     *******.4.1.345********.3
-- *******.4.1.345********.3
-- *******.4.1.345********.3
		-- *******.4.1.345********.3
		chassisInfo OBJECT IDENTIFIER ::= { systemInfo 3 }

		
--     *******.4.1.345********.3.1
-- *******.4.1.345********.3.1
-- *******.4.1.345********.3.1
		-- *******.4.1.345********.3.1
		chassisType OBJECT-TYPE
			SYNTAX DeviceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"type of the olt platform chassis"
			::= { chassisInfo 1 }

		
--     *******.4.1.345********.3.2
-- *******.4.1.345********.3.2
-- *******.4.1.345********.3.2
		-- *******.4.1.345********.3.2
		chassisFactorySerial OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..30))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. Chassis factory serial,reserved for future use."
			::= { chassisInfo 2 }

		
--     *******.4.1.345********.3.3
-- *******.4.1.345********.3.3
-- *******.4.1.345********.3.3
		-- *******.4.1.345********.3.3
		chassisRevision OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { chassisInfo 3 }

		
--     *******.4.1.345********.3.4
-- *******.4.1.345********.3.4
-- *******.4.1.345********.3.4
		-- *******.4.1.345********.3.4
		chassisTemperature OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"chassis temperature"
			::= { chassisInfo 4 }

		
--     *******.4.1.345********.3.5
-- *******.4.1.345********.3.5
-- *******.4.1.345********.3.5
		-- *******.4.1.345********.3.5
		powerStatusBit OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Power status bit masks; 
				value & 1 == 1 indicating power A on else off
				value & 2 == 2 indicating power B on else off
				"
			::= { chassisInfo 5 }

		
--     *******.4.1.345********.3.6
-- *******.4.1.345********.3.6
-- *******.4.1.345********.3.6
		-- *******.4.1.345********.3.6
		fanStatusBit OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Fan stauts bit masks
				value & 1 == 1 indicating fan 1 on else off
				value & 2 == 2 indicating fan 2 on else off
				value & 4 == 4 indicating fan 3 on else off
				value & 8 == 8 indicating fan 4 on else off"
			::= { chassisInfo 6 }

		
--     *******.4.1.34592.*******
-- *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		cardModule OBJECT IDENTIFIER ::= { systemInfo 5 }

		
--     *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		mainCard OBJECT IDENTIFIER ::= { cardModule 1 }

		
--     *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		mainCardType OBJECT-TYPE
			SYNTAX DeviceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"type of main card (it is also called control and switch card)"
			::= { mainCard 1 }

		
--     *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		mainCardFactorySerial OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..30))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. Main card factory serial, reserved for future use"
			::= { mainCard 2 }

		
--     *******.4.1.34592.*******.1.3
-- *******.4.1.34592.*******.1.3
-- *******.4.1.34592.*******.1.3
		-- *******.4.1.34592.*******.1.3
		mainCardHWRevision OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"main card hardware revision"
			::= { mainCard 3 }

		
--     *******.4.1.34592.*******.1.4
-- *******.4.1.34592.*******.1.4
-- *******.4.1.34592.*******.1.4
		-- *******.4.1.34592.*******.1.4
		mainCardSWVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"software version running on main card"
			::= { mainCard 4 }

		
--     *******.4.1.34592.*******.1.5
-- *******.4.1.34592.*******.1.5
-- *******.4.1.34592.*******.1.5
		-- *******.4.1.34592.*******.1.5
		mainCardRunningStatus OBJECT-TYPE
			SYNTAX DeviceStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The running status of main card, it can only be normal or abnormal,
				since the main card is always online"
			::= { mainCard 5 }

		
--     *******.4.1.34592.*******.1.6
-- *******.4.1.34592.*******.1.6
-- *******.4.1.34592.*******.1.6
		-- *******.4.1.34592.*******.1.6
		mainCardRunningTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The running time since the main card was last powered"
			::= { mainCard 6 }

		
--     *******.4.1.34592.*******.1.7
-- *******.4.1.34592.*******.1.7
-- *******.4.1.34592.*******.1.7
		-- *******.4.1.34592.*******.1.7
		mainCardOperate OBJECT-TYPE
			SYNTAX DeviceOperation
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The operation collection, such as reset, restore..."
			::= { mainCard 7 }

		
--     *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		ponCard OBJECT IDENTIFIER ::= { cardModule 2 }

		
--     *******.4.1.34592.*******.2.1
-- *******.4.1.34592.*******.2.1
-- *******.4.1.34592.*******.2.1
		-- *******.4.1.34592.*******.2.1
		ponCardTable OBJECT-TYPE
			SYNTAX SEQUENCE OF PonCardEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table defines base para objects of PON cards installed in the chassis"
			::= { ponCard 1 }

		
--     *******.4.1.34592.*******.2.1.1
-- *******.4.1.34592.*******.2.1.1
-- *******.4.1.34592.*******.2.1.1
		-- *******.4.1.34592.*******.2.1.1
		ponCardEntry OBJECT-TYPE
			SYNTAX PonCardEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry defines a sole PON card objects"
			INDEX { ponCardSlotId }
			::= { ponCardTable 1 }

		
		PonCardEntry ::=
			SEQUENCE { 
				ponCardSlotId
					Integer32,
				ponCardType
					DeviceType,
				ponCardFactorySerial
					OCTET STRING,
				ponCardHwRev
					DisplayString,
				ponCardFwVer
					DisplayString,
				ponCardRunningStatus
					DeviceStatus,
				ponCardRuningTime
					TimeTicks,
				ponCardOperate
					DeviceOperation,
				ponCardUpgradeStat
					INTEGER
			 }

--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardSlotId OBJECT-TYPE
			SYNTAX Integer32 (1..4)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PON card slot index, defined begin from 1 to max slot number"
			::= { ponCardEntry 1 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardType OBJECT-TYPE
			SYNTAX DeviceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PON card type value"
			::= { ponCardEntry 2 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardFactorySerial OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..30))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. PON card factory serial, reserved for future use"
			::= { ponCardEntry 3 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardHwRev OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PON card hardware revision"
			::= { ponCardEntry 4 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardFwVer OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PON card software version"
			::= { ponCardEntry 5 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardRunningStatus OBJECT-TYPE
			SYNTAX DeviceStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"pon card on-line status, each pon card may have following
				status:
				not installed (present)
				installed but can not be connected by main card
				installed and can be connected by main card"
			::= { ponCardEntry 7 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardRuningTime OBJECT-TYPE
			SYNTAX TimeTicks
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The running time since the pon card last powered"
			::= { ponCardEntry 8 }

		
--     *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		ponCardOperate OBJECT-TYPE
			SYNTAX DeviceOperation
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Operate action taken on PON card"
			::= { ponCardEntry 9 }

		
--     *******.4.1.34592.*******.*******0
-- *******.4.1.34592.*******.*******0
-- *******.4.1.34592.*******.*******0
		-- *******.4.1.34592.*******.*******0
		ponCardUpgradeStat OBJECT-TYPE
			SYNTAX INTEGER
				{
				booting(1),
				normalRun(2),
				rcvFileIng(3),
				rcvFileOk(4),
				rcvFileErr(5),
				upgrading(6),
				upgradeOk(7),
				upgradeErr(8),
				upgradeOlt(9),
				upgradeOnu(10)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { ponCardEntry 10 }

		
--     *******.4.1.345********.6
-- *******.4.1.345********.6
-- *******.4.1.345********.6
		-- *******.4.1.345********.6
		onuAuth OBJECT IDENTIFIER ::= { systemInfo 6 }

		
--     *******.4.1.345********.6.1
-- *******.4.1.345********.6.1
-- *******.4.1.345********.6.1
		-- *******.4.1.345********.6.1
		authMethod OBJECT-TYPE
			SYNTAX INTEGER
				{
				blackList(1),
				whiteList(2),
				none(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Which method that the manager will take to authenticate accessed ONUs,
				When set to blackList, the OLT will allow only the ONUs that their MAC
				is not included in the black MAC table.
				When set to whiteList,the OLT will allow only the ONUs that their MAC
				is included in the white MAC table."
			::= { onuAuth 1 }

		
--     *******.4.1.345********.6.2
-- *******.4.1.345********.6.2
-- *******.4.1.345********.6.2
		-- *******.4.1.345********.6.2
		nonAuthOper OBJECT-TYPE
			SYNTAX INTEGER { clearNonAuthMacList(2) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ONU MAC Authentication operation object, now, it can only
				be used to clear the MAC list which had tried to register
				but had not been authenticated"
			::= { onuAuth 2 }

		
--     *******.4.1.345********.6.3
-- *******.4.1.345********.6.3
-- *******.4.1.345********.6.3
		-- *******.4.1.345********.6.3
		onuAuthMacCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAuthMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MAC list table which contain the ONU MAC address list, these MAC address will
				be used to authenticate accessed ONUs."
			::= { onuAuth 3 }

		
--     *******.4.1.345********.6.3.1
-- *******.4.1.345********.6.3.1
-- *******.4.1.345********.6.3.1
		-- *******.4.1.345********.6.3.1
		onuAuthMacCfgEntry OBJECT-TYPE
			SYNTAX OnuAuthMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry in this table defines a sole MAC (when the beginMAC is equal
				to the endMAC or the endMAC is blank/zero) or a MAC range."
			INDEX { authMacEntryId }
			::= { onuAuthMacCfgTable 1 }

		
		OnuAuthMacCfgEntry ::=
			SEQUENCE { 
				authMacEntryId
					Unsigned32,
				beginMacAddr
					MacAddress,
				endMacAddr
					MacAddress,
				macAttr
					INTEGER,
				onuAuthMacRowStatus
					RowStatus
			 }

--     *******.4.1.345********.6.3.1.1
-- *******.4.1.345********.6.3.1.1
-- *******.4.1.345********.6.3.1.1
		-- *******.4.1.345********.6.3.1.1
		authMacEntryId OBJECT-TYPE
			SYNTAX Unsigned32 (1..65536)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description. entry serial ID, which used to identify an entry uniquely"
			::= { onuAuthMacCfgEntry 1 }

		
--     *******.4.1.345********.6.3.1.2
-- *******.4.1.345********.6.3.1.2
-- *******.4.1.345********.6.3.1.2
		-- *******.4.1.345********.6.3.1.2
		beginMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"begin MAC address, this object must be set to effect this entry"
			::= { onuAuthMacCfgEntry 2 }

		
--     *******.4.1.345********.6.3.1.3
-- *******.4.1.345********.6.3.1.3
-- *******.4.1.345********.6.3.1.3
		-- *******.4.1.345********.6.3.1.3
		endMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"End MAC address of a MAC range entry. Set this value to zero will result
				that this entry only indicate a unique MAC."
			::= { onuAuthMacCfgEntry 3 }

		
--     *******.4.1.345********.6.3.1.4
-- *******.4.1.345********.6.3.1.4
-- *******.4.1.345********.6.3.1.4
		-- *******.4.1.345********.6.3.1.4
		macAttr OBJECT-TYPE
			SYNTAX INTEGER
				{
				blackMac(1),
				whiteMac(2),
				obsolete(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to decide that the MAC(s) introduced in this entry
				should be classified into blackList or whiteList.
				
				When set to 'obsolete', this entry is not in use. This option is reserved
				for future use."
			::= { onuAuthMacCfgEntry 4 }

		
--     *******.4.1.345********.6.3.1.5
-- *******.4.1.345********.6.3.1.5
-- *******.4.1.345********.6.3.1.5
		-- *******.4.1.345********.6.3.1.5
		onuAuthMacRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create or delete a MAC entry"
			::= { onuAuthMacCfgEntry 5 }

		
--     *******.4.1.345********.6.4
-- *******.4.1.345********.6.4
-- *******.4.1.345********.6.4
		-- *******.4.1.345********.6.4
		nonAuthOnuListTable OBJECT-TYPE
			SYNTAX SEQUENCE OF NonAuthOnuListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contain illegal ONU MAC list which try to register
				on OLT"
			::= { onuAuth 4 }

		
--     *******.4.1.345********.6.4.1
-- *******.4.1.345********.6.4.1
-- *******.4.1.345********.6.4.1
		-- *******.4.1.345********.6.4.1
		nonAuthOnuListEntry OBJECT-TYPE
			SYNTAX NonAuthOnuListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry in this table contain a nonAuth onu MAC info"
			INDEX { oltId, nonAuthOnuMacIndex }
			::= { nonAuthOnuListTable 1 }

		
		NonAuthOnuListEntry ::=
			SEQUENCE { 
				nonAuthOnuMacIndex
					Unsigned32,
				nonAuthOnuMac
					MacAddress,
				nonAuthOnuTries
					Unsigned32,
				oltId
					Integer32
			 }

--     *******.4.1.345********.6.4.1.1
-- *******.4.1.345********.6.4.1.1
-- *******.4.1.345********.6.4.1.1
		-- *******.4.1.345********.6.4.1.1
		nonAuthOnuMacIndex OBJECT-TYPE
			SYNTAX Unsigned32 (1..65536)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Administrativly assigned entry index"
			::= { nonAuthOnuListEntry 1 }

		
--     *******.4.1.345********.6.4.1.2
-- *******.4.1.345********.6.4.1.2
-- *******.4.1.345********.6.4.1.2
		-- *******.4.1.345********.6.4.1.2
		nonAuthOnuMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MAC address which tried to register"
			::= { nonAuthOnuListEntry 2 }

		
--     *******.4.1.345********.6.4.1.3
-- *******.4.1.345********.6.4.1.3
-- *******.4.1.345********.6.4.1.3
		-- *******.4.1.345********.6.4.1.3
		nonAuthOnuTries OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The times that the illegal ONU try to register"
			::= { nonAuthOnuListEntry 3 }

		
--     *******.4.1.345********.6.4.1.4
-- *******.4.1.345********.6.4.1.4
-- *******.4.1.345********.6.4.1.4
		-- *******.4.1.345********.6.4.1.4
		oltId OBJECT-TYPE
			SYNTAX Integer32 (1..48)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nonAuthOnuListEntry 4 }

		
--     *******.4.1.345********.6.5
-- *******.4.1.345********.6.5
-- *******.4.1.345********.6.5
		-- *******.4.1.345********.6.5
		authMethodV2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				mac(1),
				loid(2),
				hybrid(3),
				blacklist(4),
				whitelist(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAuth 5 }

		
--     *******.4.1.345********.6.6
-- *******.4.1.345********.6.6
-- *******.4.1.345********.6.6
		-- *******.4.1.345********.6.6
		onuAuthBlacklistMacCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAuthBlacklistMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAuth 6 }

		
--     *******.4.1.345********.6.6.1
-- *******.4.1.345********.6.6.1
-- *******.4.1.345********.6.6.1
		-- *******.4.1.345********.6.6.1
		onuAuthBlacklistMacCfgEntry OBJECT-TYPE
			SYNTAX OnuAuthBlacklistMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { oltId, authBlacklistMacEntryId }
			::= { onuAuthBlacklistMacCfgTable 1 }

		
		OnuAuthBlacklistMacCfgEntry ::=
			SEQUENCE { 
				authBlacklistMacEntryId
					Unsigned32,
				onuAuthBlacklistMacAddr
					MacAddress,
				onuAuthBlacklistMacCfgRowStatus
					RowStatus
			 }

--     *******.4.1.345********.6.6.1.1
-- *******.4.1.345********.6.6.1.1
-- *******.4.1.345********.6.6.1.1
		-- *******.4.1.345********.6.6.1.1
		authBlacklistMacEntryId OBJECT-TYPE
			SYNTAX Unsigned32 (1..256)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"index of ONU mac in blacklist."
			::= { onuAuthBlacklistMacCfgEntry 1 }

		
--     *******.4.1.345********.6.6.1.2
-- *******.4.1.345********.6.6.1.2
-- *******.4.1.345********.6.6.1.2
		-- *******.4.1.345********.6.6.1.2
		onuAuthBlacklistMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"ONU mac in blacklist."
			::= { onuAuthBlacklistMacCfgEntry 2 }

		
--     *******.4.1.345********.6.6.1.3
-- *******.4.1.345********.6.6.1.3
-- *******.4.1.345********.6.6.1.3
		-- *******.4.1.345********.6.6.1.3
		onuAuthBlacklistMacCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create or delete a blacklist MAC entry"
			::= { onuAuthBlacklistMacCfgEntry 3 }

		
--     *******.4.1.345********.6.7
-- *******.4.1.345********.6.7
-- *******.4.1.345********.6.7
		-- *******.4.1.345********.6.7
		onuAuthWhitelistMacCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAuthWhitelistMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAuth 7 }

		
--     *******.4.1.345********.6.7.1
-- *******.4.1.345********.6.7.1
-- *******.4.1.345********.6.7.1
		-- *******.4.1.345********.6.7.1
		onuAuthWhitelistMacCfgEntry OBJECT-TYPE
			SYNTAX OnuAuthWhitelistMacCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { oltId, authWhitelistMacEntryId }
			::= { onuAuthWhitelistMacCfgTable 1 }

		
		OnuAuthWhitelistMacCfgEntry ::=
			SEQUENCE { 
				authWhitelistMacEntryId
					Unsigned32,
				onuAuthWhitelistMacAddr
					MacAddress,
				onuAuthWhitelistMacCfgRowStatus
					RowStatus
			 }

--     *******.4.1.345********.6.7.1.1
-- *******.4.1.345********.6.7.1.1
-- *******.4.1.345********.6.7.1.1
		-- *******.4.1.345********.6.7.1.1
		authWhitelistMacEntryId OBJECT-TYPE
			SYNTAX Unsigned32 (1..1024)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"index of ONU mac in whitelist."
			::= { onuAuthWhitelistMacCfgEntry 1 }

		
--     *******.4.1.345********.6.7.1.2
-- *******.4.1.345********.6.7.1.2
-- *******.4.1.345********.6.7.1.2
		-- *******.4.1.345********.6.7.1.2
		onuAuthWhitelistMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"ONU mac in whitelist."
			::= { onuAuthWhitelistMacCfgEntry 2 }

		
--     *******.4.1.345********.6.7.1.3
-- *******.4.1.345********.6.7.1.3
-- *******.4.1.345********.6.7.1.3
		-- *******.4.1.345********.6.7.1.3
		onuAuthWhitelistMacCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create or delete a whitelist MAC entry"
			::= { onuAuthWhitelistMacCfgEntry 3 }

		
--     *******.4.1.345********.6.8
-- *******.4.1.345********.6.8
-- *******.4.1.345********.6.8
		-- *******.4.1.345********.6.8
		onuAuthLoidCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAuthLoidCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAuth 8 }

		
--     *******.4.1.345********.6.8.1
-- *******.4.1.345********.6.8.1
-- *******.4.1.345********.6.8.1
		-- *******.4.1.345********.6.8.1
		onuAuthLoidCfgEntry OBJECT-TYPE
			SYNTAX OnuAuthLoidCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { authLoidEntryId }
			::= { onuAuthLoidCfgTable 1 }

		
		OnuAuthLoidCfgEntry ::=
			SEQUENCE { 
				authLoidEntryId
					Unsigned32,
				onuAuthLoidStrings
					DisplayString,
				onuAuthPasswordStrings
					DisplayString,
				onuAuthLoidCfgRowStatus
					RowStatus
			 }

--     *******.4.1.345********.6.8.1.1
-- *******.4.1.345********.6.8.1.1
-- *******.4.1.345********.6.8.1.1
		-- *******.4.1.345********.6.8.1.1
		authLoidEntryId OBJECT-TYPE
			SYNTAX Unsigned32 (1..512)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"index of ONU loid in table."
			::= { onuAuthLoidCfgEntry 1 }

		
--     *******.4.1.345********.6.8.1.2
-- *******.4.1.345********.6.8.1.2
-- *******.4.1.345********.6.8.1.2
		-- *******.4.1.345********.6.8.1.2
		onuAuthLoidStrings OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"CTC loid, max 24 Chars."
			::= { onuAuthLoidCfgEntry 2 }

		
--     *******.4.1.345********.6.8.1.3
-- *******.4.1.345********.6.8.1.3
-- *******.4.1.345********.6.8.1.3
		-- *******.4.1.345********.6.8.1.3
		onuAuthPasswordStrings OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"CTC loid password, max 12 Chars"
			::= { onuAuthLoidCfgEntry 3 }

		
--     *******.4.1.345********.6.8.1.4
-- *******.4.1.345********.6.8.1.4
-- *******.4.1.345********.6.8.1.4
		-- *******.4.1.345********.6.8.1.4
		onuAuthLoidCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to create or delete a loid entry"
			::= { onuAuthLoidCfgEntry 4 }

		
--     *******.4.1.345********.7
-- *******.4.1.345********.7
-- *******.4.1.345********.7
		-- *******.4.1.345********.7
		userManage OBJECT IDENTIFIER ::= { systemInfo 7 }

		
--     *******.4.1.345********.7.1
-- *******.4.1.345********.7.1
-- *******.4.1.345********.7.1
		-- *******.4.1.345********.7.1
		userManageTable OBJECT-TYPE
			SYNTAX SEQUENCE OF UserManageEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description. Access user management table. The table defined
				the users who can access the system shell management interface,
				including local CLI and remote telnet access.
				
				The users defined in this table is independent with those
				defined in EMS."
			::= { userManage 1 }

		
--     *******.4.1.345********.7.1.1
-- *******.4.1.345********.7.1.1
-- *******.4.1.345********.7.1.1
		-- *******.4.1.345********.7.1.1
		userManageEntry OBJECT-TYPE
			SYNTAX UserManageEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description. One entry contains one user attributes, up to 5
				entries can be created in this table."
			INDEX { userId }
			::= { userManageTable 1 }

		
		UserManageEntry ::=
			SEQUENCE { 
				userId
					Integer32,
				userName
					DisplayString,
				userPassword
					OCTET STRING,
				userPermission
					Unsigned32,
				userAccessDeviceMap
					Unsigned32,
				loginTimeout
					Unsigned32,
				userEntryRowStatus
					RowStatus
			 }

--     *******.4.1.345********.7.1.1.1
-- *******.4.1.345********.7.1.1.1
-- *******.4.1.345********.7.1.1.1
		-- *******.4.1.345********.7.1.1.1
		userId OBJECT-TYPE
			SYNTAX Integer32 (1..10)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Administrativly assigned entry index"
			::= { userManageEntry 1 }

		
--     *******.4.1.345********.7.1.1.2
-- *******.4.1.345********.7.1.1.2
-- *******.4.1.345********.7.1.1.2
		-- *******.4.1.345********.7.1.1.2
		userName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"user name"
			::= { userManageEntry 2 }

		
--     *******.4.1.345********.7.1.1.3
-- *******.4.1.345********.7.1.1.3
-- *******.4.1.345********.7.1.1.3
		-- *******.4.1.345********.7.1.1.3
		userPassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"user password"
			::= { userManageEntry 3 }

		
--     *******.4.1.345********.7.1.1.4
-- *******.4.1.345********.7.1.1.4
-- *******.4.1.345********.7.1.1.4
		-- *******.4.1.345********.7.1.1.4
		userPermission OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. This object define the user priority and other
				permission attributes"
			::= { userManageEntry 4 }

		
--     *******.4.1.345********.7.1.1.5
-- *******.4.1.345********.7.1.1.5
-- *******.4.1.345********.7.1.1.5
		-- *******.4.1.345********.7.1.1.5
		userAccessDeviceMap OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is defined for future use.
				It is a bit map value which defines which devices in the system
				can be accessed by a user.
				
				Temporary definitions:
				
				   ----------------------------------------------
				  |sys level | swithch module | olt module| onu |............
				   ----------------------------------------------
				 high 2b            2b              5b        7b   reserved  low
				
				when the map bit set to 1, the user can access the corresponding device
				otherwise can not"
			::= { userManageEntry 5 }

		
--     *******.4.1.345********.7.1.1.6
-- *******.4.1.345********.7.1.1.6
-- *******.4.1.345********.7.1.1.6
		-- *******.4.1.345********.7.1.1.6
		loginTimeout OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description. Time out time since one accessed user last operation,
				when this time elapse, the user will be kicked off"
			DEFVAL { 300 }
			::= { userManageEntry 6 }

		
--     *******.4.1.345********.7.1.1.7
-- *******.4.1.345********.7.1.1.7
-- *******.4.1.345********.7.1.1.7
		-- *******.4.1.345********.7.1.1.7
		userEntryRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Use this object to create or delete a user info"
			::= { userManageEntry 7 }

		
--     *******.4.1.345********.8
-- *******.4.1.345********.8
-- *******.4.1.345********.8
		-- *******.4.1.345********.8
		upgrade OBJECT IDENTIFIER ::= { systemInfo 8 }

		
--     *******.4.1.345********.8.1
-- *******.4.1.345********.8.1
-- *******.4.1.345********.8.1
		-- *******.4.1.345********.8.1
		ftpServerIp OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The host IP Address on which FTP server running and this
				host will be used as the server to do FTP operation"
			::= { upgrade 1 }

		
--     *******.4.1.345********.8.2
-- *******.4.1.345********.8.2
-- *******.4.1.345********.8.2
		-- *******.4.1.345********.8.2
		ftpServerUserName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The user name used to access ftp server
				"
			::= { upgrade 2 }

		
--     *******.4.1.345********.8.3
-- *******.4.1.345********.8.3
-- *******.4.1.345********.8.3
		-- *******.4.1.345********.8.3
		ftpServerUserPasswd OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The user password which will be used to access ftp server
				"
			::= { upgrade 3 }

		
--     *******.4.1.345********.8.4
-- *******.4.1.345********.8.4
-- *******.4.1.345********.8.4
		-- *******.4.1.345********.8.4
		ftpOperFileName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..63))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The file name which will be download to or upload from
				device. When the operation is download, the operation
				file should located on the host; When the operation is
				upload the file should located on the target device;
				"
			::= { upgrade 4 }

		
--     *******.4.1.345********.8.6
-- *******.4.1.345********.8.6
-- *******.4.1.345********.8.6
		-- *******.4.1.345********.8.6
		ftpOperTarget OBJECT-TYPE
			SYNTAX INTEGER
				{
				ctrlCardImage(1),
				ponCardImage(2),
				oltApp(3),
				oltPers(4),
				oltBoot(5),
				onuApp(6),
				onuPers(7),
				onuBoot(8),
				otherSpecifiedFile(9)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The operate target, which are device firmware or configuration
				file.
				
				When do upgrade or download,one of these file targets 
				should be selected to operate.
				
				        ctrlCardImage(1),
						ponCardImage(2),
						oltApp(3),
						oltPers(4),
						oltBoot(5),
						onuApp(6),
						onuPers(7),
						onuBoot(8),
						otherSpecifiedFile(9)
				"
			::= { upgrade 6 }

		
--     *******.4.1.345********.8.7
-- *******.4.1.345********.8.7
-- *******.4.1.345********.8.7
		-- *******.4.1.345********.8.7
		dwLoadFileCrcCheck OBJECT-TYPE
			SYNTAX INTEGER
				{
				checkCrc(1),
				dontCheckCrc(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to indicate the target device 
				should or not to do crc check operation,if 'checkCrc'
				the operator should give the download file's crc 
				calculate result on the 'dwLoadFileCrcValue' object.
				
				Crc check is now only useful when do download 
				operation. 
				"
			::= { upgrade 7 }

		
--     *******.4.1.345********.8.8
-- *******.4.1.345********.8.8
-- *******.4.1.345********.8.8
		-- *******.4.1.345********.8.8
		dwLoadFileCrcValue OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"
				CRC value of the download file, this value will be used to 
				compare with the CRC result calculated by the firmware running
				on device to check if error occurred during file transmission"
			::= { upgrade 8 }

		
--     *******.4.1.345********.8.9
-- *******.4.1.345********.8.9
-- *******.4.1.345********.8.9
		-- *******.4.1.345********.8.9
		operDeviceMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (10))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object defines the upgrade or download device objects,when do upgrade operation
				one or more device can be selected as operate objects. The data format defined as
				following:
				
				---------------------------------
				|  slot   |   olt   |    onu    |   
				---------------------------------
				  1byte      1byte      8byte
				
				All these 10 bytes used as bit map for device selecting, when selected set the 
				according bit to 1, else set to 0
				
				The bit sequence in each used from low to high:
				
				For example:
				slot:0000 0101, select slot 1 and 3, dont select slot 2 and 4 and others(reserved)
				olt: 0000 0011  select olt(pon port) 1 and 2, dont select others(reserved)
				onu: byte[0] map to onuId 1~8; byte[1] map to onuId 9~16,...,
				     byte[7] map to onuId 56~63
				     Bit map in byte[0]: b0~b7 map to onuId1~onuId8, etc."
			::= { upgrade 9 }

		
--     *******.4.1.345********.8.10
-- *******.4.1.345********.8.10
-- *******.4.1.345********.8.10
		-- *******.4.1.345********.8.10
		upgradeStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				paraErr(1),
				initFtpErr(2),
				transmitting(3),
				transmitErr(4),
				transmitOk(5),
				upgrading(6),
				upgradeErr(7),
				upgradeOk(8),
				uploading(9),
				uploadErr(10),
				uploadOk(11)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Upgrade process status
				"
			::= { upgrade 10 }

		
--     *******.4.1.345********.8.11
-- *******.4.1.345********.8.11
-- *******.4.1.345********.8.11
		-- *******.4.1.345********.8.11
		upgradeOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				downloadFile(1),
				upgrade(2),
				reboot(3),
				uploadFile(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object defines an operation collection, which are
				used to do downlaod,upgrade or get file(upload) operations.
				"
			::= { upgrade 11 }

		
--     *******.4.1.345********.8.12
-- *******.4.1.345********.8.12
-- *******.4.1.345********.8.12
		-- *******.4.1.345********.8.12
		ftpProgress OBJECT-TYPE
			SYNTAX Integer32
			UNITS "percent"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { upgrade 12 }

		
--     *******.4.1.34592.*******3
-- *******.4.1.34592.*******3
-- *******.4.1.34592.*******3
		-- *******.4.1.34592.*******3
		fdSysConformance OBJECT IDENTIFIER ::= { systemInfo 13 }

		
--     *******.4.1.34592.*******3.1
-- *******.4.1.34592.*******3.1
-- *******.4.1.34592.*******3.1
		-- *******.4.1.34592.*******3.1
		fdSystemGroups OBJECT IDENTIFIER ::= { fdSysConformance 1 }

		
--     *******.4.1.34592.*******3.1.1
-- *******.4.1.34592.*******3.1.1
-- *******.4.1.34592.*******3.1.1
		-- *******.4.1.34592.*******3.1.1
		sysBaseManageGroup OBJECT-GROUP
			OBJECTS { sysModel, sysDesc, sysLocation, sysContact, sysMajAlarmLed, 
				sysCriAlarmLed, sysAlarmDesc, consolePortSpd, manageIpAddr, manageNetMask, 
				manageGateway, snmpReadCommunity, snmpRWCommunity, trapDstIpAddr1, trapDstIpAddr2, 
				trapDstIpAddr3, trapDstIpAddr4, sysLogEntryEnable, sysLogLevel, sysCfgRestoreServer, 
				sysCfgRestoreType, sysCfgBackupServer, sysCfgBackupType, sysCfgRestoreFilename, outbandNetMask, 
				outbandIpAddr, sysOperate, sysCpuUtilization, sysMTU, sysAutoBackupEnable, 
				sysAutoBackupType, sysAutoBackupInterval, sysAutoBackupServer }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing fd system basic
				features management"
			::= { fdSystemGroups 1 }

		
--     *******.4.1.34592.*******3.1.2
-- *******.4.1.34592.*******3.1.2
-- *******.4.1.34592.*******3.1.2
		-- *******.4.1.34592.*******3.1.2
		chassisInfoGroup OBJECT-GROUP
			OBJECTS { chassisType, chassisFactorySerial, chassisRevision, chassisTemperature, powerStatusBit, 
				fanStatusBit }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing chassis information management"
			::= { fdSystemGroups 2 }

		
--     *******.4.1.34592.*******3.1.3
-- *******.4.1.34592.*******3.1.3
-- *******.4.1.34592.*******3.1.3
		-- *******.4.1.34592.*******3.1.3
		cardModuleGroup OBJECT-GROUP
			OBJECTS { mainCardType, mainCardFactorySerial, mainCardHWRevision, mainCardSWVersion, mainCardRunningStatus, 
				mainCardRunningTime, mainCardOperate, ponCardType, ponCardFactorySerial, ponCardHwRev, 
				ponCardFwVer, ponCardRunningStatus, ponCardRuningTime, ponCardOperate, ponCardUpgradeStat
				 }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing installed card module
				basic features management"
			::= { fdSystemGroups 3 }

		
--     *******.4.1.34592.*******3.1.4
-- *******.4.1.34592.*******3.1.4
-- *******.4.1.34592.*******3.1.4
		-- *******.4.1.34592.*******3.1.4
		onuAuthGroup OBJECT-GROUP
			OBJECTS { authMethod, nonAuthOper, beginMacAddr, endMacAddr, macAttr, 
				onuAuthMacRowStatus, nonAuthOnuMac, nonAuthOnuTries, onuAuthLoidCfgRowStatus, onuAuthPasswordStrings, 
				onuAuthLoidStrings, onuAuthWhitelistMacCfgRowStatus, onuAuthWhitelistMacAddr, onuAuthBlacklistMacCfgRowStatus, oltId, 
				onuAuthBlacklistMacAddr, authMethodV2 }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing ONU authentication MAC
				list management"
			::= { fdSystemGroups 4 }

		
--     *******.4.1.34592.*******3.1.5
-- *******.4.1.34592.*******3.1.5
-- *******.4.1.34592.*******3.1.5
		-- *******.4.1.34592.*******3.1.5
		userManageGroup OBJECT-GROUP
			OBJECTS { userName, userPassword, userPermission, userAccessDeviceMap, loginTimeout, 
				userEntryRowStatus }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing system user management"
			::= { fdSystemGroups 5 }

		
--     *******.4.1.34592.*******3.1.6
-- *******.4.1.34592.*******3.1.6
-- *******.4.1.34592.*******3.1.6
		-- *******.4.1.34592.*******3.1.6
		systemUpgradeGroup OBJECT-GROUP
			OBJECTS { ftpServerIp, ftpServerUserName, ftpServerUserPasswd, ftpOperFileName, dwLoadFileCrcCheck, 
				dwLoadFileCrcValue, operDeviceMap, upgradeStatus, ftpProgress, upgradeOperation, 
				ftpOperTarget }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing system device upgrade
				management"
			::= { fdSystemGroups 6 }

		
--     *******.4.1.34592.*******3.2
-- *******.4.1.34592.*******3.2
-- *******.4.1.34592.*******3.2
		-- *******.4.1.34592.*******3.2
		fdSystemCompliances OBJECT IDENTIFIER ::= { fdSysConformance 2 }

		
--     *******.4.1.34592.*******3.2.1
-- this module
-- this module
-- *******.4.1.34592.*******3.2.1
-- this module
-- *******.4.1.34592.*******3.2.1
-- this module
		-- *******.4.1.34592.*******3.2.1
		fdSystemCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance statement"
			MODULE -- this module
				MANDATORY-GROUPS { sysBaseManageGroup, chassisInfoGroup, cardModuleGroup, onuAuthGroup, userManageGroup, 
					systemUpgradeGroup }
			::= { fdSystemCompliances 1 }

		
	
	END

--
-- FD-SYSTEM-MIB.my
--
