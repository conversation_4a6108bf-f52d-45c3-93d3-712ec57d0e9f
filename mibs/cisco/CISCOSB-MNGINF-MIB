CISCOSB-MNGINF-MIB DEFINITIONS ::= BEGIN

-- Title:                <PERSON><PERSON><PERSON><PERSON> MNGINF ROS
--                         This Private MIB supports the Management ACL for ROS
-- Version:                    7.46
-- Date:                       20 Jan 2007



IMPORTS
    switch001                               FROM CISCOSB-MIB
    OBJECT-TYP<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>signed<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>-IDENTITY                         FROM SNMPv2-SM<PERSON>
    TEXTUAL-CONVENTION, DisplayString,
    TruthValue, RowStatus                   FROM SNMPv2-TC
    InetAddressType,InetAddress             FROM INET-ADDRESS-MIB; -- RFC2851

rlMngInf MODULE-IDENTITY
         LAST-UPDATED "200309210000Z"
         ORGANIZATION "Cisco Systems, Inc."

         CONTACT-INFO
         "Postal: 170 West Tasman Drive
         San Jose , CA 95134-1706
         USA

         
         Website:  Cisco Small Business Support Community <http://www.cisco.com/go/smallbizsupport>"

         DESCRIPTION
                "The private MIB module definition for management access control."
         REVISION "200309210000Z"
         DESCRIPTION
                "Changed IMPORTS, added this MODULE-IDENTITY clause and editorial changes."
        ::= { switch001 89 }

RlMngInfServiceType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "Management ACL Service type"
     SYNTAX INTEGER {
        dontCare(0),
        telnet(1),
        snmp(2),
        http(3),
        https(4),
        ssh(5)
     }

RlMngInfActionType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "Management ACL Action definition."
    SYNTAX INTEGER {
        permit(0),
        deny(1)
    }

rlMngInfMibVersion OBJECT-TYPE
    SYNTAX  INTEGER
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "MIB's version, the current version is 1."
    ::= {rlMngInf 1}

rlMngInfEnable OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The variable specifies if Management ACL functionality is enabled."
    ::= {rlMngInf 2}

rlMngInfActiveListName OBJECT-TYPE
    SYNTAX DisplayString (SIZE(0..32))
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The currently activated Management ACL name"
    ::= {rlMngInf 3}

rlMngInfListTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlMngInfListEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The table specifies all defined Access Lists definitions for IPv4 addresses.
         Provide backward compatibility for previous versions."
    ::= {rlMngInf 4}

rlMngInfListEntry  OBJECT-TYPE
    SYNTAX RlMngInfListEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Row definition for this table."
    INDEX { rlMngInfListName, rlMngInfListPriority }
    ::= { rlMngInfListTable 1}

RlMngInfListEntry ::= SEQUENCE {
    rlMngInfListName            DisplayString,
    rlMngInfListPriority        Unsigned32,
    rlMngInfListIfIndex         Unsigned32,
    rlMngInfListIpAddr          IpAddress,
    rlMngInfListIpNetMask       IpAddress,
    rlMngInfListService         RlMngInfServiceType,
    rlMngInfListAction          RlMngInfActionType,
    rlMngInfListRowStatus       RowStatus
}

rlMngInfListName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE(1..32))
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The Name of the Access List."
    ::= { rlMngInfListEntry 1}

rlMngInfListPriority OBJECT-TYPE
    SYNTAX  Unsigned32 (1..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The Priority value."
    ::= { rlMngInfListEntry 2}

rlMngInfListIfIndex OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IfIndex value. The IfIndex can be configured to be 0, which means don't care value."
    ::= { rlMngInfListEntry 3}

rlMngInfListIpAddr OBJECT-TYPE
    SYNTAX  IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IP address. The IP address can be configured to be 0, which means don't care value."
    ::= { rlMngInfListEntry 4}

rlMngInfListIpNetMask OBJECT-TYPE
    SYNTAX  IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The subnet mask associated with the IP address of this entry. The value of the mask is
         an IP address with all the network bits set to 1 and all the hosts bits set to 0."
    ::= { rlMngInfListEntry 5}

rlMngInfListService  OBJECT-TYPE
    SYNTAX  RlMngInfServiceType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Service type. The Service type address can be configured to be 0,
         which means any of Telnet, SNMP, HTTP, HTTPS, SSH."
    ::= { rlMngInfListEntry 6}

rlMngInfListAction  OBJECT-TYPE
    SYNTAX  RlMngInfActionType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Action type. Can be permit or deny."
    ::= { rlMngInfListEntry 7}

rlMngInfListRowStatus OBJECT-TYPE
    SYNTAX   RowStatus
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "The row status variable, used according to
         row installation and removal conventions."
    ::= { rlMngInfListEntry 8}

---         Management Interface auditing control (SysLog)

rlMngInfAuditingEnable  OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Controls whether SysLog messages
   should be issued on reject by rule"
    DEFVAL { true }
    ::= { rlMngInf 5 }

rlMngInfListInetTable OBJECT-TYPE
    SYNTAX SEQUENCE OF RlMngInfListInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The table specifies all defined Access Lists definitions  definitions
        for both IPv4 and IPv6 addresses."
    ::= {rlMngInf 6}

rlMngInfListInetEntry  OBJECT-TYPE
    SYNTAX RlMngInfListInetEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "Row definition for this table."
    INDEX { rlMngInfListInetName, rlMngInfListInetPriority }
    ::= { rlMngInfListInetTable 1}

RlMngInfListInetEntry ::= SEQUENCE {
    rlMngInfListInetName             DisplayString,
    rlMngInfListInetPriority         Unsigned32,
    rlMngInfListInetIfIndex          Unsigned32,
    rlMngInfListInetIpAddrType       InetAddressType,
    rlMngInfListInetIpAddr           InetAddress,
    rlMngInfListInetIpNetMask        IpAddress,
    rlMngInfListInetService          RlMngInfServiceType,
    rlMngInfListInetAction           RlMngInfActionType,
    rlMngInfListInetRowStatus        RowStatus,
    rlMngInfListInetIPv6PrefixLength INTEGER
}

rlMngInfListInetName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE(1..32))
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The Name of the Access List."
    ::= { rlMngInfListInetEntry 1}

rlMngInfListInetPriority OBJECT-TYPE
    SYNTAX  Unsigned32 (1..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The Priority value."
    ::= { rlMngInfListInetEntry 2}

rlMngInfListInetIfIndex OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The IfIndex value. The IfIndex can be configured to be 0, which means don't care value."
    ::= { rlMngInfListInetEntry 3}

rlMngInfListInetIpAddrType OBJECT-TYPE
    SYNTAX  InetAddressType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address type."
    ::= { rlMngInfListInetEntry 4}

rlMngInfListInetIpAddr OBJECT-TYPE
    SYNTAX  InetAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "The Inet address.Used for both IPv4 and IPv6 addresses.
        The InetIpAddress can  be configured to be 0.0.0.0 and address type IPv4 ,
        which means ignored value."
    ::= { rlMngInfListInetEntry 5}

rlMngInfListInetIpNetMask OBJECT-TYPE
    SYNTAX  IpAddress
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This field will used in case of IPv4 addresses . For IPv6 this field ignored.
         Default value ***************."
    ::= { rlMngInfListInetEntry 6}

rlMngInfListInetService  OBJECT-TYPE
    SYNTAX  RlMngInfServiceType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Service type. The Service type address can be configured to be 0,
         which means any of Telnet, SNMP, HTTP, HTTPS, SSH."
    ::= { rlMngInfListInetEntry 7}

rlMngInfListInetAction  OBJECT-TYPE
    SYNTAX  RlMngInfActionType
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Action type. Can be permit or deny."
    ::= { rlMngInfListInetEntry 8}

rlMngInfListInetRowStatus OBJECT-TYPE
    SYNTAX   RowStatus
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "The row status variable, used according to
         row installation and removal conventions."
    ::= { rlMngInfListInetEntry 9}

rlMngInfListInetIPv6PrefixLength OBJECT-TYPE
        SYNTAX      INTEGER (0.. 128)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "IPv6 prefix length.This field provide IPv6 prefix length (mask)
                     for IPv6 only.In case of IPv4 this field will ignored"
        DEFVAL      { 128 }
        ::= { rlMngInfListInetEntry 10}

END

