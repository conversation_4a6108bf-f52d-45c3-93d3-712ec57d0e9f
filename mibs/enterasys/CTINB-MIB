CTINB-MIB  DEFINITIONS ::= BEGIN
 
        --  ctinb-info-mib
        --  Revision:   01.00.00
        --  Date: October 14, 1996
 
        --  Cabletron Systems, Inc.
        --  35 Industrial Way, P.O. Box 5005
        --  Rochester, NH 03867-0505
        --  (603) 332-9400
        --  <EMAIL>
 
        --
        --  This module will be extended, as needed.
        --
 
        --  Cabletron Systems reserves the right to make changes in
        --  specification and other information contained in this document
        --  without prior notice.  The reader should consult Cabletron Systems
        --  to determine whether any such changes have been made.
        --
        --  In no event shall Cabletron Systems be liable for any incidental,
        --  indirect, special, or consequential damages whatsoever (including
        --  but not limited to lost profits) arising out of or related to this
        --  document or the information contained in it, even if Cabletron
        --  Systems has been advised of, known, or should have known, the
        --  possibility of such damages.
        --
        --  Cabletron grants vendors, end-users, and other interested parties
        --  a non-exclusive license to use this Specification in connection
        --  with the management of Cabletron products.
 
        --  Copyright August 96 Cabletron Systems

        IMPORTS 
                Counter, TimeTicks
                        FROM RFC1155-SMI
                OBJECT-TYPE
                        FROM RFC-1212
                ctINBinfo 
                        FROM CTRON-MIB-NAMES ;


--
-- The inbMonarchsystem table represents the Monarch(INB mgr) information
-- on a per INB basis
--
--
        inbMonarchSystem      OBJECT IDENTIFIER ::= { ctINBinfo 1 }

        inbMonarchSystemTable  OBJECT-TYPE 
            SYNTAX  SEQUENCE OF InbMonarchSystemEntry
            ACCESS  not-accessible
            STATUS  mandatory
            ::= { inbMonarchSystem 1 }

        inbMonarchSystemEntry  OBJECT-TYPE 
            SYNTAX  InbMonarchSystemEntry
            ACCESS  not-accessible
            STATUS  mandatory
            INDEX   { inbMonarchINB }
            ::= { inbMonarchSystemTable 1 }

        InbMonarchSystemEntry ::=
            SEQUENCE { 
                inbMonarchSystemINB
                    INTEGER,
                inbMonarchStatusTimeStamp
                    TimeTicks,
                inbMonarchBandwidth
                    INTEGER,
                inbMonarchTDMSlotMode
                    INTEGER,
                inbMonarchTDMSlotTotal
                    INTEGER,
                inbMonarchSystemTDMSlotActual
                    INTEGER,
                inbMonarchTDMSlotbandwidth
                    INTEGER
          }

        inbMonarchSystemINB  OBJECT-TYPE
            SYNTAX  INTEGER{
               inbA(1),
               inbB(2)
             }
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION 
                    "Two physical INB's may exist on a module. This
                    object distinquishes which INB, INB-A or INB-B."
            ::= { inbMonarchSystemEntry 1 }

         inbMonarchStatusTimeStamp OBJECT-TYPE
            SYNTAX  TimeTicks
            ACCESS  read-only
         STATUS  mandatory
         DESCRIPTION
                    "This object represents the value of sysUptime when
                     the Monarch last changed."
            ::= { inbMonarchSystemEntry 2 }
 
         inbMonarchBandwidth OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "The bandwidth of this INB in Megabits."
            ::= { inbMonarchSystemEntry 3 }

         inbMonarchTDMSlotMode OBJECT-TYPE
            SYNTAX  INTEGER {
                  automatic(1),
                  userPolicy(2)
               }
            ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                    "The automatic TDM mode overides any preset TDM
                     allocation. All boards get an equal number of 
                     pre-allocated TDM slots, adding up to 100% of total
                     INB bandwidth, and have Round Robin enabled. The
                     automatic mode will, in effect, provide each board
                     with a 1/(number of boards) minimum guarantee
                     INB bandwidth.
 
                     The userPolicy mode will have the per board policy
                     enforced. Each board will have a level of service
                     (TDM slots, Round Robin arbitration from the 
                     inbMonarchTable) associated with it to take effect
                     when this object is set to user_policy. Newly
                     inserted boards will default to Round Robin and
                     share the remaining fixed INB bandwidth
                     (inbMonarchTDMSlotTotal - inbMonarchTDMSlotActual)."
            ::= { inbMonarchSystemEntry 4 }

        inbMonarchTDMSlotTotal OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                    "This object represents the total possible number of INB
                     backplane TDM slots."
            ::= { inbMonarchSystemEntry 5 }

        inbMonarchSystemTDMSlotActual OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                    "This object represents the total number of allocated INB
                     backplane TDM slots. This is the sum of all instances of
                     inbMonarchTDMSlotRequest in userPolicy mode. In automatic
                     mode this number would represent the number of inserted
                     boards."
            ::= { inbMonarchSystemEntry 6 }

        inbMonarchTDMSlotbandwidth OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                    "This object represents the bandwidth in bits that
                     each slot represents on the backplane."

            ::= { inbMonarchSystemEntry 7 }
--
-- The inbMonarchTable table represents the Monarch(INB mgr) configuration information
-- on a per board/slot basis
--
--
        inbMonarch            OBJECT IDENTIFIER ::= { ctINBinfo 2 }

        inbMonarchTable  OBJECT-TYPE 
            SYNTAX  SEQUENCE OF InbMonarchEntry
            ACCESS  not-accessible
            STATUS  mandatory
            ::= { inbMonarch 1 }

        inbMonarchEntry  OBJECT-TYPE 
            SYNTAX  InbMonarchEntry
            ACCESS  not-accessible
            STATUS  mandatory
            INDEX   { inbMonarchSlot,
                      inbMonarchINB }
            ::= { inbMonarchTable 1 }

        InbMonarchEntry ::=
            SEQUENCE { 
                inbMonarchSlot
                    INTEGER,
                inbMonarchINB
                    INTEGER,
                inbMonarchStatus
                    INTEGER,
                inbMonarchLinkStatus
                    INTEGER,
                inbMonarchLinkCapacity
                    INTEGER,
                inbMonarchTDMSlotRequest
                    INTEGER,
                inbMonarchTDMSlotActual
                    INTEGER,
                inbMonarchRoundRobinControl
                    INTEGER
            }

        inbMonarchSlot  OBJECT-TYPE 
            SYNTAX  INTEGER
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "The slot number containing this module."
            ::= { inbMonarchEntry 1 }

        inbMonarchINB  OBJECT-TYPE 
            SYNTAX  INTEGER{
               inbA(1),
               inbB(2)
             }
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Two physical INB interfaces may exist on a module. This
                    object distinquishes which INB, INB-A or INB-B."
            ::= { inbMonarchEntry 2 }

        inbMonarchStatus  OBJECT-TYPE 
            SYNTAX  INTEGER {
               standBy(1),
               sysUndefined(2),
               operational(3)
             }
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION  
                    "Application state of the monarch application on this
                    module. Standby - indicates this module is not the
                    monarch but can be. SysUndefined - indicates this
                    module can not be monarch. Operational - says this
                    module is the monarch. "
            ::= { inbMonarchEntry 3 }

        inbMonarchLinkStatus OBJECT-TYPE
            SYNTAX  INTEGER {
               linkUp(1),
               linkDown(2)
             }
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION 
                    "This object describes the state of the backplane. LinkUp
                     is when this INB detects the clock on the backplane.
                     LinkDown is when no backplane clock has been detected."
            ::= { inbMonarchEntry 4 }

        inbMonarchLinkCapacity OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION 
                    "The bandwidth capacity of this board in Megabytes."
            ::= { inbMonarchEntry 5 }

 
        inbMonarchTDMSlotRequest OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-write
            STATUS  mandatory
            DESCRIPTION
                    "This object is the number of fixed TDM slots requested
                     for this board . NOTE: new request will take effect the
                     next time the inbMonarchTDMSlotMode object is set to
                     userPolicy. NOTE: this value has no meaning when the
                     inbMonarchTDMSlotMode is automatic."
            ::= { inbMonarchEntry 6 }

        inbMonarchTDMSlotActual OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-write
            STATUS  mandatory
            DESCRIPTION
                    "This object is the actual number of fixed TDM slots
                     given to this board. In automatic mode, this value is the
                     total number of slots divided by the number of inserted
                     boards, in userPolicy mode, this reflects the
                     inbMonarchTDMSlotRequest value at the last time the
                     chassis entered userPolicy mode."
            ::= { inbMonarchEntry 7 }

        inbMonarchRoundRobinControl  OBJECT-TYPE 
            SYNTAX  INTEGER {
                        enabled(1),
                        disabled(2)
                    }
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "This object enables or disable this INB the ability to
                     participate in the Round Robin arbitration phase."
            ::= { inbMonarchEntry 8 }


--
--
-- inbstats are the basic per board INB statistics
--
--
--
--
--



        inbStats              OBJECT IDENTIFIER ::= { ctINBinfo 3 }

        inbStatsTable  OBJECT-TYPE 
            SYNTAX  SEQUENCE OF InbStatsEntry
            ACCESS  not-accessible
            STATUS  mandatory
            ::= { inbStats 1 }

        inbStatsEntry  OBJECT-TYPE 
            SYNTAX  InbStatsEntry
            ACCESS  not-accessible
            STATUS  mandatory
            INDEX   { inbStatsSlot,
                      inbStatsINB }
            ::= { inbStatsTable 1 }

        InbStatsEntry ::=
            SEQUENCE { 
                inbStatsSlot
                    INTEGER,
                inbStatsINB
                    INTEGER,
                inbStatsIfindex
                    INTEGER,
                inbStatsUniCastCells
                    Counter,
                inbStatsMultiCastCells
                    Counter,
                inbStatsBroadCastCells
                    Counter,
                inbStatsXmitCells
                    Counter,
                inbStatsRecvSeqErrs
                    Counter,
                inbStatsRecvChksumErrs
                    Counter,
                inbStatsxmitToFps
                    Counter,
                inbStatsToFpsDrops
                    Counter,
                inbStatsFromInbErrs
                    Counter,
                inbStatsToINBDrops
                    Counter,
                inbStatsToInbErrs
                    Counter
            }

        inbStatsSlot  OBJECT-TYPE 
            SYNTAX  INTEGER
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "The slot number containing this module."
            ::= { inbStatsEntry 1 }

        inbStatsINB  OBJECT-TYPE 
            SYNTAX  INTEGER {
                 inbA(1),
                 inbB(2)
             }
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Two physical INB interfaces may exist on a module. This
                    object distinquishes which INB, INB-A or INB-B."
            ::= { inbStatsEntry 2 }

        inbStatsIfindex OBJECT-TYPE
            SYNTAX  INTEGER
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "The interface number of the INB. The interface 
                     identified by a particular value of this
                     object is the same interface as identified by the
                     same value of the ifIndex object defined in RFC 1213."
            ::= { inbStatsEntry 3 }

        inbStatsUniCastCells  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of UniCast INB cells received from the backplane."
            ::= { inbStatsEntry 4 }

        inbStatsMultiCastCells  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of multi-cast INB cells received from the backplane."
            ::= { inbStatsEntry 5 }

        inbStatsBroadCastCells  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of broadcast INB cells received from the backplane."
            ::= { inbStatsEntry 6 }

        inbStatsXmitCells  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of INB cells transmitted to the backplane."
            ::= { inbStatsEntry 7 }


        inbStatsRecvSeqErrs  OBJECT-TYPE
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "Number of pkts with sequence errors received from
                     the backplane."
            ::= { inbStatsEntry 8 }

        inbStatsRecvChksumErrs  OBJECT-TYPE
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "Number of pkts with checksum errors received from
                     the backplane."
            ::= { inbStatsEntry 9 }

        inbStatsxmitToFps  OBJECT-TYPE
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "Number of transmit errors to FPS."
            ::= { inbStatsEntry 10 }

        inbStatsToFpsDrops  OBJECT-TYPE
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "INBC receive fifo full count. This represents the
                     number of cells that were not forwarded to the FPS."
            ::= { inbStatsEntry 11 }

        inbStatsFromInbErrs  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of INB receive errors from the backplane. This
                     includes FPSC xmit errors(inbStatsxmitToFps), INBC
                     receive sequence errors (inbStatsRecvSeqErrs)
                     and INBC checksum errors(inbStatsRecvChksumErrs)." 
            ::= { inbStatsEntry 12 }

        inbStatsToINBDrops  OBJECT-TYPE
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION
                    "Number of FPSC recv frame drop count. This represents
                     the number of frames that were not sent out on the INB
                     backplane."
            ::= { inbStatsEntry 13 }

        inbStatsToInbErrs  OBJECT-TYPE 
            SYNTAX  Counter
            ACCESS  read-only
            STATUS  mandatory
            DESCRIPTION  
                    "Number of INB backplane transmit errors. This includes
                     FPSC receive timeout errors"
            ::= { inbStatsEntry 14 }

END 
