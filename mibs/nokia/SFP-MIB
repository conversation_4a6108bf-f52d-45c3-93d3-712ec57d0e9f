-- SFP-MIB

-- ======================================================================
--  This specification is published by Alcatel-Lucent under Non-Disclosure
--  Agreement(s) (NDA) with specific parties and has to be considered as
--  Confidential Information as defined in such NDA.
--  Alcatel-Lucent reserves the right to revise this document for any reason,
--  including but not limited to conformity with standards promulgated by
--  various agencies, utilisation of advances in the state of the technical
--  areas, or the reflection of changes in the design of any equipment,
--  techniques, or procedures described or referred to herein.
--  The product specifications and other technical or performance information
--  contained herein are subject to change without notice.
--  Updates of this document will be issued under the above NDA's.
--  Alcatel-Lucent makes no representation or warranty, expressed or implied, with
--  respect to the sufficiency, accuracy, or utility of any information or
--  opinion contained herein. Alcatel-Lucent expressly advises that any use of for 
--  any purpose or reliance upon this technical reference is at the risk of
--  the user and that Alcatel-Lucent will not be liable for any damage or injury
--  incurred by any person arising out of the sufficiency, accuracy, or
--  utility of any information or opinion contained herein.
--  This document is not to be construed as a suggestion to any manufacturer
--  to modify or change any of its products, nor does this document represent
--  any commitment by Alcatel-Lucent to sell or purchase any product.
--  Nothing contained herein will be construed as conferring by implication,
--  estoppel, or otherwise any license or right under any patent, whether or
--  not the use of any information herein necessarily employs an invention of
--  any existing or later issued patent.
--  Alcatel-Lucent reserves the right not to offer any or all of these products and
--  to withdraw any or all of them at any future time.
--  Copyright (C) 2005-2009, Alcatel-Lucent. All Rights Reserved.
-- ===========================================================================

--  MODULE-IDENTITY
--  LAST-UPDATED ""20120213260000Z""
--  ORGANIZATION "Alcatel"
--  CONTACT-INFO 
--
--      "Email: <EMAIL>"
--
--  REVISION "20170307"
--  DESCRIPTION
--      Editor : <EMAIL>
--      Changes:
--        Update sfpInvSpecificalType with 1 new type:
--               cPlusPlusI(66)
--
--  REVISION "20161104"
--  DESCRIPTION
--      Editor : <EMAIL>
--      Changes:
--        Update sfpInvSpecificalType with 2 new types:
--                 base1000-ZR-CWDM(57),
--                 base10G-ZR(58)
--
--  REVISION "20151110"
--  DESCRIPTION
--      Editor :  <EMAIL> 
--      Changes: 
--        SfpDiagTable,SfpInventoryTable and SfpRSSIProfileTable are updated 
--        for CFP4 introduced with FANT-G."
--
--  REVISION "20120620"
--  DESCRIPTION
--      "Version:  3FE25209EAAA_V4.*******
--      Editor :  vanapatla ramana 
--      Changes: 
--          Alcatel notes updated for Atttributes  dsx1SfpGenConfigTributary1, 
--          dsx1SfpGenConfigTributary2,dsx1FramerAlarmStatus and 
--				dsx1SfpClkAlarmState as Not Supported." 
--
--  REVISION "20120607"
--  DESCRIPTION
--      "Version:  3FE25209EAAA_V4.*******
--      Editor :  vanapatla ramana 
--      Changes: 
--           	  Added SGMIII loop back support and corrected the range of GenConfigMarket."
--
--  REVISION "20120525"
--  DESCRIPTION
--      "Version:  3FE25209EAAA_V4.*******
--      Editor :  vanapatla ramana 
--      Changes: 
--           	  Added ALCATEL NOTES."
--
--  REVISION "20120524"
--  DESCRIPTION
--      "Version:  3FE25209EAAA_V4.*******
--      Editor :  vanapatla ramana 
--      Reason for change: ALU01794407
--              Range of supported values for  dsx1SfpLIUTxConfig is changed from 0-11
--		changed to 1-12 "
--
--  REVISION "20120213"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  vanapatla ramana 
--      Reason for change: ALU01279245, ALU01750853  :
--              Augmented tables dsx1GeneralConfigTable, dsx1PWTDMinterfaceTable 
--              from dsx1ClockConfigTable.Augmented tables dsx1FramerTable, 
-- 		dsx1LineInterfaceunitTable  from  dsx1PWTDMConfigTable. Fields "
--
--  DESCRIPTION  

--  "The SFP MIB contains twelve tables:
--        1.  The host SFP configuration table
--        2.  The host SFP configuration extension table
--        3.  The host SFP status table.
--        4.  The expansion shelf SFP status table.
--        5.  The SFP diagnostics table.
--        6.  The SFP Inventory Table.
--        7.  The dsx1 General Configuration Table.
--        8.  The dsx1 Clock Configuration Table.
--        9.  The dsx1 Line Interface Unit Table.
--        10. The dsx1 Framer Table.
--        11. The dsx1 pseudo wire configuration Table.
--        12. The dsx1 pseudo wire protocol  Table.
--        13. The dsx1 pseudo wire TDM interface Table."
--
--  REVISION "20120108"

--  REVISION "20120108"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  Janakiraman/Vanapatla Ramana 
--      Reason for change: "ALU01279245 : Management Integration of 2xE1/DS1- SFP in ISAM"
--               Introduced dsx1GeneralConfigTable, dsx1ClockConfigTable, 
--		 dsx1LineInterfaceunitTable, dsx1FramerTable, dsx1PWTDMConfigTable, 
--		 dsx1PWprotocolEngineTable and dsx1PWTDMinterfaceTable for 2xE1/DS1 SFP"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  Marita Saveyn 
--      Reason for change: ALU01336169 :
--               expSfpType, bit 16 (65536): 2500BASE-SX
--               expSfpType, bit 17 (131072): 2500BASE-LX"
--               expSfpType, bit 18 (262144): 2500BASE-BX"
--
--  REVISION "20110208"
--  DESCRIPTION
--      "Version: To be updated
--      Editor :  Deepak Rajaram/Gurusamy Dharmaraj 
--      Reason for change: weblib comments for 3HH-01154-DDAA-PBZZA"
--
--  REVISION "20110131"
--  DESCRIPTION
--      "Version: To be updated
--      Editor :  Deepak Rajaram/Gurusamy Dharmaraj 
--      Reason for change: ALU00122060/ALU00123022 :
--               Introduced sfpInventoryTable and updated 
--               new parameters in sfpDiagTable"
--
--  REVISION "20101004"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  Marita Saveyn 
--      Reason for change: ALU00805501 :
--               hostSfpType, bit 18 (262144): 2500BASE-BX"
--
--  REVISION "20100426"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  Marita Saveyn 
--      Reason for change: ALU00303338 :
--               hostSfpType, bit 16 (65536): 2500BASE-SX
--               hostSfpType, bit 17 (131072): 2500BASE-LX"
--
--  REVISION "20100331"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V*******.3
--      Editor :  Marita Saveyn
--      Reason for change:  correct type 2500GBASE into 2500BASE."
--
--  REVISION "20091214"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V4.*******
--      Editor :  Marita Saveyn
--      Reason for change:  add sfp type 2500GBASE."
--
--  REVISION "20090414"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V*******.0
--      Editor :  Bakri Aboukarr
--      Reason for change:  support newer SFP types."
--
--  REVISION "20080923"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Tom Van Peteghem
--      Reason for change:  
--       update for IHUB mib changes."
--
--  REVISION "20080606"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Bakri Aboukarr 
--      Reason for change:  weblib comments made on 3HH-01154-CHAA-PBZZA.
--      Changes:
--       1.  Remove all smart quotes
--       2.  Change 0.00 mW to No Power."
--
--  REVISION "20071005"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Larry Boone
--      Reason for change:  weblib comments made on 3HH-01154-CGAA-PBZZA.
--      Changes:
--       1.  Revise the DESCRIPTION text for the 'Invalid' display string, 
--        to match the wording in MIB versions for prior releases.
--       2.  Change the DESCRIPTION text for sfpDiagSfpFaceplateNumber - cages
--        numbered zero on the faceplate are indexed using 
--        sfpDiagSfpFaceplateNumber = 0."
--
--  REVISION "20070930"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Larry Boone
--      Reason for change:  ANTms33137, 'SFP Digital Diagnostics (2nd part)'.
--      Changes:
--       1.  Move SFP Diagnostics assembly and release details from the 
--        sfpDiagTable ASN.1 DESCRIPTION fields to the non-ASN.1 text.  That change 
--        was requested in weblib comment 408170 (3HH-01154-CEAA-PBZZA), and was 
--        agreed with Raf van Driessche 2-Jul-2007.
--       2.  Port the (minor) ASN.1 DESCRIPTION changes for ANTms59215 from 
--        3HH-01154-CEAA-PBZZA to this version.  In summary, set the sfpDiagTxPower 
--        and sfpDiagRxPower DisplayStrings to 'Invalid' if erroneous external 
--        calibration constants yields a negative value, in milliWatts.  Similarly, 
--        set the sfpDiagTxBiasCurrent and sfpDiagSupplyVoltage DisplayStrings to 
--        'Invalid', rather than display negative values (in mA and VDC, 
--        respectively).
--       3.  Undo name changes for hostSfpDownlinkStatus code points (e.g. change 
--        devNotPresent back to sfpNotPresent), as requested by AMS."
--       
--
--  REVISION "20070720"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Larry Boone
--      Reason for change:  ANTms02125, 'Remotes Aggregator' (ERAM-A).
--      Changes:
--        1.  Fix minor MIB syntax problems.
--        2.  Make the ALCATEL NOTE RANGE values for the 
--            hostSfpCableToSlot and hostSfpCabledToSlot objects the same."
--
--  REVISION "20070713"
--  DESCRIPTION
--      "Version: 3FE25209EAAA_V3.*******
--      Editor :  Larry Boone
--      Reason for change:  ANTms02125, 'Remotes Aggregator' (ERAM-A).
--      Changes:
--        1.  Add support for XFP Type (e.g. 10GBASE_LR) by increasing the range
--        of hostSfpType.
--        2.  Define a new numbering plan for XFP cages in hostSfpStatusTable.
--        3.  Revise the SFP-specific code point names in hostSfpDownlinkStatus
--        to make them applicable to XFPs as well.
--        4.  Correct the PERSIST value for hostStatusSfpFaceplateNumber."

--  REVISION "20070601"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor:  Bo Liu
--      Changes:  updated for FDT591 - remote aggregator.
--              The sizes are extended for:
--               - hostSfpConfigTable
--               - hostSfpConfigExtTable
--               - hostSfpStatusTable
--               - expSfpStatusTable
--               - lanxPortNumber
--               The ranges of xxxSfpFaceplateNumbers are extended."
--
--  REVISION "20070516"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor:  Larry Boone
--      Changes:  change the DESCRIPTION of the sfpDiagTemperature 
--        DisplayString object - the format of the typical object 
--        value is now '-128.00 degrees Celsius' (was '-128.00 C')."
--
--  REVISION "20070423"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor:  Larry Boone
--      Changes:  correct the DESCRIPTION of the sfpDiagTxBiasCurrent 
--        object - two decimal places of precision are required."
--
--  REVISION "20070420"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor:  Larry Boone
--      Changes:  
--      a) Change the indexing of the two SFP cages of an FD-REM in the 
--         sfpDiagTable (BDFhw64555) from
--            <FD-REM_LT_Slot#1, cage=1>, 
--            <FD-REM_LT_Slot#2, cage=1>
--         to
--            <FD-REM_controllerCardSlot, cage=1>, 
--            <FD-REM_controllerCardSlot, cage=2>. 
--         Only DESCRIPTION fields are affected. 
--      b) Delete swError from the sfpDiagAvailable enum.  If a software error 
--         occurs when retrieving SFP diagnostics, return the SNMP error code 
--         'genError' in the SNMP error-status field.   
--      c) Rename the object sfpDiagCageNum to sfpDiagSfpFaceplateNumber, for
--         consistency with hostConfigSfpFaceplateNumber, 
--         hostConfigExtSfpFaceplateNumber, and hostStatusSfpFaceplateNumber."   
--
--  REVISION "20070308"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor:  Larry Boone
--      Changes: Added the sfpDiagTable (BDFhw64555) 
--               Change the size of the hostSfpConfigTable (ANTms30627)"
--
--  REVISION "20061012"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E3.3.0.0
--      Editor: John Chuah
--      Changes: Added hostSfpCablingMismatch and
--               hostSfpCabledToPhysAddress to HostSfpStatusEntry.
--               Increased the maximum rack value from 6 to 7
--               and the maximum shelf value from 3 to 4."
--
--  REVISION "20060915"
--  DESCRIPTION
--      "Version: 3FE25467AAAA_V3.*******
--      Editor: Zhu Jianhua
--      Changes: 
--               update the ranges of hostStatusSfpFaceplateNumber,
--               hostSfpFaceplateNumber, hostConfigExtSfpFaceplateNumber,
--               hostConfigSfpFaceplateNumber for 7330FD
--               Added 0 to the ranges of rack, shelf and slot."
--
--  REVISION "20060505"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_V3.*******
--      Editor: Bo Liu, Marc Van Vlimmeren
--      Changes: 
--               Added ALCATEL NOTES."
--
--  REVISION "20051121"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E3.0.0.0
--      Editor: Vairamuthu Karuppiah
--      Changes: Updated for equipment dimensioning changes to 6 
--                      racks and 4 shelves"
--
--  REVISION "20050815"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.1.3
--      Editor: Bo Liu
--           Changes: update the document based on CRs / comments in Weblib."
--
--  REVISION "20050614"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.1.2
--      Editor: Bo Liu
--      Changes: update after review (based on comments in Weblib)."
--
--  REVISION "20050513"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.1.1
--      Editor: Bo Liu
--      Changes: update after review (based on comments in Weblib)."
--
--  REVISION "20050509"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.1.0
--      Editor: Bo Liu
--      Changes: update after review (based on comments in Weblib)."
--
--  REVISION "20050502"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.0.1
--      Editor: Bo Liu
--      Changes: update after review (based on comments in Weblib)."
--
--  REVISION "20050421"
--  DESCRIPTION
--      "Version: 3FE25209AAAA_E2.3.0.0
--      Editor: Bo Liu
--      Changes: creation of the MIB"
-- ============================================================================


SFP-MIB DEFINITIONS ::= BEGIN


IMPORTS
        
        Counter, TimeTicks
                 FROM RFC1155-SMI
        OBJECT-TYPE
                  FROM RFC-1212
        PhysAddress
                        FROM RFC1213-MIB
        ifIndex
                 FROM ITF-MIB
        AsamProfileIndex     FROM ASAM-TC-MIB
        AsamProfilePointer   FROM ASAM-TC-MIB
       	AsamProfileName      FROM ASAM-TC-MIB
        AsamProfileRefCount  FROM ASAM-TC-MIB
        eqptSlotId,
        EqptSlotIndex        FROM ASAM-EQUIP-MIB
        RowStatus
                 FROM SNMPv2-TC

        DisplayString  FROM SNMPv2-TC
	MacAddress     FROM BRIDGE-MIB

        asam        FROM SYSTEM-MIB;
  
-- type definitions  

AsamNextProfileIndex ::= INTEGER(0..65535)
AsamMaxProfileIndex ::= INTEGER(0..65535)

SfpDiagTCAStatus ::= INTEGER 
          {
                 notAvailable(1),
                 normal(2),
                 exceededLowWarning(3),
                 exceededLowAlarm(4),
                 exceededHighWarning(5),
                 exceededHighAlarm(6)
           }

-- TEXTUAL-CONVENTION
-- STATUS       current
-- DESCRIPTION
--     "Diagonostics Threshold Crossing Alert status for temperature /
--     voltage / RX power / TX power / bias current, available in
--     SFP/SFP+/XFP EEPROM
--     Note : Two thresholds (warning and alarm level) are preset in
--     SFP/SFP+/XFP EEPROM during manufactor. Changing the threshold by
--     operator is not supprted
--     Possible values :
--     notAvailable : This value will be set when sfpDiagAvailable is set
--     other than noError, ie, retrieval of diagnostics is failed
--     Following values are applicable only when sfpDiagAvailable is
--     set to noError, ie, retrieval of diagnostics is success
--     normal : No threshold crossing, present value is within the threshold
--     exceededLowWarningThreshold : Present value is greater than the low
--     warning level threshold
--     exceededLowAlarmThreshold : Present value is greater than the low
--     alarm level threshold
--     exceededHighWarningThreshold : Present value is greater than the high
--     warning level threshold
--     exceededHighAlarmThreshold : Present value is greater than the high
--     alarm level threshold"

sfpMIB                   OBJECT IDENTIFIER ::= {asam 56}


-- SFP MIB

--  ==================================================================
--  |           Start of Host SFP Configuration Table                |
--  ==================================================================

hostSfpConfigTable       OBJECT-TYPE
        SYNTAX           SEQUENCE OF HostSfpConfigEntry
        ACCESS            not-accessible
        STATUS            mandatory
        DESCRIPTION     
        "This table contains the host shelf SFP direction configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 8 
         "
::= {sfpMIB 1}

hostSfpConfigEntry       OBJECT-TYPE
        SYNTAX           HostSfpConfigEntry
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "Each entry consists of a list of host shelf SFP configuration parameters."
        INDEX           {hostConfigSfpFaceplateNumber}
::= {hostSfpConfigTable 1}

HostSfpConfigEntry ::=
        SEQUENCE {
                 hostConfigSfpFaceplateNumber INTEGER,
                 hostSfpDirection             INTEGER
        }

hostConfigSfpFaceplateNumber  OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is host shelf SFP number on the faceplate. 
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1..17
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpConfigEntry 1}

hostSfpDirection         OBJECT-TYPE
        SYNTAX           INTEGER {
                              uplink (1),
                              downlink (2)
                         }
        ACCESS           read-write
        STATUS           mandatory
        DESCRIPTION     
        "The host shelf SFP can be configured as downlink or uplink. The default 
         is uplink.
         ALCATEL NOTE:
            ACCESS: NA
            USAGE: OPT
            PERSIST: YES 
            INSRVMOD: YES
            RANGE: NA
            DEFVALUE: uplink
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
         "
--         DEFVAL {1}
::= {hostSfpConfigEntry 2}

--  ==================================================================
--  |      Start of Host SFP Configuration Extension Table           |
--  ==================================================================

hostSfpConfigExtTable    OBJECT-TYPE
        SYNTAX           SEQUENCE OF HostSfpConfigExtEntry
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "This table contains the host shelf SFP configuration information 
         additional to the host SFP direction configuration.
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead.
        ALCATEL NOTE:
            TABLESIZE: 24 
        "
::= {sfpMIB 2}

hostSfpConfigExtEntry    OBJECT-TYPE
        SYNTAX           HostSfpConfigExtEntry
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "Each entry consists of a list of host shelf SFP configuration 
         parameters which supplements the parameters in hostSfpConfigTable.
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead."
        
        INDEX           {hostConfigExtSfpFaceplateNumber}
::= {hostSfpConfigExtTable 1}

HostSfpConfigExtEntry ::=
        SEQUENCE {
                 hostConfigExtSfpFaceplateNumber INTEGER,
                 hostSfpCableToRack              INTEGER,
                 hostSfpCableToShelf             INTEGER,
                 hostSfpCableToSlot              INTEGER
        }

hostConfigExtSfpFaceplateNumber   OBJECT-TYPE
        SYNTAX                    INTEGER
        ACCESS                    read-only
        STATUS                    mandatory
        DESCRIPTION     
        "This is the host shelf SFP number on the faceplate. 
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead.
         ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1..24
            DEFVALUE: NA
            UNITS: NA
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpConfigExtEntry 1}

hostSfpCableToRack       OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-write
        STATUS           mandatory
        DESCRIPTION
        "This is the Rack ID of the expansion shelf LSM which the host shelf SFP
         is planned to cable to. Value 0 means that no rack is specified.
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead.
        ALCATEL NOTE:
            ACCESS: NA
            USAGE: OPT
            PERSIST: YES
            INSRVMOD: YES
            RANGE: 0..7
            DEFVALUE: 0
            UNITS: NA
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpConfigExtEntry 2}

hostSfpCableToShelf   OBJECT-TYPE
        SYNTAX          INTEGER
        ACCESS          read-write
        STATUS          mandatory
        DESCRIPTION
         "This is the Shelf ID of the expansion shelf LSM which
         the host shelf SFP is planned to cable to.
         Value 0 means that no shelf is specified.
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead.
        ALCATEL NOTE:
            ACCESS: NA
            USAGE: OPT
            PERSIST: YES 
            INSRVMOD: YES
            RANGE: 0..4
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: The shelf identified by hostSfpCableToRack and
                   hostSfpCableToShelf must already be planned.
        "
::= {hostSfpConfigExtEntry 3}

hostSfpCableToSlot       OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-write
        STATUS           mandatory
        DESCRIPTION     
        "This is the Slot ID of the expansion shelf LSM which
        the host shelf SFP is planned to cable to.
        Value 0 means that no slot is specified.
        WARNING This table is now deprecated. ltHostSfpConfigExtTable should be used instead.
        ALCATEL NOTE:
            ACCESS: NA
            USAGE: OPT
            PERSIST: YES 
            INSRVMOD: YES
            RANGE: DEP / depends on the shelf type. 
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpConfigExtEntry 4}

--  ==================================================================
--  |               Start of Host SFP Status Table                   |
--  ==================================================================

hostSfpStatusTable      OBJECT-TYPE
      SYNTAX            SEQUENCE OF HostSfpStatusEntry
          ACCESS            not-accessible
          STATUS            mandatory
          DESCRIPTION     
          "This table contains the host shelf SFP or XFP status information.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
           
        ALCATEL NOTE:
            TABLESIZE: 26
        "
::= {sfpMIB 3}

hostSfpStatusEntry      OBJECT-TYPE
        SYNTAX          HostSfpStatusEntry
        ACCESS          not-accessible
        STATUS          mandatory
        DESCRIPTION
          "Each entry consists of a list of SFP or XFP status parameters.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead."
        INDEX           {hostStatusSfpFaceplateNumber}
::= {hostSfpStatusTable 1}

HostSfpStatusEntry ::=
        SEQUENCE {
                 hostStatusSfpFaceplateNumber  INTEGER,
                 lanxPortNumber                INTEGER,
                 hostSfpDownlinkStatus         INTEGER,
                 hostSfpCabledToRack           INTEGER,
                 hostSfpCabledToShelf          INTEGER,
                 hostSfpCabledToSlot           INTEGER,
                 hostSfpType                   INTEGER,
                 hostSfpCablingMismatch        INTEGER,
                 hostSfpCabledToPhysAddress    PhysAddress
        }

hostStatusSfpFaceplateNumber OBJECT-TYPE
        SYNTAX               INTEGER
        ACCESS               read-only
        STATUS               mandatory
        DESCRIPTION     
        "This is the host shelf SFP or XFP cage number.
         For an SFP cage, it is the faceplate number of the SFP cage (e.g. 1).
         For an XFP cage, it is 256 + (the faceplate number of the XFP cage).  For
         example, if the XFP cage has faceplate label X1, 
         hostStatusSfpFaceplateNumber == 257.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..258
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 1}

lanxPortNumber           OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        " This is the Physical LANX or IHUB port to which the SFP or XFP is 
         connected to. For an SHUB system, the lanxPortNumber here corresponds 
         to lanxInterfaceIndex in the LANX MIB. For an IHUB system, the 
         lanxPortNumber to the TmnxPortID to be used in the Timetra Port MIB.
         For IHUB this is an 32 bit encoded number for which the structure is
         defined in Timetra-TC-MIB.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..26 for SHUB / 32 Bit Encoded number for IHUB
            DEFVALUE: DEP / depends on the hostStatusSfpFaceplateNumber.
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 2}

hostSfpDownlinkStatus OBJECT-TYPE
          SYNTAX INTEGER {
                            ok (1),
                            sfpNotPresent (2),
                            los (3),
                            txFail (4),
                            invalidAlcatelId (5),
                            unknown (6),
                            sfpControlFail (7),
                            notApplicable (8),
                            txFailAndLos (9),
                            sfpPlanDetectMismatch (10)
                        }
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "This is the host shelf SFP or XFP status.
            ok (1):                     The SFP or XFP is operational.
            sfpNotPresent (2):          The SFP or XFP cage is empty.
            los (3):                    The SFP or XFP has detected Loss of Signal. 
            txFail (4):                 The SFP or XFP has detected Transmitter 
                                        Fail.
            invalidAlcatelId (5):       The SFP or XFP does not contain
                                        a valid Alcatel-Lucent ID.
            unknown (6):                The host expansion card is planned but
                                        not inserted.
            sfpControlFail (7):         The SFP or XFP is not responding, or the 
                                        I2C read failed.
            notApplicable (8):          Retrieval of status information from the
                                        SFP or XFP cage is not supported.
            txFailAndLos (9):           The SFP or XFP has detected both 
                                        Transmitter Fail and Loss of Signal.
            sfpPlanDetectMismatch (10): Not used - superseded by the
                                        hostSfpCablingMismatch object.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 6 / status is unknown
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 3}

hostSfpCabledToRack      OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Rack ID of the expansion shelf LSM to which the host shelf SFP
         is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..7
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
           DESCR: NA
        "
::= {hostSfpStatusEntry 4}

hostSfpCabledToShelf     OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Shelf ID of the expansion shelf LSM which the host shelf 
         SFP is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..4
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 5}

hostSfpCabledToSlot   OBJECT-TYPE
        SYNTAX            INTEGER
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION     
        "This is the Slot ID of the expansion shelf LSM which the host shelf SFP 
         is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: DEP / depends on the shelf type. 
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 6}

hostSfpType              OBJECT-TYPE
        SYNTAX            INTEGER 
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION     
        "This variable indicates the type of the SFP or XFP, as read from the 
        device.
        It is a bitmap represented as a sum, therefore, it can represent
        multiple types (combination of types) simultaneously.
        The SFP type bits (0..7)  are as defined in standard SFF-8472.
        The XFP type bits (8..15) are as defined in standard INF-8077_v4.5.
        The SFP octet and the XFP octet are mutually exclusive (i.e. at least 
        one of the two octets is always zero).
        The various bit positions are:
             bit  0 (1):     1000BASE-SX
             bit  1 (2):     1000BASE-LX
             bit  2 (4):     1000BASE-CX
             bit  3 (8):     1000BASE-T
             bit  4 (16):    100BASE-LX/LX10
             bit  5 (32):    100BASE-FX
             bit  6 (64):    BASE-BX10
             bit  7 (128):   BASE-PX
             bit  8 (256):   reserved for a future XFP type, INF-8077
             bit  9 (512):   10GBASE-EW
             bit 10 (1024):  10GBASE-LW
             bit 11 (2048):  10GBASE-SW
             bit 12 (4096):  10GBASE-LRM
             bit 13 (8192):  10GBASE-ER
             bit 14 (16384): 10GBASE-LR
             bit 15 (32768): 10GBASE-SR
             bit 16 (65536):  2500BASE-SX
             bit 17 (131072): 2500BASE-LX
             bit 18 (262144): 2500BASE-BX
        If the hostSfpType is unknown or not specified, hostSfpType = 0.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..65535
            DEFVALUE: 0 / hostSfpType unknown 
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {hostSfpStatusEntry 7}

hostSfpCablingMismatch   OBJECT-TYPE
        SYNTAX           INTEGER {
                            noMismatch(1),
                            unexpectedRemoteLt(2),
                            assignmentMismatch(3),
                            incompatibleShelf(4)
                         }
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
            "This is the host shelf SFP cabling mismatch status:
            noMismatch(1):            no cabling mismatch is detected.
            unexpectedRemoteLt(2):    a remote LT is detected at an unassigned
                                      downlink SFP port.
            assignmentMismatch(3):    the detected remote LT does not match
                                      the LT assigned to this host SFP.
            incompatibleShelf(4):     the detected remote LT is in a shelf
                                      type which is not the planned shelf type.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
            ALCATEL NOTE: 
                ACCESS:   RO
                USAGE:    NA
                PERSIST:  NO 
                INSRVMOD: NA
                RANGE:    NA
                DEFVALUE: 1 / noMismatch
                UNITS:    NA
                SPARSE:   NO
                DESCR:    NA
            "
::= {hostSfpStatusEntry 8}

hostSfpCabledToPhysAddress      OBJECT-TYPE
        SYNTAX                  PhysAddress
        ACCESS                  read-only
        STATUS                  mandatory
        DESCRIPTION
            "The physical (MAC) address of the remote interface that is
            cabled to this host shelf SFP.
            If the remote interface does not have such an address or
            the address is not yet discovered, this object would
            contain an octet string of zero length.
        WARNING This table is now deprecated. ltHostSfpStatusTable should be used instead.
            ALCATEL NOTE: 
                ACCESS:   RO
                USAGE:    NA
                PERSIST:  NO 
                INSRVMOD: NA
                RANGE:    NA
                DEFVALUE: NA
                UNITS:    NA 
                SPARSE:   NO
                DESCR:    NA
        "
::= {hostSfpStatusEntry 9}

--  ==================================================================
--  |         Start of Expansion Shelf SFP Status Table              |
--  ==================================================================

expSfpStatusTable        OBJECT-TYPE
        SYNTAX           SEQUENCE OF ExpSfpStatusEntry
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "This table contains the expansion shelf SFP status information.
        ALCATEL NOTE:
            TABLESIZE: 312  (24 + 288)
        "
::= {sfpMIB 4}

expSfpStatusEntry        OBJECT-TYPE
        SYNTAX           ExpSfpStatusEntry
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "Each entry consists of the expansion shelf SFP status."
        INDEX            {expSfpRack,expSfpShelf,expSfpSlot}
::= {expSfpStatusTable 1}

ExpSfpStatusEntry ::=
        SEQUENCE {
                 expSfpRack               INTEGER,
                 expSfpShelf              INTEGER,
                 expSfpSlot               INTEGER,
                 expSfpStatus             INTEGER,
                 hostSfpFaceplateNumber   INTEGER,
                 expSfpType               INTEGER,
                 hostSfpLogSlotId         EqptSlotIndex
        }

expSfpRack               OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Rack ID of the expansion shelf SFP (actually its 
         corresponding LSM).
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 1}

expSfpShelf              OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Shelf ID of the expansion shelf SFP (actually 
         its corresponding LSM).
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..4
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 2}

expSfpSlot               OBJECT-TYPE
        SYNTAX            INTEGER
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION     
        "This is the Slot ID of the expansion shelf SFP (actually its 
         corresponding LSM).
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: DEP / depends on the shelf type. 
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 3}

expSfpStatus             OBJECT-TYPE
        SYNTAX           INTEGER {
                             ok (1),
                             sfpNotPresent (2),
                             los (3),
                             txFail (4),
                             invalidAlcatelId (5),
                             unknown (6),
                             sfpControlFail (7),
                             txFailAndLos (8)
                         }
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the status of the expansion shelf SFP:
             ok (1):                 the expansion shelf SFP is operational.
             sfpNotPresent (2):      no SFP present.
             los (3):                LOS is detected by the expansion shelf SFP.
             txFail (4):             the expansion shelf TX failed.
             invalidAlcatelId (5):   the expansion shelf SFP plugged does not 
                                     have a valid Alcatel ID.
             unknown (6):            the status is not available or cannot 
                                     be retrieved.
             sfpControlFail (7):     SFP not responding or I2C failure.
             txFailAndLos (8):       the expansion shelf TX failed and LOS is
                                     detected by the expansion shelf SF 
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 6 / unknown
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 4}

hostSfpFaceplateNumber   OBJECT-TYPE
        SYNTAX            INTEGER
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION
        "This is the faceplate number of the host shelf SFP which the expansion
         shelf SFP is cabled to. If an expansion shelf SFP is not cabled to any
         host shelf SFP, the value of the corresponding hostSfpFaceplateNumber
         is set to 0.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO
            INSRVMOD: NA
            RANGE: 0..24
            DEFVALUE: 0
            UNITS: NA
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 5}

expSfpType               OBJECT-TYPE
        SYNTAX            INTEGER
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION
        "This variable indicates the type of the SFP. It is a bitmap represented
         as a sum, therefore, it can represent multiple types (combination
         of types) simultaneously. The various bit positions are:
             bit 0 (1):  1000BASE-SX
             bit 1 (2):  1000BASE-LX
             bit 2 (4):  1000BASE-CX
             bit 3 (8):  1000BASE-T
             bit 4 (16): 100BASE-LX/LX10
             bit 5 (32): 100BASE-FX
             bit 6 (64): BASE-BX10
             bit 7 (128): BASE-PX
             bit 16 (65536):  2500BASE-SX
             bit 17 (131072): 2500BASE-LX
             bit 18 (262144): 2500BASE-BX             
        If the expSfpType is unknown or not specified, expSfpType = 0.
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 6}

hostSfpLogSlotId        OBJECT-TYPE
        SYNTAX            EqptSlotIndex
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION
        "This is the LogSlotId of the host LT which the expansion
         shelf SFP is cabled to. If an expansion shelf SFP is not cabled to any
         host shelf SFP, the value of the corresponding hostSfpLogSlotId
         is set to 0.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO
            INSRVMOD: NA
            RANGE: see above
            DEFVALUE: 0
            UNITS: NA
            SPARSE: NO
            DESCR: NA
        "
::= {expSfpStatusEntry 7}


--  ==================================================================
--  |                  Start of SFP Diag Table                       |
--  ==================================================================

-- sfpDiagTable definition

sfpDiagTable OBJECT-TYPE
        SYNTAX           SEQUENCE OF SfpDiagEntry
        ACCESS              not-accessible
        STATUS              mandatory
        DESCRIPTION     
        "The primary purpose of this read-only table:  allow the SNMP 
         manager to read real-time diagnostic measurements (e.g. the 
         SFP's received optical power level) from the A2 bank of a 
         manager-specified SFP.  The A2 bank and content are defined in 
         the SFF-8472 standard (available at ftp.seagate.com).  

         The table is indexed to allow support for any SFP cage in 
         the system.  

         In a particular release, SFP diagnostics support for a 
         particular assembly may or may not be provided, and support for 
         all the cages on a supported assembly may or may not be 
         provided.  If an assembly has any supported SFP cages, a row will be 
         present in this table for each of its SFP cages.  

         A row is present whether or not the SFP cage contains an SFP,
         and whether or not the SFP supports diagnostics.  If the SFP 
         cage contains an SFP, some status information (e.g. LOS and 
         TxFault status) is available in the row.  An enumerated object 
         (sfpDiagAvailable) is used to indicate the condition/error 
         (or no error) encountered when attempting to read the 
         diagnostics information for the specified cage (e.g. cage 
         empty, or A2 checksum failed). 

         Details are documented with the individual objects.

         The following table size field is set to 'not applicable', 
         since it does not make sense for the SNMP manager to cache the 
         values in this table.  The values will be different each time 
         they are read from agent.

         ALCATEL NOTE:
            TABLESIZE: NA
            DESCR:  NA 
        "
::= {sfpMIB 5}

-- List the two index columns of the sfpDiagTable.

sfpDiagEntry       OBJECT-TYPE
        SYNTAX           SfpDiagEntry       
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "The index for the sfpDiagTable identifies an SFP cage 
         using two fields:  the logicalSlotId of the card associated 
         with the SFP cage (normally the card with the cages on its 
         faceplate), and the SFP cage number."
        INDEX       {sfpDiagLogicalSlot, sfpDiagSfpFaceplateNumber}
::= {sfpDiagTable 1}

-- List all the columns in the sfpDiagTable.

SfpDiagEntry ::=
        SEQUENCE {
                 sfpDiagLogicalSlot INTEGER,
                 sfpDiagSfpFaceplateNumber INTEGER,
                 sfpDiagAvailable INTEGER, 
                 sfpDiagLOS INTEGER,
                 sfpDiagTxFault INTEGER,
                 sfpDiagTxPower DisplayString,
                 sfpDiagRxPower DisplayString,
                 sfpDiagTxBiasCurrent DisplayString,
                 sfpDiagSupplyVoltage DisplayString, 
                 sfpDiagTemperature DisplayString,
                 sfpDiagTCAStatusTemperature   SfpDiagTCAStatus,
                 sfpDiagTCAStatusVoltage    SfpDiagTCAStatus,
                 sfpDiagTCAStatusBiasCurrent SfpDiagTCAStatus,
                 sfpDiagTCAStatusTxPower SfpDiagTCAStatus,
                 sfpDiagTCAStatusRxPower SfpDiagTCAStatus,
                 sfpDiagRSSIProfileId INTEGER,
		 sfpDiagRSSIState INTEGER,
		 sfpDiagRSSIRxPowerAlmLow  DisplayString,
    		 sfpDiagRSSIRxPowerAlmHigh  DisplayString,
		 sfpDiagRSSIRxPowerWarnLow  DisplayString,
	    	 sfpDiagRSSIRxPowerWarnHigh  DisplayString,
    		 sfpDiagRSSITxPowerAlmLow  DisplayString,
    		 sfpDiagRSSITxPowerAlmHigh  DisplayString,
    		 sfpDiagRSSITxPowerWarnLow  DisplayString,
    		 sfpDiagRSSITxPowerWarnHigh  DisplayString,
    		 sfpDiagRSSITemperatureAlmLow  DisplayString,
    		 sfpDiagRSSITemperatureAlmHigh  DisplayString,
    		 sfpDiagRSSITemperatureWarnLow  DisplayString,
    		 sfpDiagRSSITemperatureWarnHigh  DisplayString,
    		 sfpDiagRSSIBiasAlmLow  DisplayString,
    		 sfpDiagRSSIBiasAlmHigh  DisplayString,
    		 sfpDiagRSSIBiasWarnLow  DisplayString,
    		 sfpDiagRSSIBiasWarnHigh  DisplayString,
    		 sfpDiagRSSIVoltageAlmLow  DisplayString,
    		 sfpDiagRSSIVoltageAlmHigh  DisplayString,
    		 sfpDiagRSSIVoltageWarnLow  DisplayString,
    		 sfpDiagRSSIVoltageWarnHigh  DisplayString,
		 sfpDiagRSSIExtRxPowerAlmLow  DisplayString,
    		 sfpDiagRSSIExtRxPowerAlmHigh DisplayString,
    		 sfpDiagRSSIExtRxPowerWarnLow DisplayString,
    		 sfpDiagRSSIExtRxPowerWarnHigh DisplayString,
    		 sfpDiagRSSIExtTxPowerAlmLow  DisplayString,
    		 sfpDiagRSSIExtTxPowerAlmHigh  DisplayString,
    		 sfpDiagRSSIExtTxPowerWarnLow  DisplayString,
    		 sfpDiagRSSIExtTxPowerWarnHigh  DisplayString,  
    		 sfpDiagRSSIExtBiasAlmLow  DisplayString,
    		 sfpDiagRSSIExtBiasAlmHigh  DisplayString,
    		 sfpDiagRSSIExtBiasWarnLow  DisplayString,
    		 sfpDiagRSSIExtBiasWarnHigh  DisplayString,
                 sfpDiagRSSIExtTemperatureAlmLow  DisplayString,
                 sfpDiagRSSIExtTemperatureAlmHigh  DisplayString,
                 sfpDiagRSSIExtTemperatureWarnLow  DisplayString,
                 sfpDiagRSSIExtTemperatureWarnHigh  DisplayString
}

-- Define the first index column of the sfpDiagTable.

sfpDiagLogicalSlot  OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the first index of the sfpDiagTable.  

         Normally, this object contains the Logical Slot ID (as defined in
         the Equipment MIB) of the card which has the SFP cage of interest
         on its faceplate.
         If that card does not have a Logical Slot ID, the Logical Slot ID
         of a related card is used.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1..65535
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {sfpDiagEntry 1}

-- Define the second (and final) index column of the sfpDiagTable.

sfpDiagSfpFaceplateNumber      OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the second index of the sfpDiagTable. 
         It is the number of the SFP cage, as shown on the card 
         faceplate.  If the faceplate cage number is zero, 
         sfpDiagSfpFaceplateNumber=0 is used (i.e. zero is not a
         special case).
         For an SFP cage, it is the faceplate number of the SFP cage 
         (e.g. 1).
         For an SFP+/XFP cage, it is 256 + the faceplate number 
         of the XFP cage.  For example, if the SFP+/XFP cage has 
         faceplate label X1, sfpInvFaceplateNumber = 257.
         For an QSFP cage, it is 512 + the faceplate number 
         of the QSFP cage.  For example, if the QSFP cage has 
         faceplate label Q1, sfpInvFaceplateNumber = 513.
         For a CFP4 cage, it is 768 + the faceplate number 
         of the CFP4 cage.  For example, if the CFP4 cage has 
         faceplate label C1, sfpInvFaceplateNumber = 769.

        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 0..769
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {sfpDiagEntry 2}

-- Define a non-index column of the sfpDiagTable.

sfpDiagAvailable    OBJECT-TYPE
        SYNTAX      INTEGER {
                      noError (1),
                      cageDoesNotSupportDiag(2),
                      cageEmpty (3),
                      cageDoesNotSupportA2 (4),
                      a0ReadFailed (5),
                      a0ChecksumFailed (6),
                      sfpDoesNotSupportA2 (7),
                      a2ReadFailed (8),
                      a2ChecksumFailed (9)
                    }
        ACCESS      read-only
        STATUS      mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, with the following code points.

         noError - valid values for all objects in this row (e.g. 
           sfpDiagRxPower) were obtained from the SFP in the specified 
           cage.
         cageDoesNotSupportDiag - the specified cage does not support 
           reading LOS, TxFault, or A2 measurements.  All subsequent values in the 
           row have notAvailable or 'Not Available' values. 
         cageEmpty - the specified SFP cage does not contain an SFP. 
           All subsequent values in the row have notAvailable or 
           'Not Available' values. 
         cageDoesNotSupportA2 - the specified SFP cage does not support 
           access to the SFP A2 bank (e.g. because of limitations in 
           FPGA support on the host card).  sfpDiagLOS and 
           sfpDiagTxFault have valid values (since they are implemented 
           using external signals, not SFP EEPROM values).
         a0ReadFailed - the attempt to read the A0 bank of the 
           specified SFP failed.  A0 is read to determine if A2 is 
           supported, so no attempt was made to read the A2 
           information.  sfpDiagLOS and sfpDiagTxFault have valid 
           values (since they are implemented using external signals, 
           not SFP EEPROM values).
         a0ChecksumFailed - the A0 bank of the SFP EEPROM is 
           corrupted.  sfpDiagLOS and sfpDiagTxFault have valid values 
           (since they are implemented using external signals, not SFP 
           EEPROM values).
         sfpDoesNotSupportA2 - the A0 bank of the SFP EEPROM indicates
           A2 is not supported.  sfpDiagLOS and sfpDiagTxFault have 
           valid values (since they are implemented using external 
           signals, not SFP EEPROM values). 
         a2ReadFailed - the attempt to read the A2 bank of the 
           specified SFP failed.  sfpDiagLOS and sfpDiagTxFault have 
           valid values (since they are implemented using external 
           signals, not SFP EEPROM values).
         a2ChecksumFailed - the A2 bank of the SFP EEPROM is 
           corrupted.  sfpDiagLOS and sfpDiagTxFault have valid values 
           (since they are implemented using external signals, not SFP 
           EEPROM values).
  

         ALCATEL NOTE:
           ACCESS: RO
           USAGE: NA
           PERSIST: NO 
           INSRVMOD: NO
           RANGE: NA
           DEFVALUE: NA
           UNITS: NA 
           SPARSE: NO
           DESCR: NA
       "
::= { sfpDiagEntry 3}

-- Define a non-index column of the sfpDiagTable.

sfpDiagLOS    OBJECT-TYPE
        SYNTAX      INTEGER {
                      los (1),
                      noLos (2),
                      notAvailable (3)
                    }
        ACCESS      read-only
        STATUS      mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, with the following code 
         points.

         los - the SFP in the specified SFP cage has detected Loss of 
           Signal (on its receiver).
         noLos - the SFP in the specified SFP cage has detected a
           received signal with a suitable power level.  
         notAvailable - the status of the received signal for the 
           specified SFP cage cannot be determined (e.g. because the 
           cage is empty - see sfpDiagAvailable).

         ALCATEL NOTE:
           ACCESS: RO
           USAGE: NA
           PERSIST: NO 
           INSRVMOD: NO
           RANGE: NA
           DEFVALUE: NA
           UNITS: NA 
           SPARSE: NO
           DESCR: NA
        "
::= { sfpDiagEntry 4}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTxFault    OBJECT-TYPE
        SYNTAX      INTEGER {
                      txFault (1),
                      noTxFault (2),
                      notApplicable (3),
                      notAvailable (4)
                    }
        ACCESS      read-only
        STATUS      mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, with the following code 
         points.

         txFault - the SFP in the specified SFP cage has detected a 
           TxFault error (as defined in SFF-8472).
         noTxFault - the SFP in the specified SFP cage has not detected 
           a TxFault error.
         notApplicable - the SFP is electrical, so TxFault is not 
           applicable (the detection technology is optical).
         notAvailable - the TxFault status of the specified SFP cage 
           cannot be determined (e.g. because the cage is empty - see 
           sfpDiagAvailable).

         ALCATEL NOTE:
           ACCESS: RO
           USAGE: NA
           PERSIST: NO 
           INSRVMOD: NO
           RANGE: NA
           DEFVALUE: NA
           UNITS: NA 
           SPARSE: NO
           DESCR: NA
        "
::= { sfpDiagEntry 5}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTxPower OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured 
         transmit power of the specified SFP, encoded into a string.

         Five types of values are supported.  In the following text, 
         strings are enclosed in single quotes (a double quote marks the 
         end of the DESCRIPTION field).  

         1.  '-40.00 dBm' to '8.16 dBm', in 0.01 dBm increments.  Note 
              that ' dBm' is included in the string.

         2.  'No Power'.  This is a special case because an SFP reading 
             of 0.00 mW cannot be converted to dBm (since log10(0.00) is  
             undefined). 

         3.  'Not Applicable'.  This is used for an electrical SFP.  The 
             technology for measuring transmitted power is optical.

         4.  'Not Available'.  The measurement could not be obtained  
             (e.g. the specified SFP cage is empty).

         5.  'Invalid'. The transmitted power, in mW, is negative (for example,
             due to incorrect external calibration constants in the SFP).  A
             negative mW value is not valid in this context, and cannot be
             converted to dBm.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 6}

-- Define a non-index column of the sfpDiagTable.

sfpDiagRxPower OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured 
         received power of the specified SFP, encoded into a string.

         Five types of values are supported.  In the following text, 
         strings are enclosed in single quotes (a double quote marks the 
         end of the DESCRIPTION field).  

         1.  '-40.00 dBm' to '8.16 dBm', in 0.01 dBm increments.  Note 
             that ' dBm' is included in the string.

         2.  'No Power'.  This is a special case because an SFP reading 
             of 0.00 mW cannot be converted to dBm (since log10(0.00) is  
             undefined).

         3.  'Not Applicable'.  This is used for an electrical SFP.  The 
             technology for measuring received power is optical.

         4.  'Not Available'.  The measurement could not be obtained  
             (e.g. the specified SFP cage is empty).

         5.  'Invalid'. The received power, in mW, is negative (for example,
             due to incorrect external calibration constants in the SFP).  A
             negative mW value is not valid in this context, and cannot be
             converted to dBm.   
      
         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
       "
::= { sfpDiagEntry 7}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTxBiasCurrent OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured 
         transmit bias current of the specified SFP, encoded into a 
         string.

         Four types of values are supported.  In the following text, 
         strings are enclosed in single quotes (a double quote marks the 
         end of the DESCRIPTION field).  

         1.  '0.00 mA' to '131.10 mA', in 0.01 mA increments.  Note that 
             ' mA' is included in the string.

         2.  'Not Applicable'.  This is used for an electrical SFP.  The 
             technology for measuring transmit bias current is optical.

         3.  'Not Available'.  The measurement could not be obtained  
             (e.g. the specified SFP cage is empty).

         4.  'Invalid'. The bias current, in mA, is negative (for example,
             due to incorrect external calibration constants in the SFP).  A
             negative mA value is not valid in this context.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO 
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 8}

-- Define a non-index column of the sfpDiagTable.

sfpDiagSupplyVoltage OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured 
         supply voltage of the specified SFP, encoded into a string.

         Three types of values are supported.  In the following text, 
         strings are enclosed in single quotes (a double quote marks the 
         end of the DESCRIPTION field).  

         1.  '0.00 VDC' to '6.55 VDC', in 0.01 VDC increments.  Note that 
             ' VDC' is included in the string.

         2.  'Not Available'.  The measurement could not be obtained  
             (e.g. the specified SFP cage is empty).

         3.  'Invalid'. The supply voltage, in VDC, is negative (for example,
             due to incorrect external calibration constants in the SFP).  A
             negative VDC value is not valid in this context.

         The supply voltage can be retrieved from an electrical SFP, so 
         there is no need for a 'Not Applicable' string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 9}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTemperature OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured 
         temperature of the specified SFP, encoded into a string.

         Two types of values are supported.  In the following text, 
         strings are enclosed in single quotes (a double quote marks the 
         end of the DESCRIPTION field).  

         1.  '-128.00 degrees Celsius' to '128.00 degrees Celsius', 
             in 0.01 degrees Celsius increments.  
             Note that ' degrees Celsius' is included in the string.

         2.  'Not Available'.  The measurement could not be obtained  
             (e.g. the specified SFP cage is empty).

         The temperature can be retrieved from an electrical SFP, so 
         there is no need for a 'Not Applicable' string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 10}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTCAStatusTemperature OBJECT-TYPE
        SYNTAX SfpDiagTCAStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured temperature 
        alarm/warning threshold crossing status of the specified SFP/SFP+/XFP/CFP4.
        
         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 11}

-- Define a non-index column of the sfpDiagTable.

sfpDiagTCAStatusVoltage OBJECT-TYPE
        SYNTAX SfpDiagTCAStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured voltage 
         alarm/warning threshold crossing status of the specified SFP/SFP+/XFP/CFP4.
         For XFP, this parameter will display as NotAvailable in case it is not 
         supported as one of the Auxillary measured value by XFP.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 12}

sfpDiagTCAStatusBiasCurrent OBJECT-TYPE
        SYNTAX SfpDiagTCAStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured bias current 
        alarm/warning threshold crossing status of the specified SFP/SFP+/XFP/CFP4.
        
         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 13}

sfpDiagTCAStatusTxPower OBJECT-TYPE
        SYNTAX SfpDiagTCAStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured received 
        power alarm/warning threshold crossing status of the specified SFP/SFP+/XFP/CFP4.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 14}

sfpDiagTCAStatusRxPower OBJECT-TYPE
        SYNTAX SfpDiagTCAStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpDiagTable, containing the freshly-measured transmit 
        power alarm/warning threshold crossing status of the specified SFP/SFP+/XFP/CFP4.
        
         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpDiagEntry 15}

sfpDiagRSSIProfileId OBJECT-TYPE
	SYNTAX 	INTEGER 
	ACCESS	read-write
	STATUS	mandatory
	DESCRIPTION 
		"Indicates the RSSI TCA profile, refer to SfpRSSIProfileTable.
		65535 means auto mode. When configured 65535, when sfp is plugged in, system will detect sfp type and associate related sfp rssi profile. User cannot modify the threshold values of this profile id.
	1-200 denotes user-defined profile where user can configure the RSSI threshold values.

ALCATEL NOTE:
		 ACCESS: NA  
                 usage: OPT
		 persistent: YES 
                 INSRVMOD:YES
                 RANGE:1..200,65535 
                 DEFVALUE: 65535 
                 UNITS: NA    
                 SPARSE: NA "
::= { sfpDiagEntry 16}

sfpDiagRSSIState OBJECT-TYPE
	SYNTAX 	INTEGER 
	ACCESS	read-write
	STATUS	mandatory
	DESCRIPTION 
		"Enable(1) and Disable(2) to RSSI function on port level. 

ALCATEL NOTE:
		 ACCESS: NA        usage: OPT
		 persistent: YES   INSRVMOD:YES
                             RANGE:1,2          DEFVALUE: 2 
                             UNITS: NA         SPARSE: NA  "
::= { sfpDiagEntry 17}


sfpDiagRSSIRxPowerAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for alarm OLTRXPWLO. if RX power is less than this value,then alarm OLTRXPWLO will be reported.
                For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane. 
                For ONT Profile, 0.5dBm means using ont internal policy.
	        'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned threshold values.
                'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  18 }

sfpDiagRSSIRxPowerAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHI. When RX power is larger than this value,then alarm OLTRXPWHI will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.
		 For ONT Profile, 0.5dBm means using ont internal policy.
	 	 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned threshold values.
		 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  19 }

sfpDiagRSSIRxPowerWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWLOWARN. When Rx power is less than this value,alarm OLTRXPWLOWARN  will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned threshold values.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  20 }

sfpDiagRSSIRxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHIWARN. When Rx power is larger than this value,alarm OLTRXPWHIWARN will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.
		'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned threshold values.
                'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:     NA
                 PERSIST:    NO       INSRVMOD:  NA
                 RANGE:      NA       DEFVALUE:  NA
                 UNITS:      NA       SPARSE:    NA
                 DESCR:      NA "
::= { sfpDiagEntry  21 }

sfpDiagRSSITxPowerAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWLO. When Tx power is less than this value,alarm OLTTXPWLO will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
                 For ONT Profile, -63.5dBm means using ONT internal policy.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  22 }

sfpDiagRSSITxPowerAlmHigh OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWHI. When Tx power is larger than this value,alarm OLTTXPWHI will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  23 }

sfpDiagRSSITxPowerWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWLOWARN. When Tx power is less than this value,alarm OLTTXPWLOWARN will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  24 }

sfpDiagRSSITxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWHIWARN. When Tx power is larger than this value,alarm OLTTXPWHIWARN will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  25 }

sfpDiagRSSITemperatureAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPLO. when temperature is less than this value, then alarm OLTTEMPLO will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtTemperatureAlmLow. 
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  26 }

sfpDiagRSSITemperatureAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPHI. When temperature is larger than this value,alarm OLTTEMPHI will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtTemperatureAlmHigh.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  27 }

sfpDiagRSSITemperatureWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPLOWARN. when temperature is less than this value, then alarm OLTTEMPLOWARN will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtTemperatureWarnLow.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  28 }

sfpDiagRSSITemperatureWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPHIWARN. When temperature is larger than this value,alarm OLTTEMPHIWARN will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtTemperatureWarnHigh.
		'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  29 }


sfpDiagRSSIBiasAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASLO. when bias is less than this value, then alarm OLTBIASLO will be reported.
                 For CFP4,this is the module level threshold.The network lane threshold is indicated by sfpDiagRSSIExtBiasAlmLow.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  30 }

sfpDiagRSSIBiasAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASHI. when bias is larger than this value, then alarm OLTBIASHI will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtBiasAlmHigh.  
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  31 }

sfpDiagRSSIBiasWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASLOWARN. when bias is less than this value, then alarm OLTBIASLOWARN will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtBiasWarnLow.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  32 }

sfpDiagRSSIBiasWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASHIWARN. when bias is larger than this value, then alarm OLTBIASHIWARN will be reported.
                 For CFP4,this is the module level threshold.The network lane level threshold is indicated by sfpDiagRSSIExtBiasWarnHigh.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  33 }

sfpDiagRSSIVoltageAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLLO. when voltage is less than this value, then alarm OLTVOLLO will be reported.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  34 }

sfpDiagRSSIVoltageAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLHI. when voltage is larger than this value, then alarm OLTVOLHI will be reported.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:     NA
                 PERSIST:    NO       INSRVMOD:  NA
                 RANGE:      NA       DEFVALUE:  NA
                 UNITS:      NA       SPARSE:    NA
                 DESCR:      NA "
::= { sfpDiagEntry  35 }

sfpDiagRSSIVoltageWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLLOWARN. when voltage is less than this value, then alarm OLTVOLLOWARN will be reported.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:      NA       DEFVALUE: NA
                 UNITS:      NA       SPARSE:   NA
                 DESCR:      NA  "
::= { sfpDiagEntry  36 }

sfpDiagRSSIVoltageWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLHIWARN. when voltage is larger than this value, then alarm OLTVOLHIWARN will be reported.
		 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  37 }

sfpDiagRSSIExtRxPowerAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for alarm OLTRXPWLO. if RX power is less than this value, then alarm OLTRXPWLO will be reported. 
		 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.	
                 'Not Supported' is shown when the matching profile is not found.	

                ALCATEL NOTE:
                 ACCESS:     RO            USAGE:    NA
                 PERSIST:    NO            INSRVMOD: NA
                 RANGE:      NA            DEFVALUE:  0
                 UNITS:      NA            SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  38 }

sfpDiagRSSIExtRxPowerAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHI. When RX power is larger than this value,then alarm OLTRXPWHI will be reported.
	 	 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  39}

sfpDiagRSSIExtRxPowerWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWLOWARN. When Rx power is less than this value,alarm OLTRXPWLOWARN  will be reported.
		 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  40}

sfpDiagRSSIExtRxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHIWARN. When Rx power is larger than this value,alarm OLTRXPWHIWARN will be reported.
	  	 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  41}


sfpDiagRSSIExtTxPowerAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWLO. When Tx power is less than this value,alarm EXTOLTTXPWLO will be reported.
		  Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	          For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  42 }

sfpDiagRSSIExtTxPowerAlmHigh OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWHI. When Tx power is larger than this value,alarm EXTOLTTXPWHI will be reported.
		 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  43 }

sfpDiagRSSIExtTxPowerWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWLOWARN. When Tx power is less than this value,alarm EXTOLTTXPWLOWARN will be reported.
		 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  44 }

sfpDiagRSSIExtTxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWHIWARN. When Tx power is larger than this value,alarm EXTOLTTXPWHIWARN will be reported.
	 	 Applicable only for 10G EPON SFPs,Shown as 'Not Applicable' for others.
	         For EPON SFPs 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                 ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  45 }

sfpDiagRSSIExtBiasAlmLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASLO. when bias is less than this value, then alarm EXTOLTBIASLO will be reported.
                 For CFP4, this object indicates the threshold for OLTBIASLO.When any network lane's bias is less than this value, then alarm OLTBIASLO will be reported.
                 For CFP4,this is the network lane level threshold.The module level threshold is indicated by sfpDiagRSSIBiasAlmLow.
		         Applicable for 10G EPON SFPs and CFP4,Shown as 'Not Applicable' for others.
	             For EPON SFPs and CFP4 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "

::= { sfpDiagEntry  46 }

sfpDiagRSSIExtBiasAlmHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASHI. when bias is larger than this value, then alarm EXTOLTBIASHI will be reported.
                 For CFP4, this object indicates the threshold for OLTBIASHI.When any network lane's bias is larger than this value, then alarm OLTBIASHI will be reported.
                 For CFP4,this is the network lane level threshold.The module level threshold is indicated by sfpDiagRSSIBiasAlmHigh.
		         Applicable for 10G EPON SFPs and CFP4,Shown as 'Not Applicable' for others.
	             For EPON SFPs and CFP4 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  47 }

sfpDiagRSSIExtBiasWarnLow   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASLOWARN. when bias is less than this value,then alarm EXTOLTBIASLOWARN will be reported.
                 For CFP4, this object indicates the threshold for OLTBIASLOWARN.When any network lane's bias is less than this value, then alarm OLTBIASLOWARN will be reported.
                 For CFP4,this is the network lane level threshold.The module level threshold is indicated by sfpDiagRSSIBiasWarnLow.
		         Applicable for 10G EPON SFPs and CFP4,Shown as 'Not Applicable' for others. 
	             For EPON SFPs and CFP4 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  48 }

sfpDiagRSSIExtBiasWarnHigh   OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASHIWARN. when bias is larger than this value,then alarm EXTOLTBIASHIWARN will be reported.
                 For CFP4, this object indicates the threshold for OLTBIASHIWARN.When any network lane's bias is larger than this value, then alarm OLTBIASHIWARN will be reported.
                 For CFP4,this is the network lane level threshold.The module level threshold is indicated by sfpDiagRSSIBiasWarnHigh.
                 Applicable for 10G EPON SFPs and CFP4,Shown as 'Not Applicable' for others.				 
	             For EPON SFPs and CFP4 'Not Available' is shown when the measurement could not be obtained,If the specified SFP cage is empty or if RSSI is not enabled and no profile assigned.
                 'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE: NA
                 UNITS:      NA                 SPARSE:   NA
                 DESCR:      NA "
::= { sfpDiagEntry  49 }

sfpDiagRSSIExtTemperatureAlmLow  OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "This object indicates the laser temperature low alarm threshold of network lane for CFP4. 
         When any network lane's laser temperature of CFP4 is less than this value,then alarm OLTTEMPLO will be reported.
         The module level threshold is indicated by sfpDiagRSSITemperatureAlmLow.
         Applicable only for CFP4,Shown as 'Not Applicable' for others.
        'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE:  NA
                 UNITS:      NA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpDiagEntry  50 }

sfpDiagRSSIExtTemperatureAlmHigh  OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "This object indicates the laser temperature high alarm threshold of network lanefor CFP4. 
         When any network lane's laser temperature of CFP4 is larger than this value,then alarm OLTTEMPHI will be reported.
         The module level threshold is indicated by sfpDiagRSSITemperatureAlmHigh.
         Applicable only for CFP4,Shown as 'Not Applicable' for others.
        'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE:  NA
                 UNITS:      NA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpDiagEntry  51 }

sfpDiagRSSIExtTemperatureWarnLow OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "This object indicates the laser temperature low warning threshold of network lane for CFP4. 
         When any network lane's laser temperature of CFP4 is less than this value,then alarm OLTTEMPLOWARN will be reported.
         The module level threshold is indicated by sfpDiagRSSITemperatureWarnLow.
         Applicable only for CFP4,Shown as 'Not Applicable' for others.
        'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE:  NA
                 UNITS:      NA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpDiagEntry  52 }

sfpDiagRSSIExtTemperatureWarnHigh OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "This object indicates the laser temperature high warning threshold of network lane for CFP4. 
         When any network lane's laser temperature of CFP4 is larger than this value,then alarm OLTTEMPHIWARN will be reported.
         The module level threshold is indicated by sfpDiagRSSITemperatureWarnHigh.
         Applicable only for CFP4,Shown as 'Not Applicable' for others.
        'Not Supported' is shown when the matching profile is not found.

                ALCATEL NOTE:
                 ACCESS:     RO                 USAGE:    NA
                 PERSIST:    NO                 INSRVMOD: NA
                 RANGE:      NA                 DEFVALUE:  NA
                 UNITS:      NA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpDiagEntry  53 }

--  ==================================================================
--  |                    End of SFP Diag Table                       |
--  ==================================================================

--  ==================================================================
--  |                  Start of SFP Inventory Table                       |
--  ==================================================================

-- sfpInventoryTable definition

sfpInventoryTable OBJECT-TYPE
        SYNTAX           SEQUENCE OF SfpInventoryEntry
        ACCESS              not-accessible
        STATUS              mandatory
        DESCRIPTION     
        "The primary purpose of this read-only table:  allow the SNMP 
         manager to read read-only EEPROM contents from EEPROM of SFP

         Details are documented with the individual objects.

         The following table size field is set to 'not applicable', 
         since it does not make sense for the SNMP manager to cache the 
         values in this table.  The values will be different each time 
         they are read from agent.

         ALCATEL NOTE:
            TABLESIZE: NA
            DESCR:  NA 
        "
::= {sfpMIB 6}

-- List the two index columns of the sfpInventoryTable.

sfpInventoryEntry       OBJECT-TYPE
        SYNTAX           SfpInventoryEntry       
        ACCESS           not-accessible
        STATUS           mandatory
        DESCRIPTION     
        "The index for the sfpInventoryTable identifies an SFP cage 
         using two fields:  the logicalSlotId of the card associated 
         with the SFP cage (normally the card with the cages on its 
         faceplate), and the SFP cage number."
        INDEX       {sfpInvLogicalSlot, sfpInvFaceplateNumber}
::= {sfpInventoryTable 1}

-- List all the columns in the sfpInventoryTable.

SfpInventoryEntry ::=
        SEQUENCE {
                 sfpInvLogicalSlot INTEGER,
                 sfpInvFaceplateNumber INTEGER,
                 sfpInvAvailable INTEGER, 
                 sfpInvFiberType INTEGER,
                 sfpInvAluPartNumber DisplayString,
                 sfpInvCLEIcode DisplayString,
                 sfpInvTxWavelength DisplayString,
                 sfpInvAdditionalInfo DisplayString,
                 sfpInvMfgName DisplayString,
                 sfpInvMfgOUICode DisplayString,
                 sfpInvMfgSerialNumber DisplayString,
                 sfpInvMfgDate DisplayString,
                 sfpInvSpecificalType INTEGER
                }

-- Define the first index column of the sfpInventoryTable.

sfpInvLogicalSlot  OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the first index of the sfpInventoryTable.  

         Normally, this object contains the Logical Slot ID (as defined in
         the Equipment MIB) of the card which has the SFP cage of interest
         on its faceplate.
         If that card does not have a Logical Slot ID, the Logical Slot ID
         of a related card is used.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1..65535
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {sfpInventoryEntry 1}

-- Define the second (and final) index column of the sfpInventoryTable.

sfpInvFaceplateNumber      OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the second index of the sfpInventoryTable. 
         It is the number of the SFP cage, as shown on the card 
         faceplate.  If the faceplate cage number is zero, 
         sfpInvFaceplateNumber=0 is used (i.e. zero is not a
         special case).
         For an SFP cage, it is the faceplate number of the SFP cage 
         (e.g. 1).
         For an SFP+/XFP cage, it is 256 + the faceplate number 
         of the XFP cage.  For example, if the SFP+/XFP cage has 
         faceplate label X1, sfpInvFaceplateNumber = 257.
         For an QSFP cage, it is 512 + the faceplate number 
         of the QSFP cage.  For example, if the QSFP cage has 
         faceplate label Q1, sfpInvFaceplateNumber = 513.
         For a CFP4 cage, it is 768 + the faceplate number 
         of the CFP4 cage.  For example, if the CFP4 cage has 
         faceplate label C1, sfpInvFaceplateNumber = 769.

        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 0..769
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {sfpInventoryEntry 2}

-- Define a non-index column of the sfpInventoryTable.

sfpInvAvailable    OBJECT-TYPE
        SYNTAX      INTEGER {
                      noError (1),
                      cageEmpty (2),
                      cageDoesNotSupportA2 (3),
                      a0ReadFailed (4),
                      a0ChecksumFailed (5),
                      sfpDoesNotSupportA2 (6),
                      a2ReadFailed (7),
                      a2ChecksumFailed (8)
                    }
        ACCESS      read-only
        STATUS      mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, with the following code points.

         noError - valid values for all objects in this row (e.g. 
           sfpDiagRxPower) were obtained from the SFP in the specified 
           cage.
         cageEmpty - the specified SFP cage does not contain an SFP. 
           All subsequent values in the row have notAvailable or 
           'Not Available' values. 
         cageDoesNotSupportA2 - the specified SFP cage does not support 
           access to the SFP A2 bank (e.g. because of limitations in 
           FPGA support on the host card).  sfpDiagLOS and 
           sfpDiagTxFault have valid values (since they are implemented 
           using external signals, not SFP EEPROM values).
         a0ReadFailed - the attempt to read the A0 bank of the 
           specified SFP failed.  A0 is read to determine if A2 is 
           supported, so no attempt was made to read the A2 
           information.  sfpDiagLOS and sfpDiagTxFault have valid 
           values (since they are implemented using external signals, 
           not SFP EEPROM values).
         a0ChecksumFailed - the A0 bank of the SFP EEPROM is 
           corrupted.  sfpDiagLOS and sfpDiagTxFault have valid values 
           (since they are implemented using external signals, not SFP 
           EEPROM values).
         sfpDoesNotSupportA2 - the A0 bank of the SFP EEPROM indicates
           A2 is not supported.  sfpDiagLOS and sfpDiagTxFault have 
           valid values (since they are implemented using external 
           signals, not SFP EEPROM values). 
         a2ReadFailed - the attempt to read the A2 bank of the 
           specified SFP failed.  sfpDiagLOS and sfpDiagTxFault have 
           valid values (since they are implemented using external 
           signals, not SFP EEPROM values).
         a2ChecksumFailed - the A2 bank of the SFP EEPROM is 
           corrupted.  sfpDiagLOS and sfpDiagTxFault have valid values 
           (since they are implemented using external signals, not SFP 
           EEPROM values).
  

         ALCATEL NOTE:
           ACCESS: RO
           USAGE: NA
           PERSIST: NO 
           INSRVMOD: NO
           RANGE: NA
           DEFVALUE: NA
           UNITS: NA 
           SPARSE: NO
           DESCR: NA
       "
::= { sfpInventoryEntry 3}

-- Define a non-index column of the sfpInventoryTable.

sfpInvFiberType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        singleMode(1),
                        multiMode(2),
                        notAvailable(3)
                    }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the fiber type of 
        the specified SFP/SFP+.
        For XFP, it is always singleMode

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 4}

sfpInvAluPartNumber OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the Alcatel-Lucent part number
         of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 5}

sfpInvCLEIcode OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the CLEI code
         of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 6}

sfpInvTxWavelength OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the transmit wavelength
         of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 7}

sfpInvAdditionalInfo OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the customer specific
        additional information of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 8}

sfpInvMfgName OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the manufacturer or
        vendor name of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 9}

sfpInvMfgOUICode OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the manufacturer or
        vendor OUI code of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 10}

sfpInvMfgSerialNumber OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the manufacturer or
        vendor serial number of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 11}

sfpInvMfgDate OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A column in the sfpInventoryTable, containing the manufactured date 
        of the specified SFP/SFP+/XFP, encoded into a string.

         ALCATEL NOTE:
             ACCESS: RO
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NO
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NO
             DESCR: NA
        "
::= { sfpInventoryEntry 12}

sfpInvSpecificalType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        sfpUnknown(0),
                        px20(1),
                        px20plus(2),
                        prx-t1(3),
                        pr-t2(4),
                        prx-t3(5),
                        pr-t4(6),
                        bPlusCAA(7),
                        bPlusIAC(8),
                        cPlusCBA(9),
                        cPlusIBC(10),
                        base10G-ER(11),
                        base10G-SR(12),
                        base10G-LR(13),
                        base1000-T(14),
                        base1000-LX(15),
                        base1000-BX10-U(16),
                        base1000-EX(17),
                        base1000-ZX(18),
                        n1-c(19),
                        n2a-c(20),
                        n2b-c(21),
                        base1000-SX(22),
                        base1000-CX(23),
                        base1000-BX10-D(24),
                        base100-FX(25),
                        base100-TX(26),
                        base2500(27),
                        base10G-LRM(28),
                        base10G-EW(29),
                        base10G-SW(30),
                        base10G-LW(31),
                        base40G-LR4(32),
                        base40G-SR4(33),
                        base40G-CR4(34),
                        base100-LX(35),
                        base100-BX10-U(36),
                        base100-BX10-D(37),
                        base1000-VX(38),
                        e1eth(39),
                        e3eth(40),
                        stm1eth(41),
                        e1tdm(42),
                        bPlusIONU(43),
                        base10G-BX40-U(44),
                        base10G-BX40-D(45),
                        base1000-BX40-U(46),
                        base1000-BX40-D(47),
                        base1000-BX20-U(48),
                        base1000-BX20-D(49),
                        base10G-ZR-CWDM(50),
                        base10G-ZR-DWDM(51),
                        n1-i(52),
                        n2a-i(53),
                        n2b-i(54),
                        base100G-LR4(55),
                        base100G-SR4(56),
                        base10G-BX10-U(57),
                        base10G-BX10-D(58),
                        base1000-ZR-CWDM(59),
                        base10G-ZR(60),
                        base10G-BX80-U(61),
                        base10G-BX80-D(62),
                        base1000-BX80-U(63),
                        base1000-BX80-D(64),
                        base100G-ER4(65),
                        cPlusPlusI(66)

                    }
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "indicate basic type of the specified SFP(+)/XFP/QSFP(+).
        
         ALCATEL NOTE:
             ACCESS: NA
             USAGE: NA
             PERSIST: NO 
             INSRVMOD: NA
             RANGE: NA
             DEFVALUE: NA
             UNITS: NA 
             SPARSE: NA
             DESCR: NA
        "
::= { sfpInventoryEntry 13}

--  ==================================================================
--  |                    End of SFP Inventory Table                       |
--  ==================================================================

sfpRSSIProfileTable OBJECT-TYPE
	SYNTAX	SEQUENCE OF SfpPonRSSIProfileEntry
	ACCESS	not-accessible
	STATUS	mandatory
	DESCRIPTION
		"This object contains a table for defining RSSI for SFP/XFP/CFP4 by operator, which could be used for all NT/NTIO cards and DSL/GPON/EPON line cards.
		 ALCATEL NOTE:
             SUPPORT: YES   TABLESIZE: DEP"
::= { sfpMIB 7 }

sfpRSSIProfileEntry OBJECT-TYPE
	SYNTAX 	SfpPonRSSIProfileEntry
	ACCESS	not-accessible
	STATUS	mandatory
	DESCRIPTION
		 "An entry in the sfpRSSIProfileTable. 
          Row creation : an entry is created  when operator creates SfpRSSI profile.
          Row deletion : an entry is created  when operator deletes SfpRSSI profile.
          ALCATEL NOTE:
             SUPPORT: YES"
	INDEX {sfpRSSIProfileIndex}
::= {sfpRSSIProfileTable 1}

SfpPonRSSIProfileEntry ::=
	SEQUENCE { 
		sfpRSSIProfileIndex		              AsamProfileIndex,
		sfpRSSIProfileName		              DisplayString,
		sfpRSSIProfileRefCount              AsamProfileRefCount,
		sfpRSSIRowStatus                    RowStatus,
    sfpRSSIRxPowerAlmLow                    INTEGER,
    sfpRSSIRxPowerAlmHigh                   INTEGER,
    sfpRSSIRxPowerWarnLow                   INTEGER,
    sfpRSSIRxPowerWarnHigh                  INTEGER,
    sfpRSSITxPowerAlmLow                    INTEGER,
    sfpRSSITxPowerAlmHigh                   INTEGER,
    sfpRSSITxPowerWarnLow                   INTEGER,
    sfpRSSITxPowerWarnHigh                  INTEGER,
    sfpRSSITemperatureAlmLow                INTEGER,
    sfpRSSITemperatureAlmHigh               INTEGER,
    sfpRSSITemperatureWarnLow               INTEGER,
    sfpRSSITemperatureWarnHigh              INTEGER,
    sfpRSSIBiasAlmLow                       INTEGER,
    sfpRSSIBiasAlmHigh                      INTEGER,
    sfpRSSIBiasWarnLow                      INTEGER,
    sfpRSSIBiasWarnHigh                     INTEGER,
    sfpRSSIVoltageAlmLow                    INTEGER,
    sfpRSSIVoltageAlmHigh                   INTEGER,
    sfpRSSIVoltageWarnLow                   INTEGER,
    sfpRSSIVoltageWarnHigh                  INTEGER,
    sfpRSSIExtRxPowerAlmLow                    INTEGER,
    sfpRSSIExtRxPowerAlmHigh                   INTEGER,
    sfpRSSIExtRxPowerWarnLow                   INTEGER,
    sfpRSSIExtRxPowerWarnHigh                  INTEGER,
    sfpRSSIExtTxPowerAlmLow                    INTEGER,
    sfpRSSIExtTxPowerAlmHigh                   INTEGER,
    sfpRSSIExtTxPowerWarnLow                   INTEGER,
    sfpRSSIExtTxPowerWarnHigh                  INTEGER,
    sfpRSSIExtBiasAlmLow                       INTEGER,
    sfpRSSIExtBiasAlmHigh                      INTEGER,
    sfpRSSIExtBiasWarnLow                      INTEGER,
    sfpRSSIExtBiasWarnHigh                     INTEGER,
    sfpRssiProfileType                         INTEGER,
    sfpRSSIExtTemperatureAlmLow                INTEGER,
    sfpRSSIExtTemperatureAlmHigh               INTEGER,
    sfpRSSIExtTemperatureWarnLow               INTEGER,
    sfpRSSIExtTemperatureWarnHigh              INTEGER
}

sfpRSSIProfileIndex OBJECT-TYPE 
	SYNTAX AsamProfileIndex
	ACCESS not-accessible
	STATUS mandatory
	DESCRIPTION
	    "This object is the index to the sfpSfpRSSIProfileTable.
	    ALCATEL NOTE:
                 ACCESS:     NA       USAGE:      NA
                 PERSIST:    YES      INSRVMOD:   NO
                 RANGE:      NA       DEFVALUE:   NA
                 UNITS:      NA       SPARSE:     NA
"
::= { sfpRSSIProfileEntry 1}

sfpRSSIProfileName OBJECT-TYPE 
	SYNTAX DisplayString
	ACCESS read-write
	STATUS  mandatory
	DESCRIPTION	    
	    "The corresponding name for a given profile id.
The name corresponding to the ProfileId must also be unique.
ALCATEL NOTE:
                 ACCESS:     NA       USAGE:      NA
                 PERSIST:    YES      INSRVMOD:   NO
                 RANGE:      NA       DEFVALUE:   NA
                 UNITS:      NA       SPARSE:     NA"


::= {sfpRSSIProfileEntry 2}


sfpRSSIProfileRefCount 	OBJECT-TYPE 
	SYNTAX	AsamProfileRefCount
	ACCESS  read-only
	STATUS	mandatory
	DESCRIPTION
    " Indicates the number of entities using this profile. If the
          reference count is greater than 0, then this profile is 'in use',
          and may NOT be deleted. If the value is 0, then this profile is
          'not in use', and can be deleted.
          ALCATEL NOTE:
             ALCATEL NOTE:
                 ACCESS:     NA       USAGE:      NA
                 PERSIST:    NA     INSRVMOD:   NO
                 RANGE:      NA       DEFVALUE:   NA
                 UNITS:      NA       SPARSE:     NA "

::= { sfpRSSIProfileEntry  3}

sfpRSSIRowStatus OBJECT-TYPE 
	SYNTAX	RowStatus
	ACCESS	read-write
	STATUS  mandatory
	DESCRIPTION
"This object is used to create a new row or delete an row.
	 ALCATEL NOTE:
		 ACCESS: NA        usage: MAND
         persistent: YES   INSRVMOD:YES
         RANGE:1,4,6       DEFVALUE: NA  
         UNITS: NA         SPARSE: NA  "
::= { sfpRSSIProfileEntry  4}


sfpRSSIRxPowerAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for alarm OLTRXPWLO. if RX power is less than this value, then
                alarm OLTRXPWLO will be reported.
                For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane. 
                For ONT Profile, 0.5dBm means using ont internal policy.

                ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES      INSRVMOD: YES
                 RANGE:     -127000..8163      DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  5 }

sfpRSSIRxPowerAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHI. When RX power is larger than this value,
                 then alarm OLTRXPWHI will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.
		 For ONT Profile, 0.5dBm means using ont internal policy.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  6 }

sfpRSSIRxPowerWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWLOWARN. When Rx power is less than this value,
                 alarm OLTRXPWLOWARN  will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.

                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    NO       INSRVMOD: NA
                 RANGE:     -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  7 }

sfpRSSIRxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHIWARN. When Rx power is larger than this value,
                 alarm OLTRXPWHIWARN will be reported.
                 For CFP4, there is no module level 'RxPower' monitor,this is the threshold for network lane.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  8 }

sfpRSSITxPowerAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWLO. When Tx power is less than this value, 
                alarm OLTTXPWLO will be reported.
                For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
                For ONT Profile, -63.5dBm means using ONT internal policy.
                ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES      INSRVMOD: YES
                 RANGE:     -63500..63500      DEFVALUE: 0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  9 }

sfpRSSITxPowerAlmHigh OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWHI. When Tx power is larger than this value,
                 alarm OLTTXPWHI will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  10 }

sfpRSSITxPowerWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWLOWARN. When Tx power is less than this value,
                 alarm OLTTXPWLOWARN will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane. 
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  11 }

sfpRSSITxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTXPWHIWARN. When Tx power is larger than this value,
                 alarm OLTTXPWHIWARN will be reported.
                 For CFP4, there is no module level 'TxPower' monitor,this is the threshold for network lane.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  12 }

sfpRSSITemperatureAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPLO. when temperature is less than this value, then
                alarm OLTTEMPLO will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtTemperatureAlmLow.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  13 }

sfpRSSITemperatureAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPHI. When temperature is larger than this value,
                 alarm OLTTEMPHI will be reported.
                 For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtTemperatureAlmHigh.
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  14 }

sfpRSSITemperatureWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPLOWARN. when temperature is less than this value, then
                alarm OLTTEMPLOWARN will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtTemperatureWarnLow.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  15 }

sfpRSSITemperatureWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTTEMPHIWARN. When temperature is larger than this value,
                 alarm OLTTEMPHIWARN will be reported.
                 For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtTemperatureWarnHigh
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  16 }


sfpRSSIBiasAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASLO. when bias is less than this value, then
                alarm OLTBIASLO will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtBiasAlmLow.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                INSRVMOD: YES
                 RANGE:      0..131000          DEFVALUE:  0
                 UNITS:      2uA                SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  17 }

sfpRSSIBiasAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASHI. when bias is larger than this value, then
                alarm OLTBIASHI will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtBiasAlmHigh.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                INSRVMOD: YES
                 RANGE:      0..131000          DEFVALUE:  0
                 UNITS:      2uA                SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  18 }

sfpRSSIBiasWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASLOWARN. when bias is less than this value, then
                alarm OLTBIASLOWARN will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtBiasWarnLow.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                INSRVMOD: YES
                 RANGE:      0..131000          DEFVALUE:  0
                 UNITS:      2uA                SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  19 }

sfpRSSIBiasWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTBIASHIWARN. when bias is larger than this value, then
                alarm OLTBIASHIWARN will be reported.
                For CFP4, this is the module level threshold.The network lane level threshold is indicated by sfpRSSIExtBiasWarnHigh.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                INSRVMOD: YES
                 RANGE:      0..131000          DEFVALUE:  0
                 UNITS:      2uA                SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  20 }

sfpRSSIVoltageAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLLO. when voltage is less than this value, then
                alarm OLTVOLLO will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..65500           DEFVALUE:  0
                 UNITS:      100uV             SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  21 }

sfpRSSIVoltageAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLHI. when voltage is larger than this value, then
                alarm OLTVOLHI will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..65500           DEFVALUE:  0
                 UNITS:      100uV             SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  22 }

sfpRSSIVoltageWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLLOWARN. when voltage is less than this value, then
                alarm OLTVOLLOWARN will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..65500           DEFVALUE:  0
                 UNITS:      100uV             SPARSE:     NA
                 DESCR:      NA  "
::= { sfpRSSIProfileEntry  23 }

sfpRSSIVoltageWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTVOLHIWARN. when voltage is larger than this value, then
                alarm OLTVOLHIWARN will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..65500           DEFVALUE:  0
                 UNITS:      100uV             SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  24 }

sfpRSSIExtRxPowerAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for alarm OLTRXPWLO. if RX power is less than this value, then
                alarm OLTRXPWLO will be reported. 

                ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES      INSRVMOD: YES
                 RANGE:     -127000..8163      DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  25 }

sfpRSSIExtRxPowerAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHI. When RX power is larger than this value,
                 then alarm OLTRXPWHI will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:    -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  26 }

sfpRSSIExtRxPowerWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWLOWARN. When Rx power is less than this value,
                 alarm OLTRXPWLOWARN  will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  27 }

sfpRSSIExtRxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for OLTRXPWHIWARN. When Rx power is larger than this value,
                 alarm OLTRXPWHIWARN will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -127000..8163  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  28 }

sfpRSSIExtTxPowerAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWLO. When Tx power is less than this value, 
                alarm EXTOLTTXPWLO will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES      INSRVMOD: YES
                 RANGE:     -63500..63500      DEFVALUE: 0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  29 }

sfpRSSIExtTxPowerAlmHigh OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWHI. When Tx power is larger than this value,
                 alarm EXTOLTTXPWHI will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  30 }

sfpRSSIExtTxPowerWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWLOWARN. When Tx power is less than this value,
                 alarm EXTOLTTXPWLOWARN will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  31 }

sfpRSSIExtTxPowerWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "this object indicates the threshold for EXTOLTTXPWHIWARN. When Tx power is larger than this value,
                 alarm EXTOLTTXPWHIWARN will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA       USAGE:    NA
                 PERSIST:    YES       INSRVMOD: YES
                 RANGE:     -63500..63500  DEFVALUE:  0
                 UNITS:     0.001dBm     SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  32 }

sfpRSSIExtBiasAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASLO. when bias is less than this value, then
                alarm EXTOLTBIASLO will be reported.
                For CFP4,this object indicates the threshold of network lane for OLTBIASLO,when laser bias of network lane is less
                than this value,alarm OLTBIASLO will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                  USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..131000           DEFVALUE:  0
                 UNITS:      2uA                 SPARSE:     NA
                 DESCR:      NA "

::= { sfpRSSIProfileEntry  33 }

sfpRSSIExtBiasAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASHI. when bias is larger than this value, then
                alarm EXTOLTBIASHI will be reported.
                For CFP4,this object indicates the threshold of network lane for OLTBIASHI,when laser bias of network lane is larger 
                than this value,alarm OLTBIASHI will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                  USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..131000           DEFVALUE:  0
                 UNITS:      2uA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  34 }

sfpRSSIExtBiasWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASLOWARN. when bias is less than this value, then
                alarm EXTOLTBIASLOWARN will be reported.
                For CFP4,this object indicates the threshold of network lane for OLTBIASLOWARN,when laser bias of network lane is less 
                than this value,alarm OLTBIASLOWARN will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                  USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..131000           DEFVALUE:  0
                 UNITS:      2uA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  35 }

sfpRSSIExtBiasWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "For 10G EPON SFPs,this object indicates the threshold for EXTOLTBIASHIWARN. when bias is larger than this value, then
                alarm EXTOLTBIASHIWARN will be reported.
                For CFP4,this object indicates the threshold of network lane for OLTBIASHIWARN,when laser bias of network lane is larger
                than this value,alarm OLTBIASHIWARN will be reported.
                ALCATEL NOTE:
                 ACCESS:     NA                  USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:      0..131000           DEFVALUE:  0
                 UNITS:      2uA                 SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  36 } 

sfpRssiProfileType   OBJECT-TYPE
        SYNTAX INTEGER
        {
          olt(1),
          ont(2)
        }
    ACCESS    read-write
    STATUS    mandatory
    DESCRIPTION
       "RSSI profile type"
::= { sfpRSSIProfileEntry 37 }

sfpRSSIExtTemperatureAlmLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "This object indicates the laser temperature low alarm threshold of network lane for CFP4. 
                 When any network lane's laser temperature of CFP4 is less than this value,alarm OLTTEMPLO will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  38 }

sfpRSSIExtTemperatureAlmHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "This object indicates the laser temperature high alarm threshold of network lane for CFP4. 
                 When any network lane's laser temperature of CFP4 is large than this value,alarm OLTTEMPHI will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  39 }

sfpRSSIExtTemperatureWarnLow   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "This object indicates the laser temperature low warning threshold of network lane for CFP4. 
                 When any network lane's laser temperature of CFP4 is less than this value,alarm OLTTEMPLOWARN will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  40 }

sfpRSSIExtTemperatureWarnHigh   OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                "This object indicates the laser temperature high warning threshold of network lane for CFP4. 
                 When any network lane's laser temperature of CFP4 is large than this value,alarm OLTTEMPHIWARN will be reported.
                 ALCATEL NOTE:
                 ACCESS:     NA                 USAGE:    NA
                 PERSIST:    YES                 INSRVMOD: YES
                 RANGE:     -32768..32768       DEFVALUE:  0
                 UNITS:     1/256 Celsius       SPARSE:     NA
                 DESCR:      NA "
::= { sfpRSSIProfileEntry  41 }

sfpRSSIProfileIndexNext        OBJECT-TYPE
        	SYNTAX          AsamNextProfileIndex
        	ACCESS          read-only
        	STATUS          mandatory
        	DESCRIPTION     
        	"This object contains an appropriate value to be used for 
         		sfpRSSIProfileIndex when creating entries in the
         	  sfpRSSIProfileTable. 
         		The value 0 indicates that no unassigned entries are available. To 
         		obtain the next index value for a new entry, the
        		 manager issues a management protocol retrieval operation to obtain the
         		current value of this object.  After each retrieval, the agent should
        		 modify the value to the next unassigned index. 
        		 ALCATEL NOTE:
		     ACCESS: NA        usage: NA
		     persistent: NA  INSRVMOD:NA
		     RANGE:NA          DEFVALUE: NA  
         UNITS: NA        SPARSE: NA  "
::= {sfpMIB 8 }

sfpRSSIProfileMaxIndex        OBJECT-TYPE
      	SYNTAX            AsamMaxProfileIndex
      	ACCESS            read-only
     	 STATUS            mandatory
      	DESCRIPTION
         "This object represents the maximum number of
          sfpRSSIProfiles that can be created. The object
          sfpRSSIProfileMaxIndex will have values ranging
           from 0 to the value represented by this object. 
         ALCATEL NOTE:
		 ACCESS: NA        usage: NA
		 persistent: NA  INSRVMOD:NA
		 RANGE:NA          DEFVALUE: NA  
         UNITS: NA        SPARSE: NA  "
::= {sfpMIB 9 }


--  ==================================================================
--  |           Start of 2E1/DS1 SFP Clock-Reset manager Table                |
--  ==================================================================

dsx1ClockConfigTable	OBJECT-TYPE
        SYNTAX		SEQUENCE OF Dsx1ClockConfigEntry
        ACCESS          not-accessible
        STATUS          mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP Clock configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 10 
         "
::= {sfpMIB 10}

dsx1ClockConfigEntry		OBJECT-TYPE
        SYNTAX			Dsx1ClockConfigEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP general configuration parameters."
        INDEX           {dsx1SfpClkConfigLogicalSlot, dsx1SfpClkConfigFaceplateNumber}
::= {dsx1ClockConfigTable 1}

Dsx1ClockConfigEntry ::=
        SEQUENCE {
                 dsx1SfpClkConfigLogicalSlot		INTEGER,
                 dsx1SfpClkConfigFaceplateNumber	INTEGER,
		 dsx1SfpClkConfigSyncSource		INTEGER,
		 dsx1SfpClkConfigDegradeAlarm		INTEGER,
		 dsx1SfpClkConfigSoftwareReset		INTEGER,
		 dsx1SfpClkAlarmState			INTEGER,
		 dsx1SfpClkConfigClkControlSignal	INTEGER,
		 dsx1SfpClkConfigClkSource		INTEGER,
		 dsx1SfpClkRowStatus              	RowStatus,
		 dsx1SfpClkConfState                    INTEGER
        }


dsx1SfpClkConfigLogicalSlot OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the first index of the dsx1ClockConfig Table.  

         Normally, this object contains the Logical Slot ID (as defined in
         the Equipment MIB) of the card which has the SFP cage of interest
         on its faceplate.
         If that card does not have a Logical Slot ID, the Logical Slot ID
         of a related card is used.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1..65535
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1ClockConfigEntry 1}

-- Define the second (and final) index column of the dsx1ClockConfigTable.

dsx1SfpClkConfigFaceplateNumber     OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the second index of the dsx1ClockConfigTable. 
         It is the number of the SFP cage, as shown on the card 
         faceplate.  If the faceplate cage number is zero, 
         sfpDiagSfpFaceplateNumber=0 is used (i.e. zero is not a
         special case).
         For an SFP cage, it is the faceplate number of the SFP cage (e.g. 1).
         For an XFP cage, it is 256 + (the faceplate number of the XFP cage).  For
         example 257.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 0..511
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1ClockConfigEntry 2}

dsx1SfpClkConfigSyncSource	OBJECT-TYPE
          SYNTAX INTEGER {
                            tributary1 (1),
                            tributary2 (2),
			    serdes (3)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Select as sync source between clock recovered from Serdes or Tributary clock.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1..3
            DEFVALUE: 3
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "

::= {dsx1ClockConfigEntry 3}	

dsx1SfpClkConfigDegradeAlarm	OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Enable degrade alarm on los pin.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1
            DEFVALUE: 1
            UNITS: NA 
            SPARSE: NO
            DESCR: Only Default value enable allowed in R4.4x 
        "

::= {dsx1ClockConfigEntry 4}	

dsx1SfpClkConfigSoftwareReset	OBJECT-TYPE
          SYNTAX INTEGER {
                            softwareReset (0),
                            noSoftwareReset (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "noSoftwareReset (default value), this is the value that is returned when read the attribute. 
	  softwareReset, this is the value that can be set by the manager. This value will trigger a software reset. 
.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1
            DEFVALUE: 1
            UNITS: NA 
            SPARSE: NO
            DESCR: Only Default value allowed in R4.4x
        "
::= {dsx1ClockConfigEntry 5}	

dsx1SfpClkAlarmState	OBJECT-TYPE
          SYNTAX  INTEGER (0..255)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          " serdesFailAlarm (1):Failure of the SERDES clock alarm (active high)   
            tributary1FailAlarm  (2): Failure alarm: the tributary 1 clock is not present (active high)           
            tributary2FailAlarm (4):Failure alarm: the tributary 2 clock is not present (active high)
            tributaryFailAlarm (8):Sync fail alarm: all the selected tribs in LOS
	    aisSyncFail(16):Sync fail alarm: AIS (Alarm Indication signal) on the tributary selected as sync source(active high)	    
	    lofSyncFail(32):Sync fail alarm: LOF(loss of Framing) on the tributary selected as sync source(active high) 
	    losSyncFail(64):Sync fail alarm: LOS (loss of signal) on the tributary selected as sync source(active high) 
	    degradeAlarm(128):
	    Alarm state bitmap.
                           bit 0: high value indicates serdesFailAlarm 
                           bit 1:  high value indicates tributary1FailAlarm
                           bit 2:  high value indicates tributary2FailAlarm
                           bit 3:  high value indicates tributaryFailAlarm
			   bit 4:  high value indicates AIS SyncFail
			   bit 5:  high value indicates LOF SyncFail
			   bit 6:  high value indicates lOS SyncFail
			   bit 7:   high value indicates degradeAlarm

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: Not Supported
        "
::= {dsx1ClockConfigEntry 6}	

dsx1SfpClkConfigClkControlSignal	OBJECT-TYPE
          SYNTAX INTEGER {
                            squarewave (0),
                            pll (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "SEL_LOC_OR_SRC. Control signal to select the VTCXO control signal
	  (PWM) between DPLL output and a ~4MHZ square wave signal
	  generated dividing the same system clock.
	 		0  Square wave generated from system clock.
	 		1  Control signal from PLL(Phase locked loop).
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1ClockConfigEntry 7}	

dsx1SfpClkConfigClkSource	OBJECT-TYPE
          SYNTAX INTEGER {
                            clk125mloc (0),
			    clk125mrec (1)

                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "CLK_SOURCE_SEL. This bit is used to select between local VTCXO and
	   CLOCK FROM SERDES as synchronization source for the signals:
	   CK_EN-25M and CK-EN-ETSI-ANSI 
	   1  CK-125M-REC
	   0  CK-125M-LOC
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1
            DEFVALUE: 1
            UNITS: NA 
            SPARSE: NO
            DESCR: Only Default value allowed in R4.4x 
        "
::= {dsx1ClockConfigEntry 8}	

dsx1SfpClkRowStatus OBJECT-TYPE 
	SYNTAX	RowStatus
	ACCESS	read-write
	STATUS  mandatory
	DESCRIPTION
	"This object is used to create a new row or delete an
	 existing row in this table. The possible valid values are:
            (1) active
            (4) createdAndGo
            (6) destroy
        To create an entry in this table, this object should be set to
        CreateAndGo. To delete an entry from this table, RowStatus
        should be set to Destroy value by the SNMP manager.
	 ALCATEL NOTE:
	 ACCESS: NA        
	 USAGE: MAND
         PERSIST: NO   
	 INSRVMOD:NA
         RANGE:1 | 4 | 6	-- active (1), createAndGo (4), destroy (6)      
	 DEFVALUE: NA  
         UNITS: NA         
	 SPARSE: NA  "
::= { dsx1ClockConfigEntry  9}

dsx1SfpClkConfState	OBJECT-TYPE
          SYNTAX  INTEGER (0..31)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   Clock Configuration state bitmap.
                           bit 0: high value indicates SyncSource Configuration fail 
                           bit 1:  high value indicates DegradeAlarm Configuration fail 
                           bit 2:  high value indicates SoftwareReset Configuration fail 
                           bit 3:  high value indicates ClkControlSignal Configuration fail 
			   bit 4:  high value indicates ClkSource Configuration fail 
			  

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..31
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1ClockConfigEntry 10}	


--  ==================================================================
--  |           Start of 2E1/DS1 SFP General Configuration Table                |
--  ==================================================================

dsx1GeneralConfigTable OBJECT-TYPE
        SYNTAX			SEQUENCE OF   Dsx1GeneralConfigEntry
        ACCESS            	not-accessible
        STATUS            	mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP General configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 11
         "
::= {sfpMIB 11}

dsx1GeneralConfigEntry		OBJECT-TYPE
        SYNTAX			Dsx1GeneralConfigEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP general configuration parameters.
	A row entry is created or deleted in this table when the corresponding 
         operation on a row is done in dsx1ClockConfigTable"
       AUGMENTS { dsx1ClockConfigEntry } 
::= {dsx1GeneralConfigTable 1}

Dsx1GeneralConfigEntry ::=
        SEQUENCE {
            	 dsx1SfpReadyStatus                     INTEGER,
		 dsx1SfpGenConfigMarket			INTEGER,
		 dsx1SfpGenConfigTributary1		INTEGER,
		 dsx1SfpGenConfigTributary2		INTEGER,
		 dsx1SfpGenConfigSERDES			INTEGER,
		 dsx1SfpGenConfigSERDESLoopBack		INTEGER,
		 dsx1SfpGenConfigSGMIILocalLoopBack	INTEGER,
		 dsx1SfpGenConfigSGMIIRemoteLoopBack	INTEGER,
		 dsx1SfpGenConfigLIUReset		INTEGER,
		 dsx1SfpGenConfigLIUHighImpedence	INTEGER,
		 dsx1SfpGenConfigState			INTEGER
		 		 
        }

dsx1SfpReadyStatus	 OBJECT-TYPE
          SYNTAX INTEGER {
                            notready (0),
                            ready (1)
                        }
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "This is the host  SFP status.
            ready (0):          
            notready (1):          
            
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: No 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: SFP Ready state
        "
::= {dsx1GeneralConfigEntry 1}



dsx1SfpGenConfigMarket	 OBJECT-TYPE
          SYNTAX INTEGER {
                            etsi (0),
                            ansi (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " ETSI (0):          used for ETSI Market for E1 Lines.
            ANSI (1):          used for ANSI Market for T1/DS1 Lines.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: ETSI vs ANSI selection
	     	   Only etsi value allowed in R4.4x 
        "
::= {dsx1GeneralConfigEntry 2}


dsx1SfpGenConfigTributary1	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Enable/Disable Tributary1.
            disable (0):          disable Tributary 1.
            enable  (1):          enable Tributary 1.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: Not Supported

        "

::= {dsx1GeneralConfigEntry 3}	


dsx1SfpGenConfigTributary2	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Enable/Disable Tributary2.
            disable (0):          Disable Tributary 2.
            enable  (1):          enable Tributary 0.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: Not supported

        "

::= {dsx1GeneralConfigEntry 4}		


dsx1SfpGenConfigSERDES	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " disable (0):          Disable SERDES device.
            enable  (1):          enable SERDES device.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1
            DEFVALUE: 1
            UNITS: NA 
            SPARSE: NO
            DESCR:  Only Default value allowed in R4.4x 
        "

::= {dsx1GeneralConfigEntry 5}

dsx1SfpGenConfigSERDESLoopBack	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Enable of SERDES device. When held low, the SERDES
           device enters in an ultralow-power idle state and 
	   the transmitter output pins are set to high impedance 
	   state 
.
            disable (0):          Disable SERDES LoopBack.
            enable  (1):          enable SERDES LoopBack.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: Not Supported 
		  When held high the loopback function on the SERDES 
		   is enabled, the transmitter serial data is directly
		   routed to the inputs of the receiver.
        "

::= {dsx1GeneralConfigEntry 6}	

dsx1SfpGenConfigSGMIILocalLoopBack	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " disable (0):          SGMII local loopback disable towards CESoP.
            enable  (1):          SGMII local loopback enable towards CESoP.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: Not Supported
        "

::= {dsx1GeneralConfigEntry 7}	

dsx1SfpGenConfigSGMIIRemoteLoopBack	 OBJECT-TYPE
          SYNTAX INTEGER {
                            disable (0),
                            enable (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " disable (0):          SGMII remote  loopback disable towards serdes.
            enable  (1):          SGMII remote loopback enable towards serdes.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR:  Not Supported
        "

::= {dsx1GeneralConfigEntry 8}	

dsx1SfpGenConfigLIUReset	 OBJECT-TYPE
          SYNTAX INTEGER {
                            reset (0),
                            noReset (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " reset (0):          A low level value (for more than 100ns) forces the LIU to reset state.
	  This is the value that can be set by the manager. This value will trigger a LIU reset. 
            noReset  (1):     This is the value that is returned when read the attribute.     
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1
            DEFVALUE: 1
            UNITS: NA 
            SPARSE: NO
            DESCR: Only Default value allowed in R4.4x 
        "

::= {dsx1GeneralConfigEntry 9}	
dsx1SfpGenConfigLIUHighImpedence	 OBJECT-TYPE
          SYNTAX INTEGER {
                            low (0),
                            high (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          " low (0):   
            high  (1):        A high level value places all transmitter drivers of the LIU in high Impedence state.    
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0.1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "

::= {dsx1GeneralConfigEntry 10}	

dsx1SfpGenConfigState	OBJECT-TYPE
          SYNTAX  INTEGER (0..255)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   General Configuration state bitmap.
                           bit 0: high value indicates Market Configuration fail 
                           bit 1:  high value indicates Tributory1 Configuration fail 
                           bit 2:  high value indicates Tributory2 Configuration fail 
                           bit 3:  high value indicates SERDES Configuration fail 
			   bit 4:  high value indicates SERDES local loopback Configuration fail 
			   bit 5:  high value indicates SGMII local loopback Configuration fail 
                           bit 6:  high value indicates SGMII remote loopback Configuration fail 
                           bit 7:  high value indicates LIU Highimpedence Configuration fail 
			   

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..255
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1GeneralConfigEntry 11}	

--  ==================================================================
--  |           Start of 2E1/DS1 SFP dsx1 TDM interface Table                |
--  ==================================================================

dsx1PWTDMInterfaceTable	OBJECT-TYPE
        SYNTAX			SEQUENCE OF Dsx1PWTDMInterfaceEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP PW TDM interface  configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 4 
         "
::= {sfpMIB 12}

dsx1PWTDMInterfaceEntry	OBJECT-TYPE
        SYNTAX			Dsx1PWTDMInterfaceEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP TDM Interface parameters.
	A row entry is created or deleted in this table when the corresponding 
         operation on a row is done in dsx1ClockConfigTable"
        AUGMENTS { dsx1ClockConfigEntry }
::= {dsx1PWTDMInterfaceTable 1}

Dsx1PWTDMInterfaceEntry ::=
        SEQUENCE {
                 dsx1PWTDMInterfaceMarket		INTEGER,
		 dsx1PWTDMInterfaceWindowLength  	INTEGER,
		 dsx1PWTDMInterfaceWindowNumber  	INTEGER,
		 dsx1PWTDMInterfaceConfState            INTEGER    
        }


dsx1PWTDMInterfaceMarket	 OBJECT-TYPE
          SYNTAX INTEGER {
                            etsi (0),
                            ansi (1)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "etsi(0):          used for ETSI Market for E1 Lines.
           ansi (1):          used for ANSI Market for T1/DS1 Lines.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: ETSI vs ANSI selection
	    Only etsi value is allowed in R4.4x
        "
::= {dsx1PWTDMInterfaceEntry 1}


dsx1PWTDMInterfaceWindowLength	 OBJECT-TYPE
          SYNTAX INTEGER (0..255)
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Window length measured in unit of 10 ms.
	   Eg. wndow_len = 10 means window is 10*10 = 100ms long
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..255
            DEFVALUE: 10
            UNITS: milliseconds
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMInterfaceEntry 2}


dsx1PWTDMInterfaceWindowNumber	 OBJECT-TYPE
          SYNTAX INTEGER (0..255)
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Consecutively detected window number.
		Eg. wndow_num = 20 means if packets lost in consecutive 20
		windows, then assert CESalarm (Circuit emulation service alarm); otherwise, deassert CESalarm
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...255
            DEFVALUE: 20
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMInterfaceEntry 3}
	
dsx1PWTDMInterfaceConfState	OBJECT-TYPE
          SYNTAX  INTEGER (0..7)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   PWTDM Interface Configuration state bitmap.
                           bit 0: high value indicates Market Configuration fail 
                           bit 1:  high value indicates Window Length Configuration fail 
                           bit 2:  high value indicates Window Number Configuration fail 
                           
			  

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMInterfaceEntry 4}
	
--  ==================================================================
--  |           Start of 2E1/DS1 SFP dsx1 TDMoP configuration Table                |
--  ==================================================================

dsx1PWTDMConfigTable 	OBJECT-TYPE
        SYNTAX		SEQUENCE OF Dsx1PWTDMConfigEntry
        ACCESS		not-accessible
        STATUS		mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP TDMoP pseudowire  configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 19
         "
::= {sfpMIB 13}

dsx1PWTDMConfigEntry	OBJECT-TYPE
        SYNTAX		Dsx1PWTDMConfigEntry
        ACCESS		not-accessible
        STATUS		mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP TDMoP parameters."
        INDEX           {dsx1SfpPWTDMLogicalSlot, dsx1SfpPWTDMFaceplateNumber, dsx1PWTDMTributaryIndex}
::= {dsx1PWTDMConfigTable 1}

Dsx1PWTDMConfigEntry ::=
        SEQUENCE {
                 dsx1SfpPWTDMLogicalSlot	INTEGER,
                 dsx1SfpPWTDMFaceplateNumber	INTEGER,
		 dsx1PWTDMTributaryIndex	INTEGER,
		 dsx1PWTDMChannel		INTEGER,
		 dsx1PWTDMRtpMode		INTEGER,
		 dsx1PWTDMPacketLength		INTEGER,
		 dsx1PWTDMSourceAddressPointer	INTEGER,
		 dsx1PWTDMSourceAddress		MacAddress,
		 dsx1PWTDMDestinationMac	MacAddress,
		 dsx1PWTDMPriority		INTEGER,
		 dsx1PWTDMVlanId		INTEGER,
		 dsx1PWTDMECIDEthToTDM		INTEGER,
		 dsx1PWTDMECIDTDMToEth		INTEGER,
		 dsx1PWTDJitterBufferSize       INTEGER,
		 dsx1PWTDMConsequenceNumber	INTEGER,
		 dsx1PWTDMClock			INTEGER,
		 dsx1PWTDMFPGAVersion		DisplayString,
		 dsx1PWTDMRowStatus        	RowStatus,
		 dsx1PWTDMConfState		INTEGER

        }


dsx1SfpPWTDMLogicalSlot OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the first index of the dsx1PWTDMConfig Table.  

         Normally, this object contains the Logical Slot ID (as defined in
         the Equipment MIB) of the card which has the SFP cage of interest
         on its faceplate.
         If that card does not have a Logical Slot ID, the Logical Slot ID
         of a related card is used.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1..65535
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 1}

-- Define the second  index column of the  dsx1PWTDMConfig Table.

dsx1SfpPWTDMFaceplateNumber     OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the second index of the dsx1PWTDMConfig Table. 
         It is the number of the SFP cage, as shown on the card 
         faceplate.  If the faceplate cage number is zero, 
         sfpDiagSfpFaceplateNumber=0 is used (i.e. zero is not a
         special case).
         For an SFP cage, it is the faceplate number of the SFP cage (e.g. 1).
         For an XFP cage, it is 256 + (the faceplate number of the XFP cage).  For
         example 257.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 0..511
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 2}

-- Define the third (and final)  index column of the dsx1PWTDMConfig Table.

dsx1PWTDMTributaryIndex OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the third index of the dsx1PWTDMConfigTable.  

         Normally, this object used to select the tributary selecton (1 or 2 or 3)  of the 2xE1/DS1 FPGA  
	 where value 1 selects tributary 1, value 2 selects tributary 2 and value 3 selects both the tributories
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1,2,3
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 3}

dsx1PWTDMChannel	OBJECT-TYPE
          SYNTAX INTEGER {
                            close (0),
			    open (1)

                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Chan_ena
	  0: close this channel;
	  1: open this channel
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 4}

dsx1PWTDMRtpMode	OBJECT-TYPE
          SYNTAX INTEGER {
                            acr (0),
			    dcr (1)

                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Rtp_present
	  0: rtp is not present, configure to ACR mode;
	  1: rtp is present, configure DCR mode

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: this attribute is NOT supported in ISAM R4.4.x 

        "
::= {dsx1PWTDMConfigEntry 5}

dsx1PWTDMPacketLength	OBJECT-TYPE
          SYNTAX INTEGER 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Config packet length  bits (13:0)

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...16382
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 6}

dsx1PWTDMSourceAddressPointer	OBJECT-TYPE
          SYNTAX INTEGER (0..3) 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "SA select 2 bits field (TDM2Eth)
	   00 - Select Source Address 0
	   01 - Select Source Address 1
	   10 - Select Source Address 2
	   11 - Select Source Address 3

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...3
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR:This is not supported in the R4.4x
	    Source address pointer is handled internally in the sfp hardware.
        "
::= {dsx1PWTDMConfigEntry 7}

dsx1PWTDMSourceAddress	OBJECT-TYPE
          SYNTAX 	MacAddress
          ACCESS        read-write
          STATUS        mandatory
          DESCRIPTION     
          "MAC SA for each tributary

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 8}

dsx1PWTDMDestinationMac	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "MAC DA for each tributary

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 9}

dsx1PWTDMPriority	OBJECT-TYPE
          SYNTAX INTEGER (0..7) 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "802.1p 3 bits field.

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 10}

dsx1PWTDMVlanId		OBJECT-TYPE
          SYNTAX INTEGER (0..4095) 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Vlan ID.

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...4095
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 11}

dsx1PWTDMECIDEthToTDM	OBJECT-TYPE
          SYNTAX INTEGER (0..1048575) 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "ECID (Emulated Circuit Identifer) 20 bit field Eth2Tdm.

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..1048575
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 12}

dsx1PWTDMECIDTDMToEth	OBJECT-TYPE
          SYNTAX INTEGER (0..1048575) 
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "ECID (Emulated Circuit Identifer)  20 bit field Tdm2Eth.

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..1048575
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 13}

dsx1PWTDJitterBufferSize OBJECT-TYPE
          SYNTAX INTEGER (0..1023)  
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "Jitter Buffer Queue size. Actual size + configurable size + 1. Example configuring size of 7 mean buffer size of 8 packets

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE:0..1023
            DEFVALUE: NA
            UNITS: Packets 
            SPARSE: NO
            DESCR: NA
        "

::= {dsx1PWTDMConfigEntry 14}

dsx1PWTDMConsequenceNumber	OBJECT-TYPE
          SYNTAX INTEGER  (0..7)
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "New Sequence number

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 15}

dsx1PWTDMClock	OBJECT-TYPE
          SYNTAX INTEGER {
                            differentialMode (0),
			    loopbackMode (1),
			    referenceNodeTimeMode(2),
			    addaptiveMode(3)
                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "TDM_clock_strategy.
	    00: differential mode;
	    01: loop back mode;
            10:reference Node time mode;
	    11: adaptive mode.

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 2
            DEFVALUE: 2
            UNITS: NA 
            SPARSE: NO
            DESCR: only referenceNodeTimeMode is supported in R4.4x

        "
::= {dsx1PWTDMConfigEntry 16}

dsx1PWTDMFPGAVersion	OBJECT-TYPE
          SYNTAX	DisplayString
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "FPGA software version.

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMConfigEntry 17}




dsx1PWTDMRowStatus OBJECT-TYPE 
	SYNTAX	RowStatus
	ACCESS	read-write
	STATUS  mandatory
	DESCRIPTION
	"This object is used to create a new row or delete an
	 existing row in this table. The possible valid values are:
            (1) active
            (4) createdAndGo
            (6) destroy
        To create an entry in this table, this object should be set to
        CreateAndGo. To delete an entry from this table, RowStatus
        should be set to Destroy value by the SNMP manager.
	 ALCATEL NOTE:
	 ACCESS: NA        
	 USAGE: MAND
         PERSIST: NO   
	 INSRVMOD:NA
         RANGE:1 | 4 | 6	-- active (1), createAndGo (4), destroy (6)      
	 DEFVALUE: NA  
         UNITS: NA         
	 SPARSE: NA  "
::= { dsx1PWTDMConfigEntry 18}
	
dsx1PWTDMConfState	OBJECT-TYPE
          SYNTAX  INTEGER (0..8191)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   PWTDM  Configuration state bitmap.
                           bit 0: high value indicates Channel Configuration fail 
                           bit 1:  high value indicates  RTP modeSourceAddress PointerConfiguration fail 
                           bit 2:  high value indicates Packet length Configuration fail 
                           bit 3:  high value indicates SourceAddress Pointer Configuration fail 
                           bit 4:  high value indicates SourceAddress Configuration fail 
			   bit 5:  high value indicates Destination Address Configuration fail 
                           bit 6:  high value indicates Priority Configuration fail 
                           bit 7:  high value indicates vlan index Configuration fail 
                           bit 8:  high value indicates ECID eth2tdm Configuration fail 
                           bit 9:  high value indicates ECID tdm2eth Configuration fail 
			   bit 10: high value indicates Jitter buffer size Configuration fail 
                           bit 11:  high value indicates  Consequence number Configuration fail 
                           bit 12:  high value indicates clock Configuration fail 
                           	
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..8191
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "

::= { dsx1PWTDMConfigEntry 19}

--  ==================================================================
--  |           Start of 2E1/DS1 SFP dsx1LineInterfaceUnit Table                |
--  ==================================================================

dsx1LineInterfaceUnitTable	OBJECT-TYPE
        SYNTAX			SEQUENCE OF Dsx1LineInterfaceUnitEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP Line Interface Unit configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 4
         "
::= {sfpMIB 14}

dsx1LineInterfaceUnitEntry	OBJECT-TYPE
        SYNTAX			Dsx1LineInterfaceUnitEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP Line Interface Unit parameters.
	A row entry is created or deleted in this table when the corresponding 
         operation on a row is done in dsx1PWTDMConfigTable "
        AUGMENTS { dsx1PWTDMConfigEntry }
::= {dsx1LineInterfaceUnitTable 1}

Dsx1LineInterfaceUnitEntry ::=
       SEQUENCE {
                 dsx1SfpLIUConfLine 		INTEGER,
		 dsx1SfpLIURxSensitivity 	INTEGER,
		 dsx1SfpLIUTxConfig 		INTEGER,
		 dsx1SfpLIUConfState            INTEGER		
        }


dsx1SfpLIUConfLine	OBJECT-TYPE
          SYNTAX INTEGER {
                            e1 (0),
			    t1 (1)

                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "This bit selects the E1 or T1/J1 operation mode globally.
		0: E1 mode is selected.
		1: T1/J1 mode is selected.
		Note: After bit T1/E1 is changed: Before accessing any other regisers a delay of 50us is required to 
		allow the internal clocking to be settled.
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: E1 Mode alone allowed in R4.4x 
        "
::= {dsx1LineInterfaceUnitEntry 1}	

dsx1SfpLIURxSensitivity	OBJECT-TYPE
          SYNTAX INTEGER {
                            shorthaul (0),
			    longhaul (1)

                        }
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "0: Receive equalizer off (short haul receiver)
	   1: Receive equalizer on (long haul receiver)
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0,1
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1LineInterfaceUnitEntry 2}

dsx1SfpLIUTxConfig	OBJECT-TYPE
          SYNTAX INTEGER (1..12)             
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION     
          "These bits select the transmit template/LBO for short-haul/long-haul applications.

value  T1/E1/J1    TCLK        Cable    Cable range/  Allowable Cable 
                            impedance      LBO            loss
0001     E1     2.048 MHz      75 O	    -		0-43 dB
0010     E1     2.048 MHz     120 O         -		0-43 dB
0011    DSX1    1.544 MHz     100 O       0-133 ft	0-0.6 dB
0100    DSX1    1.544 MHz     100 O     133-266 ft	0.6-1.2 dB
0101    DSX1    1.544 MHz     100 O     266-399 ft	1.2-1.8 dB
0110    DSX1    1.544 MHz     100 O     399-533 ft	1.8-2.4 dB
0111    DSX1    1.544 MHz     100 O     533-655 ft	2.4-3.0 dB
1000     J1     1.544 MHz     110 O       0-655 ft	0-3.0 dB
1001    DS1     1.544 MHz     100 O       0  dB LBO	0-36 dB
1010    DS1     1.544 MHz     100 O     -7.5 dB LBO	0-28.5 dB
1011    DS1     1.544 MHz     100 O    -15.0 dB LBO	0-21 dB
1100    DS1     1.544 MHz     100 O      22.5 dB LBO	0-13.5 dB
            
        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 1,2
            DEFVALUE: 2
            UNITS: NA 
            SPARSE: NO
            DESCR: Values 1 and 2 (E1 alone) allowed in R4.4x
        "
::= {dsx1LineInterfaceUnitEntry 3}

	
dsx1SfpLIUConfState	OBJECT-TYPE
          SYNTAX  INTEGER (0..7)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   Line Interface Unit Configuration state bitmap.
                           bit 0: high value indicates ConfLine Configuration fail 
                           bit 1: high value indicates Receiver Sensitivity Configuration fail 
                           bit 2: high value indicates Transmitter Configuration fail 
                          
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO
            INSRVMOD: NA
            RANGE: 0..7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "

::= { dsx1LineInterfaceUnitEntry 4}

--  ==================================================================
--  |           Start of 2E1/DS1 SFP dsx1 Framer Table                |
--  ==================================================================

dsx1FramerTable 	OBJECT-TYPE
        SYNTAX		SEQUENCE OF Dsx1FramerEntry
        ACCESS		not-accessible
        STATUS		mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP Framer configuration.
        ALCATEL NOTE:
            TABLESIZE: 5
         "
::= {sfpMIB 15}

dsx1FramerEntry	OBJECT-TYPE
        SYNTAX		Dsx1FramerEntry
        ACCESS		not-accessible
        STATUS		mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP Framer parameters.
	A row entry is created or deleted in this table when the corresponding 
         operation on a row is done in dsx1PWTDMConfigTable"
        AUGMENTS { dsx1PWTDMConfigEntry }
::= {dsx1FramerTable 1}
	

Dsx1FramerEntry ::=
       SEQUENCE {
                 dsx1FramerGenConfig		INTEGER,
		 dsx1FramerLIU2IWFConfig	INTEGER,
		 dsx1FramerIWF2LIUConfig	INTEGER,
		 dsx1FramerAlarmStatus		INTEGER,
		 dsx1FramerConfState     	INTEGER
                }


dsx1FramerGenConfig	OBJECT-TYPE
          SYNTAX INTEGER (0..255)
	  
                      
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION    "Generai Configuration bitmap.
                           bit 0: Framed (1) or unframed (0) TDM traffic selection 
                           bit 1: RAI transparent  (only for E1)
				RAI: Remote Alarm Indication defined in E1 where it is derived from the NFAS A bit.
                           bit 2:  REI transparent (only for E1)
				REI: Remote Error Indication usually defined for SDH.
                           bit 3:  Loop toward IWF on LIU side of framer
			   bit 4:  Loop toward LIU on IWF side of framer - Not supported from R4.6x
			   bit 5:  Loop toward LIU on LIU side of framer
			   bit 6:  Loop toward IWF on IWF side of framer - Not supported from R4.6x
			   bit 7:  Extended Super Frame (ESF only for DS1) 			

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..127
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: Bit 0 to Bit 6 supported in R4.4x, Bit 4 and Bit 6 are not supported from R4.6x
        "
	::= {dsx1FramerEntry 1}

dsx1FramerLIU2IWFConfig	OBJECT-TYPE
          SYNTAX INTEGER (0..255)	
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION    "Line Interface unit to Inter working Function Configuration bitmap.
                           bit 0: Enable performance monitoring toward IWF
                           bit 1: Enable AIS toward IWF 
                           bit 2: Enable RAI  (Remote Alarm Indication) toward IWF  (only for E1)
                           bit 3: Enable REI (Remote Error Indication)  toward IWF (only for E1)
			   bit 4: Force RAI (Remote Alarm Indication) toward IWF  (only for E1)
			   bit 5: Force REI(Remote Error Indication)  toward IWF (only for E1)
			   bit 6: Force AIS toward IWF 
			   bit 7: Unused

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..15
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: only bit 0..3 are supported 
        "
	::= {dsx1FramerEntry 2}


dsx1FramerIWF2LIUConfig	OBJECT-TYPE
          SYNTAX INTEGER (0..255)
          ACCESS            read-write
          STATUS            mandatory
          DESCRIPTION    "Generai Configuration bitmap.
                           bit 0: Enable performance monitoring toward LIU
                           bit 1: Enable AIS toward LIU
                           bit 2: Enable RAI toward LIU  (only for E1)
                           bit 3: Enable REI toward LIU (only for E1)
			   bit 4: Force RAI toward LIU  (only for E1)
			   bit 5: Force REI toward LIU for CH1 (only for E1)
			   bit 6: Force AIS toward LIU for CH1
			   bit 7: Force E1 multiframe structure generation (payload A5) toward LIU
			
                      

        ALCATEL NOTE: 
            ACCESS: RW
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0..15
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: only bit 0..3 are supported 
        "
	::= {dsx1FramerEntry 3}

dsx1FramerAlarmStatus	OBJECT-TYPE
          SYNTAX INTEGER (0..262143)           
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION    "Alarm Status bitmap. High value indicates an alarm has occured
	  Bit 0	: CRC4 (Cylic Redundancy check-4) threshold alarm from LIU (only for E1) 
	  Bit 1	: CRC4 (Cylic Redundancy check-4) alarm from LIU (only for E1) 
	  Bit 2	: CRC6 (Cylic Redundancy check-6) alarm from LIU (only for ESF DS1) 
	  Bit 3	: RDI (Remote Defect Alarm) alarm from LIU (only for E1) 
	  Bit 4	: REI (Remote Error Indication) alarm from LIU (only for E1) 
          Bit 5	: LOMF (Loss of Multi-frame) alarm from LIU 
	  Bit 6	: LOF (loss of framing) alarm from LIU  
	  Bit 7	: LOS (loss of signal) alarm from LIU  
	  Bit 8	: AIS (alarm Indication Signal) alarm from LIU 
  	  Bit 9	: CRC4 (Cylic Redundancy check-4) threshold alarm from IWF  (only for E1)
	  Bit 10: CRC4 (Cylic Redundancy check-4) alarm from IWF (only for E1)
	  Bit 11: CRC6 (Cylic Redundancy check-6) alarm from IWF (only for ESF DS1)
	  Bit 12: RDI (Remote Defect Alarm) alarm from IWF (only for E1)
	  Bit 13: REI (Remote Error Indication) alarm from IWF (only for E1)
	  Bit 14: LOMF (Loss of Multi-frame) alarm from IWF  
	  Bit 15: LOF (loss of framing) alarm from IWF  
	  Bit 16: LOS (loss of signal) alarm from IWF  
	  Bit 17: AIS (alarm Indication Signal) alarm from IWF 

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: Not Suported
        "
	::= {dsx1FramerEntry 4}


	
dsx1FramerConfState	OBJECT-TYPE
          SYNTAX  INTEGER (0..7)
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "   Framer Configuration state bitmap.
                           bit 0: high value indicates General Configuration fail 
                           bit 1: high value indicates LIU2IWF Configuration fail 
                           bit 2: high value indicates IWF2LIU Configuration fail 
                          
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..7
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "

::= {dsx1FramerEntry  5}
	


--  ==================================================================
--  | Start of 2E1/DS1 SFP dsx1 Protocol Engine / jitter buffer Table           |
--  ==================================================================

dsx1PWTDMProtocolEngineTable	OBJECT-TYPE
        SYNTAX			SEQUENCE OF Dsx1PWTDMProtocolEngineEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "This table contains the 2E1/DS1 SFP protocol engine configuration
         information.
        ALCATEL NOTE:
            TABLESIZE: 11
	   DESCR: This Table is not supported, handled internally in the sfp hardware. 
         "
::= {sfpMIB 16}


dsx1PWTDMProtocolEngineEntry	OBJECT-TYPE
        SYNTAX			Dsx1PWTDMProtocolEngineEntry
        ACCESS			not-accessible
        STATUS			mandatory
        DESCRIPTION     
        "Each entry consists of a list of 2E1/DS1 SFP protocol engine parameters."
        INDEX           {dsx1SfpProtLogicalSlot, dsx1SfpProtFaceplateNumber}
::= {dsx1PWTDMProtocolEngineTable 1}

Dsx1PWTDMProtocolEngineEntry ::=
        SEQUENCE {
                 dsx1SfpProtLogicalSlot		INTEGER,
                 dsx1SfpProtFaceplateNumber	INTEGER,
		 dsx1PWTDMProtocolEngineDA1	MacAddress,
		 dsx1PWTDMProtocolEngineDA2	MacAddress,
		 dsx1PWTDMProtocolEngineDA3		MacAddress,
		 dsx1PWTDMProtocolEngineDA4		MacAddress,
		 dsx1PWTDMProtocolEngineSA1		MacAddress,
		 dsx1PWTDMProtocolEngineSA2		MacAddress,
		 dsx1PWTDMProtocolEngineSA3		MacAddress,
		 dsx1PWTDMProtocolEngineSA4		MacAddress,
		 dsx1RTPPayloadtype             	INTEGER
		        }


dsx1SfpProtLogicalSlot OBJECT-TYPE
        SYNTAX           INTEGER 
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the first index of the dsx1sfpLogicalSlot.  

         Normally, this object contains the Logical Slot ID (as defined in
         the Equipment MIB) of the card which has the SFP cage of interest
         on its faceplate.
         If that card does not have a Logical Slot ID, the Logical Slot ID
         of a related card is used.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 1..65535
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 1}

-- Define the second  index column of the dsx1PWTDMProtocolEngineTable.

dsx1SfpProtFaceplateNumber     OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the second index of the dsx1PWTDMProtocolEngineTable. 
         It is the number of the SFP cage, as shown on the card 
         faceplate.  If the faceplate cage number is zero, 
         sfpDiagSfpFaceplateNumber=0 is used (i.e. zero is not a
         special case).
         For an SFP cage, it is the faceplate number of the SFP cage (e.g. 1).
         For an XFP cage, it is 256 + (the faceplate number of the XFP cage).  For
         example 257.
	 
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NO
            RANGE: 0..511
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 2}



dsx1PWTDMProtocolEngineDA1	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Destination Address  1 in DA pool (for Eth2TDM packet discard, verify whether
received packets DA is equal to this DA pool or not)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 3}
dsx1PWTDMProtocolEngineDA2	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Destination Address  2 in DA pool (for Eth2TDM packet discard, verify whether
received packets DA is equal to this DA pool or not)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 4}

dsx1PWTDMProtocolEngineDA3	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Destination Address  3 in DA pool (for Eth2TDM packet discard, verify whether
received packets DA is equal to this DA pool or not)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 5}

dsx1PWTDMProtocolEngineDA4	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Destination Address  4 in DA pool (for Eth2TDM packet discard, verify whether
received packets DA is equal to this DA pool or not)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 6}

dsx1PWTDMProtocolEngineSA1	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Source Address  1 in SA pool (TDM2Eth, config SA on per PW basis)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 7}
dsx1PWTDMProtocolEngineSA2	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Source Address  2 in SA pool (TDM2Eth, config SA on per PW basis)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 8}

dsx1PWTDMProtocolEngineSA3	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Source Address  3 in SA pool (TDM2Eth, config SA on per PW basis)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 9}

dsx1PWTDMProtocolEngineSA4	OBJECT-TYPE
          SYNTAX MacAddress
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "MAC Source Address  4 in SA pool (TDM2Eth, config SA on per PW basis)

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 10	}




dsx1RTPPayloadtype 	OBJECT-TYPE
          SYNTAX INTEGER (0..3) 
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "RTP payload type mode.
	   00 - PayloadType=all 0s, SSID=all 1s
	   01 - PayloadType=all 1s, SSID=all 1s
	   10 - PayloadType= ecid(18 downto 12), SSID=ecid
	   11 - PayloadType= vlan_id(6 downto 0), SSID= x0000 & vlan_id

        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: YES 
            INSRVMOD: NA
            RANGE: 0...3
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {dsx1PWTDMProtocolEngineEntry 11}


--  ==================================================================
--  |      Start of Host LT SFP Configuration Extension Table         |
--  ==================================================================

ltHostSfpConfigExtTable    OBJECT-TYPE
          SYNTAX           SEQUENCE OF LtHostSfpConfigExtEntry
          ACCESS           not-accessible
          STATUS           mandatory
          DESCRIPTION
          "This table contains the host shelf LT SFP configuration information
           regarding link assignement to LT on FD-REM
          ALCATEL NOTE:
              TABLESIZE: 288 (Full FX-8 with FELT-B --> 8 * 36)
          "
::= {sfpMIB 17}

ltHostSfpConfigExtEntry    OBJECT-TYPE
          SYNTAX           LtHostSfpConfigExtEntry
          ACCESS           not-accessible
          STATUS           mandatory
          DESCRIPTION
          "Each entry consists of a list of host shelf LT SFP configuration ltHostSfpConfigExtTable."
          INDEX           {ltHostConfigSfpLogSlotId, ltHostConfigExtSfpFaceplateNumber}
::= {ltHostSfpConfigExtTable 1}

LtHostSfpConfigExtEntry ::=
          SEQUENCE {
                   ltHostConfigExtSfpFaceplateNumber INTEGER,
                   ltHostSfpCableToRack              INTEGER,
                   ltHostSfpCableToShelf             INTEGER,
                   ltHostSfpCableToSlot              INTEGER,
                   ltHostConfigSfpLogSlotId          EqptSlotIndex
          }

ltHostConfigExtSfpFaceplateNumber   OBJECT-TYPE
        SYNTAX                    INTEGER
        ACCESS                    read-only
        STATUS                    mandatory
        DESCRIPTION
        "This is the SFP number on the LT identified by ltHostConfigExtSfpLogSlotId.
         ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: YES
            INSRVMOD: NA
            RANGE: 1..24
            DEFVALUE: NA
            UNITS: NA
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpConfigExtEntry 1}

ltHostSfpCableToRack       OBJECT-TYPE
          SYNTAX           INTEGER
          ACCESS           read-write
          STATUS           mandatory
          DESCRIPTION
          "This is the Rack ID of the expansion shelf LSM which the SFP
           is planned to cable to. Value 0 means that no rack is specified.
          ALCATEL NOTE:
              ACCESS: NA
              USAGE: OPT
              PERSIST: YES
              INSRVMOD: YES
              RANGE: 0..7
              DEFVALUE: 0
              UNITS: NA
              SPARSE: NO
              DESCR: NA
          "
::= {ltHostSfpConfigExtEntry 2}

ltHostSfpCableToShelf   OBJECT-TYPE
          SYNTAX          INTEGER
          ACCESS          read-write
          STATUS          mandatory
          DESCRIPTION
           "This is the Shelf ID of the expansion shelf LSM which
           the host shelf SFP is planned to cable to.
           Value 0 means that no shelf is specified.
          ALCATEL NOTE:
              ACCESS: NA
              USAGE: OPT
              PERSIST: YES
              INSRVMOD: YES
              RANGE: 0..4
              DEFVALUE: 0
              UNITS: NA
              SPARSE: NO
              DESCR: The shelf identified by ltHostSfpCableToRack and
                     ltHostSfpCableToShelf must already be planned.
          "
::= {ltHostSfpConfigExtEntry 3}

ltHostSfpCableToSlot       OBJECT-TYPE
          SYNTAX           INTEGER
          ACCESS           read-write
          STATUS           mandatory
          DESCRIPTION
          "This is the Slot ID of the expansion shelf LSM which
          the SFP is planned to cable to.
          Value 0 means that no slot is specified.
          ALCATEL NOTE:
              ACCESS: NA
              USAGE: OPT
              PERSIST: YES
              INSRVMOD: YES
              RANGE: DEP / depends on the shelf type.
              DEFVALUE: 0
              UNITS: NA
              SPARSE: NO
              DESCR: NA
          "
::= {ltHostSfpConfigExtEntry 4}

ltHostConfigSfpLogSlotId   OBJECT-TYPE
          SYNTAX           EqptSlotIndex
          ACCESS           read-only
          STATUS           mandatory
          DESCRIPTION
          "The logical slot id of the board where the remote LT is plugged in
          ALCATEL NOTE:
              ACCESS: NA
              USAGE: OPT
              PERSIST: YES
              INSRVMOD: YES
              RANGE: DEP / depends on the shelf type.
              DEFVALUE: 0
              UNITS: NA
              SPARSE: NO
              DESCR: NA
          "
::= {ltHostSfpConfigExtEntry 5}

--  ==================================================================
--  |               Start of LT Host SFP Status Table                |
--  ==================================================================

ltHostSfpStatusTable      OBJECT-TYPE
      SYNTAX            SEQUENCE OF LtHostSfpStatusEntry
          ACCESS            not-accessible
          STATUS            mandatory
          DESCRIPTION     
          "This table contains the host shelf SFP or XFP status information.
        ALCATEL NOTE:
            TABLESIZE: 26
        "
::= {sfpMIB 18}

ltHostSfpStatusEntry      OBJECT-TYPE
        SYNTAX          LtHostSfpStatusEntry
        ACCESS          not-accessible
        STATUS          mandatory
        DESCRIPTION
          "Each entry consists of a list of SFP or XFP status parameters."
        INDEX           {ltHostStatusSfpLogSlotId, ltHostStatusSfpFaceplateNumber}
::= {ltHostSfpStatusTable 1}

LtHostSfpStatusEntry ::=
        SEQUENCE {
                 ltHostStatusSfpFaceplateNumber  INTEGER,
                 ltLanxPortNumber                INTEGER,
                 ltHostSfpDownlinkStatus         INTEGER,
                 ltHostSfpCabledToRack           INTEGER,
                 ltHostSfpCabledToShelf          INTEGER,
                 ltHostSfpCabledToSlot           INTEGER,
                 ltHostSfpType                   INTEGER,
                 ltHostSfpCablingMismatch        INTEGER,
                 ltHostSfpCabledToPhysAddress    PhysAddress,
                 ltHostStatusSfpLogSlotId        EqptSlotIndex

        }

ltHostStatusSfpFaceplateNumber OBJECT-TYPE
        SYNTAX               INTEGER
        ACCESS               read-only
        STATUS               mandatory
        DESCRIPTION     
        "This is the host shelf SFP or XFP cage number.
         For an SFP cage, it is the faceplate number of the SFP cage (e.g. 1).
         For an XFP cage, it is 256 + (the faceplate number of the XFP cage).  For
         example, if the XFP cage has faceplate label X1, 
         ltHostStatusSfpFaceplateNumber == 257.
         For LT, this is the port number.
         
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..258
            DEFVALUE: NA
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 1}

ltLanxPortNumber           OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        " This is the Physical LANX or IHUB port to which the SFP or XFP is 
         connected to. For an SHUB system, the lanxPortNumber here corresponds 
         to lanxInterfaceIndex in the LANX MIB. For an IHUB system, the 
         lanxPortNumber to the TmnxPortID to be used in the Timetra Port MIB.
         For IHUB this is an 32 bit encoded number for which the structure is
         defined in Timetra-TC-MIB.
         It is set to 0 for LT.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 1..26 for SHUB / 32 Bit Encoded number for IHUB
            DEFVALUE: DEP / depends on the ltHostStatusSfpFaceplateNumber.
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 2}

ltHostSfpDownlinkStatus OBJECT-TYPE
          SYNTAX INTEGER {
                            ok (1),
                            sfpNotPresent (2),
                            los (3),
                            txFail (4),
                            invalidAlcatelId (5),
                            unknown (6),
                            sfpControlFail (7),
                            notApplicable (8),
                            txFailAndLos (9),
                            sfpPlanDetectMismatch (10)
                        }
          ACCESS            read-only
          STATUS            mandatory
          DESCRIPTION     
          "This is the host shelf SFP or XFP status.
            ok (1):                     The SFP or XFP is operational.
            sfpNotPresent (2):          The SFP or XFP cage is empty.
            los (3):                    The SFP or XFP has detected Loss of Signal. 
            txFail (4):                 The SFP or XFP has detected Transmitter 
                                        Fail.
            invalidAlcatelId (5):       The SFP or XFP does not contain
                                        a valid Alcatel-Lucent ID.
            unknown (6):                The host expansion card is planned but
                                        not inserted.
            sfpControlFail (7):         The SFP or XFP is not responding, or the 
                                        I2C read failed.
            notApplicable (8):          Retrieval of status information from the
                                        SFP or XFP cage is not supported.
            txFailAndLos (9):           The SFP or XFP has detected both 
                                        Transmitter Fail and Loss of Signal.
            sfpPlanDetectMismatch (10): Not used - superseded by the
                                        ltHostSfpCablingMismatch object.
        ALCATEL NOTE: 
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: NA
            DEFVALUE: 6 / status is unknown
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 3}

ltHostSfpCabledToRack      OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Rack ID of the expansion shelf LSM to which the host shelf SFP
         is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..7
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
           DESCR: NA
        "
::= {ltHostSfpStatusEntry 4}

ltHostSfpCabledToShelf     OBJECT-TYPE
        SYNTAX           INTEGER
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
        "This is the Shelf ID of the expansion shelf LSM which the host shelf 
         SFP is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..4
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 5}

ltHostSfpCabledToSlot   OBJECT-TYPE
        SYNTAX            INTEGER
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION     
        "This is the Slot ID of the expansion shelf LSM which the host shelf SFP 
         is cabled to.
         Value 0 means that SFP is not cabled to any expansion shelf LSM.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: DEP / depends on the shelf type. 
            DEFVALUE: 0
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 6}

ltHostSfpType              OBJECT-TYPE
        SYNTAX            INTEGER 
        ACCESS            read-only
        STATUS            mandatory
        DESCRIPTION     
        "This variable indicates the type of the SFP or XFP, as read from the 
        device.
        It is a bitmap represented as a sum, therefore, it can represent
        multiple types (combination of types) simultaneously.
        The SFP type bits (0..7)  are as defined in standard SFF-8472.
        The XFP type bits (8..15) are as defined in standard INF-8077_v4.5.
        The SFP octet and the XFP octet are mutually exclusive (i.e. at least 
        one of the two octets is always zero).
        The various bit positions are:
             bit  0 (1):     1000BASE-SX
             bit  1 (2):     1000BASE-LX
             bit  2 (4):     1000BASE-CX
             bit  3 (8):     1000BASE-T
             bit  4 (16):    100BASE-LX/LX10
             bit  5 (32):    100BASE-FX
             bit  6 (64):    BASE-BX10
             bit  7 (128):   BASE-PX
             bit  8 (256):   reserved for a future XFP type, INF-8077
             bit  9 (512):   10GBASE-EW
             bit 10 (1024):  10GBASE-LW
             bit 11 (2048):  10GBASE-SW
             bit 12 (4096):  10GBASE-LRM
             bit 13 (8192):  10GBASE-ER
             bit 14 (16384): 10GBASE-LR
             bit 15 (32768): 10GBASE-SR
             bit 16 (65536):  2500BASE-SX
             bit 17 (131072): 2500BASE-LX
             bit 18 (262144): 2500BASE-BX

        If the ltHostSfpType is unknown or not specified, ltHostSfpType = 0.
        ALCATEL NOTE:
            ACCESS: RO
            USAGE: NA
            PERSIST: NO 
            INSRVMOD: NA
            RANGE: 0..65535
            DEFVALUE: 0 / ltHostSfpType unknown 
            UNITS: NA 
            SPARSE: NO
            DESCR: NA
        "
::= {ltHostSfpStatusEntry 7}

ltHostSfpCablingMismatch   OBJECT-TYPE
        SYNTAX           INTEGER {
                            noMismatch(1),
                            unexpectedRemoteLt(2),
                            assignmentMismatch(3),
                            incompatibleShelf(4)
                         }
        ACCESS           read-only
        STATUS           mandatory
        DESCRIPTION     
            "This is the host shelf SFP cabling mismatch status:
            noMismatch(1):            no cabling mismatch is detected.
            unexpectedRemoteLt(2):    a remote LT is detected at an unassigned
                                      downlink SFP port.
            assignmentMismatch(3):    the detected remote LT does not match
                                      the LT assigned to this host SFP.
            incompatibleShelf(4):     the detected remote LT is in a shelf
                                      type which is not the planned shelf type.
            ALCATEL NOTE: 
                ACCESS:   RO
                USAGE:    NA
                PERSIST:  NO 
                INSRVMOD: NA
                RANGE:    NA
                DEFVALUE: 1 / noMismatch
                UNITS:    NA
                SPARSE:   NO
                DESCR:    NA
            "
::= {ltHostSfpStatusEntry 8}

ltHostSfpCabledToPhysAddress      OBJECT-TYPE
        SYNTAX                  PhysAddress
        ACCESS                  read-only
        STATUS                  mandatory
        DESCRIPTION
            "The physical (MAC) address of the remote interface that is
            cabled to this host shelf SFP.
            If the remote interface does not have such an address or
            the address is not yet discovered, this object would
            contain an octet string of zero length.
            ALCATEL NOTE: 
                ACCESS:   RO
                USAGE:    NA
                PERSIST:  NO 
                INSRVMOD: NA
                RANGE:    NA
                DEFVALUE: NA
                UNITS:    NA 
                SPARSE:   NO
                DESCR:    NA
        "
::= {ltHostSfpStatusEntry 9}

ltHostStatusSfpLogSlotId        OBJECT-TYPE
        SYNTAX                  EqptSlotIndex
        ACCESS                  read-only
        STATUS                  mandatory
        DESCRIPTION
            "The logical slot id of the board where the remote LT is plugged in
            ALCATEL NOTE: 
                ACCESS:   RO
                USAGE:    NA
                PERSIST:  NO 
                INSRVMOD: NA
                RANGE:    NA
                DEFVALUE: NA
                UNITS:    NA 
                SPARSE:   NO
                DESCR:    NA
        "
::= {ltHostSfpStatusEntry 10}



END
