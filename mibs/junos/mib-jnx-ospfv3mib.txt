-- extracted from draft-ietf-ospf-ospfv3-mib-11.txt
-- at Thu Aug 17 06:06:54 2006

    OSPFV3-MIB-JUNIPER DEFINITIONS ::= BEGIN 
 
    IMPORTS 
            MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, mib-2, 
            <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ger32, Unsigned32 
                    FROM SNMPv2-<PERSON>I 

            TEXTUAL-CONVENTION, TruthValue, RowStatus 
                    FROM SNMPv2-TC 
            MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP 
                    FROM SNMPv2-CONF 
            InterfaceIndex 
                    FROM IF-MIB 
            InetAddressType, In<PERSON><PERSON><PERSON>ress, Inet<PERSON>ddressPrefixLength 
                    FROM INET-ADDRESS-MIB 
            Metric, BigMetric, Status,  
            HelloRange, DesignatedRouterPriority 
                    FROM OSPF-MIB 

            -- Juniper specific                                     *** JNX ***
            jnxOspfv3Experiment                                  -- *** JNX ***
                    FROM JUNIPER-EXPERIMENT-MIB                  -- *** JNX ***
            ;
    
    jnxOspfv3MIB MODULE-<PERSON>ENTI<PERSON> 
            LAST-UPDATED "201103301200Z" -- 30 March 2011 12:00:00 GMT 
            ORGANIZATION "IETF OSPF Working Group" 
            CONTACT-INFO 
                "WG E-Mail: <EMAIL> 
                 WG Chairs: <EMAIL> 
                            <EMAIL> 
 
                 Dan Joyal 
                 Nortel 
                 600 Technology Park Drive 
                 Billerica, MA  01821, USA 
                 <EMAIL> 
 
                 Vishwas Manral 
                 IP Infusion 
                 Bangalore 
                 India 
                 <EMAIL>" 
 
             DESCRIPTION 
                 "The MIB module for OSPF version 3. 
 
                  Copyright (C) The Internet Society (2006). 
                  This version of this MIB module is part of 
                  RFC XXXX;  see the RFC itself for full legal 
                  notices." 
    
             REVISION "200608091200Z" 
             DESCRIPTION -- RFC Editor assigns RFC xxxx 
                 "Initial version, published as RFC xxxx" 

             REVISION "201103301200Z" -- 30 March 2011 12:00:00 GMT
             DESCRIPTION 
                 "Deprecating all objects. New mib file rfc5643.mib 
                  takes care of these objects." 
             ::= { jnxOspfv3Experiment 1 }                         -- *** JNX ***
 
    -- Texual conventions 
 
    JnxOspfv3UpToRefreshIntervalTc ::= TEXTUAL-CONVENTION 
             DISPLAY-HINT "d" 
             STATUS        deprecated 
             DESCRIPTION 
                "The values one might be able to configure for                  
                variables bounded by the Refresh Interval" 
             SYNTAX      Integer32 (1..1800) 
 
    JnxOspfv3DeadIntRangeTc ::= TEXTUAL-CONVENTION 
             DISPLAY-HINT "d" 
             STATUS        deprecated 
             DESCRIPTION 
                "The range, in seconds, of dead interval value." 
             SYNTAX      Integer32 (1..'FFFF'h) 
    
    JnxOspfv3RouterIdTc ::= TEXTUAL-CONVENTION 
             DISPLAY-HINT "d" 
             STATUS      deprecated 
             DESCRIPTION 
                "A 32-bit, unsigned integer uniquely identifying the 
                router in the Autonomous System. To ensure uniqueness, 
                this may default to the value of one of the router's 
                IPv4 host addresses if IPv4 is configured on the 
                router." 
             SYNTAX      Unsigned32 (1..4294967295) 
 
    JnxOspfv3AreaIdTc ::= TEXTUAL-CONVENTION 
             DISPLAY-HINT "d" 
             STATUS      deprecated 
             DESCRIPTION 
                "An OSPFv3 Area Identifier" 
             SYNTAX      Unsigned32 (0..'FFFFFFFF'h) 
 
    JnxOspfv3IfInstIdTc ::= TEXTUAL-CONVENTION 
             DISPLAY-HINT "d" 
             STATUS      deprecated 
             DESCRIPTION 
                "An OSPFv3 interface instance ID" 
             SYNTAX      Integer32 (0..255) 
    
 
    -- Top-level structure of MIB 
    jnxOspfv3Notifications  OBJECT IDENTIFIER ::= { jnxOspfv3MIB 0 } 
    jnxOspfv3Objects        OBJECT IDENTIFIER ::= { jnxOspfv3MIB 1 } 
    jnxOspfv3Conformance    OBJECT IDENTIFIER ::= { jnxOspfv3MIB 2 } 
 
    -- OSPFv3 General Variables 
 
    -- These parameters apply globally to the Router's 
    -- OSPFv3 Process. 
 
    jnxOspfv3GeneralGroup OBJECT IDENTIFIER ::= { jnxOspfv3Objects 1 } 
 
    jnxOspfv3RouterId OBJECT-TYPE 
            SYNTAX         JnxOspfv3RouterIdTc 
            MAX-ACCESS     read-write 
            STATUS         deprecated 
            DESCRIPTION 
                "A 32-bit integer uniquely identifying the 
                router in the Autonomous System. To ensure 
                uniqueness, this may default to the value of 
                one of the router's IPv4 host addresses, 
                represented as a 32-bit unsigned integer, 
                if IPv4 is configured on the router." 
            ::= { jnxOspfv3GeneralGroup 1 } 
 
    jnxOspfv3AdminStat OBJECT-TYPE 
            SYNTAX          Status 
            MAX-ACCESS      read-write 
            STATUS          deprecated 
            DESCRIPTION 
                "The administrative status of OSPFv3 in the 
                router. The value 'enabled' denotes that the 
                OSPFv3 Process is active on at least one 
                interface; 'disabled' disables it on all 
                interfaces." 
            ::= { jnxOspfv3GeneralGroup 2 } 
 
    jnxOspfv3VersionNumber OBJECT-TYPE 
            SYNTAX          INTEGER { version3 (3) } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The version number of OSPF for IPv6 is 3." 
            ::= { jnxOspfv3GeneralGroup 3 } 
 
    jnxOspfv3AreaBdrRtrStatus OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "A flag to note whether this router is an area 
                border router." 
            REFERENCE 
                "OSPF Version 2, Section 3 Splitting the AS into 
                Areas" 
            ::= { jnxOspfv3GeneralGroup 4 } 
 
    jnxOspfv3ASBdrRtrStatus OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-write 
            STATUS          deprecated 
            DESCRIPTION 
                "A flag to note whether this router is  
                configured as an Autonomous System border router." 
            REFERENCE 
                "OSPF Version 2, Section 3.3 Classification of 
                routers" 
            ::= { jnxOspfv3GeneralGroup 5 } 

    jnxOspfv3AsScopeLsaCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of AS-Scope (e.g. AS-External) link state 
                advertisements in the link state database." 
            ::= { jnxOspfv3GeneralGroup 6 } 
 
    jnxOspfv3AsScopeLsaCksumSum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit unsigned sum of the LS checksums of 
                the AS-scoped link state advertisements  
                contained in the link state database. This sum 
                can be used to determine if there has been a 
                change in a router's link state database, and 
                to compare the link state database of two 
                routers." 
            ::= { jnxOspfv3GeneralGroup 7 } 
 
    jnxOspfv3OriginateNewLsas OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of new link-state advertisements 
                that have been originated. This number is 
                incremented each time the router originates a new 
                LSA." 
            ::= { jnxOspfv3GeneralGroup 8 } 
 
    jnxOspfv3RxNewLsas OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of link state advertisements  
                received determined to be new instantiations. 
                This number does not include newer  
                instantiations of self-originated link state 
                advertisements." 
            ::= { jnxOspfv3GeneralGroup 9 } 
 
    jnxOspfv3ExtLsaCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of External(LS type 0x4005) in the  
                link state database" 
            ::= { jnxOspfv3GeneralGroup 10 } 
 
 
    jnxOspfv3ExtAreaLsdbLimit OBJECT-TYPE 
            SYNTAX          Integer32 (-1..'7FFFFFFF'h) 
            MAX-ACCESS      read-write 
            STATUS          deprecated 
            DESCRIPTION 
                "The maximum number of non-default  
                AS-external-LSAs entries that can be stored in the 
                link state database. If the value is -1, then 
                there is no limit. 
 
                When the number of non-default AS-external-LSAs 
                in a router's link-state database reaches 
                ospfv3ExtAreaLsdbLimit, the router enters Overflow 
                state. The router never holds more than 
                ospfv3ExtAreaLsdbLimit non-default AS-external-LSAs 
                in its database. Ospfv3ExtAreaLsdbLimit MUST be set 
                identically in all routers attached to the OSPFv3 
                backbone and/or any regular OSPFv3 area. (i.e., 
                OSPFv3 stub areas and NSSAs are excluded)." 
            ::= { jnxOspfv3GeneralGroup 11 } 
 
    jnxOspfv3MulticastExtensions OBJECT-TYPE 
            SYNTAX          BITS { 
                                     intraAreaMulticast(0), 
                                     interAreaMulticast(1), 
                                     interAsMulticast(2) 
                                 } 
                                         
            MAX-ACCESS      read-write 
            STATUS          deprecated 
            DESCRIPTION 
                "A Bit Mask indicating whether the router is 
                forwarding IPv6 multicast datagrams based on 
                the algorithms defined in the  Multicast 
                Extensions to OSPF. 
 
                If intraAreaMulticast set, indicates that the router 
                can forward IPv6 multicast datagrams in the router's 
                directly attached areas (called intra-area 
                multicast routing). 
 
                If interAreaMulticast set, indicates that the router 
                can forward IPv6 multicast datagrams between OSPFv3 
                areas (called inter-area multicast routing). 
 
                If interAsMulticast set, indicates that the router can 
                forward  IPv6  multicast datagrams between 
                Autonomous Systems (called inter-AS multicast 
                routing). 
                Only certain combinations of bit settings are 
                allowed, namely: 
                        - All bits cleared (no multicasting) 
                        - intraAreaMulticast only, 
                        - intraAreaMulticast and interAreaMulticast, 
                        - intraAreaMulticast and interAsMulticast 
                        - intraAreaMulticast, interAreaMulticast and 
                                interAsMulticast 
                By default, all bits are cleared." 
            ::= { jnxOspfv3GeneralGroup 12 } 
 
    jnxOspfv3ExitOverflowInterval OBJECT-TYPE 
            SYNTAX          Unsigned32 
            UNITS           "seconds" 
            MAX-ACCESS      read-write 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds that, after entering 
                Overflow State, a router will attempt to leave 
                Overflow State. This allows the router to again 
                originate non-default, AS-External-LSAs. When 
                set to 0, the router will not leave Overflow 
                State until restarted." 
            ::= { jnxOspfv3GeneralGroup 13 } 
 
    jnxOspfv3DemandExtensions OBJECT-TYPE 
            SYNTAX         TruthValue 
            MAX-ACCESS     read-write 
            STATUS         deprecated 
            DESCRIPTION 
                "The router's support for demand routing." 
            REFERENCE 
                "Ospf Version 2, Appendix on Demand Routing" 
            ::= { jnxOspfv3GeneralGroup 14 } 
 
    jnxOspfv3ReferenceBandwidth OBJECT-TYPE  
           SYNTAX       Unsigned32  
           MAX-ACCESS   read-write  
           STATUS       deprecated  
           DESCRIPTION  
              "Reference bandwidth in kilobits/second for  
              calculating default interface metrics. The  
              default value is 100,000 KBPS (100 MBPS)"  
        ::= { jnxOspfv3GeneralGroup 15 } 
 
    jnxOspfv3RestartSupport OBJECT-TYPE  
           SYNTAX       INTEGER { none (1),  
                                  plannedOnly (2),  
                                  plannedAndUnplanned (3)  
                             } 
           MAX-ACCESS   read-write  
           STATUS       deprecated  
           DESCRIPTION  
              "The router's support for OSPF Graceful restart.  
              Options include: no restart support, only planned  
              restarts or both planned and unplanned restarts."  
           ::= { jnxOspfv3GeneralGroup 16 }  
        
    jnxOspfv3RestartInterval OBJECT-TYPE  
           SYNTAX       JnxOspfv3UpToRefreshIntervalTc 
           UNITS        "seconds"  
           MAX-ACCESS   read-write  
           STATUS       deprecated  
           DESCRIPTION  
              "Configured OSPF Graceful restart timeout interval."  
           ::= { jnxOspfv3GeneralGroup 17 }  
        
    jnxOspfv3RestartStatus OBJECT-TYPE  
           SYNTAX       INTEGER { notRestarting (1),  
                                  plannedRestart (2),  
                                  unplannedRestart (3)  
                                }  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "The current status of OSPF Graceful restart capability."  
           ::= { jnxOspfv3GeneralGroup 18 }  
        
    jnxOspfv3RestartAge OBJECT-TYPE  
           SYNTAX       JnxOspfv3UpToRefreshIntervalTc 
           UNITS        "seconds"  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Remaining time in current OSPF Graceful restart  
              interval."  
           ::= { jnxOspfv3GeneralGroup 19 }  
        
    jnxOspfv3RestartExitRc OBJECT-TYPE  
           SYNTAX       INTEGER { none (1),  
                                  inProgress (2),  
                                  completed (3), 
                                  timedOut (4), 
                                  topologyChanged (5) 
                                }  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Describes the outcome of the last attempt at a  
              Graceful restart. 
     
              none:............no restart has yet been attempted. 
              inProgress:......a restart attempt is currently underway. 
              completed:.......the last restart completed successfully. 
              timedOut:........the last restart timed out. 
              topologyChanged:.the last restart was aborted due to 
                               a topology change."  
        ::= { jnxOspfv3GeneralGroup 20 } 
 
    jnxOspfv3NotificationEnable OBJECT-TYPE 
           SYNTAX TruthValue 
           MAX-ACCESS read-write 
           STATUS deprecated 
           DESCRIPTION 
               "If this object is set to true(1), then it enables 
                the generation of OSPFv3 Notifications. If it is 
                set to false(2), these notifications are not 
                generated. 
    
                Configured values MUST survive an agent reboot." 
           DEFVAL { true } 
       ::= { jnxOspfv3GeneralGroup 21 } 
 
 
 
    -- The OSPFv3 Area Data Structure contains information 
    -- regarding the various areas. The interfaces and 
    -- virtual links are configured as part of these areas. 
    -- Area 0, by definition, is the Backbone Area 
 
    jnxOspfv3AreaTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3AreaEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Information describing the configured 
                parameters and cumulative statistics of the router's 
                attached areas. Marking this table and its objects 
                deprecated as it is now implemented as a part of  
                RFC 5643." 
            REFERENCE 
                "OSPF Version 2, Section 6 The Area Data  
                Structure" 
            ::= { jnxOspfv3Objects 2 } 
 
    jnxOspfv3AreaEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Information describing the configured  
                parameters and cumulative statistics of one of the 
                router's attached areas." 
            INDEX           { jnxOspfv3AreaId } 
            ::= { jnxOspfv3AreaTable 1 } 
 
    JnxOspfv3AreaEntry ::= SEQUENCE { 
            jnxOspfv3AreaId 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3ImportAsExtern 
                    INTEGER, 

            jnxOspfv3AreaSpfRuns 
                    Counter32, 
            jnxOspfv3AreaBdrRtrCount 
                    Gauge32, 
            jnxOspfv3AreaAsBdrRtrCount 
                    Gauge32, 
            jnxOspfv3AreaScopeLsaCount 
                    Gauge32, 
            jnxOspfv3AreaScopeLsaCksumSum 
                    Integer32, 
            jnxOspfv3AreaSummary 
                    INTEGER, 
            jnxOspfv3AreaStatus 
                    RowStatus, 
            jnxOspfv3StubMetric 
                    BigMetric, 
            jnxOspfv3AreaNssaTranslatorRole 
                    INTEGER, 
            jnxOspfv3AreaNssaTranslatorState 
                    INTEGER, 
            jnxOspfv3AreaNssaTranslatorStabInt 
                    Unsigned32, 
            jnxOspfv3AreaNssaTranslatorEvents 
                    Counter32, 
            jnxOspfv3AreaStubMetricType 
                    INTEGER 
            } 
 
    jnxOspfv3AreaId OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A 32-bit integer uniquely identifying an area. 
                Area ID 0 is used for the OSPFv3 backbone." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3AreaEntry 1 } 
 
    jnxOspfv3ImportAsExtern OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            importExternal(1),   -- normal area 
                            importNoExternal(2), -- stub area 
                            importNssa(3)        -- not-so-stubby-area 
                            } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether an area is a Stub area, NSSA, or 
                standard area. AS-scope LSAs are not imported into Stub 
                Areas or NSSAs. NSSAs import AS-External data as NSSA 
                LSAs which have Area-scope" 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            DEFVAL { importExternal } 
            ::= { jnxOspfv3AreaEntry 2 } 
 
    jnxOspfv3AreaSpfRuns OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of times that the intra-area route 
                table has been calculated using this area's 
                link state database. This is typically done 
                using Dijkstra's algorithm." 
            ::= { jnxOspfv3AreaEntry 3 } 
 
    jnxOspfv3AreaBdrRtrCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The total number of area border routers 
                reachable within this area. This is initially zero, 
                and is calculated in each SPF Pass." 
            ::= { jnxOspfv3AreaEntry 4 } 
 
    jnxOspfv3AreaAsBdrRtrCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The total number of Autonomous System border 
                routers reachable within this area. This is 
                initially zero, and is calculated in each SPF 
                Pass." 
            ::= { jnxOspfv3AreaEntry 5 } 
 
    jnxOspfv3AreaScopeLsaCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The total number of Area-Scope link state  
                advertisements in this area's link state  
                database." 
            ::= { jnxOspfv3AreaEntry 6 } 
 
    jnxOspfv3AreaScopeLsaCksumSum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit unsigned sum of the Area-Scope link state 
                advertisements' LS checksums contained in this 
                area's link state database. The sum can be used 
                to determine if there has been a change in a 
                router's link state database, and to compare the 
                link-state database of two routers." 
            ::= { jnxOspfv3AreaEntry 7 } 
 
    jnxOspfv3AreaSummary OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            noAreaSummary(1), 
                            sendAreaSummary(2) 
                            } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The variable ospfv3AreaSummary controls the 
                import of Inter-Area LSAs into stub and 
                NSSA areas. It has no effect on other areas. 
 
                If it is noAreaSummary, the router will neither 
                originate nor propagate Inter-Area LSAs into the 
                stub or NSSA area. It will rely entirely on its 
                default route. 
 
                If it is sendAreaSummary, the router will both 
                summarize and propagate Inter-Area LSAs." 
            DEFVAL   { sendAreaSummary } 
            ::= { jnxOspfv3AreaEntry 8 } 
 
    jnxOspfv3AreaStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3AreaEntry 9 } 
 
    jnxOspfv3StubMetric OBJECT-TYPE 
            SYNTAX          BigMetric 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The metric value advertised for the default route 
                 into Stub and NSSA areas." 
            ::= { jnxOspfv3AreaEntry 10 } 
 
    jnxOspfv3AreaNssaTranslatorRole OBJECT-TYPE 
            SYNTAX          INTEGER { always(1), candidate(2) } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates an NSSA Border router's ability to 
                perform NSSA translation of NSSA-LSAs into 
                AS-External-LSAs." 
            DEFVAL { candidate } 
            ::= { jnxOspfv3AreaEntry 11 } 
 
    jnxOspfv3AreaNssaTranslatorState OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            enabled(1), 
                            elected(2), 
                            disabled(3) 
                            } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates if and how an NSSA Border router is 
                 performing NSSA translation of NSSA-LSAs into  
                AS-External-LSA's. When this object is set to enabled, 
                the NSSA Border router's ospfv3AreaNssTranslatorRole 
                has been set to always. When this object is set to 
                elected, a candidate NSSA Border router is translating 
                NSSA-LSA's into AS-External-LSA's. When this object is 
                set to disabled, a candidate NSSA Border router is NOT 
                translating NSSA-LSA's into AS-External-LSA's." 
            ::= { jnxOspfv3AreaEntry 12 } 
 
    jnxOspfv3AreaNssaTranslatorStabInt OBJECT-TYPE 
            SYNTAX          Unsigned32 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds after an elected translator 
                determines its services are no longer required, that 
                it should continue to perform its translation duties." 
            DEFVAL { 40 } 
            ::= { jnxOspfv3AreaEntry 13 } 
 
    jnxOspfv3AreaNssaTranslatorEvents OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates the number of Translator State changes 
                that have occurred since the last boot-up." 
            ::= { jnxOspfv3AreaEntry 14 } 
    
    jnxOspfv3AreaStubMetricType OBJECT-TYPE 
            SYNTAX       INTEGER { 
                            ospfv3Metric (1),   -- OSPF Metric 
                            comparableCost (2), -- external type 1 
                            nonComparable (3)   -- external type 2 
                            } 
            MAX-ACCESS   read-create 
            STATUS       deprecated 
            DESCRIPTION 
               "This variable displays the type of metric 
               advertised as a default route." 
            DEFVAL { ospfv3Metric } 
            ::= { jnxOspfv3AreaEntry 15 } 
    
    -- OSPFv3 AS-Scope Link State Database 
 
    -- The Link State Database contains the AS-Scope Link State 
    -- Advertisements from throughout the areas that the 
    -- device is attached to. 
 
    jnxOspfv3AsLsdbTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3AsLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Process's AS-Scope Link State Database. 
                Marking this table and its objects deprecated as it is now 
                implemented as a part of RFC 5643."
            ::= { jnxOspfv3Objects 3 } 
 
    jnxOspfv3AsLsdbEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3AsLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A single AS-Scope Link State Advertisement." 
            INDEX           { jnxOspfv3AsLsdbType, 
                              jnxOspfv3AsLsdbRouterId, 
                              jnxOspfv3AsLsdbLsid } 
            ::= { jnxOspfv3AsLsdbTable 1 } 
 
    JnxOspfv3AsLsdbEntry ::= SEQUENCE { 
            jnxOspfv3AsLsdbType 
                    Unsigned32, 
            jnxOspfv3AsLsdbRouterId 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3AsLsdbLsid 
                    Unsigned32, 
            jnxOspfv3AsLsdbSequence 
                    Integer32, 
            jnxOspfv3AsLsdbAge 
                    Integer32, 
            jnxOspfv3AsLsdbChecksum 
                    Integer32, 
            jnxOspfv3AsLsdbAdvertisement 
                    OCTET STRING, 
            jnxOspfv3AsLsdbTypeKnown 
                    TruthValue 

            } 
 
    jnxOspfv3AsLsdbType OBJECT-TYPE 
            SYNTAX          Unsigned32(0..'FFFFFFFF'h) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The type of the link state advertisement. 
                Each link state type has a separate  
                advertisement format. AS-Scope LSAs not recognized 
                by the router may be stored in the database." 
            ::= { jnxOspfv3AsLsdbEntry 1 } 
 
    jnxOspfv3AsLsdbRouterId OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32 bit number that uniquely identifies the 
                originating router in the Autonomous System." 
            REFERENCE 
                "OSPF Version 2, Appendix C.1 Global parameters" 
            ::= { jnxOspfv3AsLsdbEntry 2 } 
 
    jnxOspfv3AsLsdbLsid OBJECT-TYPE 
            SYNTAX          Unsigned32 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Link State ID is an LS Type Specific field 
                containing a unique identifier; 
                it identifies the piece of the routing domain 
                that is being described by the advertisement. 
                In contrast to OSPFv2, the LSID has no 
                addressing semantics." 
            ::= { jnxOspfv3AsLsdbEntry 3 } 
 
    -- Note that the OSPF Sequence Number is a 32 bit signed 
    -- integer. It starts with the value '********'h, 
    -- or -'7FFFFFFF'h, and increments until '7FFFFFFF'h 
    -- Thus, a typical sequence number will be very negative. 
 
    jnxOspfv3AsLsdbSequence OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The sequence number field is a signed 32-bit 
                integer. It is used to detect old and duplicate 
                link state advertisements. The space of 
                sequence numbers is linearly ordered. The 
                larger the sequence number the more recent the 
                advertisement." 

            REFERENCE 
                "OSPF Version  2,  Section  12.1.6  LS  sequence 
                number" 
            ::= { jnxOspfv3AsLsdbEntry 4 } 
 
    jnxOspfv3AsLsdbAge OBJECT-TYPE 
            SYNTAX          Integer32 -- Should be 0..MaxAge 
                                      -- unless DoNotAge bit is set 
            UNITS           "seconds" 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the age of the link state  
                advertisement in seconds." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.1 LS age" 
            ::= { jnxOspfv3AsLsdbEntry 5 } 
 
    jnxOspfv3AsLsdbChecksum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the checksum of the complete 
                contents of the advertisement, excepting the 
                age field. The age field is excepted so that 
                an advertisement's age can be incremented 
                without updating the checksum. The checksum 
                used is the same that is used for ISO 
                connectionless datagrams; it is commonly 
                referred to as the Fletcher checksum." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.7 LS checksum" 
            ::= { jnxOspfv3AsLsdbEntry 6 } 
 
    jnxOspfv3AsLsdbAdvertisement OBJECT-TYPE 
            SYNTAX          OCTET STRING (SIZE (1..65535)) 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The entire Link State Advertisement, including 
                its header." 
            ::= { jnxOspfv3AsLsdbEntry 7 } 
 
    jnxOspfv3AsLsdbTypeKnown OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether the LSA type is recognized by 
                this Router." 
            ::= { jnxOspfv3AsLsdbEntry 8 } 

    -- OSPFv3 Area-Scope Link State Database 
 
    -- The Link State Database contains the Area-Scope Link State 
    -- Advertisements from throughout the area that the 
    -- device is attached to. 
 
    jnxOspfv3AreaLsdbTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3AreaLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Process's Area-Scope Link State Database. 
                Marking this table and its objects deprecated as it is 
                now implemented as a part of RFC 5643."  
            ::= { jnxOspfv3Objects 4 } 
 
    jnxOspfv3AreaLsdbEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A single Area-Scope Link State Advertisement." 
            INDEX           { jnxOspfv3AreaLsdbAreaId, 
                              jnxOspfv3AreaLsdbType, 
                              jnxOspfv3AreaLsdbRouterId, 
                              jnxOspfv3AreaLsdbLsid } 
            ::= { jnxOspfv3AreaLsdbTable 1 } 
 
    JnxOspfv3AreaLsdbEntry ::= SEQUENCE { 
            jnxOspfv3AreaLsdbAreaId 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3AreaLsdbType 
                    Unsigned32, 
            jnxOspfv3AreaLsdbRouterId 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3AreaLsdbLsid 
                    Unsigned32, 
            jnxOspfv3AreaLsdbSequence 
                    Integer32, 
            jnxOspfv3AreaLsdbAge 
                    Integer32, 
            jnxOspfv3AreaLsdbChecksum 
                    Integer32, 
            jnxOspfv3AreaLsdbAdvertisement 
                    OCTET STRING, 
            jnxOspfv3AreaLsdbTypeKnown 
                    TruthValue 
            } 
 
    jnxOspfv3AreaLsdbAreaId OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit identifier of the Area from which the 
                LSA was received." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3AreaLsdbEntry 1 } 
 
    jnxOspfv3AreaLsdbType OBJECT-TYPE 
            SYNTAX          Unsigned32(0..'FFFFFFFF'h) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The type of the link state advertisement. 
                Each link state type has a separate 
                advertisement format. Area-Scope LSAs unrecognized 
                by the router are also stored in this database." 
            ::= { jnxOspfv3AreaLsdbEntry 2 } 
 
    jnxOspfv3AreaLsdbRouterId OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit number that uniquely identifies the 
                originating router in the Autonomous System." 
            REFERENCE 
                "OSPF Version 2, Appendix C.1 Global parameters" 
            ::= { jnxOspfv3AreaLsdbEntry 3 } 
 
    jnxOspfv3AreaLsdbLsid OBJECT-TYPE 
            SYNTAX          Unsigned32 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Link State ID is an LS Type Specific field 
                containing a unique identifier; 
                it identifies the piece of the routing domain 
                that is being described by the advertisement. 
                In contrast to OSPFv2, the LSID has no 
                addressing semantics." 
            ::= { jnxOspfv3AreaLsdbEntry 4 } 
 
    -- Note that the OSPF Sequence Number is a 32 bit signed 
    -- integer.  It starts with the value '********'h, 
    -- or -'7FFFFFFF'h, and increments until '7FFFFFFF'h 
    -- Thus, a typical sequence number will be very negative. 
 
    jnxOspfv3AreaLsdbSequence OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The sequence number field is a signed 32-bit 
                integer. It is used to detect old and  
                duplicate link state advertisements. The space 
                of sequence numbers is linearly ordered. The 
                larger the sequence number the more recent the 
                advertisement." 
            REFERENCE 
                "OSPF Version  2,  Section  12.1.6  LS  sequence 
                number" 
            ::= { jnxOspfv3AreaLsdbEntry 5 } 
 
    jnxOspfv3AreaLsdbAge OBJECT-TYPE 
            SYNTAX          Integer32 -- Should be 0..MaxAge 
                                      -- unless DoNotAge bit is set 
            UNITS           "seconds" 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the age of the link state 
                advertisement in seconds." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.1 LS age" 
            ::= { jnxOspfv3AreaLsdbEntry 6 } 
 
    jnxOspfv3AreaLsdbChecksum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the checksum of the complete 
                contents of the advertisement, excepting the 
                age field. The age field is excepted so that 
                an advertisement's age can be incremented 
                without updating the checksum. The checksum 
                used is the same that is used for ISO  
                connectionless datagrams; it is commonly 
                referred to as the Fletcher checksum." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.7 LS checksum" 
            ::= { jnxOspfv3AreaLsdbEntry 7 } 
 
    jnxOspfv3AreaLsdbAdvertisement OBJECT-TYPE 
            SYNTAX          OCTET STRING (SIZE (1..65535)) 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The entire Link State Advertisement, including 
                its header." 
            ::= { jnxOspfv3AreaLsdbEntry 8 } 
 
    jnxOspfv3AreaLsdbTypeKnown OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether the LSA type is recognized 
                by this Router." 
            ::= { jnxOspfv3AreaLsdbEntry 9 } 
 
    -- Ospfv3 Link-Scope Link State Database 
 
    -- The Link State Database contains the Link-Scope Link State 
    -- Advertisements from the links that the 
    -- device is attached to. 
 
    jnxOspfv3LinkLsdbTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3LinkLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Process's Link-Scope Link State Database. 
                Marking this table and its objects deprecated as it is 
                now implemented as a part of RFC 5643." 
            ::= { jnxOspfv3Objects 5 } 
 
    jnxOspfv3LinkLsdbEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3LinkLsdbEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A single Link-Scope Link State Advertisement." 
            INDEX           { jnxOspfv3LinkLsdbIfIndex, 
                              jnxOspfv3LinkLsdbIfInstId, 
                              jnxOspfv3LinkLsdbType, 
                              jnxOspfv3LinkLsdbRouterId, 
                              jnxOspfv3LinkLsdbLsid } 
            ::= { jnxOspfv3LinkLsdbTable 1 } 
 
    JnxOspfv3LinkLsdbEntry ::= SEQUENCE { 
            jnxOspfv3LinkLsdbIfIndex 
                    InterfaceIndex, 
            jnxOspfv3LinkLsdbIfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3LinkLsdbType 
                    Unsigned32, 
            jnxOspfv3LinkLsdbRouterId 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3LinkLsdbLsid 
                    Unsigned32, 
            jnxOspfv3LinkLsdbSequence 
                    Integer32, 
            jnxOspfv3LinkLsdbAge 
                    Integer32, 
            jnxOspfv3LinkLsdbChecksum 
                    Integer32, 
            jnxOspfv3LinkLsdbAdvertisement 
                    OCTET STRING, 
            jnxOspfv3LinkLsdbTypeKnown 
                    TruthValue 
            } 

    jnxOspfv3LinkLsdbIfIndex OBJECT-TYPE 
            SYNTAX         InterfaceIndex 
            MAX-ACCESS     not-accessible 
            STATUS         deprecated 
            DESCRIPTION 
                "The identifier of the link from which the LSA 
                was received." 
            ::= { jnxOspfv3LinkLsdbEntry 1 } 
 
    jnxOspfv3LinkLsdbIfInstId OBJECT-TYPE 
            SYNTAX         JnxOspfv3IfInstIdTc 
            MAX-ACCESS     not-accessible 
            STATUS         deprecated 
            DESCRIPTION 
                "The identifier of the interface instance from 
                which the LSA was received." 
            ::= { jnxOspfv3LinkLsdbEntry 2 } 
 
    jnxOspfv3LinkLsdbType OBJECT-TYPE 
            SYNTAX          Unsigned32(0..'FFFFFFFF'h) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The type of the link state advertisement. 
                Each link state type has a separate 
                advertisement format. Link-Scope LSAs unrecognized 
                by the router are also stored in this database." 
            ::= { jnxOspfv3LinkLsdbEntry 3 } 
 
    jnxOspfv3LinkLsdbRouterId OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32 bit number that uniquely identifies the 
                originating router in the Autonomous System." 
            REFERENCE 
                "OSPF Version 2, Appendix C.1 Global parameters" 
            ::= { jnxOspfv3LinkLsdbEntry 4 } 
 
    jnxOspfv3LinkLsdbLsid OBJECT-TYPE 
            SYNTAX        Unsigned32 
            MAX-ACCESS    not-accessible 
            STATUS        deprecated 
            DESCRIPTION 
                "The Link State ID is an LS Type Specific field 
                containing a unique identifier; 
                it identifies the piece of the routing domain 
                that is being described by the advertisement. 
                In contrast to OSPFv2, the LSID has no 
                addressing semantics." 
            ::= { jnxOspfv3LinkLsdbEntry 5 } 

    -- Note that the OSPF Sequence Number is a 32 bit signed 
    -- integer.  It starts with the value '********'h, 
    -- or -'7FFFFFFF'h, and increments until '7FFFFFFF'h 
    -- Thus, a typical sequence number will be very negative. 
 
    jnxOspfv3LinkLsdbSequence OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The sequence number field is a signed 32-bit 
                integer. It is used to detect old and duplicate 
                link state advertisements. The space of 
                sequence numbers is linearly ordered. The 
                larger the sequence number the more recent the 
                advertisement." 
            REFERENCE 
                "OSPF Version  2,  Section  12.1.6  LS  sequence 
                number" 
            ::= { jnxOspfv3LinkLsdbEntry 6 } 
 
    jnxOspfv3LinkLsdbAge OBJECT-TYPE 
            SYNTAX          Integer32 -- Should be 0..MaxAge 
                                      -- unless DoNotAge bit is set 
            UNITS           "seconds" 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the age of the link state 
                advertisement in seconds." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.1 LS age" 
            ::= { jnxOspfv3LinkLsdbEntry 7 } 
 
    jnxOspfv3LinkLsdbChecksum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "This field is the checksum of the complete 
                contents of the advertisement, excepting the 
                age field. The age field is excepted so that 
                an advertisement's age can be incremented 
                without updating the checksum. The checksum 
                used is the same that is used for ISO 
                connectionless datagrams; it is commonly 
                referred to as the Fletcher checksum." 
            REFERENCE 
                "OSPF Version 2, Section 12.1.7 LS checksum" 
            ::= { jnxOspfv3LinkLsdbEntry 8 } 
 
    jnxOspfv3LinkLsdbAdvertisement OBJECT-TYPE 
            SYNTAX          OCTET STRING (SIZE (1..65535)) 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The entire Link State Advertisement, including 
                its header." 
            ::= { jnxOspfv3LinkLsdbEntry 9 } 
 
    jnxOspfv3LinkLsdbTypeKnown OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether the LSA type is recognized by this 
                 Router." 
            ::= { jnxOspfv3LinkLsdbEntry 10 } 
 
 
    -- OSPF Host Table 
 
    -- The Host/Metric Table indicates what hosts are directly 
    -- attached to the Router, and what metrics and types of 
    -- service should be advertised for them. 
 
    jnxOspfv3HostTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3HostEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The list of Hosts, and their metrics, that the 
                router will advertise as host routes. Marking this 
                table and its objects deprecated as it is now implemented 
                as a part of RFC 5643."  
            REFERENCE 
                "OSPF Version 2, Appendix C.6  Host route 
                parameters" 
            ::= { jnxOspfv3Objects 6 } 
 
    jnxOspfv3HostEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3HostEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A metric to be advertised when a given host is 
                reachable." 
            INDEX           { jnxOspfv3HostAddressType, 
                              jnxOspfv3HostAddress } 
            ::= { jnxOspfv3HostTable 1 } 
 
    JnxOspfv3HostEntry ::= SEQUENCE { 
            jnxOspfv3HostAddressType 
                    InetAddressType, 
            jnxOspfv3HostAddress 
                    InetAddress, 
            jnxOspfv3HostMetric 
                    Metric, 
            jnxOspfv3HostStatus 
                    RowStatus, 
            jnxOspfv3HostAreaID 
                    JnxOspfv3AreaIdTc 
            } 
 
    jnxOspfv3HostAddressType OBJECT-TYPE 
            SYNTAX          InetAddressType 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The address type of ospfv3HostAddress. Only IPv6 
                addresses without zone index are expected." 
            REFERENCE 
                "OSPF Version 2, Appendix C.6 Host route  
                parameters" 
            ::= { jnxOspfv3HostEntry 1 } 
 
 
    jnxOspfv3HostAddress OBJECT-TYPE 
            SYNTAX          InetAddress (SIZE (16)) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The IPv6 Address of the Host. Must be a Global 
                 address." 
            REFERENCE 
                "OSPF Version 2, Appendix C.6 Host route 
                parameters" 
            ::= { jnxOspfv3HostEntry 2 } 
 
    jnxOspfv3HostMetric OBJECT-TYPE 
            SYNTAX          Metric 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The Metric to be advertised." 
            REFERENCE 
                "OSPF Version 2, Appendix C.6 Host route 
                parameters" 
            ::= { jnxOspfv3HostEntry 3 } 
 
    jnxOspfv3HostStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3HostEntry 4 } 
 
    jnxOspfv3HostAreaID OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The Area the Host Entry is to be found within. 
                By default, the area that a subsuming OSPFv3 
                interface is in, or Area 0" 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3HostEntry 5 } 
 
    -- OSPFv3 Interface Table 
 
    jnxOspfv3IfTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3IfEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Interface Table describes the 
                interfaces from the viewpoint of OSPFv3. Marking this table 
                and its objects deprecated as it is now implemented as 
                a part of RFC 5643." 
            REFERENCE 
                "OSPF Version 2, Appendix C.3 Router interface 
                parameters" 
            ::= { jnxOspfv3Objects 7 } 
 
    jnxOspfv3IfEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Interface Entry describes one 
                interface from the viewpoint of OSPFv3." 
            INDEX           { jnxOspfv3IfIndex, 
                              jnxOspfv3IfInstId } 
            ::= { jnxOspfv3IfTable 1 } 
 
 
    JnxOspfv3IfEntry ::= SEQUENCE { 
            jnxOspfv3IfIndex 
                    InterfaceIndex, 
            jnxOspfv3IfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3IfAreaId 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3IfType 
                    INTEGER, 
            jnxOspfv3IfAdminStat 
                    Status, 

            jnxOspfv3IfRtrPriority 
                    DesignatedRouterPriority, 
            jnxOspfv3IfTransitDelay 
                    JnxOspfv3UpToRefreshIntervalTc, 
            jnxOspfv3IfRetransInterval 
                    JnxOspfv3UpToRefreshIntervalTc, 
            jnxOspfv3IfHelloInterval 
                    HelloRange, 
            jnxOspfv3IfRtrDeadInterval 
                     JnxOspfv3DeadIntRangeTc, 
            jnxOspfv3IfPollInterval 
                    Unsigned32, 
            jnxOspfv3IfState 
                    INTEGER, 
            jnxOspfv3IfDesignatedRouter 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3IfBackupDesignatedRouter 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3IfEvents 
                    Counter32, 
            jnxOspfv3IfStatus 
                    RowStatus, 
            jnxOspfv3IfMulticastForwarding 
                    INTEGER, 
            jnxOspfv3IfDemand 
                    TruthValue, 
            jnxOspfv3IfMetricValue 
                    Metric, 
            jnxOspfv3IfLinkScopeLsaCount 
                    Gauge32, 
            jnxOspfv3IfLinkLsaCksumSum 
                    Integer32, 
            jnxOspfv3IfDemandNbrProbe 
                    TruthValue, 
            jnxOspfv3IfDemandNbrProbeRetxLimit  
                    Unsigned32, 
            jnxOspfv3IfDemandNbrProbeInterval  
                    Unsigned32 
            } 
 
    jnxOspfv3IfIndex OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The interface index of this OSPFv3 interface. 
                 It corresponds to the interface index of the 
                 IPv6 interface on which OSPFv3 is configured." 
            ::= { jnxOspfv3IfEntry 1 } 
 
    jnxOspfv3IfInstId OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfInstIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Enables multiple interface instances of OSPFv3 
                to be run over a single link. Each protocol 
                instance would be assigned a separate ID. This ID 
                has local link significance only." 
            ::= { jnxOspfv3IfEntry 2 } 
 
    jnxOspfv3IfAreaId OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "A 32-bit integer uniquely identifying the area 
                to which the interface connects. Area ID 
                0 is used for the OSPFv3 backbone." 
            DEFVAL          { 0 } 
            ::= { jnxOspfv3IfEntry 3 } 
 
    jnxOspfv3IfType OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            broadcast(1), 
                            nbma(2), 
                            pointToPoint(3), 
                            pointToMultipoint(5) 
                            } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 interface type." 
            ::= { jnxOspfv3IfEntry 4 } 
 
    jnxOspfv3IfAdminStat OBJECT-TYPE 
            SYNTAX          Status 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 interface's administrative status. 
                The value formed on the interface, and the 
                interface will be advertised as an internal route 
                to some area. The value 'disabled' denotes 
                that the interface is external to OSPFv3." 
            DEFVAL          { enabled } 
            ::= { jnxOspfv3IfEntry 5 } 
 
    jnxOspfv3IfRtrPriority OBJECT-TYPE 
            SYNTAX          DesignatedRouterPriority 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The priority of this interface. Used in 
                multi-access networks, this field is used in 
                the designated router election algorithm. The 
                value 0 signifies that the router is not  
                eligible to become the designated router on this 
                particular network. In the event of a tie in 
                this value, routers will use their Router ID as 
                a tie breaker." 
            DEFVAL          { 1 } 
            ::= { jnxOspfv3IfEntry 6 } 
 
    jnxOspfv3IfTransitDelay OBJECT-TYPE 
            SYNTAX          JnxOspfv3UpToRefreshIntervalTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The estimated number of seconds it takes to 
                transmit a link state update packet over this 
                interface." 
            DEFVAL          { 1 } 
            ::= { jnxOspfv3IfEntry 7 } 
 
    jnxOspfv3IfRetransInterval OBJECT-TYPE 
            SYNTAX          JnxOspfv3UpToRefreshIntervalTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds between link state 
                advertisement retransmissions, for adjacencies 
                belonging to this interface. This value is 
                also used when retransmitting database  
                description and link state request packets." 
            DEFVAL          { 5 } 
            ::= { jnxOspfv3IfEntry 8 } 
 
    jnxOspfv3IfHelloInterval OBJECT-TYPE 
            SYNTAX          HelloRange 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The length of time, in seconds, between the 
                Hello packets that the router sends on the 
                interface. This value must be the same for all 
                routers attached to a common network." 
            DEFVAL          { 10 } 
            ::= { jnxOspfv3IfEntry 9 } 
 
    jnxOspfv3IfRtrDeadInterval OBJECT-TYPE 
            SYNTAX          JnxOspfv3DeadIntRangeTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds that a router's Hello 
                packets have not been seen before its  
                neighbors declare the router down on the interface. 
                This should be some multiple of the Hello interval. 
                This value must be the same for all routers attached 
                to a common network." 
            DEFVAL          { 40 } 
            ::= { jnxOspfv3IfEntry 10 } 
 
    jnxOspfv3IfPollInterval OBJECT-TYPE 
            SYNTAX          Unsigned32 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The larger time interval, in seconds, between 
                the Hello packets sent to an inactive  
                non-broadcast multi-access neighbor." 
            DEFVAL          { 120 } 
            ::= { jnxOspfv3IfEntry 11 } 
 
    jnxOspfv3IfState OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            down(1), 
                            loopback(2), 
                            waiting(3), 
                            pointToPoint(4), 
                            designatedRouter(5), 
                            backupDesignatedRouter(6), 
                            otherDesignatedRouter(7) 
                            } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The OSPFv3 Interface State." 
            ::= { jnxOspfv3IfEntry 12 } 
 
    jnxOspfv3IfDesignatedRouter OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The Router ID of the Designated Router." 
            ::= { jnxOspfv3IfEntry 13 } 
 
    jnxOspfv3IfBackupDesignatedRouter OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The Router ID of the Backup Designated 
                Router." 
            ::= { jnxOspfv3IfEntry 14 } 

    jnxOspfv3IfEvents OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of times this OSPF interface has 
                changed its state, or an error has occurred." 
            ::= { jnxOspfv3IfEntry 15 } 
 
     jnxOspfv3IfStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3IfEntry 16 } 
 
    jnxOspfv3IfMulticastForwarding OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            blocked(1),   -- no multicast forwarding 
                            multicast(2), -- using multicast address 
                            unicast(3)    -- to each OSPFv3 neighbor 
                            } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The way multicasts should forwarded on this 
                interface; not forwarded, forwarded as data 
                link multicasts, or forwarded as data link  
                unicasts. Data link multicasting is not  
                meaningful on point to point and NBMA interfaces, 
                and setting ospfv3MulticastForwarding to 0 
                effectively disables all multicast forwarding." 
            DEFVAL { blocked } 
            ::= { jnxOspfv3IfEntry 17 } 
 
    jnxOspfv3IfDemand OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether Demand OSPFv3 procedures 
                (hello suppression to FULL neighbors and 
                setting the DoNotAge flag on propagated LSAs) 
                should be performed on this interface." 
            DEFVAL { false } 
            ::= { jnxOspfv3IfEntry 18 } 
 
    jnxOspfv3IfMetricValue OBJECT-TYPE 
            SYNTAX          Metric 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The metric assigned to this interface. 
                 The default value of the Metric is 
                Reference Bandwidth / ifSpeed. The value 
                of the reference bandwidth is configured 
                by the ospfv3ReferenceBandwidth object." 
            ::= { jnxOspfv3IfEntry 19 } 
 
     jnxOspfv3IfLinkScopeLsaCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The total number of Link-Scope link state 
                advertisements in this link's link state 
                database." 
            ::= { jnxOspfv3IfEntry 20 } 
 
     jnxOspfv3IfLinkLsaCksumSum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit unsigned sum of the Link-Scope link state 
                advertisements' LS checksums contained in this 
                link's link state database. The sum can be used 
                to determine if there has been a change in a 
                router's link state database, and to compare the 
                link state database of two routers." 
            ::= { jnxOspfv3IfEntry 21 } 
 
    jnxOspfv3IfDemandNbrProbe OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                   "Indicates whether or not neighbor probing is 
                   enabled to determine whether or not the neighbor  
                   is inactive. Neighbor probing is disabled by  
                   default." 
            DEFVAL { false } 
            ::= { jnxOspfv3IfEntry 22 } 
 
   jnxOspfv3IfDemandNbrProbeRetxLimit OBJECT-TYPE  
           SYNTAX       Unsigned32  
           UNITS        "seconds"  
           MAX-ACCESS   read-create 
           STATUS       deprecated  
           DESCRIPTION  
              "The number of consecutive LSA retransmissions before 
              the neighbor is deemed inactive and the neighbor  
              adjacency is brought down."  
           DEFVAL          { 10 } 
           ::= { jnxOspfv3IfEntry 23}  
 
 
   jnxOspfv3IfDemandNbrProbeInterval OBJECT-TYPE  
           SYNTAX       Unsigned32  
           UNITS        "seconds"  
           MAX-ACCESS   read-create 
           STATUS       deprecated  
           DESCRIPTION  
              "Defines how often the neighbor will be probed."  
           DEFVAL          { 120 } 
           ::= { jnxOspfv3IfEntry 24 }  
 
 
    -- OSPFv3 Virtual Interface Table 
 
    -- The Virtual Interface Table describes the virtual 
    -- links that the OSPFv3 Process is configured to 
    -- carry on. 
 
    jnxOspfv3VirtIfTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3VirtIfEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Information about this router's virtual 
                interfaces. Marking this table and its objects 
                deprecated as it is now implemented as a part of RFC 5643."  
            REFERENCE 
                "OSPF Version 2, Appendix C.4 Virtual link 
                parameters" 
            ::= { jnxOspfv3Objects 8 } 
 
    jnxOspfv3VirtIfEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3VirtIfEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Information about a single Virtual Interface." 
            INDEX           { jnxOspfv3VirtIfAreaId, 
                              jnxOspfv3VirtIfNeighbor } 
            ::= { jnxOspfv3VirtIfTable 1 } 
 
    JnxOspfv3VirtIfEntry ::= SEQUENCE { 
            jnxOspfv3VirtIfAreaId 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3VirtIfNeighbor 
                    JnxOspfv3RouterIdTc, 

            jnxOspfv3VirtIfIndex 
                    InterfaceIndex, 
            jnxOspfv3VirtIfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3VirtIfTransitDelay 
                    JnxOspfv3UpToRefreshIntervalTc, 
            jnxOspfv3VirtIfRetransInterval 
                    JnxOspfv3UpToRefreshIntervalTc, 
            jnxOspfv3VirtIfHelloInterval 
                    HelloRange, 
            jnxOspfv3VirtIfRtrDeadInterval 
                    JnxOspfv3DeadIntRangeTc, 
            jnxOspfv3VirtIfState 
                    INTEGER, 
            jnxOspfv3VirtIfEvents 
                    Counter32, 
            jnxOspfv3VirtIfStatus 
                    RowStatus, 
            jnxOspfv3VirtIfLinkScopeLsaCount 
                    Gauge32, 
            jnxOspfv3VirtIfLinkLsaCksumSum 
                    Integer32 
            } 
 
    jnxOspfv3VirtIfAreaId OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Transit Area that the Virtual Link 
                traverses. By definition, this is not 
                Area 0" 
            ::= { jnxOspfv3VirtIfEntry 1 } 
 
    jnxOspfv3VirtIfNeighbor OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Router ID of the Virtual Neighbor." 
            ::= { jnxOspfv3VirtIfEntry 2 } 
 
    jnxOspfv3VirtIfIndex OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The local interface index assigned to this 
                OSPFv3 virtual interface. It is advertised in 
                Hello's sent over the virtal link and in the 
                router's router-LSAs." 
            ::= { jnxOspfv3VirtIfEntry 3 } 

    jnxOspfv3VirtIfInstId OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfInstIdTc 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "Specifies the interface instance ID to be used 
                for the virtual interface. This ID has local link 
                significance only." 
            DEFVAL          { 0 } 
            ::= { jnxOspfv3VirtIfEntry 4 } 
 
    jnxOspfv3VirtIfTransitDelay OBJECT-TYPE 
            SYNTAX          JnxOspfv3UpToRefreshIntervalTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The estimated number of seconds it takes to 
                transmit a link state update packet over this 
                interface." 
            DEFVAL          { 1 } 
            ::= { jnxOspfv3VirtIfEntry 5 } 
 
    jnxOspfv3VirtIfRetransInterval OBJECT-TYPE 
            SYNTAX          JnxOspfv3UpToRefreshIntervalTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds between link state 
                advertisement retransmissions, for adjacencies 
                belonging to this interface. This value is 
                also used when retransmitting database 
                description and link state request packets. This 
                value should be well over the expected 
                round-trip time." 
            DEFVAL          { 5 } 
            ::= { jnxOspfv3VirtIfEntry 6 } 
 
    jnxOspfv3VirtIfHelloInterval OBJECT-TYPE 
            SYNTAX          HelloRange 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The length of time, in seconds, between the 
                Hello packets that the router sends on the 
                interface.  This value must be the same for the 
                virtual neighbor." 
            DEFVAL          { 10 } 
            ::= { jnxOspfv3VirtIfEntry 7 } 
 
    jnxOspfv3VirtIfRtrDeadInterval OBJECT-TYPE 
            SYNTAX          JnxOspfv3DeadIntRangeTc 
            UNITS           "seconds" 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of seconds that a router's Hello 
                packets have not been seen before its 
                neighbors declare the router down. This should 
                be some multiple of the Hello interval. This 
                value must be the same for the virtual 
                neighbor." 
            DEFVAL          { 60 } 
            ::= { jnxOspfv3VirtIfEntry 8 } 
 
    jnxOspfv3VirtIfState OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            down(1), 
                            pointToPoint(4) 
                            } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "ospf virtual interface states. The same encoding 
                as the ospfV3IfTable is used." 
            ::= { jnxOspfv3VirtIfEntry 9 } 
 
    jnxOspfv3VirtIfEvents OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of state changes or error events on 
                this Virtual Link" 
            ::= { jnxOspfv3VirtIfEntry 10 } 
 
    jnxOspfv3VirtIfStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3VirtIfEntry 11 } 
 
    jnxOspfv3VirtIfLinkScopeLsaCount OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The total number of Link-Scope link state 
                advertisements in this virtual link's link state 
                database." 
            ::= { jnxOspfv3VirtIfEntry 12 } 
 
    jnxOspfv3VirtIfLinkLsaCksumSum OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The 32-bit unsigned sum of the Link-Scope link-state 
                advertisements' LS checksums contained in this 
                virtual link's link-state database. The sum can be used 
                to determine if there has been a change in a 
                router's link state database, and to compare the 
                link state database of two routers." 
            ::= { jnxOspfv3VirtIfEntry 13 } 
 
 
    -- OSPFv3 Neighbor Table 
 
    -- The OSPFv3 Neighbor Table describes all neighbors in 
    -- the locality of the subject router. 
 
    jnxOspfv3NbrTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3NbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A table of non-virtual neighbor information." 
            REFERENCE 
                "OSPF Version 2, Section 10 The Neighbor Data 
                Structure. Marking this table and its objects 
                deprecated as it is now implemented as a part of 
                RFC 5643." 
            ::= { jnxOspfv3Objects 9 } 
 
    jnxOspfv3NbrEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3NbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The information regarding a single neighbor." 
            REFERENCE 
                "OSPF Version 2, Section 10 The Neighbor Data 
                Structure" 
            INDEX           { jnxOspfv3NbrIfIndex, 
                              jnxOspfv3NbrIfInstId, 
                              jnxOspfv3NbrRtrId } 
            ::= { jnxOspfv3NbrTable 1 } 
 
    JnxOspfv3NbrEntry ::= SEQUENCE { 
            jnxOspfv3NbrIfIndex 
                    InterfaceIndex, 
            jnxOspfv3NbrIfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3NbrRtrId 
                    JnxOspfv3RouterIdTc,
            jnxOspfv3NbrAddressType 
                    InetAddressType, 
            jnxOspfv3NbrAddress 
                    InetAddress,             
            jnxOspfv3NbrOptions 
                    Integer32, 
            jnxOspfv3NbrPriority 
                    DesignatedRouterPriority, 
            jnxOspfv3NbrState 
                    INTEGER, 
            jnxOspfv3NbrEvents 
                    Counter32, 
            jnxOspfv3NbrLsRetransQLen 
                    Gauge32, 
            jnxOspfv3NbrHelloSuppressed 
                    TruthValue, 
            jnxOspfv3NbrIfId 
                    InterfaceIndex, 
            jnxOspfv3NbrRestartHelperStatus  
                    INTEGER,  
            jnxOspfv3NbrRestartHelperAge  
                    JnxOspfv3UpToRefreshIntervalTc,  
            jnxOspfv3NbrRestartHelperExitRc  
                    INTEGER 
            } 
 
    jnxOspfv3NbrIfIndex OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The local link ID of the link over which the 
                 neighbor can be reached." 
            ::= { jnxOspfv3NbrEntry 1 } 
 
    jnxOspfv3NbrIfInstId OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfInstIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Interface instance over which the neighbor 
                can be reached. This ID has local link 
                significance only." 
            ::= { jnxOspfv3NbrEntry 2 } 
 
    jnxOspfv3NbrRtrId OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
               "A 32-bit integer uniquely identifying the neighboring 
               router in the Autonomous System." 
            ::= { jnxOspfv3NbrEntry 3 } 
 
    jnxOspfv3NbrAddressType OBJECT-TYPE 
            SYNTAX          InetAddressType 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The address type of ospfv3NbrAddress. Only IPv6 
                addresses without zone index are expected." 
            ::= { jnxOspfv3NbrEntry 4 } 
 
    jnxOspfv3NbrAddress OBJECT-TYPE 
            SYNTAX          InetAddress (SIZE (16)) 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The IPv6 address of the neighbor associated with 
                the local link." 
            ::= { jnxOspfv3NbrEntry 5 } 
 
    jnxOspfv3NbrOptions OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "A Bit Mask corresponding to the neighbor's 
                options field." 
            REFERENCE 
                "OSPF Version 3, Appendix A.2 the Options field" 
            ::= { jnxOspfv3NbrEntry 6 } 
 
    jnxOspfv3NbrPriority OBJECT-TYPE 
            SYNTAX          DesignatedRouterPriority 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The priority of this neighbor in the designated 
                router election algorithm. The value 0 signifies 
                that the neighbor is not eligible to become the 
                designated router on this particular network." 
            ::= { jnxOspfv3NbrEntry 7 } 
 
    jnxOspfv3NbrState OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            down(1), 
                            attempt(2), 
                            init(3), 
                            twoWay(4), 
                            exchangeStart(5), 
                            exchange(6), 
                            loading(7), 
                            full(8) 
                            } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The State of the relationship with this 
                Neighbor." 
            REFERENCE 
                "OSPF Version 2, Section 10.1 Neighbor States" 
            ::= { jnxOspfv3NbrEntry 8 } 
 
    jnxOspfv3NbrEvents OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of times this neighbor relationship 
                has changed state, or an error has occurred." 
            ::= { jnxOspfv3NbrEntry 9 } 
 
    jnxOspfv3NbrLsRetransQLen OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The current length of the retransmission 
                queue." 
            ::= { jnxOspfv3NbrEntry 10 } 
 
    jnxOspfv3NbrHelloSuppressed OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether Hellos are being suppressed 
                to the neighbor" 
            ::= { jnxOspfv3NbrEntry 11 } 
 
    jnxOspfv3NbrIfId OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The interface ID that the neighbor advertises 
                in its Hello Packets on this link, that is, the 
                neighbor's local interface index." 
            ::= { jnxOspfv3NbrEntry 12 } 
 
    jnxOspfv3NbrRestartHelperStatus OBJECT-TYPE  
           SYNTAX       INTEGER { notHelping (1),  
                                  helping (2)  
                                }  

           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Indicates whether the router is acting  
              as a Graceful restart helper for the neighbor."  
              ::= { jnxOspfv3NbrEntry 13 }  
        
    jnxOspfv3NbrRestartHelperAge OBJECT-TYPE  
           SYNTAX       JnxOspfv3UpToRefreshIntervalTc 
           UNITS        "seconds"  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Remaining time in current OSPF Graceful restart  
              interval, if the router is acting as a restart  
              helper for the neighbor."  
           ::= { jnxOspfv3NbrEntry 14 }  
        
    jnxOspfv3NbrRestartHelperExitRc OBJECT-TYPE  
           SYNTAX       INTEGER { none (1), 
                                  inProgress (2),  
                                  completed (3),  
                                  timedOut (4),  
                                  topologyChanged (5) 
                                }  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Describes the outcome of the last attempt at acting  
              as a Graceful restart helper for the neighbor. 
    
              none:............no restart has yet been attempted. 
              inProgress:......a restart attempt is currentlyly underway. 
              completed:.......the last restart completed successfully. 
              timedOut:........the last restart timed out. 
              topologyChanged:.the last restart was aborted due to 
                               a topology change."  
        ::= { jnxOspfv3NbrEntry 15 } 
 
 
    -- OSPFv3 Configured Neighbor Table 
 
    -- The OSPFv3 Configured Neighbor Table describes all configured 
    -- neighbors 
 
    jnxOspfv3CfgNbrTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3CfgNbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A table of configured, non-virtual neighbor 
                information. Marking this table and its objects 
                deprecated as it is now implemented as a part of RFC 5643." 
            REFERENCE 
                "OSPF Version 2, Section 10 The Neighbor Data 
                Structure" 
            ::= { jnxOspfv3Objects 10 } 
 
    jnxOspfv3CfgNbrEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3CfgNbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The information regarding a single configured 
                neighbor or neighbor discovered by lower-level 
                protocols such as Inverse Neighbor Discovery." 
            REFERENCE 
                "OSPF Version 2, Section 10 The Neighbor Data 
                Structure" 
            INDEX           { jnxOspfv3CfgNbrIfIndex, 
                              jnxOspfv3CfgNbrIfInstId, 
                              jnxOspfv3CfgNbrAddressType, 
                              jnxOspfv3CfgNbrAddress } 
            ::= { jnxOspfv3CfgNbrTable 1 } 
 
    JnxOspfv3CfgNbrEntry ::= SEQUENCE { 
            jnxOspfv3CfgNbrIfIndex 
                    InterfaceIndex, 
            jnxOspfv3CfgNbrIfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3CfgNbrAddressType 
                    InetAddressType, 
            jnxOspfv3CfgNbrAddress 
                    InetAddress, 
            jnxOspfv3CfgNbrPriority 
                    DesignatedRouterPriority, 
            jnxOspfv3CfgNbrStatus 
                    RowStatus 
            } 
 
    jnxOspfv3CfgNbrIfIndex OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The local link ID of the link over which the 
                 neighbor can be reached." 
            ::= { jnxOspfv3CfgNbrEntry 1 } 
 
    jnxOspfv3CfgNbrIfInstId OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfInstIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Interface instance over which the neighbor 
                can be reached. This ID has local link 
                significance only." 
            ::= { jnxOspfv3CfgNbrEntry 2 } 
 
    jnxOspfv3CfgNbrAddressType OBJECT-TYPE 
            SYNTAX          InetAddressType 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The address type of ospfv3NbrAddress. Only IPv6 
                addresses without zone index are expected." 
            ::= { jnxOspfv3CfgNbrEntry 3 } 
 
    jnxOspfv3CfgNbrAddress OBJECT-TYPE 
            SYNTAX          InetAddress (SIZE (16)) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The IPv6 address of the neighbor associated with 
                the local link." 
            ::= { jnxOspfv3CfgNbrEntry 4 } 
 
    jnxOspfv3CfgNbrPriority OBJECT-TYPE 
            SYNTAX          DesignatedRouterPriority 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "The priority of this neighbor in the designated 
                router election algorithm. The value 0 signifies 
                that the neighbor is not eligible to become the 
                designated router on this particular network." 
            DEFVAL          { 1 } 
            ::= { jnxOspfv3CfgNbrEntry 5 } 
 
    jnxOspfv3CfgNbrStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3CfgNbrEntry 6 } 
 
    -- jnxOspfv3 Virtual Neighbor Table 
 
    -- This table describes all virtual neighbors. 
    -- Since Virtual Links are configured in the 
    -- virtual interface table, this table is read-only. 
 
    jnxOspfv3VirtNbrTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3VirtNbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A table of virtual neighbor information. Marking this 
                table and its objects deprecated as it is now 
                implemented as a part of RFC 5643." 
            REFERENCE 
                "OSPF Version 2, Section 15 Virtual Links" 
            ::= { jnxOspfv3Objects 11 } 
 
    jnxOspfv3VirtNbrEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3VirtNbrEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "Virtual neighbor information." 
            INDEX           { jnxOspfv3VirtNbrArea, 
                              jnxOspfv3VirtNbrRtrId } 
            ::= { jnxOspfv3VirtNbrTable 1 } 
 
    JnxOspfv3VirtNbrEntry ::= SEQUENCE { 
            jnxOspfv3VirtNbrArea 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3VirtNbrRtrId 
                    JnxOspfv3RouterIdTc, 
            jnxOspfv3VirtNbrIfIndex 
                    InterfaceIndex, 
            jnxOspfv3VirtNbrIfInstId 
                    JnxOspfv3IfInstIdTc, 
            jnxOspfv3VirtNbrAddressType 
                    InetAddressType, 
            jnxOspfv3VirtNbrAddress 
                    InetAddress, 
            jnxOspfv3VirtNbrOptions 
                    Integer32, 
            jnxOspfv3VirtNbrState 
                    INTEGER, 
            jnxOspfv3VirtNbrEvents 
                    Counter32, 
            jnxOspfv3VirtNbrLsRetransQLen 
                    Gauge32, 
            jnxOspfv3VirtNbrHelloSuppressed 
                    TruthValue, 
            jnxOspfv3VirtNbrIfId 
                    InterfaceIndex, 
            jnxOspfv3VirtNbrRestartHelperStatus  
                    INTEGER,  
            jnxOspfv3VirtNbrRestartHelperAge  
                    JnxOspfv3UpToRefreshIntervalTc,  
            jnxOspfv3VirtNbrRestartHelperExitRc  
                    INTEGER 
            } 
 
    jnxOspfv3VirtNbrArea OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Transit Area Identifier." 
            ::= { jnxOspfv3VirtNbrEntry 1 } 
 
    jnxOspfv3VirtNbrRtrId OBJECT-TYPE 
            SYNTAX          JnxOspfv3RouterIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A 32-bit integer uniquely identifying the 
                neighboring router in the Autonomous System." 
            ::= { jnxOspfv3VirtNbrEntry 2 } 
 
    jnxOspfv3VirtNbrIfIndex OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The local interface ID for the virtual link over 
                which the neighbor can be reached." 
            ::= { jnxOspfv3VirtNbrEntry 3 } 
 
    jnxOspfv3VirtNbrIfInstId OBJECT-TYPE 
            SYNTAX          JnxOspfv3IfInstIdTc 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The interface instance for the virtual link over 
                which the neighbor can be reached." 
            ::= { jnxOspfv3VirtNbrEntry 4 } 
 
    jnxOspfv3VirtNbrAddressType OBJECT-TYPE 
            SYNTAX          InetAddressType 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The address type of ospfv3VirtNbrAddress. Only IPv6 
                addresses without zone index are expected." 
            ::= { jnxOspfv3VirtNbrEntry 5 } 
 
    jnxOspfv3VirtNbrAddress OBJECT-TYPE 
            SYNTAX          InetAddress (SIZE (16)) 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The IPv6 address advertised by this Virtual Neighbor. 
                It must be a Global scope address." 
            ::= { jnxOspfv3VirtNbrEntry 6 } 
 
    jnxOspfv3VirtNbrOptions OBJECT-TYPE 
            SYNTAX          Integer32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "A Bit Mask corresponding to the neighbor's options 
                field." 
            REFERENCE 
                "OSPF Version 3, Appendix A.2 the Options field" 
            ::= { jnxOspfv3VirtNbrEntry 7 } 
 
    jnxOspfv3VirtNbrState OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            down(1), 
                            attempt(2), 
                            init(3), 
                            twoWay(4), 
                            exchangeStart(5), 
                            exchange(6), 
                            loading(7), 
                            full(8) 
                            } 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The state of the Virtual Neighbor Relationship." 
            ::= { jnxOspfv3VirtNbrEntry 8 } 
 
    jnxOspfv3VirtNbrEvents OBJECT-TYPE 
            SYNTAX          Counter32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The number of times this virtual link has 
                changed its state, or an error has occurred." 
            ::= { jnxOspfv3VirtNbrEntry 9 } 
 
    jnxOspfv3VirtNbrLsRetransQLen OBJECT-TYPE 
            SYNTAX          Gauge32 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The current length of the retransmission 
                queue." 
            ::= { jnxOspfv3VirtNbrEntry 10 } 
 
    jnxOspfv3VirtNbrHelloSuppressed OBJECT-TYPE 
            SYNTAX          TruthValue 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "Indicates whether Hellos are being suppressed 
                to the neighbor" 
            ::= { jnxOspfv3VirtNbrEntry 11 } 

    jnxOspfv3VirtNbrIfId OBJECT-TYPE 
            SYNTAX          InterfaceIndex 
            MAX-ACCESS      read-only 
            STATUS          deprecated 
            DESCRIPTION 
                "The interface ID that the neighbor advertises 
                in its Hello Packets on this virtual link, that is, 
                the neighbor's local interface ID." 
            ::= { jnxOspfv3VirtNbrEntry 12 } 
 
   jnxOspfv3VirtNbrRestartHelperStatus OBJECT-TYPE  
           SYNTAX       INTEGER { notHelping (1),  
                                  helping (2)  
                                }  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Indicates whether the router is acting  
              as a Graceful restart helper for the neighbor."  
              ::= { jnxOspfv3VirtNbrEntry 13 }  
        
    jnxOspfv3VirtNbrRestartHelperAge OBJECT-TYPE  
           SYNTAX       JnxOspfv3UpToRefreshIntervalTc 
           UNITS        "seconds"  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Remaining time in current OSPF Graceful restart  
              interval, if the router is acting as a restart  
              helper for the neighbor."  
           ::= { jnxOspfv3VirtNbrEntry 14 }  
        
    jnxOspfv3VirtNbrRestartHelperExitRc OBJECT-TYPE  
           SYNTAX       INTEGER { none (1),  
                                  inProgress (2),  
                                  completed (3),  
                                  timedOut (4),  
                                  topologyChanged (5)  
                                }  
           MAX-ACCESS   read-only  
           STATUS       deprecated  
           DESCRIPTION  
              "Describes the outcome of the last attempt at acting  
               as a Graceful restart helper for the neighbor. 
    
              none:............no restart has yet been attempted. 
              inProgress:......a restart attempt is currently underway. 
              completed:.......the last restart completed successfully. 
              timedOut:........the last restart timed out. 
              topologyChanged:.the last restart was aborted due to 
                               a topology change."  
        ::= { jnxOspfv3VirtNbrEntry 15 } 

    -- 
    -- The OSPFv3 Area Aggregate Table 
    -- 
 
    jnxOspfv3AreaAggregateTable OBJECT-TYPE 
            SYNTAX          SEQUENCE OF JnxOspfv3AreaAggregateEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A range of IPv6 prefixes specified by a 
                prefix/prefix length pair. Note that if 
                ranges are configured such that one range 
                subsumes another range the most specific 
                match is the preferred one. Marking this table 
                and its objects deprecated as it is now implemented 
                as part of RFC 5643." 
            ::= { jnxOspfv3Objects 12 } 
 
    jnxOspfv3AreaAggregateEntry OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaAggregateEntry 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "A range of IPv6 prefixes specified by a 
                prefix/prefix length pair. Note that if 
                ranges are configured such that one range 
                subsumes another range the most specific match is 
                the preferred one." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2  Area parameters" 
            INDEX           { jnxOspfv3AreaAggregateAreaID,  
                              jnxOspfv3AreaAggregateAreaLsdbType, 
                              jnxOspfv3AreaAggregatePrefixType, 
                              jnxOspfv3AreaAggregatePrefix, 
                              jnxOspfv3AreaAggregatePrefixLength } 
            ::= { jnxOspfv3AreaAggregateTable 1 } 
 
    JnxOspfv3AreaAggregateEntry ::= SEQUENCE { 
            jnxOspfv3AreaAggregateAreaID 
                    JnxOspfv3AreaIdTc, 
            jnxOspfv3AreaAggregateAreaLsdbType 
                    INTEGER, 
            jnxOspfv3AreaAggregatePrefixType 
                    InetAddressType, 
            jnxOspfv3AreaAggregatePrefix 
                    InetAddress, 
            jnxOspfv3AreaAggregatePrefixLength 
                    InetAddressPrefixLength, 
            jnxOspfv3AreaAggregateStatus 
                    RowStatus, 
            jnxOspfv3AreaAggregateEffect 
                    INTEGER, 
            jnxOspfv3AreaAggregateRouteTag 
                    INTEGER 
            } 
 
    jnxOspfv3AreaAggregateAreaID OBJECT-TYPE 
            SYNTAX          JnxOspfv3AreaIdTc 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The Area the Address Aggregate is to be found 
                within." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3AreaAggregateEntry 1 } 
 
    jnxOspfv3AreaAggregateAreaLsdbType OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            interAreaPrefixLsa(8195), -- 0x2003 
                            nssaExternalLsa(8199)     -- 0x2007 
                            } 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The type of the Address Aggregate.  This field 
                specifies the Area Lsdb type that this Address 
                Aggregate applies to." 
            REFERENCE 
                "OSPF Version 2, Appendix A.4.1 The Link State 
                Advertisement header" 
            ::= { jnxOspfv3AreaAggregateEntry 2 } 
 
    jnxOspfv3AreaAggregatePrefixType OBJECT-TYPE 
            SYNTAX          InetAddressType 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The prefix type of ospfv3AreaAggregatePrefix. Only 
                IPv6 addresses are expected." 
            ::= { jnxOspfv3AreaAggregateEntry 4 } 
 
    jnxOspfv3AreaAggregatePrefix OBJECT-TYPE 
            SYNTAX          InetAddress (SIZE (0..16)) 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The IPv6 Prefix." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3AreaAggregateEntry 5 } 
 
    jnxOspfv3AreaAggregatePrefixLength OBJECT-TYPE 
            SYNTAX          InetAddressPrefixLength (3..128) 
            UNITS           "bits" 
            MAX-ACCESS      not-accessible 
            STATUS          deprecated 
            DESCRIPTION 
                "The length of the prefix (in bits). A prefix can 
                not be shorter than 3 bits." 
            REFERENCE 
                "OSPF Version 2, Appendix C.2 Area parameters" 
            ::= { jnxOspfv3AreaAggregateEntry 6 } 
 
    jnxOspfv3AreaAggregateStatus OBJECT-TYPE 
            SYNTAX          RowStatus 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This object permits management of the table by 
                facilitating actions such as row creation, 
                construction and destruction. 
 
                The value of this object has no effect on 
                whether other objects in this conceptual row can be 
                modified." 
            ::= { jnxOspfv3AreaAggregateEntry 7 } 
 
    jnxOspfv3AreaAggregateEffect OBJECT-TYPE 
            SYNTAX          INTEGER { 
                            advertiseMatching(1), 
                            doNotAdvertiseMatching(2) 
                            } 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "Prefixes subsumed by ranges either trigger the 
                advertisement of the indicated aggregate 
                (advertiseMatching), or result in the prefix not 
                being advertised at all outside the area." 
            DEFVAL          { advertiseMatching } 
            ::= { jnxOspfv3AreaAggregateEntry 8 } 
 
    jnxOspfv3AreaAggregateRouteTag OBJECT-TYPE 
            SYNTAX          INTEGER 
            MAX-ACCESS      read-create 
            STATUS          deprecated 
            DESCRIPTION 
                "This tag is advertised only in the summarized 
                 As-External LSA when summarizing from NSSA-LSA's to  
                 AS-External-LSA's." 
            DEFVAL         { 0 } 
            ::= { jnxOspfv3AreaAggregateEntry 9 } 
 
 
 
    -- The jnxOspfv3 Notification Table 
    
    -- The jnxOspfv3 Notification Table records fields that are 
    -- required for notifications 
    
    jnxOspfv3NotificationEntry OBJECT IDENTIFIER 
            ::= { jnxOspfv3Objects 13 } 
    
    jnxOspfv3ConfigErrorType OBJECT-TYPE 
        SYNTAX       INTEGER { 
                        badVersion (1), 
                        areaMismatch (2), 
                        unknownNbmaNbr (3), -- Router is DR eligible 
                        unknownVirtualNbr (4), 
                        helloIntervalMismatch (5), 
                        deadIntervalMismatch (6), 
                        optionMismatch (7), 
                        mtuMismatch (8), 
                        duplicateRouterId (9), 
                        noError (10) } 
        MAX-ACCESS   accessible-for-notify 
        STATUS   deprecated 
        DESCRIPTION 
           "Potential types of configuration conflicts. 
           Used by the ospfv3ConfigError and  
           ospfv3ConfigVirtError notifications. When the last value 
           of a notification using this object is needed, but no 
           notifications of that type have been sent, this value 
           pertaining to this object should be returned as 
           noError" 
        ::= { jnxOspfv3NotificationEntry 1 } 
 
    jnxOspfv3PacketType OBJECT-TYPE 
        SYNTAX       INTEGER { 
                        hello (1), 
                        dbDescript (2), 
                        lsReq (3), 
                        lsUpdate (4), 
                        lsAck (5), 
                        nullPacket (6) } 
        MAX-ACCESS   accessible-for-notify 
        STATUS       deprecated 
        DESCRIPTION 
           "OSPFv3 packet types. When the last value of a notification 
           using this object is needed, but no notifications of 
           that type have been sent, the value pertaining 
           to this object should be returned as nullPacket" 
        ::= { jnxOspfv3NotificationEntry 2 } 
 
    jnxOspfv3PacketSrc      OBJECT-TYPE 
            SYNTAX       InetAddress 
            MAX-ACCESS   accessible-for-notify 
            STATUS       deprecated 
            DESCRIPTION 
               "The IPv6 address of an inbound packet that cannot 
               be identified by a neighbor instance. When 
               the last value of a notification using this object is 
               needed, but no notifications of that type have been sent, 
               the value pertaining to this object should 
               be returned as 0 
 
               Only IPv6 addresses without zone index are expected." 
        ::= { jnxOspfv3NotificationEntry 3 } 
 
 
    -- Notification definitions 
 
 
   jnxOspfv3VirtIfStateChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId,  -- The originator of the notification 
                  jnxOspfv3VirtIfState  -- The new state 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3VirtIfStateChange notification signifies that there 
           has been a change in the state of an OSPFv3 virtual 
           interface. 
 
           This notification should be generated when the interface 
           state regresses (e.g., goes from Point-to-Point to Down) 
           or progresses to a terminal state (i.e., Point-to-Point)." 
        ::= { jnxOspfv3Notifications 1 } 
 
   jnxOspfv3NbrStateChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId,   -- The originator of  
                                    -- the notification 
                  jnxOspfv3NbrState    -- The new state 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3NbrStateChange notification signifies that 
           there has been a change in the state of a 
           non-virtual OSPFv3 neighbor. This notification should be 
           generated when the neighbor state regresses 
           (e.g., goes from Attempt or Full to 1-Way or 
           Down) or progresses to a terminal state (e.g., 
           2-Way or Full). When an neighbor transitions 
           from or to Full on non-broadcast multi-access 
           and broadcast networks, the notification should be 
           generated by the designated router. A designated 
           router transitioning to Down will be noted by 
           ospfIfStateChange." 
        ::= { jnxOspfv3Notifications 2 } 
 
   jnxOspfv3VirtNbrStateChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
                  jnxOspfv3VirtNbrState  -- The new state 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3VirtNbrStateChange notification signifies 
           that there has been a change in the state of an OSPFv3 
           virtual neighbor. This notification should be generated 
           when the neighbor state regresses (e.g., goes 
           from Attempt or Full to 1-Way or Down) or 
           progresses to a terminal state (e.g., Full)." 
        ::= { jnxOspfv3Notifications 3 } 
 
   jnxOspfv3IfConfigError NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3IfState,         -- State of the interface 
           jnxOspfv3PacketSrc,       -- IPv6 address of source 
           jnxOspfv3ConfigErrorType, -- Type of error 
           jnxOspfv3PacketType       -- Type of packet 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3IfConfigError notification signifies that a 
           packet has been received on a non-virtual 
           interface from a router whose configuration 
           parameters conflict with this router's 
           configuration parameters. Note that the event 
           optionMismatch should cause a notification only if it 
           prevents an adjacency from forming." 
        ::= { jnxOspfv3Notifications 4 } 
 
   jnxOspfv3VirtIfConfigError NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3VirtIfState,     -- State of the interface 
           jnxOspfv3ConfigErrorType, -- Type of error 
           jnxOspfv3PacketType 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3VirtIfConfigError notification signifies that a 
           packet has been received on a virtual interface 
           from a router whose configuration parameters 
           conflict with this router's configuration 
           parameters. Note that the event optionMismatch 
           should cause a notification only if it prevents an 
           adjacency from forming." 
        ::= { jnxOspfv3Notifications 5 } 
 
 
   jnxOspfv3IfRxBadPacket NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3IfState,         -- State of the interface 
           jnxOspfv3PacketSrc,       -- The source IPv6 address 
           jnxOspfv3PacketType       -- Type of packet 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3IfRxBadPacket notification signifies that an 
           ospfv3 packet that cannot be parsed has been received on a 
           non-virtual interface." 
        ::= { jnxOspfv3Notifications 6 } 
 
   jnxOspfv3VirtIfRxBadPacket NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
          jnxOspfv3VirtIfState,      -- State of the interface 
          jnxOspfv3PacketType        -- Type of packet 
          } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3VirtIfRxBadPacket notification signifies 
           that an OSPFv3 packet that cannot be parsed has been received 
           on a virtual interface." 
        ::= { jnxOspfv3Notifications 7 } 
 
 
   jnxOspfv3LsdbOverflow NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3ExtAreaLsdbLimit -- Limit on External LSAs 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3LsdbOverflow notification signifies that the 
           number of LSAs in the router's link-state 
           database has exceeded ospfv3ExtAreaLsdbLimit." 
        ::= { jnxOspfv3Notifications 8 } 
 
   jnxOspfv3LsdbApproachingOverflow NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3ExtAreaLsdbLimit 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3LsdbApproachingOverflow notification signifies 
           that the number of LSAs in the router's 
           link-state database has exceeded ninety percent of 
           ospfv3ExtAreaLsdbLimit." 
        ::= { jnxOspfv3Notifications 9 } 
 
   jnxOspfv3IfStateChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3IfState   -- The new state 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3IfStateChange notification signifies that there 
           has been a change in the state of a non-virtual 
           OSPFv3 interface. This notification should be generated 
           when the interface state regresses (e.g., goes 
           from Dr to Down) or progresses to a terminal 
           state (i.e., Point-to-Point, DR Other, Dr, or 
           Backup)." 
        ::= { jnxOspfv3Notifications 10 } 
 
   jnxOspfv3NssaTranslatorStatusChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
           jnxOspfv3AreaNssaTranslatorState  -- new state 
           } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3NssaTranslatorStatusChange notification 
           indicates that there has been a change in the router's 
           ability to translate OSPFv3 NSSA LSAs into OSPFv3 External 
           LSAs. This notification should be generated when the 
           Translator Status transitions from or to any defined 
           status on a per area basis." 
        ::= { jnxOspfv3Notifications 11 } 
 
   jnxOspfv3RestartStatusChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
                  jnxOspfv3RestartStatus,  -- new status 
                  jnxOspfv3RestartInterval, 
                  jnxOspfv3RestartExitRc 
                } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3RestartStatusChange notification signifies that 
           there has been a change in the graceful restart 
           state for the router. This notification should be 
           generated when the router restart status 
           changes." 
        ::= { jnxOspfv3Notifications 12 } 
    
   jnxOspfv3NbrRestartHelperStatusChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
                  jnxOspfv3NbrRestartHelperStatus,  -- new status 
                  jnxOspfv3NbrRestartHelperAge, 
                  jnxOspfv3NbrRestartHelperExitRc 
                } 
        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3NbrRestartHelperStatusChange notification 
           signifies that there has been a change in the 
           graceful restart helper state for the neighbor. 
           This notification should be generated when the 
           neighbor restart helper status transitions for a neighbor." 
        ::= { jnxOspfv3Notifications 13 } 
    
   jnxOspfv3VirtNbrRestartHelperStatusChange NOTIFICATION-TYPE 
        OBJECTS { jnxOspfv3RouterId, -- The originator of the notification 
                  jnxOspfv3VirtNbrRestartHelperStatus,  -- new status 
                  jnxOspfv3VirtNbrRestartHelperAge, 
                  jnxOspfv3VirtNbrRestartHelperExitRc 
                } 

        STATUS       deprecated 
        DESCRIPTION 
           "An ospfv3VirtNbrRestartHelperStatusChange 
           notification signifies that there has been a 
           change in the graceful restart helper state for 
           the virtual neighbor. This notification should be 
           generated when the virtual neighbor restart helper status 
           transitions for a virtual neighbor." 
        ::= { jnxOspfv3Notifications 14 } 
 
             
    
    -- conformance information 
 
    jnxOspfv3Groups      OBJECT IDENTIFIER ::= { jnxOspfv3Conformance 1 } 
    jnxOspfv3Compliances OBJECT IDENTIFIER ::= { jnxOspfv3Conformance 2 } 
 
    -- compliance statements 
 
    jnxOspfv3Compliance MODULE-COMPLIANCE 
            STATUS          deprecated 
            DESCRIPTION     "The compliance statement" 
            MODULE          -- this module 
            MANDATORY-GROUPS { 
                            jnxOspfv3BasicGroup, 
                            jnxOspfv3AreaGroup, 
                            jnxOspfv3IfGroup, 
                            jnxOspfv3VirtIfGroup, 
                            jnxOspfv3NbrGroup, 
                            jnxOspfv3CfgNbrGroup, 
                            jnxOspfv3VirtNbrGroup, 
                            jnxOspfv3AreaAggregateGroup, 
                            jnxOspfv3NotificationObjectGroup, 
                            jnxOspfv3NotificationGroup 
                            } 
 
            GROUP           jnxOspfv3AsLsdbGroup 
            DESCRIPTION 
                "This group is required for OSPFv3 systems that 
                display their AS-scope link state database." 
 
            GROUP           jnxOspfv3AreaLsdbGroup 
            DESCRIPTION 
                "This group is required for OSPFv3 systems that 
                display their Area-scope link state database." 
 
            GROUP           jnxOspfv3LinkLsdbGroup 
            DESCRIPTION 
                "This group is required for OSPFv3 systems that 
                display their Link-scope link state database." 
 
            GROUP           jnxOspfv3HostGroup 
            DESCRIPTION 
                "This group is required for OSPFv3 systems that 
                support attached hosts." 
 
            OBJECT          jnxOspfv3NbrAddressType 
            SYNTAX          InetAddressType { 
                                              ipv6(2) 
                                            }
            DESCRIPTION 
                "An implementation is only required to support IPv6 
                address without zone index." 
 
            OBJECT          jnxOspfv3VirtNbrAddressType 
            SYNTAX          InetAddressType { 
                                              ipv6(2) 
                                            }
            DESCRIPTION 
                "An implementation is only required to support IPv6 
                address without zone index." 
 
            ::= { jnxOspfv3Compliances 1 } 
 
    -- units of conformance 
 
    jnxOspfv3BasicGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3RouterId, 
                            jnxOspfv3AdminStat, 
                            jnxOspfv3VersionNumber, 
                            jnxOspfv3AreaBdrRtrStatus, 
                            jnxOspfv3ASBdrRtrStatus, 
                            jnxOspfv3AsScopeLsaCount, 
                            jnxOspfv3AsScopeLsaCksumSum, 
                            jnxOspfv3OriginateNewLsas, 
                            jnxOspfv3RxNewLsas, 
                            jnxOspfv3ExtLsaCount, 
                            jnxOspfv3ExtAreaLsdbLimit, 
                            jnxOspfv3MulticastExtensions, 
                            jnxOspfv3ExitOverflowInterval, 
                            jnxOspfv3DemandExtensions, 
                            jnxOspfv3ReferenceBandwidth, 
                            jnxOspfv3RestartSupport, 
                            jnxOspfv3RestartInterval, 
                            jnxOspfv3RestartStatus, 
                            jnxOspfv3RestartAge, 
                            jnxOspfv3RestartExitRc, 
                            jnxOspfv3NotificationEnable 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for managing/monitoring 
                OSPFv3 global parameters." 
            ::= { jnxOspfv3Groups 1 } 
 
 
    jnxOspfv3AreaGroup OBJECT-GROUP 
            OBJECTS         {                       
                            jnxOspfv3ImportAsExtern, 
                            jnxOspfv3AreaSpfRuns, 
                            jnxOspfv3AreaBdrRtrCount, 
                            jnxOspfv3AreaAsBdrRtrCount, 
                            jnxOspfv3AreaScopeLsaCount, 
                            jnxOspfv3AreaScopeLsaCksumSum, 
                            jnxOspfv3AreaSummary, 
                            jnxOspfv3AreaStatus, 
                            jnxOspfv3StubMetric, 
                            jnxOspfv3AreaNssaTranslatorRole, 
                            jnxOspfv3AreaNssaTranslatorState, 
                            jnxOspfv3AreaNssaTranslatorStabInt, 
                            jnxOspfv3AreaNssaTranslatorEvents, 
                            jnxOspfv3AreaStubMetricType 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for ospfv3 systems 
                supporting areas." 
            ::= { jnxOspfv3Groups 2 } 
 
    jnxOspfv3AsLsdbGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3AsLsdbSequence, 
                            jnxOspfv3AsLsdbAge, 
                            jnxOspfv3AsLsdbChecksum, 
                            jnxOspfv3AsLsdbAdvertisement, 
                            jnxOspfv3AsLsdbTypeKnown 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for ospfv3 systems 
                that display their AS-scope link state database." 
            ::= { jnxOspfv3Groups 3 } 
 
    jnxOspfv3AreaLsdbGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3AreaLsdbSequence, 
                            jnxOspfv3AreaLsdbAge, 
                            jnxOspfv3AreaLsdbChecksum, 
                            jnxOspfv3AreaLsdbAdvertisement, 
                            jnxOspfv3AreaLsdbTypeKnown 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for OSPFv3 systems 
                that display their Area-scope link state database." 
            ::= { jnxOspfv3Groups 4 } 
 
    jnxOspfv3LinkLsdbGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3LinkLsdbSequence, 
                            jnxOspfv3LinkLsdbAge, 
                            jnxOspfv3LinkLsdbChecksum, 
                            jnxOspfv3LinkLsdbAdvertisement, 
                            jnxOspfv3LinkLsdbTypeKnown 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for OSPFv3 systems 
                that display their Link-scope link state database." 
            ::= { jnxOspfv3Groups 5 } 
 
    jnxOspfv3HostGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3HostMetric, 
                            jnxOspfv3HostStatus, 
                            jnxOspfv3HostAreaID 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used for OSPFv3 systems 
                that support attached hosts." 
            ::= { jnxOspfv3Groups 6 } 
 
    jnxOspfv3IfGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3IfAreaId, 
                            jnxOspfv3IfType, 
                            jnxOspfv3IfAdminStat, 
                            jnxOspfv3IfRtrPriority, 
                            jnxOspfv3IfTransitDelay, 
                            jnxOspfv3IfRetransInterval, 
                            jnxOspfv3IfHelloInterval, 
                            jnxOspfv3IfRtrDeadInterval, 
                            jnxOspfv3IfPollInterval, 
                            jnxOspfv3IfState, 
                            jnxOspfv3IfDesignatedRouter, 
                            jnxOspfv3IfBackupDesignatedRouter, 
                            jnxOspfv3IfEvents, 
                            jnxOspfv3IfStatus, 
                            jnxOspfv3IfMulticastForwarding, 
                            jnxOspfv3IfDemand, 
                            jnxOspfv3IfMetricValue, 
                            jnxOspfv3IfLinkScopeLsaCount, 
                            jnxOspfv3IfLinkLsaCksumSum, 
                            jnxOspfv3IfDemandNbrProbe, 
                            jnxOspfv3IfDemandNbrProbeRetxLimit,  
                            jnxOspfv3IfDemandNbrProbeInterval  
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These interface objects used for  
                managing/monitoring OSPFv3 interfaces." 
            ::= { jnxOspfv3Groups 7 } 
 
    jnxOspfv3VirtIfGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3VirtIfIndex, 
                            jnxOspfv3VirtIfInstId, 
                            jnxOspfv3VirtIfTransitDelay, 
                            jnxOspfv3VirtIfRetransInterval, 
                            jnxOspfv3VirtIfHelloInterval, 
                            jnxOspfv3VirtIfRtrDeadInterval, 
                            jnxOspfv3VirtIfState, 
                            jnxOspfv3VirtIfEvents, 
                            jnxOspfv3VirtIfStatus, 
                            jnxOspfv3VirtIfLinkScopeLsaCount, 
                            jnxOspfv3VirtIfLinkLsaCksumSum  
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These virtual interface objects are used for  
                managing/monitoring OSPFv3 virtual interfaces." 
            ::= { jnxOspfv3Groups 8 } 
 
    jnxOspfv3NbrGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3NbrAddressType, 
                            jnxOspfv3NbrAddress, 
                            jnxOspfv3NbrOptions, 
                            jnxOspfv3NbrPriority, 
                            jnxOspfv3NbrState, 
                            jnxOspfv3NbrEvents, 
                            jnxOspfv3NbrLsRetransQLen, 
                            jnxOspfv3NbrHelloSuppressed, 
                            jnxOspfv3NbrIfId, 
                            jnxOspfv3NbrRestartHelperStatus,  
                            jnxOspfv3NbrRestartHelperAge,  
                            jnxOspfv3NbrRestartHelperExitRc 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These neighbor objects are used for 
                managing/monitoring OSPFv3 neighbors." 
            ::= { jnxOspfv3Groups 9 } 
 
    jnxOspfv3CfgNbrGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3CfgNbrPriority, 
                            jnxOspfv3CfgNbrStatus 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These configured neighbor objects are used for 
                managing/monitoring ospfv3 configured neighbors." 
            ::= { jnxOspfv3Groups 10 } 
 
    jnxOspfv3VirtNbrGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3VirtNbrIfIndex, 
                            jnxOspfv3VirtNbrIfInstId, 
                            jnxOspfv3VirtNbrAddressType,  
                            jnxOspfv3VirtNbrAddress, 
                            jnxOspfv3VirtNbrOptions, 
                            jnxOspfv3VirtNbrState, 
                            jnxOspfv3VirtNbrEvents, 
                            jnxOspfv3VirtNbrLsRetransQLen, 
                            jnxOspfv3VirtNbrHelloSuppressed, 
                            jnxOspfv3VirtNbrIfId, 
                            jnxOspfv3VirtNbrRestartHelperStatus,  
                            jnxOspfv3VirtNbrRestartHelperAge,  
                            jnxOspfv3VirtNbrRestartHelperExitRc 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These virtual neighbor objects are used for 
                managing/monitoring OSPFv3 virtual neighbors." 
            ::= { jnxOspfv3Groups 11 } 
 
    jnxOspfv3AreaAggregateGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3AreaAggregateStatus, 
                            jnxOspfv3AreaAggregateEffect, 
                            jnxOspfv3AreaAggregateRouteTag 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These area aggregate objects used required for 
                aggregating OSPFv3 prefixes for summarization  
                across areas." 
            ::= { jnxOspfv3Groups 12 } 
 
    jnxOspfv3NotificationObjectGroup OBJECT-GROUP 
            OBJECTS         { 
                            jnxOspfv3ConfigErrorType, 
                            jnxOspfv3PacketType, 
                            jnxOspfv3PacketSrc 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "These objects are used to record notification 
                parameters" 
            ::= { jnxOspfv3Groups 13 } 
 
    jnxOspfv3NotificationGroup NOTIFICATION-GROUP 
            NOTIFICATIONS   { 
                            jnxOspfv3VirtIfStateChange, 
                            jnxOspfv3NbrStateChange, 
                            jnxOspfv3VirtNbrStateChange, 
                            jnxOspfv3IfConfigError, 
                            jnxOspfv3VirtIfConfigError, 
                            jnxOspfv3IfRxBadPacket, 
                            jnxOspfv3VirtIfRxBadPacket, 
                            jnxOspfv3LsdbOverflow, 
                            jnxOspfv3LsdbApproachingOverflow, 
                            jnxOspfv3IfStateChange, 
                            jnxOspfv3NssaTranslatorStatusChange, 
                            jnxOspfv3RestartStatusChange, 
                            jnxOspfv3NbrRestartHelperStatusChange, 
                            jnxOspfv3VirtNbrRestartHelperStatusChange 
                            } 
            STATUS          deprecated 
            DESCRIPTION 
                "This group is used for OSPFv3 notifications" 
            ::= { jnxOspfv3Groups 14 } 
 
    END 

--     
--   Copyright (C) The Internet Society (2006). 
--   
--   This document is subject to the rights, licenses and restrictions 
--   contained in BCP 78, and except as set forth therein, the authors 
--   retain all their rights.  
--  
--   This document and the information contained herein are provided on an 
--   "AS IS" basis and THE CONTRIBUTOR, THE ORGANIZATION HE/SHE REPRESENTS 
--   OR IS SPONSORED BY (IF ANY), THE INTERNET SOCIETY AND THE INTERNET 
--   ENGINEERING TASK FORCE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED, 
--   INCLUDING BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE 
--   INFORMATION HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
--   WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. 
--  
--  

