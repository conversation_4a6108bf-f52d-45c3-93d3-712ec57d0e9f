   HOST-RESOURCES-TYPES DEFINITIONS ::= BEGIN

   IMPORTS
     MODULE-IDENTITY, OBJECT-IDENTITY        FROM SNMPv2-SMI
     hrMIBAdminInfo, hrStorage, hrDevice     FROM HOST-RESOURCES-MIB;

   hostResourcesTypesModule MODULE-IDENTITY
     LAST-UPDATED "200003060000Z"    -- 6 March, 2000
     ORGANIZATION "IETF Host Resources MIB Working Group"
     CONTACT-INFO
         "Steve Waldbusser
         Postal: Lucent Technologies, Inc.
                 1213 Innsbruck Dr.
                 Sunnyvale, CA 94089
                 USA
         Phone: ************
         Fax:   ************
         Email: <EMAIL>

         In addition, the Host Resources MIB mailing list is dedicated
         to discussion of this MIB. To join the mailing list, send a
         request <NAME_EMAIL>. The mailing
         list <NAME_EMAIL>."
     DESCRIPTION
         "This MIB module registers type definitions for
         storage types, device types, and file system types.
         After the initial revision, this module will be
         maintained by IANA."
     REVISION "200003060000Z"    -- 6 March 2000
     DESCRIPTION
         "The original version of this module, published as RFC
         2790."
     ::= { hrMIBAdminInfo 4 }

   -- Registrations for some storage types, for use with hrStorageType
   hrStorageTypes          OBJECT IDENTIFIER ::= { hrStorage 1 }

   hrStorageOther OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used when no other defined
           type is appropriate."
       ::= { hrStorageTypes 1 }

   hrStorageRam OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for RAM."
       ::= { hrStorageTypes 2 }

   hrStorageVirtualMemory OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for virtual memory,
           temporary storage of swapped or paged memory."
       ::= { hrStorageTypes 3 }

   hrStorageFixedDisk OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for non-removable
           rigid rotating magnetic storage devices."
       ::= { hrStorageTypes 4 }

   hrStorageRemovableDisk OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for removable rigid
           rotating magnetic storage devices."
       ::= { hrStorageTypes 5 }

   hrStorageFloppyDisk OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for non-rigid rotating
           magnetic storage devices."
       ::= { hrStorageTypes 6 }

   hrStorageCompactDisc OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for read-only rotating
           optical storage devices."
       ::= { hrStorageTypes 7 }

   hrStorageRamDisk OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for a file system that
           is stored in RAM."
       ::= { hrStorageTypes 8 }

   hrStorageFlashMemory OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for flash memory."
       ::= { hrStorageTypes 9 }

   hrStorageNetworkDisk OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The storage type identifier used for a
           networked file system."
       ::= { hrStorageTypes 10 }

   -- Registrations for some device types, for use with hrDeviceType
   hrDeviceTypes             OBJECT IDENTIFIER ::= { hrDevice 1 }

   hrDeviceOther OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used when no other defined
           type is appropriate."
       ::= { hrDeviceTypes 1 }

   hrDeviceUnknown OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used when the device type is
           unknown."
       ::= { hrDeviceTypes 2 }

   hrDeviceProcessor OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a CPU."
       ::= { hrDeviceTypes 3 }

   hrDeviceNetwork OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a network interface."
       ::= { hrDeviceTypes 4 }

   hrDevicePrinter OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a printer."
       ::= { hrDeviceTypes 5 }

   hrDeviceDiskStorage OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a disk drive."
       ::= { hrDeviceTypes 6 }

   hrDeviceVideo OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a video device."
       ::= { hrDeviceTypes 10 }

   hrDeviceAudio OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for an audio device."
       ::= { hrDeviceTypes 11 }

   hrDeviceCoprocessor OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a co-processor."
       ::= { hrDeviceTypes 12 }

   hrDeviceKeyboard OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a keyboard device."
       ::= { hrDeviceTypes 13 }

   hrDeviceModem OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a modem."
       ::= { hrDeviceTypes 14 }

   hrDeviceParallelPort OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a parallel port."
       ::= { hrDeviceTypes 15 }

   hrDevicePointing OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a pointing device
           (e.g., a mouse)."
       ::= { hrDeviceTypes 16 }

   hrDeviceSerialPort OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a serial port."
       ::= { hrDeviceTypes 17 }

   hrDeviceTape OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a tape storage device."
       ::= { hrDeviceTypes 18 }

   hrDeviceClock OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a clock device."
       ::= { hrDeviceTypes 19 }

   hrDeviceVolatileMemory OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a volatile memory
           storage device."
       ::= { hrDeviceTypes 20 }

   hrDeviceNonVolatileMemory OBJECT-IDENTITY
       STATUS current
       DESCRIPTION
           "The device type identifier used for a non-volatile memory
           storage device."
       ::= { hrDeviceTypes 21 }

   -- Registrations for some popular File System types,
   -- for use with hrFSType.
   hrFSTypes               OBJECT IDENTIFIER ::= { hrDevice 9 }

   hrFSOther OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used when no other
           defined type is appropriate."
       ::= { hrFSTypes 1 }

   hrFSUnknown OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used when the type of
           file system is unknown."
       ::= { hrFSTypes 2 }

   hrFSBerkeleyFFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Berkeley Fast File System."
       ::= { hrFSTypes 3 }

   hrFSSys5FS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           System V File System."
       ::= { hrFSTypes 4 }

   hrFSFat OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for
           DOS's FAT file system."
       ::= { hrFSTypes 5 }

   hrFSHPFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for OS/2's
           High Performance File System."
       ::= { hrFSTypes 6 }

   hrFSHFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Macintosh Hierarchical File System."
       ::= { hrFSTypes 7 }

   hrFSMFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Macintosh File System."
       ::= { hrFSTypes 8 }

   hrFSNTFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Windows NT File System."
       ::= { hrFSTypes 9 }

   hrFSVNode OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           VNode File System."
       ::= { hrFSTypes 10 }

   hrFSJournaled OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Journaled File System."
       ::= { hrFSTypes 11 }

   hrFSiso9660 OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           ISO 9660 File System for CD's."
       ::= { hrFSTypes 12 }

   hrFSRockRidge OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           RockRidge File System for CD's."
       ::= { hrFSTypes 13 }

   hrFSNFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           NFS File System."
       ::= { hrFSTypes 14 }

   hrFSNetware OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Netware File System."
       ::= { hrFSTypes 15 }

   hrFSAFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Andrew File System."
       ::= { hrFSTypes 16 }

   hrFSDFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           OSF DCE Distributed File System."
       ::= { hrFSTypes 17 }

   hrFSAppleshare OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           AppleShare File System."
       ::= { hrFSTypes 18 }

   hrFSRFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           RFS File System."
       ::= { hrFSTypes 19 }

   hrFSDGCFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Data General DGCFS."
       ::= { hrFSTypes 20 }

   hrFSBFS OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           SVR4 Boot File System."
       ::= { hrFSTypes 21 }

   hrFSFAT32 OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Windows FAT32 File System."
       ::= { hrFSTypes 22 }

   hrFSLinuxExt2 OBJECT-IDENTITY
       STATUS  current
       DESCRIPTION
           "The file system type identifier used for the
           Linux EXT2 File System."
       ::= { hrFSTypes 23 }

   END
