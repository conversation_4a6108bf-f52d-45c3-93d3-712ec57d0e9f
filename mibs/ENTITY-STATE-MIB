

   ENTITY-STATE-MIB DEFINITIONS ::= BEGIN

     IMPORTS
         MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, mib-2
             FROM SNMPv2-SMI
         DateAndTime
             FROM SNMPv2-TC
         MODULE-COMPLIANCE, OBJECT-G<PERSON><PERSON>, NOTIFICATION-GROUP
              FROM SNMPv2-CONF
         entPhysicalIndex
              FROM ENTITY-MIB
         EntityAdminState, EntityOperState, EntityUsageState,
         EntityAlarmStatus, EntityStandbyStatus
              FROM ENTITY-STATE-TC-MIB;

     entityStateMIB MODULE-IDENTITY
         LAST-UPDATED "200511220000Z"
         ORGANIZATION "IETF Entity MIB Working Group"

         CONTACT-INFO
                 " General Discussion: <EMAIL>
                   To Subscribe:
                   http://www.ietf.org/mailman/listinfo/entmib

                   http://www.ietf.org/html.charters/entmib-charter.html

                   Sharon Chisholm
                   Nortel Networks
                   PO Box 3511 Station C
                   Ottawa, Ont.  K1Y 4H7
                   Canada
                   <EMAIL>

                   David <PERSON>
                   548 Qualbrook Ct
                   San Jose, CA 95110
                   USA
                   Phone: ************
                   <EMAIL>
                  "
         DESCRIPTION
             "This MIB defines a state extension to the Entity MIB.

              Copyright (C) The Internet Society 2005.  This version
              of this MIB module is part of RFC 4268; see the RFC
              itself for full legal notices."
         REVISION    "200511220000Z"
         DESCRIPTION
             "Initial version, published as RFC 4268."
         ::= { mib-2 131 }

     -- Entity State Objects

     entStateObjects OBJECT IDENTIFIER ::= { entityStateMIB 1 }

     entStateTable OBJECT-TYPE
      SYNTAX      SEQUENCE OF EntStateEntry
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION
          "A table of information about state/status of entities.
           This is a sparse augment of the entPhysicalTable.  Entries
           appear in this table for values of
           entPhysicalClass [RFC4133] that in this implementation
           are able to report any of the state or status stored in
           this table.

           "
      ::= { entStateObjects 1 }

       entStateEntry OBJECT-TYPE
          SYNTAX      EntStateEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
              "State information about this physical entity."
          INDEX       { entPhysicalIndex }
          ::= { entStateTable 1 }

       EntStateEntry ::= SEQUENCE {
           entStateLastChanged DateAndTime,
           entStateAdmin       EntityAdminState,
           entStateOper        EntityOperState,
           entStateUsage       EntityUsageState,
           entStateAlarm       EntityAlarmStatus,
           entStateStandby     EntityStandbyStatus
          }

     entStateLastChanged OBJECT-TYPE
      SYNTAX      DateAndTime
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
        "The value of this object is the date and
         time when the value of any of entStateAdmin,
         entStateOper, entStateUsage, entStateAlarm,
         or entStateStandby changed for this entity.

         If there has been no change since
         the last re-initialization of the local system,
         this object contains the date and time of
         local system initialization.  If there has been
         no change since the entity was added to the
         local system, this object contains the date and
         time of the insertion."
      ::= { entStateEntry 1 }

   entStateAdmin OBJECT-TYPE
          SYNTAX      EntityAdminState
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
               "The administrative state for this entity.

                This object refers to an entities administrative
                permission to service both other entities within
                its containment hierarchy as well other users of
                its services defined by means outside the scope
                of this MIB.

                Setting this object to 'notSupported' will result
                in an 'inconsistentValue' error.  For entities that
                do not support administrative state, all set
                operations will result in an 'inconsistentValue'
                error.

                Some physical entities exhibit only a subset of the
                remaining administrative state values.  Some entities
                cannot be locked, and hence this object exhibits only
                the 'unlocked' state.  Other entities cannot be shutdown
                gracefully, and hence this object does not exhibit the
                'shuttingDown' state.  A value of 'inconsistentValue'
                will be returned if attempts are made to set this
                object to values not supported by its administrative
                model."
          ::= { entStateEntry 2 }

    entStateOper OBJECT-TYPE
          SYNTAX      EntityOperState
          MAX-ACCESS  read-only
          STATUS      current
          DESCRIPTION
              "The operational state for this entity.

               Note that unlike the state model used within the
               Interfaces MIB [RFC2863], this object does not follow
               the administrative state.  An administrative state of
               down does not predict an operational state
               of disabled.

               A value of 'testing' means that entity currently being
               tested and cannot therefore report whether it is
               operational or not.

               A value of 'disabled' means that an entity is totally
               inoperable and unable to provide service both to entities
               within its containment hierarchy, or to other receivers
               of its service as defined in ways outside the scope of
               this MIB.

               A value of 'enabled' means that an entity is fully or
               partially operable and able to provide service both to

               entities within its containment hierarchy, or to other
               receivers of its service as defined in ways outside the
               scope of this MIB.

               Note that some implementations may not be able to
               accurately report entStateOper while the
               entStateAdmin object has a value other than 'unlocked'.
               In these cases, this object MUST have a value
               of 'unknown'."
          ::= { entStateEntry 3 }

    entStateUsage OBJECT-TYPE
          SYNTAX      EntityUsageState
          MAX-ACCESS  read-only
          STATUS      current
          DESCRIPTION
              "The usage state for this entity.

               This object refers to an entity's ability to service more
               physical entities in a containment hierarchy.  A value
               of 'idle' means this entity is able to contain other
               entities but that no other entity is currently
               contained within this entity.

               A value of 'active' means that at least one entity is
               contained within this entity, but that it could handle
               more.  A value of 'busy' means that the entity is unable
               to handle any additional entities being contained in it.

               Some entities will exhibit only a subset of the
               usage state values.  Entities that are unable to ever
               service any entities within a containment hierarchy will
               always have a usage state of 'busy'.  Some entities will
               only ever be able to support one entity within its
               containment hierarchy and will therefore only exhibit
               values of 'idle' and 'busy'."
             ::= { entStateEntry 4 }

    entStateAlarm OBJECT-TYPE
          SYNTAX      EntityAlarmStatus
          MAX-ACCESS  read-only
          STATUS      current
          DESCRIPTION
              "The alarm status for this entity.  It does not include
               the alarms raised on child components within its
               containment hierarchy.

               A value of 'unknown' means that this entity is

               unable to report alarm state.  Note that this differs
               from 'indeterminate', which means that alarm state
               is supported and there are alarms against this entity,
               but the severity of some of the alarms is not known.

               If no bits are set, then this entity supports reporting
               of alarms, but there are currently no active alarms
               against this entity."
          ::= { entStateEntry 5 }

   entStateStandby OBJECT-TYPE
          SYNTAX EntityStandbyStatus
          MAX-ACCESS read-only
          STATUS current
          DESCRIPTION
               "The standby status for this entity.

               Some entities will exhibit only a subset of the
               remaining standby state values.  If this entity
               cannot operate in a standby role, the value of this
               object will always be 'providingService'."
     ::= { entStateEntry 6 }

   -- Notifications
    entStateNotifications OBJECT IDENTIFIER ::= { entityStateMIB 0 }

   entStateOperEnabled NOTIFICATION-TYPE
      OBJECTS { entStateAdmin,
                entStateAlarm
              }
      STATUS             current
      DESCRIPTION
              "An entStateOperEnabled notification signifies that the
               SNMP entity, acting in an agent role, has detected that
               the entStateOper object for one of its entities has
               transitioned into the 'enabled' state.

               The entity this notification refers can be identified by
               extracting the entPhysicalIndex from one of the
               variable bindings.  The entStateAdmin and entStateAlarm
               varbinds may be examined to find out additional
               information on the administrative state at the time of
               the operation state change as well as to find out whether
               there were any known alarms against the entity at that
               time that may explain why the physical entity has become
               operationally disabled."
     ::= { entStateNotifications 1 }

   entStateOperDisabled NOTIFICATION-TYPE
      OBJECTS { entStateAdmin,
                entStateAlarm }
      STATUS             current
      DESCRIPTION
              "An entStateOperDisabled notification signifies that the
               SNMP entity, acting in an agent role, has detected that
               the entStateOper object for one of its entities has
               transitioned into the 'disabled' state.

               The entity this notification refers can be identified by
               extracting the entPhysicalIndex from one of the
               variable bindings.  The entStateAdmin and entStateAlarm
               varbinds may be examined to find out additional
               information on the administrative state at the time of
               the operation state change as well as to find out whether
               there were any known alarms against the entity at that
               time that may affect the physical entity's
               ability to stay operationally enabled."
     ::= { entStateNotifications 2 }

   -- Conformance and Compliance

   entStateConformance OBJECT IDENTIFIER ::= { entityStateMIB 2 }

   entStateCompliances OBJECT IDENTIFIER
                     ::= { entStateConformance 1 }

   entStateCompliance MODULE-COMPLIANCE
         STATUS  current
         DESCRIPTION
             "The compliance statement for systems supporting
             the Entity State MIB."
         MODULE -- this module
             MANDATORY-GROUPS {
              entStateGroup
             }
         GROUP       entStateNotificationsGroup
            DESCRIPTION
                "This group is optional."
         OBJECT entStateAdmin
          MIN-ACCESS  read-only
          DESCRIPTION
              "Write access is not required."
      ::= { entStateCompliances 1 }

   entStateGroups OBJECT IDENTIFIER ::= { entStateConformance 2 }

   entStateGroup OBJECT-GROUP
      OBJECTS {
              entStateLastChanged,
              entStateAdmin,
              entStateOper,
              entStateUsage,
              entStateAlarm,
              entStateStandby
              }
       STATUS   current
       DESCRIPTION
            "Standard Entity State group."
       ::= { entStateGroups 1}

   entStateNotificationsGroup NOTIFICATION-GROUP
      NOTIFICATIONS {
              entStateOperEnabled,
              entStateOperDisabled
              }
       STATUS   current
       DESCRIPTION
            "Standard Entity State Notification group."
       ::= { entStateGroups 2}

   END


