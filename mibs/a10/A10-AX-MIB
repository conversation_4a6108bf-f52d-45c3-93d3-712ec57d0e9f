	--
-- File Name : A10-AX-MIB.txt
--
-- Copyright(C) 2005-2011, A10 Networks Inc. All rights reserved.
-- Software for all A10 products contain trade secrets and confidential
-- information of A10 Networks and its subsidiaries and may not be disclosed,
-- copied, reproduced or distributed to anyone outside of A10 Networks
-- without prior written consent of A10 Networks, Inc.
--
-- Description:     This is the A10 AX mib file.
--
-- History:
--
--
--

A10-AX-MIB DEFINITIONS ::= BEGIN
--================================================================
-- A10-AX-MIB
--      Management MIB for A10 application acceleration appliance
--================================================================
  IMPORTS

    DisplayString, PhysAddress
      FROM SNMPv2-TC

        InetAddressType
      FROM INET-ADDRESS-MIB

    a10Mgmt
      FROM A10-COMMON-MIB

    CounterBasedGauge64
      FROM HCNUM-TC

    MODULE-IDENTITY, OBJECT-TYPE, Gauge32, Counter32, Integer32, Counter64, OBJECT-IDENTITY, NOTIFICATION-TYPE
      FROM SNMPv2-SMI;

    axMgmt MODULE-IDENTITY
                LAST-UPDATED    "200705071327Z"
                ORGANIZATION    "A10 Networks, Inc."
                CONTACT-INFO    "Address: A10 Networks, Inc.
                                          2309 Bering Drive
                                          San Jose, CA 95131
                                 Phone:   ******-822-7210 (USA/Canada)
                                          ******-325-8676 (International)
                                 E-mail:  <EMAIL>"

                DESCRIPTION     "Management root OID for the application acceleration family appliance"
                ::=  {  a10Mgmt 4  }

    axSystem           OBJECT IDENTIFIER ::=  {  axMgmt  1  }
    axLogging          OBJECT IDENTIFIER ::=  {  axMgmt  2  }
    axApp              OBJECT IDENTIFIER ::=  {  axMgmt  3  }
    acosRoot            OBJECT IDENTIFIER ::=  {  axMgmt  100  }
--==================================================================
--  axSystem
--==================================================================

    axSysVersion           OBJECT IDENTIFIER ::= { axSystem 1 }
    axSysMemory            OBJECT IDENTIFIER ::= { axSystem 2 }
    axSysCpu               OBJECT IDENTIFIER ::= { axSystem 3 }
    axSysDisk              OBJECT IDENTIFIER ::= { axSystem 4 }
    axSysHwInfo            OBJECT IDENTIFIER ::= { axSystem 5 }
    axSysInfo              OBJECT IDENTIFIER ::= { axSystem 6 }
    axNetwork              OBJECT IDENTIFIER ::= { axSystem 7 }

--  axSysVersion
  axSysPrimaryVersionOnDisk  OBJECT-TYPE
    SYNTAX      OCTET STRING ( SIZE(0 .. 255) )
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The primary system image version on hard disk."
    ::=  {  axSysVersion  1  }

  axSysSecondaryVersionOnDisk  OBJECT-TYPE
    SYNTAX      OCTET STRING ( SIZE(0 .. 255) )
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The secondary system image version on hard disk."
    ::=  {  axSysVersion  2  }

  axSysPrimaryVersionOnCF  OBJECT-TYPE
    SYNTAX      OCTET STRING ( SIZE(0 .. 255) )
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The primary system image version on Compact Flash."
    ::=  {  axSysVersion  3  }

  axSysSecondaryVersionOnCF  OBJECT-TYPE
    SYNTAX      OCTET STRING ( SIZE(0 .. 255) )
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The secondary system image version on Compact Flash."
    ::=  {  axSysVersion  4  }

--  axSysMemory

  axSysMemoryTotal  OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The total memory(KB)."
    ::=  {  axSysMemory  1  }

  axSysMemoryUsage  OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS    read-only
    STATUS      current
    DESCRIPTION   "The usage memory(KB)."
    ::=  {  axSysMemory  2  }

--  axSysCpu info

    axSysCpuNumber OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The cpu number in a10System"
      ::= { axSysCpu 1 }

    axSysCpuTable OBJECT-TYPE
      SYNTAX SEQUENCE OF AxSysCpuEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The cpu information table."
      ::= { axSysCpu 2 }

    axSysCpuEntry OBJECT-TYPE
      SYNTAX  AxSysCpuEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The cpu entry"
      INDEX {
        axSysCpuIndex
      }
      ::= { axSysCpuTable 1 }

    AxSysCpuEntry ::=
      SEQUENCE {
              axSysCpuIndex           Integer32,
              axSysCpuUsage           DisplayString,
              axSysCpuUsageValue      Integer32,
              axSysCpuCtrlCpuFlag     Integer32
      }

    axSysCpuIndex OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The index of the CPU."
      ::= { axSysCpuEntry 1 }

    axSysCpuUsage OBJECT-TYPE
      SYNTAX DisplayString
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU Usage."
      ::= { axSysCpuEntry 2 }

    axSysCpuUsageValue OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU usage value."
      ::= { axSysCpuEntry 3 }

    axSysCpuCtrlCpuFlag OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The control CPU flag: 1 - control CPU, 0 - data CPU."
      ::= { axSysCpuEntry 4 }

    axSysAverageCpuUsage OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The average CPU usage in last 5 seconds."
      ::= { axSysCpu 3 }

    axSysAverageControlCpuUsage OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The average control CPU usage in last 5 seconds."
      ::= { axSysCpu 4 }

    axSysAverageDataCpuUsage OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The average data CPU usage  in last 5 seconds."
      ::= { axSysCpu 5 }

    axSysCpuUsageTable OBJECT-TYPE
      SYNTAX SEQUENCE OF AxSysCpuUsageEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The cpu usage information table."
      ::= { axSysCpu 6 }

    axSysCpuUsageEntry OBJECT-TYPE
      SYNTAX  AxSysCpuUsageEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The CPU Usage entry"
      INDEX {
        axSysCpuIndexInUsage,
        axSysCpuUsagePeriodIndex
      }
      ::= { axSysCpuUsageTable 1 }

    AxSysCpuUsageEntry ::=
      SEQUENCE {
              axSysCpuIndexInUsage        Integer32,
              axSysCpuUsagePeriodIndex    Integer32,
              axSysCpuUsageValueAtPeriod  Integer32,
              axSysCpuUsageCtrlCpuFlag    Integer32
      }

    axSysCpuIndexInUsage OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The index of the CPU."
      ::= { axSysCpuUsageEntry 1 }

    axSysCpuUsagePeriodIndex OBJECT-TYPE
      SYNTAX Integer32 ( 1 .. 5 )
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU usage sampling period:
                1: 1-second sampling,
                2: 5-second sampling,
                3: 10-second sampling,
                4: 30-second sampling,
                5: 60-second sampling."
      ::= { axSysCpuUsageEntry 2 }

    axSysCpuUsageValueAtPeriod OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU usage value at given period, 1-sec, 5-sec, 10-sec, 30-sec, and 60-sec."
      ::= { axSysCpuUsageEntry 3 }

    axSysCpuUsageCtrlCpuFlag OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The control CPU flag: 1 - control CPU, 0 - data CPU."
      ::= { axSysCpuUsageEntry 4 }

    axSysCpuUsagePerPartitionTable OBJECT-TYPE
      SYNTAX SEQUENCE OF AxSysCpuUsagePerPartitionEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The cpu usage per partition information table."
      ::= { axSysCpu 7 }

    axSysCpuUsagePerPartitionEntry OBJECT-TYPE
      SYNTAX  AxSysCpuUsagePerPartitionEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The CPU Usage per partition entry"
      INDEX {
        axSysCpuIndexInUsagePerPartition,
        axSysCpuUsagePerPartitionPeriodIndex,
        axSysCpuUsagePartitionName
      }
      ::= { axSysCpuUsagePerPartitionTable 1 }

    AxSysCpuUsagePerPartitionEntry ::=
      SEQUENCE {
              axSysCpuIndexInUsagePerPartition        Integer32,
              axSysCpuUsagePerPartitionPeriodIndex    Integer32,
              axSysCpuUsagePartitionName              DisplayString,
              axSysCpuUsagePerPartitionValueAtPeriod  Integer32,
              axSysCpuUsagePerPartitionCtrlCpuFlag    Integer32
      }

    axSysCpuIndexInUsagePerPartition OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The index of the CPU. The value, 0, is for the control CPU."
      ::= { axSysCpuUsagePerPartitionEntry 1 }

    axSysCpuUsagePerPartitionPeriodIndex OBJECT-TYPE
      SYNTAX Integer32 ( 1 .. 5 )
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU usage per partition sampling period:
                1: 1-second sampling,
                2: 5-second sampling,
                3: 10-second sampling,
                4: 30-second sampling,
                5: 60-second sampling."
      ::= { axSysCpuUsagePerPartitionEntry 2 }

    axSysCpuUsagePartitionName OBJECT-TYPE
      SYNTAX DisplayString
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The partition name in the CPU usage per partition table."
      ::= { axSysCpuUsagePerPartitionEntry 3 }

    axSysCpuUsagePerPartitionValueAtPeriod OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The CPU usage per partition value at given period, 1-sec, 5-sec, 10-sec, 30-sec, and 60-sec."
      ::= { axSysCpuUsagePerPartitionEntry 4 }

    axSysCpuUsagePerPartitionCtrlCpuFlag OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The control CPU flag: 1 - control CPU, 0 - data CPU."
      ::= { axSysCpuUsagePerPartitionEntry 5 }

-- axSysDisk info

    axSysDiskTotalSpace OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The total space of the disk in MB."
      ::= { axSysDisk 1 }

    axSysDiskFreeSpace OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The free space of the disk in MB."
      ::= { axSysDisk 2 }

-- axSysHwInfo

    axSysHwPhySystemTemp OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The physical system temperature in Celsius."
      ::= { axSysHwInfo 1 }

    axSysHwFan1Speed OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS deprecated
      DESCRIPTION
        "The fan1's speed"
      ::= { axSysHwInfo 2 }

    axSysHwFan2Speed OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS deprecated
      DESCRIPTION
        "The fan2's speed"
      ::= { axSysHwInfo 3 }

    axSysHwFan3Speed OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS deprecated
      DESCRIPTION
        "The fan3's speed"
      ::= { axSysHwInfo 4 }

         axSysHwPhySystemTempStatus OBJECT-TYPE
      SYNTAX INTEGER {
        failed(0),
        low-med(1),
        med-med(2),
        med-high(3),
        ok(4)
      }
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The system temperature status range"
      ::= { axSysHwInfo 5 }

    axSysLowerOrLeftPowerSupplyStatus OBJECT-TYPE
      SYNTAX INTEGER {
                off(0),
                on(1),
                unknown(-1)
      }
      MAX-ACCESS read-only
          STATUS deprecated
      DESCRIPTION
        "The lower power supply status for AX 2000, 2100, 2200, 3100, 3200, 4330, 4430, 5100, 5200, 5330, 5430, 5630, 6430 and 6630; or,
         the left power supply status for AX 2500, 2600, 300, or
         the AX 1000 power supply status."
      ::= { axSysHwInfo 7 }

    axSysUpperOrRightPowerSupplyStatus OBJECT-TYPE
      SYNTAX INTEGER {
                off(0),
                on(1),
                unknown(-1)
      }
      MAX-ACCESS read-only
          STATUS deprecated
      DESCRIPTION
         "The upper power supply status for AX 2000, 2100, 2200, 3100, 3200, 4330, 4430, 5100, 5200, 5330, 5430, 5630, 6430 and 6630
         the right power supply status for AX 2500, 2600, 3000.
         Not applied for AX 1000."
      ::= { axSysHwInfo 8 }

--  axSysFanStatusTable

    axSysFanStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxSysFanStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The table contains system fan status "
        ::= { axSysHwInfo 9 }

    axSysFanStatusEntry OBJECT-TYPE
        SYNTAX  AxSysFanStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axSysFanStatusTable"
        INDEX {
                axFanIndex
        }
        ::= { axSysFanStatusTable 1 }

    AxSysFanStatusEntry ::=
        SEQUENCE {
                axFanIndex               Integer32,
                axFanName                DisplayString,
                axFanStatus              INTEGER,
            axFanSpeed               Integer32
        }

    axFanIndex OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The system fan index."
        ::= { axSysFanStatusEntry 1 }

    axFanName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The system fan name."
        ::= { axSysFanStatusEntry 2 }

    axFanStatus OBJECT-TYPE
        SYNTAX INTEGER {
                failed(0),
                okFixedHigh(4),
                okLowMed(5),
                okMedMed(6),
                okMedHigh(7),
                notReady(-2),
                unknown(-1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Fan status:
                   0: Failed, 4: OK-fixed/high, 5: OK-low/med, 6: OK-med/med, 7: OK-med/high,
                   -2: not ready, -1: unknown."
        ::= { axSysFanStatusEntry 3 }

    axFanSpeed OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The fan speed."
        ::= { axSysFanStatusEntry 4 }

-- axPowerSupplyVoltageTotal

    axPowerSupplyVoltageTotal OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of axPowerSupplyVoltage entries."
        ::= { axSysHwInfo 10 }

-- axPowerSupplyVoltageTable

    axPowerSupplyVoltageTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxPowerSupplyVoltageEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains the system power supply voltage status."
        ::= { axSysHwInfo 11 }

    axPowerSupplyVoltageEntry OBJECT-TYPE
        SYNTAX  AxPowerSupplyVoltageEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axPowerSupplyVoltageTable"
        INDEX {
                axPowerSupplyVoltageIndex
        }
        ::= { axPowerSupplyVoltageTable 1 }

    AxPowerSupplyVoltageEntry ::=
        SEQUENCE {
                axPowerSupplyVoltageIndex         INTEGER,
                axPowerSupplyVoltageStatus        INTEGER,
                axPowerSupplyVoltageDescription   DisplayString
        }

    axPowerSupplyVoltageIndex OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The table index."
        ::= { axPowerSupplyVoltageEntry 1 }

    axPowerSupplyVoltageStatus OBJECT-TYPE
        SYNTAX INTEGER {
                invalid(0),
                normal(1),
                unknown(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The status of the indexed system power supply voltage.
                 This is only supported for the platform where the sensor data is available."
        ::= { axPowerSupplyVoltageEntry 2 }

    axPowerSupplyVoltageDescription OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The description of the system power supply voltage."
        ::= { axPowerSupplyVoltageEntry 3 }

-- axSysPowerSupplyStatusTable

    axSysPowerSupplyStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxSysPowerSupplyStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The table contains power supply  status."
        ::= { axSysHwInfo 12 }

    axSysPowerSupplyStatusEntry OBJECT-TYPE
        SYNTAX  AxSysPowerSupplyStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axSysPowerSupplyStatusTable"
        INDEX {
                axPowerSupplyIndex
        }
        ::= { axSysPowerSupplyStatusTable 1 }

    AxSysPowerSupplyStatusEntry ::=
        SEQUENCE {
                axPowerSupplyIndex      Integer32,
            axPowerSupplyName       DisplayString,
                axPowerSupplyStatus     INTEGER
        }

    axPowerSupplyIndex OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The system power suplly index."
        ::= { axSysPowerSupplyStatusEntry 1 }

    axPowerSupplyName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The pwer supply name."
        ::= { axSysPowerSupplyStatusEntry 2 }

    axPowerSupplyStatus OBJECT-TYPE
        SYNTAX INTEGER {
        off(0),
        on(1),
        absent(2),
                unknown(-1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                   "The power supply status."
        ::= { axSysPowerSupplyStatusEntry 3 }

-- axSysInfo

    axSysStartupMode OBJECT-TYPE
      SYNTAX INTEGER {
                primaryDisk(1),
                secondaryDisk(2),
                primaryCF(3),
                secondaryCF(4),
                unknown(0)
      }
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The startup mode."
      ::= { axSysInfo 1 }

    axSysSerialNumber OBJECT-TYPE
      SYNTAX OCTET STRING ( SIZE(0 .. 255) )
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The system serial number."
      ::= { axSysInfo 2 }

    axSysFirmwareVersion OBJECT-TYPE
      SYNTAX OCTET STRING ( SIZE(0 .. 255) )
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The system firmware version."
      ::= { axSysInfo 3 }

    axSysAFleXEngineVersion OBJECT-TYPE
      SYNTAX OCTET STRING ( SIZE(0 .. 255) )
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The system aFlex engine version."
      ::= { axSysInfo 4 }

--==================================================================
--  axNetwork
--==================================================================

    axInterfaces           OBJECT IDENTIFIER ::= { axNetwork 1 }
    axVlans                OBJECT IDENTIFIER ::= { axNetwork 2 }
    axTrunks               OBJECT IDENTIFIER ::= { axNetwork 3 }
    axLayer3               OBJECT IDENTIFIER ::= { axNetwork 100 }

--==================================================================
--  axInterfaces
--==================================================================

    axInterface            OBJECT IDENTIFIER ::= { axInterfaces 1 }
    axInterfaceStat        OBJECT IDENTIFIER ::= { axInterfaces 2 }

--  axInterface

    axInterfaceCount OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of axInterface entries in the table."
        ::= { axInterface 1 }

    axInterfaceTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxInterfaceEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing information of the physical interfaces."
        ::= { axInterface 2 }

    axInterfaceEntry OBJECT-TYPE
        SYNTAX  AxInterfaceEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axInterface Table"
        INDEX {
                axInterfaceIndex
        }
        ::= { axInterfaceTable 1 }

    AxInterfaceEntry ::=
        SEQUENCE {
                axInterfaceIndex                            Integer32,
                axInterfaceName                             DisplayString,
                axInterfaceMediaMaxSpeed                    Integer32,
                axInterfaceMediaMaxDuplex                   INTEGER,
                axInterfaceMediaActiveSpeed                 Integer32,
                axInterfaceMediaActiveDuplex                INTEGER,
                axInterfaceMacAddr                          PhysAddress,
                axInterfaceMtu                              Integer32,
                axInterfaceAdminStatus                      INTEGER,
                axInterfaceStatus                           INTEGER,
                axInterfaceAlias                            DisplayString,
                axInterfaceFlowCtrlAdminStatus              INTEGER,
                axInterfaceFlowCtrlOperStatus               INTEGER
        }

    axInterfaceIndex OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The index value of the interface."
        ::= { axInterfaceEntry 1 }

    axInterfaceName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of the interface."
        ::= { axInterfaceEntry 2 }

    axInterfaceMediaMaxSpeed OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The best possible media speed in MBPS for the interface."
        ::= { axInterfaceEntry 3 }

    axInterfaceMediaMaxDuplex OBJECT-TYPE
        SYNTAX INTEGER {
                none(0),
                half(1),
                full(2),
                auto(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The best possible media duplex mode for the interface.
                 half    - Force half duplex;
                 full    - Force full duplex;
                 none    - All media is deselected."
        ::= { axInterfaceEntry 4 }

    axInterfaceMediaActiveSpeed OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current active media speed for the interface."
        ::= { axInterfaceEntry 5 }

    axInterfaceMediaActiveDuplex OBJECT-TYPE
        SYNTAX INTEGER {
                none(0),
                half(1),
                full(2),
                auto(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The active media duplex mode for the specified interface.
                 half    - Half duplex;
                 full    - Full duplex;
                 auto    - Auto duplex;
                 none    - All media is disabled."
        ::= { axInterfaceEntry 6 }

    axInterfaceMacAddr OBJECT-TYPE
        SYNTAX PhysAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The MAC address of the specified interface."
        ::= { axInterfaceEntry 7 }

    axInterfaceMtu OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The maximum transmission unit size of datagram which can be
                sent/received on the specified interface."
        ::= { axInterfaceEntry 8 }

    axInterfaceAdminStatus OBJECT-TYPE
        SYNTAX INTEGER {
                false(0),
                true(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The state of this interface, whether it is enabled."
        ::= { axInterfaceEntry 9 }

    axInterfaceStatus OBJECT-TYPE
        SYNTAX INTEGER {
                up(0),
                down(1),
                disabled(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current state of the interface.
                 up            - has link and is initialized;
                 down          - has no link and is initialized;
                 disabled      - has been forced down "
        ::= { axInterfaceEntry 10 }

    axInterfaceAlias OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The alias name of the interface if defined."
        ::= { axInterfaceEntry 11 }

    axInterfaceFlowCtrlAdminStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The control flow of this interface is enabled or disabled."
        ::= { axInterfaceEntry 12 }

    axInterfaceFlowCtrlOperStatus OBJECT-TYPE
        SYNTAX INTEGER {
                false(0),
                true(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The control flow state of this interface."
        ::= { axInterfaceEntry 13 }

--  axInterfaceStat

    axInterfaceStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxInterfaceStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of the physical interfacess."
        ::= { axInterfaceStat 1 }

    axInterfaceStatEntry OBJECT-TYPE
        SYNTAX  AxInterfaceStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axInterfaceStat Table"
        INDEX {
                axInterfaceStatIndex
        }
        ::= { axInterfaceStatTable 1 }

    AxInterfaceStatEntry ::=
        SEQUENCE {
                axInterfaceStatIndex              Integer32,
                axInterfaceStatPktsIn             Counter64,
                axInterfaceStatBytesIn            Counter64,
                axInterfaceStatPktsOut            Counter64,
                axInterfaceStatBytesOut           Counter64,
                axInterfaceStatMcastIn            Counter64,
                axInterfaceStatMcastOut           Counter64,
                axInterfaceStatErrorsIn           Counter64,
                axInterfaceStatErrorsOut          Counter64,
                axInterfaceStatDropsIn            Counter64,
                axInterfaceStatDropsOut           Counter64,
                axInterfaceStatCollisions         Counter64,
                axInterfaceStatBitsPerSecIn       Counter64,
                axInterfaceStatPktsPerSecIn       Counter64,
                axInterfaceStatUtilPercentIn      Integer32,
                axInterfaceStatBitsPerSecOut      Counter64,
                axInterfaceStatPktsPerSecOut      Counter64,
                axInterfaceStatUtilPercentOut     Integer32
        }

    axInterfaceStatIndex OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The index value of the interface."
        ::= { axInterfaceStatEntry 1 }

    axInterfaceStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received on this interface."
        ::= { axInterfaceStatEntry 2 }

    axInterfaceStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received on this interface."
        ::= { axInterfaceStatEntry 3 }

    axInterfaceStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets transmitted out of this interface."
        ::= { axInterfaceStatEntry 4 }

    axInterfaceStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes transmitted out of this interface."
        ::= { axInterfaceStatEntry 5 }

    axInterfaceStatMcastIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of multicast packets received on this interface."
        ::= { axInterfaceStatEntry 6 }

    axInterfaceStatMcastOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of multicast packets transmitted out of this interface."
        ::= { axInterfaceStatEntry 7 }

    axInterfaceStatErrorsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of received packets that are either undersized,
                oversized, or have FCS errors."
        ::= { axInterfaceStatEntry 8 }

    axInterfaceStatErrorsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of excessive collisions, incremented for each
                frame that experienced 16 collisions during transmission and
                was aborted."
        ::= { axInterfaceStatEntry 9 }

    axInterfaceStatDropsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets dropped on ingress for various reasons."
        ::= { axInterfaceStatEntry 10 }

    axInterfaceStatDropsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets aged out or with excessive transmission
                delays due to multiple deferrals."
        ::= { axInterfaceStatEntry 11 }

    axInterfaceStatCollisions OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of collisions on this interface, incremented by the
                number of collisions experienced during transmissions of a frame"
        ::= { axInterfaceStatEntry 12 }

    axInterfaceStatBitsPerSecIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The input rate in bits per second."
        ::= { axInterfaceStatEntry 13 }

    axInterfaceStatPktsPerSecIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The input rate in packets per second."
        ::= { axInterfaceStatEntry 14 }

    axInterfaceStatUtilPercentIn OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The input utilization in percentage.  For the ve interface, it's 0."
        ::= { axInterfaceStatEntry 15 }

    axInterfaceStatBitsPerSecOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The output rate in bits per second."
        ::= { axInterfaceStatEntry 16 }

    axInterfaceStatPktsPerSecOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The output rate in packets per second."
        ::= { axInterfaceStatEntry 17 }

    axInterfaceStatUtilPercentOut OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The output utilization in percentage.  For the ve interface, it's 0."
        ::= { axInterfaceStatEntry 18 }

--==================================================================
--  axVlans
--==================================================================

    axVlanCfg              OBJECT IDENTIFIER ::= { axVlans 1 }

--  axVlanCfgTable

    axVlanCfgTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVlanCfgEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The table contains VLAN configuration."
        ::= { axVlanCfg 1 }

    axVlanCfgEntry OBJECT-TYPE
        SYNTAX  AxVlanCfgEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVlanCfgTable"
        INDEX {
                axVlanId
        }
        ::= { axVlanCfgTable 1 }

    AxVlanCfgEntry ::=
        SEQUENCE {
                axVlanId                            Integer32,
                axVlanName                          DisplayString,
                axVlanRouterInterface               Integer32
        }

    axVlanId OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The VLAN id."
        ::= { axVlanCfgEntry 1 }

    axVlanName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The VLAN name."
        ::= { axVlanCfgEntry 2 }

    axVlanRouterInterface OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "VLAN router interface (ve) if configured.  If a SNMP-Get value is zero, that means this object is not configured."
        ::= { axVlanCfgEntry 3 }

--  axVlanCfgMemberTable

    axVlanCfgMemberTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVlanCfgMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The table contains VLAN member configuration."
        ::= { axVlanCfg 2 }

    axVlanCfgMemberEntry OBJECT-TYPE
        SYNTAX  AxVlanCfgMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVlanCfgMemberTable"
        INDEX {
                axVlanMemberVlanId, axVlanMemberIntfId
        }
        ::= { axVlanCfgMemberTable 1 }

    AxVlanCfgMemberEntry ::=
        SEQUENCE {
                axVlanMemberVlanId                  Integer32,
                axVlanMemberIntfId                  Integer32,
                axVlanMemberTagged                  INTEGER
        }

    axVlanMemberVlanId OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The VLAN id."
        ::= { axVlanCfgMemberEntry 1 }

    axVlanMemberIntfId OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The interface id configures as the VLAN member."
        ::= { axVlanCfgMemberEntry 2 }

    axVlanMemberTagged OBJECT-TYPE
        SYNTAX INTEGER  {
                false(0),
                true(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The tagged/untagged state of the specific VLAN member."
        ::= { axVlanCfgMemberEntry 3 }

--==================================================================
--  axTrunks
--==================================================================

    axTrunk                      OBJECT IDENTIFIER ::= { axTrunks 1 }
    axTrunkStats                 OBJECT IDENTIFIER ::= { axTrunks 2 }
    axTrunkCfgMembers            OBJECT IDENTIFIER ::= { axTrunks 3 }

--  axTrunk

    axTrunkTotal OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of axTrunk entries in the table."
        ::= { axTrunk 1 }

    axTrunkTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxTrunkEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains trunk information."
        ::= { axTrunk 2 }

    axTrunkEntry OBJECT-TYPE
        SYNTAX  AxTrunkEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axTrunkTable"
        INDEX {
                axTrunkName
        }
        ::= { axTrunkTable 1 }

    AxTrunkEntry ::=
        SEQUENCE {
                axTrunkName                             DisplayString,
                axTrunkStatus                           INTEGER,
                axTrunkDescription                      DisplayString,
                axTrunkTypeLacpEnabled                  INTEGER,
                axTrunkCfgMemberCount                   INTEGER,
                axTrunkPortThreshold                    INTEGER,
                axTrunkPortThresholdTimer               INTEGER
        }

    axTrunkName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk name."
        ::= { axTrunkEntry 1 }

    axTrunkStatus OBJECT-TYPE
        SYNTAX INTEGER {
                down(0),
                up(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk status."
        ::= { axTrunkEntry 2 }

    axTrunkDescription OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk description."
        ::= { axTrunkEntry 3 }

    axTrunkTypeLacpEnabled OBJECT-TYPE
        SYNTAX INTEGER {
                false(0),
                true(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk type is dynamic, LACP."
        ::= { axTrunkEntry 4 }

    axTrunkCfgMemberCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of configured trunk members."
        ::= { axTrunkEntry 5 }

    axTrunkPortThreshold OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Threshold for minimum number of ports that need to be up."
        ::= { axTrunkEntry 6 }

    axTrunkPortThresholdTimer OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Timer for port-threshold in second."
        ::= { axTrunkEntry 7 }

--  axTrunkStats

    axTrunkStatTotal OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of axTrunkStat entries in the table."
        ::= { axTrunkStats 1 }

    axTrunkStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxTrunkStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains trunk statistic information."
        ::= { axTrunkStats 2 }

    axTrunkStatEntry OBJECT-TYPE
        SYNTAX  AxTrunkStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axTrunkStatTable"
        INDEX {
                axTrunkStatName
        }
        ::= { axTrunkStatTable 1 }

    AxTrunkStatEntry ::=
        SEQUENCE {
                axTrunkStatName              DisplayString,
                axTrunkStatPktsIn            Counter64,
                axTrunkStatBytesIn           Counter64,
                axTrunkStatPktsOut           Counter64,
                axTrunkStatBytesOut          Counter64,
                axTrunkStatMcastIn           Counter64,
                axTrunkStatMcastOut          Counter64,
                axTrunkStatErrorsIn          Counter64,
                axTrunkStatErrorsOut         Counter64,
                axTrunkStatDropsIn           Counter64,
                axTrunkStatDropsOut          Counter64
        }

    axTrunkStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk name."
        ::= { axTrunkStatEntry 1 }

    axTrunkStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of received packets on the given trunk."
        ::= { axTrunkStatEntry 2 }

    axTrunkStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of received bytes on the given trunk."
        ::= { axTrunkStatEntry 3 }

    axTrunkStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of transmitted packets on the given trunk."
        ::= { axTrunkStatEntry 4 }

    axTrunkStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of transmitted bytes on the given trunk."
        ::= { axTrunkStatEntry 5 }

    axTrunkStatMcastIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of received multicast packets on the given trunk."
        ::= { axTrunkStatEntry 6 }

    axTrunkStatMcastOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of transmitted multicast packets out of the given trunk."
        ::= { axTrunkStatEntry 7 }

    axTrunkStatErrorsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of received packets with errors by the given trunk."
        ::= { axTrunkStatEntry 8 }

    axTrunkStatErrorsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of excessive collisions, incremented for each
                frame that experienced 16 collisions during transmission and
                was aborted on the given trunk."
        ::= { axTrunkStatEntry 9 }

    axTrunkStatDropsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of dropped packets on the given trunk."
        ::= { axTrunkStatEntry 10 }

    axTrunkStatDropsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of packets aged out or with excessive transmission
                delays due to multiple deferrals on the given trunk."
        ::= { axTrunkStatEntry 11 }

--  axTrunkCfgMembers

    axTrunkCfgMemberTotal OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of axTrunkCfgMember entries."
        ::= { axTrunkCfgMembers 1 }

    axTrunkCfgMemberTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxTrunkCfgMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains configured trunk member information."
        ::= { axTrunkCfgMembers 2 }

    axTrunkCfgMemberEntry OBJECT-TYPE
        SYNTAX  AxTrunkCfgMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the sysTrunkCfgMember Table"
        INDEX {
                axTrunkCfgMemberTrunkName,
                axTrunkCfgMemberName
        }
        ::= { axTrunkCfgMemberTable 1 }

    AxTrunkCfgMemberEntry ::=
        SEQUENCE {
                axTrunkCfgMemberTrunkName     DisplayString,
                axTrunkCfgMemberName          DisplayString,
                axTrunkCfgMemberAdminStatus   INTEGER,
                axTrunkCfgMemberOperStatus    INTEGER
        }

    axTrunkCfgMemberTrunkName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk name."
        ::= { axTrunkCfgMemberEntry 1 }

    axTrunkCfgMemberName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk member name: the physical port belongs to the trunk."
        ::= { axTrunkCfgMemberEntry 2 }

    axTrunkCfgMemberAdminStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk port member administrative status."
        ::= { axTrunkCfgMemberEntry 3 }

    axTrunkCfgMemberOperStatus OBJECT-TYPE
        SYNTAX INTEGER {
                down(0),
                up(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The trunk port member operational status."
        ::= { axTrunkCfgMemberEntry 4 }

--================================================================
-- axLogging leafs
--================================================================

    axLogBufferSize    OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging database size."
        DEFVAL          { 100000 }
        ::=  {  axLogging  1  }

    axLogBufferPri OBJECT-TYPE
        SYNTAX INTEGER {
                emergency(0),
                alert(1),
                critical(2),
                error(3),
                warning(4),
                notice(5),
                info(6),
                debug(7),
                notDefined(-1)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging buffer priority, logging messages which levels above
                                that value must be output to internal database."
        DEFVAL          { 7 }
        ::=  {  axLogging  2  }

    axLogConsolePri    OBJECT-TYPE
        SYNTAX INTEGER {
                emergency(0),
                alert(1),
                critical(2),
                error(3),
                warning(4),
                notice(5),
                info(6),
                debug(7),
                notDefined(-1)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging console priority, logging messages which levels above
                                that value must be output to console."
        DEFVAL          { 7 }
        ::=  {  axLogging  3  }

    axLogEmailPri  OBJECT-TYPE
        SYNTAX INTEGER {
                emergency(0),
                alert(1),
                critical(2),
                error(3),
                warning(4),
                notice(5),
                info(6),
                debug(7),
                notDefined(-1)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging email priority, logging messages which levels above
                                that value must be output to email address."
        DEFVAL          { -1 }
        ::=  {  axLogging  4  }

    axLogEmailAddr OBJECT-TYPE
        SYNTAX          DisplayString
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The email address that receive the logging messages."
        ::=  {  axLogging  5  }

    axLogSyslogPri OBJECT-TYPE
        SYNTAX INTEGER {
                emergency(0),
                alert(1),
                critical(2),
                error(3),
                warning(4),
                notice(5),
                info(6),
                debug(7),
                notDefined(-1)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging syslog priority, logging messages which levels above
                                that value must be output to syslog host."
        DEFVAL          { -1 }
        ::=  {  axLogging  8  }

    axLogSyslogHostTable    OBJECT-TYPE
        SYNTAX SEQUENCE OF AxLogSyslogHostEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "The syslog host table."
        ::= { axLogging  9 }

    axLogSyslogHostEntry OBJECT-TYPE
      SYNTAX  AxLogSyslogHostEntry
      MAX-ACCESS not-accessible
      STATUS current
      DESCRIPTION
        "The syslog host entry"
      INDEX {
        axLogSyslogHostIndex
      }
      ::= { axLogSyslogHostTable 1 }

    AxLogSyslogHostEntry ::=
      SEQUENCE {
              axLogSyslogHostIndex               Integer32,
              axLogSyslogHost                    DisplayString
      }

    axLogSyslogHostIndex OBJECT-TYPE
      SYNTAX Integer32
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The index of the syslog host list."
      ::= { axLogSyslogHostEntry 1 }

    axLogSyslogHost OBJECT-TYPE
      SYNTAX DisplayString
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
        "The syslog host IP address or DNS name."
      ::= { axLogSyslogHostEntry 2 }

    axLogSyslogPort    OBJECT-TYPE
        SYNTAX          Integer32  ( 1 .. 32767  )
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging syslog host port."
        DEFVAL          { 514 }
        ::=  {  axLogging  10  }

    axLogMonitorPri    OBJECT-TYPE
        SYNTAX INTEGER {
                emergency(0),
                alert(1),
                critical(2),
                error(3),
                warning(4),
                notice(5),
                info(6),
                debug(7),
                notDefined(-1)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION     "The logging monitor priority, logging messages which levels above
                                that value must be output to snmp trap host."
        DEFVAL          { -1 }
        ::=  {  axLogging  11  }

--================================================================
-- axLayer3
--================================================================

    axArpInfo          OBJECT IDENTIFIER ::= { axLayer3 1 }

--================================================================
-- axArpInfo
--================================================================

    axArpEntryTotal OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of ARP entries in the table."
        ::= { axArpInfo 1 }

    axArpInfoTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxArpInfoEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains opertional ARP information."
        ::= { axArpInfo 2 }

    axArpInfoEntry OBJECT-TYPE
        SYNTAX  AxArpInfoEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axArpInfoTable"
        INDEX {
                axArpIpAddr
        }
        ::= { axArpInfoTable 1 }

    AxArpInfoEntry ::=
        SEQUENCE {
                axArpIpAddr                             DisplayString,
                axArpMacAddr                            PhysAddress,
                axArpEntryVlan                          INTEGER,
                axArpEntrySourceInterface               INTEGER,
                axArpEntrySourceIntName                 DisplayString,
                axArpEntryType                          INTEGER,
                axArpEntryAging                         INTEGER
        }

    axArpIpAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The destination IP address of the ARP entry."
        ::= { axArpInfoEntry 1 }

    axArpMacAddr OBJECT-TYPE
        SYNTAX PhysAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The MAC address for the ARP entry."
        ::= { axArpInfoEntry 2 }

    axArpEntryVlan OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The VLAN identifier for the ARP entry."
        ::= { axArpInfoEntry 3 }

    axArpEntrySourceInterface OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port number in ifIndex for the ARP entry taking effective."
        ::= { axArpInfoEntry 4 }

    axArpEntrySourceIntName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The interface description name for axArpEntrySourceInterface."
        ::= { axArpInfoEntry 5 }

    axArpEntryType OBJECT-TYPE
        SYNTAX INTEGER {
                incomplete(0),
                static(1),
                dynamic(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The type of the ARP entry."
        ::= { axArpInfoEntry 6 }

    axArpEntryAging OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The aging time of the ARP entry in seconds."
        ::= { axArpInfoEntry 7 }

--==================================================================
-- axApp
--==================================================================

    axAppGlobals            OBJECT IDENTIFIER ::= { axApp 1 }
    axServers               OBJECT IDENTIFIER ::= { axApp 2 }
    axServiceGroups         OBJECT IDENTIFIER ::= { axApp 3 }
    axVirtualServers        OBJECT IDENTIFIER ::= { axApp 4 }
    axConnReuseStats        OBJECT IDENTIFIER ::= { axApp 5 }
    axFastHttpProxyStats    OBJECT IDENTIFIER ::= { axApp 6 }
    axHttpProxyStats        OBJECT IDENTIFIER ::= { axApp 7 }
    axTcpProxyStats         OBJECT IDENTIFIER ::= { axApp 8 }
    axSslStats              OBJECT IDENTIFIER ::= { axApp 9 }
    axFtpStats              OBJECT IDENTIFIER ::= { axApp 10 }
    axNetStats              OBJECT IDENTIFIER ::= { axApp 11 }
    axNotification          OBJECT IDENTIFIER ::= { axApp 12 }
    axSmtpProxyStats        OBJECT IDENTIFIER ::= { axApp 13 }
    axSslProxyStats         OBJECT IDENTIFIER ::= { axApp 14 }
    axPersistentStats       OBJECT IDENTIFIER ::= { axApp 15 }
    axSwitchStats           OBJECT IDENTIFIER ::= { axApp 16 }
    axHA                    OBJECT IDENTIFIER ::= { axApp 17 }
    axIpNatStats            OBJECT IDENTIFIER ::= { axApp 18 }
    axSessionStats          OBJECT IDENTIFIER ::= { axApp 19 }
    axGslb                  OBJECT IDENTIFIER ::= { axApp 20 }
        axNetworkingStats       OBJECT IDENTIFIER ::= { axApp 21 }

-- axGlobals
    axAppGlobalSetting         OBJECT IDENTIFIER ::= { axAppGlobals 1 }
    axAppGlobalStats           OBJECT IDENTIFIER ::= { axAppGlobals 2 }
    axGlobalAppBuffer          OBJECT IDENTIFIER ::= { axAppGlobals 3 }
        axL3vStats                                 OBJECT IDENTIFIER ::= { axAppGlobals 4 }

-- axServers
    axServer                   OBJECT IDENTIFIER ::= { axServers 1 }
    axServerStat               OBJECT IDENTIFIER ::= { axServers 2 }
    axServerPort               OBJECT IDENTIFIER ::= { axServers 3 }
    axServerPortStat           OBJECT IDENTIFIER ::= { axServers 4 }

-- axServiceGroups
    axServiceGroup             OBJECT IDENTIFIER ::= { axServiceGroups 1 }
    axServiceGroupStat         OBJECT IDENTIFIER ::= { axServiceGroups 2 }
    axServiceGroupMember       OBJECT IDENTIFIER ::= { axServiceGroups 3 }
    axServiceGroupMemberStat   OBJECT IDENTIFIER ::= { axServiceGroups 4 }

-- axVirtualServers
    axVirtualServer            OBJECT IDENTIFIER ::= { axVirtualServers 1 }
    axVirtualServerStat        OBJECT IDENTIFIER ::= { axVirtualServers 2 }
    axVirtualServerPort        OBJECT IDENTIFIER ::= { axVirtualServers 3 }
    axVirtualServerPortStat    OBJECT IDENTIFIER ::= { axVirtualServers 4 }
    axVirtualServerNameStat        OBJECT IDENTIFIER ::= { axVirtualServers 5 }
    axVirtualServerNamePortStat    OBJECT IDENTIFIER ::= { axVirtualServers 6 }

-- axHA
    axHAGlobalConfig           OBJECT IDENTIFIER ::= { axHA 1 }
    axHAGroup                  OBJECT IDENTIFIER ::= { axHA 2 }
    axHAFloatingIP             OBJECT IDENTIFIER ::= { axHA 3 }

--==================================================================
-- axAppGlobalSetting
--==================================================================

    axAppGlobalSystemResourceUsageTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxAppGlobalSystemResourceUsageEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of the system resource usages,
                 it should contains the results as the CLI command,
                 'show system resouce-usage'
                 Resource                         Current    Default    Minimum    Maximum
                 --------------------------------------------------------------------------
                 l4-session-count                 1048576    1048576    131072     8388608
                 nat-pool-addr-count              500        500        500        4000
                 real-server-count                1024       1024       512        2048
                 real-port-count                  2048       2048       512        4096
                 service-group-count              512        512        512        1024
                 virtual-port-count               512        512        256        1024
                 virtual-server-count             512        512        512        1024
                 http-template-count              256        256        32         1024
                 proxy-template-count             256        256        32         1024
                 conn-reuse-template-count        256        256        32         1024
                 fast-tcp-template-count          256        256        32         1024
                 fast-udp-template-count          256        256        32         1024
                 client-ssl-template-count        256        256        32         1024
                 server-ssl-template-count        256        256        32         1024
                 stream-template-count            256        256        32         1024
                 persist-cookie-template-count    256        256        32         1024
                 persist-srcip-template-count     256        256        32         1024
                 nalloc-mem-val                   0          0          0          5120
                "
        ::= { axAppGlobalSetting 1 }

    axAppGlobalSystemResourceUsageEntry OBJECT-TYPE
        SYNTAX  AxAppGlobalSystemResourceUsageEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axAppGlobalSystemResourceUsage Table"
        INDEX {
                axAppGlobalSystemResourceIndex
        }
        ::= { axAppGlobalSystemResourceUsageTable 1 }

    AxAppGlobalSystemResourceUsageEntry ::=
        SEQUENCE {
                axAppGlobalSystemResourceIndex        INTEGER,
                axAppGlobalSystemResourceName         DisplayString,
                axAppGlobalAllowedCurrentValue        INTEGER,
                axAppGlobalAllowedDefaultValue        INTEGER,
                axAppGlobalAllowedMinValue            INTEGER,
                axAppGlobalAllowedMaxValue            INTEGER
        }

    axAppGlobalSystemResourceIndex OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The system resource usage table index."
        ::= { axAppGlobalSystemResourceUsageEntry 1 }

    axAppGlobalSystemResourceName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The system resource name."
        ::= { axAppGlobalSystemResourceUsageEntry 2 }

    axAppGlobalAllowedCurrentValue OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current value for the allowed system resource."
        ::= { axAppGlobalSystemResourceUsageEntry 3 }

    axAppGlobalAllowedDefaultValue OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The default value for the allowed system resource."
        ::= { axAppGlobalSystemResourceUsageEntry 4 }

    axAppGlobalAllowedMinValue OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The minimum value for the allowed system resource."
        ::= { axAppGlobalSystemResourceUsageEntry 5 }

    axAppGlobalAllowedMaxValue OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The maximum value for the allowed system resource."
        ::= { axAppGlobalSystemResourceUsageEntry 6 }

--==================================================================
-- axAppGlobalStats
--==================================================================

    axAppGlobalTotalCurrentConnections  OBJECT-TYPE
        SYNTAX                  CounterBasedGauge64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total current connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  1  }

    axAppGlobalTotalNewConnections      OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total new connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  2  }

    axAppGlobalTotalNewL4Connections    OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total new L4 connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  3  }

    axAppGlobalTotalNewL7Connections    OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total new L7 connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  4  }

    axAppGlobalTotalNewIPNatConnections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total new IP-NAT connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  5  }

    axAppGlobalTotalSSLConnections      OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total SSL connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  6  }

    axAppGlobalTotalL7Requests  OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total L7 requests"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  7  }

    axGlobalAppPacketDrop       OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total number of buffer drops in the last 10 seconds."
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  8  }

    axGlobalTotalAppPacketDrop  OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total number of accumulative buffer drops."
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  9  }

    axGlobalTotalL4Session      OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total number of L4 sessions."
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  10  }

   axGlobalTotalThroughput OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total throughput of all the interfaces."
        DEFVAL { 0 }
        ::= { axAppGlobalStats 13 }

    axAppGlobalTotalCurrentConnectionsInteger OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total current connections"
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  11  }

    axGlobalTotalL4SessionInteger       OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total number of L4 sessions."
        DEFVAL                  { 0 }
        ::=  {  axAppGlobalStats  12  }

--==================================================================
-- axGlobalAppBuffer
--==================================================================

    axAppGlobalBufferConfigLimit        OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Application buffer configured limit."
        DEFVAL                  { 0 }
        ::=  {  axGlobalAppBuffer  1  }

    axAppGlobalBufferCurrentUsage       OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Application buffer current usage."
        DEFVAL                  { 0 }
        ::=  {  axGlobalAppBuffer  2  }

--==================================================================
-- axL3vStats
--==================================================================

    axL3vGlobalStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxL3vGlobalStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The global statitics in a l3v partition."
        ::= { axL3vStats 1 }

    axL3vGlobalStatsEntry OBJECT-TYPE
        SYNTAX  AxL3vGlobalStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axL3vGlobalStatsTable"
        INDEX {
                axL3vGlobalStatsPartitionName
        }
        ::= { axL3vGlobalStatsTable 1 }

    AxL3vGlobalStatsEntry ::=
        SEQUENCE {
                axL3vGlobalStatsPartitionName                    DisplayString,
                axL3vGlobalStatsTotalThroughput              Counter64,
                axL3vGlobalStatsTotalCurrentConnections      Counter64,
                axL3vGlobalStatsTotalNewConnections          Counter64,
                axL3vGlobalStatsTotalNewL4Connections        Counter64,
                        axL3vGlobalStatsTotalNewL7Connections            Counter64,
                        axL3vGlobalStatsTotalSslConnections              Counter64,
                        axL3vGlobalStatsTotalL7Requests                          Counter64,
                        axL3vGlobalStatsTotalL4Sessions                          Counter64
        }

    axL3vGlobalStatsPartitionName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The L3V partition name."
        ::= { axL3vGlobalStatsEntry 1 }

   axL3vGlobalStatsTotalThroughput OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total throughput of all the interfaces in a L3V partition."
   ::= { axL3vGlobalStatsEntry 2 }

   axL3vGlobalStatsTotalCurrentConnections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total current connections in a L3V partition."
   ::= { axL3vGlobalStatsEntry 3 }

   axL3vGlobalStatsTotalNewConnections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total new connections in a L3V partition."
   ::= { axL3vGlobalStatsEntry 4 }

   axL3vGlobalStatsTotalNewL4Connections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total new layer 4 connections in a L3V partition."
   ::= { axL3vGlobalStatsEntry 5 }

   axL3vGlobalStatsTotalNewL7Connections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total new layer 7 connections in a L3V partition."
   ::= { axL3vGlobalStatsEntry 6 }

   axL3vGlobalStatsTotalSslConnections OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total SSL connections in a L3V partition."
   ::= { axL3vGlobalStatsEntry 7 }

   axL3vGlobalStatsTotalL7Requests OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total layer 7 requests in a L3V partition."
   ::= { axL3vGlobalStatsEntry 8 }

   axL3vGlobalStatsTotalL4Sessions OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
        "Total layer 7 session in a L3V partition."
   ::= { axL3vGlobalStatsEntry 9 }

--==================================================================
-- axServer
--==================================================================

    axServerCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total count of axServer entries in the table."
        ::= { axServer 1 }

    axServerTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServerEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of the servers."
        ::= { axServer 2 }

    axServerEntry OBJECT-TYPE
        SYNTAX  AxServerEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServer Table"
        INDEX {
                axServerName
        }
        ::= { axServerTable 1 }

    AxServerEntry ::=
        SEQUENCE {
                axServerName                                     DisplayString,
                axServerAddress                              DisplayString,
                axServerEnabledState                         INTEGER,
                axServerHealthMonitor                        DisplayString,
                axServerMonitorState                         INTEGER,
                        axServerAddressType                                      InetAddressType
        }

    axServerName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of the server."
        ::= { axServerEntry 1 }

    axServerAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP address or host name of the server."
        ::= { axServerEntry 2 }

    axServerEnabledState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The monitor state for this node address."
        ::= { axServerEntry 3 }

    axServerHealthMonitor OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The health monitor name assigned to the server"
        ::= { axServerEntry 4 }

    axServerMonitorState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server monitor status is in
                        0: Disabled (administrative disabled)
                        1: Up (administrative enabled)
                        2: Down (administrative enabled)"
        ::= { axServerEntry 5 }

        axServerAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axServerAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axServerEntry 6 }


--==================================================================
-- axServerStat
--==================================================================

    axServerStatCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total count of axServerStat entries in the table."
        ::= { axServerStat 1 }

    axServerStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServerStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of node addresses."
        ::= { axServerStat 2 }

    axServerStatEntry OBJECT-TYPE
        SYNTAX  AxServerStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServerStat Table"
        INDEX {
                axServerStatAddress
        }
        ::= { axServerStatTable 1 }

    AxServerStatEntry ::=
        SEQUENCE {
                axServerStatAddress                       DisplayString,
                axServerStatName                          DisplayString,
                axServerStatServerPktsIn                  Counter64,
                axServerStatServerBytesIn                 Counter64,
                axServerStatServerPktsOut                 Counter64,
                axServerStatServerBytesOut                Counter64,
                axServerStatServerTotalConns              Counter64,
                axServerStatServerCurConns                Integer32,
                axServerStatServerPersistConns            Integer32,
                axServerStatServerStatus                  INTEGER,
                axServerStatServerTotalL7Reqs             Counter64,
                axServerStatServerTotalCurrL7Reqs         Counter64,
                axServerStatServerTotalSuccL7Reqs         Counter64,
                axServerStatServerPeakConns               Counter32,
                        axServerStatAddressType                           InetAddressType
        }

    axServerStatAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this server."
        ::= { axServerStatEntry 1 }

    axServerStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server name."
        ::= { axServerStatEntry 2 }

    axServerStatServerPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axServerStatEntry 3 }

    axServerStatServerBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axServerStatEntry 4 }

    axServerStatServerPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent for server to client."
        ::= { axServerStatEntry 5 }

    axServerStatServerBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axServerStatEntry 6 }

    axServerStatServerTotalConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total connections from server side."
        ::= { axServerStatEntry 7 }

    axServerStatServerCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from server side."
        ::= { axServerStatEntry 8 }

    axServerStatServerPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from server side."
        ::= { axServerStatEntry 9 }

    axServerStatServerStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server status is in
                        0: Disabled (administrative disabled)
                        1: Up (administrative enabled)
                        2: Down (administrative enabled)"
        ::= { axServerStatEntry 10 }

    axServerStatServerTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axServerStatEntry 11 }

    axServerStatServerTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axServerStatEntry 12 }

    axServerStatServerTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axServerStatEntry 13 }

    axServerStatServerPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of the peak connections"
        ::= { axServerStatEntry 14 }

        axServerStatAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axServerStatAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axServerStatEntry 15 }


--==================================================================
-- axServerPort
--==================================================================

    axServerPortTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServerPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of real servers."
        ::= { axServerPort 1 }

    axServerPortEntry OBJECT-TYPE
        SYNTAX  AxServerPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServer Port Table"
        INDEX {
                axServerNameInPort,
                axServerPortType,
                axServerPortNum
        }
        ::= { axServerPortTable 1 }

    AxServerPortEntry ::=
        SEQUENCE {
                axServerNameInPort                         DisplayString,
                axServerPortType                           INTEGER,
                axServerPortNum                            Integer32,
                axServerAddressInPort                      DisplayString,
                axServerPortEnabledState                   INTEGER,
                axServerPortHealthMonitor                  DisplayString,
                axServerPortConnLimit                      Integer32,
                axServerPortWeight                         Integer32,
                axServerPortMonitorState                   INTEGER,
                        axServerAddressInPortType                          InetAddressType
        }

    axServerNameInPort OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server name."
        ::= { axServerPortEntry 1 }

    axServerPortType OBJECT-TYPE
        SYNTAX INTEGER {
                tcp(2),
                udp(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port type of the server port."
        ::= { axServerPortEntry 2 }

    axServerPortNum OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port number of the server."
        ::= { axServerPortEntry 3 }

    axServerAddressInPort OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP address or host name of the server."
        ::= { axServerPortEntry 4 }

    axServerPortEnabledState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The monitor state for this node address."
        ::= { axServerPortEntry 5 }

    axServerPortHealthMonitor OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The health monitor name assigned to the server"
        ::= { axServerPortEntry 6 }

    axServerPortConnLimit OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The connection limit of the server port."
        ::= { axServerPortEntry 7 }

    axServerPortWeight OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The weight of the server port."
        ::= { axServerPortEntry 8 }

    axServerPortMonitorState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port status is in
                        0: Disabled (administrative disabled)
                        1: Up (administrative enabled)
                        2: Down (administrative enabled)"
        ::= { axServerPortEntry 9 }

        axServerAddressInPortType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axServerAddressInPort:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axServerPortEntry 10 }


--==================================================================
-- axServerPortStat
--==================================================================

    axServerPortStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServerPortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of node addresses."
        ::= { axServerPortStat 1 }

    axServerPortStatEntry OBJECT-TYPE
        SYNTAX  AxServerPortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServerStat Table"
        INDEX {
                axServerStatAddrInPort,
                axServerStatPortType,
                axServerStatPortNum
        }
        ::= { axServerPortStatTable 1 }

    AxServerPortStatEntry ::=
        SEQUENCE {
                axServerStatAddrInPort                  DisplayString,
                axServerStatPortType                    INTEGER,
                axServerStatPortNum                     Integer32,
                axServerStatNameInPort                  DisplayString,
                axServerPortStatPktsIn                  Counter64,
                axServerPortStatBytesIn                 Counter64,
                axServerPortStatPktsOut                 Counter64,
                axServerPortStatBytesOut                Counter64,
                axServerPortStatTotalConns              Counter64,
                axServerPortStatCurConns                Integer32,
                axServerPortStatPersistConns            Integer32,
                axServerPortStatStatus                  INTEGER,
                axServerPortStatTotalL7Reqs             Counter64,
                axServerPortStatTotalCurrL7Reqs         Counter64,
                axServerPortStatTotalSuccL7Reqs         Counter64,
                axServerPortStatPeakConns               Counter32,
                        axServerStatAddrInPortType                      InetAddressType
        }

    axServerStatAddrInPort OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this server."
        ::= { axServerPortStatEntry 1 }

    axServerStatPortType OBJECT-TYPE
        SYNTAX INTEGER {
                tcp(2),
                udp(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port type."
        ::= { axServerPortStatEntry 2 }

    axServerStatPortNum OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port number."
        ::= { axServerPortStatEntry 3 }

    axServerStatNameInPort OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this server."
        ::= { axServerPortStatEntry 4 }

    axServerPortStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axServerPortStatEntry 5 }

    axServerPortStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axServerPortStatEntry 6 }

    axServerPortStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axServerPortStatEntry 7 }

    axServerPortStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axServerPortStatEntry 8 }

    axServerPortStatTotalConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The maximum connections from server side."
        ::= { axServerPortStatEntry 9 }

    axServerPortStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from server side."
        ::= { axServerPortStatEntry 10 }

    axServerPortStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from server side."
        ::= { axServerPortStatEntry 11 }

    axServerPortStatStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port status is in
                        0: Disabled (administrative disabled)
                        1: Up (administrative enabled)
                        2: Down (administrative enabled)"
        ::= { axServerPortStatEntry 12 }

    axServerPortStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axServerPortStatEntry 13 }

    axServerPortStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axServerPortStatEntry 14 }

    axServerPortStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axServerPortStatEntry 15 }

    axServerPortStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axServerPortStatEntry 16 }

        axServerStatAddrInPortType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axServerStatAddrInPort:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axServerPortStatEntry 17 }

--==================================================================
-- axServiceGroup
--==================================================================

    axServiceGroupCount OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of axServiceGroup entries in the table."
        ::= { axServiceGroup 1 }

    axServiceGroupTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServiceGroupEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing information of service groups."
        ::= { axServiceGroup 2 }

    axServiceGroupEntry OBJECT-TYPE
        SYNTAX  AxServiceGroupEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServiceGroup Table"
        INDEX {
                axServiceGroupName
        }
        ::= { axServiceGroupTable 1 }

    AxServiceGroupEntry ::=
        SEQUENCE {
                axServiceGroupName               DisplayString,
                axServiceGroupType               INTEGER,
                axServiceGroupLbAlgorithm        INTEGER,
                        axServiceGroupDisplayStatus      INTEGER,
        }

    axServiceGroupName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group name."
        ::= { axServiceGroupEntry 1 }

    axServiceGroupType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The type of the service group."
        ::= { axServiceGroupEntry 2 }

    axServiceGroupLbAlgorithm OBJECT-TYPE
        SYNTAX INTEGER {
                roundRobin(0),
                weightRoundRobin(1),
                leastConnection(2),
                weightLeastConnection(3),
                serviceLeastConnection(4),
                serviceWeightLeastConnection(5),
                fastResponseTime(6),
                leastRequest(7),
                roundRobinStrict(8),
                sourceIpHashBasedStateless(9),
                sourceIpOnlyHashBasedStateless(10),
                destinationIpHashBasedStateless(11),
                sourceDestinationIpHashBasedStateless(12),
                perPacketRoundRobinStateless(13),
                sourceIpOnlyHash(15),
                sourceIpWithPortHash(16),
                destinationIpOnlyHash(17),
                destinationIpWithPortHash(18)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The load balance method for the service group"
        ::= { axServiceGroupEntry 3 }

    axServiceGroupDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                allUp(1),
                functionalUp(2),
                partialUp(3),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of the service group:
                    AllUp(1), FunctionalUp(2), PartialUp(3), Stopped(4)."
        ::= { axServiceGroupEntry 4 }



--==================================================================
-- axServiceGroupStat
--==================================================================

    axServiceGroupStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServiceGroupStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of service groups."
        ::= { axServiceGroupStat 1 }

    axServiceGroupStatEntry OBJECT-TYPE
        SYNTAX  AxServiceGroupStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServiceGroupStat Table"
        INDEX {
                axServiceGroupStatName
        }
        ::= { axServiceGroupStatTable 1 }

    AxServiceGroupStatEntry ::=
        SEQUENCE {
                axServiceGroupStatName                        DisplayString,
                axServiceGroupStatPktsIn                      Counter64,
                axServiceGroupStatBytesIn                     Counter64,
                axServiceGroupStatPktsOut                     Counter64,
                axServiceGroupStatBytesOut                    Counter64,
                axServiceGroupStatTotConns                    Counter64,
                axServiceGroupStatCurConns                    Integer32,
                axServiceGroupStatPersistConns                Integer32,
                axServiceGroupStatDisplayStatus               INTEGER,
                axServiceGroupStatTotalL7Reqs                 Counter64,
                axServiceGroupStatTotalCurrL7Reqs             Counter64,
                axServiceGroupStatTotalSuccL7Reqs             Counter64,
                axServiceGroupStatPeakConns                   Counter32
        }

    axServiceGroupStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group name."
        ::= { axServiceGroupStatEntry 1 }

    axServiceGroupStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axServiceGroupStatEntry 2 }

    axServiceGroupStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axServiceGroupStatEntry 3 }

    axServiceGroupStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axServiceGroupStatEntry 4 }

    axServiceGroupStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axServiceGroupStatEntry 5 }

    axServiceGroupStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total connections from server side."
        ::= { axServiceGroupStatEntry 6 }

    axServiceGroupStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from server side."
        ::= { axServiceGroupStatEntry 7 }

    axServiceGroupStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from server side."
        ::= { axServiceGroupStatEntry 8 }

    axServiceGroupStatDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                allUp(1),
                functionalUp(2),
                partialUp(3),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of the service group:
                    AllUp(1), FunctionalUp(2), PartialUp(3), Stopped(4)."
        ::= { axServiceGroupStatEntry 9 }

    axServiceGroupStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axServiceGroupStatEntry 10 }

    axServiceGroupStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axServiceGroupStatEntry 11 }

    axServiceGroupStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axServiceGroupStatEntry 12 }

    axServiceGroupStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axServiceGroupStatEntry 13 }

--==================================================================
-- axServiceGroupMember
--==================================================================

    axServiceGroupMemberTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServiceGroupMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing information of service group members."
        ::= { axServiceGroupMember 1 }

    axServiceGroupMemberEntry OBJECT-TYPE
        SYNTAX  AxServiceGroupMemberEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServiceGroupMember Table"
        INDEX {
                axServiceGroupNameInMember,
                axServiceGroupMemberAddrType,
                axServerNameInServiceGroupMember,
                axServerPortNumInServiceGroupMember
        }
        ::= { axServiceGroupMemberTable 1 }

    AxServiceGroupMemberEntry ::=
        SEQUENCE {
                axServiceGroupNameInMember                DisplayString,
                axServiceGroupMemberAddrType              INTEGER,
                axServerNameInServiceGroupMember          DisplayString,
                axServerPortNumInServiceGroupMember       Integer32,
                axServerPortPriorityInServiceGroupMember  Integer32,
                axServerPortStatusInServiceGroupMember    INTEGER
        }

    axServiceGroupNameInMember OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of axServiceGroup."
        ::= { axServiceGroupMemberEntry 1 }

    axServiceGroupMemberAddrType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The type of service group"
        ::= { axServiceGroupMemberEntry 2 }

    axServerNameInServiceGroupMember OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server name in the service group member."
        ::= { axServiceGroupMemberEntry 3 }

    axServerPortNumInServiceGroupMember OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port number of this member."
        ::= { axServiceGroupMemberEntry 4 }

    axServerPortPriorityInServiceGroupMember OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The priority value of a service group member."
        ::= { axServiceGroupMemberEntry 5 }

    axServerPortStatusInServiceGroupMember OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port status of the service group member:
                    Disabled(0), Up(1), Down(2)."
        ::= { axServiceGroupMemberEntry 6 }

--==================================================================
-- axServiceGroupMemberStat
--==================================================================

    axServiceGroupMemberStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxServiceGroupMemberStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of service group members."
        ::= { axServiceGroupMemberStat 1 }

    axServiceGroupMemberStatEntry OBJECT-TYPE
        SYNTAX  AxServiceGroupMemberStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axServiceGroupMemberStat Table"
        INDEX {
                axServiceGroupMemberStatName,
                axServiceGroupMemberStatAddrType,
                axServerNameInServiceGroupMemberStat,
                axServerPortNumInServiceGroupMemberStat
        }
        ::= { axServiceGroupMemberStatTable 1 }

    AxServiceGroupMemberStatEntry ::=
        SEQUENCE {
                axServiceGroupMemberStatName                     DisplayString,
                axServiceGroupMemberStatAddrType                 INTEGER,
                axServerNameInServiceGroupMemberStat             DisplayString,
                axServerPortNumInServiceGroupMemberStat          Integer32,
                axServiceGroupMemberStatPktsIn                   Counter64,
                axServiceGroupMemberStatBytesIn                  Counter64,
                axServiceGroupMemberStatPktsOut                  Counter64,
                axServiceGroupMemberStatBytesOut                 Counter64,
                axServiceGroupMemberStatPersistConns             Integer32,
                axServiceGroupMemberStatTotConns                 Counter64,
                axServiceGroupMemberStatCurConns                 Integer32,
                axServerPortStatusInServiceGroupMemberStat       INTEGER,
                axServiceGroupMemberStatTotalL7Reqs              Counter64,
                axServiceGroupMemberStatTotalCurrL7Reqs          Counter64,
                axServiceGroupMemberStatTotalSuccL7Reqs          Counter64,
                axServiceGroupMemberStatResponseTime             Integer32,
                axServiceGroupMemberStatPeakConns                Counter32
        }

    axServiceGroupMemberStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group name."
        ::= { axServiceGroupMemberStatEntry 1 }

    axServiceGroupMemberStatAddrType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The type of service group"
        ::= { axServiceGroupMemberStatEntry 2 }

    axServerNameInServiceGroupMemberStat OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this member in the service group."
        ::= { axServiceGroupMemberStatEntry 3 }

    axServerPortNumInServiceGroupMemberStat OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port number of this member."
        ::= { axServiceGroupMemberStatEntry 4 }

    axServiceGroupMemberStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axServiceGroupMemberStatEntry 5 }

    axServiceGroupMemberStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axServiceGroupMemberStatEntry 6 }

    axServiceGroupMemberStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axServiceGroupMemberStatEntry 7 }

    axServiceGroupMemberStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axServiceGroupMemberStatEntry 8 }

    axServiceGroupMemberStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from server side."
        ::= { axServiceGroupMemberStatEntry 9 }

    axServiceGroupMemberStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total connections from server side."
        ::= { axServiceGroupMemberStatEntry 10 }

    axServiceGroupMemberStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from server side."
        ::= { axServiceGroupMemberStatEntry 11 }

    axServerPortStatusInServiceGroupMemberStat OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The server port status of the service group member:
                    Disabled(0), Up(1), Down(2)"
        ::= { axServiceGroupMemberStatEntry 12 }

    axServiceGroupMemberStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axServiceGroupMemberStatEntry 13 }

    axServiceGroupMemberStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axServiceGroupMemberStatEntry 14 }

    axServiceGroupMemberStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axServiceGroupMemberStatEntry 15 }

    axServiceGroupMemberStatResponseTime OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group member response time in millisecond."
        ::= { axServiceGroupMemberStatEntry 16 }

    axServiceGroupMemberStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group member peak connections."
        ::= { axServiceGroupMemberStatEntry 17 }

--==================================================================
-- axVirtualServer
--==================================================================

    axVirtualServerCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of axVirtualServer entries in the table."
        ::= { axVirtualServer 1 }

    axVirtualServerTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing information of virtual servers."
        ::= { axVirtualServer 2 }

    axVirtualServerEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServer Table"
        INDEX {
                axVirtualServerName
        }
        ::= { axVirtualServerTable 1 }

    AxVirtualServerEntry ::=
        SEQUENCE {
                axVirtualServerName                      DisplayString,
                axVirtualServerAddress                   DisplayString,
                axVirtualServerEnabled                   INTEGER,
                axVirtualServerHAGroup                   DisplayString,
                axVirtualServerDisplayStatus             INTEGER,
                        axVirtualServerAddressType                       InetAddressType
        }

    axVirtualServerName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of this virtual server."
        ::= { axVirtualServerEntry 1 }

    axVirtualServerAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this virtual server."
        ::= { axVirtualServerEntry 2 }

    axVirtualServerEnabled OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Whether this virtual server is enabled."
        ::= { axVirtualServerEntry 3 }

    axVirtualServerHAGroup OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "HA group name configured for the virtual server."
        ::= { axVirtualServerEntry 4 }

    axVirtualServerDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                partialUp(3),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server port:
                    Disabled(0), AllUp(1), FunctionalUp(2), PartialUp(3), Stopped(4)."
        ::= { axVirtualServerEntry 5 }

        axVirtualServerAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axVirtualServerAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axVirtualServerEntry 6 }

--==================================================================
-- axVirtualServerStat
--==================================================================

    axVirtualServerStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of virtual servers."
        ::= { axVirtualServerStat 1 }

    axVirtualServerStatEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServerStat Table"
        INDEX {
                axVirtualServerStatAddress
        }
        ::= { axVirtualServerStatTable 1 }

    AxVirtualServerStatEntry ::=
        SEQUENCE {
                axVirtualServerStatAddress              DisplayString,
                axVirtualServerStatName                 DisplayString,
                axVirtualServerStatPktsIn               Counter64,
                axVirtualServerStatBytesIn              Counter64,
                axVirtualServerStatPktsOut              Counter64,
                axVirtualServerStatBytesOut             Counter64,
                axVirtualServerStatPersistConns         Integer32,
                axVirtualServerStatTotConns             Counter64,
                axVirtualServerStatCurConns             Integer32,
                axVirtualServerStatStatus               INTEGER,
                axVirtualServerStatDisplayStatus        INTEGER,
                axVirtualServerStatTotalL7Reqs          Counter64,
                axVirtualServerStatTotalCurrL7Reqs      Counter64,
                axVirtualServerStatTotalSuccL7Reqs      Counter64,
                axVirtualServerStatPeakConns            Counter32,
                        axVirtualServerStatAddressType          InetAddressType
        }

    axVirtualServerStatAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The address of this virtual server."
        ::= { axVirtualServerStatEntry 1 }

    axVirtualServerStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of this virtual server."
        ::= { axVirtualServerStatEntry 2 }

    axVirtualServerStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axVirtualServerStatEntry 3 }

    axVirtualServerStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axVirtualServerStatEntry 4 }

    axVirtualServerStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axVirtualServerStatEntry 5 }

    axVirtualServerStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axVirtualServerStatEntry 6 }

    axVirtualServerStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from client side."
        ::= { axVirtualServerStatEntry 7 }

    axVirtualServerStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total connections from client side."
        ::= { axVirtualServerStatEntry 8 }

    axVirtualServerStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from client side."
        ::= { axVirtualServerStatEntry 9 }

    axVirtualServerStatStatus OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                disabled(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current virtual server status."
        ::= { axVirtualServerStatEntry 10 }

    axVirtualServerStatDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                partialUp(3),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server:
                    Disabled(0), AllUp(1), FunctionalUp(2), PartialUp(3), Stopped(4)."
        ::= { axVirtualServerStatEntry 11 }

    axVirtualServerStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axVirtualServerStatEntry 12 }

    axVirtualServerStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axVirtualServerStatEntry 13 }

    axVirtualServerStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axVirtualServerStatEntry 14 }

    axVirtualServerStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axVirtualServerStatEntry 15 }

    axVirtualServerStatAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axVirtualServerStatAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axVirtualServerStatEntry 16 }

--==================================================================
-- axVirtualServerPort
--==================================================================

    axVirtualServerPortTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing information of virtual server port configuration."
        ::= { axVirtualServerPort 1 }

    axVirtualServerPortEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServerPort Table"
        INDEX {
                axVirtualServerPortName,
                axVirtualServerPortType,
                axVirtualServerPortNum
        }
        ::= { axVirtualServerPortTable 1 }

    AxVirtualServerPortEntry ::=
        SEQUENCE {
                axVirtualServerPortName                      DisplayString,
                axVirtualServerPortType                      INTEGER,
                axVirtualServerPortNum                       Integer32,
                axVirtualServerPortAddress                   DisplayString,
                axVirtualServerPortEnabled                   INTEGER,
                axVirtualServerPortServiceGroup              DisplayString,
                axVirtualServerPortHaGroupID                 INTEGER,
                axVirtualServerPortPersistTemplateType       INTEGER,
                axVirtualServerPortPersistTempl              DisplayString,
                axVirtualServerPortTemplate                  DisplayString,
                axVirtualServerPortPolicyTemplate            DisplayString,
                axVirtualServerPortTCPTemplate               DisplayString,
                axVirtualServerPortHTTPTemplate              DisplayString,
                axVirtualServerPortRamCacheTemplate          DisplayString,
                axVirtualServerPortConnReuseTemplate         DisplayString,
                axVirtualServerPortTCPProxyTemplate          DisplayString,
                axVirtualServerPortClientSSLTemplate         DisplayString,
                axVirtualServerPortServerSSLTemplate         DisplayString,
                axVirtualServerPortRTSPTemplate              DisplayString,
                axVirtualServerPortSMTPTemplate              DisplayString,
                axVirtualServerPortSIPTemplate               DisplayString,
                axVirtualServerPortUDPTemplate               DisplayString,
                axVirtualServerPortDisplayStatus             INTEGER,
                        xVirtualServerPortAddressType                            InetAddressType,
                        axVirtualServerPortDiameterTemplate          DisplayString,
                        axVirtualServerPortAddressType                       InetAddressType
        }

    axVirtualServerPortName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of the virtual server. "
        ::= { axVirtualServerPortEntry 1 }

    axVirtualServerPortType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3),
                rtsp(8),
                ftp(9),
                mms(10),
                fastHTTP(12),
                http(14),
                https(15),
                sslProxy(16),
                smtp(17),
                sip(11),
                sips(19),
                sip-TCP(18),
                others(5),
                tcpProxy(20),
                diameter(21),
                dnsUdp(22),
                tftp(23),
                dnsTcp(24)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port type of a virtual server port."
        ::= { axVirtualServerPortEntry 2 }

    axVirtualServerPortNum OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The virtual server port number."
        ::= { axVirtualServerPortEntry 3 }

    axVirtualServerPortAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this virtual server. "
        ::= { axVirtualServerPortEntry 4 }

    axVirtualServerPortEnabled OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The status of this virtual server port is enabled."
        ::= { axVirtualServerPortEntry 5 }

    axVirtualServerPortServiceGroup OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service group is assigned to the virtual server port."
        ::= { axVirtualServerPortEntry 6 }

    axVirtualServerPortHaGroupID OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA group ID assigned to the virtual service port."
        ::= { axVirtualServerPortEntry 7 }

    axVirtualServerPortPersistTemplateType OBJECT-TYPE
        SYNTAX INTEGER {
                cookiePersist(1),
                sourcIPPersist(2),
                destinationIPPersist(3),
                sslIDPersist(4),
                unknown(0)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The persistent template type if applicable."
        ::= { axVirtualServerPortEntry 8 }

    axVirtualServerPortPersistTempl OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The persistent template dependent on the axVirtualServerPortPersistTemplateType value."
        ::= { axVirtualServerPortEntry 9 }

    axVirtualServerPortTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The virtual server port template for all port types except for Firewall."
        ::= { axVirtualServerPortEntry 10 }

    axVirtualServerPortPolicyTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The policy template for all port types except for Firewall."
        ::= { axVirtualServerPortEntry 11 }

    axVirtualServerPortTCPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The TCP template for TPC/FastHTTP/RTSP/FTP/MMS/Others port types."
        ::= { axVirtualServerPortEntry 12 }

    axVirtualServerPortHTTPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HTTP template for HTTP/HTTPS/FastHTTP port types."
        ::= { axVirtualServerPortEntry 13 }

    axVirtualServerPortRamCacheTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The RAM cache template for HTTP/HTTPS port types."
        ::= { axVirtualServerPortEntry 14 }

    axVirtualServerPortConnReuseTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The connection reuse template for HTTP/HTTPS/FastHTTP port types."
        ::= { axVirtualServerPortEntry 15 }

    axVirtualServerPortTCPProxyTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The TCP proxy template for HTTP/HTTPS/SSLProxy/SMTP port types."
        ::= { axVirtualServerPortEntry 16 }

    axVirtualServerPortClientSSLTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Client-SSL template for HTTPS/SSLProxy/SMTP port types."
        ::= { axVirtualServerPortEntry 17 }

    axVirtualServerPortServerSSLTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Server-SSL template for HTTPS port type only."
        ::= { axVirtualServerPortEntry 18 }

    axVirtualServerPortRTSPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The RTSP template for RTSP port type only."
        ::= { axVirtualServerPortEntry 19 }

    axVirtualServerPortSMTPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The SMTP template for SMTP port type only."
        ::= { axVirtualServerPortEntry 20 }

    axVirtualServerPortSIPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The SIP template for SIP port type only."
        ::= { axVirtualServerPortEntry 21 }

    axVirtualServerPortUDPTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The UDP template for UDP port type only."
        ::= { axVirtualServerPortEntry 22 }

    axVirtualServerPortDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server port:
                    Disabled(0), AllUp(1), FunctionalUp(2), Stopped(4)."
        ::= { axVirtualServerPortEntry 23 }

-- axVirtualServerPortDisplayStatus 24 is used in 266 for axVirtualServerPortAddressType

        axVirtualServerPortAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axVirtualServerPortAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axVirtualServerPortEntry 24 }

    axVirtualServerPortDiameterTemplate OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Diameter template for diameter type only."
        ::= { axVirtualServerPortEntry 25 }

--==================================================================
-- axVirtualServerPortStat
--==================================================================

    axVirtualServerPortStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerPortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of virtual server service ports."
        ::= { axVirtualServerPortStat 1 }

    axVirtualServerPortStatEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerPortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServerPortStat Table"
        INDEX {
                axVirtualServerPortStatAddress,
                axVirtualServerStatPortType,
                axVirtualServerStatPortNum
        }
        ::= { axVirtualServerPortStatTable 1 }

    AxVirtualServerPortStatEntry ::=
        SEQUENCE {
                axVirtualServerPortStatAddress              DisplayString,
                axVirtualServerStatPortType                 INTEGER,
                axVirtualServerStatPortNum                  Integer32,
                axVirtualServerPortStatName                 DisplayString,
                axVirtualServerStatPortStatus               INTEGER,
                axVirtualServerPortStatPktsIn               Counter64,
                axVirtualServerPortStatBytesIn              Counter64,
                axVirtualServerPortStatPktsOut              Counter64,
                axVirtualServerPortStatBytesOut             Counter64,
                axVirtualServerPortStatPersistConns         Integer32,
                axVirtualServerPortStatTotConns             Counter64,
                axVirtualServerPortStatCurConns             Integer32,
                axVirtualServerStatPortDisplayStatus        INTEGER,
                axVirtualServerPortStatTotalL7Reqs          Counter64,
                axVirtualServerPortStatTotalCurrL7Reqs      Counter64,
                axVirtualServerPortStatTotalSuccL7Reqs      Counter64,
                axVirtualServerPortStatPeakConns            Counter32,
                        axVirtualServerPortStatAddressType              InetAddressType
        }

    axVirtualServerPortStatAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ip address of this virtual address."
        ::= { axVirtualServerPortStatEntry 1 }

    axVirtualServerStatPortType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3),
                rtsp(8),
                ftp(9),
                mms(10),
                fastHTTP(12),
                http(14),
                https(15),
                sslProxy(16),
                smtp(17),
                sip(11),
                sips(19),
                sip-tcp(18),
                others(5),
                tcpProxy(20),
                diameter(21),
                dnsUdp(22),
                tftp(23),
        dnsTcp(24)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port type of a virtual server port"
        ::= { axVirtualServerPortStatEntry 2 }

    axVirtualServerStatPortNum OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service port number."
        ::= { axVirtualServerPortStatEntry 3 }

    axVirtualServerPortStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of the virtual server"
        ::= { axVirtualServerPortStatEntry 4 }

    axVirtualServerStatPortStatus OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                disabled(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The status of this virtual server port."
        ::= { axVirtualServerPortStatEntry 5 }

    axVirtualServerPortStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axVirtualServerPortStatEntry 6 }

    axVirtualServerPortStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axVirtualServerPortStatEntry 7 }

    axVirtualServerPortStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axVirtualServerPortStatEntry 8 }

    axVirtualServerPortStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axVirtualServerPortStatEntry 9 }

    axVirtualServerPortStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "Persistent connections from client side."
        ::= { axVirtualServerPortStatEntry 10 }

    axVirtualServerPortStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Total connections from client side."
        ::= { axVirtualServerPortStatEntry 11 }

    axVirtualServerPortStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Current connections from client side."
        ::= { axVirtualServerPortStatEntry 12 }

    axVirtualServerStatPortDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server port:
                    Disabled(0), AllUp(1), FunctionalUp(2), Stopped(4)."
        ::= { axVirtualServerPortStatEntry 13 }

    axVirtualServerPortStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axVirtualServerPortStatEntry 14 }

    axVirtualServerPortStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axVirtualServerPortStatEntry 15 }

    axVirtualServerPortStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axVirtualServerPortStatEntry 16 }

    axVirtualServerPortStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axVirtualServerPortStatEntry 17 }

        axVirtualServerPortStatAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axVirtualServerPortStatAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axVirtualServerPortStatEntry 18 }

--==================================================================
-- axVirtualServerNameStat
--==================================================================

    axVirtualServerNameStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerNameStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of virtual servers."
        ::= { axVirtualServerNameStat 1 }

    axVirtualServerNameStatEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerNameStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServerNameStat Table"
        INDEX {
                axVirtualServerStatDisplayName
        }
        ::= { axVirtualServerNameStatTable 1 }

    AxVirtualServerNameStatEntry ::=
        SEQUENCE {
                axVirtualServerStatDisplayName          DisplayString,
                axVirtualServerNameStatPktsIn               Counter64,
                axVirtualServerNameStatBytesIn              Counter64,
                axVirtualServerNameStatPktsOut              Counter64,
                axVirtualServerNameStatBytesOut             Counter64,
                axVirtualServerNameStatPersistConns         Integer32,
                axVirtualServerNameStatTotConns             Counter64,
                axVirtualServerNameStatCurConns             Integer32,
                axVirtualServerNameStatStatus               INTEGER,
                axVirtualServerNameStatDisplayStatus        INTEGER,
                axVirtualServerNameStatTotalL7Reqs          Counter64,
                axVirtualServerNameStatTotalCurrL7Reqs      Counter64,
                axVirtualServerNameStatTotalSuccL7Reqs      Counter64,
                axVirtualServerNameStatPeakConns            Counter32
        }

    axVirtualServerStatDisplayName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of this virtual server."
        ::= { axVirtualServerNameStatEntry 1 }

    axVirtualServerNameStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axVirtualServerNameStatEntry 2 }

    axVirtualServerNameStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axVirtualServerNameStatEntry 3 }

    axVirtualServerNameStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axVirtualServerNameStatEntry 4 }

    axVirtualServerNameStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axVirtualServerNameStatEntry 5 }

    axVirtualServerNameStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "The persistent connections from client side."
        ::= { axVirtualServerNameStatEntry 6 }

    axVirtualServerNameStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total connections from client side."
        ::= { axVirtualServerNameStatEntry 7 }

    axVirtualServerNameStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current connections from client side."
        ::= { axVirtualServerNameStatEntry 8 }

    axVirtualServerNameStatStatus OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                disabled(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The current virtual server status."
        ::= { axVirtualServerNameStatEntry 9 }

    axVirtualServerNameStatDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                partialUp(3),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server:
                    Disabled(0), AllUp(1), FunctionalUp(2), PartialUp(3), Stopped(4)."
        ::= { axVirtualServerNameStatEntry 10 }

    axVirtualServerNameStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axVirtualServerNameStatEntry 11 }

    axVirtualServerNameStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axVirtualServerNameStatEntry 12 }

    axVirtualServerNameStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axVirtualServerNameStatEntry 13 }

    axVirtualServerNameStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axVirtualServerNameStatEntry 14 }

--==================================================================
-- axVirtualServerNamePortStat
--==================================================================

    axVirtualServerNamePortStatTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxVirtualServerNamePortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table containing statistic information of virtual server service ports."
        ::= { axVirtualServerNamePortStat 1 }

    axVirtualServerNamePortStatEntry OBJECT-TYPE
        SYNTAX  AxVirtualServerNamePortStatEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axVirtualServerNamePortStat Table"
        INDEX {
                axVirtualServerNamePortStatName,
                axVirtualServerNameStatPortType,
                axVirtualServerNameStatPortNum
        }
        ::= { axVirtualServerNamePortStatTable 1 }

    AxVirtualServerNamePortStatEntry ::=
        SEQUENCE {
                axVirtualServerNamePortStatName                 DisplayString,
                axVirtualServerNameStatPortType                 INTEGER,
                axVirtualServerNameStatPortNum                  Integer32,
                axVirtualServerNameStatPortStatus               INTEGER,
                axVirtualServerNamePortStatPktsIn               Counter64,
                axVirtualServerNamePortStatBytesIn              Counter64,
                axVirtualServerNamePortStatPktsOut              Counter64,
                axVirtualServerNamePortStatBytesOut             Counter64,
                axVirtualServerNamePortStatPersistConns         Integer32,
                axVirtualServerNamePortStatTotConns             Counter64,
                axVirtualServerNamePortStatCurConns             Integer32,
                axVirtualServerNameStatPortDisplayStatus        INTEGER,
                axVirtualServerNamePortStatTotalL7Reqs          Counter64,
                axVirtualServerNamePortStatTotalCurrL7Reqs      Counter64,
                axVirtualServerNamePortStatTotalSuccL7Reqs      Counter64,
                axVirtualServerNamePortStatPeakConns            Counter32
        }

    axVirtualServerNamePortStatName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The name of the virtual server"
        ::= { axVirtualServerNamePortStatEntry 1 }

    axVirtualServerNameStatPortType OBJECT-TYPE
        SYNTAX INTEGER {
                firewall(1),
                tcp(2),
                udp(3),
                rtsp(8),
                ftp(9),
                mms(10),
                fastHTTP(12),
                http(14),
                https(15),
                sslProxy(16),
                smtp(17),
                sip(11),
                sips(19),
                sip-tcp(18),
                others(5),
                tcpProxy(20),
                diameter(21),
                dnsUdp(22),
                tftp(23),
        dnsTcp(24)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The port type of a virtual server port"
        ::= { axVirtualServerNamePortStatEntry 2 }

    axVirtualServerNameStatPortNum OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service port number."
        ::= { axVirtualServerNamePortStatEntry 3 }

    axVirtualServerNameStatPortStatus OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                disabled(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The status of this virtual server port."
        ::= { axVirtualServerNamePortStatEntry 4 }

    axVirtualServerNamePortStatPktsIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets received from client to server."
        ::= { axVirtualServerNamePortStatEntry 5 }

    axVirtualServerNamePortStatBytesIn OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes received from client to server."
        ::= { axVirtualServerNamePortStatEntry 6 }

    axVirtualServerNamePortStatPktsOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of packets sent from server to client."
        ::= { axVirtualServerNamePortStatEntry 7 }

    axVirtualServerNamePortStatBytesOut OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of bytes sent from server to client."
        ::= { axVirtualServerNamePortStatEntry 8 }

    axVirtualServerNamePortStatPersistConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS deprecated
        DESCRIPTION
                "Persistent connections from client side."
        ::= { axVirtualServerNamePortStatEntry 9 }

    axVirtualServerNamePortStatTotConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Total connections from client side."
        ::= { axVirtualServerNamePortStatEntry 10 }

    axVirtualServerNamePortStatCurConns OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Current connections from client side."
        ::= { axVirtualServerNamePortStatEntry 11 }

    axVirtualServerNameStatPortDisplayStatus OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                allUp(1),
                functionalUp(2),
                stopped(4)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The display status of this virtual server port:
                    Disabled(0), AllUp(1), FunctionalUp(2), Stopped(4)."
        ::= { axVirtualServerNamePortStatEntry 12 }

    axVirtualServerNamePortStatTotalL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of L7 requests if applicable"
        ::= { axVirtualServerNamePortStatEntry 13 }

    axVirtualServerNamePortStatTotalCurrL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of current L7 requests if applicable"
        ::= { axVirtualServerNamePortStatEntry 14 }

    axVirtualServerNamePortStatTotalSuccL7Reqs OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of successful L7 requests if applicable"
        ::= { axVirtualServerNamePortStatEntry 15 }

    axVirtualServerNamePortStatPeakConns OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of peak connections"
        ::= { axVirtualServerNamePortStatEntry 16 }

--==================================================================
-- axConnReuseStat
--==================================================================
        axConnReuseStatTotalOpenPersist OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of open persistent connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStats  1  }

        axConnReuseStatTotalActivePersist OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of active persistent connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStats  2  }

        axConnReuseStatTotalEstablished OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of established connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStats  3  }

        axConnReuseStatTotalTerminated OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of terminated connection-reuse sessions."
                ::=  {  axConnReuseStats  4  }

        axConnReuseStatTotalBound OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of bound connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStats  5  }


        axConnReuseStatTotalUNBound OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of unbound connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStats  6  }

        axConnReuseStatTable    OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxConnReuseStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION     "The connection-reuse status table."
                ::=  { axConnReuseStats  7 }

        axConnReuseStatEntry    OBJECT-TYPE
                SYNTAX          AxConnReuseStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION     "The connection-reuse entry."
                INDEX           {  axConnReuseStatCpuIndex  }
                ::=  { axConnReuseStatTable 1 }

        AxConnReuseStatEntry  ::=  SEQUENCE {
                axConnReuseStatCpuIndex  Integer32,
                axConnReuseStatOpenPersist  Counter32,
                axConnReuseStatActivePersist  Counter32,
                axConnReuseStatTotalEst  Counter32,
                axConnReuseStatTotalTerm  Counter32,
                axConnReuseStatTotalBind  Counter32,
                axConnReuseStatTotalUNBind  Counter32,
                axConnReuseStatTotalDelayedUNBind Counter32,
                axConnReuseStatTotalLongRes     Counter32,
                axConnReuseStatTotalMissedRes Counter32
                }


        axConnReuseStatCpuIndex OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Cpu Index to the connection-reuse STAT."
                ::=  {  axConnReuseStatEntry  1  }


        axConnReuseStatOpenPersist      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of open persistent connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  2  }


        axConnReuseStatActivePersist    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of active persistent connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  3  }


        axConnReuseStatTotalEst OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of established connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  4  }


        axConnReuseStatTotalTerm        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of terminated connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  5  }


        axConnReuseStatTotalBind        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of bound connection-reuse sessions."
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  6  }


        axConnReuseStatTotalUNBind      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of unbound connection-reuse sessions"
                DEFVAL                  { 0 }
                ::=  {  axConnReuseStatEntry  7  }

        axConnReuseStatTotalDelayedUNBind OBJECT-TYPE
    SYNTAX                  Counter32
    MAX-ACCESS              read-only
    STATUS                  current
    DESCRIPTION
                        "Number of connections whose unbinding was delayed."
    DEFVAL                  { 0 }
    ::=  {  axConnReuseStatEntry  8  }


  axConnReuseStatTotalLongRes OBJECT-TYPE
    SYNTAX                  Counter32
    MAX-ACCESS              read-only
    STATUS                  current
    DESCRIPTION
                        "Number of responses that took too long."
    DEFVAL                  { 0 }
    ::=  {  axConnReuseStatEntry  9  }


  axConnReuseStatTotalMissedRes OBJECT-TYPE
    SYNTAX                  Counter32
    MAX-ACCESS              read-only
    STATUS                  current
    DESCRIPTION
                        "Number of missed responses to HTTP requests."
    DEFVAL                  { 0 }
    ::=  {  axConnReuseStatEntry  10  }

        axConnReuseStatTotalDelayedUNBound OBJECT-TYPE
    SYNTAX                  INTEGER
    MAX-ACCESS              read-only
    STATUS                  current
    DESCRIPTION
                        "Number of connections whose unbinding was delayed."
    DEFVAL                  { 0 }
    ::=  {  axConnReuseStats  8  }


   axConnReuseStatTotalLongResponse OBJECT-TYPE
     SYNTAX                  INTEGER
     MAX-ACCESS              read-only
     STATUS                  current
     DESCRIPTION
                        "Number of responses that took too long."
     DEFVAL                  { 0 }
     ::=  {  axConnReuseStats  9  }

        axConnReuseStatTotalMissedResponse OBJECT-TYPE
     SYNTAX                  INTEGER
     MAX-ACCESS              read-only
     STATUS                  current
     DESCRIPTION
                        "Number of missed responses to HTTP requests."
     DEFVAL                  { 0 }
     ::=  {  axConnReuseStats  10  }

--==================================================================
-- axFastHttpProxyStat
--==================================================================

        axFastHttpProxyStatTotalConn    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of proxy connections."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  1  }


        axFastHttpProxyStatTotalReq OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http requests."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  2  }

        axFastHttpProxyStatTotalSuccReq OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http requests which connected successful."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  3  }

        axFastHttpProxyStatTotalNoProxy OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of no proxy fail."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  4  }

        axFastHttpProxyStatTotalCRst    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http connections reset by client."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  5  }

        axFastHttpProxyStatTotalSRst    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http connections reset by server."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  6  }

        axFastHttpProxyStatTotalNoTuple OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of no tuple fail."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  7  }

        axFastHttpProxyStatTotalReqErr  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of request parse failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  8  }

        axFastHttpProxyStatTotalSvrSelErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server select failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  9  }

        axFastHttpProxyStatTotalFwdReqErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of firewall request fail."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  10  }

        axFastHttpProxyStatTotalFwdDataReqErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of firewall data request failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  11 }

        axFastHttpProxyStatTotalReqReXmit OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of retransmitted http request."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  12  }

        axFastHttpProxyStatTotalReqPktOutOrder OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of request packet out of order."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  13  }

        axFastHttpProxyStatTotalSvrReSel OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server reselect."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  14  }

        axFastHttpProxyStatTotalPreMatureClose OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server premature closed connections."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  15  }

        axFastHttpProxyStatTotalSvrConn OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server connections made."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  16  }

        axFastHttpProxyStatTotalSNATErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of Source NAT failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStats  17  }

        axFastHttpProxyStatTable        OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxFastHttpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The fast http proxy status table."
                ::=  { axFastHttpProxyStats  18 }

        axFastHttpProxyStatEntry        OBJECT-TYPE
                SYNTAX          AxFastHttpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The fast http proxy status entry."
                INDEX           {  axFastHttpProxyStatCpuIndex  }
                ::=  { axFastHttpProxyStatTable 1 }

        AxFastHttpProxyStatEntry  ::=  SEQUENCE {
                axFastHttpProxyStatCpuIndex  Integer32,
                axFastHttpProxyStatCurrProxyConns  Counter32,
                axFastHttpProxyStatTotalProxyConns  Counter32,
                axFastHttpProxyStatHttpReq  Counter32,
                axFastHttpProxyStatHttpReqSucc  Counter32,
                axFastHttpProxyStatNoProxyErr  Counter32,
                axFastHttpProxyStatClientRst  Counter32,
                axFastHttpProxyStatServerRst  Counter32,
                axFastHttpProxyStatNoTupleErr  Counter32,
                axFastHttpProxyStatParseReqFail  Counter32,
                axFastHttpProxyStatServerSelFail  Counter32,
                axFastHttpProxyStatFwdReqFail  Counter32,
                axFastHttpProxyStatFwdReqDataFail  Counter32,
                axFastHttpProxyStatReqReTran  Counter32,
                axFastHttpProxyStatReqPktOutOrder  Counter32,
                axFastHttpProxyStatServerReSel  Counter32,
                axFastHttpProxyStatServerPreMatureClose  Counter32,
                axFastHttpProxyStatServerConnMade  Counter32
                }


        axFastHttpProxyStatCpuIndex     OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The cpu index of fast http proxy STAT table"
                ::=  {  axFastHttpProxyStatEntry  1  }


        axFastHttpProxyStatCurrProxyConns       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of current fast http proxy connections"
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  2  }


        axFastHttpProxyStatTotalProxyConns      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of fast http proxy connections of current cpu."
                ::=  {  axFastHttpProxyStatEntry  3  }


        axFastHttpProxyStatHttpReq      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http requests."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  4  }


        axFastHttpProxyStatHttpReqSucc  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http reqests which connected successfully."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  5  }


        axFastHttpProxyStatNoProxyErr   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of no proxy error."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  6  }


        axFastHttpProxyStatClientRst    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http connections reset by client."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  7  }


        axFastHttpProxyStatServerRst    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http connections reset by server."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  8  }


        axFastHttpProxyStatNoTupleErr   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of no tuple fail."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  9  }


        axFastHttpProxyStatParseReqFail OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of request parse failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  10  }


        axFastHttpProxyStatServerSelFail        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server select failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  11  }


        axFastHttpProxyStatFwdReqFail   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of firewall request fail."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  12  }


        axFastHttpProxyStatFwdReqDataFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of firewall data request failed."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  13  }


        axFastHttpProxyStatReqReTran    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of retransmitted http request."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  14  }


        axFastHttpProxyStatReqPktOutOrder       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of request packet out of order."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  15  }


        axFastHttpProxyStatServerReSel  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server reselect."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  16  }


        axFastHttpProxyStatServerPreMatureClose OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server premature closed connections."
                DEFVAL                  { 0 }
                ::=  {  axFastHttpProxyStatEntry  17  }


        axFastHttpProxyStatServerConnMade       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server connections made."
                ::=  {  axFastHttpProxyStatEntry  18  }

--==================================================================
-- axHttpProxyStat
--==================================================================

        axHttpProxyStatTotalConn        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of proxy connections."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  1  }


        axHttpProxyStatTotalReq OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http requests."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  2  }

        axHttpProxyStatTotalSuccReq OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http requests which connected
successful."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  3  }

        axHttpProxyStatTotalNoProxy OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of no proxy fail."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  4  }

        axHttpProxyStatTotalCRst    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http connections reset by client."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  5  }

        axHttpProxyStatTotalSRst    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http connections reset by server."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  6  }

        axHttpProxyStatTotalNoTuple OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of no tuple fail."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  7  }

        axHttpProxyStatTotalReqErr  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of request parse failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  8  }

        axHttpProxyStatTotalSvrSelErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server select failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  9  }

        axHttpProxyStatTotalFwdReqErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of firewall request fail."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  10  }

        axHttpProxyStatTotalFwdDataReqErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of firewall data request failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  11 }

        axHttpProxyStatTotalReqReXmit OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of retransmitted http request."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  12  }

        axHttpProxyStatTotalReqPktOutOrder OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of request packet out of order."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  13  }

        axHttpProxyStatTotalSvrReSel OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server reselect."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  14  }

        axHttpProxyStatTotalPreMatureClose OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server premature closed connections."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  15  }

        axHttpProxyStatTotalSvrConn OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of server connections made."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  16  }

        axHttpProxyStatTotalSNATErr OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of Source NAT failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStats  17  }

        axHttpProxyStatTable    OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxHttpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The http proxy STAT table."
                ::=  { axHttpProxyStats  18 }

        axHttpProxyStatEntry    OBJECT-TYPE
                SYNTAX          AxHttpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The http proxy STAT entry."
                INDEX           {  axHttpProxyStatCpuIndex  }
                ::=  { axHttpProxyStatTable 1 }

        AxHttpProxyStatEntry  ::=  SEQUENCE {
                axHttpProxyStatCpuIndex  Integer32,
                axHttpProxyStatCurrProxyConns  Counter32,
                axHttpProxyStatTotalProxyConns  Counter32,
                axHttpProxyStatHttpReq  Counter32,
                axHttpProxyStatHttpReqSucc  Counter32,
                axHttpProxyStatNoProxyErr  Counter32,
                axHttpProxyStatClientRst  Counter32,
                axHttpProxyStatServerRst  Counter32,
                axHttpProxyStatNoTupleErr  Counter32,
                axHttpProxyStatParseReqFail  Counter32,
                axHttpProxyStatServerSelFail  Counter32,
                axHttpProxyStatFwdReqFail  Counter32,
                axHttpProxyStatFwdReqDataFail  Counter32,
                axHttpProxyStatReqReTran  Counter32,
                axHttpProxyStatReqPktOutOrder  Counter32,
                axHttpProxyStatServerReSel  Counter32,
                axHttpProxyStatServerPreMatureClose  Counter32,
                axHttpProxyStatServerConnMade  Counter32
                }


        axHttpProxyStatCpuIndex OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The cpu Index of http proxy STAT table."
                ::=  {  axHttpProxyStatEntry  1  }


        axHttpProxyStatCurrProxyConns   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of current http proxy connections"
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  2  }


        axHttpProxyStatTotalProxyConns  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of http proxy connections of current cpu."
                ::=  {  axHttpProxyStatEntry  3  }


        axHttpProxyStatHttpReq  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http requests."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  4  }


        axHttpProxyStatHttpReqSucc      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http reqests which connected successfully."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  5  }


        axHttpProxyStatNoProxyErr       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of no proxy error."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  6  }


        axHttpProxyStatClientRst        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http connections reset by client."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  7  }


        axHttpProxyStatServerRst        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of http connections reset by server."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  8  }


        axHttpProxyStatNoTupleErr       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of no tuple fail."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  9  }


        axHttpProxyStatParseReqFail     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of request parse failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  10  }


        axHttpProxyStatServerSelFail    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "he count of server select failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  11  }


        axHttpProxyStatFwdReqFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of firewall request fail."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  12  }


        axHttpProxyStatFwdReqDataFail   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of firewall data request failed."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  13  }


        axHttpProxyStatReqReTran        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of retransmitted http request."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  14  }


        axHttpProxyStatReqPktOutOrder   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of request packet out of order."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  15  }


        axHttpProxyStatServerReSel      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server reselect."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  16  }


        axHttpProxyStatServerPreMatureClose     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server premature closed connections."
                DEFVAL                  { 0 }
                ::=  {  axHttpProxyStatEntry  17  }


        axHttpProxyStatServerConnMade   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of server connections made."
                ::=  {  axHttpProxyStatEntry  18  }


--==================================================================
-- axTCPProxyStat
--==================================================================

        axTcpProxyStatTotalCurrEstConn  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of currently established tcp Connections."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 1  }

        axTcpProxyStatTotalActiveOpenConn  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of actively opened tcp Connections."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 2  }

        axTcpProxyStatTotalPassiveOpenConn  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of passively opened tcp connections."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 3  }

        axTcpProxyStatTotalConnAttemptFail  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of connectting attempt fails."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 4  }

        axTcpProxyStatTotalInTCPPacket  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of received tcp packets."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 5  }

        axTcpProxyStatTotalOutTCPPkt  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of sent tcp packets."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 6  }

        axTcpProxyStatTotalReXmitPkt  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of re-transmitted packets."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 7  }

        axTcpProxyStatTotalRstRcvOnEstConn  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of reset received on established connection."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 8  }

        axTcpProxyStatTotalRstSent  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of reset sent."
                DEFVAL                  { 0 }
                ::=  {  axTcpProxyStats 9  }

        axTCPProxyStatTable     OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxTCPProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The TCP proxy STAT table."
                ::=  { axTcpProxyStats  10 }

        axTCPProxyStatEntry     OBJECT-TYPE
                SYNTAX          AxTCPProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The TCP proxy STAT entry."
                INDEX           {  axTcpProxyStatCpuIndex  }
                ::=  { axTCPProxyStatTable 1 }

        AxTCPProxyStatEntry  ::=  SEQUENCE {
                axTcpProxyStatCpuIndex  Integer32,
                axTcpProxyStatCurrEstConns  Counter32,
                axTcpProxyStatActiveOpenConns  Counter32,
                axTcpProxyStatPassiveOpenConns  Counter32,
                axTcpProxyStatConnAttempFail  Counter32,
                axTcpProxyStatTotalInTCPPkt  Counter32,
                axTcpProxyStatTotalOutPkt  Counter32,
                axTcpProxyStatReTranPkt  Counter32,
                axTcpProxyStatRstRvdEstConn  Counter32,
                axTcpProxyStatRstSent  Counter32,
                axTcpProxyStatInputErr  Counter32,
                axTcpProxyStatSocketAlloc  Counter32,
                axTcpProxyStatOrphanSocket  Counter32,
                axTcpProxyStatMemAlloc  Counter32,
                axTcpProxyStatTotalRxBuf  Counter32,
                axTcpProxyStatTotalTxBuf  Counter32,
                axTcpProxyStatTCPSYNSNTState  Counter32,
                axTcpProxyStatTCPSYNRCVState  Counter32,
                axTcpProxyStatTCPFINW1State  Counter32,
                axTcpProxyStatTCPFINW2State  Counter32,
                axTcpProxyStatTimeWstate  Counter32,
                axTcpProxyStatTCPCloseState  Counter32,
                axTcpProxyStatTCPCloseWState  Counter32,
                axTcpProxyStatTCPLastACKState  Counter32,
                axTcpProxyStatTCPListenState  Counter32,
                axTcpProxyStatTCPClosingState  Counter32
                }


        axTcpProxyStatCpuIndex  OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The cpu index of TCP proxy STAT table."
                ::=  {  axTCPProxyStatEntry  1  }


        axTcpProxyStatCurrEstConns      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of currently established connections."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  2  }


        axTcpProxyStatActiveOpenConns   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of active open connections."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  3  }


        axTcpProxyStatPassiveOpenConns  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of passive open connections."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  4  }


        axTcpProxyStatConnAttempFail    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of connecting attempt fail."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  5  }


        axTcpProxyStatTotalInTCPPkt     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of packet received."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  6  }


        axTcpProxyStatTotalOutPkt       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of packets sent."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  7  }


        axTcpProxyStatReTranPkt OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of re-transmit packets."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  8  }


        axTcpProxyStatRstRvdEstConn     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of reset received on established connections."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  9  }


        axTcpProxyStatRstSent   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of Reset Sent."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  10  }


        axTcpProxyStatInputErr  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of Input Error."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  11  }


        axTcpProxyStatSocketAlloc       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of socket allocated."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  12  }


        axTcpProxyStatOrphanSocket      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of orphan sockets."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  13  }


        axTcpProxyStatMemAlloc  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The size of allocated memory used by tcp proxy."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  14  }


        axTcpProxyStatTotalRxBuf        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The size of Rx buffer."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  15  }


        axTcpProxyStatTotalTxBuf        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The size of TX buffer."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  16  }


        axTcpProxyStatTCPSYNSNTState    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in SYN-SNT state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  17  }


        axTcpProxyStatTCPSYNRCVState    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in SYN-RCV state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  18  }


        axTcpProxyStatTCPFINW1State     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in FIN-W1 state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  19  }


        axTcpProxyStatTCPFINW2State     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in FIN-W2 state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  20  }


        axTcpProxyStatTimeWstate        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in TCP TimeW state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  21  }


        axTcpProxyStatTCPCloseState     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in close state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  22  }


        axTcpProxyStatTCPCloseWState    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in closeW state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  23  }


        axTcpProxyStatTCPLastACKState   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in lastACK state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  24  }


        axTcpProxyStatTCPListenState    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in listen state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  25  }


        axTcpProxyStatTCPClosingState   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP connections in closing state."
                DEFVAL                  { 0 }
                ::=  {  axTCPProxyStatEntry  26  }

--==================================================================
-- axSslStat
--==================================================================
        axSslStatSSLModNum      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The number of SSL modules."
                DEFVAL                  { 1 }
                ::=  {  axSslStats  1  }

        axSslStatCurrSSLConn    OBJECT-TYPE
                SYNTAX                  Counter64
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Current SSL Connections."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  2  }

        axSslStatTotalSSLConn   OBJECT-TYPE
                SYNTAX                  Counter64
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total SSL connections."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  3  }

        axSslStatFailSSLHandshake       OBJECT-TYPE
                SYNTAX                  Counter64
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Failed SSL handshake."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  4  }

        axSslStatSSLMemUsage    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The SSL Memory usage(Byte)."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  5  }

        axSslStatTable  OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxSslStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The SSL STAT table."
                ::=  { axSslStats  6 }

        axSslStatEntry  OBJECT-TYPE
                SYNTAX          AxSslStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The SSL STAT entry."
                INDEX           {  axSslStatModuleIndex  }
                ::=  { axSslStatTable 1 }

        AxSslStatEntry  ::=  SEQUENCE {
                axSslStatModuleIndex  Integer32,
                axSslStatEnableCryptoEngine  Counter32,
                axSslStatAvailCryptoEngine  Counter32
                }

        axSslStatModuleIndex    OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The Module Index of SSL STAT table"
                ::=  {  axSslStatEntry  1  }

        axSslStatEnableCryptoEngine     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The number of enabled crypto engines."
                DEFVAL                  { 22 }
                ::=  {  axSslStatEntry  2  }

        axSslStatAvailCryptoEngine      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The number of available crypto engines."
                DEFVAL                  { 22 }
                ::=  {  axSslStatEntry  3  }

        axSslStatSSLFailedCAVfy OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times an SSL session was terminated due to a certificate verification failure."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  7  }

        axSslStatSSLNoHWContextMem      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times the encryption processor was unable to allocate memory."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  8  }

        axSslStatSSLHWRingFull  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times the AX software was unable to enqueue an SSL record to the SSL processor for encryption/decryption.(Number of times the processor reached its performance limit.)"
                DEFVAL                  { 0 }
                ::=  {  axSslStats  9  }

        axSslStatSSLFailedCryptoOperation       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times when the crypto opertion fails."
                DEFVAL                  { 0 }
                ::=  {  axSslStats  10  }

--==================================================================
-- axFtpStat
--==================================================================

        axFtpStatTotalCtrlSession       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of control sessions."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  1  }

        axFtpStatTotalALGPkt    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of ALG packets."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  2  }

        axFtpStatALGPktReXmit   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of ALG packets rexmitted."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  3  }

        axFtpStatOutConnCtrl    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of out of control connections."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  4  }

        axFtpStatTotalDataSession       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of data sessions."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  5  }

        axFtpStatOutConnData    OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The total count of out of data connections."
                DEFVAL                  { 0 }
                ::=  {  axFtpStats  6  }

--==================================================================
-- axNetStat
--==================================================================
        axNetStatIPOutNoRoute   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of IP out no route."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  1  }

        axNetStatTCPOutRst      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP out Reset."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  2  }

        axNetStatTCPSynRcv      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP SYN packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  3  }

        axNetStatTCPSYNCookieSent       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP SYN cookie sent."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  4  }

        axNetStatTCPSYNCookieSentFail   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The count of TCP SYN cookie sent fail."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  5  }

        axNetStatTCPReceive     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  6  }

        axNetStatUDPReceive     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of UDP packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  7  }

        axNetStatServerSelFail  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times selection of a real server failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  8  }

        axNetStatSourceNATFail  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a source NAT failure occurred."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  9  }

        axNetStatTCPSynCookieFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a TCP SYN cookie failure occurred."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  10  }

        axNetStatNoVportDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times traffic was dropped because the requested virtual port was not available."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  11  }

        axNetStatNoSynPktDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SYN packets dropped."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  12  }

        axNetStatConnLimitDrop  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the server connection limit had been reached."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  13  }

        axNetStatConnLimitReset OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of connections reset because the server connection limit had been reached."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  14  }

        axNetStatProxyNoSockDrop        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the proxy did not have an available socket."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  15  }

        axNetStataFlexDrop      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to an aFlex."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  16  }

        axNetStatSessionAgingOut        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of sessions that have aged out."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  17  }

        axNetStatTCPNoSLB       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP packets in non SLB processing."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  18  }

        axNetStatUDPNoSLB       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of UDP packets in non SLB processing."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  19  }

        axNetStatTCPOutRSTNoSYN OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST no SYN."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  20  }

        axNetStatTCPOutRSTL4Proxy       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST L4 proxy."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  21  }

        axNetStatTCPOutRSTACKattack     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST ACK attack."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  22  }

        axNetStatTCPOutRSTAFleX OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST aFlex."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  23  }

        axNetStatTCPOutRSTStaleSess     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST stale session."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  24  }

        axNetStatTCPOutRSTProxy         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of TCP out RST TCP proxy."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  25  }

        axNetStatNoSYNPktDropFIN        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of No SYN pkt drops - FIN."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  26  }

        axNetStatNoSYNPktDropRST        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of No SYN pkt drops - RST."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  27  }

        axNetStatNoSYNPktDropACK        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of No SYN pkt drops - ACK."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  28  }

        axNetStatSYNThrotte             OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SYN Throttle."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  29  }

        axNetStatSSLSIDPersistSucc              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL SID persist successful."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  30  }

        axNetStatSSLSIDPersistFail              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL SID persist failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  31  }

        axNetStatClientSSLSIDNotFound           OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID not found."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  32  }

        axNetStatClientSSLSIDMatch              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID match"
                DEFVAL                  { 0 }
                ::=  {  axNetStats  33  }

        axNetStatClientSSLSIDNotMatch           OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID not match."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  34  }

        axNetStatServerSSLSIDNotFound           OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID not found."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  35  }

        axNetStatServerSSLSIDReset              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID reset."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  36  }

        axNetStatServerSSLSIDMatch              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID match."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  37  }

        axNetStatServerSSLSIDNotMatch           OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID not match."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  38  }

        axNetStatCreateSSLSIDSucc               OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Create SSL SID successfully."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  39  }

        axNetStatCreateSSLSIDFail               OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Create SSL SID failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  40  }

        axNetStatConnRateLimitDrops     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Conn rate limit drops."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  41  }

        axNetStatConnRateLimitResets    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Conn rate limit resets."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  42  }

        axNetStatInbandHMRetry          OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Inband HM retry."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  43  }

        axNetStatInbandHMReassign       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Inband HM reassign."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  44  }


        axNetStat2TCPReceive    OBJECT-TYPE
                SYNTAX                  Counter64
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP packets received in the 64-bit counter."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  45  }

        axNetStat2UDPReceive    OBJECT-TYPE
                SYNTAX                  Counter64
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of UDP packets received in the 64-bit counter."
                DEFVAL                  { 0 }
                ::=  {  axNetStats  46  }

        axNetStatL4SynAttack    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 SYN attacks"
                DEFVAL                  { 0 }
                ::=  {  axNetStats  47  }

        axNetStatExt    OBJECT IDENTIFIER ::= { axNetStats  90 }

        axNetStatExtL2Dsr       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L2 DSR"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  1  }

        axNetStatExtL3Dsr       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L3 DSR"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  2  }

        axNetStatExtNatNoFwdRoute       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Source NAT no fwd route"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  3  }

        axNetStatExtNatNoRevRoute       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Source NAT no rev route"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  4  }

        axNetStatExtNatIcmpProcess      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Source NAT ICMP process"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  5  }

        axNetStatExtNatIcmpNoMatch      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Source NAT ICMP no match"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  6  }

        axNetStatExtAutoNatIdMismatch   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Auto NAT id mismatch"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  7  }

        axNetStatExtNoVportDrop OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of vport not matching drops"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  8  }

        axNetStatExtTcpSessionAgedOut   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP Session aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  9  }

        axNetStatExtUdpSessionAgedOut   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of UDP Session aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  10  }

        axNetStatExtOtherSessionAgedOut OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Other Session aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  11  }

        axNetStatExtAutoReselectServer  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Auto-reselect server"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  12  }

        axNetStatExtFastAgingSet        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Fast aging set"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  13  }

        axNetStatExtFastAgingReset      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Fast aging reset"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  14  }

        axNetStatExtTcpInvalidDrop      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP invalid drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  15  }

        axNetStatExtOutOfSeqAckDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Out of sequence ACK drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  16  }

        axNetStatExtTcpSynStaleSessionDrop      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SYN stale sess drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  17  }

        axNetStatExtAnomalyOutOfSeq     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Anomaly out of sequence"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  18  }

        axNetStatExtAnomalyZeroWindow   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Anomaly zero window"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  19  }

        axNetStatExtAnomalyBadContent   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Anomaly bad content"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  20  }

        axNetStatExtAnomalyPbslbDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Anomaly pbslb drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  21  }

        axNetStatExtNoResourceDrop      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of No resource drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  22  }

        axNetStatExtResetUnknownConns   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Reset unknown conn"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  23  }

        axNetStatExtRstL7OnFailover     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of RST L7 on failover "
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  24  }

        axNetStatExtTcpSynOtherFlagsDrop        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP SYN Other Flags Drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  25  }

        axNetStatExtTcpSynWithDataDrop  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP SYN With Data Drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  26  }

        axNetStatExtIgnoreMsl   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of ignore msl"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  27  }

        axNetStatExtNatPortPreserveTry  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of NAT Port Preserve Try"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  28  }

        axNetStatExtNatPortPreserveSucc OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of NAT Port Preserve Succ"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  29  }

        axNetStatExtBwLimitExceedDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of BW-Limit Exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  30  }

        axNetStatExtBwWaterMarkDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of BW-Watermark drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  31  }

        axNetStatExtL4CpsExceedDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 CPS exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  32  }

        axNetStatExtNatCpsExceedDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of NAT CPS exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  33  }

        axNetStatExtL7CpsExceedDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L7 CPS exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  34  }

        axNetStatExtSslCpsExceedDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SSL CPS exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  35  }

        axNetStatExtSslTptExceedDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SSL TPT exceed drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  36  }

        axNetStatExtSslTptWaterMarkDrop OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SSL TPT-Watermark drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  37  }

        axNetStatExtL3vConnLimitDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L3V Conn Limit Drop"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  38  }

        axNetStatExtL4ServerHandshakeFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 server handshake fail"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  39  }

        axNetStatExtL4AxReXmitSyn       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 AX re-xmit SYN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  40  }

        axNetStatExtL4RcvAckOnSyn       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv ACK on SYN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  41  }

        axNetStatExtL4RcvRstOnSyn       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv RST on SYN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  42  }

        axNetStatExtTcpNoEstSessionAgedOut      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP no-Est Sess aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  43  }

        axNetStatExtNoEstCsynRcvAgedOut OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of no-Est CSYN rcv aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  44  }

        axNetStatExtNoEstSsynSntAgedOut OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of no-Est SSYN snt aged out"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  45  }

        axNetStatExtL4RcvReXmitSyn      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rexmit SYN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  46  }

        axNetStatExtL4RcvReXmitSynDelq  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rexmit SYN (delq)"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  47  }

        axNetStatExtL4RcvReXmitSynAck   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rexmit SYN|ACK"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  48  }

        axNetStatExtL4RcvReXmitSynAckDq OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rexmit SYN|ACK DQ "
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  49  }

        axNetStatExtL4RcvFwdLastAck     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv fwd last ACK"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  50  }

        axNetStatExtL4RcvRevLastAck     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rev last ACK"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  51  }

        axNetStatExtL4RcvFwdFin OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv fwd FIN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  52  }

        axNetStatExtL4RcvFwdFinDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv fwd FIN dup"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  53  }

        axNetStatExtL4RcvFwdFinAck      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv fwd FIN|ACK"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  54  }

        axNetStatExtL4RcvRevFin OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rev FIN"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  55  }

        axNetStatExtL4RcvRevFinDup      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rev FIN dup"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  56  }

        axNetStatExtL4RcvFevFinAck      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rev FIN|ACK"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  57  }

        axNetStatExtL4RcvFwdRst OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv fwd RST"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  58  }

        axNetStatExtL4RcfRevRst OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 rcv rev RST"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  59  }

        axNetStatExtL4UdpReqsNoRsp      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP reqs no rsp"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  60  }

        axNetStatExtL4UdpReqRsps        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP req rsps"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  61  }

        axNetStatExtL4UdpReqRspNotMatch OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP req/rsp not match"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  62  }

        axNetStatExtL4UdpReqGreaterRsps OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP req > rsps"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  63  }

        axNetStatExtL4UdpRspsGreaterReqs        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP rsps > reqs"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  64  }

        axNetStatExtL4UdpReqs   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP reqs"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  65  }

        axNetStatExtL4UdpRsps   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 UDP rsps"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  66  }

        axNetStatExtL4TcpEst    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of L4 TCP Established"
                DEFVAL                  { 0 }
                ::=  {  axNetStatExt  67  }

        axNetStatTable  OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxNetStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The Net STAT table."
                ::=  { axNetStats  100 }

        axNetStatEntry  OBJECT-TYPE
                SYNTAX          AxNetStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The Net STAT entry."
                INDEX           {  axNetStatCpuIndex  }
                ::=  { axNetStatTable 1 }

        AxNetStatEntry  ::=  SEQUENCE {
                axNetStatCpuIndex  Gauge32,
                axNetStatIPOutNoRt Counter32,
                axNetStatTCPOutReset Counter32,
                axNetStatTCPSynRecv Counter32,
                axNetStatTCPSYNCookieSnt Counter32,
                axNetStatTCPSYNCookieSntFail Counter32,
                axNetStatTCPRcv Counter32,
                axNetStatUDPRcv Counter32,
                axNetStatServerSelFails Counter32,
                axNetStatSourceNATFails Counter32,
                axNetStatTCPSynCookieFails Counter32,
                axNetStatNoVportDrops Counter32,
                axNetStatNoSynPktDrops Counter32,
                axNetStatConnLimitDrops Counter32,
                axNetStatConnLimitResets Counter32,
                axNetStatProxyNoSockDrops Counter32,
                axNetStataFlexDrops Counter32,
                axNetStatSessionsAgingOut Counter32,
                axNetStatTCPsNoSLB Counter32,
                axNetStatUDPsNoSLB Counter32,

                axNetStatEntryTCPOutRSTNoSYN Counter32,
                axNetStatEntryTCPOutRSTL4Proxy Counter32,
                axNetStatEntryTCPOutRSTACKattack Counter32,
                axNetStatEntryTCPOutRSTAFleX Counter32,
                axNetStatEntryTCPOutRSTStaleSess Counter32,
                axNetStatEntryTCPOutRSTProxy Counter32,
                axNetStatEntryNoSYNPktDropFIN Counter32,
                axNetStatEntryNoSYNPktDropRST Counter32,
                axNetStatEntryNoSYNPktDropACK Counter32,
                axNetStatEntrySYNThrotte Counter32,
                axNetStatEntrySSLSIDPersistSucc Counter32,
                axNetStatEntrySSLSIDPersistFail Counter32,
                axNetStatEntryClientSSLSIDNotFound Counter32,
                axNetStatEntryClientSSLSIDMatch Counter32,
                axNetStatEntryClientSSLSIDNotMatch Counter32,
                axNetStatEntryServerSSLSIDNotFound Counter32,
                axNetStatEntryServerSSLSIDReset Counter32,
                axNetStatEntryServerSSLSIDMatch Counter32,
                axNetStatEntryServerSSLSIDNotMatch Counter32,
                axNetStatEntryCreateSSLSIDSucc Counter32,
                axNetStatEntryCreateSSLSIDFail Counter32,
                axNetStatEntryConnRateLimitDrops Counter32,
                axNetStatEntryConnRateLimitResets Counter32,
                axNetStatEntryInbandHMRetry Counter32,
                axNetStatEntryInbandHMReassign Counter32
                }

        axNetStatCpuIndex       OBJECT-TYPE
                SYNTAX                  Gauge32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The Module Index of Net STAT table"
                ::=  {  axNetStatEntry  1  }

        axNetStatIPOutNoRt      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IP packets that could not be routed."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  2  }

        axNetStatTCPOutReset    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP Resets sent."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  3  }

        axNetStatTCPSynRecv     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP SYN packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  4  }

        axNetStatTCPSYNCookieSnt        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP SYN cookies sent."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  5  }

        axNetStatTCPSYNCookieSntFail    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP SYN cookie send attempts that failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  6  }

        axNetStatTCPRcv OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  7  }

        axNetStatUDPRcv OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of UDP packets received."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  8  }

        axNetStatServerSelFails OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times selection of a real server failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  9  }

        axNetStatSourceNATFails OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a source NAT failure occurred."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  10  }

        axNetStatTCPSynCookieFails      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a TCP SYN cookie failure occurred."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  11  }

        axNetStatNoVportDrops   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times traffic was dropped because the requested virtual port was not available."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  12  }

        axNetStatNoSynPktDrops  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SYN packets dropped."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  13  }

        axNetStatConnLimitDrops OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the server connection limit had been reached."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  14  }

        axNetStatConnLimitResets        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of connections reset because the server connection limit had been reached."
                DEFVAL                  { 0 }
                ::=  { axNetStatEntry  15  }

        axNetStatProxyNoSockDrops       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the proxy did not have an available socket."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  16  }

        axNetStataFlexDrops     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to an aFlex."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  17 }

        axNetStatSessionsAgingOut       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of sessions that have aged out."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  18  }

        axNetStatTCPsNoSLB      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP packets in non SLB processing."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  19  }

        axNetStatUDPsNoSLB      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of UDP packets in non SLB processing."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  20  }

        axNetStatEntryTCPOutRSTNoSYN    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST no SYN."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  21  }

        axNetStatEntryTCPOutRSTL4Proxy  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST L4 proxy."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  22  }

        axNetStatEntryTCPOutRSTACKattack        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST ACK attack."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  23  }

        axNetStatEntryTCPOutRSTAFleX    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST aFlex."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  24  }

        axNetStatEntryTCPOutRSTStaleSess        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST stale session."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  25  }

        axNetStatEntryTCPOutRSTProxy            OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of TCP out RST TCP proxy."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  26  }

        axNetStatEntryNoSYNPktDropFIN   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of No SYN pkt drops - FIN."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  27  }

        axNetStatEntryNoSYNPktDropRST   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of No SYN pkt drops - RST."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  28  }

        axNetStatEntryNoSYNPktDropACK   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of No SYN pkt drops - ACK."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  29  }

        axNetStatEntrySYNThrotte                OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SYN Throttle."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  30  }

        axNetStatEntrySSLSIDPersistSucc         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL SID persist successful."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  31  }

        axNetStatEntrySSLSIDPersistFail         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL SID persist failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  32  }

        axNetStatEntryClientSSLSIDNotFound              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID not found."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  33  }

        axNetStatEntryClientSSLSIDMatch         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID match"
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  34  }

        axNetStatEntryClientSSLSIDNotMatch              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Client SSL SID not match."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  35  }

        axNetStatEntryServerSSLSIDNotFound              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID not found."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  36  }

        axNetStatEntryServerSSLSIDReset         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID reset."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  37  }

        axNetStatEntryServerSSLSIDMatch         OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID match."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  38  }

        axNetStatEntryServerSSLSIDNotMatch              OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Server SSL SID not match."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  39  }

        axNetStatEntryCreateSSLSIDSucc          OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Create SSL SID successfully."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  40  }

        axNetStatEntryCreateSSLSIDFail          OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of Create SSL SID failed."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  41  }

        axNetStatEntryConnRateLimitDrops        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Conn rate limit drops."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  42  }

        axNetStatEntryConnRateLimitResets       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Conn rate limit resets."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  43  }

        axNetStatEntryInbandHMRetry             OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Inband HM retry."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  44  }

        axNetStatEntryInbandHMReassign  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of Inband HM reassign."
                DEFVAL                  { 0 }
                ::=  {  axNetStatEntry  45  }

--================================================================
--  axSmtpProxyStats
--================================================================
        axSmtpProxyStatsCurrProxyConns  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of currently active SMTP connections using the AX Series device as an SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  1  }

        axSmtpProxyStatsTotalProxyConns OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SMTP connections that have used the AX Series device as an SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  2  }

        axSmtpProxyStatsSmtpRequests    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SMTP requests received by the SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  3  }

        axSmtpProxyStatsSmtpReqSuccs    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SMTP requests received by the AX Series device that were successfully fulfilled (by connection to a real server)."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  4  }

        axSmtpProxyStatsNoProxyError    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of proxy errors."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  5  }

        axSmtpProxyStatsClientRST       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times TCP connections with clients were reset."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  6  }

        axSmtpProxyStatsServerRST       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times TCP connections with servers were reset."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  7  }

        axSmtpProxyStatsNoTupleError    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of tuple errors."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  8  }

        axSmtpProxyStatsParseReqFail    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times parsing of an SMTP request failed."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  9  }

        axSmtpProxyStatsServerSelFail   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times selection of a real server failed."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  10  }

        axSmtpProxyStatsFwdReqFail      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of forward request failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  11  }

        axSmtpProxyStatsFwdReqDataFail  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of forward request data failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  12  }

        axSmtpProxyStatsReqRetrans      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of retransmitted requests."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  13  }

        axSmtpProxyStatsReqPktOutOrder  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of request packets received from clients out of sequence."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  14  }

        axSmtpProxyStatsServerResel     OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a request was forwarded to another server because the current server was failing."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  15  }

        axSmtpProxyStatsSvrPrematureClose       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times the connection with a server closed prematurely."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  16  }

        axSmtpProxyStatsSvrConnMade     OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of connections made with servers."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  17  }

        axSmtpProxyStatsSNATFail        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of source NAT failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStats  18  }

        axSmtpProxyStatTable    OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxSmtpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The Smtp proxy STAT table."
                ::=  { axSmtpProxyStats  19 }

        axSmtpProxyStatEntry OBJECT-TYPE
                SYNTAX          AxSmtpProxyStatEntry
                MAX-ACCESS      not-accessible
                STATUS  current
                DESCRIPTION
                                                                                                "The Smtp proxy STAT entry."
                INDEX           {               axSmtpProxyStatCpuIndex         }
                ::=             {       axSmtpProxyStatTable 1 }

        AxSmtpProxyStatEntry  ::=  SEQUENCE {
                axSmtpProxyStatCpuIndex  Integer32,
                axSmtpProxyStatCurrProxyConn Counter32,
                axSmtpProxyStatTotalProxyConn Counter32,
                axSmtpProxyStatSmtpReq Counter32,
                axSmtpProxyStatSmtpReqSucc Counter32,
                axSmtpProxyStatNoProxyError Counter32,
                axSmtpProxyStatClientRST Counter32,
                axSmtpProxyStatServerRST Counter32,
                axSmtpProxyStatNoTupleError Counter32,
                axSmtpProxyStatParseReqFail Counter32,
                axSmtpProxyStatServerSelFail Counter32,
                axSmtpProxyStatFwdReqFail Counter32,
                axSmtpProxyStatFwdReqDataFail Counter32,
                axSmtpProxyStatReqRetrans Counter32,
                axSmtpProxyStatReqPktOutOrder Counter32,
                axSmtpProxyStatServerResel Counter32,
                axSmtpProxyStatSvrPrematureClose Counter32,
                axSmtpProxyStatSvrConnMade Counter32,
                axSmtpProxyStatSNATFail Counter32
        }

        axSmtpProxyStatCpuIndex OBJECT-TYPE
        SYNTAX                  Integer32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                        "The Module Index of Smtp Proxy STAT table"
        ::=  {  axSmtpProxyStatEntry  1  }

        axSmtpProxyStatCurrProxyConn    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of currently active SMTP connections using the AX Series device as an SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  2  }

        axSmtpProxyStatTotalProxyConn   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SMTP connections that have used the AX Series device as an SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  3  }

        axSmtpProxyStatSmtpReq  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SMTP requests received by the SMTP proxy."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  4  }

        axSmtpProxyStatSmtpReqSucc      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SMTP requests received by the AX Series device that were successfully fulfilled (by connection to a real server)."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  5  }

        axSmtpProxyStatNoProxyError     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of proxy errors."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  6  }

        axSmtpProxyStatClientRST        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times TCP connections with clients were reset."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  7  }

        axSmtpProxyStatServerRST        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times TCP connections with servers were reset."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  8  }

        axSmtpProxyStatNoTupleError     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of tuple errors."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  9  }

        axSmtpProxyStatParseReqFail     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times parsing of an SMTP request failed."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  10  }

        axSmtpProxyStatServerSelFail    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times selection of a real server failed."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  11  }

        axSmtpProxyStatFwdReqFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of forward request failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  12  }

        axSmtpProxyStatFwdReqDataFail   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of forward request data failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  13  }

        axSmtpProxyStatReqRetrans       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of retransmitted requests."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  14  }

        axSmtpProxyStatReqPktOutOrder   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of request packets received from clients out of sequence."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  15  }

        axSmtpProxyStatServerResel      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a request was forwarded to another server because the current server was failing."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  16  }

        axSmtpProxyStatSvrPrematureClose        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "TNumber of times the connection with a server closed prematurely."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  17  }

        axSmtpProxyStatSvrConnMade      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of connections made with servers."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  18  }

        axSmtpProxyStatSNATFail OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of source NAT failures."
                DEFVAL                  { 0 }
                ::=  {  axSmtpProxyStatEntry  19  }

--================================================================
--  axSslProxyStats
--================================================================
        axSslProxyStatsCurrProxyConns   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of currently active connections using the AX device as an SSL proxy."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  1  }

        axSslProxyStatsTotalProxyConns  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of connections using the AX device as an SSL proxy."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  2  }

        axSslProxyStatsClientErr        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of client errors."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  3  }

        axSslProxyStatsServerErr        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of server errors."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  4  }

        axSslProxyStatsSessNotFound     OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times a session was not found."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  5  }

        axSslProxyStatsNoRoute  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times no route was available."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  6  }

        axSslProxyStatsSvrSelFail       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of times selection or a real server failed."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  7  }

        axSslProxyStatsSNATFail OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of occurrences of source NAT failure."
                DEFVAL                  { 0 }
                ::=  {  axSslProxyStats  8  }

--================================================================
--  axPersistentStats
--================================================================
        axPersistentStatsUrlHashPersistOKPri    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests successfully sent to the primary server selected by URL hashing. The primary server is the one that was initially selected and then re-used based on the hash value."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  1  }

        axPersistentStatsUrlHashPersistOKSec    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that were sent to another server (a secondary server) because the primary server selected by URL hashing was unavailable."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  2  }

        axPersistentStatsUrlHashPersistFail     OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that could not be fulfilled using URL hashing."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  3  }

        axPersistentStatsSIPPersistOK   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests successfully sent to the same server as previous requests from the same client, based on source-IP persistence."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  4  }

        axPersistentStatsSIPPersistFail OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that could not be fulfilled by the same server as previous requests from the same client, based on source-IP persistence."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  5  }

        axPersistentStatsSSLSIDPersistOK        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL session ID persistent success."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  6  }

        axPersistentStatsSSLSIDPersistFail      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of SSL session ID persistent failure."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  7  }

        axPersistentStatsCookiePersistOK        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of cookie persistent success."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  8  }

        axPersistentStatsCookiePersistFail      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of cookie persistent failure."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  9  }

        axPersistentStatsPersistCookieNotFound  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Total number of cookie persistent failure in not-found cases."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStats  10  }

        axPersistentStatTable   OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxPersistentStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The Persistent STAT table."
                ::=  { axPersistentStats  11 }

        axPersistentStatEntry OBJECT-TYPE
                SYNTAX          AxPersistentStatEntry
                MAX-ACCESS      not-accessible
                STATUS  current
                DESCRIPTION
                                                                                                "The Persistent STAT entry."
                INDEX           {               axPersistentStatCpuIndex                }
                ::=             {       axPersistentStatTable 1 }

        AxPersistentStatEntry  ::=  SEQUENCE {
                axPersistentStatCpuIndex  Integer32,
                axPersistentStatUrlHashPersistOKPri Counter32,
                axPersistentStatUrlHashPersistOKSec Counter32,
                axPersistentStatUrlHashPersistFail Counter32,
                axPersistentStatSIPPersistOK Counter32,
                axPersistentStatSIPPersistFail Counter32,
                axPersistentStatSSLSIDPersistOK Counter32,
                axPersistentStatSSLSIDPersistFail Counter32,
                axPersistentStatCookiePersistOK Counter32,
                axPersistentStatCookiePersistFail Counter32,
                axPersistentStatPersistCookieNotFound Counter32
        }

        axPersistentStatCpuIndex        OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The cpu index of Persistent STAT table"
                ::=  {  axPersistentStatEntry  1  }

        axPersistentStatUrlHashPersistOKPri     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests successfully sent to the primary server selected by URL hashing. The primary server is the one that was initially selected and then re-used based on the hash value."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  2  }

        axPersistentStatUrlHashPersistOKSec     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that were sent to another server (a secondary server) because the primary server selected by URL hashing was unavailable."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  3 }

        axPersistentStatUrlHashPersistFail      OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that could not be fulfilled using URL hashing."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  4  }

        axPersistentStatSIPPersistOK    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests successfully sent to the same server as previous requests from the same client, based on source-IP persistence."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  5  }

        axPersistentStatSIPPersistFail  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of requests that could not be fulfilled by the same server as previous requests from the same client, based on source-IP persistence."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  6  }

        axPersistentStatSSLSIDPersistOK OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SSL session ID persistent success."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  7  }

        axPersistentStatSSLSIDPersistFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of SSL session ID persistent failure."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  8  }

        axPersistentStatCookiePersistOK OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of cookie persistent success."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  9  }

        axPersistentStatCookiePersistFail       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of cookie persistent failure."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  10  }

        axPersistentStatPersistCookieNotFound   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of cookie persistent failure in not-found cases."
                DEFVAL                  { 0 }
                ::=  {  axPersistentStatEntry  11  }

--================================================================
--  axSwitchStats
--================================================================
        axSwitchStatsL2Forward  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been Layer 2 switched."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  1  }

        axSwitchStatsL3IPForward        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been Layer 3 routed."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  2  }

        axSwitchStatsIPv4NoRouteDrop    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv4 packets that were dropped due to routing failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  3  }

        axSwitchStatsL3IPv6Forward      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv6 packets that have been Layer 3 routed."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  4  }

        axSwitchStatsIPv6NoRouteDrop    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv6 packets that were dropped due to routing failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  5  }

        axSwitchStatsL4Process  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that went to a VIP or NAT for processing."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  6  }

        axSwitchStatsIncorrectLenDrop   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to incorrect protocol length.A high value for this counter can indicate a packet length attack."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  7  }

        axSwitchStatsProtoDownDrop      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the corresponding protocol was disabled."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  8  }

        axSwitchStatsUnknownProtoDrop   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the protocol was unknown."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  9  }

        axSwitchStatsTTLExceedDrop      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to TTL expiration."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  10  }

        axSwitchStatsLinkdownDrop       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the outgoing link was down."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  11  }

        axSwitchStatsSRCPortSuppress    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Packet drops because of source port suppression."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  12  }

        axSwitchStatsVLANFlood  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been broadcast to a VLAN."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  13  }

        axSwitchStatsIPFragRcv  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv4 fragments that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  14  }

        axSwitchStatsARPReqRcv  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of ARP requests that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  15  }

        axSwitchStatsARPRespRcv OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of ARP responses that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  16  }

        axSwitchStatsFwdKernel  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets received by the kernel from data interfaces."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  17  }

        axSwitchStatsIPTCPFragRcv       OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IP TCP fragments received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  18  }

        axSwitchStatsIPFragOverlap      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of overlapping fragments received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  19  }

        axSwitchStatsIPFragOverlapDrop  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of fragments dropped due to overload."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  20  }

        axSwitchStatsIPFragReasmOk      OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of successfully reassembled IP fragments."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  21  }

        axSwitchStatsIPFragReasmFail    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of fragment reassembly failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  22  }

        axSwitchStatsAnomLanAttackDrop  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by an IP land attack filter.This statistic and the other Anomaly statistics show how many packets were dropped by DDoS protection filters. For the AX device to drop these packets, the corresponding DDoS protection options must be enabled."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  23  }

        axSwitchStatsAnomIPOptionDrop   OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by an IP option filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  24  }

        axSwitchStatsAnomPingDeathDrop  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a ping-of-death filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  25  }

        axSwitchStatsAnomAllFragDrop    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a frag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  26  }

        axSwitchStatsAnomTCPNoFragDrop  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-no-flag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  27  }

        axSwitchStatsAnomSYNFragDrop    OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-syn-frag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  28  }

        axSwitchStatsAnomTCPSynFinDrop  OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-syn-fin filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  29  }

        axSwitchStatsAnomAnyDrop        OBJECT-TYPE
                SYNTAX                  INTEGER
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by any type of hardware-based DDoS protection filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStats  30  }

        axSwitchStatTable       OBJECT-TYPE
                SYNTAX          SEQUENCE  OF  AxSwitchStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The switch status table."
                ::=  { axSwitchStats  31 }

        axSwitchStatEntry       OBJECT-TYPE
                SYNTAX          AxSwitchStatEntry
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The Switch status entry."
                INDEX           {  axSwitchStatCpuIndex  }
                ::=  { axSwitchStatTable 1 }

        AxSwitchStatEntry  ::=  SEQUENCE {
                axSwitchStatCpuIndex  Integer32,
                axSwitchStatL2Forward  Counter32,
                axSwitchStatL3IPForward  Counter32,
                axSwitchStatIPv4NoRouteDrop  Counter32,
                axSwitchStatL3IPv6Forward  Counter32,
                axSwitchStatIPv6NoRouteDrop  Counter32,
                axSwitchStatL4Process  Counter32,
                axSwitchStatIncorrectLenDrop  Counter32,
                axSwitchStatProtoDownDrop  Counter32,
                axSwitchStatUnknownProtoDrop  Counter32,
                axSwitchStatTTLExceedDrop  Counter32,
                axSwitchStatLinkdownDrop  Counter32,
                axSwitchStatSRCPortSuppress  Counter32,
                axSwitchStatVLANFlood  Counter32,
                axSwitchStatIPFragRcv  Counter32,
                axSwitchStatARPReqRcv  Counter32,
                axSwitchStatARPRespRcv  Counter32,
                axSwitchStatFwdKernel  Counter32,
                axSwitchStatIPTCPFragRcv  Counter32,
                axSwitchStatIPFragOverlap  Counter32,
                axSwitchStatIPFragOverlapDrop  Counter32,
                axSwitchStatIPFragReasmOk  Counter32,
                axSwitchStatIPFragReasmFail  Counter32,
                axSwitchStatAnomLanAttackDrop  Counter32,
                axSwitchStatAnomIPOptionDrop  Counter32,
                axSwitchStatAnomPingDeathDrop  Counter32,
                axSwitchStatAnomAllFragDrop  Counter32,
                axSwitchStatAnomTCPNoFragDrop  Counter32,
                axSwitchStatAnomSYNFragDrop  Counter32,
                axSwitchStatAnomTCPSynFinDrop  Counter32,
                axSwitchStatAnomAnyDrop  Counter32
                }

        axSwitchStatCpuIndex    OBJECT-TYPE
                SYNTAX                  Integer32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "The cpu index of Switch STAT table"
                ::=  {  axSwitchStatEntry  1  }

        axSwitchStatL2Forward   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been Layer 2 switched."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  2  }

        axSwitchStatL3IPForward OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been Layer 3 routed."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  3  }

        axSwitchStatIPv4NoRouteDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv4 packets that were dropped due to routing failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  4  }

        axSwitchStatL3IPv6Forward       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv6 packets that have been Layer 3 routed."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  5  }

        axSwitchStatIPv6NoRouteDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv6 packets that were dropped due to routing failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  6  }

        axSwitchStatL4Process   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that went to a VIP or NAT for processing."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  7  }

        axSwitchStatIncorrectLenDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to incorrect protocol length.A high value for this counter can indicate a packet length attack."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  8  }

        axSwitchStatProtoDownDrop       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the corresponding protocol was disabled."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  9  }

        axSwitchStatUnknownProtoDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the protocol was unknown."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  10  }

        axSwitchStatTTLExceedDrop       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped due to TTL expiration."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  11  }

        axSwitchStatLinkdownDrop        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped because the outgoing link was down."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  12  }

        axSwitchStatSRCPortSuppress     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Packet drops because of source port suppression."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  13  }

        axSwitchStatVLANFlood   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets that have been broadcast to a VLAN."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  14  }

        axSwitchStatIPFragRcv   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IPv4 fragments that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  15  }

        axSwitchStatARPReqRcv   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of ARP requests that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  16  }

        axSwitchStatARPRespRcv  OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of ARP responses that have been received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  17  }

        axSwitchStatFwdKernel   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets received by the kernel from data interfaces."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  18  }

        axSwitchStatIPTCPFragRcv        OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of IP TCP fragments received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  19  }

        axSwitchStatIPFragOverlap       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of overlapping fragments received."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  20  }

        axSwitchStatIPFragOverlapDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of fragments dropped due to overload."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  21  }

        axSwitchStatIPFragReasmOk       OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of successfully reassembled IP fragments."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  22  }

        axSwitchStatIPFragReasmFail     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of fragment reassembly failures."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  23  }

        axSwitchStatAnomLanAttackDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by an IP land attack filter.This statistic and the other Anomaly statistics show how many packets were dropped by DDoS protection filters. For the AX device to drop these packets, the corresponding DDoS protection options must be enabled."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  24  }

        axSwitchStatAnomIPOptionDrop    OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by an IP option filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  25  }

        axSwitchStatAnomPingDeathDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a ping-of-death filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  26  }

        axSwitchStatAnomAllFragDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a frag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  27  }

        axSwitchStatAnomTCPNoFragDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-no-flag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  28  }

        axSwitchStatAnomSYNFragDrop     OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-syn-frag filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  29  }

        axSwitchStatAnomTCPSynFinDrop   OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by a tcp-syn-fin filter."
                DEFVAL                  { 0 }
                ::=  {  axSwitchStatEntry  30  }

        axSwitchStatAnomAnyDrop OBJECT-TYPE
                SYNTAX                  Counter32
                MAX-ACCESS              read-only
                STATUS                  current
                DESCRIPTION
                        "Number of packets dropped by any type of hardware-based DDoS protection filter."
                DEFVAL                  { 0 }
                ::=  { axSwitchStatEntry  31  }

--==================================================================
-- axHAGlobalConfig
--==================================================================

    axHAConfigEnabled OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA configuration enabled flag."
        ::= { axHAGlobalConfig 1 }

    axHAID OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Local configured HA group ID."
        ::= { axHAGlobalConfig 2 }

    axHASetID OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Local configured HA group set-id"
        ::= { axHAGlobalConfig 3 }

    axHAPreemptStatusEnabled OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enabled(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA preempt enabled flag"
        ::= { axHAGlobalConfig 4 }

    axHATimeoutInterval OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA time interval."
        ::= { axHAGlobalConfig 5 }

    axHATimeoutRetry OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA retries when time out."
        ::= { axHAGlobalConfig 6 }

    axHAARPRetry OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The ARP retries."
        ::= { axHAGlobalConfig 7 }

--==================================================================
-- axHAGroup
--==================================================================

    axHAGroupCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of valid HA groups."
        ::= { axHAGroup 1 }

    axHAGroupStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxHAGroupStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains the HA group status information."
        ::= { axHAGroup 2 }

    axHAGroupStatusEntry OBJECT-TYPE
        SYNTAX  AxHAGroupStatusEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axHAGroupStatus Table"
        INDEX {
                axHAGroupID
        }
        ::= { axHAGroupStatusTable 1 }

    AxHAGroupStatusEntry ::=
        SEQUENCE {
                axHAGroupID                  Integer32,
                axHAGroupLocalStatus         INTEGER,
                axHAGroupLocalPriority       Integer32,
                axHAGroupPeerStatus          INTEGER,
                axHAGroupPeerPriority        Integer32
        }

    axHAGroupID OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA group ID."
        ::= { axHAGroupStatusEntry 1 }

    axHAGroupLocalStatus OBJECT-TYPE
        SYNTAX INTEGER {
                standby(0),
                active(1),
                notConfigured(9)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The local status of this HA group."
        ::= { axHAGroupStatusEntry 2 }

    axHAGroupLocalPriority OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The local priority of this HA group."
        ::= { axHAGroupStatusEntry 3 }

    axHAGroupPeerStatus OBJECT-TYPE
        SYNTAX INTEGER {
                standby(0),
                active(1),
                notConfigured(9)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The peer status of this HA group."
        ::= { axHAGroupStatusEntry 4 }

    axHAGroupPeerPriority OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The peer priority of this HA group."
        ::= { axHAGroupStatusEntry 5 }

--==================================================================
-- axHAFloatingIP
--==================================================================

    axHAFloatingIPCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of HA floating-IP entries."
        ::= { axHAFloatingIP 1 }

    axHAFloatingIPTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxHAFloatingIPEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table contains the HA floating-IP information."
        ::= { axHAFloatingIP 2 }

    axHAFloatingIPEntry OBJECT-TYPE
        SYNTAX  AxHAFloatingIPEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "The index column in the axHAFloatingIP Table"
        INDEX {
                axHAFloatingIPIndex
        }
        ::= { axHAFloatingIPTable 1 }

    AxHAFloatingIPEntry ::=
        SEQUENCE {
                axHAFloatingIPIndex          INTEGER,
                axHAFloatingIPAddress        DisplayString,
                axHAFloatingIPHaGroupID      INTEGER
        }

    axHAFloatingIPIndex OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The index column."
        ::= { axHAFloatingIPEntry 1 }

    axHAFloatingIPAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "HA floating-IP address (either IPv4 or IPv6)."
        ::= { axHAFloatingIPEntry 2 }

    axHAFloatingIPHaGroupID OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The HA group ID for the HA floating-IP entry."
        ::= { axHAFloatingIPEntry 3 }

-- axIpNatStats
    axIpNatStatsGlobal            OBJECT IDENTIFIER ::= { axIpNatStats 1 }
    axIpNatStatsIntfInsideOutside OBJECT IDENTIFIER ::= { axIpNatStats 2 }
    axIpNatStatsDynamicMapping    OBJECT IDENTIFIER ::= { axIpNatStats 3 }
    axIpNatPoolStats              OBJECT IDENTIFIER ::= { axIpNatStats 100 }
        axIpNatLoggingStats           OBJECT IDENTIFIER ::= { axIpNatStats 101 }
    axIpNatLsnStats               OBJECT IDENTIFIER ::= { axIpNatStats 4 }
    axIpNatNat64Stats             OBJECT IDENTIFIER ::= { axIpNatStats 5 }
    axIpNatDsliteStats            OBJECT IDENTIFIER ::= { axIpNatStats 6 }
    axIpNatStatsDynamicMappingAclName    OBJECT IDENTIFIER ::= { axIpNatStats 19 }
    axFixedNatStats               OBJECT IDENTIFIER ::= { axIpNatStats 120 }

--==================================================================
-- axIpNatStatsGlobal
--==================================================================

    axIpNatStatsGlobalHits      OBJECT-TYPE
        SYNTAX                  CounterBasedGauge64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total hits in the IP source NAT."
        DEFVAL                  { 0 }
        ::=  {  axIpNatStatsGlobal  1  }

    axIpNatStatsGlobalMisses    OBJECT-TYPE
        SYNTAX                  CounterBasedGauge64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "Total misses in the IP source NAT"
        DEFVAL                  { 0 }
        ::=  {  axIpNatStatsGlobal  2  }

--==================================================================
-- axIpNatStatsIntfInsideOutside
--==================================================================

    axIpNatStatsIntfInsideOutsideTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxIpNatStatsIntfInsideOutsideEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of IP NAT interface inside/outside"
        ::= { axIpNatStatsIntfInsideOutside 1 }

    axIpNatStatsIntfInsideOutsideEntry OBJECT-TYPE
        SYNTAX  AxIpNatStatsIntfInsideOutsideEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axIpNatStatsIntfInsideOutside Table"
        INDEX {
                axIpNatStatsInsideOutsideIntfIndex
        }
        ::= { axIpNatStatsIntfInsideOutsideTable 1 }

    AxIpNatStatsIntfInsideOutsideEntry ::=
        SEQUENCE {
                axIpNatStatsInsideOutsideIntfIndex       INTEGER,
                axIpNatStatsInsideOutsideIntfName        DisplayString,
                axIpNatStatsInsideOutsideIntfDirection   INTEGER
        }

    axIpNatStatsInsideOutsideIntfIndex OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The index of the AxIpNatStatsIntfInsideOutside table."
        ::= { axIpNatStatsIntfInsideOutsideEntry 1 }

    axIpNatStatsInsideOutsideIntfName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The interface name, such as ethernet1, ethernet2, ve3, ..."
        ::= { axIpNatStatsIntfInsideOutsideEntry 2 }

    axIpNatStatsInsideOutsideIntfDirection OBJECT-TYPE
        SYNTAX INTEGER {
                inside(0),
                outside(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The interface bind direction, inside or outside."
        ::= { axIpNatStatsIntfInsideOutsideEntry 3 }

--==================================================================
-- axIpNatStatsDynamicMapping
--==================================================================

    axIpNatStatsDynamicMappingTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxIpNatStatsDynamicMappingEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of IP NAT interface inside/outside"
        ::= { axIpNatStatsDynamicMapping 1 }

    axIpNatStatsDynamicMappingEntry OBJECT-TYPE
        SYNTAX  AxIpNatStatsDynamicMappingEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axIpNatStatsDynamicMapping Table"
        INDEX {
                axIpNatStatsDynamicMappingAccessListID
        }
        ::= { axIpNatStatsDynamicMappingTable 1 }

    AxIpNatStatsDynamicMappingEntry ::=
        SEQUENCE {
                axIpNatStatsDynamicMappingAccessListID       INTEGER,
                axIpNatStatsDynamicMappingPoolName           DisplayString,
                axIpNatStatsDynamicMappingStartAddress       DisplayString,
                axIpNatStatsDynamicMappingEndAddress         DisplayString,
                axIpNatStatsDynamicMappingTotalAddresses     INTEGER,
                axIpNatStatsDynamicMappingAllocAddresses     INTEGER,
                axIpNatStatsDynamicMappingMissAddresses      INTEGER,
                        axIpNatStatsDynamicMappingStartAddressType   InetAddressType,
                axIpNatStatsDynamicMappingEndAddressType     InetAddressType
        }

    axIpNatStatsDynamicMappingAccessListID OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The access list id."
        ::= { axIpNatStatsDynamicMappingEntry 1 }

    axIpNatStatsDynamicMappingPoolName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP source NAT pool name"
        ::= { axIpNatStatsDynamicMappingEntry 2 }

    axIpNatStatsDynamicMappingStartAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The start adddress of the pool"
        ::= { axIpNatStatsDynamicMappingEntry 3 }

    axIpNatStatsDynamicMappingEndAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The end address of the pool"
        ::= { axIpNatStatsDynamicMappingEntry 4 }

    axIpNatStatsDynamicMappingTotalAddresses OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total addresses in the pool."
        ::= { axIpNatStatsDynamicMappingEntry 5 }

    axIpNatStatsDynamicMappingAllocAddresses OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total allocated addresses in the pool"
        ::= { axIpNatStatsDynamicMappingEntry 6 }


    axIpNatStatsDynamicMappingMissAddresses OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total misses in the pool"
        ::= { axIpNatStatsDynamicMappingEntry 7 }

    axIpNatStatsDynamicMappingStartAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axIpNatStatsDynamicMappingStartAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axIpNatStatsDynamicMappingEntry 8 }

    axIpNatStatsDynamicMappingEndAddressType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The type of axIpNatStatsDynamicMappingEndAddress:
            unknown(0), ipv4(1), ipv6(2)..."
    ::= { axIpNatStatsDynamicMappingEntry 9 }


--==================================================================
-- axIpNatPoolStats
--==================================================================

    axIpNatPoolStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxIpNatPoolStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "A table has information of IP NAT pool statistics."
        ::= { axIpNatPoolStats 1 }

    axIpNatPoolStatsEntry OBJECT-TYPE
        SYNTAX  AxIpNatPoolStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Columns in the axIpNatPoolStats Table"
        INDEX {
                axIpNatPoolName
        }
        ::= { axIpNatPoolStatsTable 1 }

    AxIpNatPoolStatsEntry ::=
        SEQUENCE {
                axIpNatPoolName          DisplayString,
                axIpNatPoolStartAddress  DisplayString,
                axIpNatPoolEndAddress    DisplayString,
                axIpNatPoolPortUsage     INTEGER,
                axIpNatPoolTotalUsed     INTEGER,
                axIpNatPoolTotalFree     INTEGER,
                axIpNatPoolFailed        INTEGER
        }

    axIpNatPoolName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP NAT pool name"
        ::= { axIpNatPoolStatsEntry 1 }

    axIpNatPoolStartAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP NAT pool start address (IPv4 or IPv6)"
        ::= { axIpNatPoolStatsEntry 2 }

    axIpNatPoolEndAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The IP NAT pool end address (IPv4 or IPv6)"
        ::= { axIpNatPoolStatsEntry 3 }

    axIpNatPoolPortUsage OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
               "The total port usage in the pool."
        ::= { axIpNatPoolStatsEntry 4 }

    axIpNatPoolTotalUsed OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total of used addresses in the pool."
        ::= { axIpNatPoolStatsEntry 5 }

    axIpNatPoolTotalFree OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total of free addresses in the pool"
        ::= { axIpNatPoolStatsEntry 6 }

    axIpNatPoolFailed OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total misses in the pool"
        ::= { axIpNatPoolStatsEntry 7 }

--==================================================================
-- axSessionStats
    axSessionStatsGlobal        OBJECT IDENTIFIER ::= { axSessionStats 1 }

--==================================================================
-- axSessionStatsGlobal
--==================================================================

    axSessionGlobalStatTCPEstablished   OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of TCP established."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  1  }

    axSessionGlobalStatTCPHalfOpen      OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of TCP half open."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  2  }

    axSessionGlobalStatUDP      OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of UDP connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  3  }

    axSessionGlobalStatNonTcpUdpIPSession       OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of non TCP/UDP IP sessions."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  4  }

    axSessionGlobalStatOther    OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of others."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  5  }

    axSessionGlobalStatReverseNATTCP    OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of reverse NAT TCP."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  6  }

    axSessionGlobalStatReverseNATUDP    OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of reverse NAT UDP."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  7  }

    axSessionGlobalStatFreeBufferCount  OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of free buffer."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  8  }

    axSessionGlobalStatFreeCurrentConns OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of free current connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  9  }

    axSessionGlobalStatConnCount        OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of current connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  10  }

    axSessionGlobalStatConnFree OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of freed connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  11  }

    axSessionGlobalStatTCPSynHalfOpen   OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of TCP SYN half open."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  12 }

    axSessionGlobalStatConnSMPAllocated OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of allocated SMP connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  13  }

    axSessionGlobalStatConnSMPFree      OBJECT-TYPE
        SYNTAX                  Gauge32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
                "The count of free SMP connections."
        DEFVAL                  { 0 }
        ::=  {  axSessionStatsGlobal  14  }

--==================================================================
-- axGslb
--==================================================================

    axGslbZones           OBJECT IDENTIFIER ::= { axGslb 1 }
    axGslbSites           OBJECT IDENTIFIER ::= { axGslb 2 }
    axGslbServiceIPs      OBJECT IDENTIFIER ::= { axGslb 3 }
    axGslbServiceIpPorts  OBJECT IDENTIFIER ::= { axGslb 4 }
    axGslbSiteSlbDevices  OBJECT IDENTIFIER ::= { axGslb 5 }
    axGslbGroups                  OBJECT IDENTIFIER ::= { axGslb 6 }

--==================================================================
-- axGslbZones
--==================================================================

    axGslbZoneCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of axGslbZoneStatsTable entries in the table."
        ::= { axGslbZones 1 }

    axGslbZoneStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbZoneStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB zones"
        ::= { axGslbZones 2 }

    axGslbZoneStatsEntry OBJECT-TYPE
        SYNTAX  AxGslbZoneStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbStatsZones Table"
        INDEX {
        axGslbZoneName
        }
        ::= { axGslbZoneStatsTable 1 }

    AxGslbZoneStatsEntry ::=
        SEQUENCE {
         axGslbZoneName                  DisplayString,
         axGslbZoneAdminState            INTEGER,
         axGslbZoneOperState             INTEGER,
         axGslbZoneReceivedQueries       Counter64,
         axGslbZoneSentResponses         Counter64,
         axGslbZoneProxyModeCount        Counter64,
         axGslbZoneCacheModeCount        Counter64,
         axGslbZoneServerModeCount       Counter64,
         axGslbZoneStickyModeCount       Counter64,
         axGslbZoneBackupModeCount       Counter64
        }

    axGslbZoneName OBJECT-TYPE
         SYNTAX DisplayString
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The name of the zone entry."
         ::= { axGslbZoneStatsEntry 1 }

    axGslbZoneAdminState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enable(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Zone administrative state."
        ::= { axGslbZoneStatsEntry 2 }

    axGslbZoneOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                unknown(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Zone operational state."
        ::= { axGslbZoneStatsEntry 3 }

    axGslbZoneReceivedQueries OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The number of received queries on the zone entry."
         ::= { axGslbZoneStatsEntry 4 }

    axGslbZoneSentResponses OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The number of sent response on the zone entry."
         ::= { axGslbZoneStatsEntry 5 }

    axGslbZoneProxyModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in proxy mode."
         ::= { axGslbZoneStatsEntry 6 }

    axGslbZoneCacheModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in cache mode."
         ::= { axGslbZoneStatsEntry 7 }

    axGslbZoneServerModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in server mode."
         ::= { axGslbZoneStatsEntry 8 }

    axGslbZoneStickyModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in sticky mode."
         ::= { axGslbZoneStatsEntry 9 }

    axGslbZoneBackupModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in backup mode."
         ::= { axGslbZoneStatsEntry 10 }

    axGslbZoneServiceStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbZoneServiceStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB zone services"
        ::= { axGslbZones 3 }

    axGslbZoneServiceStatsEntry OBJECT-TYPE
        SYNTAX  AxGslbZoneServiceStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbZoneServiceStats Table"
        INDEX {
    axGslbZoneServiceFqdn
        }
        ::= { axGslbZoneServiceStatsTable 1 }

    AxGslbZoneServiceStatsEntry ::=
        SEQUENCE {
         axGslbZoneServiceFqdn          DisplayString,
         axGslbZoneNameInServiceEntry           DisplayString,
         axGslbZoneServiceName                  DisplayString,
         axGslbZoneServicePortNum               INTEGER,
         axGslbZoneServiceAdminState            INTEGER,
         axGslbZoneServiceOperState             INTEGER,
         axGslbZoneServiceReceivedQueries       Counter64,
         axGslbZoneServiceSentResponses         Counter64,
         axGslbZoneServiceProxyModeCount        Counter64,
         axGslbZoneServiceCacheModeCount        Counter64,
         axGslbZoneServiceServerModeCount       Counter64,
         axGslbZoneServiceStickyModeCount       Counter64,
         axGslbZoneServiceBackupModeCount       Counter64
        }

    axGslbZoneServiceFqdn OBJECT-TYPE
     SYNTAX DisplayString
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
      "The Fqdn of Zone Service Entry."
     ::= { axGslbZoneServiceStatsEntry 1 }

    axGslbZoneNameInServiceEntry  OBJECT-TYPE
         SYNTAX DisplayString
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The name of the zone in service entry."
         ::= { axGslbZoneServiceStatsEntry 2 }

    axGslbZoneServiceName OBJECT-TYPE
         SYNTAX DisplayString
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The service name of the zone service entry."
         ::= { axGslbZoneServiceStatsEntry 3 }

    axGslbZoneServicePortNum OBJECT-TYPE
         SYNTAX INTEGER
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The port number of the zone service entry."
         ::= { axGslbZoneServiceStatsEntry 4 }

    axGslbZoneServiceAdminState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enable(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Zone Service administrative state."
        ::= { axGslbZoneServiceStatsEntry 5 }

    axGslbZoneServiceOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                unknown(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The Zone Service operational state."
        ::= { axGslbZoneServiceStatsEntry 6 }

    axGslbZoneServiceReceivedQueries OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The number of received queries on the zone service entry."
         ::= { axGslbZoneServiceStatsEntry 7 }

    axGslbZoneServiceSentResponses OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The number of sent response on the zone service entry."
         ::= { axGslbZoneServiceStatsEntry 8 }

    axGslbZoneServiceProxyModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in proxy mode."
         ::= { axGslbZoneServiceStatsEntry 9 }

    axGslbZoneServiceCacheModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in cache mode."
         ::= { axGslbZoneServiceStatsEntry 10 }

    axGslbZoneServiceServerModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in server mode."
         ::= { axGslbZoneServiceStatsEntry 11 }

    axGslbZoneServiceStickyModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in sticky mode."
         ::= { axGslbZoneServiceStatsEntry 12 }

    axGslbZoneServiceBackupModeCount OBJECT-TYPE
         SYNTAX Counter64
         MAX-ACCESS read-only
         STATUS current
         DESCRIPTION
          "The count in backup mode."
         ::= { axGslbZoneServiceStatsEntry 13 }

--==================================================================
-- axGslbSites
--==================================================================

    axGslbSiteCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The total number of sites."
        ::= { axGslbSites 1 }

    axGslbSiteStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbSiteStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB sites"
        ::= { axGslbSites 2 }

    axGslbSiteStatsEntry OBJECT-TYPE
        SYNTAX  AxGslbSiteStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbSiteStats Table"
        INDEX {
        axGslbSiteName
        }
        ::= { axGslbSiteStatsTable 1 }

    AxGslbSiteStatsEntry ::=
        SEQUENCE {
         axGslbSiteName              DisplayString,
         axGslbSiteAdminState        INTEGER,
         axGslbSiteOperState         INTEGER,
         axGslbSiteHitCount          Counter64
        }

    axGslbSiteName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The name of the site entry."
        ::= { axGslbSiteStatsEntry 1 }

    axGslbSiteAdminState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enable(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The site administrative state."
        ::= { axGslbSiteStatsEntry 2 }

    axGslbSiteOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2),
                unknown(3)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The site operational state."
        ::= { axGslbSiteStatsEntry 3 }

    axGslbSiteHitCount OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The hit count of the site."
        ::= { axGslbSiteStatsEntry 4 }

    axGslbSiteDeviceStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbSiteDeviceStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB site devices"
        ::= { axGslbSites 3 }

    axGslbSiteDeviceStatsEntry OBJECT-TYPE
        SYNTAX  AxGslbSiteDeviceStatsEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbSiteDevicesStats Table"
        INDEX {
        axGslbSiteNameInDeviceEntry, axGslbSiteSlbDeviceIpAddr, axGslbSiteServiceIpAddr, axGslbSiteServiceIpPortNum
        }
        ::= { axGslbSiteDeviceStatsTable 1 }

    AxGslbSiteDeviceStatsEntry ::=
        SEQUENCE {
         axGslbSiteNameInDeviceEntry          DisplayString,
         axGslbSiteSlbDeviceIpAddr            DisplayString,
         axGslbSiteServiceIpAddr              DisplayString,
         axGslbSiteServiceIpPortNum           INTEGER,
         axGslbSiteSlbDeviceName              DisplayString,
         axGslbSiteServiceIpName              DisplayString,
         axGslbSiteServiceIpAdminState        INTEGER,
         axGslbSiteServiceIpOperState         INTEGER,
         axGslbSiteServiceIpHitCount          Counter64
        }

    axGslbSiteNameInDeviceEntry  OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The name of the site."
        ::= { axGslbSiteDeviceStatsEntry 1 }

    axGslbSiteSlbDeviceIpAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The IP address of the SLB device in the site entry."
        ::= { axGslbSiteDeviceStatsEntry 2 }

    axGslbSiteServiceIpAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The IP address of the service-ip in the site device entry."
        ::= { axGslbSiteDeviceStatsEntry 3 }

    axGslbSiteServiceIpPortNum OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The port number of the service-ip in the site device entry."
        ::= { axGslbSiteDeviceStatsEntry 4 }

    axGslbSiteSlbDeviceName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The SLB device name in the site device entry."
        ::= { axGslbSiteDeviceStatsEntry 5 }

    axGslbSiteServiceIpName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The service-ip name in the site device entry."
        ::= { axGslbSiteDeviceStatsEntry 6 }

    axGslbSiteServiceIpAdminState OBJECT-TYPE
        SYNTAX INTEGER {
                disabled(0),
                enable(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service-ip administrative state."
        ::= { axGslbSiteDeviceStatsEntry 7 }

    axGslbSiteServiceIpOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service-ip operational state."

        ::= { axGslbSiteDeviceStatsEntry 8 }

    axGslbSiteServiceIpHitCount OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The hit count of the service-ip name in the site device entry."
        ::= { axGslbSiteDeviceStatsEntry 9 }

--==================================================================
-- axGslbServiceIPs
--==================================================================

    axGslbServiceIPCount OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The number of axGslbServiceIPTable entries in the table."
        ::= { axGslbServiceIPs 1 }

    axGslbServiceIPTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbServiceIPEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB service IPs"
        ::= { axGslbServiceIPs 2 }

    axGslbServiceIPEntry OBJECT-TYPE
        SYNTAX  AxGslbServiceIPEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbServiceIPs Table"
        INDEX {
           axGslbServiceIpAddr
        }
        ::= { axGslbServiceIPTable 1 }

    AxGslbServiceIPEntry ::=
        SEQUENCE {
         axGslbServiceIpAddr                  DisplayString,
         axGslbServiceIpName                  DisplayString,
         axGslbServiceIpSiteName              DisplayString,
         axGslbServiceIpAdminState            INTEGER,
         axGslbServiceIpOperState             INTEGER,
         axGslbServiceIpIsVirtualServerFlag   INTEGER,
         axGslbServiceIpProtocolFlag          INTEGER,
         axGslbServiceIpServicePortCount      Counter32,
         axGslbServiceIpHitCount              Counter64
        }

    axGslbServiceIpAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The IP address of the service-ip entry."
        ::= { axGslbServiceIPEntry 1 }

    axGslbServiceIpName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The name of the service-ip entry."
        ::= { axGslbServiceIPEntry 2 }

    axGslbServiceIpSiteName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The site name has the service-ip entry."
        ::= { axGslbServiceIPEntry 3 }

    axGslbServiceIpAdminState OBJECT-TYPE
        SYNTAX INTEGER {
           disabled(0),
           enable(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service-ip administrative state."
        ::= { axGslbServiceIPEntry 4 }

    axGslbServiceIpOperState OBJECT-TYPE
        SYNTAX INTEGER {
           up(1),
           down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The service-ip operational state."
        ::= { axGslbServiceIPEntry 5 }

    axGslbServiceIpIsVirtualServerFlag OBJECT-TYPE
        SYNTAX INTEGER {
          isVirtualServer(1),
          other(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The flag of virtual server."
        ::= { axGslbServiceIPEntry 6 }

    axGslbServiceIpProtocolFlag OBJECT-TYPE
        SYNTAX INTEGER {
          gslbProtocol(1),
          localProtocol(2),
          unknown(0)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The service-ip with the GSLB protocol or local protocol."
        ::= { axGslbServiceIPEntry 7 }

    axGslbServiceIpServicePortCount OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The number of ports for the service-ip entry."
        ::= { axGslbServiceIPEntry 8 }

    axGslbServiceIpHitCount OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The hit count of the service-ip entry."
        ::= { axGslbServiceIPEntry 9 }

--==================================================================
-- axGslbServiceIpPorts
--==================================================================

    axGslbServiceIpPortTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbServiceIpPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB service ports"
        ::= { axGslbServiceIpPorts 1 }

    axGslbServiceIpPortEntry OBJECT-TYPE
        SYNTAX  AxGslbServiceIpPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbServicePorts Table"
        INDEX {
        axGslbServiceIpPortAddr, axGslbServiceIpPortNum
        }
        ::= { axGslbServiceIpPortTable 1 }

    AxGslbServiceIpPortEntry ::=
        SEQUENCE {
         axGslbServiceIpPortAddr                  DisplayString,
         axGslbServiceIpPortNum                   INTEGER,
         axGslbServiceIpPortOperState             INTEGER,
         axGslbServiceIpPortProtocolFlag          INTEGER,
         axGslbServiceIpPortActiveServerCount     Counter32,
         axGslbServiceIpPortCurrConns             Counter64
        }

    axGslbServiceIpPortAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The IP address of the service-ip entry."
        ::= { axGslbServiceIpPortEntry 1 }

    axGslbServiceIpPortNum OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The port number of the service-ip port entry"
        ::= { axGslbServiceIpPortEntry 2 }

    axGslbServiceIpPortOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The service-ip port operational state."
        ::= { axGslbServiceIpPortEntry 3}

    axGslbServiceIpPortProtocolFlag OBJECT-TYPE
        SYNTAX INTEGER {
                gslbProtocol(1),
                localProtocol(2),
                unknown(0)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The service-ip port with the GSLB protocol or local protocol."
        ::= { axGslbServiceIpPortEntry 4 }

    axGslbServiceIpPortActiveServerCount OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The number of the active real servers."
        ::= { axGslbServiceIpPortEntry 5 }

    axGslbServiceIpPortCurrConns OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The number of the current connections."
        ::= { axGslbServiceIpPortEntry 6 }

--==================================================================
-- axGslbSiteSlbDevices
--==================================================================

    axGslbSiteSlbDeviceTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbSiteSlbDeviceEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information of GSLB service ports"
        ::= { axGslbSiteSlbDevices 1 }

    axGslbSiteSlbDeviceEntry OBJECT-TYPE
        SYNTAX  AxGslbSiteSlbDeviceEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbSiteSlbDevices Table"
        INDEX {
        axGslbSiteSlbDeviceSiteName, axGslbSiteSlbForDeviceIpAddr
        }
        ::= { axGslbSiteSlbDeviceTable 1 }

    AxGslbSiteSlbDeviceEntry ::=
        SEQUENCE {
         axGslbSiteSlbDeviceSiteName              DisplayString,
         axGslbSiteSlbForDeviceIpAddr             DisplayString,
         axGslbSiteForSlbDeviceName               DisplayString,
         axGslbSiteSlbDeviceProtocolFlag          INTEGER,
         axGslbSiteSlbDeviceAdminPreference       INTEGER,
         axGslbSiteSlbDeviceSessionUtilization    INTEGER,
         axGslbSiteSlbDeviceAvailSessionCount     Counter32,
         axGslbSiteSlbDeviceServicIpCount         Counter32
        }

    axGslbSiteSlbDeviceSiteName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The site name of the slb-device entry."
        ::= { axGslbSiteSlbDeviceEntry 1 }

    axGslbSiteSlbForDeviceIpAddr OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The IP address of the slb-device entry."
        ::= { axGslbSiteSlbDeviceEntry 2 }

    axGslbSiteForSlbDeviceName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The name of the slb-device entry."
        ::= { axGslbSiteSlbDeviceEntry 3 }

    axGslbSiteSlbDeviceProtocolFlag OBJECT-TYPE
        SYNTAX INTEGER {
                gslbProtocol(1),
                localProtocol(2),
                unknown(0)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The SLB device with the GSLB protocol or local protocol."
        ::= { axGslbSiteSlbDeviceEntry 4 }

    axGslbSiteSlbDeviceAdminPreference OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The administrative preference of the SLB device entry."
        ::= { axGslbSiteSlbDeviceEntry 5 }

    axGslbSiteSlbDeviceSessionUtilization OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The session utilization of the SLB device entry in percentage."
        ::= { axGslbSiteSlbDeviceEntry 6 }

    axGslbSiteSlbDeviceAvailSessionCount OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The number of the available sessions in the SLB device entry."
        ::= { axGslbSiteSlbDeviceEntry 7 }

    axGslbSiteSlbDeviceServicIpCount OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The number of the service-ip entries."
        ::= { axGslbSiteSlbDeviceEntry 8 }

--==================================================================
-- axGslbGroups
--==================================================================

    axGslbGroupTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AxGslbGroupEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A table has information on GSLB groups."
        ::= { axGslbGroups 1 }

    axGslbGroupEntry OBJECT-TYPE
        SYNTAX  AxGslbGroupEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Columns in the axGslbGroup Table"
        INDEX {
        axGslbGroupName, axGslbGroupMember, axGslbGroupAddress
        }
        ::= { axGslbGroupTable 1 }

    AxGslbGroupEntry ::=
        SEQUENCE {
         axGslbGroupName                  DisplayString,
                 axGslbGroupMember                                DisplayString,
         axGslbGroupSysID                 DisplayString,
         axGslbGroupPriority              INTEGER,
         axGslbGroupAttribute             BITS,
         axGslbGroupStatus                            INTEGER,
         axGslbGroupAddress               DisplayString
        }

    axGslbGroupName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The name of the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 1 }

    axGslbGroupMember OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The member of the group in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 2 }

    axGslbGroupSysID OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The sys id of the member in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 3 }

    axGslbGroupPriority OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The priority of the member in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 4 }

    axGslbGroupAttribute OBJECT-TYPE
        SYNTAX BITS {
                master(1),
                disabled(2),
                learn(3),
                passive(4),
                bridge(5),
                super(6)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The attribute of the member in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 5 }

    axGslbGroupStatus OBJECT-TYPE
        SYNTAX INTEGER {
                ok(1),
                idle(2),
                connect(3),
                active(4),
                openSent(5),
                openConfirm(6),
                established(7),
                unknown(8),
                ready(9),
            masterSync(10),
                fullSync(11),
                synced(12),
                stopped(13),
                waitSync(14),
                vcs(15),
                ha(16),
                auto(17)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The status of the member in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 6 }

    axGslbGroupAddress OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The ip address of the member in the axGslbGroupTable entry."
        ::= { axGslbGroupEntry 7 }

END
