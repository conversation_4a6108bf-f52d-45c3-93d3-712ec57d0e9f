--
-- Eltex Enterprise Specific MIB: ltp8x MIB
--
-- Copyright (c) 2009, Eltex Co
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

ELTEX-LTP8X DEFINITIONS ::= BEGIN

IMPORTS
	DisplayString,
	TimeInterval,
	TimeStamp,
	MacAddress,
	TruthValue,
	RowStatus,
	TEXTUAL-CONVENTION
		FROM SNMPv2-TC
	elHardware,
	mcTrapExState,
	mcTrapLParam1,
	mcTrapLParam2,
	mcTrapLParam3,
	mcTrapID,
	mcTrapDescr
		FROM ELTEX-SMI-ACTUAL
	PortList
		FROM Q-BRIDGE-MIB
	MODULE-IDENTITY,
	OBJECT-TYPE,
	NOTIFICATION-TYPE,
	Integer32,
	Ip<PERSON>ddress,
	Unsigned32,
	Counter64,
	TimeTicks
		FROM SNMPv2-SMI
	Ipv6Address
		FROM IPV6-TC
	OBJECT-GROUP,
	NOTIFICATION-GROUP
		FROM SNMPv2-CONF;

ltp8x MODULE-IDENTITY
	LAST-UPDATED "201007210000Z"
	ORGANIZATION "Eltex Co"
	CONTACT-INFO
		"<EMAIL>"
	DESCRIPTION
		"LTP8X MIB"
	REVISION "201007210000Z"
	DESCRIPTION
		"First revision"
	::= { elHardware 22 }
	
ONTSerial ::= TEXTUAL-CONVENTION
	DISPLAY-HINT "1x:"
	STATUS		current
	DESCRIPTION " "	
	SYNTAX		OCTET STRING (SIZE (8))
	
LTPONTState ::= TEXTUAL-CONVENTION
	STATUS		current
	DESCRIPTION " "
	SYNTAX INTEGER  {
		free(0),
		allocated(1),
		authInProgress(2),
		authFailed(3),
		authOk(4),
		cfgInProgress(5),
		cfgFailed(6),
		ok(7),
		failed(8),
		blocked(9),
		mibreset(10),
		preconfig(11),
		fwUpdating(12),
		unactivated(13),
		redundant(14),
		disabled(15),
		unknown(16)
		}
		
DBAServiceClass ::= TEXTUAL-CONVENTION
	STATUS 		current
	DESCRIPTION " "
	SYNTAX INTEGER {
		dbaData(0),
		dbaVoIP(1),
		dbaTDMCBR(2)
	}

DBAStatusReport ::= TEXTUAL-CONVENTION
	STATUS		current
	DESCRIPTION " "
	SYNTAX INTEGER {
		statusReportNSR(0),
		statusReportType0(1),
		statusReportType1(2)
	}
	
AddressEntryType ::= TEXTUAL-CONVENTION
	STATUS 		current
	DESCRIPTION " "
	SYNTAX INTEGER {
		dynamic(0),
		static(1),
		dynamicAndStatic(2)
	}
	
VideoRxPowerConv ::= TEXTUAL-CONVENTION
	STATUS		current
	DESCRIPTION " "
	SYNTAX INTEGER {
		noVideoSignal(32767)
	}
	
ltp4x OBJECT IDENTIFIER ::= { elHardware 70 }

ltp8xPONChannels OBJECT IDENTIFIER 	::= { ltp8x 2 }

		ltp8xPONChannelStateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xPONChannelStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xPONChannels 1 }
		
		ltp8xPONChannelStateEntry OBJECT-TYPE
			SYNTAX                Ltp8xPONChannelStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xPONChannelSlot, ltp8xPONChannelID }
			::= { ltp8xPONChannelStateTable 1 }
		
		Ltp8xPONChannelStateEntry ::= SEQUENCE {
				ltp8xPONChannelSlot				Unsigned32,
				ltp8xPONChannelID				Unsigned32,
				ltp8xPONChannelState			Unsigned32,
				ltp8xPONChannelONTCount			Unsigned32,
				ltp8xPONChannelEnabled			TruthValue,
				ltp8xPONChannelSFPVendor		DisplayString,
				ltp8xPONChannelSFPProductNumber	DisplayString,
				ltp8xPONChannelSFPRevision		DisplayString,
				ltp8xPONChannelTxPower			Integer32,
				ltp8xPONChannelSFPTemperature	Integer32,
				ltp8xPONChannelSFPVoltage		Integer32,
				ltp8xPONChannelSFPTxBiasCurrent	Integer32,
				ltp8xPONChannelReconfigure		Unsigned32,
				ltp8xPONChannelResetCounters	Unsigned32
		}
		
					ltp8xPONChannelSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xPONChannelStateEntry 1 }
					
					ltp8xPONChannelID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xPONChannelStateEntry 2 }
					
					ltp8xPONChannelState OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Possible values:
							free(0),
							inited(1),
							cfgInProgress(2),
							cfgFailed(3),
							ok(4),
							failed(5),
							redundant(6),
							disabled(7),
							unknown(8)"
						::= { ltp8xPONChannelStateEntry 3 }
						
					ltp8xPONChannelONTCount OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xPONChannelStateEntry 4 }
						
					ltp8xPONChannelEnabled OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "This variable only makes sense in MA4000. In LTP-8X - use ifTable."
						::= { ltp8xPONChannelStateEntry 5 }
						
					ltp8xPONChannelSFPVendor OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xPONChannelStateEntry 6 }
						
					ltp8xPONChannelSFPProductNumber OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xPONChannelStateEntry 7 }
						
					ltp8xPONChannelSFPRevision OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xPONChannelStateEntry 8 }
						
					ltp8xPONChannelTxPower OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in dBm * 1000"
						::= { ltp8xPONChannelStateEntry 9 }
						
					ltp8xPONChannelSFPTemperature OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in deg. C"
						::= { ltp8xPONChannelStateEntry 10 }
						
					ltp8xPONChannelSFPVoltage OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in uV"
						::= { ltp8xPONChannelStateEntry 11 }
						
					ltp8xPONChannelSFPTxBiasCurrent OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in uA"
						::= { ltp8xPONChannelStateEntry 12 }
						
					ltp8xPONChannelReconfigure OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set this to 1 to reconfigure OLT channel"
						::= { ltp8xPONChannelStateEntry 20 }
						
					ltp8xPONChannelResetCounters OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set this to 1 to reset channel counters"
						::= { ltp8xPONChannelStateEntry 21 }
						
		ltp8xPONChannelActModeTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xPONChannelActModeEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xPONChannels 2 }

			ltp8xPONChannelActModeEntry OBJECT-TYPE
				SYNTAX                Ltp8xPONChannelActModeEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xPONChannelActModeSlot, ltp8xPONChannelActModeChannel }
				::= { ltp8xPONChannelActModeTable 1 }

			Ltp8xPONChannelActModeEntry ::= SEQUENCE {
					ltp8xPONChannelActModeSlot				Unsigned32,
					ltp8xPONChannelActModeChannel			Unsigned32,
					ltp8xPONChannelActModeHostControlledLumpedSN	TruthValue
			}
						
						ltp8xPONChannelActModeSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPONChannelActModeEntry 1 }
						
						ltp8xPONChannelActModeChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPONChannelActModeEntry 2 }
						
						ltp8xPONChannelActModeHostControlledLumpedSN OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPONChannelActModeEntry 3 }
							
		ltp8xPONChannelAddressTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xPONChannelAddressEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xPONChannels 3 }
			
			ltp8xPONChannelAddressEntry OBJECT-TYPE
				SYNTAX                Ltp8xPONChannelAddressEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xPONChannelAddressSlot,
								ltp8xPONChannelAddressChannel,
								ltp8xPONChannelAddressEntryID }
				::= { ltp8xPONChannelAddressTable 1 }
			
			Ltp8xPONChannelAddressEntry ::= SEQUENCE {
					ltp8xPONChannelAddressSlot					Unsigned32,
					ltp8xPONChannelAddressChannel				Unsigned32,
					ltp8xPONChannelAddressEntryID				Unsigned32,
					ltp8xPONChannelAddressONTSerial				ONTSerial,
					ltp8xPONChannelAddressONTID					Unsigned32,
					ltp8xPONChannelAddressPriority				Integer32,
					ltp8xPONChannelAddressCVID					Integer32,
					ltp8xPONChannelAddressSVID					Integer32,
					ltp8xPONChannelAddressMacAddress			MacAddress,
					ltp8xPONChannelAddressCPUDestined			TruthValue,
					ltp8xPONChannelAddressDatapathForwarding	TruthValue,
					ltp8xPONChannelAddressUniPort				Unsigned32,
					ltp8xPONChannelAddressEntryType				AddressEntryType,
					ltp8xPONChannelAddressAge					Unsigned32,
					ltp8xPONChannelAddressGEMPortId				Unsigned32,
					ltp8xPONChannelAddressUVID					Integer32
			}
			
				
						ltp8xPONChannelAddressSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xPONChannelAddressEntry 1 }
							
						ltp8xPONChannelAddressChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xPONChannelAddressEntry 2 }
							
						ltp8xPONChannelAddressEntryID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xPONChannelAddressEntry 3 }
							
						ltp8xPONChannelAddressONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xPONChannelAddressEntry 4 }
							
						ltp8xPONChannelAddressONTID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xPONChannelAddressEntry 5 }
							
						ltp8xPONChannelAddressPriority		OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Priority of the packets"
							::= { ltp8xPONChannelAddressEntry 6 }	
							
						ltp8xPONChannelAddressCVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "C-VLAN ID of the packet"
							::= { ltp8xPONChannelAddressEntry 7 }
							
						ltp8xPONChannelAddressSVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "S-VLAN ID of the packet"
							::= { ltp8xPONChannelAddressEntry 8 }
							
						ltp8xPONChannelAddressMacAddress OBJECT-TYPE
							SYNTAX  MacAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "MAC address"
							::= { ltp8xPONChannelAddressEntry 9 }
							
						ltp8xPONChannelAddressCPUDestined OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Whether packet should be forwarded to CPU"
							::= { ltp8xPONChannelAddressEntry 10 }
							
						ltp8xPONChannelAddressDatapathForwarding OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Whether packet should be forwarded to Datapath"
							::= { ltp8xPONChannelAddressEntry 11 }
							
						ltp8xPONChannelAddressUniPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPONChannelAddressEntry 12 }
							
						ltp8xPONChannelAddressEntryType OBJECT-TYPE
							SYNTAX  AddressEntryType
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPONChannelAddressEntry 13 }
							
						ltp8xPONChannelAddressAge OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "The age of the entry"
							::= { ltp8xPONChannelAddressEntry 14 }
							
						ltp8xPONChannelAddressGEMPortId OBJECT-TYPE		
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "GEM Port-ID from which packet has come"
							::= { ltp8xPONChannelAddressEntry 15 }
							
						ltp8xPONChannelAddressUVID OBJECT-TYPE		
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "U-VLAN ID of the packet"
							::= { ltp8xPONChannelAddressEntry 16 }	

ltp8xONT OBJECT IDENTIFIER 	::= { ltp8x 3 }

		ltp8xONTStateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 1 }
		
		ltp8xONTStateEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTSlot, ltp8xONTSerial }
			::= { ltp8xONTStateTable 1 }
		
		Ltp8xONTStateEntry ::= SEQUENCE {
				ltp8xONTSlot					Unsigned32,
				ltp8xONTSerial					ONTSerial,
				ltp8xONTStateChannel			Unsigned32,
				ltp8xONTStateID 				Unsigned32,
				ltp8xONTStateState 				LTPONTState,
				ltp8xONTStateEqualizationDelay 	Unsigned32,
				ltp8xONTStateFecState			TruthValue,
				ltp8xONTStateEncryptionKey		DisplayString,
				ltp8xONTStateOMCIPortId			Integer32,
				ltp8xONTStateDistance			Unsigned32,
				ltp8xONTStateRSSI				Integer32,
				ltp8xONTStateEquipmentID		DisplayString,
				ltp8xONTStateTxPower			Integer32,
				ltp8xONTStateRxPower			Integer32,
				ltp8xONTStateTemperature		Integer32,
				ltp8xONTStateVideoRxPower		Integer32,
				ltp8xONTStateVersion			DisplayString,
				ltp8xONTStateHWVersion			DisplayString,
				ltp8xONTStateReconfigure		Unsigned32,
				ltp8xONTStateUpdateFirmware		Unsigned32,
				ltp8xONTStateReset				Unsigned32,
				ltp8xONTStateResetToDefaults	Unsigned32,
				ltp8xONTStateRFPortOn			TruthValue,
				ltp8xONTStateLaserVoltage		INTEGER,
				ltp8xONTStateLaserBiasCurrent	Unsigned32
		}
		
					ltp8xONTSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTStateEntry 1 }
					
					ltp8xONTSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 2 }
					
					ltp8xONTStateChannel OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 3 }
					
					ltp8xONTStateID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 4 }				
						
					ltp8xONTStateState OBJECT-TYPE
						SYNTAX  LTPONTState
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 5 }
		
					ltp8xONTStateEqualizationDelay OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 6 }
		
					ltp8xONTStateFecState OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 7 }
						
					ltp8xONTStateEncryptionKey OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 8 }
						
					ltp8xONTStateOMCIPortId OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 9 }
						
					ltp8xONTStateDistance OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Measured in meters."
						::= { ltp8xONTStateEntry 10 }

					ltp8xONTStateRSSI OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Received signal strength indication. Measured in 0.1dbm. 
						Value 65535 - means that RSSI value is not available (not supported by SFP)"
						::= { ltp8xONTStateEntry 11 }
						
					ltp8xONTStateEquipmentID OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTStateEntry 12 }
						
					ltp8xONTStateTxPower OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in dBm * 1000 (only for NTP-2C)"
						::= { ltp8xONTStateEntry 13 }
						
					ltp8xONTStateRxPower OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in dBm * 1000 (only for NTP-2C)"
						::= { ltp8xONTStateEntry 14 }
						
					ltp8xONTStateTemperature OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in celsius * 10 (only for NTP-2C)"
						::= { ltp8xONTStateEntry 15 }
						
					ltp8xONTStateVideoRxPower OBJECT-TYPE
						SYNTAX  VideoRxPowerConv
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "in dBm * 1000 (only for NTP-2C)"
						::= { ltp8xONTStateEntry 16 }
						
					ltp8xONTStateVersion OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 17 }
						
					ltp8xONTStateHWVersion OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 18 }
						
					ltp8xONTStateReconfigure OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Always = 0. Set this to 1 to reconfigure ONT"
						::= { ltp8xONTStateEntry 20 }
						
					ltp8xONTStateUpdateFirmware OBJECT-TYPE
						SYNTAX	Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Always = 0. Set this to 1 to update ONT firmware"
						::= { ltp8xONTStateEntry 21 }
						
					ltp8xONTStateReset OBJECT-TYPE
						SYNTAX	Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set to 1 to reboot ONT"
						::= { ltp8xONTStateEntry 22 }
						
					ltp8xONTStateResetToDefaults OBJECT-TYPE
						SYNTAX	Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set to 1 to reset ONT to defaults via OMCI"
						::= { ltp8xONTStateEntry 23 }
						
					ltp8xONTStateRFPortOn OBJECT-TYPE
						SYNTAX	TruthValue
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 24 }
						
					ltp8xONTStateLaserVoltage OBJECT-TYPE
						SYNTAX	INTEGER
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 25 }
						
					ltp8xONTStateLaserBiasCurrent OBJECT-TYPE
						SYNTAX	Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStateEntry 26 }
						
	ltp8xONTUNIPortsStateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTUNIPortsStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 2 }
		
		ltp8xONTUNIPortsStateEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTUNIPortsStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTUNIPortsStateSlot, ltp8xONTUNIPortsStateSerial, ltp8xONTUNIPortsStatePort }
			::= { ltp8xONTUNIPortsStateTable 1 }
		
		Ltp8xONTUNIPortsStateEntry ::= SEQUENCE {
				ltp8xONTUNIPortsStateSlot					Unsigned32,
				ltp8xONTUNIPortsStateSerial					ONTSerial,
				ltp8xONTUNIPortsStatePort					Unsigned32,
				ltp8xONTUNIPortsStateAvailable				TruthValue,
				ltp8xONTUNIPortsStateLinkUp					TruthValue,
				ltp8xONTUNIPortsStateSpeed					INTEGER,
				ltp8xONTUNIPortsStateDuplex					INTEGER
		}
		
					ltp8xONTUNIPortsStateSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTUNIPortsStateEntry 1 }
					
					ltp8xONTUNIPortsStateSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTUNIPortsStateEntry 2 }
					
					ltp8xONTUNIPortsStatePort OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTUNIPortsStateEntry 3 }
					
					ltp8xONTUNIPortsStateAvailable OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Shows if info on this port is available. If not - next 3 fields must be ignored"
						::= { ltp8xONTUNIPortsStateEntry 4 }				
						
					ltp8xONTUNIPortsStateLinkUp OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTUNIPortsStateEntry 5 }
		
					ltp8xONTUNIPortsStateSpeed OBJECT-TYPE
						SYNTAX  INTEGER {
								speedAuto(0),
								speed10M(1),
								speed100M(2),
								speed1G(3),
								speedNotAvailable(4) }
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTUNIPortsStateEntry 6 }
						
					ltp8xONTUNIPortsStateDuplex OBJECT-TYPE
						SYNTAX  INTEGER {
								duplexAuto(0),
								duplexFull(1),
								duplexHalf(2),
								duplexNotAvaiable(3) }
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTUNIPortsStateEntry 7 }
						
	ltp8xONTStatistics OBJECT IDENTIFIER 	::= { ltp8xONT 3 }
	
			ltp8xONTWANCountersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTWANCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 1 }
			
			ltp8xONTWANCountersEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTWANCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTWANCountersSlot, ltp8xONTWANCountersSerial, 
								ltp8xONTWANCountersCrossConnect }
				::= { ltp8xONTWANCountersTable 1 }
			
			Ltp8xONTWANCountersEntry ::= SEQUENCE {
					ltp8xONTWANCountersSlot			Unsigned32,
					ltp8xONTWANCountersSerial		ONTSerial,
					ltp8xONTWANCountersCrossConnect	Unsigned32,
					ltp8xONTWANCountersRXDrops		Unsigned32,
					ltp8xONTWANCountersRXErrors		Unsigned32,
					ltp8xONTWANCountersRecvBytes 	Unsigned32,
					ltp8xONTWANCountersRecvFrames	Unsigned32,
					ltp8xONTWANCountersTXDrops		Unsigned32,
					ltp8xONTWANCountersTXErrors		Unsigned32,
					ltp8xONTWANCountersTrmtBytes	Unsigned32,
					ltp8xONTWANCountersTrmtFrames	Unsigned32
			}
			
						ltp8xONTWANCountersSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTWANCountersEntry 1 }
						
						ltp8xONTWANCountersSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 2 }
						
						ltp8xONTWANCountersCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 3 }
							
						ltp8xONTWANCountersRXDrops OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 4 }
							
						ltp8xONTWANCountersRXErrors OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 5 }
							
						ltp8xONTWANCountersRecvBytes OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 6 }
							
						ltp8xONTWANCountersRecvFrames OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 7 }
							
						ltp8xONTWANCountersTXDrops OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 8 }
							
						ltp8xONTWANCountersTXErrors OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 9 }
							
						ltp8xONTWANCountersTrmtBytes OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 10 }
							
						ltp8xONTWANCountersTrmtFrames OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTWANCountersEntry 11 }
							
		ltp8xONTGEMPortCountersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTGEMPortCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 2 }
			
			ltp8xONTGEMPortCountersEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTGEMPortCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTGEMPortCountersSlot, ltp8xONTGEMPortCountersSerial, 
								ltp8xONTGEMPortCountersCrossConnect }
				::= { ltp8xONTGEMPortCountersTable 1 }
			
			Ltp8xONTGEMPortCountersEntry ::= SEQUENCE {
					ltp8xONTGEMPortCountersSlot						Unsigned32,
					ltp8xONTGEMPortCountersSerial					ONTSerial,
					ltp8xONTGEMPortCountersCrossConnect				Unsigned32,
					ltp8xONTGEMPortCountersDSFinishedIntervals		Unsigned32,
					ltp8xONTGEMPortCountersDSGEMFrames				Unsigned32,
					ltp8xONTGEMPortCountersDSPayloadBytesLOW 		Unsigned32,
					ltp8xONTGEMPortCountersDSPayloadBytesHIGH		Unsigned32,
					ltp8xONTGEMPortCountersUSFinishedIntervals		Unsigned32,
					ltp8xONTGEMPortCountersUSGEMFrames				Unsigned32,
					ltp8xONTGEMPortCountersUSPayloadBytesLOW		Unsigned32,
					ltp8xONTGEMPortCountersUSPayloadBytesHIGH		Unsigned32
			}
			
						ltp8xONTGEMPortCountersSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTGEMPortCountersEntry 1 }
						
						ltp8xONTGEMPortCountersSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 2 }
						
						ltp8xONTGEMPortCountersCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 3 }
							
						ltp8xONTGEMPortCountersDSFinishedIntervals OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 4 }
							
						ltp8xONTGEMPortCountersDSGEMFrames OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 5 }
							
						ltp8xONTGEMPortCountersDSPayloadBytesLOW OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 6 }
							
						ltp8xONTGEMPortCountersDSPayloadBytesHIGH OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 7 }
							
						ltp8xONTGEMPortCountersUSFinishedIntervals OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 8 }
							
						ltp8xONTGEMPortCountersUSGEMFrames OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 9 }
							
						ltp8xONTGEMPortCountersUSPayloadBytesLOW OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 10 }
							
						ltp8xONTGEMPortCountersUSPayloadBytesHIGH OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortCountersEntry 11 }
							
							
			ltp8xONTEthPerformMonitoringHistDataTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTEthPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 3 }
			
			ltp8xONTEthPerformMonitoringHistDataEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTEthPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTEthPerformMonitoringHistDataSlot, ltp8xONTEthPerformMonitoringHistDataSerial, 
								ltp8xONTEthPerformMonitoringHistDataPort, ltp8xONTEthPerformMonitoringHistDataCounterID}
				::= { ltp8xONTEthPerformMonitoringHistDataTable 1 }
			
			Ltp8xONTEthPerformMonitoringHistDataEntry ::= SEQUENCE {
					ltp8xONTEthPerformMonitoringHistDataSlot					Unsigned32,
					ltp8xONTEthPerformMonitoringHistDataSerial					ONTSerial,
					ltp8xONTEthPerformMonitoringHistDataPort					Unsigned32,
					ltp8xONTEthPerformMonitoringHistDataCounterID				Unsigned32,
					ltp8xONTEthPerformMonitoringHistDataCounterName				DisplayString,
					ltp8xONTEthPerformMonitoringHistDataCounterValue	 		Unsigned32
			}
			
						ltp8xONTEthPerformMonitoringHistDataSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 1 }
						
						ltp8xONTEthPerformMonitoringHistDataSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 2 }
						
						ltp8xONTEthPerformMonitoringHistDataPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 3 }
							
						ltp8xONTEthPerformMonitoringHistDataCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 4 }
							
						ltp8xONTEthPerformMonitoringHistDataCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 5 }
							
						ltp8xONTEthPerformMonitoringHistDataCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthPerformMonitoringHistDataEntry 6 }
							
							
			ltp8xONTGalEthPerformMonitoringHistDataTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTGalEthPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 4 }
			
			ltp8xONTGalEthPerformMonitoringHistDataEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTGalEthPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTGalEthPerformMonitoringHistDataSlot, ltp8xONTGalEthPerformMonitoringHistDataSerial, 
								ltp8xONTGalEthPerformMonitoringHistDataCrossConnect, ltp8xONTGalEthPerformMonitoringHistDataCounterID}
				::= { ltp8xONTGalEthPerformMonitoringHistDataTable 1 }
			
			Ltp8xONTGalEthPerformMonitoringHistDataEntry ::= SEQUENCE {
					ltp8xONTGalEthPerformMonitoringHistDataSlot					Unsigned32,
					ltp8xONTGalEthPerformMonitoringHistDataSerial					ONTSerial,
					ltp8xONTGalEthPerformMonitoringHistDataCrossConnect				Unsigned32,
					ltp8xONTGalEthPerformMonitoringHistDataCounterID				Unsigned32,
					ltp8xONTGalEthPerformMonitoringHistDataCounterName				DisplayString,
					ltp8xONTGalEthPerformMonitoringHistDataCounterValue	 		Unsigned32
			}
			
						ltp8xONTGalEthPerformMonitoringHistDataSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 1 }
						
						ltp8xONTGalEthPerformMonitoringHistDataSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 2 }
						
						ltp8xONTGalEthPerformMonitoringHistDataCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 3 }
							
						ltp8xONTGalEthPerformMonitoringHistDataCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 4 }
							
						ltp8xONTGalEthPerformMonitoringHistDataCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 5 }
							
						ltp8xONTGalEthPerformMonitoringHistDataCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGalEthPerformMonitoringHistDataEntry 6 }
							
			ltp8xONTFecPerformMonitoringHistDataTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFecPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 5 }
			
			ltp8xONTFecPerformMonitoringHistDataEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFecPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFecPerformMonitoringHistDataSlot, ltp8xONTFecPerformMonitoringHistDataSerial, 
								ltp8xONTFecPerformMonitoringHistDataDummyIndex, ltp8xONTFecPerformMonitoringHistDataCounterID}
				::= { ltp8xONTFecPerformMonitoringHistDataTable 1 }
			
			Ltp8xONTFecPerformMonitoringHistDataEntry ::= SEQUENCE {
					ltp8xONTFecPerformMonitoringHistDataSlot					Unsigned32,
					ltp8xONTFecPerformMonitoringHistDataSerial					ONTSerial,
					ltp8xONTFecPerformMonitoringHistDataDummyIndex				Unsigned32,
					ltp8xONTFecPerformMonitoringHistDataCounterID				Unsigned32,
					ltp8xONTFecPerformMonitoringHistDataCounterName				DisplayString,
					ltp8xONTFecPerformMonitoringHistDataCounterValue	 		Unsigned32
			}
			
						ltp8xONTFecPerformMonitoringHistDataSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 1 }
						
						ltp8xONTFecPerformMonitoringHistDataSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 2 }
						
						ltp8xONTFecPerformMonitoringHistDataDummyIndex OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Always = 1. Needed to preserve common statistics table structure"
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 3 }
							
						ltp8xONTFecPerformMonitoringHistDataCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 4 }
							
						ltp8xONTFecPerformMonitoringHistDataCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 5 }
							
						ltp8xONTFecPerformMonitoringHistDataCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFecPerformMonitoringHistDataEntry 6 }
							
			ltp8xONTEthFrameDSPerformMonitoringHistDataTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTEthFrameDSPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 6 }
			
			ltp8xONTEthFrameDSPerformMonitoringHistDataEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTEthFrameDSPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTEthFrameDSPerformMonitoringHistDataSlot, ltp8xONTEthFrameDSPerformMonitoringHistDataSerial, 
								ltp8xONTEthFrameDSPerformMonitoringHistDataPort, ltp8xONTEthFrameDSPerformMonitoringHistDataCounterID}
				::= { ltp8xONTEthFrameDSPerformMonitoringHistDataTable 1 }
			
			Ltp8xONTEthFrameDSPerformMonitoringHistDataEntry ::= SEQUENCE {
					ltp8xONTEthFrameDSPerformMonitoringHistDataSlot					Unsigned32,
					ltp8xONTEthFrameDSPerformMonitoringHistDataSerial					ONTSerial,
					ltp8xONTEthFrameDSPerformMonitoringHistDataPort						Unsigned32,
					ltp8xONTEthFrameDSPerformMonitoringHistDataCounterID				Unsigned32,
					ltp8xONTEthFrameDSPerformMonitoringHistDataCounterName				DisplayString,
					ltp8xONTEthFrameDSPerformMonitoringHistDataCounterValue	 		Unsigned32
			}
			
						ltp8xONTEthFrameDSPerformMonitoringHistDataSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 1 }
						
						ltp8xONTEthFrameDSPerformMonitoringHistDataSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 2 }
						
						ltp8xONTEthFrameDSPerformMonitoringHistDataPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 3 }
							
						ltp8xONTEthFrameDSPerformMonitoringHistDataCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 4 }
							
						ltp8xONTEthFrameDSPerformMonitoringHistDataCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 5 }
							
						ltp8xONTEthFrameDSPerformMonitoringHistDataCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameDSPerformMonitoringHistDataEntry 6 }
							
			ltp8xONTEthFrameUSPerformMonitoringHistDataTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTEthFrameUSPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 7 }
			
			ltp8xONTEthFrameUSPerformMonitoringHistDataEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTEthFrameUSPerformMonitoringHistDataEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTEthFrameUSPerformMonitoringHistDataSlot, ltp8xONTEthFrameUSPerformMonitoringHistDataSerial, 
								ltp8xONTEthFrameUSPerformMonitoringHistDataPort, ltp8xONTEthFrameUSPerformMonitoringHistDataCounterID}
				::= { ltp8xONTEthFrameUSPerformMonitoringHistDataTable 1 }
			
			Ltp8xONTEthFrameUSPerformMonitoringHistDataEntry ::= SEQUENCE {
					ltp8xONTEthFrameUSPerformMonitoringHistDataSlot					Unsigned32,
					ltp8xONTEthFrameUSPerformMonitoringHistDataSerial					ONTSerial,
					ltp8xONTEthFrameUSPerformMonitoringHistDataPort						Unsigned32,
					ltp8xONTEthFrameUSPerformMonitoringHistDataCounterID				Unsigned32,
					ltp8xONTEthFrameUSPerformMonitoringHistDataCounterName				DisplayString,
					ltp8xONTEthFrameUSPerformMonitoringHistDataCounterValue	 		Unsigned32
			}
			
						ltp8xONTEthFrameUSPerformMonitoringHistDataSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 1 }
						
						ltp8xONTEthFrameUSPerformMonitoringHistDataSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 2 }
						
						ltp8xONTEthFrameUSPerformMonitoringHistDataPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 3 }
							
						ltp8xONTEthFrameUSPerformMonitoringHistDataCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 4 }
							
						ltp8xONTEthFrameUSPerformMonitoringHistDataCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 5 }
							
						ltp8xONTEthFrameUSPerformMonitoringHistDataCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameUSPerformMonitoringHistDataEntry 6 }
							
			ltp8xONTGEMPortPerformMonitoringDSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTGEMPortPerformMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 8 }
			
			ltp8xONTGEMPortPerformMonitoringDSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTGEMPortPerformMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTGEMPortPerformMonitoringDSSlot, ltp8xONTGEMPortPerformMonitoringDSSerial, 
								ltp8xONTGEMPortPerformMonitoringDSCrossConnect, ltp8xONTGEMPortPerformMonitoringDSCounterID}
				::= { ltp8xONTGEMPortPerformMonitoringDSTable 1 }
			
			Ltp8xONTGEMPortPerformMonitoringDSEntry ::= SEQUENCE {
					ltp8xONTGEMPortPerformMonitoringDSSlot					Unsigned32,
					ltp8xONTGEMPortPerformMonitoringDSSerial					ONTSerial,
					ltp8xONTGEMPortPerformMonitoringDSCrossConnect			Unsigned32,
					ltp8xONTGEMPortPerformMonitoringDSCounterID				Unsigned32,
					ltp8xONTGEMPortPerformMonitoringDSCounterName				DisplayString,
					ltp8xONTGEMPortPerformMonitoringDSCounterValue	 		Unsigned32
			}
			
						ltp8xONTGEMPortPerformMonitoringDSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 1 }
						
						ltp8xONTGEMPortPerformMonitoringDSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 2 }
						
						ltp8xONTGEMPortPerformMonitoringDSCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 3 }
							
						ltp8xONTGEMPortPerformMonitoringDSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 4 }
							
						ltp8xONTGEMPortPerformMonitoringDSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 5 }
							
						ltp8xONTGEMPortPerformMonitoringDSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringDSEntry 6 }
							
			ltp8xONTGEMPortPerformMonitoringUSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTGEMPortPerformMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 9 }
			
			ltp8xONTGEMPortPerformMonitoringUSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTGEMPortPerformMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTGEMPortPerformMonitoringUSSlot, ltp8xONTGEMPortPerformMonitoringUSSerial, 
								ltp8xONTGEMPortPerformMonitoringUSCrossConnect, ltp8xONTGEMPortPerformMonitoringUSCounterID}
				::= { ltp8xONTGEMPortPerformMonitoringUSTable 1 }
			
			Ltp8xONTGEMPortPerformMonitoringUSEntry ::= SEQUENCE {
					ltp8xONTGEMPortPerformMonitoringUSSlot					Unsigned32,
					ltp8xONTGEMPortPerformMonitoringUSSerial					ONTSerial,
					ltp8xONTGEMPortPerformMonitoringUSCrossConnect			Unsigned32,
					ltp8xONTGEMPortPerformMonitoringUSCounterID				Unsigned32,
					ltp8xONTGEMPortPerformMonitoringUSCounterName				DisplayString,
					ltp8xONTGEMPortPerformMonitoringUSCounterValue	 		Unsigned32
			}
			
						ltp8xONTGEMPortPerformMonitoringUSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 1 }
						
						ltp8xONTGEMPortPerformMonitoringUSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 2 }
						
						ltp8xONTGEMPortPerformMonitoringUSCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 3 }
							
						ltp8xONTGEMPortPerformMonitoringUSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 4 }
							
						ltp8xONTGEMPortPerformMonitoringUSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 5 }
							
						ltp8xONTGEMPortPerformMonitoringUSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTGEMPortPerformMonitoringUSEntry 6 }
							
			ltp8xONTCrossConnectDSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTCrossConnectDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 10 }
			
			ltp8xONTCrossConnectDSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTCrossConnectDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTCrossConnectDSSlot, ltp8xONTCrossConnectDSSerial, 
								ltp8xONTCrossConnectDSCrossConnect, ltp8xONTCrossConnectDSCounterID}
				::= { ltp8xONTCrossConnectDSTable 1 }
			
			Ltp8xONTCrossConnectDSEntry ::= SEQUENCE {
					ltp8xONTCrossConnectDSSlot					Unsigned32,
					ltp8xONTCrossConnectDSSerial					ONTSerial,
					ltp8xONTCrossConnectDSCrossConnect			Unsigned32,
					ltp8xONTCrossConnectDSCounterID				Unsigned32,
					ltp8xONTCrossConnectDSCounterName				DisplayString,
					ltp8xONTCrossConnectDSCounterValue	 		Unsigned32
			}
			
						ltp8xONTCrossConnectDSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTCrossConnectDSEntry 1 }
						
						ltp8xONTCrossConnectDSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectDSEntry 2 }
						
						ltp8xONTCrossConnectDSCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectDSEntry 3 }
							
						ltp8xONTCrossConnectDSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectDSEntry 4 }
							
						ltp8xONTCrossConnectDSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectDSEntry 5 }
							
						ltp8xONTCrossConnectDSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectDSEntry 6 }
							
			ltp8xONTCrossConnectUSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTCrossConnectUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 11 }
			
			ltp8xONTCrossConnectUSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTCrossConnectUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTCrossConnectUSSlot, ltp8xONTCrossConnectUSSerial, 
								ltp8xONTCrossConnectUSCrossConnect, ltp8xONTCrossConnectUSCounterID}
				::= { ltp8xONTCrossConnectUSTable 1 }
			
			Ltp8xONTCrossConnectUSEntry ::= SEQUENCE {
					ltp8xONTCrossConnectUSSlot					Unsigned32,
					ltp8xONTCrossConnectUSSerial					ONTSerial,
					ltp8xONTCrossConnectUSCrossConnect			Unsigned32,
					ltp8xONTCrossConnectUSCounterID				Unsigned32,
					ltp8xONTCrossConnectUSCounterName				DisplayString,
					ltp8xONTCrossConnectUSCounterValue	 		Unsigned32
			}
			
						ltp8xONTCrossConnectUSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTCrossConnectUSEntry 1 }
						
						ltp8xONTCrossConnectUSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectUSEntry 2 }
						
						ltp8xONTCrossConnectUSCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectUSEntry 3 }
							
						ltp8xONTCrossConnectUSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectUSEntry 4 }
							
						ltp8xONTCrossConnectUSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectUSEntry 5 }
							
						ltp8xONTCrossConnectUSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTCrossConnectUSEntry 6 }
							
		ltp8xONTServiceDSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTServiceDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 12 }
			
			ltp8xONTServiceDSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTServiceDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTServiceDSSlot, ltp8xONTServiceDSSerial, 
								ltp8xONTServiceDSService, ltp8xONTServiceDSCounterID}
				::= { ltp8xONTServiceDSTable 1 }
			
			Ltp8xONTServiceDSEntry ::= SEQUENCE {
					ltp8xONTServiceDSSlot					Unsigned32,
					ltp8xONTServiceDSSerial					ONTSerial,
					ltp8xONTServiceDSService			Unsigned32,
					ltp8xONTServiceDSCounterID				Unsigned32,
					ltp8xONTServiceDSCounterName				DisplayString,
					ltp8xONTServiceDSCounterValue	 		Unsigned32
			}
			
						ltp8xONTServiceDSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTServiceDSEntry 1 }
						
						ltp8xONTServiceDSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceDSEntry 2 }
						
						ltp8xONTServiceDSService OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceDSEntry 3 }
							
						ltp8xONTServiceDSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceDSEntry 4 }
							
						ltp8xONTServiceDSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceDSEntry 5 }
							
						ltp8xONTServiceDSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceDSEntry 6 }
							
			ltp8xONTServiceUSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTServiceUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 13 }
			
			ltp8xONTServiceUSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTServiceUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTServiceUSSlot, ltp8xONTServiceUSSerial, 
								ltp8xONTServiceUSService, ltp8xONTServiceUSCounterID}
				::= { ltp8xONTServiceUSTable 1 }
			
			Ltp8xONTServiceUSEntry ::= SEQUENCE {
					ltp8xONTServiceUSSlot					Unsigned32,
					ltp8xONTServiceUSSerial					ONTSerial,
					ltp8xONTServiceUSService			Unsigned32,
					ltp8xONTServiceUSCounterID				Unsigned32,
					ltp8xONTServiceUSCounterName				DisplayString,
					ltp8xONTServiceUSCounterValue	 		Unsigned32
			}
			
						ltp8xONTServiceUSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTServiceUSEntry 1 }
						
						ltp8xONTServiceUSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceUSEntry 2 }
						
						ltp8xONTServiceUSService OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceUSEntry 3 }
							
						ltp8xONTServiceUSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceUSEntry 4 }
							
						ltp8xONTServiceUSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceUSEntry 5 }
							
						ltp8xONTServiceUSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTServiceUSEntry 6 }
							
		ltp8xONTEthFrameExtendedPerformMonitoringDSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTEthFrameExtendedPerformMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 14 }
			
			ltp8xONTEthFrameExtendedPerformMonitoringDSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTEthFrameExtendedPerformMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTEthFrameExtendedPerformMonitoringDSSlot, ltp8xONTEthFrameExtendedPerformMonitoringDSSerial, 
								ltp8xONTEthFrameExtendedPerformMonitoringDSPort, ltp8xONTEthFrameExtendedPerformMonitoringDSCounterID}
				::= { ltp8xONTEthFrameExtendedPerformMonitoringDSTable 1 }
			
			Ltp8xONTEthFrameExtendedPerformMonitoringDSEntry ::= SEQUENCE {
					ltp8xONTEthFrameExtendedPerformMonitoringDSSlot					Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringDSSerial					ONTSerial,
					ltp8xONTEthFrameExtendedPerformMonitoringDSPort						Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringDSCounterID				Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringDSCounterName				DisplayString,
					ltp8xONTEthFrameExtendedPerformMonitoringDSCounterValue	 		Unsigned32
			}
			
						ltp8xONTEthFrameExtendedPerformMonitoringDSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 1 }
						
						ltp8xONTEthFrameExtendedPerformMonitoringDSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 2 }
						
						ltp8xONTEthFrameExtendedPerformMonitoringDSPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 3 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringDSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 4 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringDSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 5 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringDSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringDSEntry 6 }
							
		ltp8xONTEthFrameExtendedPerformMonitoringUSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTEthFrameExtendedPerformMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 15 }
			
			ltp8xONTEthFrameExtendedPerformMonitoringUSEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTEthFrameExtendedPerformMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTEthFrameExtendedPerformMonitoringUSSlot, ltp8xONTEthFrameExtendedPerformMonitoringUSSerial, 
								ltp8xONTEthFrameExtendedPerformMonitoringUSPort, ltp8xONTEthFrameExtendedPerformMonitoringUSCounterID}
				::= { ltp8xONTEthFrameExtendedPerformMonitoringUSTable 1 }
			
			Ltp8xONTEthFrameExtendedPerformMonitoringUSEntry ::= SEQUENCE {
					ltp8xONTEthFrameExtendedPerformMonitoringUSSlot					Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringUSSerial					ONTSerial,
					ltp8xONTEthFrameExtendedPerformMonitoringUSPort						Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringUSCounterID				Unsigned32,
					ltp8xONTEthFrameExtendedPerformMonitoringUSCounterName				DisplayString,
					ltp8xONTEthFrameExtendedPerformMonitoringUSCounterValue	 		Unsigned32
			}
			
						ltp8xONTEthFrameExtendedPerformMonitoringUSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 1 }
						
						ltp8xONTEthFrameExtendedPerformMonitoringUSSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 2 }
						
						ltp8xONTEthFrameExtendedPerformMonitoringUSPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 3 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringUSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 4 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringUSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 5 }
							
						ltp8xONTEthFrameExtendedPerformMonitoringUSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTEthFrameExtendedPerformMonitoringUSEntry 6 }
							
		ltp8xONTResetCountersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTResetCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTStatistics 20 }
			
			ltp8xONTResetCountersEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTResetCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTResetCountersSlot, ltp8xONTResetCountersSerial }
				::= { ltp8xONTResetCountersTable 1 }
			
			Ltp8xONTResetCountersEntry ::= SEQUENCE {
					ltp8xONTResetCountersSlot					Unsigned32,
					ltp8xONTResetCountersSerial					ONTSerial,
					ltp8xONTResetCountersAction					Unsigned32
			}
			
						ltp8xONTResetCountersSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTResetCountersEntry 1 }
						
						ltp8xONTResetCountersSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTResetCountersEntry 2 }
						
						ltp8xONTResetCountersAction OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set to 1 to reset ONT counters"
							::= { ltp8xONTResetCountersEntry 3 }
	
	ltp8xONTConfigTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 4 }
		
		ltp8xONTConfigEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTConfigSlot, ltp8xONTConfigSerial }
			::= { ltp8xONTConfigTable 1 }
		
		Ltp8xONTConfigEntry ::= SEQUENCE {
				ltp8xONTConfigSlot				Unsigned32,
				ltp8xONTConfigSerial			ONTSerial,
				ltp8xONTConfigChannel			Unsigned32,
				ltp8xONTConfigID 				Unsigned32,
				ltp8xONTConfigServicesProfile	Unsigned32,
				ltp8xONTConfigPassword			DisplayString,
				ltp8xONTConfigFecUp				TruthValue,
				ltp8xONTConfigDescription		DisplayString,
				ltp8xONTConfigManagementProfile	Unsigned32,
				ltp8xONTConfigMulticastProfile	Unsigned32,
				ltp8xONTConfigCrossConnectProfile0	Unsigned32,
				ltp8xONTConfigCrossConnectProfile1	Unsigned32,
				ltp8xONTConfigCrossConnectProfile2	Unsigned32,
				ltp8xONTConfigCrossConnectProfile3	Unsigned32,
				ltp8xONTConfigCrossConnectProfile4	Unsigned32,
				ltp8xONTConfigCrossConnectProfile5	Unsigned32,
				ltp8xONTConfigCrossConnectProfile6	Unsigned32,
				ltp8xONTConfigCrossConnectProfile7	Unsigned32,
				ltp8xONTConfigShapingProfile Unsigned32,
				ltp8xONTConfigRowStatus			RowStatus,
				ltp8xONTConfigEncryptionEnabled	TruthValue,
				ltp8xONTConfigDownstreamBroadcastEnabled TruthValue,
				ltp8xONTConfigAllocProfile0	Unsigned32,
				ltp8xONTConfigAllocProfile1	Unsigned32,
				ltp8xONTConfigAllocProfile2	Unsigned32,
				ltp8xONTConfigAllocProfile3	Unsigned32,
				ltp8xONTConfigAllocProfile4	Unsigned32,
				ltp8xONTConfigAllocProfile5	Unsigned32,
				ltp8xONTConfigAllocProfile6	Unsigned32,
				ltp8xONTConfigAllocProfile7	Unsigned32,
				ltp8xONTConfigPortsProfile Unsigned32,
				ltp8xONTConfigRFPortEnabled	INTEGER,
				ltp8xONTConfigHostControlledOMCI	TruthValue,
				ltp8xONTConfigVoiceProfile	Unsigned32,
				ltp8xONTConfigEnabled		TruthValue,
				ltp8xONTConfigScriptingProfile	Unsigned32,
				ltp8xONTConfigBerInterval Unsigned32,
				ltp8xONTConfigBerUpdatePeriod Unsigned32,
				ltp8xONTConfigOMCIErrorTolerant TruthValue,
				ltp8xONTConfigCustomModel INTEGER,
				ltp8xONTConfigEMSProfile Unsigned32,
				ltp8xONTConfigBandwidthManagementACSProfile	Unsigned32,
				ltp8xONTConfigTemplate	Unsigned32
		}
		
					ltp8xONTConfigSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTConfigEntry 1 }
					
					ltp8xONTConfigSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTConfigEntry 2 }
					
					ltp8xONTConfigChannel OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "ONT assigned for this channel, 255 if ONT can appear on any channel"
						::= { ltp8xONTConfigEntry 3 }
						
					ltp8xONTConfigID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 4 }
						
					ltp8xONTConfigServicesProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  deprecated
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 5 }
						
					ltp8xONTConfigPassword  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 6 }
						
					ltp8xONTConfigFecUp OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTConfigEntry 7 }
						
					ltp8xONTConfigDescription OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTConfigEntry 8 }
						
					ltp8xONTConfigManagementProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 9 }
						
					ltp8xONTConfigMulticastProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 10 }
					
					ltp8xONTConfigCrossConnectProfile0 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 11 }
						
					ltp8xONTConfigCrossConnectProfile1 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 12 }
						
					ltp8xONTConfigCrossConnectProfile2 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 13 }
						
					ltp8xONTConfigCrossConnectProfile3 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 14 }
						
					ltp8xONTConfigCrossConnectProfile4 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 15 }
						
					ltp8xONTConfigCrossConnectProfile5 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 16 }
						
					ltp8xONTConfigCrossConnectProfile6 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 17 }
						
					ltp8xONTConfigCrossConnectProfile7 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 18 }
						
					ltp8xONTConfigShapingProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTConfigEntry 19 }
						
					ltp8xONTConfigRowStatus OBJECT-TYPE
						SYNTAX	RowStatus
						MAX-ACCESS	read-write
						STATUS	current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 20 }
						
					ltp8xONTConfigEncryptionEnabled OBJECT-TYPE
						SYNTAX	TruthValue
						MAX-ACCESS	read-write
						STATUS current
						DESCRIPTION "Is encryption enabled for this ONT"
						::= { ltp8xONTConfigEntry 21 }
					
					ltp8xONTConfigDownstreamBroadcastEnabled OBJECT-TYPE
						SYNTAX	TruthValue
						MAX-ACCESS	read-write
						STATUS current
						DESCRIPTION "Is broadcast enabled for this ONT"
						::= { ltp8xONTConfigEntry 22 }
						
					ltp8xONTConfigAllocProfile0 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 23 }
						
					ltp8xONTConfigAllocProfile1 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 24 }
						
					ltp8xONTConfigAllocProfile2 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 25 }
						
					ltp8xONTConfigAllocProfile3 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 26 }
						
					ltp8xONTConfigAllocProfile4 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 27 }
						
					ltp8xONTConfigAllocProfile5 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 28 }
						
					ltp8xONTConfigAllocProfile6 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 29 }
						
					ltp8xONTConfigAllocProfile7 OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 30 }
						
					ltp8xONTConfigPortsProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 31 }
						
					ltp8xONTConfigRFPortEnabled OBJECT-TYPE
						SYNTAX  INTEGER
						{
							disabled(0),
							enabled(1),
							noChange(2)
						}
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 32 }
						
					ltp8xONTConfigHostControlledOMCI OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  deprecated
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 33 }
						
					ltp8xONTConfigVoiceProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 34 }
						
					ltp8xONTConfigEnabled OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTConfigEntry 35 }
						
					ltp8xONTConfigScriptingProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 36 }
						
					ltp8xONTConfigBerInterval OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "BER interval value to send to ONT."
						::= { ltp8xONTConfigEntry 37 }
						
					ltp8xONTConfigBerUpdatePeriod OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Mininal time between two consecutive BER mesuarements, sec."
						::= { ltp8xONTConfigEntry 38 }
						
					ltp8xONTConfigOMCIErrorTolerant OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Continue configuration after OMCI errors."
						::= { ltp8xONTConfigEntry 39 }
						
					ltp8xONTConfigCustomModel OBJECT-TYPE
						SYNTAX  INTEGER {
									script(3),
									none(4)
								}
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Ont custom configuration model."
						::= { ltp8xONTConfigEntry 40 }
						
					ltp8xONTConfigEMSProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 41 }
						
					ltp8xONTConfigBandwidthManagementACSProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that profile is not assigned."
						::= { ltp8xONTConfigEntry 42 }
						
					ltp8xONTConfigTemplate OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Value 65535 means that template is not assigned."
						::= { ltp8xONTConfigEntry 43 }
						
						
	ltp8xONTServiceOverrideTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTServiceOverrideEntry
			MAX-ACCESS        not-accessible
			STATUS                deprecated
			DESCRIPTION
					" "
			::= { ltp8xONT 5 }
		
		ltp8xONTServiceOverrideEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTServiceOverrideEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTServiceOverrideID, ltp8xONTServiceOverrideSlot , 
								 ltp8xONTServiceOverrideSerial}
			::= { ltp8xONTServiceOverrideTable 1 }
		
		Ltp8xONTServiceOverrideEntry ::= SEQUENCE {
				ltp8xONTServiceOverrideID								Unsigned32,
				ltp8xONTServiceOverrideSlot								Unsigned32,				
				ltp8xONTServiceOverrideSerial							ONTSerial,
				ltp8xONTServiceOverrideEnabled							TruthValue,
				ltp8xONTServiceOverrideCustomerVID						Unsigned32,
				ltp8xONTServiceOverrideCustomerCOS						Unsigned32 
		}
		
					ltp8xONTServiceOverrideID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceOverrideEntry 1 }
					
					ltp8xONTServiceOverrideSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTServiceOverrideEntry 2 }					
						
					ltp8xONTServiceOverrideSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceOverrideEntry 3 }	
						
					ltp8xONTServiceOverrideEnabled OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceOverrideEntry 4 }
						
					ltp8xONTServiceOverrideCustomerVID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceOverrideEntry 5 }
						
					ltp8xONTServiceOverrideCustomerCOS OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  deprecated
						DESCRIPTION "This entry is deprecated"
						::= { ltp8xONTServiceOverrideEntry 6 }
						
						
						
		ltp8xONTManagementProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTManagementProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 6 }
			
			ltp8xONTManagementProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTManagementProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTManagementID }
				::= { ltp8xONTManagementProfileTable 1 }
			
			Ltp8xONTManagementProfileEntry ::= SEQUENCE {
					ltp8xONTManagementID										Unsigned32,
					ltp8xONTManagementDescription							DisplayString,
					ltp8xONTManagementName									DisplayString,
					ltp8xONTManagementCrossConnect							Unsigned32,
					ltp8xONTManagementURL									DisplayString,
					ltp8xONTManagementUsername								DisplayString,
					ltp8xONTManagementPassword								DisplayString,
					ltp8xONTManagementOMCIConfiguration						TruthValue,
					ltp8xONTManagementRowStatus								RowStatus
			}
			
				
						ltp8xONTManagementID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 1 }	
							
						ltp8xONTManagementDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 2 }
							
						ltp8xONTManagementName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 3 }
							
						ltp8xONTManagementCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 4 }
							
						ltp8xONTManagementURL OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 5 }
							
						ltp8xONTManagementUsername OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 6 }
							
						ltp8xONTManagementPassword OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 7 }
							
						ltp8xONTManagementOMCIConfiguration OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 8 }
							
						ltp8xONTManagementRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTManagementProfileEntry 20 }
							
							
							
		ltp8xONTMulticastProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTMulticastProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 7 }
			
			ltp8xONTMulticastProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTMulticastProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTMulticastID }
				::= { ltp8xONTMulticastProfileTable 1 }
			
			Ltp8xONTMulticastProfileEntry ::= SEQUENCE {
					ltp8xONTMulticastID										Unsigned32,
					ltp8xONTMulticastDescription							DisplayString,
					ltp8xONTMulticastName									DisplayString
			}
			
				
						ltp8xONTMulticastID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastProfileEntry 1 }	
							
						ltp8xONTMulticastDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastProfileEntry 2 }
							
						ltp8xONTMulticastName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastProfileEntry 3 }
							
							
						
						
		ltp8xONTServicesProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTServicesProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                deprecated
				DESCRIPTION
						" "
				::= { ltp8xONT 8 }
			
			ltp8xONTServicesProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTServicesProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTServicesID }
				::= { ltp8xONTServicesProfileTable 1 }
			
			Ltp8xONTServicesProfileEntry ::= SEQUENCE {
					ltp8xONTServicesID										Unsigned32,
					ltp8xONTServicesDescription							DisplayString,
					ltp8xONTServicesName								DisplayString
			}
			
				
						ltp8xONTServicesID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTServicesProfileEntry 1 }	
							
						ltp8xONTServicesDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTServicesProfileEntry 2 }
							
						ltp8xONTServicesName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTServicesProfileEntry 3 }
							
		ltp8xONTCrossConnectProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTCrossConnectProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 9 }
			
			ltp8xONTCrossConnectProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTCrossConnectProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTCrossConnectID }
				::= { ltp8xONTCrossConnectProfileTable 1 }
			
			Ltp8xONTCrossConnectProfileEntry ::= SEQUENCE {
					ltp8xONTCrossConnectID									Unsigned32,
					ltp8xONTCrossConnectDescription							DisplayString,
					ltp8xONTCrossConnectName								DisplayString,
					ltp8xONTCrossConnectModel								INTEGER,
					ltp8xONTCrossConnectBridgeGroup							Unsigned32,
					ltp8xONTCrossConnectTagMode								INTEGER,
					ltp8xONTCrossConnectOuterVID							INTEGER,
					ltp8xONTCrossConnectOuterCOS							INTEGER,
					ltp8xONTCrossConnectInnerVID							INTEGER,
					ltp8xONTCrossConnectUVID								INTEGER,
					ltp8xONTCrossConnectUCOS								INTEGER,
					ltp8xONTCrossConnectMacTableEntryLimit					INTEGER,
					ltp8xONTCrossConnectType								INTEGER,
					ltp8xONTCrossConnectIphostEid							Integer32,
					ltp8xONTCrossConnectPriorityQueue						Unsigned32,
					ltp8xONTCrossConnectRowStatus							RowStatus
			}
			
				
						ltp8xONTCrossConnectID OBJECT-TYPE
							SYNTAX  Unsigned32 
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 1 }	
							
						ltp8xONTCrossConnectDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 2 }
							
						ltp8xONTCrossConnectName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 3 }
							
						ltp8xONTCrossConnectModel OBJECT-TYPE
							SYNTAX  INTEGER {
								ontRg(0),
								ont(1)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 4 }
						
						ltp8xONTCrossConnectBridgeGroup OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 5 }
						
						ltp8xONTCrossConnectTagMode OBJECT-TYPE
							SYNTAX  INTEGER {
								singleTagged(0),
								doubleTagged(1)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 6 }
							
						ltp8xONTCrossConnectOuterVID OBJECT-TYPE
							SYNTAX  INTEGER { auto(65535) }
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values from -100 to -131 specify usage of terminal-vid. -100 - terminal-vid-0, -131 - terminal-vid-31"
							::= { ltp8xONTCrossConnectProfileEntry 7 }
							
						ltp8xONTCrossConnectOuterCOS OBJECT-TYPE
							SYNTAX  INTEGER { unused(255) }
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values from 100 to 131 specify usage of terminal-vid. 100 - terminal-vid-0, 131 - terminal-vid-31"
							::= { ltp8xONTCrossConnectProfileEntry 8 }
							
						ltp8xONTCrossConnectInnerVID OBJECT-TYPE
							SYNTAX  INTEGER { auto(65535) }
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values from -100 to -131 specify usage of terminal-vid. -100 - terminal-vid-0, -131 - terminal-vid-31"
							::= { ltp8xONTCrossConnectProfileEntry 9 }
							
						ltp8xONTCrossConnectUVID OBJECT-TYPE
							SYNTAX  INTEGER { auto(65535) }
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values from -100 to -131 specify usage of terminal-vid. -100 - terminal-vid-0, -131 - terminal-vid-31"
							::= { ltp8xONTCrossConnectProfileEntry 10 }
							
						ltp8xONTCrossConnectUCOS OBJECT-TYPE
							SYNTAX  INTEGER { unused(255) }
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values from 100 to 131 specify usage of terminal-vid. 100 - terminal-vid-0, 131 - terminal-vid-31"
							::= { ltp8xONTCrossConnectProfileEntry 11 }
							
						ltp8xONTCrossConnectMacTableEntryLimit OBJECT-TYPE
							SYNTAX  INTEGER {
								unlimited(127)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 12 }
							
						ltp8xONTCrossConnectType OBJECT-TYPE
							SYNTAX  INTEGER {
								general(0),
								multicast(1),
								management(2),
								voice(3)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 13 }
							
						ltp8xONTCrossConnectIphostEid OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 14 }
							
						ltp8xONTCrossConnectPriorityQueue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Priority queue for upstream traffic. 0-7"
							::= { ltp8xONTCrossConnectProfileEntry 15 }
							
						ltp8xONTCrossConnectRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTCrossConnectProfileEntry 20 }
											
		ltp8xONTMulticastGroupsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTMulticastGroupsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 10 }
			
			ltp8xONTMulticastGroupsEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTMulticastGroupsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTMulticastGroupsSlot,
								ltp8xONTMulticastGroupsSerial,
								ltp8xONTMulticastGroupsEntryID }
				::= { ltp8xONTMulticastGroupsTable 1 }
			
			Ltp8xONTMulticastGroupsEntry ::= SEQUENCE {
					ltp8xONTMulticastGroupsSlot				Unsigned32,
					ltp8xONTMulticastGroupsSerial			ONTSerial,
					ltp8xONTMulticastGroupsEntryID			Unsigned32,
					ltp8xONTMulticastGroupsVLAN				Unsigned32,
					ltp8xONTMulticastGroupsSourceIP			IpAddress,
					ltp8xONTMulticastGroupsDestIP			IpAddress,
					ltp8xONTMulticastGroupsBEBandwidth		Unsigned32,
					ltp8xONTMulticastGroupsClientIP			IpAddress,
					ltp8xONTMulticastGroupsRecentJoinTime	TimeTicks
			}
			
				
						ltp8xONTMulticastGroupsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 1 }
							
						ltp8xONTMulticastGroupsSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 2 }
							
						ltp8xONTMulticastGroupsEntryID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 3 }
							
						ltp8xONTMulticastGroupsVLAN	OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 4 }	
							
						ltp8xONTMulticastGroupsSourceIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 5 }
							
						ltp8xONTMulticastGroupsDestIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTMulticastGroupsEntry 6 }
							
						ltp8xONTMulticastGroupsBEBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Best efforts actual bandwidth estimate, bytes per second"
							::= { ltp8xONTMulticastGroupsEntry 7 }
							
						ltp8xONTMulticastGroupsClientIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Client (set-top box) IP address, that is, the IP address of the device currently joined"
							::= { ltp8xONTMulticastGroupsEntry 8 }
							
						ltp8xONTMulticastGroupsRecentJoinTime OBJECT-TYPE
							SYNTAX  TimeTicks
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Time since the most recent join of this client to the IP channel, in seconds"
							::= { ltp8xONTMulticastGroupsEntry 9 }
							
		ltp8xONTBufferZoneTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTBufferZoneEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						"A buffer table, to prevent GETNEXT and GETBULK requests from
						 peeking into ONTAddressTable, because it can be very time-consuming."
				::= { ltp8xONT 11 }
			
			ltp8xONTBufferZoneEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTBufferZoneEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTBufferZoneValue }
				::= { ltp8xONTBufferZoneTable 1 }
			
			Ltp8xONTBufferZoneEntry ::= SEQUENCE {
					ltp8xONTBufferZoneValue				Unsigned32
			}
			
				
						ltp8xONTBufferZoneValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTBufferZoneEntry 1 }
							
		ltp8xONTAddressTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTAddressEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 12 }
			
			ltp8xONTAddressEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTAddressEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTAddressSlot,
								ltp8xONTAddressSerial,
								ltp8xONTAddressEntryID }
				::= { ltp8xONTAddressTable 1 }
			
			Ltp8xONTAddressEntry ::= SEQUENCE {
					ltp8xONTAddressSlot					Unsigned32,
					ltp8xONTAddressSerial				ONTSerial,
					ltp8xONTAddressEntryID				Unsigned32,
					ltp8xONTAddressPriority				Integer32,
					ltp8xONTAddressCVID					Integer32,
					ltp8xONTAddressSVID					Integer32,
					ltp8xONTAddressMacAddress			MacAddress,
					ltp8xONTAddressCPUDestined			TruthValue,
					ltp8xONTAddressDatapathForwarding	TruthValue,
					ltp8xONTAddressUniPort				Unsigned32,
					ltp8xONTAddressEntryType			AddressEntryType,
					ltp8xONTAddressAge					Unsigned32,
					ltp8xONTAddressGEMPortId			Unsigned32,
					ltp8xONTAddressUVID					Integer32
			}
			
				
						ltp8xONTAddressSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAddressEntry 1 }
							
						ltp8xONTAddressSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAddressEntry 2 }
							
						ltp8xONTAddressEntryID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAddressEntry 3 }
							
						ltp8xONTAddressPriority		OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Priority of the packets"
							::= { ltp8xONTAddressEntry 4 }	
							
						ltp8xONTAddressCVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "C-VLAN ID of the packet"
							::= { ltp8xONTAddressEntry 5 }
							
						ltp8xONTAddressSVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "S-VLAN ID of the packet"
							::= { ltp8xONTAddressEntry 6 }
							
						ltp8xONTAddressMacAddress OBJECT-TYPE
							SYNTAX  MacAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "MAC address"
							::= { ltp8xONTAddressEntry 7 }
							
						ltp8xONTAddressCPUDestined OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Whether packet should be forwarded to CPU"
							::= { ltp8xONTAddressEntry 8 }
							
						ltp8xONTAddressDatapathForwarding OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Whether packet should be forwarded to Datapath"
							::= { ltp8xONTAddressEntry 9 }
							
						ltp8xONTAddressUniPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTAddressEntry 10 }
							
						ltp8xONTAddressEntryType OBJECT-TYPE
							SYNTAX  AddressEntryType
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTAddressEntry 11 }
							
						ltp8xONTAddressAge OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "The age of the entry"
							::= { ltp8xONTAddressEntry 12 }
							
						ltp8xONTAddressGEMPortId OBJECT-TYPE		
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "GEM Port-ID from which packet has come"
							::= { ltp8xONTAddressEntry 13 }
							
						ltp8xONTAddressUVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "U-VLAN ID of the packet"
							::= { ltp8xONTAddressEntry 14 }
							
			ltp8xONTMassUpdateFirmwareTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTMassUpdateFirmwareEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 13 }
		
			ltp8xONTMassUpdateFirmwareEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTMassUpdateFirmwareEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTMassUpdateFirmwareSlot }
				::= { ltp8xONTMassUpdateFirmwareTable 1 }
			
			Ltp8xONTMassUpdateFirmwareEntry ::= SEQUENCE {
					ltp8xONTMassUpdateFirmwareSlot					Unsigned32,
					ltp8xONTMassUpdateFirmwareAction				Unsigned32
			}
			
						ltp8xONTMassUpdateFirmwareSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTMassUpdateFirmwareEntry 1 }
						
						ltp8xONTMassUpdateFirmwareAction OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set to 1 to initiate ONT mass update"
							::= { ltp8xONTMassUpdateFirmwareEntry 2 }
							
							
		ltp8xONTCustomCrossConnectTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTCustomCrossConnectEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 14 }
		
		ltp8xONTCustomCrossConnectEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTCustomCrossConnectEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTCustomCrossConnectSlot , 
								 ltp8xONTCustomCrossConnectSerial, ltp8xONTCustomCrossConnectID}
			::= { ltp8xONTCustomCrossConnectTable 1 }
		
		Ltp8xONTCustomCrossConnectEntry ::= SEQUENCE {
				ltp8xONTCustomCrossConnectSlot								Unsigned32,				
				ltp8xONTCustomCrossConnectSerial							ONTSerial,
				ltp8xONTCustomCrossConnectID								Unsigned32,
				ltp8xONTCustomCrossConnectEnabled							TruthValue,
				ltp8xONTCustomCrossConnectVID								Integer32,
				ltp8xONTCustomCrossConnectCOS								INTEGER,
				ltp8xONTCustomCrossConnectSVID								Integer32
		}
		
					ltp8xONTCustomCrossConnectSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTCustomCrossConnectEntry 1 }					
						
					ltp8xONTCustomCrossConnectSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTCustomCrossConnectEntry 2 }
						
					ltp8xONTCustomCrossConnectID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTCustomCrossConnectEntry 3 }	
						
					ltp8xONTCustomCrossConnectEnabled OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTCustomCrossConnectEntry 4 }
						
					ltp8xONTCustomCrossConnectVID OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTCustomCrossConnectEntry 5 }
						
					ltp8xONTCustomCrossConnectCOS OBJECT-TYPE
						SYNTAX  INTEGER {
								unused(255)
							}
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTCustomCrossConnectEntry 6 }
						
					ltp8xONTCustomCrossConnectSVID OBJECT-TYPE
						SYNTAX  Integer32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "-1 - unused"
						::= { ltp8xONTCustomCrossConnectEntry 7 }
						
							
		
		ltp8xONTACSConfigTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 15 }
		
		ltp8xONTACSConfigEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTACSConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTACSConfigSerial }
			::= { ltp8xONTACSConfigTable 1 }
		
		Ltp8xONTACSConfigEntry ::= SEQUENCE {
				ltp8xONTACSConfigSerial				ONTSerial,
				ltp8xONTACSUserID 					DisplayString,
				ltp8xONTACSConfigProfile			DisplayString,
				ltp8xONTACSConfigVoice1Enable		DisplayString,
				ltp8xONTACSConfigVoice1Number		DisplayString,
				ltp8xONTACSConfigVoice1Password		DisplayString,
				ltp8xONTACSConfigVoice2Enable		DisplayString,
				ltp8xONTACSConfigVoice2Number 		DisplayString,
				ltp8xONTACSConfigVoice2Password		DisplayString,
				ltp8xONTACSConfigSIPProxy			DisplayString,
				ltp8xONTACSConfigPPPLogin			DisplayString,
				ltp8xONTACSConfigPPPPassword		DisplayString,
				ltp8xONTACSConfigInternetVlan		DisplayString,
				ltp8xONTACSConfigResetToDefaults	Unsigned32,
				ltp8xONTACSConfigReboot				Unsigned32,
				ltp8xONTACSConfigReconfigure		Unsigned32,
				ltp8xONTACSConfigDelete				Unsigned32
		}
		
					ltp8xONTACSConfigSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTACSConfigEntry 1 }
						
					ltp8xONTACSUserID OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 2 }
						
					ltp8xONTACSConfigProfile OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 3 }
						
					ltp8xONTACSConfigVoice1Enable  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 4 }
						
					ltp8xONTACSConfigVoice1Number  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 5 }
						
					ltp8xONTACSConfigVoice1Password  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 6 }
						
					ltp8xONTACSConfigVoice2Enable  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 7 }
						
					ltp8xONTACSConfigVoice2Number  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 8 }
						
					ltp8xONTACSConfigVoice2Password  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 9 }
						
					ltp8xONTACSConfigSIPProxy  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 10 }
						
					ltp8xONTACSConfigPPPLogin  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 11 }
						
					ltp8xONTACSConfigPPPPassword  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 12 }
						
					ltp8xONTACSConfigInternetVlan  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 13 }
						
					ltp8xONTACSConfigResetToDefaults  OBJECT-TYPE
						SYNTAX Unsigned32
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 14 }
						
					ltp8xONTACSConfigReboot  OBJECT-TYPE
						SYNTAX Unsigned32
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 15 }
						
					ltp8xONTACSConfigReconfigure  OBJECT-TYPE
						SYNTAX Unsigned32
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigEntry 16 }
						
					ltp8xONTACSConfigDelete  OBJECT-TYPE
						SYNTAX Unsigned32
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION "Set to 1 to delete config in ACS"
						::= { ltp8xONTACSConfigEntry 20 }
						
						
						
		ltp8xONTACSProfilesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSProfilesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 16 }
		
		ltp8xONTACSProfilesEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTACSProfilesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTACSProfilesName }
			::= { ltp8xONTACSProfilesTable 1 }
		
		Ltp8xONTACSProfilesEntry ::= SEQUENCE {
				ltp8xONTACSProfilesName				DisplayString,
				ltp8xONTACSProfilesDescription		DisplayString
		}
		
						
					ltp8xONTACSProfilesName	OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSProfilesEntry 1 }
						
					ltp8xONTACSProfilesDescription OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSProfilesEntry 2 }
						
						
						
		ltp8xONTACSConfigAltTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSConfigAltEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 17 }
		
		ltp8xONTACSConfigAltEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTACSConfigAltEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTACSConfigAltSerial }
			::= { ltp8xONTACSConfigAltTable 1 }
		
		Ltp8xONTACSConfigAltEntry ::= SEQUENCE {
				ltp8xONTACSConfigAltSerial				ONTSerial,
				ltp8xONTACSConfigAltSubscriberID		DisplayString,
				ltp8xONTACSConfigAltProfile				DisplayString,
				ltp8xONTACSConfigAltVoice1Enable		DisplayString,
				ltp8xONTACSConfigAltVoice1Number		DisplayString,
				ltp8xONTACSConfigAltVoice1Password		DisplayString,
				ltp8xONTACSConfigAltVoice2Enable		DisplayString,
				ltp8xONTACSConfigAltVoice2Number 		DisplayString,
				ltp8xONTACSConfigAltVoice2Password		DisplayString,
				ltp8xONTACSConfigAltSIPProxy			DisplayString,
				ltp8xONTACSConfigAltPPPLogin			DisplayString,
				ltp8xONTACSConfigAltPPPPassword		DisplayString,
				ltp8xONTACSConfigAltInternetVlan		DisplayString,
				ltp8xONTACSConfigAltResetToDefaults	Unsigned32,
				ltp8xONTACSConfigAltRowStatus			RowStatus
		}
		
					ltp8xONTACSConfigAltSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTACSConfigAltEntry 1 }
						
					ltp8xONTACSConfigAltSubscriberID OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 2 }
						
					ltp8xONTACSConfigAltProfile OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 3 }
						
					ltp8xONTACSConfigAltVoice1Enable  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 4 }
						
					ltp8xONTACSConfigAltVoice1Number  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 5 }
						
					ltp8xONTACSConfigAltVoice1Password  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 6 }
						
					ltp8xONTACSConfigAltVoice2Enable  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 7 }
						
					ltp8xONTACSConfigAltVoice2Number  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 8 }
						
					ltp8xONTACSConfigAltVoice2Password  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 9 }
						
					ltp8xONTACSConfigAltSIPProxy  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 10 }
						
					ltp8xONTACSConfigAltPPPLogin  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 11 }
						
					ltp8xONTACSConfigAltPPPPassword  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 12 }
						
					ltp8xONTACSConfigAltInternetVlan  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 13 }
						
					ltp8xONTACSConfigAltResetToDefaults  OBJECT-TYPE
						SYNTAX Unsigned32
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSConfigAltEntry 14 }
						
					ltp8xONTACSConfigAltRowStatus	OBJECT-TYPE
						SYNTAX RowStatus
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTACSConfigAltEntry 20 }
						
		ltp8xONTShapingProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTShapingProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 18 }
			
			ltp8xONTShapingProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTShapingProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTShapingID }
				::= { ltp8xONTShapingProfileTable 1 }
			
			Ltp8xONTShapingProfileEntry ::= SEQUENCE {
					ltp8xONTShapingID									Unsigned32,
					ltp8xONTShapingDescription							DisplayString,
					ltp8xONTShapingName									DisplayString,
					ltp8xONTShapingDownstreamOnePolicer					TruthValue,
					ltp8xONTShapingRowStatus							RowStatus
			}
			
				
						ltp8xONTShapingID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileEntry 1 }	
							
						ltp8xONTShapingDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileEntry 2 }
							
						ltp8xONTShapingName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileEntry 3 }
							
						ltp8xONTShapingDownstreamOnePolicer OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileEntry 4 }

						ltp8xONTShapingRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileEntry 10 }
							
							
		--ltp8xONTShapingProfileUpstreamTable OBJECT-TYPE
		--bug 13643 > 13812
		ltp8xONTShapingProfileServicesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTShapingProfileServicesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 19 }
			
			ltp8xONTShapingProfileServicesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTShapingProfileServicesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTShapingID, ltp8xONTShapingCrossConnect }
				::= { ltp8xONTShapingProfileServicesTable 1 }
			
			Ltp8xONTShapingProfileServicesEntry ::= SEQUENCE {
					ltp8xONTShapingCrossConnect							Unsigned32,
					ltp8xONTShapingUpstreamEnable						TruthValue,
					ltp8xONTShapingUpstreamCommitedRate					Unsigned32,
					ltp8xONTShapingUpstreamPeakRate						Unsigned32,
					ltp8xONTShapingDownstreamEnable						TruthValue,
					ltp8xONTShapingDownstreamPeakRate					Unsigned32
			}
							
						ltp8xONTShapingCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileServicesEntry 1 }
							
						ltp8xONTShapingUpstreamEnable OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileServicesEntry 2 }
							
						ltp8xONTShapingUpstreamCommitedRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Commited bandwidth (In Kbit/sec, with granularity of 64), 0-2488320, 2488320"
							::= { ltp8xONTShapingProfileServicesEntry 3 }
							
						ltp8xONTShapingUpstreamPeakRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Peak bandwidth (In Kbit/sec, with granularity of 64), 0-2488320, 2488320"
							::= { ltp8xONTShapingProfileServicesEntry 4 }
							
						ltp8xONTShapingDownstreamEnable OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTShapingProfileServicesEntry 5 }
							
						ltp8xONTShapingDownstreamPeakRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Maximum commited bandwidth (In Kbit/sec, with granularity of 64), 0-2488320, 2488320"
							::= { ltp8xONTShapingProfileServicesEntry 6 }
						
		ltp8xONTACSState OBJECT IDENTIFIER 	::= { ltp8xONT 20 }
		
		ltp8xONTACSStateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONTACSState 1 }
		
		ltp8xONTACSStateEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTACSStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTACSStateBindingID  }
			::= { ltp8xONTACSStateTable 1 }
		
		Ltp8xONTACSStateEntry ::= SEQUENCE {
				ltp8xONTACSStateBindingID		Integer32,
				ltp8xONTACSStateSerial			ONTSerial,
				ltp8xONTACSStateBindingName		DisplayString,
				ltp8xONTACSStateBindingValue	DisplayString,
				ltp8xONTACSStateDeleteRow		Unsigned32
		}
		
							
					ltp8xONTACSStateBindingID OBJECT-TYPE
						SYNTAX  Integer32 (1..256)
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSStateEntry 1 }
						
					ltp8xONTACSStateSerial	OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSStateEntry 2 }
						
					ltp8xONTACSStateBindingName OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSStateEntry 3 }
						
					ltp8xONTACSStateBindingValue OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTACSStateEntry 4 }
						
					ltp8xONTACSStateDeleteRow OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set this to 1 to delete row"
						::= { ltp8xONTACSStateEntry 5 }
						
						
		ltp8xONTACSStateCommitRequest	OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-write
			STATUS  current
			DESCRIPTION "Set this to 1 to commit request"
			::= { ltp8xONTACSState 10 }
			
		ltp8xONTACSStateClear	OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-write
			STATUS  current
			DESCRIPTION "Set this to 1 to clear table"
			::= { ltp8xONTACSState 11 }
			
		ltp8xONTACSStateMaxIndex OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION ""
			::= { ltp8xONTACSState 12 }
			
		ltp8xONTACSStateLastSetIndex OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION ""
			::= { ltp8xONTACSState 13 }
			
		ltp8xONTACSStateLock OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION "Set 1 - to lock table, 2 - to unlock table"
			::= { ltp8xONTACSState 14 }
			
			
		
		ltp8xONTStaticWANConfigTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTStaticWANConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 21 }
		
		ltp8xONTStaticWANConfigEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTStaticWANConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTStaticWANConfigSerial,
						ltp8xONTStaticWANConfigConnection }
			::= { ltp8xONTStaticWANConfigTable 1 }
		
		Ltp8xONTStaticWANConfigEntry ::= SEQUENCE {
				ltp8xONTStaticWANConfigSerial				ONTSerial,
				ltp8xONTStaticWANConfigConnection			Unsigned32,
				ltp8xONTStaticWANConfigDefaultGateway		DisplayString,
				ltp8xONTStaticWANConfigExternalIPAddress	DisplayString,
				ltp8xONTStaticWANConfigSubnetMask			DisplayString
				
		}
		
					ltp8xONTStaticWANConfigSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xONTStaticWANConfigEntry 1 }
						
					ltp8xONTStaticWANConfigConnection OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTStaticWANConfigEntry 2 }
						
					ltp8xONTStaticWANConfigDefaultGateway OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTStaticWANConfigEntry 3 }
						
					ltp8xONTStaticWANConfigExternalIPAddress  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTStaticWANConfigEntry 4 }
						
					ltp8xONTStaticWANConfigSubnetMask  OBJECT-TYPE
						SYNTAX DisplayString
						MAX-ACCESS  read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTStaticWANConfigEntry 5 }
						
		
		ltp8xONTBandwidthManagementProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTBandwidthManagementProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 22 }
			
			ltp8xONTBandwidthManagementProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTBandwidthManagementProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTBandwidthManagementID }
				::= { ltp8xONTBandwidthManagementProfileTable 1 }
			
			Ltp8xONTBandwidthManagementProfileEntry ::= SEQUENCE {
					ltp8xONTBandwidthManagementID										Unsigned32,
					ltp8xONTBandwidthManagementDescription							DisplayString,
					ltp8xONTBandwidthManagementName									DisplayString
			}
			
				
						ltp8xONTBandwidthManagementID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTBandwidthManagementProfileEntry 1 }	
							
						ltp8xONTBandwidthManagementDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTBandwidthManagementProfileEntry 2 }
							
						ltp8xONTBandwidthManagementName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTBandwidthManagementProfileEntry 3 }
							
							
							
		ltp8xONTServiceBandwidthManagementTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTServiceBandwidthManagementEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 23 }
		
		ltp8xONTServiceBandwidthManagementEntry OBJECT-TYPE
			SYNTAX                Ltp8xONTServiceBandwidthManagementEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xONTServiceBandwidthManagementServiceID,
			ltp8xONTServiceBandwidthManagementSlot, 
								 ltp8xONTServiceBandwidthManagementSerial }
								 
			::= { ltp8xONTServiceBandwidthManagementTable 1 }
		
		Ltp8xONTServiceBandwidthManagementEntry ::= SEQUENCE {
				ltp8xONTServiceBandwidthManagementServiceID							Unsigned32,
				ltp8xONTServiceBandwidthManagementSlot								Unsigned32,
				ltp8xONTServiceBandwidthManagementSerial							ONTSerial,
				ltp8xONTServiceBandwidthManagementProfile							Unsigned32
		}
		
					ltp8xONTServiceBandwidthManagementServiceID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceBandwidthManagementEntry 1 }
					
					ltp8xONTServiceBandwidthManagementSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xONTServiceBandwidthManagementEntry 2 }					
						
					ltp8xONTServiceBandwidthManagementSerial OBJECT-TYPE
						SYNTAX  ONTSerial
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceBandwidthManagementEntry 3 }	
						
					ltp8xONTServiceBandwidthManagementProfile OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xONTServiceBandwidthManagementEntry 4 }
						
		ltp8xONTTemplates OBJECT IDENTIFIER 	::= { ltp8xONT 24 }
		
		ltp8xONTTemplateValuesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTTemplateValuesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONTTemplates 1 }
		
			ltp8xONTTemplateValuesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTTemplateValuesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTTemplateValuesID }
				::= { ltp8xONTTemplateValuesTable 1 }
			
			Ltp8xONTTemplateValuesEntry ::= SEQUENCE {
					ltp8xONTTemplateValuesID				Unsigned32,
					ltp8xONTTemplateValuesName				DisplayString,
					ltp8xONTTemplateValuesDescription		DisplayString,
					ltp8xONTTemplateValuesSerial			ONTSerial,
					ltp8xONTTemplateValuesPassword			DisplayString,
					ltp8xONTTemplateValuesFecUp				TruthValue,
					ltp8xONTTemplateValuesManagementProfile	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile0	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile1	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile2	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile3	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile4	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile5	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile6	Unsigned32,
					ltp8xONTTemplateValuesCrossConnectProfile7	Unsigned32,
					ltp8xONTTemplateValuesShapingProfile Unsigned32,
					ltp8xONTTemplateValuesDownstreamBroadcastEnabled TruthValue,
					ltp8xONTTemplateValuesAllocProfile0	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile1	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile2	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile3	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile4	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile5	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile6	Unsigned32,
					ltp8xONTTemplateValuesAllocProfile7	Unsigned32,
					ltp8xONTTemplateValuesPortsProfile Unsigned32,
					ltp8xONTTemplateValuesRFPortEnabled	INTEGER,
					ltp8xONTTemplateValuesScriptingProfile	Unsigned32,
					ltp8xONTTemplateValuesBerInterval Unsigned32,
					ltp8xONTTemplateValuesBerUpdatePeriod Unsigned32,
					ltp8xONTTemplateValuesOMCIErrorTolerant TruthValue,
					ltp8xONTTemplateValuesRowStatus			RowStatus
			}
			
						ltp8xONTTemplateValuesID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Template index"
							::= { ltp8xONTTemplateValuesEntry 1 }
							
						ltp8xONTTemplateValuesName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Template index"
							::= { ltp8xONTTemplateValuesEntry 2 }
							
						ltp8xONTTemplateValuesDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Template index"
							::= { ltp8xONTTemplateValuesEntry 3 }
						
						ltp8xONTTemplateValuesSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateValuesEntry 4 }
							
						ltp8xONTTemplateValuesPassword  OBJECT-TYPE
							SYNTAX DisplayString
							MAX-ACCESS  read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateValuesEntry 6 }
							
						ltp8xONTTemplateValuesFecUp OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateValuesEntry 7 }
							
						ltp8xONTTemplateValuesManagementProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 9 }
						
						ltp8xONTTemplateValuesCrossConnectProfile0 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 11 }
							
						ltp8xONTTemplateValuesCrossConnectProfile1 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 12 }
							
						ltp8xONTTemplateValuesCrossConnectProfile2 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 13 }
							
						ltp8xONTTemplateValuesCrossConnectProfile3 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 14 }
							
						ltp8xONTTemplateValuesCrossConnectProfile4 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 15 }
							
						ltp8xONTTemplateValuesCrossConnectProfile5 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 16 }
							
						ltp8xONTTemplateValuesCrossConnectProfile6 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 17 }
							
						ltp8xONTTemplateValuesCrossConnectProfile7 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 18 }
							
						ltp8xONTTemplateValuesShapingProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateValuesEntry 19 }
						
						ltp8xONTTemplateValuesDownstreamBroadcastEnabled OBJECT-TYPE
							SYNTAX	TruthValue
							MAX-ACCESS	read-write
							STATUS current
							DESCRIPTION "Is broadcast enabled for this ONT"
							::= { ltp8xONTTemplateValuesEntry 22 }
							
						ltp8xONTTemplateValuesAllocProfile0 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 23 }
							
						ltp8xONTTemplateValuesAllocProfile1 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 24 }
							
						ltp8xONTTemplateValuesAllocProfile2 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 25 }
							
						ltp8xONTTemplateValuesAllocProfile3 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 26 }
							
						ltp8xONTTemplateValuesAllocProfile4 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 27 }
							
						ltp8xONTTemplateValuesAllocProfile5 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 28 }
							
						ltp8xONTTemplateValuesAllocProfile6 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 29 }
							
						ltp8xONTTemplateValuesAllocProfile7 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 30 }
							
						ltp8xONTTemplateValuesPortsProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateValuesEntry 31 }
							
						ltp8xONTTemplateValuesRFPortEnabled OBJECT-TYPE
							SYNTAX  INTEGER
							{
								disabled(0),
								enabled(1),
								noChange(2)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateValuesEntry 32 }
							
						ltp8xONTTemplateValuesScriptingProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateValuesEntry 36 }
							
						ltp8xONTTemplateValuesBerInterval OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "BER interval value to send to ONT."
							::= { ltp8xONTTemplateValuesEntry 37 }
							
						ltp8xONTTemplateValuesBerUpdatePeriod OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Mininal time between two consecutive BER mesuarements, sec."
							::= { ltp8xONTTemplateValuesEntry 38 }
							
						ltp8xONTTemplateValuesOMCIErrorTolerant OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Continue configuration after OMCI errors."
							::= { ltp8xONTTemplateValuesEntry 39 }
							
						ltp8xONTTemplateValuesRowStatus OBJECT-TYPE
							SYNTAX	RowStatus
							MAX-ACCESS	read-write
							STATUS	current
							DESCRIPTION " "
							::= { ltp8xONTTemplateValuesEntry 100 }
							
			ltp8xONTTemplateOverridesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTTemplateOverridesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONTTemplates 2 }
		
			ltp8xONTTemplateOverridesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTTemplateOverridesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTTemplateValuesID }
				::= { ltp8xONTTemplateOverridesTable 1 }
			
			Ltp8xONTTemplateOverridesEntry ::= SEQUENCE {
					ltp8xONTTemplateOverridesSerial			TruthValue,
					ltp8xONTTemplateOverridesPassword			TruthValue,
					ltp8xONTTemplateOverridesFecUp				TruthValue,
					ltp8xONTTemplateOverridesManagementProfile	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile0	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile1	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile2	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile3	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile4	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile5	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile6	TruthValue,
					ltp8xONTTemplateOverridesCrossConnectProfile7	TruthValue,
					ltp8xONTTemplateOverridesShapingProfile TruthValue,
					ltp8xONTTemplateOverridesDownstreamBroadcastEnabled TruthValue,
					ltp8xONTTemplateOverridesAllocProfile0	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile1	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile2	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile3	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile4	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile5	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile6	TruthValue,
					ltp8xONTTemplateOverridesAllocProfile7	TruthValue,
					ltp8xONTTemplateOverridesPortsProfile TruthValue,
					ltp8xONTTemplateOverridesRFPortEnabled	TruthValue,
					ltp8xONTTemplateOverridesScriptingProfile	TruthValue,
					ltp8xONTTemplateOverridesBerInterval TruthValue,
					ltp8xONTTemplateOverridesBerUpdatePeriod TruthValue,
					ltp8xONTTemplateOverridesOMCIErrorTolerant TruthValue
			}
						
						ltp8xONTTemplateOverridesSerial OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateOverridesEntry 4 }
							
						ltp8xONTTemplateOverridesPassword  OBJECT-TYPE
							SYNTAX TruthValue
							MAX-ACCESS  read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateOverridesEntry 6 }
							
						ltp8xONTTemplateOverridesFecUp OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateOverridesEntry 7 }
							
						ltp8xONTTemplateOverridesManagementProfile OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 9 }
						
						ltp8xONTTemplateOverridesCrossConnectProfile0 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 11 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile1 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 12 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile2 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 13 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile3 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 14 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile4 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 15 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile5 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 16 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile6 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 17 }
							
						ltp8xONTTemplateOverridesCrossConnectProfile7 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 18 }
							
						ltp8xONTTemplateOverridesShapingProfile OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateOverridesEntry 19 }
						
						ltp8xONTTemplateOverridesDownstreamBroadcastEnabled OBJECT-TYPE
							SYNTAX	TruthValue
							MAX-ACCESS	read-write
							STATUS current
							DESCRIPTION "Is broadcast enabled for this ONT"
							::= { ltp8xONTTemplateOverridesEntry 22 }
							
						ltp8xONTTemplateOverridesAllocProfile0 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 23 }
							
						ltp8xONTTemplateOverridesAllocProfile1 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 24 }
							
						ltp8xONTTemplateOverridesAllocProfile2 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 25 }
							
						ltp8xONTTemplateOverridesAllocProfile3 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 26 }
							
						ltp8xONTTemplateOverridesAllocProfile4 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 27 }
							
						ltp8xONTTemplateOverridesAllocProfile5 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 28 }
							
						ltp8xONTTemplateOverridesAllocProfile6 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 29 }
							
						ltp8xONTTemplateOverridesAllocProfile7 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 30 }
							
						ltp8xONTTemplateOverridesPortsProfile OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateOverridesEntry 31 }
							
						ltp8xONTTemplateOverridesRFPortEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTTemplateOverridesEntry 32 }
							
						ltp8xONTTemplateOverridesScriptingProfile OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Value 65535 means that profile is not assigned."
							::= { ltp8xONTTemplateOverridesEntry 36 }
							
						ltp8xONTTemplateOverridesBerInterval OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "BER interval value to send to ONT."
							::= { ltp8xONTTemplateOverridesEntry 37 }
							
						ltp8xONTTemplateOverridesBerUpdatePeriod OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Mininal time between two consecutive BER mesuarements, sec."
							::= { ltp8xONTTemplateOverridesEntry 38 }
							
						ltp8xONTTemplateOverridesOMCIErrorTolerant OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Continue configuration after OMCI errors."
							::= { ltp8xONTTemplateOverridesEntry 39 }
							
							
			ltp8xONTTemplateServicesValuesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTTemplateServicesValuesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONTTemplates 10 }
		
			ltp8xONTTemplateServicesValuesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTTemplateServicesValuesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTTemplateValuesID,
								ltp8xONTTemplateServicesValuesServiceID }
				::= { ltp8xONTTemplateServicesValuesTable 1 }
			
			Ltp8xONTTemplateServicesValuesEntry ::= SEQUENCE {
					ltp8xONTTemplateServicesValuesServiceID				Unsigned32,
					ltp8xONTTemplateServicesValuesCrossConnectProfile	Unsigned32,
					ltp8xONTTemplateServicesValuesDBAProfile			Unsigned32
					
			}
							
						ltp8xONTTemplateServicesValuesServiceID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesValuesEntry 1 }
							
						ltp8xONTTemplateServicesValuesCrossConnectProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesValuesEntry 2 }
							
						ltp8xONTTemplateServicesValuesDBAProfile			OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesValuesEntry 3 }
							
			ltp8xONTTemplateServicesOverridesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTTemplateServicesOverridesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONTTemplates 11 }
		
			ltp8xONTTemplateServicesOverridesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTTemplateServicesOverridesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTTemplateValuesID,
								ltp8xONTTemplateServicesOverridesServiceID }
				::= { ltp8xONTTemplateServicesOverridesTable 1 }
			
			Ltp8xONTTemplateServicesOverridesEntry ::= SEQUENCE {
					ltp8xONTTemplateServicesOverridesServiceID				Unsigned32,
					ltp8xONTTemplateServicesOverridesCrossConnectProfile	TruthValue,
					ltp8xONTTemplateServicesOverridesDBAProfile			TruthValue
					
			}
							
						ltp8xONTTemplateServicesOverridesServiceID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesOverridesEntry 1 }
							
						ltp8xONTTemplateServicesOverridesCrossConnectProfile OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesOverridesEntry 2 }
							
						ltp8xONTTemplateServicesOverridesDBAProfile			OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTTemplateServicesOverridesEntry 3 }
							
							
		ltp8xONTFullServicesConfigTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTFullServicesConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 25 }
		
			ltp8xONTFullServicesConfigEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFullServicesConfigEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFullServicesConfigSlot,
								ltp8xONTFullServicesConfigSerial,
								ltp8xONTFullServicesConfigServiceID }
				::= { ltp8xONTFullServicesConfigTable 1 }
			
			Ltp8xONTFullServicesConfigEntry ::= SEQUENCE {
					ltp8xONTFullServicesConfigSlot					Unsigned32,
					ltp8xONTFullServicesConfigSerial				ONTSerial,
					ltp8xONTFullServicesConfigServiceID				Unsigned32,
					ltp8xONTFullServicesConfigCrossConnectProfile	Unsigned32,
					ltp8xONTFullServicesConfigDBAProfile			Unsigned32
					
			}
			
						ltp8xONTFullServicesConfigSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Slot index, for LTP - always 1"
							::= { ltp8xONTFullServicesConfigEntry 1 }
							
						ltp8xONTFullServicesConfigSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFullServicesConfigEntry 2 }
							
						ltp8xONTFullServicesConfigServiceID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFullServicesConfigEntry 3 }
							
						ltp8xONTFullServicesConfigCrossConnectProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFullServicesConfigEntry 4 }
							
						ltp8xONTFullServicesConfigDBAProfile OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFullServicesConfigEntry 5 }
							
		ltp8xONTSelectiveTunnelTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTSelectiveTunnelEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 26 }
		
			ltp8xONTSelectiveTunnelEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTSelectiveTunnelEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTSelectiveTunnelSlot,
								ltp8xONTSelectiveTunnelSerial,
								ltp8xONTSelectiveTunnelServiceID,
								ltp8xONTSelectiveTunnelUVIDIndex }
				::= { ltp8xONTSelectiveTunnelTable 1 }
			
			Ltp8xONTSelectiveTunnelEntry ::= SEQUENCE {
					ltp8xONTSelectiveTunnelSlot					Unsigned32,
					ltp8xONTSelectiveTunnelSerial				ONTSerial,
					ltp8xONTSelectiveTunnelServiceID			Unsigned32,
					ltp8xONTSelectiveTunnelUVIDIndex			Unsigned32,
					ltp8xONTSelectiveTunnelUVID					Integer32
					
			}
			
						ltp8xONTSelectiveTunnelSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Slot index, for LTP - always 1"
							::= { ltp8xONTSelectiveTunnelEntry 1 }
							
						ltp8xONTSelectiveTunnelSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTSelectiveTunnelEntry 2 }
							
						ltp8xONTSelectiveTunnelServiceID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTSelectiveTunnelEntry 3 }
							
						ltp8xONTSelectiveTunnelUVIDIndex OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTSelectiveTunnelEntry 4 }
							
						ltp8xONTSelectiveTunnelUVID OBJECT-TYPE
							SYNTAX  Integer32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "-1 - means that uvid not specified"
							::= { ltp8xONTSelectiveTunnelEntry 5 }

			
		ltp8xONTFirmwares OBJECT IDENTIFIER 	::= { ltp8xONT 30 }
		
			ltp8xONTFirmwaresFilesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFirmwaresFilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 1 }
			
			ltp8xONTFirmwaresFilesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFirmwaresFilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFirmwaresFilesEntryID  }
				::= { ltp8xONTFirmwaresFilesTable 1 }
			
			Ltp8xONTFirmwaresFilesEntry ::= SEQUENCE {
					ltp8xONTFirmwaresFilesEntryID		Integer32,
					ltp8xONTFirmwaresFilesName			DisplayString,
					ltp8xONTFirmwaresFilesVersion		DisplayString,
					ltp8xONTFirmwaresFilesHardware		DisplayString,
					ltp8xONTFirmwaresFilesDelete		Unsigned32
			}
			

						ltp8xONTFirmwaresFilesEntryID OBJECT-TYPE
							SYNTAX  Integer32 (1..64)
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresFilesEntry 1 }
							
						ltp8xONTFirmwaresFilesName	OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresFilesEntry 2 }
							
						ltp8xONTFirmwaresFilesVersion OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresFilesEntry 3 }
							
						ltp8xONTFirmwaresFilesHardware OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresFilesEntry 4 }
							
						ltp8xONTFirmwaresFilesDelete OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set this to 1 to delete file"
							::= { ltp8xONTFirmwaresFilesEntry 10 }
							
							
							
			ltp8xONTFirmwaresTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFirmwaresEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 2 }
			
			ltp8xONTFirmwaresEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFirmwaresEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFirmwaresEntryID  }
				::= { ltp8xONTFirmwaresTable 1 }
			
			Ltp8xONTFirmwaresEntry ::= SEQUENCE {
					ltp8xONTFirmwaresEntryID		Integer32,
					ltp8xONTFirmwaresName			DisplayString,
					ltp8xONTFirmwaresVersion		DisplayString,
					ltp8xONTFirmwaresHardware		DisplayString,
					ltp8xONTFirmwaresURL			DisplayString,
					ltp8xONTFirmwaresScheduler		TruthValue,
					ltp8xONTFirmwaresSafeMode		TruthValue,
					ltp8xONTFirmwaresDowngrade		TruthValue,
					ltp8xONTFirmwaresRowStatus		RowStatus
			}
			
						ltp8xONTFirmwaresEntryID OBJECT-TYPE
							SYNTAX  Integer32 (1..64)
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 1 }
							
						ltp8xONTFirmwaresName	OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "File name for firmware"
							::= { ltp8xONTFirmwaresEntry 2 }
							
						ltp8xONTFirmwaresVersion OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 3 }
							
						ltp8xONTFirmwaresHardware OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 4 }
							
						ltp8xONTFirmwaresURL OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 5 }
							
						ltp8xONTFirmwaresScheduler OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 6 }
							
						ltp8xONTFirmwaresSafeMode OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 7 }
							
						ltp8xONTFirmwaresDowngrade OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 8 }
							
						ltp8xONTFirmwaresRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-create
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresEntry 10 }
							
							
		ltp8xONTFirmwaresProfilesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFirmwaresProfilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 3 }
			
			ltp8xONTFirmwaresProfilesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFirmwaresProfilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFirmwaresProfilesFWID, 
									ltp8xONTFirmwaresProfilesName }
				::= { ltp8xONTFirmwaresProfilesTable 1 }
			
			Ltp8xONTFirmwaresProfilesEntry ::= SEQUENCE {
					ltp8xONTFirmwaresProfilesFWID			Integer32,
					ltp8xONTFirmwaresProfilesName			DisplayString,
					ltp8xONTFirmwaresProfilesRowStatus		RowStatus
			}
			
						ltp8xONTFirmwaresProfilesFWID OBJECT-TYPE
							SYNTAX  Integer32 (1..64)
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresProfilesEntry 1 }
							
						ltp8xONTFirmwaresProfilesName	OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFirmwaresProfilesEntry 2 }
							
						ltp8xONTFirmwaresProfilesRowStatus	OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-create
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTFirmwaresProfilesEntry 10 }
							
		ltp8xONTFirmwaresSchedulerConfig OBJECT IDENTIFIER 	::= { ltp8xONTFirmwares 5 }
		
			ltp8xONTFirmwaresSchedulerDailyFrom OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Time in format HH:MM"
				::= { ltp8xONTFirmwaresSchedulerConfig 1 }
				
			ltp8xONTFirmwaresSchedulerDailyTo OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Time in format HH:MM"
				::= { ltp8xONTFirmwaresSchedulerConfig 2 }
				
			ltp8xONTFirmwaresSchedulerPeriodFrom OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Date in format YYYY-MM-DD"
				::= { ltp8xONTFirmwaresSchedulerConfig 3 }
				
			ltp8xONTFirmwaresSchedulerPeriodTo OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Date in format YYYY-MM-DD"
				::= { ltp8xONTFirmwaresSchedulerConfig 4 }
				
			ltp8xONTFirmwaresSchedulerWeeklyFrom OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Day of a week (1-7)"
				::= { ltp8xONTFirmwaresSchedulerConfig 5 }
				
			ltp8xONTFirmwaresSchedulerWeeklyTo OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Day of a week (1-7)"
				::= { ltp8xONTFirmwaresSchedulerConfig 6 }
				
				
		ltp8xONTFirmwaresSpecificsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFirmwaresSpecificsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 7 }
			
			ltp8xONTFirmwaresSpecificsEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFirmwaresSpecificsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFirmwaresSpecificsName  }
				::= { ltp8xONTFirmwaresSpecificsTable 1 }
			
			Ltp8xONTFirmwaresSpecificsEntry ::= SEQUENCE {
					ltp8xONTFirmwaresSpecificsName			DisplayString,
					ltp8xONTFirmwaresSpecificsVersion		DisplayString,
					ltp8xONTFirmwaresSpecificsHardware		DisplayString,
					ltp8xONTFirmwaresSpecificsVendor		DisplayString,
					ltp8xONTFirmwaresSpecificsRowStatus		RowStatus
			}
			
							
						ltp8xONTFirmwaresSpecificsName	OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..255))
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresSpecificsEntry 1 }
							
						ltp8xONTFirmwaresSpecificsVersion OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresSpecificsEntry 2 }
							
						ltp8xONTFirmwaresSpecificsHardware OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresSpecificsEntry 3 }
							
						ltp8xONTFirmwaresSpecificsVendor OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFirmwaresSpecificsEntry 4 }
							
						ltp8xONTFirmwaresSpecificsRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set this to 1 to delete file"
							::= { ltp8xONTFirmwaresSpecificsEntry 10 }
							
		ltp8xONTFirmwaresVersionPriorityFile OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-write
			STATUS  current
			DESCRIPTION " "
			::= { ltp8xONTFirmwares 8 }
							
		ltp8xONTFirmwaresDownload OBJECT IDENTIFIER 	::= { ltp8xONTFirmwares 10 }
		
			ltp8xONTFirmwaresDownloadIPAddress OBJECT-TYPE
				SYNTAX  IpAddress
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "TFTP server address"
				::= { ltp8xONTFirmwaresDownload 1 }
				
			ltp8xONTFirmwaresDownloadPath OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION " "
				::= { ltp8xONTFirmwaresDownload 2 }
				
			ltp8xONTFirmwaresDownloadAction OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Set to 1 to initiate download"
				::= { ltp8xONTFirmwaresDownload 3 }
				
			ltp8xONTFirmwaresDownloadResult OBJECT-TYPE
				SYNTAX  INTEGER {
					notActive(1),
					inProgess(2),
					success(3),
					failed(4)
				}
				MAX-ACCESS read-only
				STATUS  current
				DESCRIPTION ""
				::= { ltp8xONTFirmwaresDownload 4 }
				
			ltp8xONTFirmwaresDownloadProtocol OBJECT-TYPE
				SYNTAX  INTEGER {
					tftp(1),
					http(2)
				}
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION ""
				::= { ltp8xONTFirmwaresDownload 5 }
				
			ltp8xONTFirmwaresDownloadPort OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION " "
				::= { ltp8xONTFirmwaresDownload 6 }
				
		ltp8xONTFirmwareUpdateViaOMCI OBJECT IDENTIFIER 	::= { ltp8xONTFirmwares 11 }
		
			ltp8xONTFirmwareUpdateViaOMCISlot OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Slot number. For LTP8X, always 1"
				::= { ltp8xONTFirmwareUpdateViaOMCI 1 }
				
			ltp8xONTFirmwareUpdateViaOMCISerial OBJECT-TYPE
				SYNTAX  ONTSerial
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Serial number of ONT"
				::= { ltp8xONTFirmwareUpdateViaOMCI 2 }
				
			ltp8xONTFirmwareUpdateViaOMCIFilename OBJECT-TYPE
				SYNTAX  DisplayString
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Firmware file name as it shown in ltp8xONTFirmwaresFilesTable"
				::= { ltp8xONTFirmwareUpdateViaOMCI 3 }
				
			ltp8xONTFirmwareUpdateViaOMCIAction OBJECT-TYPE
				SYNTAX  Unsigned32
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION "Set this to 1 to create update task"
				::= { ltp8xONTFirmwareUpdateViaOMCI 4 }
				
				
				
			ltp8xONTFWUpdateSchedulerTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTFWUpdateSchedulerEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 20 }
			
			ltp8xONTFWUpdateSchedulerEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTFWUpdateSchedulerEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTFWUpdateSchedulerSlot,
									ltp8xONTFWUpdateSchedulerTaskID }
				::= { ltp8xONTFWUpdateSchedulerTable 1 }
				
			
			Ltp8xONTFWUpdateSchedulerEntry ::= SEQUENCE {
					ltp8xONTFWUpdateSchedulerSlot								Unsigned32,
					ltp8xONTFWUpdateSchedulerTaskID								Unsigned32,
					ltp8xONTFWUpdateSchedulerSerial								ONTSerial,
					ltp8xONTFWUpdateSchedulerTaskState							INTEGER,
					ltp8xONTFWUpdateSchedulerFilename							DisplayString,
					ltp8xONTFWUpdateSchedulerTries								Unsigned32,
					ltp8xONTFWUpdateSchedulerONTChannel							Unsigned32,
					ltp8xONTFWUpdateSchedulerONTId								Unsigned32,
					ltp8xONTFWUpdateSchedulerUseSerial							TruthValue,
					ltp8xONTFWUpdateSchedulerRowStatus							RowStatus
			}
			
						ltp8xONTFWUpdateSchedulerSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 1 }
							
						ltp8xONTFWUpdateSchedulerTaskID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 2 }	
							
						ltp8xONTFWUpdateSchedulerSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 3 }
							
						ltp8xONTFWUpdateSchedulerTaskState OBJECT-TYPE
							SYNTAX  INTEGER {
								scheduled(0),
								inProgress(1),
								failed(2),
								done(3),
								unnecessary(4)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 4 }
							
						ltp8xONTFWUpdateSchedulerFilename OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 5 }
							
						ltp8xONTFWUpdateSchedulerTries OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 6 }
							
						ltp8xONTFWUpdateSchedulerONTChannel	OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 7 }
							
						ltp8xONTFWUpdateSchedulerONTId	OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 8 }
							
						ltp8xONTFWUpdateSchedulerUseSerial	OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 9 }
							
						ltp8xONTFWUpdateSchedulerRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTFWUpdateSchedulerEntry 10 }
							
			ltp8xONTAutoUpdateTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTAutoUpdateEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTFirmwares 22 }
			
			ltp8xONTAutoUpdateEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTAutoUpdateEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTAutoUpdateDescription }
				::= { ltp8xONTAutoUpdateTable 1 }
				
			
			Ltp8xONTAutoUpdateEntry ::= SEQUENCE {
					ltp8xONTAutoUpdateDescription						DisplayString,
					ltp8xONTAutoUpdateEquipmentID						DisplayString,
					ltp8xONTAutoUpdateFirmwareVersion					DisplayString,
					ltp8xONTAutoUpdateFilename							DisplayString,
					ltp8xONTAutoUpdateMode								INTEGER,
                    ltp8xONTAutoUpdateFirmwareVersionMatches			TruthValue,
                    ltp8xONTAutoUpdateDowngrade             			TruthValue,
					ltp8xONTAutoUpdateRowStatus							RowStatus
			}
							
						ltp8xONTAutoUpdateDescription OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..31))
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 1 }	
							
						ltp8xONTAutoUpdateEquipmentID OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..31))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 2 }
							
						ltp8xONTAutoUpdateFirmwareVersion OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..31))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 3 }
							
						ltp8xONTAutoUpdateFilename OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 4 }
							
						ltp8xONTAutoUpdateMode OBJECT-TYPE
							SYNTAX  INTEGER {
								disable(0),   
								immediate(1),
								postpone(2),
								global(3)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 5 }
                            
                        ltp8xONTAutoUpdateFirmwareVersionMatches OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTAutoUpdateEntry 6 }
                            
                        ltp8xONTAutoUpdateDowngrade OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 7 }
							
						ltp8xONTAutoUpdateRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAutoUpdateEntry 10 }
							
							
			ltp8xONTAutoUpdateEnabled OBJECT-TYPE
				SYNTAX  INTEGER {
					disabled(0),
					immediate(1),
					postpone(2) }
				MAX-ACCESS read-write
				STATUS  current
				DESCRIPTION " "
				::= { ltp8xONTFirmwares 23 }
			
				
			ltp8xONTAllocProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTAllocProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 40 }
			
			ltp8xONTAllocProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTAllocProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTAllocID }
				::= { ltp8xONTAllocProfileTable 1 }
			
			Ltp8xONTAllocProfileEntry ::= SEQUENCE {
					ltp8xONTAllocID									Unsigned32,
					ltp8xONTAllocDescription						DisplayString,
					ltp8xONTAllocName								DisplayString,
					ltp8xONTAllocServiceClass						INTEGER,
					ltp8xONTAllocStatusReporting					INTEGER,
					ltp8xONTAllocSize								Unsigned32,
					ltp8xONTAllocPeriod								Unsigned32,
					ltp8xONTAllocFixedBandwidth						Unsigned32,
					ltp8xONTAllocGuaranteedBandwidth				Unsigned32,
					ltp8xONTAllocBestEffortBandwidth				Unsigned32,
					ltp8xONTAllocRowStatus							RowStatus
			}
			
				
						ltp8xONTAllocID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 1 }	
							
						ltp8xONTAllocDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 2 }
							
						ltp8xONTAllocName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 3 }
							
						ltp8xONTAllocServiceClass OBJECT-TYPE
							SYNTAX  INTEGER {
								data(0),
								voip(2),
								cbr(3),
								periodicAllocation(4),
								type5(5)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 4 }
							
						ltp8xONTAllocStatusReporting OBJECT-TYPE
							SYNTAX  INTEGER {
								nsr(0),
								type0(1),
								type1(2)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 5 }
							
						ltp8xONTAllocSize OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-194400 Default: 48600"
							::= { ltp8xONTAllocProfileEntry 6 }	
							
						ltp8xONTAllocPeriod OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0,1,2,4,8,16,32 Default: 8"
							::= { ltp8xONTAllocProfileEntry 7 }	
					
						ltp8xONTAllocFixedBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-269248  Default:0"
							::= { ltp8xONTAllocProfileEntry 8 }
							
						ltp8xONTAllocGuaranteedBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-1273856 Default:64"
							::= { ltp8xONTAllocProfileEntry 9 }
							
						ltp8xONTAllocBestEffortBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-1273856 Default:1273856"
							::= { ltp8xONTAllocProfileEntry 10 }
							
						ltp8xONTAllocRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTAllocProfileEntry 20 }
							
							
			ltp8xONTPortsProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTPortsProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 41 }
			
			ltp8xONTPortsProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTPortsProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTPortsID }
				::= { ltp8xONTPortsProfileTable 1 }
			
			Ltp8xONTPortsProfileEntry ::= SEQUENCE {
					ltp8xONTPortsID									Unsigned32,
					ltp8xONTPortsDescription						DisplayString,
					ltp8xONTPortsName								DisplayString,
					ltp8xONTPortsIGMPVersion						Unsigned32,
					ltp8xONTPortsIGMPUpstreamMode					INTEGER,
					ltp8xONTPortsIGMPImmediateLeave					TruthValue,
					ltp8xONTPortsIGMPRobustness						Unsigned32,
					ltp8xONTPortsIGMPQuerierIP						IpAddress,
					ltp8xONTPortsIGMPQueryInterval					Unsigned32,
					ltp8xONTPortsIGMPQueryMaxResponseTime			Unsigned32,
					ltp8xONTPortsIGMPLastMemberQueryInterval		Unsigned32,
					ltp8xONTPortsVEIPMulticastEnable				TruthValue,
					ltp8xONTPortsVEIPIGMPUpstreamVID				Unsigned32,
					ltp8xONTPortsVEIPIGMPUpstreamPriority			Unsigned32,
					ltp8xONTPortsVEIPIGMPUpstreamTagControl			INTEGER,
					ltp8xONTPortsVEIPMaxGroups						Unsigned32,
					ltp8xONTPortsVEIPMaxMulticastBandwidth			Unsigned32,
					ltp8xONTPortsRowStatus							RowStatus,
					ltp8xONTPortsVEIPIGMPDownstreamVID				Unsigned32,
					ltp8xONTPortsVEIPIGMPDownstreamPriority			Unsigned32,
					ltp8xONTPortsVEIPIGMPDownstreamTagControl		INTEGER,
					
					ltp8xONTPortsMulticastIPVersion					INTEGER,
									
					ltp8xONTPortsMLDVersion							Unsigned32,
					ltp8xONTPortsMLDUpstreamMode					INTEGER,
					ltp8xONTPortsMLDImmediateLeave					TruthValue,
					ltp8xONTPortsMLDRobustness						Unsigned32,
					ltp8xONTPortsMLDQuerierIP						Ipv6Address,
					ltp8xONTPortsMLDQueryInterval					Unsigned32,
					ltp8xONTPortsMLDQueryMaxResponseTime			Unsigned32,
					ltp8xONTPortsMLDLastMemberQueryInterval			Unsigned32
					
					
			}
			
				
						ltp8xONTPortsID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileEntry 1 }	
							
						ltp8xONTPortsDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileEntry 2 }
							
						ltp8xONTPortsName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileEntry 3 }
							
						ltp8xONTPortsIGMPVersion OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values : 1,2,3. 0 means that MLD is active"
							::= { ltp8xONTPortsProfileEntry 4 }
							
						ltp8xONTPortsIGMPUpstreamMode OBJECT-TYPE
							SYNTAX  INTEGER {
								snooping(0),
								spr(1),
								proxy(2)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 5 }
							
						ltp8xONTPortsIGMPImmediateLeave OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 6 }
							
						ltp8xONTPortsIGMPRobustness OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 7 }
							
						ltp8xONTPortsIGMPQuerierIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 8 }
							
						ltp8xONTPortsIGMPQueryInterval OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 9 }
							
						ltp8xONTPortsIGMPQueryMaxResponseTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 10 }
							
						ltp8xONTPortsIGMPLastMemberQueryInterval OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 11 }
							
						ltp8xONTPortsVEIPMulticastEnable OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 12 }
							
						ltp8xONTPortsVEIPIGMPUpstreamVID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 13 }
							
						ltp8xONTPortsVEIPIGMPUpstreamPriority OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 14 }
							
						ltp8xONTPortsVEIPIGMPUpstreamTagControl OBJECT-TYPE
							SYNTAX  INTEGER {
								pass(0),
								addTag(1),
								replaceTag(2),
								replaceVid(3)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 15 }
							
						ltp8xONTPortsVEIPMaxGroups OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 16 }
							
						ltp8xONTPortsVEIPMaxMulticastBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 17 }
							
						ltp8xONTPortsRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileEntry 20 }
							
						ltp8xONTPortsVEIPIGMPDownstreamVID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 21 }
							
						ltp8xONTPortsVEIPIGMPDownstreamPriority OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 22 }
							
						ltp8xONTPortsVEIPIGMPDownstreamTagControl OBJECT-TYPE
							SYNTAX  INTEGER {
								pass(0),
								removeTag(1),
								addTag(2),
								replaceTag(3),
								replaceVid(4),
								addTagSubscriberInfo(5),
								replaceTagSubscriberInfo(6),
								replaceVidSubscriberInfo(7)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 23 }
						
						ltp8xONTPortsMulticastIPVersion OBJECT-TYPE
							SYNTAX  INTEGER {
								ipv4(0),
								ipv6(1)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 30 }
							
						ltp8xONTPortsMLDVersion OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Values : 1,2,3"
							::= { ltp8xONTPortsProfileEntry 31 }
							
						ltp8xONTPortsMLDUpstreamMode OBJECT-TYPE
							SYNTAX  INTEGER {
								snooping(0),
								spr(1),
								proxy(2)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 32 }
							
						ltp8xONTPortsMLDImmediateLeave OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 33 }
							
						ltp8xONTPortsMLDRobustness OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 34 }
							
						ltp8xONTPortsMLDQuerierIP OBJECT-TYPE
							SYNTAX  Ipv6Address
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 35 }
							
						ltp8xONTPortsMLDQueryInterval OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 36 }
							
						ltp8xONTPortsMLDQueryMaxResponseTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 37 }
							
						ltp8xONTPortsMLDLastMemberQueryInterval OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileEntry 38 }
							
							
			ltp8xONTPortsProfileUNITable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTPortsProfileUNIEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 42 }
			
			ltp8xONTPortsProfileUNIEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTPortsProfileUNIEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTPortsID,
									ltp8xONTPortsUNIPort }
				::= { ltp8xONTPortsProfileUNITable 1 }
			
			Ltp8xONTPortsProfileUNIEntry ::= SEQUENCE {
					ltp8xONTPortsUNIPort							Unsigned32,
					ltp8xONTPortsUNIBridgeGroup						INTEGER,
					ltp8xONTPortsUNIMulticastEnabled				TruthValue,
					ltp8xONTPortsUNIIGMPUpstreamVID					Unsigned32,
					ltp8xONTPortsUNIIGMPUpstreamPriority			Unsigned32,
					ltp8xONTPortsUNIIGMPUpstreamTagControl			INTEGER,
					ltp8xONTPortsUNIMaxGroups						Unsigned32,
					ltp8xONTPortsUNIMaxMulticastBandwidth			Unsigned32,
					ltp8xONTPortsUNIShapingDownstreamEnabled		TruthValue,
					ltp8xONTPortsUNIShapingDownstreamCommitedRate	Unsigned32,
					ltp8xONTPortsUNIShapingDownstreamPeakRate		Unsigned32,
					ltp8xONTPortsUNIShapingUpstreamEnabled			TruthValue,
					ltp8xONTPortsUNIShapingUpstreamCommitedRate		Unsigned32,
					ltp8xONTPortsUNIShapingUpstreamPeakRate			Unsigned32,
					ltp8xONTPortsUNIIGMPDownstreamVID				Unsigned32,
					ltp8xONTPortsUNIIGMPDownstreamPriority			Unsigned32,
					ltp8xONTPortsUNIIGMPDownstreamTagControl		INTEGER
			}
			
				
						ltp8xONTPortsUNIPort OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileUNIEntry 1 }	
							
						ltp8xONTPortsUNIBridgeGroup OBJECT-TYPE
							SYNTAX  INTEGER {
								routed(0)}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileUNIEntry 2 }
							
						ltp8xONTPortsUNIMulticastEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileUNIEntry 3 }
							
						ltp8xONTPortsUNIIGMPUpstreamVID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 4 }
							
						ltp8xONTPortsUNIIGMPUpstreamPriority OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 5 }
							
						ltp8xONTPortsUNIIGMPUpstreamTagControl OBJECT-TYPE
							SYNTAX  INTEGER {
								pass(0),
								addTag(1),
								replaceTag(2),
								replaceVid(3)
							}							
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 6 }
							
						ltp8xONTPortsUNIMaxGroups OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 7 }
							
						ltp8xONTPortsUNIMaxMulticastBandwidth OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 8 }
							
						ltp8xONTPortsUNIShapingDownstreamEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 9 }
							
						ltp8xONTPortsUNIShapingDownstreamCommitedRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 10 }
							
						ltp8xONTPortsUNIShapingDownstreamPeakRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 11 }
							
						ltp8xONTPortsUNIShapingUpstreamEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 12 }
							
						ltp8xONTPortsUNIShapingUpstreamCommitedRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 13 }
							
						ltp8xONTPortsUNIShapingUpstreamPeakRate OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 14 }
							
						ltp8xONTPortsUNIIGMPDownstreamVID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 15 }
							
						ltp8xONTPortsUNIIGMPDownstreamPriority OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 16 }
							
						ltp8xONTPortsUNIIGMPDownstreamTagControl OBJECT-TYPE
							SYNTAX  INTEGER {
								pass(0),
								removeTag(1),
								addTag(2),
								replaceTag(3),
								replaceVid(4),
								addTagSubscriberInfo(5),
								replaceTagSubscriberInfo(6),
								replaceVidSubscriberInfo(7)
							}							
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileUNIEntry 17 }
							
			ltp8xONTVoiceProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTVoiceProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 43 }
			
			ltp8xONTVoiceProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTVoiceProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTVoiceID }
				::= { ltp8xONTVoiceProfileTable 1 }
			
			Ltp8xONTVoiceProfileEntry ::= SEQUENCE {
					ltp8xONTVoiceID									Unsigned32,
					ltp8xONTVoiceDescription						DisplayString,
					ltp8xONTVoiceName								DisplayString,
					ltp8xONTVoiceCrossConnect						Unsigned32,
					ltp8xONTVoiceRowStatus							RowStatus
			}
			
				
						ltp8xONTVoiceID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTVoiceProfileEntry 1 }	
							
						ltp8xONTVoiceDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTVoiceProfileEntry 2 }
							
						ltp8xONTVoiceName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTVoiceProfileEntry 3 }
							
						ltp8xONTVoiceCrossConnect OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTVoiceProfileEntry 4 }
							
						ltp8xONTVoiceRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTVoiceProfileEntry 10 }
							
		ltp8xONTScriptingProfiles OBJECT IDENTIFIER 	::= { ltp8xONT 44 }
		
			ltp8xONTScriptingProfileTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTScriptingProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTScriptingProfiles 1 }
			
			ltp8xONTScriptingProfileEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTScriptingProfileEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTScriptingID }
				::= { ltp8xONTScriptingProfileTable 1 }
			
			Ltp8xONTScriptingProfileEntry ::= SEQUENCE {
					ltp8xONTScriptingID									Unsigned32,
					ltp8xONTScriptingDescription						DisplayString,
					ltp8xONTScriptingName								DisplayString,
					ltp8xONTScriptingRowStatus							RowStatus
			}
			
						ltp8xONTScriptingID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileEntry 1 }	
							
						ltp8xONTScriptingDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileEntry 2 }
							
						ltp8xONTScriptingName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileEntry 3 }
							
						ltp8xONTScriptingRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileEntry 10 }
							
							
			ltp8xONTScriptingProfileScriptsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTScriptingProfileScriptsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONTScriptingProfiles 2 }
			
			ltp8xONTScriptingProfileScriptsEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTScriptingProfileScriptsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTScriptingID, ltp8xONTScriptingChunkID }
				::= { ltp8xONTScriptingProfileScriptsTable 1 }
			
			Ltp8xONTScriptingProfileScriptsEntry ::= SEQUENCE {
					ltp8xONTScriptingChunkID							Unsigned32,
					ltp8xONTScriptingText								OCTET STRING
			}
			
						ltp8xONTScriptingChunkID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileScriptsEntry 1 }	
							
						ltp8xONTScriptingText OBJECT-TYPE
							SYNTAX  OCTET STRING (SIZE(0..65535))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTScriptingProfileScriptsEntry 2 }
							
		ltp8xONTPortsProfileMCDynamicEntriesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTPortsProfileMCDynamicEntriesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 46 }
			
			ltp8xONTPortsProfileMCDynamicEntriesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTPortsProfileMCDynamicEntriesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTPortsID, ltp8xONTPortsMCEntryID}
				::= { ltp8xONTPortsProfileMCDynamicEntriesTable 1 }
			
			Ltp8xONTPortsProfileMCDynamicEntriesEntry ::= SEQUENCE {
					ltp8xONTPortsMCEntryID							Unsigned32,
					ltp8xONTPortsMCVLANID							Unsigned32,
					ltp8xONTPortsMCFirstGroupIP						IpAddress,
					ltp8xONTPortsMCLastGroupIP						IpAddress
			}
			
				
						ltp8xONTPortsMCEntryID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMCDynamicEntriesEntry 1 }	
							
						ltp8xONTPortsMCVLANID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMCDynamicEntriesEntry 2 }
							
						ltp8xONTPortsMCFirstGroupIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMCDynamicEntriesEntry 3 }
							
						ltp8xONTPortsMCLastGroupIP OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileMCDynamicEntriesEntry 4 }
							
		ltp8xONTPortsProfileMLDDynamicEntriesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xONTPortsProfileMLDDynamicEntriesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xONT 47 }
			
			ltp8xONTPortsProfileMLDDynamicEntriesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTPortsProfileMLDDynamicEntriesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTPortsID, ltp8xONTPortsMLDEntryID}
				::= { ltp8xONTPortsProfileMLDDynamicEntriesTable 1 }
			
			Ltp8xONTPortsProfileMLDDynamicEntriesEntry ::= SEQUENCE {
					ltp8xONTPortsMLDEntryID							Unsigned32,
					ltp8xONTPortsMLDVLANID							Unsigned32,
					ltp8xONTPortsMLDMCFirstGroupIP					Ipv6Address,
					ltp8xONTPortsMLDMCLastGroupIP					Ipv6Address,
					ltp8xONTPortsMLDMCPreviewLength					Unsigned32,
					ltp8xONTPortsMLDMCPreviewRepeatTime				Unsigned32,
					ltp8xONTPortsMLDMCPreviewRepeatCount			Unsigned32,
					ltp8xONTPortsMLDMCPreviewResetTime				Unsigned32
			}
			
						ltp8xONTPortsMLDEntryID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 1 }	
							
						ltp8xONTPortsMLDVLANID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 2 }
							
						ltp8xONTPortsMLDMCFirstGroupIP OBJECT-TYPE
							SYNTAX  Ipv6Address
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 5 }
							
						ltp8xONTPortsMLDMCLastGroupIP OBJECT-TYPE
							SYNTAX  Ipv6Address
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 6 }
							
						ltp8xONTPortsMLDMCPreviewLength OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-65535, seconds"
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 7 }
							
						ltp8xONTPortsMLDMCPreviewRepeatTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-65535, seconds"
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 8 }
							
						ltp8xONTPortsMLDMCPreviewRepeatCount OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "0-65535"
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 9 }
							
						ltp8xONTPortsMLDMCPreviewResetTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Reset hour. 0-24"
							::= { ltp8xONTPortsProfileMLDDynamicEntriesEntry 10 }
							
		ltp8xONTMulticastStatsTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTMulticastStatsEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 50 }
		
			ltp8xONTMulticastStatsEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTMulticastStatsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTMulticastStatsSlot,
								ltp8xONTMulticastStatsONTSerial,
								ltp8xONTMulticastStatsRecordID }
				::= { ltp8xONTMulticastStatsTable 1 }
			
			Ltp8xONTMulticastStatsEntry ::= SEQUENCE {
					ltp8xONTMulticastStatsSlot					Unsigned32,
					ltp8xONTMulticastStatsONTSerial				ONTSerial,
					ltp8xONTMulticastStatsRecordID				Unsigned32,
					ltp8xONTMulticastStatsMulticastAddress		IpAddress,
					ltp8xONTMulticastStatsStart					DisplayString,
					ltp8xONTMulticastStatsStop					DisplayString
			}
			
						ltp8xONTMulticastStatsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTMulticastStatsEntry 1 }
						
						ltp8xONTMulticastStatsONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTMulticastStatsEntry 2 }
							
						ltp8xONTMulticastStatsRecordID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTMulticastStatsEntry 3 }
							
						ltp8xONTMulticastStatsMulticastAddress	 OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTMulticastStatsEntry 4 }
							
						ltp8xONTMulticastStatsStart OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTMulticastStatsEntry 5 }
							
						ltp8xONTMulticastStatsStop  OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTMulticastStatsEntry 6 }
							
		ltp8xONTACSPropertiesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSPropertiesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 60 }
		
			ltp8xONTACSPropertiesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTACSPropertiesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTACSPropertiesONTSerial,
								ltp8xONTACSPropertiesPropertyID }
				::= { ltp8xONTACSPropertiesTable 1 }
			
			Ltp8xONTACSPropertiesEntry ::= SEQUENCE {
					ltp8xONTACSPropertiesONTSerial				ONTSerial,
					ltp8xONTACSPropertiesPropertyID				Unsigned32,
					ltp8xONTACSPropertiesPropertyName			DisplayString,
					ltp8xONTACSPropertiesPropertyValue			DisplayString,
					ltp8xONTACSPropertiesRowStatus				RowStatus
			}
						
						ltp8xONTACSPropertiesONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPropertiesEntry 1 }
							
						ltp8xONTACSPropertiesPropertyID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPropertiesEntry 2 }
							
						ltp8xONTACSPropertiesPropertyName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPropertiesEntry 3 }
							
						ltp8xONTACSPropertiesPropertyValue	 OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPropertiesEntry 4 }
							
						ltp8xONTACSPropertiesRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPropertiesEntry 5 }
							
		ltp8xONTACSPropertiesTableSupported OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONT 61 }
							
		ltp8xONTACSPrivatesConfigTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSPrivatesConfigEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 62 }
		
			ltp8xONTACSPrivatesConfigEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTACSPrivatesConfigEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTACSPrivatesPropertyName,
								ltp8xONTACSPrivatesPrivateIndex }
				::= { ltp8xONTACSPrivatesConfigTable 1 }
			
			Ltp8xONTACSPrivatesConfigEntry ::= SEQUENCE {
					ltp8xONTACSPrivatesPropertyName				DisplayString,
					ltp8xONTACSPrivatesPrivateIndex				Unsigned32,
					ltp8xONTACSPrivatesPrivateName				DisplayString,
					ltp8xONTACSPrivatesRowStatus				RowStatus
			}
						
						ltp8xONTACSPrivatesPropertyName OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..255))
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPrivatesConfigEntry 1 }
							
						ltp8xONTACSPrivatesPrivateIndex OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPrivatesConfigEntry 2 }
							
						ltp8xONTACSPrivatesPrivateName OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..255))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPrivatesConfigEntry 3 }
							
						ltp8xONTACSPrivatesRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSPrivatesConfigEntry 4 }	
							
		ltp8xONTACSUserPropertiesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTACSUserPropertiesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 63 }
		
			ltp8xONTACSUserPropertiesEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTACSUserPropertiesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTACSUserPropertiesName,
								ltp8xONTACSUserPropertiesSerial }
				::= { ltp8xONTACSUserPropertiesTable 1 }
			
			Ltp8xONTACSUserPropertiesEntry ::= SEQUENCE {
					ltp8xONTACSUserPropertiesName				DisplayString,
					ltp8xONTACSUserPropertiesSerial				ONTSerial,
					ltp8xONTACSUserPropertiesValue				DisplayString
			}
							
						ltp8xONTACSUserPropertiesName OBJECT-TYPE
							SYNTAX  DisplayString (SIZE(1..255))
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSUserPropertiesEntry 1 }
							
						ltp8xONTACSUserPropertiesSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSUserPropertiesEntry 2 }
							
						ltp8xONTACSUserPropertiesValue OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTACSUserPropertiesEntry 3 }
							
		ltp8xONTConnectionLogTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTConnectionLogEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 70 }
		
			ltp8xONTConnectionLogEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTConnectionLogEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTConnectionLogSlot,
									ltp8xONTConnectionLogONTSerial }
				::= { ltp8xONTConnectionLogTable 1 }
			
			Ltp8xONTConnectionLogEntry ::= SEQUENCE {
					ltp8xONTConnectionLogSlot					Unsigned32,
					ltp8xONTConnectionLogONTSerial				ONTSerial,
					ltp8xONTConnectionLogText					DisplayString
			}
						ltp8xONTConnectionLogSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConnectionLogEntry 1 }
						
						ltp8xONTConnectionLogONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConnectionLogEntry 2 }
							
						ltp8xONTConnectionLogText OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConnectionLogEntry 3 }
							
		ltp8xONTConfigFreenessTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xONTConfigFreenessEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xONT 80 }
		
			ltp8xONTConfigFreenessEntry OBJECT-TYPE
				SYNTAX                Ltp8xONTConfigFreenessEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xONTConfigFreenessSlot,
									ltp8xONTConfigFreenessChannel, ltp8xONTConfigFreenessID }
				::= { ltp8xONTConfigFreenessTable 1 }
			
			Ltp8xONTConfigFreenessEntry ::= SEQUENCE {
					ltp8xONTConfigFreenessSlot					Unsigned32,
					ltp8xONTConfigFreenessChannel				Unsigned32,
					ltp8xONTConfigFreenessID					Unsigned32,
					ltp8xONTConfigFreenessSerial				ONTSerial
			}
						ltp8xONTConfigFreenessSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConfigFreenessEntry 1 }
						
						ltp8xONTConfigFreenessChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConfigFreenessEntry 2 }
							
						ltp8xONTConfigFreenessID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConfigFreenessEntry 3 }
							
						ltp8xONTConfigFreenessSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTConfigFreenessEntry 4 }
							
							
		ltp8xONTDisable OBJECT IDENTIFIER 	::= { ltp8xONT 30000 }
			
						ltp8xONTDisableSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xONTDisable 1 }
						
						ltp8xONTDisableONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTDisable 2 }
							
						ltp8xONTDisableChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTDisable 3 }
							
						ltp8xONTDisableActionDisable OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTDisable 4 }
							
						ltp8xONTDisableActionEnable OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xONTDisable 5 }
				
							
	ltp8xOLT OBJECT IDENTIFIER 	::= { ltp8x 5 }
	
		ltp8xOLTStateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 1 }
		
		ltp8xOLTStateEntry OBJECT-TYPE
			SYNTAX                Ltp8xOLTStateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xOLTStateSlot }
			::= { ltp8xOLTStateTable 1 }
		
		Ltp8xOLTStateEntry ::= SEQUENCE {
				ltp8xOLTStateSlot				Unsigned32,
				ltp8xOLTStateDriverVersion		DisplayString,
				ltp8xOLTStateFirmwareVersion	DisplayString,
				ltp8xOLTStateHardwareVersion	DisplayString,
				ltp8xOLTStateFirmwareVersionChip2	DisplayString,
				ltp8xOLTStateHardwareVersionChip2	DisplayString,
				ltp8xOLTStateReconfigure 		Unsigned32
		}
		
					ltp8xOLTStateSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xOLTStateEntry 1 }
					
					ltp8xOLTStateDriverVersion OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 2 }
						
					ltp8xOLTStateFirmwareVersion OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 3 }
						
					ltp8xOLTStateHardwareVersion OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 4 }
						
					ltp8xOLTStateFirmwareVersionChip2 OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 5 }
						
					ltp8xOLTStateHardwareVersionChip2 OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 6 }
						
					ltp8xOLTStateReconfigure OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS current
						DESCRIPTION ""
						::= { ltp8xOLTStateEntry 10 }
						
		ltp8xOLTMIBBoundary1 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 2 }
						
		ltp8xOLTDhcpRATable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTDhcpRAEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					"OLT DHCP relay agent state"
			::= { ltp8xOLT 3 }
		
		ltp8xOLTDhcpRAEntry OBJECT-TYPE
			SYNTAX                Ltp8xOLTDhcpRAEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xOLTDhcpRASlot }
			::= { ltp8xOLTDhcpRATable 1 }
		
		Ltp8xOLTDhcpRAEntry ::= SEQUENCE {
				ltp8xOLTDhcpRASlot				Unsigned32
		}
		
					ltp8xOLTDhcpRASlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xOLTDhcpRAEntry 1 }
						
						
		ltp8xOLTMIBBoundary2 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 4 }
						
		ltp8xOLTConfigActivationTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTConfigActivationEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 5 }
		
			ltp8xOLTConfigActivationEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTConfigActivationEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTConfigActivationSlot }
				::= { ltp8xOLTConfigActivationTable 1 }
			
			Ltp8xOLTConfigActivationEntry ::= SEQUENCE {
					ltp8xOLTConfigActivationSlot			Unsigned32,
					ltp8xOLTConfigActivationPeriod			Unsigned32,
					ltp8xOLTConfigActivationCheckPassword	TruthValue
			}
			
						ltp8xOLTConfigActivationSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTConfigActivationEntry 1 }
						
						ltp8xOLTConfigActivationPeriod OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigActivationEntry 2 }
							
						ltp8xOLTConfigActivationCheckPassword OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigActivationEntry 3 }
							
							
		ltp8xOLTMIBBoundary3 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 6 }
						
		ltp8xOLTConfigDhcpTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTConfigDhcpEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 7 }
		
			ltp8xOLTConfigDhcpEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTConfigDhcpEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTConfigDhcpSlot }
				::= { ltp8xOLTConfigDhcpTable 1 }
			
			Ltp8xOLTConfigDhcpEntry ::= SEQUENCE {
					ltp8xOLTConfigDhcpSlot					Unsigned32,
					ltp8xOLTConfigDhcpRelayAgentEnabled		TruthValue,
					ltp8xOLTConfigDhcpCircuitIDFormat		DisplayString,
					ltp8xOLTConfigDhcpRemoteIDFormat		DisplayString,
					ltp8xOLTConfigDhcpOverwrtOption82		TruthValue,
					ltp8xOLTConfigDhcpDosBlockEnabled		TruthValue,
					ltp8xOLTConfigDhcpBcPacketPerSecond		Unsigned32,
					ltp8xOLTConfigDhcpPortBlockTime			Unsigned32,
					ltp8xOLTConfigDhcpTrustedServerEnabled	TruthValue,
					ltp8xOLTConfigDhcpTrustedPrimary		IpAddress,
					ltp8xOLTConfigDhcpTrustedSecondary		IpAddress,
					ltp8xOLTConfigDhcpTrustedServerTimeout	Unsigned32
			}
			
						ltp8xOLTConfigDhcpSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTConfigDhcpEntry 1 }
						
						ltp8xOLTConfigDhcpRelayAgentEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 2 }
							
						ltp8xOLTConfigDhcpCircuitIDFormat OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 3 }
							
						ltp8xOLTConfigDhcpRemoteIDFormat OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 4 }
							
						ltp8xOLTConfigDhcpOverwrtOption82 OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 5 }
							
						ltp8xOLTConfigDhcpDosBlockEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 6 }
							
						ltp8xOLTConfigDhcpBcPacketPerSecond OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 7 }
							
						ltp8xOLTConfigDhcpPortBlockTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 8 }
					
						ltp8xOLTConfigDhcpTrustedServerEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 9 }
							
						ltp8xOLTConfigDhcpTrustedPrimary OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 10 }
							
						ltp8xOLTConfigDhcpTrustedSecondary OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 11 }
							
						ltp8xOLTConfigDhcpTrustedServerTimeout OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigDhcpEntry 12 }
							
							
							
	ltp8xOLTMIBBoundary4 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 8 }
			
			
						
		ltp8xOLTConfigPPPoETable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTConfigPPPoEEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 9 }
		
			ltp8xOLTConfigPPPoEEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTConfigPPPoEEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTConfigPPPoESlot }
				::= { ltp8xOLTConfigPPPoETable 1 }
			
			Ltp8xOLTConfigPPPoEEntry ::= SEQUENCE {
					ltp8xOLTConfigPPPoESlot					Unsigned32,
					ltp8xOLTConfigPPPoEPlusEnabled			TruthValue,
					ltp8xOLTConfigPPPoECircuitIDFormat		DisplayString,
					ltp8xOLTConfigPPPoERemoteIDFormat		DisplayString,
					ltp8xOLTConfigPPPoEVendorID				Unsigned32,
					ltp8xOLTConfigPPPoEMaxSessions			Unsigned32,
					ltp8xOLTConfigPPPoEMaxSessionsPerUser	Unsigned32,
					ltp8xOLTConfigPPPoEDosBlockEnabled		TruthValue,
					ltp8xOLTConfigPPPoEBcPacketPerSecond	Unsigned32,
					ltp8xOLTConfigPPPoEPortBlockTime		Unsigned32
			}
			
						ltp8xOLTConfigPPPoESlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTConfigPPPoEEntry 1 }
						
						ltp8xOLTConfigPPPoEPlusEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 2 }
							
						ltp8xOLTConfigPPPoECircuitIDFormat OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 3 }
							
						ltp8xOLTConfigPPPoERemoteIDFormat OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 4 }
							
						ltp8xOLTConfigPPPoEVendorID	 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 5 }
							
						ltp8xOLTConfigPPPoEMaxSessions OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 6 }
							
						ltp8xOLTConfigPPPoEMaxSessionsPerUser OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 7 }
							
						ltp8xOLTConfigPPPoEDosBlockEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 8 }
					
						ltp8xOLTConfigPPPoEBcPacketPerSecond OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 9 }
							
						ltp8xOLTConfigPPPoEPortBlockTime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTConfigPPPoEEntry 10 }
							
							
	ltp8xOLTMIBBoundary5 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 10 }
			
			
						
		ltp8xOLTPPPoESessionsTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTPPPoESessionsEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 11 }
		
			ltp8xOLTPPPoESessionsEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTPPPoESessionsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTPPPoESessionsSlot,
								ltp8xOLTPPPoESessionsChannel,
								ltp8xOLTPPPoESessionsOntID,
								ltp8xOLTPPPoESessionsClientMac }
				::= { ltp8xOLTPPPoESessionsTable 1 }
			
			Ltp8xOLTPPPoESessionsEntry ::= SEQUENCE {
					ltp8xOLTPPPoESessionsSlot					Unsigned32,
					ltp8xOLTPPPoESessionsChannel				Unsigned32,
					ltp8xOLTPPPoESessionsOntID					Unsigned32,
					ltp8xOLTPPPoESessionsClientMac				MacAddress,
					ltp8xOLTPPPoESessionsPort					Unsigned32,
					ltp8xOLTPPPoESessionsSessionID				Unsigned32,
					ltp8xOLTPPPoESessionsDuration				TimeTicks,
					ltp8xOLTPPPoESessionsUnblock				TimeTicks,
					ltp8xOLTPPPoESessionsSerial					ONTSerial
			}
			
						ltp8xOLTPPPoESessionsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTPPPoESessionsEntry 1 }
						
						ltp8xOLTPPPoESessionsChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 2 }
							
						ltp8xOLTPPPoESessionsOntID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 3 }
							
						ltp8xOLTPPPoESessionsClientMac OBJECT-TYPE
							SYNTAX  MacAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 4 }
							
						ltp8xOLTPPPoESessionsPort	 OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 5 }
							
						ltp8xOLTPPPoESessionsSessionID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 6 }
							
						ltp8xOLTPPPoESessionsDuration OBJECT-TYPE
							SYNTAX  TimeTicks
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 7 }
							
						ltp8xOLTPPPoESessionsUnblock OBJECT-TYPE
							SYNTAX  TimeTicks
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 8 }
							
						ltp8xOLTPPPoESessionsSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTPPPoESessionsEntry 9 }
							
							
	ltp8xOLTMIBBoundary6 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 12 }
			
			
						
		ltp8xOLTMulticastStatsTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTMulticastStatsEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 13 }
		
			ltp8xOLTMulticastStatsEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTMulticastStatsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTMulticastStatsSlot,
								ltp8xOLTMulticastStatsChannel,
								ltp8xOLTMulticastStatsRecordID }
				::= { ltp8xOLTMulticastStatsTable 1 }
			
			Ltp8xOLTMulticastStatsEntry ::= SEQUENCE {
					ltp8xOLTMulticastStatsSlot					Unsigned32,
					ltp8xOLTMulticastStatsChannel				Unsigned32,
					ltp8xOLTMulticastStatsRecordID				Unsigned32,
					ltp8xOLTMulticastStatsONTSerial				ONTSerial,
					ltp8xOLTMulticastStatsMulticastAddress		IpAddress,
					ltp8xOLTMulticastStatsStart					DisplayString,
					ltp8xOLTMulticastStatsStop					DisplayString
			}
			
						ltp8xOLTMulticastStatsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTMulticastStatsEntry 1 }
						
						ltp8xOLTMulticastStatsChannel OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 2 }
							
						ltp8xOLTMulticastStatsRecordID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 3 }
							
						ltp8xOLTMulticastStatsONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 4 }
							
						ltp8xOLTMulticastStatsMulticastAddress	 OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 5 }
							
						ltp8xOLTMulticastStatsStart OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 6 }
							
						ltp8xOLTMulticastStatsStop  OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsEntry 7 }
							
		ltp8xOLTMIBBoundary7 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 14 }
						
		ltp8xOLTAddressTableProfilesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTAddressTableProfilesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 15 }
		
			ltp8xOLTAddressTableProfilesEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTAddressTableProfilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTAddressTableProfilesID }
				::= { ltp8xOLTAddressTableProfilesTable 1 }
			
			Ltp8xOLTAddressTableProfilesEntry ::= SEQUENCE {
					ltp8xOLTAddressTableProfilesID				Unsigned32,
					ltp8xOLTAddressTableProfilesDescription				DisplayString
			}
			
						ltp8xOLTAddressTableProfilesID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xOLTAddressTableProfilesEntry 1 }
						
						ltp8xOLTAddressTableProfilesDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTAddressTableProfilesEntry 2 }
							
							
		ltp8xOLTMIBBoundary8 OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
			::= { ltp8xOLT 16 }
						
		ltp8xOLTVlanProfilesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTVlanProfilesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 17 }
		
			ltp8xOLTVlanProfilesEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTVlanProfilesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTVlanProfilesID }
				::= { ltp8xOLTVlanProfilesTable 1 }
			
			Ltp8xOLTVlanProfilesEntry ::= SEQUENCE {
					ltp8xOLTVlanProfilesID				Unsigned32,
					ltp8xOLTVlanProfilesDescription				DisplayString
			}
			
						ltp8xOLTVlanProfilesID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xOLTVlanProfilesEntry 1 }
						
						ltp8xOLTVlanProfilesDescription OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVlanProfilesEntry 2 }
							
							
			ltp8xOLTUpdateFirmwareTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTUpdateFirmwareEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 18 }
		
			ltp8xOLTUpdateFirmwareEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTUpdateFirmwareEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTUpdateFirmwareSlot }
				::= { ltp8xOLTUpdateFirmwareTable 1 }
			
			Ltp8xOLTUpdateFirmwareEntry ::= SEQUENCE {
					ltp8xOLTUpdateFirmwareSlot					Unsigned32,
					ltp8xOLTUpdateFirmwareAction				Unsigned32
			}
			
						ltp8xOLTUpdateFirmwareSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTUpdateFirmwareEntry 1 }
						
						ltp8xOLTUpdateFirmwareAction OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set to 1 to initiate OLT update"
							::= { ltp8xOLTUpdateFirmwareEntry 2 }
							
			ltp8xOLTMulticastStatsBackwardsTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTMulticastStatsBackwardsEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 19 }
		
			ltp8xOLTMulticastStatsBackwardsEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTMulticastStatsBackwardsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTMulticastStatsBackwardsSlot,
								ltp8xOLTMulticastStatsBackwardsONTSerial,
								ltp8xOLTMulticastStatsBackwardsRecordID }
				::= { ltp8xOLTMulticastStatsBackwardsTable 1 }
			
			Ltp8xOLTMulticastStatsBackwardsEntry ::= SEQUENCE {
					ltp8xOLTMulticastStatsBackwardsSlot					Unsigned32,
					ltp8xOLTMulticastStatsBackwardsONTSerial			ONTSerial,
					ltp8xOLTMulticastStatsBackwardsRecordID				Unsigned32,
					ltp8xOLTMulticastStatsBackwardsMulticastAddress		IpAddress,
					ltp8xOLTMulticastStatsBackwardsStart				DisplayString,
					ltp8xOLTMulticastStatsBackwardsStop					DisplayString
			}
			
						ltp8xOLTMulticastStatsBackwardsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTMulticastStatsBackwardsEntry 1 }
							
						ltp8xOLTMulticastStatsBackwardsONTSerial OBJECT-TYPE
							SYNTAX  ONTSerial
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsBackwardsEntry 2 }
							
						ltp8xOLTMulticastStatsBackwardsRecordID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsBackwardsEntry 3 }
							
						ltp8xOLTMulticastStatsBackwardsMulticastAddress	 OBJECT-TYPE
							SYNTAX  IpAddress
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsBackwardsEntry 4 }
							
						ltp8xOLTMulticastStatsBackwardsStart OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsBackwardsEntry 5 }
							
						ltp8xOLTMulticastStatsBackwardsStop  OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTMulticastStatsBackwardsEntry 6 }
							
			ltp8xOLTONTAutoFirmwareUpdateTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xOLTONTAutoFirmwareUpdateEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xOLT 20 }
		
			ltp8xOLTONTAutoFirmwareUpdateEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTONTAutoFirmwareUpdateEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTONTAutoFirmwareUpdateSlot }
				::= { ltp8xOLTONTAutoFirmwareUpdateTable 1 }
			
			Ltp8xOLTONTAutoFirmwareUpdateEntry ::= SEQUENCE {
					ltp8xOLTONTAutoFirmwareUpdateSlot					Unsigned32,
					ltp8xOLTONTAutoFirmwareUpdateEnabled				TruthValue
			}
			
						ltp8xOLTONTAutoFirmwareUpdateSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTONTAutoFirmwareUpdateEntry 1 }
						
						ltp8xOLTONTAutoFirmwareUpdateEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTONTAutoFirmwareUpdateEntry 2 }
							
		ltp8xOLTVInterfaceMonitoringDSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xOLTVInterfaceMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xOLT 30 }
			
			ltp8xOLTVInterfaceMonitoringDSEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTVInterfaceMonitoringDSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTVInterfaceMonitoringDSSlot, ltp8xOLTVInterfaceMonitoringDSChannelRange,
									ltp8xOLTVInterfaceMonitoringDSCounterID}
				::= { ltp8xOLTVInterfaceMonitoringDSTable 1 }
			
			Ltp8xOLTVInterfaceMonitoringDSEntry ::= SEQUENCE {
					ltp8xOLTVInterfaceMonitoringDSSlot					Unsigned32,
					ltp8xOLTVInterfaceMonitoringDSChannelRange			INTEGER,
					ltp8xOLTVInterfaceMonitoringDSCounterID				Unsigned32,
					ltp8xOLTVInterfaceMonitoringDSCounterName			DisplayString,
					ltp8xOLTVInterfaceMonitoringDSCounterValue	 		Unsigned32
			}
			
						ltp8xOLTVInterfaceMonitoringDSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTVInterfaceMonitoringDSEntry 1 }
						
						ltp8xOLTVInterfaceMonitoringDSChannelRange OBJECT-TYPE
							SYNTAX INTEGER  {
									range0-3(1),
									range4-7(2) }
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringDSEntry 2 }
							
						ltp8xOLTVInterfaceMonitoringDSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringDSEntry 3 }
							
						ltp8xOLTVInterfaceMonitoringDSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringDSEntry 4 }
							
						ltp8xOLTVInterfaceMonitoringDSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringDSEntry 5 }
							
		ltp8xOLTVInterfaceMonitoringUSTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xOLTVInterfaceMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xOLT 31 }
			
			ltp8xOLTVInterfaceMonitoringUSEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTVInterfaceMonitoringUSEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTVInterfaceMonitoringUSSlot, ltp8xOLTVInterfaceMonitoringUSChannelRange,
									ltp8xOLTVInterfaceMonitoringUSCounterID}
				::= { ltp8xOLTVInterfaceMonitoringUSTable 1 }
			
			Ltp8xOLTVInterfaceMonitoringUSEntry ::= SEQUENCE {
					ltp8xOLTVInterfaceMonitoringUSSlot					Unsigned32,
					ltp8xOLTVInterfaceMonitoringUSChannelRange			INTEGER,
					ltp8xOLTVInterfaceMonitoringUSCounterID				Unsigned32,
					ltp8xOLTVInterfaceMonitoringUSCounterName			DisplayString,
					ltp8xOLTVInterfaceMonitoringUSCounterValue	 		Unsigned32
			}
			
						ltp8xOLTVInterfaceMonitoringUSSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTVInterfaceMonitoringUSEntry 1 }
						
						ltp8xOLTVInterfaceMonitoringUSChannelRange OBJECT-TYPE
							SYNTAX INTEGER  {
									range0-3(1),
									range4-7(2) }
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringUSEntry 2 }
							
						ltp8xOLTVInterfaceMonitoringUSCounterID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringUSEntry 3 }
							
						ltp8xOLTVInterfaceMonitoringUSCounterName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringUSEntry 4 }
							
						ltp8xOLTVInterfaceMonitoringUSCounterValue OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTVInterfaceMonitoringUSEntry 5 }
							
							
			ltp8xOLTResetCountersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xOLTResetCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xOLT 50 }
			
			ltp8xOLTResetCountersEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTResetCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTResetCountersSlot }
				::= { ltp8xOLTResetCountersTable 1 }
			
			Ltp8xOLTResetCountersEntry ::= SEQUENCE {
					ltp8xOLTResetCountersSlot					Unsigned32,
					ltp8xOLTResetCountersAction					Unsigned32
			}
			
						ltp8xOLTResetCountersSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTResetCountersEntry 1 }
						
						ltp8xOLTResetCountersAction OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Set to 1 to reset OLT counters"
							::= { ltp8xOLTResetCountersEntry 2 }
							
							
			ltp8xOLTTerminalVLANsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xOLTTerminalVLANsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xOLT 51 }
			
			ltp8xOLTTerminalVLANsEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTTerminalVLANsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTTerminalVLANsSlot,
									ltp8xOLTTerminalVLANsID }
				::= { ltp8xOLTTerminalVLANsTable 1 }
			
			Ltp8xOLTTerminalVLANsEntry ::= SEQUENCE {
					ltp8xOLTTerminalVLANsSlot					Unsigned32,
					ltp8xOLTTerminalVLANsID						Unsigned32,
					ltp8xOLTTerminalVLANsName					DisplayString,
					ltp8xOLTTerminalVLANsVID					Unsigned32,
					ltp8xOLTTerminalVLANsCOS					INTEGER,
					ltp8xOLTTerminalVLANsRowStatus				RowStatus
			}
			
						ltp8xOLTTerminalVLANsSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xOLTTerminalVLANsEntry 1 }
						
						ltp8xOLTTerminalVLANsID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Record index"
							::= { ltp8xOLTTerminalVLANsEntry 2 }
							
						ltp8xOLTTerminalVLANsName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Terminal VLAN name"
							::= { ltp8xOLTTerminalVLANsEntry 3 }
							
						ltp8xOLTTerminalVLANsVID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTTerminalVLANsEntry 4 }
							
						ltp8xOLTTerminalVLANsCOS OBJECT-TYPE
							SYNTAX  INTEGER {
								unused(255)
							}
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTTerminalVLANsEntry 5 }
							
						ltp8xOLTTerminalVLANsRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTTerminalVLANsEntry 10 }
							
			ltp8xOLTTerminalVLANsNamesTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xOLTTerminalVLANsNamesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xOLT 52 }
			
			ltp8xOLTTerminalVLANsNamesEntry OBJECT-TYPE
				SYNTAX                Ltp8xOLTTerminalVLANsNamesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xOLTTerminalVLANsNamesID }
				::= { ltp8xOLTTerminalVLANsNamesTable 1 }
			
			Ltp8xOLTTerminalVLANsNamesEntry ::= SEQUENCE {
					ltp8xOLTTerminalVLANsNamesID					Unsigned32,
					ltp8xOLTTerminalVLANsNamesName					DisplayString,					
					ltp8xOLTTerminalVLANsNamesRowStatus				RowStatus
			}
						
						ltp8xOLTTerminalVLANsNamesID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION "Record index"
							::= { ltp8xOLTTerminalVLANsNamesEntry 2 }
							
						ltp8xOLTTerminalVLANsNamesName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION "Terminal VLAN name"
							::= { ltp8xOLTTerminalVLANsNamesEntry 3 }
							
						ltp8xOLTTerminalVLANsNamesRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xOLTTerminalVLANsNamesEntry 10 }
							
							
							
							
	ltp8xSwitch OBJECT IDENTIFIER 	::= { ltp8x 9 }
	
		ltp8xSwitchPortsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xSwitchPortsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSwitch 1 }
			
			ltp8xSwitchPortsEntry OBJECT-TYPE
				SYNTAX                Ltp8xSwitchPortsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xSwitchPortsID }
				::= { ltp8xSwitchPortsTable 1 }
			
			Ltp8xSwitchPortsEntry ::= SEQUENCE {
					ltp8xSwitchPortsID				Unsigned32,
					ltp8xSwitchPortsName			DisplayString
			}
			
						ltp8xSwitchPortsID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xSwitchPortsEntry 1 }
						
						ltp8xSwitchPortsName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchPortsEntry 2 }
							
	
		ltp8xSwitchVLANTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xSwitchVLANEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSwitch 2 }
			
			ltp8xSwitchVLANEntry OBJECT-TYPE
				SYNTAX                Ltp8xSwitchVLANEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xSwitchVLANSlot,
								ltp8xSwitchVLANVid }
				::= { ltp8xSwitchVLANTable 1 }
			
			Ltp8xSwitchVLANEntry ::= SEQUENCE {
					ltp8xSwitchVLANSlot							Unsigned32,
					ltp8xSwitchVLANVid							Unsigned32,
					ltp8xSwitchVLANName							DisplayString,
					ltp8xSwitchVLANTaggedPorts					PortList,
					ltp8xSwitchVLANUntaggedPorts				PortList,
					ltp8xSwitchVLANRowStatus					RowStatus,
					ltp8xSwitchVLANIGMPSnoopingEnabled			TruthValue,
					ltp8xSwitchVLANIGMPSnoopingQuerierEnabled	TruthValue,
					ltp8xSwitchVLANMLDSnoopingEnabled			TruthValue,
					ltp8xSwitchVLANMLDSnoopingQuerierEnabled	TruthValue
			}
			
						ltp8xSwitchVLANSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xSwitchVLANEntry 1 }
							
						ltp8xSwitchVLANVid OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 2 }
						
						ltp8xSwitchVLANName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 3 }
							
						ltp8xSwitchVLANTaggedPorts OBJECT-TYPE
							SYNTAX  PortList
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 4 }
							
						ltp8xSwitchVLANUntaggedPorts OBJECT-TYPE
							SYNTAX  PortList
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 5 }
							
						ltp8xSwitchVLANRowStatus OBJECT-TYPE
							SYNTAX  RowStatus
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 6 }
							
						ltp8xSwitchVLANIGMPSnoopingEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 10 }
							
						ltp8xSwitchVLANIGMPSnoopingQuerierEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 11 }
							
						ltp8xSwitchVLANMLDSnoopingEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 20 }
							
						ltp8xSwitchVLANMLDSnoopingQuerierEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchVLANEntry 21 }
							
			ltp8xSwitchIGMPSnoopingTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xSwitchIGMPSnoopingEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSwitch 3 }
			
			ltp8xSwitchIGMPSnoopingEntry OBJECT-TYPE
				SYNTAX                Ltp8xSwitchIGMPSnoopingEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xSwitchIGMPSnoopingSlot }
				::= { ltp8xSwitchIGMPSnoopingTable 1 }
			
			Ltp8xSwitchIGMPSnoopingEntry ::= SEQUENCE {
					ltp8xSwitchIGMPSnoopingSlot							Unsigned32,
					ltp8xSwitchIGMPSnoopingEnabled						TruthValue,
					ltp8xSwitchMLDSnoopingEnabled						TruthValue
			}
			
						ltp8xSwitchIGMPSnoopingSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xSwitchIGMPSnoopingEntry 1 }
							
						ltp8xSwitchIGMPSnoopingEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchIGMPSnoopingEntry 2 }
							
						ltp8xSwitchMLDSnoopingEnabled OBJECT-TYPE
							SYNTAX  TruthValue
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchIGMPSnoopingEntry 3 }
							
		ltp8xSwitchOperationalVLANTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xSwitchOperationalVLANEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSwitch 4 }
			
			ltp8xSwitchOperationalVLANEntry OBJECT-TYPE
				SYNTAX                Ltp8xSwitchOperationalVLANEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xSwitchOperationalVLANSlot,
								ltp8xSwitchOperationalVLANVid }
				::= { ltp8xSwitchOperationalVLANTable 1 }
			
			Ltp8xSwitchOperationalVLANEntry ::= SEQUENCE {
					ltp8xSwitchOperationalVLANSlot							Unsigned32,
					ltp8xSwitchOperationalVLANVid							Unsigned32,
					ltp8xSwitchOperationalVLANName							DisplayString,
					ltp8xSwitchOperationalVLANTaggedPorts					PortList,
					ltp8xSwitchOperationalVLANUntaggedPorts					PortList
			}
			
						ltp8xSwitchOperationalVLANSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xSwitchOperationalVLANEntry 1 }
							
						ltp8xSwitchOperationalVLANVid OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchOperationalVLANEntry 2 }
						
						ltp8xSwitchOperationalVLANName OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchOperationalVLANEntry 3 }
							
						ltp8xSwitchOperationalVLANTaggedPorts OBJECT-TYPE
							SYNTAX  PortList
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchOperationalVLANEntry 4 }
							
						ltp8xSwitchOperationalVLANUntaggedPorts OBJECT-TYPE
							SYNTAX  PortList
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSwitchOperationalVLANEntry 5 }
							
		ltp8xSwitchPortCountersTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xSwitchPortCountersEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xSwitch 5 }
		
		ltp8xSwitchPortCountersEntry OBJECT-TYPE
			SYNTAX                Ltp8xSwitchPortCountersEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xSwitchPortCountersSlot, ltp8xSwitchPortCountersPortID }
			::= { ltp8xSwitchPortCountersTable 1 }
		
		Ltp8xSwitchPortCountersEntry ::= SEQUENCE {
				ltp8xSwitchPortCountersSlot				Unsigned32,
				ltp8xSwitchPortCountersPortID			Unsigned32,
				ltp8xSwitchPortGoodOctetsRcv			Counter64,
				ltp8xSwitchPortBadOctetsRcv				Counter64,
				ltp8xSwitchPortMacTransmitErr			Counter64,
				ltp8xSwitchPortGoodPktsRcv				Counter64,
				ltp8xSwitchPortBadPktsRcv				Counter64,
				ltp8xSwitchPortBrdcPktsRcv				Counter64,
				ltp8xSwitchPortMcPktsRcv				Counter64,
				ltp8xSwitchPortPkts64Octets				Counter64,
				ltp8xSwitchPortPkts65to127Octets		Counter64,
				ltp8xSwitchPortPkts128to255Octets		Counter64,
				ltp8xSwitchPortPkts256to511Octets		Counter64,
				ltp8xSwitchPortPkts512to1023Octets		Counter64,
				ltp8xSwitchPortPkts1024tomaxOctets		Counter64,
				ltp8xSwitchPortGoodOctetsSent			Counter64,
				ltp8xSwitchPortGoodPktsSent				Counter64,
				ltp8xSwitchPortExcessiveCollisions		Counter64,
				ltp8xSwitchPortMcPktsSent				Counter64,
				ltp8xSwitchPortBrdcPktsSent				Counter64,
				ltp8xSwitchPortUnrecogMacCntrRcv		Counter64,
				ltp8xSwitchPortFcSent					Counter64,
				ltp8xSwitchPortGoodFcRcv				Counter64,
				ltp8xSwitchPortDropEvents				Counter64,
				ltp8xSwitchPortUndersizePkts			Counter64,
				ltp8xSwitchPortFragmentsPkts			Counter64,
				ltp8xSwitchPortOversizePkts				Counter64,
				ltp8xSwitchPortJabberPkts				Counter64,
				ltp8xSwitchPortMacRcvError				Counter64,
				ltp8xSwitchPortBadCrc					Counter64,
				ltp8xSwitchPortCollisions				Counter64,
				ltp8xSwitchPortLateCollisions			Counter64,
				ltp8xSwitchPortBadFcRcv					Counter64,
				ltp8xSwitchPortCountersReset			Unsigned32
		}
		
					ltp8xSwitchPortCountersSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xSwitchPortCountersEntry 1 }
					
					ltp8xSwitchPortCountersPortID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xSwitchPortCountersEntry 2 }
						
					ltp8xSwitchPortGoodOctetsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of ethernet frames received that are neither bad ethernet frames nor MAC Control pkts. This includes Bridge Control packets (LCAP, BPDU)"
						::= { ltp8xSwitchPortCountersEntry 3 }
						
					ltp8xSwitchPortBadOctetsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xSwitchPortCountersEntry 4 }
						
					ltp8xSwitchPortMacTransmitErr OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of frames not transmitted correctly or dropped due to internal MAC Tx error"
						::= { ltp8xSwitchPortCountersEntry 5 }
						
					ltp8xSwitchPortGoodPktsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of ethernet frames received at this MAC."
						::= { ltp8xSwitchPortCountersEntry 6 }
						
					ltp8xSwitchPortBadPktsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of bad ethernet frames received"
						::= { ltp8xSwitchPortCountersEntry 7 }
						
					ltp8xSwitchPortBrdcPktsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of good packets received that were directed to the broadcast address"
						::= { ltp8xSwitchPortCountersEntry 8 }
						
					ltp8xSwitchPortMcPktsRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of good packets received that were directed to a multicast address"
						::= { ltp8xSwitchPortCountersEntry 9 }
						
					ltp8xSwitchPortPkts64Octets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are 64 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 10 }
						
					ltp8xSwitchPortPkts65to127Octets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are 65 to 127 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 11 }
						
					ltp8xSwitchPortPkts128to255Octets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are 128 to 255 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 12 }
						
					ltp8xSwitchPortPkts256to511Octets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are 256 to 511 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 13 }
						
					ltp8xSwitchPortPkts512to1023Octets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are 512 to 1023 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 14 }
						
					ltp8xSwitchPortPkts1024tomaxOctets OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total bytes of received and transmitted Good and Bad frames which are more than 1024 bytes in size. This does not include MAC Control Frames."
						::= { ltp8xSwitchPortCountersEntry 15 }
						
					ltp8xSwitchPortGoodOctetsSent OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Sum of lengths of all good ethernet frames sent from this MAC. This does not include 802.3 Flow Control packets, packets dropped due to excessive collision or packets with a Tx Error."
						::= { ltp8xSwitchPortCountersEntry 16 }
						
					ltp8xSwitchPortGoodPktsSent OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of ethernet frames sent from this MAC. This does not include 802.3 Flow Control packets, packets dropped due to excessive collision or packets with a Tx Error."
						::= { ltp8xSwitchPortCountersEntry 17 }
						
					ltp8xSwitchPortExcessiveCollisions OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of frames dropped in the transmit MAC due to excessive collisions. This is applicable for Half Duplex mode only."
						::= { ltp8xSwitchPortCountersEntry 18 }
						
					ltp8xSwitchPortMcPktsSent OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of good packets sent that have a multicast destination MAC address. This does not include 802.3 Flow Control packets, packets dropped due to excessive collision or packets with a Tx Error."
						::= { ltp8xSwitchPortCountersEntry 19 }
						
					ltp8xSwitchPortBrdcPktsSent OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of good packets sent that have a broadcast destination MAC address. This does not include 802.3 Flow Control packets, packets dropped due to excessive collision or packets with a Tx Error."
						::= { ltp8xSwitchPortCountersEntry 20 }
						
					ltp8xSwitchPortUnrecogMacCntrRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of received MAC Control frames that have an opcode different from 00 01."
						::= { ltp8xSwitchPortCountersEntry 21 }
						
					ltp8xSwitchPortFcSent OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of Flow Control frames sent."
						::= { ltp8xSwitchPortCountersEntry 22 }
						
					ltp8xSwitchPortGoodFcRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of good Flow Control frames received"
						::= { ltp8xSwitchPortCountersEntry 23 }
						
					ltp8xSwitchPortDropEvents OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of instances that the port was unable to receive packets due to insufficient bandwidth to one of the PP internal resources, such as the DRAM or buffer allocation."
						::= { ltp8xSwitchPortCountersEntry 24 }
						
					ltp8xSwitchPortUndersizePkts OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of undersize packets received."
						::= { ltp8xSwitchPortCountersEntry 25 }
						
					ltp8xSwitchPortFragmentsPkts OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of fragments received."
						::= { ltp8xSwitchPortCountersEntry 26 }
						
					ltp8xSwitchPortOversizePkts OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of oversize packets received."
						::= { ltp8xSwitchPortCountersEntry 27 }
						
					ltp8xSwitchPortJabberPkts OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of jabber packets received."
						::= { ltp8xSwitchPortCountersEntry 28 }
						
					ltp8xSwitchPortMacRcvError OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of Rx Error events seen by the receive side of the MAC"
						::= { ltp8xSwitchPortCountersEntry 29 }
						
					ltp8xSwitchPortBadCrc OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of CRC error events."
						::= { ltp8xSwitchPortCountersEntry 30 }
						
					ltp8xSwitchPortCollisions OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of collisions seen by the MAC"
						::= { ltp8xSwitchPortCountersEntry 31 }
						
					ltp8xSwitchPortLateCollisions OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Total number of late collisions seen by the MAC"
						::= { ltp8xSwitchPortCountersEntry 32 }
						
					ltp8xSwitchPortBadFcRcv OBJECT-TYPE
						SYNTAX  Counter64
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Number of bad Flow Control frames received."
						::= { ltp8xSwitchPortCountersEntry 33 }
						
					ltp8xSwitchPortCountersReset OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION "Set this to 1 to reset this port counters."
						::= { ltp8xSwitchPortCountersEntry 50 }
						
		ltp8xSwitchMacListTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xSwitchMacListEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8xSwitch 6 }
		
		ltp8xSwitchMacListEntry OBJECT-TYPE
			SYNTAX                Ltp8xSwitchMacListEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xSwitchMacListSlot, ltp8xSwitchMacListVID,
								ltp8xSwitchMacListMacAddress }
			::= { ltp8xSwitchMacListTable 1 }
		
		Ltp8xSwitchMacListEntry ::= SEQUENCE {
				ltp8xSwitchMacListSlot				Unsigned32,
				ltp8xSwitchMacListVID				Unsigned32,
				ltp8xSwitchMacListMacAddress		MacAddress,
				ltp8xSwitchMacListInterface			Unsigned32,				
				ltp8xSwitchMacListStatic			TruthValue,
				ltp8xSwitchMacListMacAddressString	DisplayString
				
		}
		
					ltp8xSwitchMacListSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xSwitchMacListEntry 1 }
					
					ltp8xSwitchMacListVID OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION " "
						::= { ltp8xSwitchMacListEntry 2 }
						
					ltp8xSwitchMacListMacAddress OBJECT-TYPE
						SYNTAX  MacAddress
						MAX-ACCESS not-accessible
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xSwitchMacListEntry 3 }
						
					ltp8xSwitchMacListInterface OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Interface index. Corresponds with ltp8xSwitchPortsTable"
						::= { ltp8xSwitchMacListEntry 4 }
						
					ltp8xSwitchMacListStatic OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xSwitchMacListEntry 5 }
						
					ltp8xSwitchMacListMacAddressString OBJECT-TYPE
						SYNTAX  DisplayString
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xSwitchMacListEntry 6 }
						
		ltp8xSwitchPortsUtilization OBJECT IDENTIFIER 	::= { ltp8xSwitch 8 }
		
			ltp8xPortsUtilizationInterval OBJECT-TYPE
					SYNTAX     Unsigned32
					MAX-ACCESS read-write
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xSwitchPortsUtilization 1 }
		
			ltp8xPortsUtilizationTable OBJECT-TYPE
			SYNTAX		SEQUENCE OF Ltp8xPortsUtilizationEntry
			MAX-ACCESS	not-accessible
			STATUS		current
			DESCRIPTION	""
				::= { ltp8xSwitchPortsUtilization 2 }

			ltp8xPortsUtilizationEntry OBJECT-TYPE
				SYNTAX		Ltp8xPortsUtilizationEntry
				MAX-ACCESS	not-accessible
				STATUS		current
				DESCRIPTION ""
				INDEX	{ ltp8xPortsUtilizationSlot, ltp8xPortsUtilizationPortID }
				::= { ltp8xPortsUtilizationTable 1 }

				Ltp8xPortsUtilizationEntry ::= SEQUENCE {
					ltp8xPortsUtilizationSlot				Unsigned32,
					ltp8xPortsUtilizationPortID				Unsigned32,
					ltp8xPortsUtilizationLastKbitsSent		Counter64,
					ltp8xPortsUtilizationLastKbitsRecv		Counter64,
					ltp8xPortsUtilizationLastFramesSent		Counter64,
					ltp8xPortsUtilizationLastFramesRecv		Counter64,
					ltp8xPortsUtilizationAverageKbitsSent	Counter64,
					ltp8xPortsUtilizationAverageKbitsRecv	Counter64,
					ltp8xPortsUtilizationAverageFramesSent	Counter64,
					ltp8xPortsUtilizationAverageFramesRecv	Counter64
				}
			
				ltp8xPortsUtilizationSlot OBJECT-TYPE
					SYNTAX     Unsigned32
					MAX-ACCESS not-accessible
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 1 }
				
				ltp8xPortsUtilizationPortID OBJECT-TYPE
					SYNTAX     Unsigned32
					MAX-ACCESS not-accessible
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 2 }
			
				ltp8xPortsUtilizationLastKbitsSent OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 3 }
			
				ltp8xPortsUtilizationLastKbitsRecv OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 4 }
					
				ltp8xPortsUtilizationLastFramesSent OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 5 }
					
				ltp8xPortsUtilizationLastFramesRecv OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 6 }
					
				ltp8xPortsUtilizationAverageKbitsSent OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 7 }
					
				ltp8xPortsUtilizationAverageKbitsRecv OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 8 }
					
				ltp8xPortsUtilizationAverageFramesSent OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 9 }
					
				ltp8xPortsUtilizationAverageFramesRecv OBJECT-TYPE
					SYNTAX     Counter64
					MAX-ACCESS read-only
					STATUS     current
					DESCRIPTION " "
					::= { ltp8xPortsUtilizationEntry 10 }
						
					
							
		ltp8xQOSConfig OBJECT IDENTIFIER ::= { ltp8xSwitch 10 }
			
			ltp8xQOSConfigTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xQOSConfigEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				::= { ltp8xQOSConfig 1  }
			
				ltp8xQOSConfigEntry OBJECT-TYPE
				SYNTAX                Ltp8xQOSConfigEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xQOSConfigSlot }
				::= { ltp8xQOSConfigTable 1 }
	
				Ltp8xQOSConfigEntry ::= SEQUENCE {
						ltp8xQOSConfigSlot						Unsigned32,
						ltp8xQOSDefaultQueue					Unsigned32,
						ltp8xQOSType							INTEGER,
						ltp8xQOSDownstreamQinQPrioritization	TruthValue
				}
							ltp8xQOSConfigSlot  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
								::= { ltp8xQOSConfigEntry 1 }
							
							ltp8xQOSDefaultQueue  OBJECT-TYPE
								SYNTAX     Unsigned32 (0..6)
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION "Default priority queue"
								::= { ltp8xQOSConfigEntry 2 }
								
							ltp8xQOSType OBJECT-TYPE
								SYNTAX     INTEGER {
										typeAllEqual(0),
										type8021p(1),
										typeDscpTos(2),
										typeDscpTos8021p(3)
										}
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION "Priority field of packet"
								::= { ltp8xQOSConfigEntry 3 }
								
							ltp8xQOSDownstreamQinQPrioritization OBJECT-TYPE
								SYNTAX     TruthValue
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION "Default priority queue"
								::= { ltp8xQOSConfigEntry 4 }
			
		ltp8xQOS8021pMappingTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xQOS8021pMappingEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xQOSConfig 2  }
		
			ltp8xQOS8021pMappingEntry OBJECT-TYPE
			SYNTAX                Ltp8xQOS8021pMappingEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xQOS8021pMappingSlot, 
								ltp8xQOS8021pMappingQueue }
			::= { ltp8xQOS8021pMappingTable 1 }

			Ltp8xQOS8021pMappingEntry ::= SEQUENCE {
					ltp8xQOS8021pMappingSlot 	Unsigned32,
					ltp8xQOS8021pMappingQueue	Unsigned32,
					ltp8xQOS8021pMappingFields	OCTET STRING
			}
			
						ltp8xQOS8021pMappingSlot  OBJECT-TYPE
							SYNTAX     Unsigned32
							MAX-ACCESS not-accessible
							STATUS     current
							DESCRIPTION " "
							::= { ltp8xQOS8021pMappingEntry 1 }
							
						ltp8xQOS8021pMappingQueue  OBJECT-TYPE
							SYNTAX     Unsigned32
							MAX-ACCESS not-accessible
							STATUS     current
							DESCRIPTION " "
							::= { ltp8xQOS8021pMappingEntry 2 }
							
						ltp8xQOS8021pMappingFields  OBJECT-TYPE
							SYNTAX     OCTET STRING (SIZE (1))
							MAX-ACCESS read-write
							STATUS     current
							DESCRIPTION "Bitmask, in which every bit maps corresponding field to queue."
							::= { ltp8xQOS8021pMappingEntry 3 }
							
		ltp8xQOSDSCPMappingTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xQOSDSCPMappingEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xQOSConfig 3  }
		
			ltp8xQOSDSCPMappingEntry OBJECT-TYPE
			SYNTAX                Ltp8xQOSDSCPMappingEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xQOSDSCPMappingSlot, ltp8xQOSDSCPMappingQueue }
			::= { ltp8xQOSDSCPMappingTable 1 }

			Ltp8xQOSDSCPMappingEntry ::= SEQUENCE {
					ltp8xQOSDSCPMappingSlot 	Unsigned32,
					ltp8xQOSDSCPMappingQueue	Unsigned32,
					ltp8xQOSDSCPMappingFields	OCTET STRING
			}
						ltp8xQOSDSCPMappingSlot  OBJECT-TYPE
							SYNTAX     Unsigned32
							MAX-ACCESS not-accessible
							STATUS     current
							DESCRIPTION " "
							::= { ltp8xQOSDSCPMappingEntry 1 }
			
						ltp8xQOSDSCPMappingQueue  OBJECT-TYPE
							SYNTAX     Unsigned32
							MAX-ACCESS not-accessible
							STATUS     current
							DESCRIPTION " "
							::= { ltp8xQOSDSCPMappingEntry 2 }
							
						ltp8xQOSDSCPMappingFields  OBJECT-TYPE
							SYNTAX     OCTET STRING (SIZE (8))
							MAX-ACCESS read-write
							STATUS     current
							DESCRIPTION "Bitmask, in which every bit maps corresponding field to queue."
							::= { ltp8xQOSDSCPMappingEntry 3 }
							
		ltp8xACLConfig OBJECT IDENTIFIER ::= { ltp8xSwitch 15 }
				
			ltp8xACLGlobalModeTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xACLGlobalModeEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xACLConfig 1 }
			
				ltp8xACLGlobalModeEntry OBJECT-TYPE
					SYNTAX                Ltp8xACLGlobalModeEntry
					MAX-ACCESS        not-accessible
					STATUS                current
					DESCRIPTION
							""
					INDEX         { ltp8xACLGlobalModeSlot }
					::= { ltp8xACLGlobalModeTable 1 }
				
				Ltp8xACLGlobalModeEntry ::= SEQUENCE {
						ltp8xACLGlobalModeSlot		Unsigned32,
						ltp8xACLGlobalMode			INTEGER
				}
				
							ltp8xACLGlobalModeSlot OBJECT-TYPE
								SYNTAX  Unsigned32
								MAX-ACCESS not-accessible
								STATUS  current
								DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
								::= { ltp8xACLGlobalModeEntry 1 }
							
							ltp8xACLGlobalMode OBJECT-TYPE
								SYNTAX  INTEGER {
											blackList(0),
											whiteList(1) 
											}
								MAX-ACCESS read-write
								STATUS  current
								DESCRIPTION ""
								::= { ltp8xACLGlobalModeEntry 2 }
		
			ltp8xACLListsTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xACLListsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				::= { ltp8xACLConfig 2  }
			
				ltp8xACLListsEntry OBJECT-TYPE
				SYNTAX                Ltp8xACLListsEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xACLListsSlot, ltp8xACLListsID }
				::= { ltp8xACLListsTable 1 }
	
				Ltp8xACLListsEntry ::= SEQUENCE {
						ltp8xACLListsSlot 			Unsigned32,
						ltp8xACLListsID				Unsigned32,
						ltp8xACLListsName			DisplayString,
						ltp8xACLListsPorts			PortList,
						ltp8xACLListsFiltersCount	Unsigned32,
						ltp8xACLListsRowStatus		RowStatus
				}
							ltp8xACLListsSlot  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 1 }
				
							ltp8xACLListsID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 2 }
								
							ltp8xACLListsName  OBJECT-TYPE
								SYNTAX     DisplayString
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 3 }
								
							ltp8xACLListsPorts  OBJECT-TYPE
								SYNTAX     PortList
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 4 }
								
							ltp8xACLListsFiltersCount  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS read-only
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 5 }
								
							ltp8xACLListsRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLListsEntry 10 }
								
								
			ltp8xACLFiltersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xACLFiltersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				::= { ltp8xACLConfig 3  }
			
				ltp8xACLFiltersEntry OBJECT-TYPE
				SYNTAX                Ltp8xACLFiltersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xACLFiltersSlot,
								ltp8xACLFiltersListID,
								ltp8xACLFiltersFilterID  }
				::= { ltp8xACLFiltersTable 1 }
	
				Ltp8xACLFiltersEntry ::= SEQUENCE {
						ltp8xACLFiltersSlot 	Unsigned32,
						ltp8xACLFiltersListID	Unsigned32,
						ltp8xACLFiltersFilterID	Unsigned32,
						ltp8xACLFiltersType		INTEGER,
						ltp8xACLFiltersMacAddress	MacAddress,
						ltp8xACLFiltersIpAddress	IpAddress,
						ltp8xACLFiltersProtocol		Unsigned32,
						ltp8xACLFiltersPort			Unsigned32,
						ltp8xACLFiltersRowStatus	RowStatus
				}
							ltp8xACLFiltersSlot  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 1 }
				
							ltp8xACLFiltersListID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 2 }
								
							ltp8xACLFiltersFilterID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 3 }
								
							ltp8xACLFiltersType  OBJECT-TYPE
								SYNTAX     INTEGER {
									macSA(0),
									macDA(1),
									l2Proto(2),
									ipProto(3),
									ipSA(4),
									ipDA(5),
									tcpSPort(6),
									tcpDPort(7),
									updSPort(8),
									udpDPort(9)
								}
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 4 }
								
							ltp8xACLFiltersMacAddress  OBJECT-TYPE
								SYNTAX     MacAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 5 }
								
							ltp8xACLFiltersIpAddress  OBJECT-TYPE
								SYNTAX     IpAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 6 }
								
							ltp8xACLFiltersProtocol  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 7 }
								
							ltp8xACLFiltersPort  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 8}
								
							ltp8xACLFiltersRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xACLFiltersEntry 10 }
								
		ltp8xIGMPProxyReportTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xIGMPProxyReportEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSwitch 20 }
			
				ltp8xIGMPProxyReportEntry OBJECT-TYPE
					SYNTAX                Ltp8xIGMPProxyReportEntry
					MAX-ACCESS        not-accessible
					STATUS                current
					DESCRIPTION
							""
					INDEX         { ltp8xIGMPProxyReportSlot }
					::= { ltp8xIGMPProxyReportTable 1 }
				
				Ltp8xIGMPProxyReportEntry ::= SEQUENCE {
						ltp8xIGMPProxyReportSlot		Unsigned32,
						ltp8xIGMPProxyReportEnabled		TruthValue,
						ltp8xMLDProxyReportEnabled		TruthValue
				}
				
							ltp8xIGMPProxyReportSlot OBJECT-TYPE
								SYNTAX  Unsigned32
								MAX-ACCESS not-accessible
								STATUS  current
								DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
								::= { ltp8xIGMPProxyReportEntry 1 }
							
							ltp8xIGMPProxyReportEnabled OBJECT-TYPE
								SYNTAX  TruthValue
								MAX-ACCESS read-write
								STATUS  current
								DESCRIPTION ""
								::= { ltp8xIGMPProxyReportEntry 2 }
								
							ltp8xMLDProxyReportEnabled OBJECT-TYPE
								SYNTAX  TruthValue
								MAX-ACCESS read-write
								STATUS  current
								DESCRIPTION ""
								::= { ltp8xIGMPProxyReportEntry 3 }
				
		ltp8xIGMPProxyReportRangesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xIGMPProxyReportRangesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xSwitch 21 }
		
				ltp8xIGMPProxyReportRangesEntry OBJECT-TYPE
				SYNTAX                Ltp8xIGMPProxyReportRangesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xIGMPProxyReportRangesSlot, ltp8xIGMPProxyReportRangesID }
				::= { ltp8xIGMPProxyReportRangesTable 1 }
	
				Ltp8xIGMPProxyReportRangesEntry ::= SEQUENCE {
						ltp8xIGMPProxyReportRangesSlot	Unsigned32,
						ltp8xIGMPProxyReportRangesID	Unsigned32,
						ltp8xIGMPProxyReportRangesStart	IpAddress,
						ltp8xIGMPProxyReportRangesEnd		IpAddress,
						ltp8xIGMPProxyReportRangesFromVLAN	INTEGER,
						ltp8xIGMPProxyReportRangesToVLAN 	Integer32,
						ltp8xIGMPProxyRowStatus				RowStatus
				}
				
							ltp8xIGMPProxyReportRangesSlot  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 1 }
							
							ltp8xIGMPProxyReportRangesID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 2 }
								
							ltp8xIGMPProxyReportRangesStart  OBJECT-TYPE
								SYNTAX     IpAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 3 }
								
							ltp8xIGMPProxyReportRangesEnd  OBJECT-TYPE
								SYNTAX     IpAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 4 }
								
							ltp8xIGMPProxyReportRangesFromVLAN  OBJECT-TYPE
								SYNTAX     INTEGER {
									all(65535) }
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 5 }
								
							ltp8xIGMPProxyReportRangesToVLAN  OBJECT-TYPE
								SYNTAX     Integer32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 6 }
								
							ltp8xIGMPProxyRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesEntry 10 }
								
		ltp8xIGMPProxyReportRangesGlobalTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xIGMPProxyReportRangesGlobalEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xSwitch 22 }
		
				ltp8xIGMPProxyReportRangesGlobalEntry OBJECT-TYPE
				SYNTAX                Ltp8xIGMPProxyReportRangesGlobalEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xIGMPProxyReportRangesGlobalID }
				::= { ltp8xIGMPProxyReportRangesGlobalTable 1 }
	
				Ltp8xIGMPProxyReportRangesGlobalEntry ::= SEQUENCE {
						ltp8xIGMPProxyReportRangesGlobalID	Unsigned32,
						ltp8xIGMPProxyReportRangesGlobalStart	IpAddress,
						ltp8xIGMPProxyReportRangesGlobalEnd		IpAddress,
						ltp8xIGMPProxyReportRangesGlobalFromVLAN	INTEGER,
						ltp8xIGMPProxyReportRangesGlobalToVLAN 	Integer32,
						ltp8xIGMPProxyGlobalRowStatus				RowStatus
				}
											
							ltp8xIGMPProxyReportRangesGlobalID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 1 }
								
							ltp8xIGMPProxyReportRangesGlobalStart  OBJECT-TYPE
								SYNTAX     IpAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 2 }
								
							ltp8xIGMPProxyReportRangesGlobalEnd  OBJECT-TYPE
								SYNTAX     IpAddress
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 3 }
								
							ltp8xIGMPProxyReportRangesGlobalFromVLAN  OBJECT-TYPE
								SYNTAX     INTEGER {
									all(65535) }
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 4 }
								
							ltp8xIGMPProxyReportRangesGlobalToVLAN  OBJECT-TYPE
								SYNTAX     Integer32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 5 }
								
							ltp8xIGMPProxyGlobalRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xIGMPProxyReportRangesGlobalEntry 10 }
								
			ltp8xMLDProxyReportRangesTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xMLDProxyReportRangesEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xSwitch 23 }
		
				ltp8xMLDProxyReportRangesEntry OBJECT-TYPE
				SYNTAX                Ltp8xMLDProxyReportRangesEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xMLDProxyReportRangesSlot, ltp8xMLDProxyReportRangesID }
				::= { ltp8xMLDProxyReportRangesTable 1 }
	
				Ltp8xMLDProxyReportRangesEntry ::= SEQUENCE {
						ltp8xMLDProxyReportRangesSlot	Unsigned32,
						ltp8xMLDProxyReportRangesID	Unsigned32,
						ltp8xMLDProxyReportRangesStart	Ipv6Address,
						ltp8xMLDProxyReportRangesEnd		Ipv6Address,
						ltp8xMLDProxyReportRangesFromVLAN	INTEGER,
						ltp8xMLDProxyReportRangesToVLAN 	Integer32,
						ltp8xMLDProxyRowStatus				RowStatus
				}
				
							ltp8xMLDProxyReportRangesSlot  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 1 }
							
							ltp8xMLDProxyReportRangesID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 2 }
								
							ltp8xMLDProxyReportRangesStart  OBJECT-TYPE
								SYNTAX     Ipv6Address
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 3 }
								
							ltp8xMLDProxyReportRangesEnd  OBJECT-TYPE
								SYNTAX     Ipv6Address
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 4 }
								
							ltp8xMLDProxyReportRangesFromVLAN  OBJECT-TYPE
								SYNTAX     INTEGER {
									all(65535) }
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 5 }
								
							ltp8xMLDProxyReportRangesToVLAN  OBJECT-TYPE
								SYNTAX     Integer32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 6 }
								
							ltp8xMLDProxyRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesEntry 10 }
								
		ltp8xMLDProxyReportRangesGlobalTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xMLDProxyReportRangesGlobalEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			::= { ltp8xSwitch 24 }
		
				ltp8xMLDProxyReportRangesGlobalEntry OBJECT-TYPE
				SYNTAX                Ltp8xMLDProxyReportRangesGlobalEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xMLDProxyReportRangesGlobalID }
				::= { ltp8xMLDProxyReportRangesGlobalTable 1 }
	
				Ltp8xMLDProxyReportRangesGlobalEntry ::= SEQUENCE {
						ltp8xMLDProxyReportRangesGlobalID	Unsigned32,
						ltp8xMLDProxyReportRangesGlobalStart	Ipv6Address,
						ltp8xMLDProxyReportRangesGlobalEnd		Ipv6Address,
						ltp8xMLDProxyReportRangesGlobalFromVLAN	INTEGER,
						ltp8xMLDProxyReportRangesGlobalToVLAN 	Integer32,
						ltp8xMLDProxyGlobalRowStatus				RowStatus
				}
											
							ltp8xMLDProxyReportRangesGlobalID  OBJECT-TYPE
								SYNTAX     Unsigned32
								MAX-ACCESS not-accessible
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 1 }
								
							ltp8xMLDProxyReportRangesGlobalStart  OBJECT-TYPE
								SYNTAX     Ipv6Address
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 2 }
								
							ltp8xMLDProxyReportRangesGlobalEnd  OBJECT-TYPE
								SYNTAX     Ipv6Address
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 3 }
								
							ltp8xMLDProxyReportRangesGlobalFromVLAN  OBJECT-TYPE
								SYNTAX     INTEGER {
									all(65535) }
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 4 }
								
							ltp8xMLDProxyReportRangesGlobalToVLAN  OBJECT-TYPE
								SYNTAX     Integer32
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 5 }
								
							ltp8xMLDProxyGlobalRowStatus  OBJECT-TYPE
								SYNTAX     RowStatus
								MAX-ACCESS read-write
								STATUS     current
								DESCRIPTION " "
								::= { ltp8xMLDProxyReportRangesGlobalEntry 10 }
						
	ltp8xP2PTable OBJECT-TYPE
			SYNTAX                SEQUENCE OF Ltp8xP2PEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					" "
			::= { ltp8x 10 }
		
		ltp8xP2PEntry OBJECT-TYPE
			SYNTAX                Ltp8xP2PEntry
			MAX-ACCESS        not-accessible
			STATUS                current
			DESCRIPTION
					""
			INDEX         { ltp8xP2PSlot }
			::= { ltp8xP2PTable 1 }
		
		Ltp8xP2PEntry ::= SEQUENCE {
				ltp8xP2PSlot				Unsigned32,
				ltp8xP2PEnabled				TruthValue
		}
		
					ltp8xP2PSlot OBJECT-TYPE
						SYNTAX  Unsigned32
						MAX-ACCESS read-only
						STATUS  current
						DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
						::= { ltp8xP2PEntry 1 }
					
					ltp8xP2PEnabled OBJECT-TYPE
						SYNTAX  TruthValue
						MAX-ACCESS read-write
						STATUS  current
						DESCRIPTION ""
						::= { ltp8xP2PEntry 2 }
						
	ltp8xPLC  OBJECT IDENTIFIER 	::= { ltp8x 11 }
	
			ltp8xPLCBoardStateTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xPLCBoardStateEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xPLC 1 }
			
			ltp8xPLCBoardStateEntry OBJECT-TYPE
				SYNTAX                Ltp8xPLCBoardStateEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xPLCBoardStateSlot }
				::= { ltp8xPLCBoardStateTable 1 }
			
			Ltp8xPLCBoardStateEntry ::= SEQUENCE {
					ltp8xPLCBoardStateSlot					Unsigned32,
					ltp8xPLCBoardStateRAMFree				Unsigned32,
					ltp8xPLCBoardStateLoadAverage1Minute	Unsigned32,
					ltp8xPLCBoardStateLoadAverage5Minutes	Unsigned32,
					ltp8xPLCBoardStateLoadAverage15Minutes	Unsigned32,
					ltp8xPLCBoardStateSensor1Temperature	Unsigned32,
					ltp8xPLCBoardStateSensor2Temperature	Unsigned32,
					ltp8xPLCBoardStateUptime				Unsigned32,
					ltp8xPLCBoardStateSerialNumber			DisplayString,
					ltp8xPLCBoardStateFirmwareRevision		DisplayString,
					ltp8xPLCBoardStateDiskFreeSpace			Unsigned32,
					ltp8xPLCBoardStateModuleVersion			DisplayString,
					ltp8xPLCBoardStateBuildTime				DisplayString,
					ltp8xPLCBoardStateBuildRevision			Unsigned32,
					ltp8xPLCBoardStateHardwareVesion		Unsigned32,
					ltp8xPLCBoardStateSensor1TemperatureExt	INTEGER,
					ltp8xPLCBoardStateSensor2TemperatureExt	INTEGER
			}
			
						ltp8xPLCBoardStateSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xPLCBoardStateEntry 1 }
							
						ltp8xPLCBoardStateRAMFree OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 2 }
						
						ltp8xPLCBoardStateLoadAverage1Minute OBJECT-TYPE
							SYNTAX   Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 3 }
							
						ltp8xPLCBoardStateLoadAverage5Minutes OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 4 }
							
						ltp8xPLCBoardStateLoadAverage15Minutes OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 5 }
							
						ltp8xPLCBoardStateSensor1Temperature OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 6 }
							
						ltp8xPLCBoardStateSensor2Temperature OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 7 }
							
						ltp8xPLCBoardStateUptime OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 8 }
							
						ltp8xPLCBoardStateSerialNumber OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 9 }
							
						ltp8xPLCBoardStateFirmwareRevision OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 10 }
							
						ltp8xPLCBoardStateDiskFreeSpace OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 11 }
							
						ltp8xPLCBoardStateModuleVersion OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 12 }
							
						ltp8xPLCBoardStateBuildTime OBJECT-TYPE
							SYNTAX  DisplayString
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 13 }
							
						ltp8xPLCBoardStateBuildRevision OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 14 }
							
						ltp8xPLCBoardStateHardwareVesion OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 15 }
							
						ltp8xPLCBoardStateSensor1TemperatureExt OBJECT-TYPE
							SYNTAX  INTEGER
								{ notValid(65535) }
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 16 }
							
						ltp8xPLCBoardStateSensor2TemperatureExt OBJECT-TYPE
							SYNTAX  INTEGER
								{ notValid(65535) }
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xPLCBoardStateEntry 17 }
							
		ltp8xSyncCounters OBJECT IDENTIFIER 	::= { ltp8x 15 }
	
			ltp8xSyncCountersTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xSyncCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xSyncCounters 1 }
			
			ltp8xSyncCountersEntry OBJECT-TYPE
				SYNTAX                Ltp8xSyncCountersEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xSyncCountersSlot }
				::= { ltp8xSyncCountersTable 1 }
			
			Ltp8xSyncCountersEntry ::= SEQUENCE {
					ltp8xSyncCountersSlot		Unsigned32,
					ltp8xSyncCountersConfig		Unsigned32,
					ltp8xSyncCountersState		Unsigned32
			}
			
						ltp8xSyncCountersSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION "Board's slot number when works within MA4000. For LTP-8X - always 1"
							::= { ltp8xSyncCountersEntry 1 }
							
						ltp8xSyncCountersConfig OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSyncCountersEntry 2 }
						
						ltp8xSyncCountersState OBJECT-TYPE
							SYNTAX   Unsigned32
							MAX-ACCESS read-only
							STATUS  current
							DESCRIPTION ""
							::= { ltp8xSyncCountersEntry 3 }
							
		ltp8xRawData OBJECT IDENTIFIER 	::= { ltp8x 90 }
							
			ltp8xRawMacTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xRawMacEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xRawData 1 }
			
			ltp8xRawMacEntry OBJECT-TYPE
				SYNTAX                Ltp8xRawMacEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xRawMacSlot, ltp8xRawMacChunkID }
				::= { ltp8xRawMacTable 1 }
			
			Ltp8xRawMacEntry ::= SEQUENCE {
					ltp8xRawMacSlot								Unsigned32,
					ltp8xRawMacChunkID							Unsigned32,
					ltp8xRawMacText								OCTET STRING
			}
			
						ltp8xRawMacSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawMacEntry 1 }	
						
						ltp8xRawMacChunkID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawMacEntry 2 }	
							
						ltp8xRawMacText OBJECT-TYPE
							SYNTAX  OCTET STRING (SIZE(0..65535))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawMacEntry 3 }
							
		ltp8xRawSwitchMacTable OBJECT-TYPE
				SYNTAX                SEQUENCE OF Ltp8xRawSwitchMacEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						" "
				::= { ltp8xRawData 2 }
			
			ltp8xRawSwitchMacEntry OBJECT-TYPE
				SYNTAX                Ltp8xRawSwitchMacEntry
				MAX-ACCESS        not-accessible
				STATUS                current
				DESCRIPTION
						""
				INDEX         { ltp8xRawSwitchMacSlot, ltp8xRawSwitchMacChunkID }
				::= { ltp8xRawSwitchMacTable 1 }
			
			Ltp8xRawSwitchMacEntry ::= SEQUENCE {
					ltp8xRawSwitchMacSlot								Unsigned32,
					ltp8xRawSwitchMacChunkID							Unsigned32,
					ltp8xRawSwitchMacText								OCTET STRING
			}
			
						ltp8xRawSwitchMacSlot OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawSwitchMacEntry 1 }	
						
						ltp8xRawSwitchMacChunkID OBJECT-TYPE
							SYNTAX  Unsigned32
							MAX-ACCESS not-accessible
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawSwitchMacEntry 2 }	
							
						ltp8xRawSwitchMacText OBJECT-TYPE
							SYNTAX  OCTET STRING (SIZE(0..65535))
							MAX-ACCESS read-write
							STATUS  current
							DESCRIPTION " "
							::= { ltp8xRawSwitchMacEntry 3 }
						
ltp8xMIBBoundary OBJECT-TYPE
	SYNTAX  Unsigned32
	MAX-ACCESS read-only
	STATUS  current
	DESCRIPTION "This boundary object needed for GETNEXT requests not to query anything from another mibs."
	::= { ltp8x 99 }
						
	
ltp8xTraps OBJECT IDENTIFIER 	::= { ltp8x 100 }

	ltp8xAlarmTraps  OBJECT IDENTIFIER   ::= {ltp8xTraps 1}

		ltp8xLoadAverageAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Load average has exceeded limit. Param1 = 0 - 1 min, 1 - 5min, 2 - 15min, description - LA values"
			::= { ltp8xAlarmTraps 1 }
			
		ltp8xRAMAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Ammount of free RAM < 32Mb. Param1 = ammount of free ram."
			::= { ltp8xAlarmTraps 2 }
			
		ltp8xLoginAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			" "
			::= { ltp8xAlarmTraps 3 }
			
		ltp8xConfigSaveAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config saving error."
			::= { ltp8xAlarmTraps 4 }
			
		ltp8xFirmwareUpdateAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Firmware update error."
			::= { ltp8xAlarmTraps 5 }
			
		ltp8xDuplicateMacAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Duplicate mac detected. Param1 = 0 - port, 1 - trunk; Param2 = port or trunk number; descr = mac-address"
			::= { ltp8xAlarmTraps 6 }
			
		ltp8xDataLinkLayerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Data link layer failure. Param1 = ifIndex."
			::= { ltp8xAlarmTraps 7 }
			
		ltp8xPhysicalLayerFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Period between physical layer failures is less than 30 seconds. Param1 = ifIndex"
			::= { ltp8xAlarmTraps 8 }
			
		ltp8xDataLinkLayerFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Period between data link layer failures is less that 30 seconds. Param1 = ifIndex"
			::= { ltp8xAlarmTraps 9 }
			
		ltp8xInterfaceCriticalLoadAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Interface is critically loaded. Param1 = ifIndex"
			::= { ltp8xAlarmTraps 10 }
			
		ltp8xFreeSpaceAlarmTrap	NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS	current
			DESCRIPTION
			"Ammount of free space is too low. Param1 = ammount of free space (kB)."
			::= { ltp8xAlarmTraps 11 }
			
		ltp8xTemperatureAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS	current
			DESCRIPTION
			"Temperature is too high. Param1 = sensor number, description = temperature values"
			::= { ltp8xAlarmTraps 12 }
			
		ltp8xFanAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Fan speed is too low or too high. Param1 = fan number, description = fans speeds"
			::= { ltp8xAlarmTraps 13 }
			
		ltp8xOntAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"PON channel has no ONT. Param1 = channel id"
			::= { ltp8xAlarmTraps 14 }
			
		ltp8xOntPhysicalAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT link down. Param1 = channel id, Param2 = ONT id, descr = ONT serial"
			::= { ltp8xAlarmTraps 15 }
			
		ltp8xOltUpdateAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"OLT update failed. Param1 = OLT id"
			::= { ltp8xAlarmTraps 16 }
			
		ltp8xOntUpdateAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT update failed. Param1 = channel id, Param2 = ONT id, descr = ONT serial"
			::= { ltp8xAlarmTraps 17 }
			
		ltp8xChannelFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Channel flapping detected. Param1 = channel id"
			::= { ltp8xAlarmTraps 18 }
			
		ltp8xOntFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT flapping detected. Param1 = channel id, Param2 = ONT id"
			::= { ltp8xAlarmTraps 19 }
			
		ltp8xFileDownloadAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"File download failed, Param1 = file type (0 - ONT firmware), descr - file name and ip-address"
			::= { ltp8xAlarmTraps 20 }
			
		ltp8xBatteryPowerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT switched to battery power. param1 - channelID, param2 - ontID, decription - serial"
			::= { ltp8xAlarmTraps 21 }
			
		ltp8xBatteryLowAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - channelID, param2 - ontID, decription - ONTn/m (ELTX00000000) Battery low"
			::= { ltp8xAlarmTraps 22 }
			
		ltp8xLanLosAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), param3 - LAN port id (m), ONTn/m (ELTX00000000) LAN LOS on port"
			::= { ltp8xAlarmTraps 23 }
			
		ltp8xOntConfigAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), descr - ONTn/m (ELTX00000000) ONT has no configuration"
			::= { ltp8xAlarmTraps 24 }
			
		ltp8xOntFirmwareDeleteAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"descr = <filename> deletion failure"
			::= { ltp8xAlarmTraps 25 }
			
		ltp8xLowRxPowerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONTn/m (ELTX00000000) low RX level (XX)"
			::= { ltp8xAlarmTraps 28 }
			
		ltp8xPowerSupplyAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Power supply[X] is offline"
			::= { ltp8xAlarmTraps 30 }
				
			
		ltp8xRedundancyMasterChannelFailTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - master channel id, Param2 - slave channel id, descr = Master channel fail at hostname. Switch from channel n to m"
			::= { ltp8xAlarmTraps 105 }
			
		ltp8xPonAlarmChannelTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), descr = PonAlarm : AlarmName from Channel n"
			::= { ltp8xAlarmTraps 213 }
			
		ltp8xPonAlarmONUiTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ONT id (m), descr = PonAlarm : AlarmName from ONTn/m (ELTX00000000)"
			::= { ltp8xAlarmTraps 214 }
			
		ltp8xONTSignalDegradeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ont id (m), Descr - ONTn/m (ELTX00000000) Signal degrade"
			::= { ltp8xAlarmTraps 217 }
			
		ltp8xONTHighRecvOpticalPwrTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ont id (m), Descr - ONTn/m (ELTX00000000) High received optical power"
			::= { ltp8xAlarmTraps 218 }
			
		ltp8xOLTDeviceNotWorkingTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id (n), Descr - OLTn is not working "
			::= { ltp8xAlarmTraps 219 }
			
		ltp8xChannelOntCntOverflowTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel id (n), Descr - ONUs count"
			::= { ltp8xAlarmTraps 220 }
			
		ltp8xConfigRereadAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config reread error."
			::= { ltp8xAlarmTraps 225 }
			
		ltp8xConfigBrokenAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Boot configuration broken. System configuration inconsistent."
			::= { ltp8xAlarmTraps 227 }
			
	ltp8xOkTraps  OBJECT IDENTIFIER   ::= {ltp8xTraps 2}

		ltp8xLoadAverageOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Load average is back to normal. Param1 = 0 - 1 min, 1 - 5min, 2 - 15min, description - LA values"
			::= { ltp8xOkTraps 1 }
			
		ltp8xRAMOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Ammount of free RAM > 32Mb. Param1 = ammount of free RAM"
			::= { ltp8xOkTraps 2 }
			
		ltp8xLoginOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Succesfull login."
			::= { ltp8xOkTraps 3 }
			
		ltp8xConfigSaveOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config saved succesfully."
			::= { ltp8xOkTraps 4 }
			
		ltp8xFirmwareUpdateOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Firmware updated succesfully."
			::= { ltp8xOkTraps 5 }
			
		ltp8xDuplicateMacOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Duplicate mac situation resolved. Param1 = interface number, descr = mac-address"
			::= { ltp8xOkTraps 6 }
			
		ltp8xDataLinkLayerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Data link layer is operational. Param1 = ifIndex."
			::= { ltp8xOkTraps 7 }
			
		ltp8xPhysicalLayerFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"No flapping detected on physical layer. Param1 = ifIndex."
			::= { ltp8xOkTraps 8 }
			
		ltp8xDataLinkLayerFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"No flapping detected on data link layer. Param1 = ifIndex."
			::= { ltp8xOkTraps 9 }
			
		ltp8xInterfaceCriticalLoadOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Normal load on interface. Param1 = ifIndex."
			::= { ltp8xOkTraps 10 }
			
		ltp8xFreeSpaceOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Ammount of free space is back to normal. Param1 = ammount of free space (kB)."
			::= { ltp8xOkTraps 11 }
			
		ltp8xTemperatureOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS current
			DESCRIPTION
			"Temperature is back to normal. Param1 = sensor number, description = temperature values"
			::= { ltp8xOkTraps 12 }
			
		ltp8xFanOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS current
			DESCRIPTION
			"Fan speed is back to normal. Param1 = fan number, description = fans speeds"
			::= { ltp8xOkTraps 13 }
			
		ltp8xOntOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"PON channel has ONT(s). Param1 = channel id"
			::= { ltp8xOkTraps 14 }
			
		ltp8xOntPhysicalOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT link up. Param1 = channel id, Param2 = ONT id, descr = ONT serial"
			::= { ltp8xOkTraps 15 }
			
		ltp8xOltUpdateOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"OLT update sucessful. Param1 = OLT id"
			::= { ltp8xOkTraps 16 }
			
		ltp8xOntUpdateOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT update successful. Param1 = channel id, Param2 = ONT id, descr = ONT serial"
			::= { ltp8xOkTraps 17 }
			
		ltp8xChannelFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Channel flapping ended. Param1 = channel id"
			::= { ltp8xOkTraps 18 }
			
		ltp8xOntFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT flapping ended. Param1 = channel id, Param2 = ONT id"
			::= { ltp8xOkTraps 19 }
			
		ltp8xFileDownloadOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"File download succeeded, Param1 = file type (0 - ONT firmware), descr - file name and ip-address"
			::= { ltp8xOkTraps 20 }
			
		ltp8xBatteryPowerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT switched to external power. param1 - channelID, param2 - ontID, decription - serial"
			::= { ltp8xOkTraps 21 }
			
		ltp8xBatteryLowOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - channelID, param2 - ontID, decription - ONTn/m (ELTX00000000) Battery charged"
			::= { ltp8xOkTraps 22 }
			
		ltp8xLanLosOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), param3 - LAN port id (m), LAN connection established on port m"
			::= { ltp8xOkTraps 23 }
			
		ltp8xOntConfigOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), descr - ONTn/m (ELTX00000000) ONT has received valid configuration"
			::= { ltp8xOkTraps 24 }
			
		ltp8xOntFirmwareDeleteOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"descr = <filename> successfully deleted"
			::= { ltp8xOkTraps 25 }
			
		ltp8xLowRxPowerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONTn/m (ELTX00000000) RX level (XX)"
			::= { ltp8xOkTraps 28 }
			
		ltp8xPowerSupplyOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Power supply[X] is online"
			::= { ltp8xOkTraps 30 }
			
		ltp8xLogoutTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"User logged out. Description : user <username> (ipaddress) disconnected from service <servicename>"
			::= { ltp8xOkTraps 102 }
			
		ltp8xOntDyingGaspTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel ID, Param2 - Ont ID, Descr - ONTn/m (ELTX00000000) Dying Gasp"
			::= { ltp8xOkTraps 103 }
			
		ltp8xRedundantChannelSwitchTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Redundant channel has been switched. Param1 - master channel; Param2 - slave channel"
			::= { ltp8xOkTraps 104 }
			
		ltp8xONTREITrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ONTn/m (ELTX00000000) Remote error indication (REI) value ..."
			::= { ltp8xOkTraps 106 }
			
		ltp8xONTPowerOffTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ONTn/m (ELTX00000000) power off"
			::= { ltp8xOkTraps 107 }
			
		ltp8xConfigChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config has been changed."
			::= { ltp8xOkTraps 200 }
			
		ltp8xONTStateChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ELTXhhhhhhhh pon_channel id state"
			::= { ltp8xOkTraps 210 }
			
		ltp8xONTConfigChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"operation ELTXhhhhhhhh pon_channel id description"
			::= { ltp8xOkTraps 211 }
			
		ltp8xChannelStateChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel, descr - state"
			::= { ltp8xOkTraps 212 }
			
		ltp8xOLTDeviceResetTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id"
			::= { ltp8xOkTraps 216 }
			
		ltp8xOLTDeviceWorkingTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id (n), Descr - OLTn is working "
			::= { ltp8xOkTraps 219 }
			
		ltp8xChannelOntCntOverflowOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel id (n), Descr - ONUs count"
			::= { ltp8xOkTraps 220 }
			
		ltp8xConfigRereadOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config reread success."
			::= { ltp8xOkTraps 225 }
			
		ltp8xRSSIUpdateTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"RSSI value change notification."
			::= { ltp8xOkTraps 226 }

	plc8AlarmTraps  OBJECT IDENTIFIER   ::= {ltp8xTraps 3}

		plc8LoadAverageAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Load average has exceeded limit. Param1 = 1 - 5min, 2 - 15min. Param2 = load average value."
			::= { plc8AlarmTraps 1 }
			
		plc8RAMAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Ammount of free RAM < 32Mb. Param1 = ammount of free ram."
			::= { plc8AlarmTraps 2 }
			
		plc8LoginAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			" "
			::= { plc8AlarmTraps 3 }
			
		plc8ConfigSaveAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config saving error."
			::= { plc8AlarmTraps 4 }
			
		plc8FirmwareUpdateAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Firmware update error."
			::= { plc8AlarmTraps 5 }
			
		plc8DuplicateMacAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Duplicate mac detected. Param1 = 0 - port, 1 - trunk; Param2 = port or trunk number; descr = mac-address"
			::= { plc8AlarmTraps 6 }
			
		plc8DataLinkLayerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Data link layer failure. Param1 = ifIndex."
			::= { plc8AlarmTraps 7 }
			
		plc8PhysicalLayerFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Period between physical layer failures is less than 30 seconds. Param1 = ifIndex"
			::= { plc8AlarmTraps 8 }
			
		plc8DataLinkLayerFlappingAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Period between data link layer failures is less that 30 seconds. Param1 = ifIndex"
			::= { plc8AlarmTraps 9 }
			
		plc8InterfaceCriticalLoadAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Interface is critically loaded. Param1 = ifIndex"
			::= { plc8AlarmTraps 10 }
			
		plc8FreeSpaceAlarmTrap	NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS	current
			DESCRIPTION
			""
			::= { plc8AlarmTraps 11 }
			
		plc8TemperatureAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS	current
			DESCRIPTION
			""
			::= { plc8AlarmTraps 12 }
			
		plc8FanAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8AlarmTraps 13 }
			
		plc8OntAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8AlarmTraps 14 }
			
		plc8OntPhysicalAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8AlarmTraps 15 }
			
		plc8FileDownloadAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"File download failed, Param1 = file type (0 - ONT firmware), descr - file name and ip-address"
			::= { plc8AlarmTraps 20 }
			
		plc8BatteryPowerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT switched to battery power. param1 - channelID, param2 - ontID, decription - serial"
			::= { plc8AlarmTraps 21 }
			
		plc8BatteryLowAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - channelID, param2 - ontID, decription - ONTn/m (ELTX00000000) Battery low"
			::= { plc8AlarmTraps 22 }
			
		plc8LanLosAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), param3 - LAN port id (m), ONTn/m (ELTX00000000) LAN LOS on port"
			::= { plc8AlarmTraps 23 }
			
		plc8OntConfigAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), descr - ONTn/m (ELTX00000000) ONT has no configuration"
			::= { plc8AlarmTraps 24 }
			
		plc8LowRxPowerAlarmTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONTn/m (ELTX00000000) low RX level (XX)"
			::= { plc8AlarmTraps 28 }
			
		plc8RedundancyMasterChannelFailTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - master channel id, Param2 - slave channel id, descr = Master channel fail at hostname. Switch from channel n to m"
			::= { plc8AlarmTraps 105 }

		plc8PonAlarmChannelTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), descr = PonAlarm : AlarmName from Channel n"
			::= { plc8AlarmTraps 213 }
			
		plc8PonAlarmONUiTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ONT id (m), descr = PonAlarm : AlarmName from ONTn/m (ELTX00000000)"
			::= { plc8AlarmTraps 214 }
			
		plc8ONTSignalDegradeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ont id (m), Descr - ONTn/m (ELTX00000000) Signal degrade"
			::= { plc8AlarmTraps 217 }
			
		plc8ONTHighRecvOpticalPwrTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id (n), Param2 - ont id (m), Descr - ONTn/m (ELTX00000000) High received optical power"
			::= { plc8AlarmTraps 218 }
			
		plc8OLTDeviceNotWorkingTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id (n), Descr - OLTn is not working "
			::= { plc8AlarmTraps 219 }
			
		plc8ChannelOntCntOverflowTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel id (n), Descr - ONUs count"
			::= { plc8AlarmTraps 220 }
			
	plc8OkTraps  OBJECT IDENTIFIER   ::= {ltp8xTraps 4}

		plc8LoadAverageOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Load average is back to normal. Param1 = 1 - 5min, 2 - 15min. Param2 = load average value"
			::= { plc8OkTraps 1 }
			
		plc8RAMOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Ammount of free RAM > 32Mb. Param1 = ammount of free RAM"
			::= { plc8OkTraps 2 }
			
		plc8LoginOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Succesfull login."
			::= { plc8OkTraps 3 }
			
		plc8ConfigSaveOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config saved succesfully."
			::= { plc8OkTraps 4 }
			
		plc8FirmwareUpdateOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Firmware updated succesfully."
			::= { plc8OkTraps 5 }
			
		plc8DuplicateMacOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Duplicate mac situation resolved. Param1 = interface number, descr = mac-address"
			::= { plc8OkTraps 6 }
			
		plc8DataLinkLayerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Data link layer is operational. Param1 = ifIndex."
			::= { plc8OkTraps 7 }
			
		plc8PhysicalLayerFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"No flapping detected on physical layer. Param1 = ifIndex."
			::= { plc8OkTraps 8 }
			
		plc8DataLinkLayerFlappingOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"No flapping detected on data link layer. Param1 = ifIndex."
			::= { plc8OkTraps 9 }
			
		plc8InterfaceCriticalLoadOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Normal load on interface. Param1 = ifIndex."
			::= { plc8OkTraps 10 }
			
		plc8FreeSpaceOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8OkTraps 11 }
			
		plc8TemperatureOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS current
			DESCRIPTION
			""
			::= { plc8OkTraps 12 }
			
		plc8FanOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS current
			DESCRIPTION
			""
			::= { plc8OkTraps 13 }
			
		plc8OntOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8OkTraps 14 }
			
		plc8OntPhysicalOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			""
			::= { plc8OkTraps 15 }
			
		plc8FileDownloadOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"File download succeeded, Param1 = file type (0 - ONT firmware), descr - file name and ip-address"
			::= { plc8OkTraps 20 }
			
		plc8BatteryPowerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONT switched to external power. param1 - channelID, param2 - ontID, decription - serial"
			::= { plc8OkTraps 21 }
			
		plc8BatteryLowOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - channelID, param2 - ontID, decription - ONTn/m (ELTX00000000) Battery charged"
			::= { plc8OkTraps 22 }
			
		plc8LanLosOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), param3 - LAN port id (m), LAN connection established on port m"
			::= { plc8OkTraps 23 }
			
		plc8OntConfigOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"param1 - Channel id (n), param2 - ONT id (m), descr - ONTn/m (ELTX00000000) ONT has received valid configuration"
			::= { plc8OkTraps 24 }
			
		plc8LowRxPowerOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"ONTn/m (ELTX00000000) RX level (XX)"
			::= { plc8OkTraps 28 }
			
		plc8OntDyingGaspTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel ID, Param2 - Ont ID, Descr - ONTn/m (ELTX00000000) Dying Gasp"
			::= { plc8OkTraps 103 }
			
		plc8RedundantChannelSwitchTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Redundant channel has been switched. Param1 - master channel; Param2 - slave channel;"
			::= { plc8OkTraps 104 }
			
		plc8ONTREITrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ONTn/m (ELTX00000000) Remote error indication (REI) value ..."
			::= { plc8OkTraps 106 }
			
		plc8ONTPowerOffTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ONTn/m (ELTX00000000) power off"
			::= { plc8OkTraps 107 }
			
		
			
		plc8ConfigChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Config has been changed."
			::= { plc8OkTraps 200 }
			
		plc8ONTStateChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel id; Param2 - ont id. Description - ELTXhhhhhhhh pon_channel id state"
			::= { plc8OkTraps 210 }
			
		plc8ONTConfigChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"operation ELTXhhhhhhhh pon_channel id description"
			::= { plc8OkTraps 211 }
			
		plc8ChannelStateChangeTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - channel, descr - state"
			::= { plc8OkTraps 212 }
			
		plc8OLTDeviceResetTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id"
			::= { plc8OkTraps 216 }
			
		plc8OLTDeviceWorkingTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - OLT id (n), Descr - OLTn is working "
			::= { plc8OkTraps 219 }
			
		plc8ChannelOntCntOverflowOkTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"Param1 - Channel id (n), Descr - ONUs count"
			::= { plc8OkTraps 220 }
			
		plc8RSSIUpdateTrap NOTIFICATION-TYPE
			OBJECTS {
				mcTrapExState,
				mcTrapLParam1,
				mcTrapLParam2,
				mcTrapLParam3,
				mcTrapID,
				mcTrapDescr}
			STATUS  current
			DESCRIPTION
			"RSSI value change notification."
			::= { plc8OkTraps 226 }

ltp8xObjectGroup OBJECT-GROUP
			OBJECTS 
			{
				ltp8xPONChannelSlot,
				ltp8xPONChannelID,
				ltp8xPONChannelState,
				ltp8xPONChannelONTCount,
				ltp8xPONChannelEnabled,
				
				ltp8xONTSlot,
				ltp8xONTSerial,
				ltp8xONTStateChannel,
				ltp8xONTStateID,
				ltp8xONTStateState,
				ltp8xONTStateEqualizationDelay,
				ltp8xONTStateFecState,
				ltp8xONTStateEncryptionKey,
				ltp8xONTStateOMCIPortId,
				ltp8xONTStateDistance,
				ltp8xONTStateReconfigure,
				ltp8xONTStateUpdateFirmware,
				
				ltp8xONTConfigSlot,
				ltp8xONTConfigSerial,
				ltp8xONTConfigChannel,
				ltp8xONTConfigID,
				ltp8xONTConfigServicesProfile,
				ltp8xONTConfigPassword,
				ltp8xONTConfigFecUp,
				ltp8xONTConfigRowStatus,
				
				ltp8xONTServiceOverrideID,
				ltp8xONTServiceOverrideSlot,
				ltp8xONTServiceOverrideSerial,
				ltp8xONTServiceOverrideEnabled,
				ltp8xONTServiceOverrideCustomerVID,
				ltp8xONTServiceOverrideCustomerCOS, 
				
				ltp8xONTServicesID,
				ltp8xONTServicesDescription,
				
				ltp8xOLTStateSlot,
				ltp8xOLTStateDriverVersion,
				ltp8xOLTStateFirmwareVersion,
				ltp8xOLTStateHardwareVersion,
				
				ltp8xOLTDhcpRASlot,
				
				ltp8xOLTConfigActivationSlot,
				ltp8xOLTConfigActivationPeriod,
				ltp8xOLTConfigActivationCheckPassword,
				
				ltp8xOLTMulticastStatsSlot,
				ltp8xOLTMulticastStatsChannel,
				ltp8xOLTMulticastStatsRecordID,
				ltp8xOLTMulticastStatsONTSerial,
				ltp8xOLTMulticastStatsMulticastAddress,
				ltp8xOLTMulticastStatsStart,
				ltp8xOLTMulticastStatsStop,
								
				ltp8xP2PSlot,
				ltp8xP2PEnabled,
				
				ltp8xMIBBoundary
			}
			STATUS  current
			DESCRIPTION
		           "none"
	            ::= { ltp8x 200 }
	            
ltp8xTrapsObjectGroup NOTIFICATION-GROUP
			NOTIFICATIONS
			{
				ltp8xLoadAverageAlarmTrap,
				ltp8xRAMAlarmTrap,
				ltp8xLoginAlarmTrap,
				ltp8xConfigSaveAlarmTrap,
				ltp8xFirmwareUpdateAlarmTrap,
				ltp8xDuplicateMacAlarmTrap,
				ltp8xDataLinkLayerAlarmTrap,
				ltp8xPhysicalLayerFlappingAlarmTrap,
				ltp8xDataLinkLayerFlappingAlarmTrap,
				ltp8xInterfaceCriticalLoadAlarmTrap,
				ltp8xFreeSpaceAlarmTrap,
				ltp8xTemperatureAlarmTrap,
				ltp8xFanAlarmTrap,
				ltp8xOntAlarmTrap,
				ltp8xOntPhysicalAlarmTrap,
				ltp8xOltUpdateAlarmTrap,
				ltp8xOntUpdateAlarmTrap,
				ltp8xOntFlappingAlarmTrap,
				
				ltp8xLoadAverageOkTrap,
				ltp8xRAMOkTrap,
				ltp8xLoginOkTrap,
				ltp8xConfigSaveOkTrap,
				ltp8xFirmwareUpdateOkTrap,
				ltp8xDuplicateMacOkTrap,
				ltp8xDataLinkLayerOkTrap,
				ltp8xPhysicalLayerFlappingOkTrap,
				ltp8xDataLinkLayerFlappingOkTrap,
				ltp8xInterfaceCriticalLoadOkTrap,
				ltp8xFreeSpaceOkTrap,
				ltp8xTemperatureOkTrap,
				ltp8xFanOkTrap,
				ltp8xOntOkTrap,
				ltp8xOntPhysicalOkTrap,
				ltp8xOltUpdateOkTrap,
				ltp8xOntUpdateOkTrap,
				ltp8xOntFlappingOkTrap,
				
				ltp8xConfigChangeTrap,
				
				plc8LoadAverageAlarmTrap,
				plc8RAMAlarmTrap,
				plc8LoginAlarmTrap,
				plc8ConfigSaveAlarmTrap,
				plc8FirmwareUpdateAlarmTrap,
				plc8DuplicateMacAlarmTrap,
				plc8DataLinkLayerAlarmTrap,
				plc8PhysicalLayerFlappingAlarmTrap,
				plc8DataLinkLayerFlappingAlarmTrap,
				plc8InterfaceCriticalLoadAlarmTrap,
				plc8FreeSpaceAlarmTrap,
				plc8TemperatureAlarmTrap,
				plc8FanAlarmTrap,
				plc8OntAlarmTrap,
				plc8OntPhysicalAlarmTrap,
				
				plc8LoadAverageOkTrap,
				plc8RAMOkTrap,
				plc8LoginOkTrap,
				plc8ConfigSaveOkTrap,
				plc8FirmwareUpdateOkTrap,
				plc8DuplicateMacOkTrap,
				plc8DataLinkLayerOkTrap,
				plc8PhysicalLayerFlappingOkTrap,
				plc8DataLinkLayerFlappingOkTrap,
				plc8InterfaceCriticalLoadOkTrap,
				plc8FreeSpaceOkTrap,
				plc8TemperatureOkTrap,
				plc8FanOkTrap,
				plc8OntAlarmTrap,
				plc8OntPhysicalAlarmTrap,
				plc8ConfigChangeTrap
				
			}
			STATUS  current
			DESCRIPTION
		           "none"
	            ::= { ltp8x 201 }

END
