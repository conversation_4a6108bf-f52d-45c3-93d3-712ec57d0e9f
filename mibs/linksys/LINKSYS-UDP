LINKSYS-<PERSON><PERSON> DEFINITIONS ::= BEGIN

-- Title:      LINKSYS UDP Private Extension
-- Version:    **********
-- Date:       17 May 2004

IMPORTS
    rnd                                                     FROM LINKSYS-MIB
    ipAddrEntry                                             FROM IP-MIB
    ipCidrRouteEntry,ipCidrRouteDest,
    ipCidrRouteMask, ipCidrRouteTos, ipCidrRouteNextHop     FROM IP-FORWARD-MIB
    Unsigned32, Integer32, Counter32, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    MODULE-IDENTITY, OBJECT-TYPE                            FROM SNMPv2-SMI
    DisplayString, RowStatus, TruthValue,
    TEXTUAL-CONVENTION                                      FROM SNMPv2-TC
    InetAddress,InetAddressType                             FROM INET-ADDRESS-MIB;

rsUDP MODULE-IDENTITY
      LAST-UPDATED "200406010000Z"
      ORGANIZATION "Linksys LLC."
      CONTACT-INFO
           "www.linksys.com/business/support"
      DESCRIPTION
           "The private MIB module definition for RND UDP MIB."
      REVISION "200406010000Z"
      DESCRIPTION
           "Initial version of this MIB."
      ::= { rnd 42 }



rsUdpRelayTable  OBJECT-TYPE
   SYNTAX  SEQUENCE OF RsUdpRelayEntry
   MAX-ACCESS not-accessible
   STATUS     current
   DESCRIPTION
       "This table contains the udp relay configuration per port."
   ::= {rsUDP 1}

rsUdpRelayEntry  OBJECT-TYPE
   SYNTAX     RsUdpRelayEntry
   MAX-ACCESS not-accessible
   STATUS     current
   DESCRIPTION
       " The row definition for this table."

   INDEX {rsUdpRelayDstPort,
          rsUdpRelaySrcIpInf,
          rsUdpRelayDstIpAddr }
   ::= {rsUdpRelayTable 1}

RsUdpRelayEntry  ::= SEQUENCE {
    rsUdpRelayDstPort   INTEGER,
    rsUdpRelaySrcIpInf  IpAddress,
    rsUdpRelayDstIpAddr IpAddress,
    rsUdpRelayStatus    RowStatus,
    rsUdpRelayUserInfo  INTEGER
}

rsUdpRelayDstPort OBJECT-TYPE
   SYNTAX     INTEGER
   MAX-ACCESS read-only
   STATUS     current
   DESCRIPTION
       "The UDP port number in the UDP message header."
   ::= {rsUdpRelayEntry 1}

rsUdpRelaySrcIpInf OBJECT-TYPE
   SYNTAX      IpAddress
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
        "The source interface IP that receives UDP message.
         *************** from all IP interface.
         0.0.0.0 - ************* and ********* - ***************
         not relevant addresses."
--    DEFVAL  { *************** }
   ::= {rsUdpRelayEntry 2}

rsUdpRelayDstIpAddr OBJECT-TYPE
   SYNTAX      IpAddress
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
        "The destination IP address the UDP message will be forward.
         0.0.0.0 does not forward, *************** broadcasts to all
         addresses."
--    DEFVAL  { *************** }
   ::= {rsUdpRelayEntry 3}

rsUdpRelayStatus OBJECT-TYPE
   SYNTAX       RowStatus
   MAX-ACCESS  read-write
   STATUS       current
   DESCRIPTION
       "The status of a table entry.
        It is used to delete an entry from this table."
  ::= { rsUdpRelayEntry 4 }

rsUdpRelayUserInfo  OBJECT-TYPE
   SYNTAX      INTEGER
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
        "The information used for implementation purposes"
  DEFVAL  { 0 }
  ::= { rsUdpRelayEntry 5 }

rsUdpRelayMibVersion  OBJECT-TYPE
   SYNTAX      INTEGER
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
        "Mib version. The current version is 1."
   ::= {rsUDP 2}

rlUdpSessionTable  OBJECT-TYPE
    SYNTAX  SEQUENCE OF RlUdpSessionEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "This table contains the udp sessions information"
    ::= {rsUDP 3}

rlUdpSessionEntry  OBJECT-TYPE
    SYNTAX     RlUdpSessionEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
         " The row definition for this table."
    INDEX { rlUdpSessionLocalAddrType,
            rlUdpSessionLocalAddr,
            rlUdpSessionLocalPort,
            rlUdpSessionAppInst}
   ::= {rlUdpSessionTable 1}

RlUdpSessionEntry  ::= SEQUENCE {
    rlUdpSessionLocalAddrType           InetAddressType,
    rlUdpSessionLocalAddr               InetAddress,
    rlUdpSessionLocalPort               INTEGER,
    rlUdpSessionAppInst                 INTEGER,
    rlUdpSessionAppName                 DisplayString
}

rlUdpSessionLocalAddrType  OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The type of the rlUdpSessionLocalAddress address"
    ::= { rlUdpSessionEntry 1 }

rlUdpSessionLocalAddr OBJECT-TYPE
    SYNTAX     InetAddress
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The UDP port session number."
    ::= {rlUdpSessionEntry 2}

rlUdpSessionLocalPort OBJECT-TYPE
    SYNTAX     INTEGER
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The UDP port local IP address."
    ::= {rlUdpSessionEntry 3}

rlUdpSessionAppInst OBJECT-TYPE
    SYNTAX     INTEGER (0..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The instance ID for the application on the port (for future use)."
    ::= {rlUdpSessionEntry 4}

rlUdpSessionAppName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..12))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The name of the application that created the session."
    ::= {rlUdpSessionEntry 5}

END

