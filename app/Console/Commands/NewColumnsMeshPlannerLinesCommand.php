<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Repository\ProjectAutoRepository;

class NewColumnsMeshPlannerLinesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mesh_planner_lines:set_new_columns_value';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            (new ProjectAutoRepository())->setStartNodeIdAndEndNodeId();
            $this->info("successfuly");
        } catch (\Exception $e) {
            $this->info("ERROR {$e->getMessage()} - {$e->getLine()}");
        }
    }
}
