<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BeginImportPoiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'terms_and_conditions' => [
                'accepted',
            ]
        ];
    }

    public function messages()
    {
        return [
            'terms_and_conditions.required' => 'Terms and condiions checkbox is required',
        ];
    }
}
