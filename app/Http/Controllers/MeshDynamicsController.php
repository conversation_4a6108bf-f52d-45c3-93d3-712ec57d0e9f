<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\MeshDynamics;
use App\Models\RMQ;
use Illuminate\Http\Request;
use Toastr;
use Illuminate\Support\Facades\Auth;
use Redirect;
use LibreNMS\Config;
use Illuminate\Support\Facades\DB;


class MeshDynamicsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function clearHistory(Request $request)
    {
        if ($request->input('device_id')) {
            $deviceId = (int) $request->input('device_id');
            DB::statement("delete from arra_mesh_dynamics_device_details where device_id={$deviceId}; ");
            DB::statement("delete from arra_mesh_dynamics_devices_list where device_id={$deviceId}; ");
            DB::statement("delete from wireless_sensors where device_id={$deviceId}; ");
            return 1;
        }
        return 0;
    }

    public function verifydevicediscorvery(Request $request){

        if(isset($request->device_id) && $request->device_id && isset($request->device_identifier) && $request->device_identifier){

            return MeshDynamics::hadDeviceDiscoveryCounter($request->device_id,$request->device_identifier);

        }

        return 0;
    }


    public function rundiscorvery(Request $request){

        if(isset($request->dhlist) && $request->dhlist && isset($request->did) && (int) $request->did){

            $devices = $request->dhlist;

            if($devices && is_array($devices) && sizeof($devices) > 0){

                $device_id = (int) $request->did;
                $token = $request->session()->token();
                $device_identifier = md5($token.time().Auth::user()->user_id);
                if (session()->has('temp_mesh_discovery_devices')) {
                    session()->forget('temp_mesh_discovery_devices');
                } else {
                    session()->put('temp_mesh_discovery_devices', $devices);
                }
                $params = array(
                    'device_id' => $device_id,
                    'device_identifier' => $device_identifier,
                    'discovery_counter' => sizeof($devices),
                    'devices_list' => json_encode($devices)
                );
                $dcounter = MeshDynamics::insertDeviceDiscoveryCounter($params);
                if($dcounter){

                    foreach ($devices as $d_hostname){
                        RMQ::setDeviceDiscoveryMessageRmq($d_hostname,$params);
                    }

                }

                return $params;

            }

        }

        return 0;
    }


    public function getdata(Request $request){

        $params = array();



        if(isset($request->type) && isset($request->device_id)){

            $params['device_id'] = trim(strip_tags($request->device_id));
            $params['type'] = trim(strip_tags($request->type));

            if(isset($request->order_type)){
                $params['order_type'] = trim(strip_tags($request->order_type));
            }
            if(isset($request->order_value)){
                $params['order_value'] = trim(strip_tags($request->order_value));
            }

            $table_data = MeshDynamics::getDevicesByMesh($params);

            if($table_data){

                $mesh_type = $params['type'];
                return view('mesh-dynamics.mesh-table', compact('table_data','mesh_type'));

            }
        }

        return '0';

    }



}