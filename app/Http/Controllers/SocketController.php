<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Device;
use App\Models\GroupActions;
use App\Models\CPM;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;
use LibreNMS\Alerting\QueryBuilderFilter;
use LibreNMS\Alerting\QueryBuilderFluentParser;
use Toastr;
use Illuminate\Support\Facades\Auth;
use Redirect;

class SocketController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');      
        $this->middleware('2fa');
        $this->middleware(function ($request, $next) {
            if(!\Auth::user()->isAdmin()){
                Redirect::to('/')->send();
            }
            return $next($request);
        });
        
        
    }

    public function index()
    {        
        GroupActions::getAllQueue();
        die('x');
        
        return view('socket.index');
    }

}