<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ArraMeshDynamicsNodes;
use Toastr;
use Illuminate\Support\Facades\Auth;
use Redirect;
use App\Models\ArraMeshDynamicsMeshs;
use Mail;


class TestController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('2fa');
        $this->middleware(function ($request, $next) {
            if(!\Auth::user()->isAdmin()){
                Redirect::to('/')->send();
            }
            return $next($request);
        });


    }

    public function index()
    {
        die('xxxx');
        $to_name = 'Test Name';
        $to_email = '<EMAIL>';
        
        try {
                Mail::send('mails.testing', $data, function($message) use ($to_name, $to_email) {
                        $message->to($to_email, $to_name)->subject('Testing Notification');
                    
                        $message->from('<EMAIL>','ARRA Team');
                    }
                );
        } catch (Exception $e) {
            
            echo $e->getMessage();
            die('_error_');
//             Log::channel('testing')->error('message '.Auth::user()->id.' with message: '.$e->getMessage());
        }
        
        die('');
        
        $device_id = 16;
//        $device_id = 2;
        ArraMeshDynamicsNodes::updateDeviceNodes($device_id);
        ArraMeshDynamicsMeshs::updateDeviceMeshs($device_id);
        die('');
    }

}