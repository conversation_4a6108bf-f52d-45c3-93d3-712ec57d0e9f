<?php
namespace App\Models;

use App\Models\Device;
use App\Models\DeviceStatus;
use App\Models\Location;
use App\Models\GroupTags;
use Illuminate\Database\Eloquent\Builder;
use LibreNMS\Config;
use LibreNMS\Util\Rewrite;
use LibreNMS\Util\Url;
use LibreNMS\Util\Time;
use LibreNMS\Alert\AlertUtil;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DeviceCustomDetails extends BaseModel
{

    public static function getDeviceGroupByID($device_id){

        $obj = DB::table("device_group_device")
            ->select('device_group_id as group_id')
            ->where("device_id", "=", "{$device_id}");
        $obj_info = $obj->first();

        if(isset($obj_info->group_id)){
            return $obj_info->group_id;
        }

        return 0;

    }


    public static function simpleArraDeviceName($device,$down = true)
    {
        if(is_array($device)){
            $device = (object) $device;
        }

        if(isset($device->purpose) && $device->purpose){
            $output = $device->purpose;
        } else {
            $output = $device->hostname;
        }


        return $output;
    }

    public static function getArraDeviceName($device)
    {

        $device = (object) $device;


        if(isset($_SESSION['arra_device_group_name']) && $_SESSION['arra_device_group_name']){
            $group_name = $_SESSION['arra_device_group_name'];
        } else {
            $group_name = GroupTags::getUserGroupName($device->device_id);
        }

        if($group_name){
            $group_name = $group_name." ";
        }

        if($device->purpose){
            $hostname = static::arraDeviceLink($device);
        } else {
            $hostname = '';
        }


        $hostname_html = '';

        $long_ip_pices = explode(":",$device->hostname);
        if(sizeof($long_ip_pices) > 1){



            $hostname_short_url = static::arraDeviceLink($device,substr($long_ip_pices[sizeof($long_ip_pices) - 2],-2)."".$long_ip_pices[sizeof($long_ip_pices) - 1]);
            $hostname_long_url = static::arraDeviceLink($device,$device->hostname);

            $hostname_html =  $group_name.
                "<span id='short_ip_".$device->device_id."' > ".$hostname_short_url." <a onclick='showlongdevice(".$device->device_id.")' class='' href='javascript:void(0)'><span class='arra-custom-sign'>+</span></a></span>".
                "<span style='display:none' id='long_ip_".$device->device_id."' > ".$hostname_long_url." <a onclick='showshortdevice(".$device->device_id.")' class='' href='javascript:void(0)'><span class='arra-custom-sign'>-</span></a></span>";


        } else {
            $short_ip_pices = explode(".",$device->hostname);
            if(sizeof($short_ip_pices) > 1){


                $hostname_short_url = static::arraDeviceLink($device,$short_ip_pices[sizeof($short_ip_pices) - 2].".".$short_ip_pices[sizeof($short_ip_pices) - 1]);
                $hostname_long_url = static::arraDeviceLink($device,$device->hostname);

                $hostname_html =  $group_name.
                    "<span id='short_ip_".$device->device_id."' > ".$hostname_short_url." <a onclick='showlongdevice(".$device->device_id.")' class='' href='javascript:void(0)'><span class=' arra-custom-sign'>+</span></a></span>".
                    "<span style='display:none' id='long_ip_".$device->device_id."' > ".$hostname_long_url." <a onclick='showshortdevice(".$device->device_id.")' class='' href='javascript:void(0)'><span class='arra-custom-sign'>-</span></a></span>";


            } else {

                $hostname_html = static::arraDeviceLink($device,$device->hostname);
            }
        }

        $hostname .= '<br /><div class="">' . $hostname_html."</div>";


        return $hostname;
    }

    public static function arraDeviceLink($device, $text = null, $vars = [], $start = 0, $end = 0, $escape_text = 1, $overlib = 1)
    {
        if (is_null($device)) {
            return '';
        }

        if (!$start) {
            $start = Carbon::now()->subDay()->timestamp;
        }

        if (!$end) {
            $end = Carbon::now()->timestamp;
        }

        if (!$text) {
            $text = $device->purpose;
        }

//        if ($escape_text) {
//            $text = htmlentities($text);
//        }

        $class = self::deviceLinkDisplayClass($device)." arra-ip-link-normal";
        $graphs = self::getOverviewGraphsForDevice($device);
        $url = Url::deviceUrl($device, $vars);

        // beginning of overlib box contains large hostname followed by hardware & OS details
        $contents = '<div><span class="list-large">' . $device->sysName . '</span>';
        if ($device->hardware) {
            $contents .= ' - ' . htmlentities($device->hardware);
        }

        if ($device->os) {
            $contents .= ' - ' . htmlentities(Config::getOsSetting($device->os, 'text'));
        }

        if ($device->version) {
            $contents .= ' ' . htmlentities($device->version);
        }

        if ($device->features) {
            $contents .= ' (' . htmlentities($device->features) . ')';
        }

        if ($device->location_id) {
            $contents .= ' - ' . htmlentities($device->location);
        }

        $contents .= '</div>';

        foreach ((array)$graphs as $entry) {
            $graph = isset($entry['graph']) ? $entry['graph'] : 'unknown';
            $graphhead = isset($entry['text']) ? $entry['text'] : 'unknown';
            $contents .= '<div class="overlib-box">';
            $contents .= '<span class="overlib-title">' . $graphhead . '</span><br />';
            $contents .= Url::minigraphImage($device, $start, $end, $graph);
            $contents .= Url::minigraphImage($device, Carbon::now()->subWeek(1)->timestamp, $end, $graph);
            $contents .= '</div>';
        }

        if ($overlib == 0) {
            $link = $contents;
        } else {
            $contents = self::escapeBothQuotes($contents);
            $link = Url::overlibLink($url, $text, $contents, $class);
        }

        return $link;
    }


    private static function deviceLinkDisplayClass($device)
    {
        if ($device->disabled) {
            return 'list-device-disabled';
        }

        if ($device->ignore) {
            return $device->status ? 'list-device-ignored-up' : 'list-device-ignored';
        }

        $device_arra_status = DeviceStatus::getArraDeviceStatus($device->device_id);
        if($device_arra_status == 'red') {

            return $device->status ? 'list-device' : 'list-device-down';
        } else {
            return 'list-device';
        }

    }

    private static function getOverviewGraphsForDevice($device)
    {
        if ($device->snmp_disable) {
            return Config::getOsSetting('ping', 'over');
        }

        if ($graphs = Config::getOsSetting($device->os, 'over')) {
            return $graphs;
        }

        if ($os_group = Config::getOsSetting($device->os, 'os_group')) {
            $name = key($os_group);
            if (isset($os_group[$name]['over'])) {
                return $os_group[$name]['over'];
            }
        }

        return Config::getOsSetting('default', 'over');
    }

    private static function escapeBothQuotes($string)
    {
        return str_replace(["'", '"'], "\'", $string);
    }
}