<?php
namespace App\Models;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class Cmds extends BaseModel
{
    

    
    public static function getDeviceCMDsByMAC($mac)
    {
        $mac = str_replace(":", "", $mac);
        $mac = substr_replace($mac, ":", 4, 0);
        $mac = substr_replace($mac, ":", 9, 0);
        
        if($mac){
            
           
            $obj = DB::table("device_cmds")
            ->leftjoin("devices", "devices.device_id", "=", "device_cmds.device_id")
            ->select('device_cmds.cmds','device_cmds.device_id')
            // ->where('ports.ifName', '=', "eth0")
            // ->where('ports.ifPhysAddress', '=', "{$mac}")
            ->where('devices.hostname','like',"%{$mac}")
            ->first();
            

            if (isset($obj->cmds)  && $obj->cmds ) {
                $cmds = $obj->cmds;
                
                static::deleteData($obj->device_id);
            	return $cmds;
            }
            
        }
        
    
        return false;
    
    }
    
    public static function getDeviceDetails($device_id)
    {
        $obj = DB::table("devices")
        ->select('*')
        ->where('device_id', '=', "{$device_id}")
        ->first();
    
    	
        if ($obj) {
            return (array) $obj;
        }
    
        return false;
    
    }

    public static function getCMDsDeviceDetails($device_id)
    {
        $obj = DB::table("device_cmds")
        ->select('*')
        ->where('device_id', '=', "{$device_id}")
        ->first();
    
    	
        if ($obj) {
            return (array) $obj;
        }
    
        return false;
    
    }



    
    public static function deleteData($device_id){    
    
        $delete_temp = DB::table('device_cmds')->where('device_id', '=', "{$device_id}")->delete();
    
        return true;
    }
    
    

    
    public static function saveData($params){
        
        
        $result = DB::table("device_cmds")
        ->select("*")
        ->where("device_id", "=", "{$params['device_id']}")
        ->first();

        
        if(!$result){
            
            $id = DB::table('device_cmds')->insertGetId(
                    [
                        'device_id' => $params['device_id'],
                        'cmds' => $params['cmds'],
                        'hostname' => '',
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s")
                    ]
                );
            return $id;
            
        } else {
            
            $update_user = DB::table("device_cmds")
            ->where("id", "=", $result->id)
            ->update(
                [
                    'cmds' => $params['cmds'],
                    'updated_at' => date("Y-m-d H:i:s")
                ]
                );
            
            return $result->id;
        }
        
    }
    
}
