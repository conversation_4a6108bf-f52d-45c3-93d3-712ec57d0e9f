<?php
namespace App\Models;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Auth;
use function GuzzleHttp\json_decode;
use function Opis\Closure\unserialize;
use App\Models\GroupSettings;

class Reseller extends BaseModel
{
    
    public static function getBillingUrl($token){
    
        $call_url = "get-admin-url.php?token=".trim($token);
        $params = array();
        $response = GroupSettings::doApiCall($call_url,"GET",$params,false);
    
        return $response;
    
    }
    
    
    public static function getResellerUrl($token){
        
        $call_url = "get-reseller-url.php?token=".trim($token);
        $params = array();
        $response = GroupSettings::doApiCall($call_url,"GET",$params,false);

        return $response;
        
    }
    
    
}