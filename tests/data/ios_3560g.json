{"bgp-peers": {"discovery": {"bgpPeers": [{"astext": "", "bgpPeerIdentifier": "*************", "bgpPeerRemoteAs": 64513, "bgpPeerState": "idle", "bgpPeerAdminStatus": "stop", "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 0, "bgpPeerOutTotalMessages": 0, "bgpPeerFsmEstablishedTime": 0, "bgpPeerInUpdateElapsedTime": 0, "context_name": "", "bgpLocalAs": 64513, "vrfLocalAs": null}, {"astext": "", "bgpPeerIdentifier": "*************", "bgpPeerRemoteAs": 64513, "bgpPeerState": "idle", "bgpPeerAdminStatus": "stop", "bgpLocalAddr": "0.0.0.0", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 0, "bgpPeerOutUpdates": 0, "bgpPeerInTotalMessages": 0, "bgpPeerOutTotalMessages": 0, "bgpPeerFsmEstablishedTime": 0, "bgpPeerInUpdateElapsedTime": 0, "context_name": "", "bgpLocalAs": 64513, "vrfLocalAs": null}], "bgpPeers_cbgp": [{"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "multicast", "AcceptedPrefixes": 0, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 0, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 0, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 0, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "unicast", "AcceptedPrefixes": 0, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 0, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 0, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 0, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "multicast", "AcceptedPrefixes": 0, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 0, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 0, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 0, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "unicast", "AcceptedPrefixes": 0, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 0, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 0, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 0, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}]}, "poller": {"bgpPeers": [{"astext": "", "bgpPeerIdentifier": "*************", "bgpPeerRemoteAs": 64513, "bgpPeerState": "established", "bgpPeerAdminStatus": "start", "bgpLocalAddr": "*************", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 5, "bgpPeerOutUpdates": 4, "bgpPeerInTotalMessages": 100, "bgpPeerOutTotalMessages": 99, "bgpPeerFsmEstablishedTime": 893, "bgpPeerInUpdateElapsedTime": 0, "context_name": "", "bgpLocalAs": 64513, "vrfLocalAs": null}, {"astext": "", "bgpPeerIdentifier": "*************", "bgpPeerRemoteAs": 64513, "bgpPeerState": "established", "bgpPeerAdminStatus": "start", "bgpLocalAddr": "*************", "bgpPeerRemoteAddr": "0.0.0.0", "bgpPeerDescr": "", "bgpPeerInUpdates": 8, "bgpPeerOutUpdates": 4, "bgpPeerInTotalMessages": 19, "bgpPeerOutTotalMessages": 15, "bgpPeerFsmEstablishedTime": 467, "bgpPeerInUpdateElapsedTime": 0, "context_name": "", "bgpLocalAs": 64513, "vrfLocalAs": null}], "bgpPeers_cbgp": [{"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "multicast", "AcceptedPrefixes": 2, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 4, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 2, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 4, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "unicast", "AcceptedPrefixes": 15, "DeniedPrefixes": 0, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 6, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 15, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 0, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 6, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "multicast", "AcceptedPrefixes": 2, "DeniedPrefixes": 2, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 4, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 2, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 2, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 4, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}, {"bgpPeerIdentifier": "*************", "afi": "ipv4", "safi": "unicast", "AcceptedPrefixes": 17, "DeniedPrefixes": 3, "PrefixAdminLimit": 0, "PrefixThreshold": 0, "PrefixClearThreshold": 0, "AdvertisedPrefixes": 6, "SuppressedPrefixes": 0, "WithdrawnPrefixes": 0, "AcceptedPrefixes_delta": 17, "AcceptedPrefixes_prev": 0, "DeniedPrefixes_delta": 3, "DeniedPrefixes_prev": 0, "AdvertisedPrefixes_delta": 6, "AdvertisedPrefixes_prev": 0, "SuppressedPrefixes_delta": 0, "SuppressedPrefixes_prev": 0, "WithdrawnPrefixes_delta": 0, "WithdrawnPrefixes_prev": 0, "context_name": ""}]}}, "os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.*******.615", "sysDescr": "Cisco IOS Software, C3560 Software (C3560-IPSERVICESK9-M), Version 15.0(2)SE11, RELEASE SOFTWARE (fc3)\nTechnical Support: http://www.cisco.com/techsupport\r\nCopyright (c) 1986-2017 by Cisco Systems, Inc.\r\nCompiled Sat 19-Aug-17 09:21 by prod_rel_team", "sysContact": null, "version": null, "hardware": null, "features": null, "os": "ios", "type": "network", "serial": null, "icon": "cisco.svg", "location": null}]}, "poller": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.*******.615", "sysDescr": "Cisco IOS Software, C3560 Software (C3560-IPSERVICESK9-M), Version 15.0(2)SE11, RELEASE SOFTWARE (fc3)\nTechnical Support: http://www.cisco.com/techsupport\r\nCopyright (c) 1986-2017 by Cisco Systems, Inc.\r\nCompiled Sat 19-Aug-17 09:21 by prod_rel_team", "sysContact": "<private>", "version": "15.0(2)SE11", "hardware": "WS-C3560G-24TS-S", "features": "IPSERVICESK9", "os": "ios", "type": "network", "serial": "FOC1426Y2AY", "icon": "cisco.svg", "location": "<private>"}]}}}