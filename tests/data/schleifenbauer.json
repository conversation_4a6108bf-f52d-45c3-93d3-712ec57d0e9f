{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.31034.11.1", "sysDescr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sysContact": null, "version": null, "hardware": null, "features": null, "os": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "power", "serial": null, "icon": "schleifenbauer.svg", "location": null}]}, "poller": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.31034.11.1", "sysDescr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sysContact": "<private>", "version": "- version 250, build 180821PL1736", "hardware": "SSDCHIN1307-002", "features": null, "os": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "power", "serial": "SVNL00056319", "icon": "schleifenbauer.svg", "location": "<private>"}]}}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "en", "ifName": "en", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifConnectorPresent": null, "ifPromiscuousMode": null, "ifHighSpeed": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "en", "ifPhysAddress": null, "ifHardType": null, "ifLastChange": 0, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "en", "ifName": "en", "portName": null, "ifIndex": 1, "ifSpeed": 100000000, "ifConnectorPresent": null, "ifPromiscuousMode": null, "ifHighSpeed": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "en", "ifPhysAddress": "d02212b07a57", "ifHardType": null, "ifLastChange": 204, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 136133, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 126378, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 23559478, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 23203132, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 47308, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 1563, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 54771, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.4.0", "sensor_index": "sdbMgmtStsDuplicateDevices.0", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "Duplicate Device Addresses", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.3.0", "sensor_index": "sdbMgmtStsNewDevices.0", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "New Devices", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 2, "sensor_limit_warn": 1, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.1", "sensor_index": "SVNL00056319-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L1 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0.32, "sensor_limit": 32, "sensor_limit_warn": 28, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.2", "sensor_index": "SVNL00056319-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L2 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 32, "sensor_limit_warn": 28, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.3", "sensor_index": "SVNL00056319-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L3 RMS Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 0.32, "sensor_limit": 32, "sensor_limit_warn": 28, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103120", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.1", "sensor_index": "SVNL00056319-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L1 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 70, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.2", "sensor_index": "SVNL00056319-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L2 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.3", "sensor_index": "SVNL00056319-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L3 Apparent Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 70, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103130", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.1", "sensor_index": "SVNL00056319-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L1 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.2", "sensor_index": "SVNL00056319-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L2 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_consumed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.3", "sensor_index": "SVNL00056319-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L3 Lifetime kWh Total", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66, "sensor_limit": 16777215, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103140", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.1", "sensor_index": "SVNL00056319-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L1 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 0.4594, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.2", "sensor_index": "SVNL00056319-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L2 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power_factor", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.3", "sensor_index": "SVNL00056319-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L3 Power Factor", "group": null, "sensor_divisor": 10000, "sensor_multiplier": 1, "sensor_current": 0.4706, "sensor_limit": 1, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103150", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.1.5.0", "sensor_index": "sdbMgmtStsRingState.0", "sensor_type": "sdbMgmtStsRingState", "sensor_descr": "Databus Ring State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": "sdbMgmtStsRingState"}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.1", "sensor_index": "SVNL00056319-L1", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L1 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 229, "sensor_limit": 263.35, "sensor_limit_warn": null, "sensor_limit_low": 194.65, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1101110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.2", "sensor_index": "SVNL00056319-L2", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L2 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 228.6, "sensor_limit": 262.89, "sensor_limit_warn": null, "sensor_limit_low": 194.31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1102110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.31034.********.*******.210.3", "sensor_index": "SVNL00056319-L3", "sensor_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "SVNL00056319-L3 Voltage", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 228.78, "sensor_limit": 263.097, "sensor_limit_warn": null, "sensor_limit_low": 194.463, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1103110", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}], "state_indexes": [{"state_name": "sdbMgmtStsRingState", "state_descr": "open", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 2}, {"state_name": "sdbMgmtStsRingState", "state_descr": "closed", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}]}, "poller": "matches discovery"}}