{"applications": {"discovery": {"applications": [{"app_type": "powerdns", "app_state": "UNKNOWN", "discovered": 1, "app_state_prev": null, "app_status": "", "app_instance": ""}], "application_metrics": []}, "poller": {"applications": [{"app_type": "powerdns", "app_state": "OK", "discovered": 1, "app_state_prev": "UNKNOWN", "app_status": "", "app_instance": ""}], "application_metrics": [{"metric": "corruptPackets", "value": 263994, "value_prev": null, "app_type": "powerdns"}, {"metric": "def_cacheInserts", "value": 103620, "value_prev": null, "app_type": "powerdns"}, {"metric": "def_cacheLookup", "value": 46904, "value_prev": null, "app_type": "powerdns"}, {"metric": "latency", "value": 66, "value_prev": null, "app_type": "powerdns"}, {"metric": "pc_hit", "value": 26752965, "value_prev": null, "app_type": "powerdns"}, {"metric": "pc_miss", "value": 56238592, "value_prev": null, "app_type": "powerdns"}, {"metric": "pc_size", "value": 10010, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_tcpAnswers", "value": 342298, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_tcpQueries", "value": 342338, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_timedout", "value": 101, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udp4Answers", "value": 69164219, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udp4Queries", "value": 69141375, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udp6Answers", "value": 13642502, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udp6Queries", "value": 13619383, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udpAnswers", "value": 82806721, "value_prev": null, "app_type": "powerdns"}, {"metric": "q_udpQueries", "value": 82760758, "value_prev": null, "app_type": "powerdns"}, {"metric": "qc_hit", "value": 116321508, "value_prev": null, "app_type": "powerdns"}, {"metric": "qc_miss", "value": 132908205, "value_prev": null, "app_type": "powerdns"}, {"metric": "qsize", "value": 0, "value_prev": null, "app_type": "powerdns"}, {"metric": "rec_answers", "value": 0, "value_prev": null, "app_type": "powerdns"}, {"metric": "rec_questions", "value": 0, "value_prev": null, "app_type": "powerdns"}, {"metric": "servfailPackets", "value": 263461, "value_prev": null, "app_type": "powerdns"}]}}, "os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".1.3.6.1.4.1.8072.3.2.10", "sysDescr": "Linux server 3.10.0-693.5.2.el7.x86_64 #1 SMP Fri Oct 20 20:32:50 UTC 2017 x86_64", "sysContact": null, "version": null, "hardware": null, "features": null, "os": "linux", "type": "server", "serial": null, "icon": "linux.svg", "location": null}]}, "poller": {"devices": [{"sysName": "<private>", "sysObjectID": ".1.3.6.1.4.1.8072.3.2.10", "sysDescr": "Linux server 3.10.0-693.5.2.el7.x86_64 #1 SMP Fri Oct 20 20:32:50 UTC 2017 x86_64", "sysContact": "<private>", "version": "3.10.0-693.5.2.el7.x86_64", "hardware": "Generic x86 64-bit", "features": null, "os": "linux", "type": "server", "serial": null, "icon": "linux.svg", "location": "<private>"}]}}}