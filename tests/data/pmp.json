{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.**********.250.256", "sysDescr": "CANOPY 16.0.0.1 AP", "sysContact": null, "version": null, "hardware": null, "features": null, "os": "pmp", "type": "wireless", "serial": null, "icon": "cambium.svg", "location": null}]}, "poller": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.**********.250.256", "sysDescr": "CANOPY 16.0.0.1 AP", "sysContact": "<private>", "version": "CANOPY 16.0.0.1 AP", "hardware": "PMP 450 AP", "features": null, "os": "pmp", "type": "wireless", "serial": null, "icon": "cambium.svg", "location": "<private>"}]}}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Cambium 10/100 Ethernet", "ifName": "et1", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifConnectorPresent": null, "ifPromiscuousMode": null, "ifHighSpeed": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "None", "ifPhysAddress": null, "ifHardType": null, "ifLastChange": 0, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Canopy MultiPoint", "ifName": "rf1", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifConnectorPresent": null, "ifPromiscuousMode": null, "ifHighSpeed": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "None", "ifPhysAddress": null, "ifHardType": null, "ifLastChange": 0, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Cambium 10/100 Ethernet", "ifName": "et1", "portName": null, "ifIndex": 1, "ifSpeed": 100000000000000, "ifConnectorPresent": "0", "ifPromiscuousMode": "0", "ifHighSpeed": 100000000, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1510, "ifType": "ethernetCsmacd", "ifAlias": "None", "ifPhysAddress": "0a003ea0903b", "ifHardType": null, "ifLastChange": 0, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 3039089666, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1708983354, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 2, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": *************, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 248796010435, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 65620835, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 534384, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Canopy MultiPoint", "ifName": "rf1", "portName": null, "ifIndex": 2, "ifSpeed": 194969600000000, "ifConnectorPresent": "0", "ifPromiscuousMode": "0", "ifHighSpeed": 194969600, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "None", "ifPhysAddress": "1a003ea0903b", "ifHardType": null, "ifLastChange": 0, "ifVlan": "", "ifTrunk": null, "counter_in": null, "counter_out": null, "ignore": 0, "disabled": 0, "detailed": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1709207255, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 3038036927, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 248811614795, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": *************, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 652, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 13536, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 930254, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 36690498, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.**********.*******.0", "sensor_index": "0", "sensor_type": "WHISP-APS-MIB::whispGPSStats", "sensor_descr": "GPS Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": "WHISP-APS-MIB::whispGPSStats"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.**********.********.0", "sensor_index": "0", "sensor_type": "pmp", "sensor_descr": "Cambium Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 20, "sensor_limit": 40, "sensor_limit_warn": null, "sensor_limit_low": 10, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "state_name": null}], "state_indexes": [{"state_name": "WHISP-APS-MIB::whispGPSStats", "state_descr": "gpsSynchronized", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "WHISP-APS-MIB::whispGPSStats", "state_descr": "gpsLostSync", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "WHISP-APS-MIB::whispGPSStats", "state_descr": "generatingSync", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}]}, "poller": "matches discovery"}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "0", "sensor_type": "pmp", "sensor_descr": "Client Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 34, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.*******.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "0", "sensor_type": "pmp-downlink", "sensor_descr": "1m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 18, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "0", "sensor_type": "pmp-uplink", "sensor_descr": "1m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 16, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "1", "sensor_type": "pmp-downlink", "sensor_descr": "5m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 15, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "1", "sensor_type": "pmp-uplink", "sensor_descr": "5m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 15, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "2", "sensor_type": "pmp-downlink", "sensor_descr": "15m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 9, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "2", "sensor_type": "pmp-uplink", "sensor_descr": "15m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecCRCError", "sensor_descr": "CRC Errors", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.*********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecOutErrorsCount", "sensor_descr": "Out Error Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 2, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecInErrorsCount", "sensor_descr": "In Error Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "0", "sensor_type": "pmp", "sensor_descr": "Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5785, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "clients", "sensor_index": "0", "sensor_type": "pmp", "sensor_descr": "Client Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 34, "sensor_prev": 34, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.*******.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "0", "sensor_type": "pmp-downlink", "sensor_descr": "1m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 18, "sensor_prev": 18, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "0", "sensor_type": "pmp-uplink", "sensor_descr": "1m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 16, "sensor_prev": 16, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "1", "sensor_type": "pmp-downlink", "sensor_descr": "5m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 15, "sensor_prev": 15, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "1", "sensor_type": "pmp-uplink", "sensor_descr": "5m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 15, "sensor_prev": 15, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "2", "sensor_type": "pmp-downlink", "sensor_descr": "15m Downlink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 9, "sensor_prev": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.1.0\"]"}, {"sensor_deleted": 0, "sensor_class": "utilization", "sensor_index": "2", "sensor_type": "pmp-uplink", "sensor_descr": "15m Uplink Utilization", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 10, "sensor_prev": 10, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.2.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecCRCError", "sensor_descr": "CRC Errors", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.*********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecOutErrorsCount", "sensor_descr": "Out Error Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 2, "sensor_prev": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "errors", "sensor_index": "0", "sensor_type": "pmp-fecInErrorsCount", "sensor_descr": "In Error Count", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 0, "sensor_prev": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}, {"sensor_deleted": 0, "sensor_class": "frequency", "sensor_index": "0", "sensor_type": "pmp", "sensor_descr": "Frequency", "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 5785, "sensor_prev": 5785, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.**********.********.0\"]"}]}}}