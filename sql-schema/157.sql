ALTER TABLE `alert_schedule` <PERSON><PERSON><PERSON> `start` `start` <PERSON><PERSON><PERSON>IME NOT NULL DEFAULT '1970-01-02 00:00:01', CHANGE `end` `end` DATETIME NOT NULL DEFAULT '1970-01-02 00:00:01';
ALTER TABLE alert_schedule ADD COLUMN `recurring` tinyint (1) UNSIGNED NOT NULL DEFAULT 0 AFTER `schedule_id`, ADD COLUMN `start_recurring_dt` DATE NOT NULL DEFAULT '1970-01-01 00:00:01' AFTER `end`, ADD COLUMN `end_recurring_dt` DATE DEFAULT NULL AFTER `start_recurring_dt`, ADD COLUMN `start_recurring_hr` TIME NOT NULL DEFAULT '00:00:00' AFTER `end_recurring_dt`, ADD COLUMN `end_recurring_hr` TIME NOT NULL DEFAULT '00:00:00' AFTER `start_recurring_hr`, ADD COLUMN `recurring_day` VARCHAR(15) AFTER `end_recurring_hr`;
