alter table access_points modify accesspoint_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table alert_device_map modify id int unsigned auto_increment, modify device_id int unsigned not null, modify rule_id int unsigned not null;
alter table alert_group_map modify id int unsigned auto_increment, modify rule_id int unsigned not null, modify group_id int unsigned not null;
alter table alert_log modify id int unsigned auto_increment, modify rule_id int unsigned not null, modify device_id int unsigned not null;
alter table alert_rules modify id int unsigned auto_increment;
alter table alert_schedulables modify item_id int unsigned auto_increment, modify schedule_id int unsigned not null, modify alert_schedulable_id int unsigned not null;
alter table alert_schedule modify schedule_id int unsigned auto_increment;
alter table alert_template_map modify id int unsigned auto_increment, modify alert_templates_id int unsigned not null, modify alert_rule_id int unsigned not null;
alter table alert_templates modify id int unsigned auto_increment;
alter table alert_transport_groups modify transport_group_id int unsigned auto_increment;
alter table alert_transport_map modify id int unsigned auto_increment, modify rule_id int unsigned not null, modify transport_or_group_id int unsigned not null;
alter table alert_transports modify transport_id int unsigned auto_increment;
alter table alerts modify id int unsigned auto_increment, modify device_id int unsigned not null, modify rule_id int unsigned not null;
alter table api_tokens modify id int unsigned auto_increment, modify user_id int unsigned not null;
alter table application_metrics modify app_id int unsigned not null;
alter table applications modify app_id int  unsigned auto_increment, modify device_id int unsigned not null;
alter table authlog modify id int unsigned auto_increment;
alter table bgpPeers modify bgpPeer_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table bgpPeers_cbgp modify device_id int unsigned not null;
alter table bill_data modify bill_id int unsigned not null;
alter table bill_history modify bill_hist_id int unsigned auto_increment, modify bill_id int unsigned not null;
alter table bill_perms modify user_id int unsigned not null, modify bill_id int unsigned not null;
alter table bill_port_counters modify port_id int unsigned not null, modify bill_id int unsigned not null;
alter table bill_ports modify bill_id int unsigned not null, modify port_id int unsigned not null;
alter table bills modify bill_id int unsigned auto_increment;
alter table callback modify callback_id int unsigned auto_increment;
alter table cef_switching modify cef_switching_id int unsigned auto_increment, modify device_id int unsigned not null, modify updated int unsigned not null, modify updated_prev int unsigned not null;
alter table ciscoASA modify ciscoASA_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table config modify config_id int unsigned auto_increment;
alter table customers modify customer_id int unsigned auto_increment;
alter table dashboards modify dashboard_id int unsigned auto_increment, modify user_id int unsigned default 0 not null;
alter table device_graphs modify device_id int unsigned not null;
alter table device_mibs modify device_id int unsigned not null;
alter table device_oids modify device_id int unsigned not null;
alter table device_perf modify device_id int unsigned not null;
alter table devices modify location_id int unsigned null, modify agent_uptime int unsigned default 0 not null;
alter table devices_attribs modify attrib_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table devices_perms modify user_id int unsigned not null, modify device_id int unsigned not null;
alter table entityState modify entity_state_id int unsigned auto_increment, modify device_id int unsigned null, modify entPhysical_id int unsigned null;
alter table entPhysical modify entPhysical_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table entPhysical_state modify device_id int unsigned not null;
alter table eventlog modify event_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table hrDevice modify hrDevice_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table ipsec_tunnels modify tunnel_id int unsigned auto_increment, modify device_id int unsigned not null, modify peer_port int unsigned not null, modify local_port int unsigned not null;
alter table ipv4_addresses modify ipv4_address_id int unsigned auto_increment, modify port_id int unsigned not null;
alter table ipv4_mac modify port_id int unsigned not null, modify device_id int unsigned null;
alter table ipv4_networks modify ipv4_network_id int unsigned auto_increment;
alter table ipv6_addresses modify ipv6_address_id int unsigned auto_increment, modify port_id int unsigned not null;
alter table ipv6_networks modify ipv6_network_id int unsigned auto_increment;
alter table juniAtmVp modify juniAtmVp_id int unsigned not null, modify port_id int unsigned not null, modify vp_id int unsigned not null;
alter table links modify id int unsigned auto_increment, modify local_port_id int unsigned null, modify local_device_id int unsigned not null, modify remote_port_id int unsigned null, modify remote_device_id int unsigned not null;
alter table loadbalancer_rservers modify rserver_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table loadbalancer_vservers modify classmap_id int unsigned not null, modify device_id int unsigned not null;
alter table locations modify id int unsigned auto_increment;
alter table mac_accounting modify ma_id int unsigned auto_increment, modify port_id int unsigned not null, modify poll_time int unsigned null, modify poll_prev int unsigned null, modify poll_period int unsigned null;
alter table mefinfo modify id int unsigned auto_increment, modify device_id int unsigned not null;
alter table mempools modify mempool_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table munin_plugins modify mplug_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table munin_plugins_ds modify mplug_id int unsigned not null;
alter table netscaler_vservers modify vsvr_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table notifications modify notifications_id int unsigned auto_increment;
alter table notifications_attribs modify attrib_id int unsigned auto_increment, modify notifications_id int unsigned not null, modify user_id int unsigned not null;
alter table ospf_areas modify id int unsigned auto_increment, modify device_id int unsigned not null;
alter table ospf_instances modify id int unsigned auto_increment, modify device_id int unsigned not null, modify ospf_instance_id int unsigned not null;
alter table ospf_nbrs modify id int unsigned auto_increment, modify device_id int unsigned not null, modify port_id int unsigned null;
alter table ospf_ports modify id int unsigned auto_increment, modify device_id int unsigned not null, modify port_id int unsigned not null;
alter table packages modify pkg_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table perf_times modify id int unsigned auto_increment, modify devices int unsigned not null, modify start int unsigned not null;
alter table plugins modify plugin_id int unsigned auto_increment;
alter table poller_cluster modify id int unsigned auto_increment;
alter table poller_cluster_stats modify id int unsigned auto_increment, modify parent_poller int unsigned default 0 not null;
alter table poller_groups modify id int unsigned auto_increment;
alter table pollers modify id int unsigned auto_increment, modify devices int unsigned not null;
alter table ports modify port_id int unsigned auto_increment, modify device_id int unsigned default 0 not null, modify poll_time int unsigned null, modify poll_prev int unsigned null, modify poll_period int unsigned null;
alter table ports_adsl modify port_id int unsigned not null;
alter table ports_nac modify port_id int unsigned not null;
alter table ports_perms modify user_id int unsigned not null, modify port_id int unsigned not null;
alter table ports_stack modify device_id int unsigned not null, modify port_id_high int unsigned not null, modify port_id_low int unsigned not null;
alter table ports_statistics modify port_id int unsigned not null;
alter table ports_stp modify port_stp_id int unsigned auto_increment, modify device_id int unsigned not null, modify port_id int unsigned not null;
alter table ports_vlans modify port_vlan_id int unsigned auto_increment, modify device_id int unsigned not null, modify port_id int unsigned not null;
alter table processes modify device_id int unsigned not null;
alter table processors modify processor_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table proxmox modify id int unsigned auto_increment;
alter table proxmox modify device_id int unsigned default 0 not null;
alter table proxmox_ports modify id int unsigned auto_increment;
alter table pseudowires modify pseudowire_id int unsigned auto_increment, modify device_id int unsigned not null, modify port_id int unsigned not null, modify peer_device_id int unsigned not null;
alter table route modify device_id int unsigned not null, modify discoveredAt int unsigned not null;
alter table sensors_to_state_indexes modify sensors_to_state_translations_id int unsigned auto_increment;
alter table services modify service_id int unsigned auto_increment, modify device_id int unsigned not null, modify service_changed int unsigned default 0 not null;
alter table session modify session_id int unsigned auto_increment;
alter table slas modify sla_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table state_translations modify state_translation_id int unsigned auto_increment, modify state_index_id int unsigned not null;
alter table storage modify storage_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table stp modify stp_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table syslog modify device_id int unsigned null;
alter table tnmsneinfo modify id int unsigned auto_increment, modify device_id int unsigned not null;
alter table toner modify toner_id int unsigned auto_increment, modify device_id int unsigned default 0 not null;
alter table transport_group_transport modify transport_group_id int unsigned not null, modify transport_id int unsigned not null;
alter table ucd_diskio modify diskio_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table users modify user_id int unsigned auto_increment;
alter table users_prefs modify user_id int unsigned not null;
alter table users_widgets modify user_widget_id int unsigned auto_increment, modify user_id int unsigned not null, modify widget_id int unsigned not null, modify dashboard_id int unsigned not null;
alter table vlans modify vlan_id int unsigned auto_increment, modify device_id int unsigned null;
alter table vminfo modify id int unsigned auto_increment, modify device_id int unsigned not null;
alter table vrf_lite_cisco modify vrf_lite_cisco_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table vrfs modify vrf_id int unsigned auto_increment, modify device_id int unsigned not null;
alter table widgets modify widget_id int unsigned auto_increment;
alter table wireless_sensors modify sensor_id int unsigned auto_increment, modify access_point_id int unsigned null;

LOCK TABLES sensors WRITE, sensors_to_state_indexes WRITE, state_indexes WRITE;

SELECT COUNT(*) INTO @ibfk_1_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_ibfk_1' AND `constraint_type` = 'FOREIGN KEY'; SET @statement1 := IF(@ibfk_1_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_ibfk_1', 'SELECT "info: foreign key sensors_to_state_indexes_ibfk_1 does not exist."'); PREPARE statement1 FROM @statement1; EXECUTE statement1;
SELECT COUNT(*) INTO @ibfk_2_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_ibfk_2' AND `constraint_type` = 'FOREIGN KEY'; SET @statement2 := IF(@ibfk_2_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_ibfk_2', 'SELECT "info: foreign key sensors_to_state_indexes_ibfk_2 does not exist."'); PREPARE statement2 FROM @statement2; EXECUTE statement2;
SELECT COUNT(*) INTO @ibfk_3_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_ibfk_3' AND `constraint_type` = 'FOREIGN KEY'; SET @statement3 := IF(@ibfk_3_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_ibfk_3', 'SELECT "info: foreign key sensors_to_state_indexes_ibfk_3 does not exist."'); PREPARE statement3 FROM @statement3; EXECUTE statement3;
SELECT COUNT(*) INTO @ibfk_4_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_ibfk_4' AND `constraint_type` = 'FOREIGN KEY'; SET @statement4 := IF(@ibfk_4_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_ibfk_4', 'SELECT "info: foreign key sensors_to_state_indexes_ibfk_4 does not exist."'); PREPARE statement4 FROM @statement4; EXECUTE statement4;
SELECT COUNT(*) INTO @ibfk_5_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_ibfk_5' AND `constraint_type` = 'FOREIGN KEY'; SET @statement5 := IF(@ibfk_5_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_ibfk_5', 'SELECT "info: foreign key sensors_to_state_indexes_ibfk_5 does not exist."'); PREPARE statement5 FROM @statement5; EXECUTE statement5;
SELECT COUNT(*) INTO @sensor_id_foreign_EXISTS FROM `information_schema`.`table_constraints` WHERE `table_name` = 'sensors_to_state_indexes' AND `constraint_name` = 'sensors_to_state_indexes_sensor_id_foreign' AND `constraint_type` = 'FOREIGN KEY'; SET @statement0 := IF(@sensor_id_foreign_EXISTS > 0, 'ALTER TABLE sensors_to_state_indexes DROP FOREIGN KEY sensors_to_state_indexes_sensor_id_foreign', 'SELECT "info: foreign key sensors_to_state_indexes_sensor_id_foreign does not exist."'); PREPARE statement0 FROM @statement0; EXECUTE statement0;

alter table sensors modify sensor_id int unsigned auto_increment;
alter table sensors_to_state_indexes modify sensor_id int unsigned not null, modify state_index_id int unsigned not null;
alter table state_indexes modify state_index_id int unsigned auto_increment;
ALTER TABLE `sensors_to_state_indexes` ADD CONSTRAINT `sensors_to_state_indexes_ibfk_1` FOREIGN KEY (`state_index_id`) REFERENCES `state_indexes`(`state_index_id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `sensors_to_state_indexes` ADD CONSTRAINT `sensors_to_state_indexes_sensor_id_foreign` FOREIGN KEY (`sensor_id`) REFERENCES `sensors` (`sensor_id`) ON DELETE CASCADE;
UNLOCK TABLES;
