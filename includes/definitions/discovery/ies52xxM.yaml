mib: IES5206-MIB
modules:
    processors:
            data:
                -
                    oid: cpuStatsTable
                    value: cpuStatsCurrent
                    num_oid: '.*******.4.1.890.*********.*******.{{ $index }}'
                    descr: 'Processor {{ $index }}'
                    warn_percent: cpuStatsHighThreshold
                    skip_values:
                    -
                      oid: cpuStatsIndex
                      op: '='
                      value: 0
    sensors:
        pre-cache:
            data:
                - oid:
                    - fanConfLowThreshold
                    - fanConfHighThreshold
                    - temperatureConfLowThreshold
                    - temperatureConfHighThreshold
                    - voltageConfLowThreshold
                    - voltageConfHighThreshold
        state:
            data:
                -
                    oid: slotModuleTable
                    value: slotModuleStatus
                    num_oid: '.*******.4.1.890.*********.*******.{{ $index }}'
                    descr: 'Slot {{ $index }} {{ $slotModuleDescr }}'
                    state_name: moduleStatus
                    skip_values: 0
                    states:
                        - { descr: empty, graph: 0, value: 0, generic: 3 }
                        - { descr: inactive, graph: 0, value: 1, generic: 2 }
                        - { descr: init, graph: 0, value: 2, generic: 1 }
                        - { descr: active, graph: 0, value: 3, generic: 0 }
                        - { descr: standby, graph: 0, value: 4, generic: 3 }
                        - { descr: disable, graph: 0, value: 5, generic: 2 }
                -
                    oid: slotModuleTable
                    value: slotModuleAlarmStatus
                    num_oid: '.*******.4.1.890.*********.*******.{{ $index }}'
                    descr: 'Slot {{ $index }} {{ $slotModuleDescr }}'
                    state_name: moduleAlarm
                    group: Alarm
                    states:
                        - { descr: hasAlarm, graph: 0, value: 1, generic: 2 }
                        - { descr: noAlarm, graph: 0, value: 2, generic: 0 }
        dbm:
            options:
                skip_values: 0
            data:
                -
                    oid: geDdmiTable
                    value: geDdmiTxPowerdBm
                    num_oid: '.*******.4.1.890.*********.*********.{{ $index }}'
                    descr: '{{ $geDdmiVendorName }} {{ $geDdmiVendorPn }} Tx'
                    entPhysicalIndex: '{{ $index }}'
                    divisor: 10000
                    index: 'tx-dbm.{{ $index }}'
                    low_limit: geDdmiTxPowerLowAlarmdBm
                    high_limit: geDdmiTxPowerHighAlarmdBm
                -
                    oid: geDdmiTable
                    value: geDdmiRxPowerdBm
                    num_oid: '.*******.4.1.890.*********.********.{{ $index }}'
                    descr: '{{ $geDdmiVendorName }} {{ $geDdmiVendorPn }} Rx'
                    index: 'rx-dbm.{{ $index }}'
                    divisor: 10000
                    entPhysicalIndex: '{{ $index }}'
                    low_limit: geDdmiRxPowerLowAlarmdBm
                    high_limit: geDdmiRxPowerHighAlarmdBm
        fanspeed:
            data:
                -
                    oid: fanStatsTable
                    value: fanRpmCurValue
                    num_oid: '.*******.4.1.890.*********.5.4.1.1.{{ $index }}'
                    descr: fanRpmDescr
                    low_limit: fanConfLowThreshold
                    high_limit: fanConfHighThreshold
        temperature:
            data:
                -
                    oid: temperatureStatsTable
                    value: temperatureCurValue
                    num_oid: '.*******.4.1.890.*********.5.5.1.1.{{ $index }}'
                    descr: 'Slot {{ $index }} {{ $temperatureDescr }}'
                    low_limit: temperatureConfLowThreshold
                    high_limit: temperatureConfHighThreshold
        voltage:
            data:
                -
                    oid: voltageStatsTable
                    value: voltageCurValue
                    num_oid: '.*******.4.1.890.*********.5.6.1.1.{{ $index }}'
                    descr: 'Slot {{ $index }} {{ $voltageDescr }}'
                    low_limit: voltageConfLowThreshold
                    high_limit: voltageConfHighThreshold
                    divisor: 1000
