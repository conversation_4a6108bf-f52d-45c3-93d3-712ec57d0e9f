mib:  MWR-ETHERNET-MIB:MWR-RADIO-MC-MIB
modules:
    sensors:
        temperature:
            options:
                skip_values_lt: 0
            data:
                -
                    oid: mwrSystemTemperature
                    num_oid: '.*******.4.1.7262.*******.3.{{ $index }}'
                    descr: 'Current System Temperature'
                    index: 'mwrSystemTemperature.{{ $index }}'
        state:
            data:
                -
                    oid: mwrEmcRadioStatusTable
                    value: mwrEmcRadioLinkStatus
                    num_oid: '.*******.4.1.7262.**********.1.1.11.{{ $index }}'
                    descr: 'Wireless Link {{ $index }}'
                    index: 'mwrEmcRadioLinkStatus.{{ $index }}'
                    snmp_flags: '-OQUsbe'
                    state_name: mwrEmcRadioLinkStatus
                    states:
                        - { value: -1, generic: 3, graph: 0, descr: 'null' }
                        - { value:  1, generic: 0, graph: 1, descr: up }
                        - { value:  2, generic: 2, graph: 1, descr: down }
