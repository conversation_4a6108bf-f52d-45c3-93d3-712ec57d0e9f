mib: TELESTE-COMMON-MIB
modules:
    sensors:
        pre-cache:
            data:
                - oid:
                    - moduleSlotNo
                    - moduleHwType
        temperature:
            data:
                -
                    oid: statusInternalTemperature
                    num_oid: '.*******.4.1.3715.********.1.3.{{ $index }}'
                    descr: "Slot {{ $moduleSlotNo }} ({{ $moduleHwType }}) "
                    index: '{{ $index }}'
                    divisor: 10
                    skip_value_lt: -50
        state:
            data:
                -
                    oid: statusGeneral
                    num_oid: '.*******.4.1.3715.********.{{ $index }}'
                    descr: General Status
                    state_name: statusGeneral
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Notification, graph: 1, value: 2, generic: 1}
                        - { descr: Warning, graph: 1, value: 3, generic: 1}
                        - { descr: Alarm, graph: 1, value: 4, generic: 3}
                -
                    oid: statusTemperature
                    num_oid: '.*******.4.1.3715.********.{{ $index }}'
                    descr: Temperature Status
                    state_name: statusTemperature
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Hihi, graph: 1, value: 2, generic: 3}
                        - { descr: Hi, graph: 1, value: 3, generic: 3}
                        - { descr: Lo, graph: 1, value: 4, generic: 2}
                        - { descr: Lolo, graph: 1, value: 5, generic: 2}
                -
                    oid: statusFan
                    num_oid: '.*******.4.1.3715.99.1.2.6.{{ $index }}'
                    descr: Temperature Status
                    state_name: statusTemperature
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Failure, graph: 1, value: 2, generic: 3}
                -
                    oid: statusHardware
                    num_oid: '.*******.4.1.3715.99.1.2.7.{{ $index }}'
                    descr: Hardware Status
                    state_name: statusHardware
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Failure, graph: 1, value: 2, generic: 3}
                -
                    oid: statusSoftware
                    num_oid: '.*******.4.1.3715.99.1.2.8.{{ $index }}'
                    descr: Software Status
                    state_name: statusSoftware
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Failure, graph: 1, value: 2, generic: 3 }
                        - { descr: Missing, graph: 1, value: 3, generic: 2 }
                        - { descr: Initialising, graph: 1, value: 4, generic: 2 }
