<?php

use LibreNMS\Util\ObjectCache;
use App\Models\MeshDynamics;

if (ObjectCache::portCounts(['total'], $device['device_id'])['total'] > 0) {
    echo '<div class="row">
          <div class="col-md-12">
            <div class="panel panel-default panel-condensed">
              <div class="panel-heading">
              <i class="fa fa-road fa-lg icon-theme" aria-hidden="true"></i><strong> Overall Traffic</strong>
            </div>
            <table class="table table-hover table-condensed table-striped">';

    if ($screen_width = Session::get('screen_width')) {
        if ($screen_width > 970) {
            $graph_array['width'] = round(($screen_width - 390 )/2, 0);
            $graph_array['height'] = round($graph_array['width'] /3);
            $graph_array['lazy_w'] = $graph_array['width'] + 80;
        } else {
            $graph_array['width'] = $screen_width - 190;
            $graph_array['height'] = round($graph_array['width'] /3);
            $graph_array['lazy_w'] = $graph_array['width'] + 80;
        }
    }

    $graph_array['to'] = \LibreNMS\Config::get('time.now');
    $graph_array['device'] = $device['device_id'];
    $graph_array['type']   = 'device_bits';
    $graph_array['from'] = \LibreNMS\Config::get('time.day');
    $graph_array['legend'] = 'no';
    $graph = generate_lazy_graph_tag($graph_array);

    #Generate tooltip
    $graph_array['width'] = 210;
    $graph_array['height'] = 100;
    $link_array         = $graph_array;
    $link_array['page'] = 'graphs';
    unset($link_array['height'], $link_array['width']);
    $link = generate_url($link_array);

    $graph_array['width'] = '210';
    $overlib_content      = generate_overlib_content($graph_array, $device['hostname'].' - Device Traffic');

    echo '<tr>
          <td colspan="4">';
    echo overlib_link($link, $graph, $overlib_content, null);
    echo '  </td>
        </tr>';

    $ports = ObjectCache::portCounts(['total', 'up', 'down', 'disabled'], $device['device_id']);
    echo '
    <tr>
      <td><i class="fa fa-link fa-lg" style="color:black" aria-hidden="true"></i> '.$ports['total'].'</td>
      <td><i class="fa fa-link fa-lg interface-upup" aria-hidden="true"></i> '.$ports['up'].'</td>
      <td><i class="fa fa-link fa-lg interface-updown" aria-hidden="true"></i> '.$ports['down'].'</td>
      <td><i class="fa fa-link fa-lg interface-admindown" aria-hidden="true"></i> '.$ports['disabled'].'</td>
    </tr>';

    echo '<tr>
          <td colspan="4">';

    $ifsep = '';

    foreach (dbFetchRows("SELECT * FROM `ports` WHERE device_id = ? AND `deleted` != '1' AND `disabled` = 0", array($device['device_id'])) as $data) {
        $data = cleanPort($data);
        $data = array_merge($data, $device);
        echo "$ifsep".generate_port_link($data, makeshortif(strtolower($data['label'])));
        $ifsep = ', ';
    }

    unset($ifsep);
    echo '  </td>';
    echo '</tr>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}//end if
?>
<?php
$device_nodes = MeshDynamics::getDeviceNodes($device['device_id']);
// print_r($device_nodes);die('');
?>

    <div class="panel panel-default panel-condensed pos-relative" style="min-height: 90px;">
        <div class="section-loading" style="display:none"><img src="/images/custom/loader.svg" alt="loader"/></div>
        <div class="section-loading-progress" style="display:none">
            <div class="ldBar label-center" id="mesh_progress_bar" data-value="0" data-counter="0" data-preset="circle"></div>
            <div style="margin-top: 10px;"> Processing please wait <img style="display: inline-block; margin-left: 5px;" src="/images/custom/loading-circles-small2.gif" alt="loader"/></div>
        </div>
        <div class="panel-heading">
            <strong> Mesh Dynamics</strong>
            <a title="Clear History" class="btn btn-primary btn-sm pull-right" aria-label="Clear History" href="javascript:void(0)" data-toggle="modal" data-target="#confirm-clear-history" >Clear History</a>
            <a title="Refresh Mesh" class="btn btn-primary btn-sm pull-right" style="margin-right: 5px;"  aria-label="Refresh Mesh" href="javascript:void(0)" data-toggle="modal" data-target="#confirm-mesh-update-data" >Refresh Mesh</a>
            <a title="Refresh Device" class="btn btn-primary btn-sm pull-right" style="margin-right: 5px;"  aria-label="Refresh Device" href="javascript:void(0)" data-toggle="modal" data-target="#confirm-discover-device" >Refresh Device</a>
        </div>

        <div class="mesh-dynamics-wrap">

            <div class="row">
                <div class="col-lg-12">

                    <input type="hidden" id="mesh1_order_type" value="signal" />
                    <input type="hidden" id="mesh1_order_value" value="DESC" />
                    <input type="hidden" id="mesh2_order_type" value="signal" />
                    <input type="hidden" id="mesh2_order_value" value="DESC" />

                    <div class="mesh-stats-table" id="mesh1-details">
                    </div>

                    <div class="mesh-stats-table" id="mesh2-details">
                    </div>

                </div>
            </div>

            <?php if($device_nodes['success'] && sizeof($device_nodes['nodes']) > 0){ ?>
                  <?php
                       if(sizeof($device_nodes['nodes']) > 1) {
                           $devices_list_short = array();
                           $devices_list_long = array();
                           $d_counter = 0;
                           foreach ($device_nodes['nodes'] as $d_node) {

                               if($d_node['device_id'] != $device['device_id']) {

                                   $d_counter++;
                                   $devices_list_long[] = '<a href="/device/device=' . $d_node['device_id'] . '/" >' . $d_node['sysname'] . '</a>';

                                   if ($d_counter < 8) {
                                       $devices_list_short[] = '<a href="/device/device=' . $d_node['device_id'] . '/" >' . $d_node['sysname'] . '</a>';
                                   }

                                   if ($d_node['is_gateway']) {
                                       $gateway_node = $d_node['sysname'];

                                   }
                               }
                           }

                           $hops = sizeof($device_nodes['nodes']) - 2;
                           if ($hops < 0) {
                               $hops = 0;
                           }
                       }
                  ?>
                <?php if(isset($gateway_node)){ ?>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mesh-stats-table">
                            <table class="table table-condensed">
                                <thead>
                                <tr>
                                    <th> <span class="mesh-stats-title"><span class="status-circle dark"></span> Gateway</span></th>
                                    <th> Hops</th>

                                </tr>
                                </thead>
                                <tbody>

                                <tr>
                                    <td valign="middle">
                                        <span class="mesh-stats-mesh-id lighter"><?php echo $gateway_node; ?></span>
                                    </td>

                                    <td class="hops-td" align="left" valign="middle">
                                        <span class="clr-lighter"><?php echo $hops; ?> Hops</span> <br>

                                        <?php if($d_counter > 7){ ?>

                                        <span class="hops-value" id="gateway_short_list">
                                            <?php echo implode(" ",$devices_list_short); ?>   <a href="javascript:void(0)" onclick="showgatewaylist('long');" class="chide">Show + </a>
                                        </span>
                                        <span class="hops-value" id="gateway_long_list" style="display: none;">
                                            <?php echo implode(" ",$devices_list_long); ?>   <a href="javascript:void(0)" onclick="showgatewaylist('short');" class="chide">Hide - </a>
                                        </span>
                                        <?php } else { ?>
                                            <span class="hops-value">
                                            <?php echo implode(" ",$devices_list_long); ?>
                                            </span>
                                        <?php } ?>

                                    </td>

                                </tr>

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
                <?php } ?>
            <?php } ?>

            <div class="modal fade" id="confirm-mesh-update-data" tabindex="-1" role="dialog" aria-labelledby="Update Data" aria-hidden="true">
                <div class="modal-dialog modal-sm" style="min-width: 320px;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                            <h5 class="modal-title" id="Delete">Update Mesh Dynamics Data</h5>
                        </div>
                        <div class="modal-body">
                            <p id="p-update-text-modal">This proccess could take up to a few minutes.<br /> If you want to continue, click Proceed. </p>
                        </div>
                        <div class="modal-footer">
                            <div class="remove_token_form">

                                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" data-target="alert-rule-removal" data-dismiss="modal" id="mesh_dynamics_update_data">Proceed</button>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="confirm-clear-history" tabindex="-1" role="dialog" aria-labelledby="Update Data" aria-hidden="true">
                <div class="modal-dialog modal-sm" style="min-width: 320px;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                            <h5 class="modal-title" id="Delete">Clear History of Mesh Dynamics Data</h5>
                        </div>
                        <div class="modal-body">
                            <p id="p-update-text-modal">Are you sure you want to clear history? </p>
                        </div>
                        <div class="modal-footer">
                            <div class="remove_token_form">

                                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" data-target="alert-rule-removal" data-dismiss="modal" id="mesh_dynamics_clear_history">Proceed</button>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

<script src="/js/arra/loading-bar.min.js"></script>

<script>

    var progress_counter = 0;

    function showgatewaylist(list_type){

        if(list_type == 'short'){
            $("#gateway_long_list").hide();
            $("#gateway_short_list").show();
        } else {
            $("#gateway_long_list").show();
            $("#gateway_short_list").hide();
        }

    }

    function changeArraMeshOrder(type,order_type){

        var order_type_init = $("#" + type + "_order_type").val();

        if(order_type_init == order_type){

            var order_value = $("#" + type + "_order_value").val();
            if(order_value == 'DESC'){
                $("#" + type + "_order_value").val('ASC');
            } else {
                $("#" + type + "_order_value").val('DESC');
            }
        } else {
            $("#" + type + "_order_type").val(order_type);
            $("#" + type + "_order_value").val('DESC');
        }

        getArraMeshData(type,1,1);

    }

    function getArraMeshData(type,show_loading,hide_loading){

        if(show_loading){
            $(".section-loading").show();
        }

        var order_type = $("#" + type + "_order_type").val();
        var order_value = $("#" + type + "_order_value").val();

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: "<?php echo url('mesh-dynamics/get-data'); ?>",
            type: 'POST',
            data: { type: type, order_type: order_type, order_value: order_value, device_id: <?php echo $device['device_id']; ?>  },
            success: function(response)
            {

                if(response != 0){
                    $("#" + type + "-details").empty().html(response);
                }

                if(hide_loading){
                    $(".section-loading").hide();
                }
                // console.log('response',response);
            }
        });

    }

    function meshClearHistory(){
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: "<?php echo url('mesh-dynamics/clear-history'); ?>",
            type: 'POST',
            data: { device_id: '<?php echo $device['device_id']; ?>' },
            success: function(response)
            {

                window.location.reload();
            }
        });
    }

    function meshupdatedata(){


        $("#mesh_progress_bar").attr("data-value",0);
        $("#mesh_progress_bar").attr("data-counter",0);

        var bar1 = new ldBar("#mesh_progress_bar");
        bar1.set(0);

        $( "#countdown_timer_status" ).trigger( "click" );
        $(".section-loading-progress").show();

        var mesh_device_list = [];
        var counter = 0;

        $( ".mesh_device_host" ).each(function() {

            var mesh_device_hostname = $( this ).val();

            if(mesh_device_hostname && jQuery.inArray( mesh_device_hostname, mesh_device_list ) < 0){
                mesh_device_list[counter] = mesh_device_hostname;
                counter++;
            }

        });


        // console.log("mesh_device_list",mesh_device_list);
        if(mesh_device_list.length > 0){
            $("#mesh_progress_bar").attr("data-counter",mesh_device_list.length);

            var percentege_val = Math.round(100 / mesh_device_list.length);

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "<?php echo url('mesh-dynamics/run-discorvery'); ?>",
                type: 'POST',
                data: { dhlist: mesh_device_list, did: '<?php echo $device['device_id']; ?>' },
                success: function(response)
                {

                    if(response.device_id && response.device_identifier){

                        var device_id = response.device_id;
                        var device_identifier = response.device_identifier;
                        // console.log('device_id',device_id);
                        // console.log('device_identifier',device_identifier);

                        setTimeout(function(){
                            verifydevicedicoverrunscript(device_id,device_identifier,percentege_val);
                        }, 5000);

                    }
                    // console.log('response',response);
                }
            });
        }

    }

    function verifydevicedicoverrunscript(device_id,device_identifier,percentege_val){

        // console.log('device_id',device_id);
        // console.log('device_identifier',device_identifier);
        // console.log('percentege_val',percentege_val);


        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: "<?php echo url('mesh-dynamics/verify-device-discorvery'); ?>",
            type: 'POST',
            data: { device_id: device_id, device_identifier: device_identifier },
            success: function(response)
            {
                // console.log('verify_response: ',response);

                $("#mesh_progress_bar").attr("data-counter",response);
                setmeshprogressbarr(percentege_val);

                if(response != 0){

                    setTimeout(function(){
                        verifydevicedicoverrunscript(device_id,device_identifier,percentege_val);
                    }, 5000);

                }
            }
        });

    }

    function setmeshprogressbarr(progress_counter){

        if(current_counter == 0){
            var progress_percent = 100;
        } else {
            var current_counter = parseInt($("#mesh_progress_bar").attr("data-counter"));
            var progress_percent = 100 - (current_counter * progress_counter);
        }


        $("#mesh_progress_bar").attr("data-value",progress_percent);

        var bar1 = new ldBar("#mesh_progress_bar");
        bar1.set(progress_percent);

        if(current_counter == 0){
            setTimeout(function(){
                location.reload();
            }, 2000);

        }

    }

    $( document ).ready(function() {

        $( "#mesh_dynamics_update_data" ).click(function() {

            meshupdatedata();

        });

        $( "#mesh_dynamics_clear_history" ).click(function() {

            meshClearHistory();

        });


        $("#mesh1_order_type").val('signal');
        $("#mesh1_order_value").val('DESC');
        $("#mesh2_order_type").val('signal');
        $("#mesh2_order_value").val('DESC');
        getArraMeshData('mesh1',1,0);
        getArraMeshData('mesh2',0,1);
    });


</script>
<script type="text/javascript">

    // document.getElementById('mesh_dynamics_update_data').onclick = function () {
    //     xhr = new XMLHttpRequest();
    //     xhr.open("GET", "ajax_output.php?id=capture&format=text&type=discovery&hostname=fde3:8675:3091:0000:0000:f0aa:0b00:2180", true);
    //     xhr.onprogress = function (e) {
    //         output.innerHTML = e.currentTarget.responseText;
    //         output.scrollTop = output.scrollHeight - output.clientHeight; // scrolls the output area
    //     };
    //     xhr.onreadystatechange = function () {
    //         if (xhr.readyState == 4) {
    //             console.log("Complete");
    //         }
    //     };
    //     xhr.send();
    // };

</script>
<input type="hidden" class="mesh_device_host" value="<?php echo $device['hostname'] ?>" />
<?php if($device_nodes['success'] && sizeof($device_nodes['nodes']) > 0){ ?>
            <div class="row">
                <div class="col-lg-12">

                    <div class="mesh-increments clearfix">
                        <?php $counter = 0; $rx_type = 1;?>
                        <?php foreach ($device_nodes['nodes'] as $arra_node){ ?>


                            <?php if( (!$arra_node['is_gateway'] && isset($arra_node['sysname']) && $arra_node['sysname'] && ($arra_node['right']['snr'] > 0 || $arra_node['right']['snr'] <= 0 || $arra_node['left']['snr'] <= 0 || $arra_node['left']['snr'] > 0)  ) || ($arra_node['is_gateway'] && isset($arra_node['sysname']) && $arra_node['sysname']) ){ ?>

                                <?php echo '<input type="hidden" class="mesh_device_host" value="' . $arra_node['hostname'] . '" />'; ?>
                                <?php $counter++; ?>
                                <div class="mi-box-wrap">


                                    <?php if($counter == 1){ ?>
                                        <div class="mi-box-no">Start</div>
                                    <?php }elseif($counter == 2){ ?>
                                        <div class="mi-box-no">1st</div>
                                    <?php }elseif($counter == 3){ ?>
                                        <div class="mi-box-no">2nd</div>
                                    <?php }elseif($counter == 4 ){ ?>
                                        <div class="mi-box-no">3rd</div>
                                    <?php } else {  ?>
                                        <div class="mi-box-no"><?php echo $counter - 1; ?>th</div>
                                    <?php }  ?>
                                    <div class="mi-box-container clearfix">

                                        <?php if(isset($arra_node['left']) && isset($arra_node['left']['snr']) && ($arra_node['left']['snr'] <= 0 || $arra_node['left']['snr'] > 0)){ ?>

                                            <div class="mi-box-left">

                                                <div><span class="clr-lighter"><?php echo $arra_node['left']['snr']; ?></span> <span class="mi-box-small">SNR</span></div>
                                                <div><span class="clr-lighter"><?php echo $arra_node['left']['mesh']['current_mesh_signal']; ?>/<?php echo $arra_node['left']['noise-floor']; ?></span> </div>
                                                <?php if($rx_type){ $rx_type = 0; ?>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['left']['rx']; ?></span> <span class="mi-box-small">Rx</span></div>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['left']['tx']; ?></span> <span class="mi-box-small">Tx</span></div>
                                                <?php } else {  $rx_type = 1; ?>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['left']['tx']; ?></span> <span class="mi-box-small">Tx</span></div>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['left']['rx']; ?></span> <span class="mi-box-small">Rx</span></div>
                                                <?php }  ?>


                                            </div>

                                        <?php }  else { ?>
                                            <div class="mi-box-left">&nbsp;</div>
                                        <?php }  ?>



                                        <div class="mi-box">

                                            <?php if(isset($arra_node['left'])  && isset($arra_node['left']['snr']) && ($arra_node['left']['snr'] <= 0 || $arra_node['left']['snr'] > 0) ){ ?>

                                                <?php
                                                if($arra_node['left']['mesh']['current_mesh_type'] == 'arra-mesh0'){
                                                    $color_class = 'yellow';
                                                } elseif($arra_node['left']['mesh']['current_mesh_type'] == 'arra-mesh1'){
                                                    $color_class = 'deeppink';
                                                } else {
                                                    $color_class = 'green mdot';
                                                }
                                                ?>
                                                <span class="status-circle <?php echo $color_class; ?> posleft"></span>

                                            <?php } ?>

                                            <?php if(isset($arra_node['right'])  && isset($arra_node['right']['snr']) && ($arra_node['right']['snr'] > 0 || $arra_node['right']['snr'] <= 0)){ ?>

                                                <?php

                                                if($arra_node['is_gateway']){
                                                    $color_class = 'dark';
                                                } elseif($arra_node['right']['mesh']['current_mesh_type'] == 'arra-mesh0'){
                                                    $color_class = 'yellow';
                                                } elseif($arra_node['right']['mesh']['current_mesh_type'] == 'arra-mesh1'){
                                                    $color_class = 'deeppink';
                                                } else {
                                                    $color_class = 'green mdot';
                                                }
                                                ?>
                                                <span class="status-circle <?php echo $color_class; ?> posright"></span>

                                            <?php } ?>


                                            <div class="mi-box-id"><?php echo $arra_node['sysname']; ?></div>

                                            <div class="mi-box-stats clearfix">
                                                <?php if(isset($arra_node['left'])  && isset($arra_node['left']['snr']) && ($arra_node['left']['snr'] <= 0 || $arra_node['left']['snr'] > 0)){ ?>
                                                    <div class="mi-box-stats-left">

                                                        <span><?php echo $arra_node['left']['ch']; ?> ch</span>
                                                        <span><?php echo $arra_node['left']['bw']; ?> bw</span>
                                                        <span><?php echo $arra_node['left']['db']; ?> db</span>

                                                    </div>
                                                <?php }  else { ?>
                                                    <div class="mi-box-stats-left"></div>
                                                <?php }  ?>

                                                <?php if(isset($arra_node['right']) && isset($arra_node['right']['snr']) && ($arra_node['right']['snr'] > 0 || $arra_node['right']['snr'] <= 0) && !$arra_node['is_gateway']){ ?>
                                                    <div class="mi-box-stats-right">

                                                        <span><?php echo $arra_node['right']['ch']; ?> ch</span>
                                                        <span><?php echo $arra_node['right']['bw']; ?> bw</span>
                                                        <span><?php echo $arra_node['right']['db']; ?> db</span>

                                                    </div>
                                                <?php }  else { ?>
                                                    <div class="mi-box-stats-right"></div>
                                                <?php }  ?>

                                                <?php if(!isset($arra_node['left']['ch']) && !isset($arra_node['right']['ch'])){ ?>
                                                    <div class="mi-box-stats-right">

                                                        <span>&nbsp;</span>
                                                        <span>&nbsp; </span>
                                                        <span>&nbsp; </span>

                                                    </div>

                                                <?php }  ?>

                                            </div>

                                        </div>

                                        <?php if(isset($arra_node['right']) && isset($arra_node['right']['snr']) && ($arra_node['right']['snr'] > 0 || $arra_node['right']['snr'] <= 0) && !$arra_node['is_gateway']){ ?>

                                            <div class="mi-box-right">

                                                <div><span class="clr-lighter"><?php echo $arra_node['right']['snr']; ?></span> <span class="mi-box-small">SNR</span></div>
                                                <div><span class="clr-lighter"><?php echo $arra_node['right']['mesh']['current_mesh_signal']; ?>/<?php echo $arra_node['right']['noise-floor']; ?></span> </div>
                                                <?php if($rx_type){ $rx_type = 0; ?>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['right']['rx']; ?></span> <span class="mi-box-small">Rx</span></div>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['right']['tx']; ?></span> <span class="mi-box-small">Tx</span></div>
                                                <?php } else {  $rx_type = 1; ?>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['right']['tx']; ?></span> <span class="mi-box-small">Tx</span></div>
                                                    <div><span class="clr-lighter"><?php echo $arra_node['right']['rx']; ?></span> <span class="mi-box-small">Rx</span></div>
                                                <?php }  ?>


                                            </div>

                                        <?php }  elseif($arra_node['is_gateway']) { ?>
                                            <div class="mi-box-right" style="width: 65px; margin-left: 3px;">

                                                <img src="/images/custom/plug-img.png" style="max-width: 100%; height:auto; margin-top: 5px;" alt=""/>

                                            </div>
                                        <?php }  else { ?>
                                            <div class="mi-box-right">&nbsp;</div>
                                        <?php }  ?>


                                    </div>

                                </div>
                            <?php } ?>
                       
					<?php } ?>

                    </div>

                </div>
            </div>
<?php } ?>
        </div>

    </div>
