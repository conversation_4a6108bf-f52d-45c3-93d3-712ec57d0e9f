<?php
/**
 * extendair.inc.php
 *
 * LibreNMS os poller module for Exalt ExtendAir
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * @package    LibreNMS
 * @link       http://librenms.org
 * @copyright  2017 <PERSON>
 * <AUTHOR> <<EMAIL>>
 */

$extendair_data = snmp_get_multi_oid($device, ['serialNumber.0', 'firmwareVersion.0'], '-OQs', 'ExaltComProducts');

$serial  = $extendair_data['serialNumber.0'];
$version = $extendair_data['firmwareVersion.0'];

unset($extendair_data);
