#voucher_used_code {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap:wrap;
}

#voucher_used_code > a {
	display: inline-block;
	text-align: center;
	text-decoration: none;
}

#voucher_used_code > a .product-tf {
	border: 5px solid #8caf14;
	background: #b0cb3c;
	display: block;
	text-align: center;
	min-width: 120px;
	height: 120px;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
	border-radius: 12px;
	margin: 20px 10px 10px 10px;
	font-size: 28px;
	font-weight: 700;
	text-decoration: none;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	transition: all 0.3s ease-in-out;
	padding:0 5px;
}

#voucher_used_code > a .product-price {
	display: block;
	margin-top: 10px;
	font-size: 21px;
	text-decoration: none;
	color: #acc92f;
	font-family: Century Gothic, CenturyGothic, AppleGothic, Verdana, sans-serif;
}

#voucher_used_code > a:hover .product-tf {
	border-color: #fff;
}
#voucher_products {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap:wrap;
}

#voucher_products > a {
	display: inline-block;
	text-align: center;
	text-decoration: none;
}

#voucher_products > a .product-tf {
	border: 5px solid #000;
	background: #fff;
	display: block;
	text-align: center;
	min-width: 165px;
	height: 120px;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
	border-radius: 12px;
	margin: 20px 10px 10px 10px;
	font-size: 28px;
	font-weight: 700;
	text-decoration: none;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #000;
	transition: all 0.3s ease-in-out;
	padding:0 5px;
}

@media screen and (max-width: 480px) {
	#voucher_products > a .product-tf {
		min-width: 130px;
		height: 95px;
		font-size: 22px;
	}
}

#voucher_products > a .product-price {
	display: block;
	margin-top: 10px;
	font-size: 21px;
	text-decoration: none;
	color: #fff;
	font-family: Century Gothic, CenturyGothic, AppleGothic, Verdana, sans-serif;
}

#voucher_products > a:hover .product-tf {
	border-color: #0054a6;
	color: #0054a6;
}


#plan_products {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap:wrap;
}

#plan_products > a {
	display: inline-block;
	text-align: center;
	text-decoration: none;
}

#plan_products > a .product-tf {
	border: 5px solid #000;
	background: #fff;
	display: block;
	text-align: center;
	min-width: 165px;
	height: 120px;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
	border-radius: 12px;
	margin: 20px 10px 10px 10px;
	font-size: 28px;
	font-weight: 700;
	text-decoration: none;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #000;
	transition: all 0.3s ease-in-out;
	padding:0 5px;
}

@media screen and (max-width: 480px) {
	#plan_products > a .product-tf {
		min-width: 130px;
		height: 95px;
		font-size: 22px;
	}
}

#plan_products > a .product-price {
	display: block;
	margin-top: 10px;
	font-size: 21px;
	text-decoration: none;
	color: #fff;
	font-family: Century Gothic, CenturyGothic, AppleGothic, Verdana, sans-serif;
}

#plan_products > a:hover .product-tf {
	border-color: #0054a6;
	color: #0054a6;
}

.plan_products_active .product-tf{
    border-color: #0054a6 !important;
    color: #0054a6 !important;
}

.modal-vouchers__promo img {
	max-width: 60px !important;
}

#vouchers_step2 {
	display: none;
	padding-top: 0px;
	max-width: 1140px;
	margin: 20px auto 0 auto;
}


@media (max-width: 768px) {
	#modal-vouchers {
		min-width: 0;
	}
}

.vouchers-btn-container {
	display: flex;
	justify-content: center;
	text-align: center;
	margin-top: 20px;
    padding: 0 20px;
}

.payment-back {
	display: inline-block;
	padding: 14px 25px;
	color: #fff !important;
	background: #000;
	font-size: 14px;
	font-weight: 700;
	text-transform: uppercase;
	margin: 15px 0;
	border-radius: 6px;
	transition: all 0.3s ease-in-out;
	text-decoration: none;
	min-width: 150px;
	border: none;
	outline: none;
	cursor: pointer;
	font-family: 'Muli', Arial, sans-serif !important;
	line-height: 18px;
	margin-right: 10px;
}

.payment-back:hover {
	background: #0054a6;
}

.payment-button {
	display: inline-block;
	padding: 13px 25px;
	color: #fff;
	background: #006ad1;
	font-size: 21px;
	font-weight: 700;
	text-transform: uppercase;
	margin: 15px 0;
	border-radius: 6px;
	transition: all 0.3s ease-in-out;
	text-decoration: none;
	min-width: 150px;
	border: none;
	outline: none;
	cursor: pointer;
	font-family: 'Muli', Arial, sans-serif !important;
}

.payment-button:hover {
	background: #0054a6;
}

.payment-button:disabled,
.payment-button[disabled] {
	background: #ccc;
	cursor: default
}

#voucher_gateways {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10px;
	flex-wrap: wrap;
}

.modal-vouchers__subtitle {
	text-align: center;
	font-size: 36px;
	letter-spacing: -1px;
	color: #fff;
	margin: 0px 0 10px 0;
	text-align: center;
	color: #fff;
}

@media screen and (max-width: 550px) {
  .modal-vouchers__subtitle {
    font-size: 28px;
  }
}

#voucher_gateways > a {
	display: inline-block;
	border: 3px solid #000;
	padding: 7px;
	margin: 5px 10px;
	text-decoration: none;
	color: #000;
	font-size: 16px;
	font-weight: 700;
	border-radius: 6px;
	transition: all 0.3s ease-in-out;
	position: relative;
	min-width: 94px;
	text-align: center;
	background: #fff;;
}

@media screen and (max-width: 480px) {
	#voucher_gateways > a:nth-child(2) {
		page-break-after: always; 
		break-after: always;
	}
	#voucher_gateways > a {
		min-width: 110px;
		margin-bottom: 15px;
	}
	
	.vouchers-btn-container {
		margin-top: 10px;
	}
}

#voucher_gateways > a span {
	display: block;
	font-size: 12px;
	font-weight: 700;
	color: #000;
	text-align: center;
	margin-top: 10px;
}

#voucher_gateways > a:after {
	background: url("../img/checkmark.png") no-repeat;
	width:24px;
	height: 24px;
	background-size: 24px;
	position: absolute;
	right: -13px;
	bottom: -9px;
	content:"";
	opacity: 0;
	transition: all 0.3s ease-in-out;

}

#voucher_gateways > a:hover {
	border-color: #0054a6;
	color: #0054a6;
}

#voucher_gateways > a.active {
	border-color: #31af91;
	background: #fff;
}

#voucher_gateways > a.active:after {
	opacity: 1;
}

#modal-vouchers-payment iframe {
	max-width: 100%;
}

.modal-vouchers__promo {
	margin-bottom: 20px !important;
}

#modal-vouchers {
	padding-bottom: 0px;
}
.hidden-el{
	display: none !important;
}

#order-standard_cart .cc-input-container {
	background: none !important;
	border: none !important;
}

#order-standard_cart .cc-input-container .radio-inline, #order-standard_cart label {
	color: #fff !important;
}

#order-standard_cart .cc-input-container .form-group {
	clear: both
}

#order-standard_cart #inputCardCVV .field {
    font-size: 12px !important;
    padding-left: 32px !important;
	border-radius: 50px 0 0 50px !important;
}

.prepend-icon .btn-default {
	border: 2px solid #fff !important;
	border-left: none !important;
	border-radius: 0 50px 50px 0 !important;
	background:rgba(255,255,255,0.35) !important;
	color:#fff !important;
}

.ar-pbox {
	border: 5px solid #000;
	background: #fff;
	display: block;
	text-align: center;
	min-width: 220px;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
	border-radius: 12px;
	margin: 15px 10px 10px 10px;
	
	text-decoration: none;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #000;
	transition: all 0.3s ease-in-out;
	padding:20px 20px;
	flex-wrap: wrap;
	flex-direction: column;
	
}

.ar-pbox .ar-pbox-title {
	font-size: 28px;
	font-weight: 700;
	width: 100%;
	text-shadow: 0 0 13px rgba(0,0,0,0.2)
}

.ar-pbox .ar-pbox-desc {
	text-align: left;
	width: 100%;
	margin: 15px 0 0 0;
	line-height: 28px;
	font-size: 18px;
	font-weight: 400;
	padding-left: 10px;
}


.ar-pbox:hover {
	border-color: #0054a6;
	color: #0054a6;
}

.plan_products_active .ar-pbox {
    border-color: #0054a6 !important;
    color: #0054a6 !important;
}