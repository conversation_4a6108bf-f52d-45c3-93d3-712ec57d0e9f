@import "reset";

* {
    box-sizing: border-box;
}

html {
    min-height: 100vh;
    width: 100%;
}

.bg_theme {
    padding-top: 40px;
    padding-bottom: 40px;
    width: 100%;
    height: 100%;
    min-height: 100vh;
    font-family: 'Muli', sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-repeat: no-repeat;
}

.bg_theme_1 {
    background-image: url("../images/bg1.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_2 {
    background-image: url("../images/bg2.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_3 {
    background-image: url("../images/bg3.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_4 {
    background-image: url("../images/bg4.jpg");
    background-position: center;
    background-size: 100% 100%;

}

.bg_theme_5 {
    background-image: url("../images/bg5.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_6 {
    background-image: url("../images/bg6.jpg");
    background-position: center;
    background-size: 100% 100%;

}

.bg_theme_7 {
    background-image: url("../images/bg7.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_8 {
    background-image: url("../images/bg8.jpg");
    background-position: bottom;
    background-size: cover;

}

.bg_theme_9 {
    background-image: url("../images/bg9.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_10 {
    background-image: url("../images/bg10.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_11 {
    background-image: url("../images/bg11.jpg");
    background-position: center;
    background-size: cover;

}

.bg_theme_12 {
    background-image: url("../images/bg12.jpg");
    background-position: center;
    background-size: 100% 100%;

}


.header {
    text-align: center;

    .logo {
        display: block;

        img {
            max-width: 300px;

            @media screen and (max-width: 700px) {
                max-width: 250px;
            }
        }
    }
}



.main {
    flex: 1 1 100%;
    padding-top: 30px;
    padding-bottom: 60px;
    height: 100%;
    display: block;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    text-align: center;

    @media screen and (max-width: 700px) {
        padding-bottom: 30px;
    }

    #toggle-modal {
        text-decoration: none;
        font-size: 36px;
        color: #ffffff;
        font-weight: bold;
        line-height: 1.2;
        text-shadow: 3px 4px 3px rgba(0, 0, 0, 1);
        text-transform: uppercase;
        transition: all 0.3s ease-in-out;

        &:hover {
            color: #C0C0C0;
        }

        @media screen and (max-width: 450px) {
            font-size: 32px;
        }
    }

    .wifi {
        max-width: 150px;

        @media screen and (max-width: 700px) {
            max-width: 100px;
        }
    }

    .error-message {
        max-width: 500px;
        font-size: 22px;
        margin-left: 20px;
        margin-right: 20px;
        color: #ffffff;
        padding: 10px 15px;
        background-color: #ff000d;
        border: 1px solid #d8000c;
        border-radius: 5px;
        font-weight: bold;
        box-shadow: 0px 10px 35px 10px rgba(0, 0, 0, 1);
        // text-shadow: 2px 2px 2px black;
        margin-bottom: 30px;

        @media screen and (max-width: 700px) {
            font-size: 18px;
            max-width: 300px;
        }

    }

    .search-section {

        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin: 80px 0 80px 0;

        form {
            position: relative;
        }

        @media screen and (max-width: 700px) {
            margin: 50px 0 50px 0;
        }

        @media screen and (max-width: 450px) {
            margin: 40px 0 40px 0;
        }

        input {
            font-family: 'Muli', sans-serif;
            font-size: 40px;
            padding: 20px 75px 20px 40px;
            max-width: 380px;
            border: none;
            -webkit-appearance: none;
            border-radius: 10px;
            outline: none;
            box-shadow: 0px 17px 40px 10px rgba(0, 0, 0, 1);
            transition: all 0.3s ease-in-out;

            @media screen and (max-width: 700px) {
                padding: 15px 63px 15px 25px;
                font-size: 32px;
                max-width: 300px;
            }

            @media screen and (max-width: 450px) {
                padding: 10px 63px 10px 15px;
                max-width: 270px;
            }

            &:focus {
                box-shadow: 0px 10px 35px 15px rgba(0, 0, 0, 1);
            }

            &::placeholder {
                color: #a8a5a6;

            }
        }

        button {
            padding: 0 17px;
            position: absolute;
            top: 7px;
            bottom: 7px;
            right: 0;
            border: none;
            background: transparent;
            outline: none;
            cursor: pointer;
            border-left: 4px solid #000000;


            img {
                vertical-align: middle;
                width: 30px;

                @media screen and (max-width: 700px) {
                    width: 20px;
                }
            }
        }
    }

}

.footer {
    .links {
        ul {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            font-size: 22px;
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 1);

            @media screen and (max-width: 700px) {
                flex-direction: column;
            }

            li {
                padding: 20px;

                @media screen and (max-width: 700px) {
                    padding: 10px;
                }

                a {
                    text-decoration: none;
                    color: #ffffff;
                    transition: all 0.3s ease-in-out;

                    &:hover {
                        color: #C0C0C0;
                    }
                }
            }
        }
    }
}

.ui-dialog {
    box-shadow: 0px 10px 35px 10px #000000;
    border-radius: 20px;
    display: flex;
    flex-direction: column;

    .ui-dialog-titlebar {
        background: #ffffff;
        border: none;
    }

    .ui-button {
        border: none;
        background-color: #ffffff;
        right: 15px;
        top: 25px;
        outline: none;

        .ui-icon {
            background-image: url("../images/close.png");
            background-size: cover;
            background-position: center;
            width: 18px;
            height: 18px;
        }
    }
}

.ui-widget-overlay {
    background: #000000;
}

#modal-vouchers {
    padding-left: 80px;
    padding-right: 80px;
    padding-bottom: 50px;
    color: #000000;
    font-family: 'Muli', sans-serif;

    @media screen and (max-width: 550px) {
        padding-left: 15px;
        padding-right: 15px;
        padding-bottom: 30px;
    }
}


.modal-vouchers {
    &__title {
        text-align: center;
        font-weight: bold;
        font-size: 28px;
        text-transform: uppercase;

        @media screen and (max-width: 550px) {
            font-size: 24px;
        }
    }

    &__promo {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-bottom: 70px;
        margin-top: 50px;

        @media screen and (max-width: 550px) {
            margin-bottom: 30px;
            margin-top: 30px;
        }

        img {
            max-width: 100px;
            height: auto;

            @media screen and (max-width: 550px) {
                max-width: 80px;
            }
        }

        p {
            margin-left: 10px;
            text-align: center;
            font-size: 22px;
            max-width: 180px;

            @media screen and (max-width: 550px) {
                font-size: 18px;
                max-width: 150px;
            }
        }
    }

    &__payment {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        justify-content: center;
        margin-bottom: 10px;
    }

    &__img {
        border-radius: 10px;
        width: 80px;
        height: 45px;
        border: 2px solid #acacac;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 10px;

        @media screen and (max-width: 550px) {
            width: 60px;
            height: 50px;
        }

        &:last-of-type {
            margin-right: 0;
        }

        img {
            max-width: 63px;
            max-height: 29px;

            @media screen and (max-width: 550px) {
                padding: 5px;
                max-width: 100%;
            }
        }
    }

    &__text {
        text-align: center;
        font-size: 16px;

        @media screen and (max-width: 550px) {
            font-size: 14px;
        }
    }
}