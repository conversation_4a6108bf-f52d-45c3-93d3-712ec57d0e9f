function getArraVoucherToken(){
	var name = 'arranetworks_arra_voucher';
	try {
		var vouchertoken = localStorage.getItem(name);
		
		if(vouchertoken){
			return vouchertoken;
		} else {
			return 0;
		}
	} catch(e) {
		return 0;
    }
}

function setArraVoucherToken(){
	
	var name = 'arranetworks_arra_voucher';	
	var voucher_token = $("#voucher_token").val();
	try {
		localStorage.removeItem(name);
		localStorage.setItem(name, voucher_token);
	} catch(e) {
		return 0;
    }
}

function getArraTempVoucherToken(){
	var name = 'last_arranetworks_arra_voucher';	
	try {
		var vouchertoken = localStorage.getItem(name);
		
		if(vouchertoken){
			return vouchertoken;
		} else {
			return '';
		}
	} catch(e) {
		return '';
    }
}

function setArraTempVoucherToken(voucher_code){
	
	var name = 'last_arranetworks_arra_voucher';
	try {
		localStorage.removeItem(name);
		localStorage.setItem(name, voucher_code);
	} catch(e) {
		return '';
    }
}


function changeVoucherStatus(voucher_code){
	
	// keep last voucher
	setArraTempVoucherToken(voucher_code);
	
	$.get(BASECHEKOUTURL + '/index.php?m=arranetworkscore&action=change_voucher_status&voucher='+ voucher_code, function(data){		   
		   if(data){			   
		   }
		   
	  });
}

jQuery( document ).ready(function() {
	//setArraVoucherToken();
});