$(function() {
  $("#modal-vouchers").dialog({
    modal: true,
    autoOpen: false,
    title: null,
    resizable: false,
    draggable: false,
    height: "auto",
    maxHeight: 600,
    width: "auto"
  });

  $("#toggle-modal").on("click", function() {
    $("#modal-vouchers").dialog("open");
  });

  

});

function opensubscriptionmodal(){
  $("#vouchers_step3").hide();
  $("#vouchers_step4").show();

}

function gotomacaction(mac){

  if($( ".webixtype_form" ).length){

    $(".webix_el_box input").val(mac);
    var tst = $(".webix_el_box input").val();

    $( ".webixtype_form" ).trigger( "click" );
    console.log('webixtype_form - click with mac: ' + tst);
  }

}

$(document).ready(function() {

  $("#go-to-step3").on("click", function() {
    $("#vouchers_step4").hide();
    $("#vouchers_step3").show();
  });

  $("#subscription_login_action").on("click", function() {
    var subscription_key = $.trim($("#subscription_login_input").val());


    if(subscription_key){

      var getparams =  new URLSearchParams(window.location.search);
      if(getparams.has('mac') && getparams.has('client_mac')){
        var mac = getparams.get('mac');
        var client_mac = getparams.get('client_mac');

        $.ajax({
          url: BASECHEKOUTURL+'/index.php?m=arranetworkscore&action=add_user_subsciption_device&device_mac='+client_mac+'&us='+subscription_key + '&router_mac_address='+mac,
          type: 'GET',
          data: {},
          success: function(response)
          {
            console.log('response',response);
            if(response.success == 1){
              $('#device_mac').val('');
              $("#success-mac").fadeIn(500).delay(2000).fadeOut(1000);

              if(response.redirect_url){
                arraredirecturl = response.redirect_url;
                gotomacaction(client_mac);

                // window.location = response.redirect_url;

              } else {
                // window.location = "https://www.google.com";
              }


            } else {

              $("#subscription_login_error").empty().html(response.message);
              $("#subscription_login_error").fadeIn(1000).delay(2000).fadeOut(1000);

              if(response.exist){
                if(response.redirect_url) {
                  arraredirecturl = response.redirect_url;
                }
                gotomacaction(client_mac);
                // setTimeout(function(){
                //
                //     if(response.redirect_url){
                //
                //       window.location = response.redirect_url;
                //
                //     } else {
                //       window.location = "https://www.google.com";
                //     }
                //
                //   }, 2000);
              }
            }
          }
        });

      } else {

        $("#subscription_login_error").empty().html('Use a valid Client MAC.');
        $("#subscription_login_error").fadeIn(1000).delay(2000).fadeOut(1000);


      }

    } else {
      $("#subscription_login_input").val('');
      $("#subscription_login_input").focus();
      $("#subscription_login_error").empty().html('Use a valid KEY.');
      $("#subscription_login_error").fadeIn(1000).delay(2000).fadeOut(1000);

    }

  });


  $(".webix_el_box input").click(function() {
	alert("clicked")
});
});
