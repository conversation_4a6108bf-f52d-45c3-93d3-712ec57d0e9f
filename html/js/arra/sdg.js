
function validatecpform(){
	
	var error = 0;
	var group = $.trim($('#group').val());

	if(group == ""){
		error++;
		$('.group').addClass( "has-error" );
		$('#group-error').html( "Plase select a Group");
		
		setTimeout(function() {
			$('.group').removeClass("has-error");
			$('#group-error').html( "");
		   }, 2000);

	}
	var host = $.trim($('#host').val());
	if(host == ""){
		error++;
		$('.host').removeClass( "has-error" ).addClass( "has-error" );
		$('#host-error').html( "Plase insert a valid Barcode MAC");
		setTimeout(function() {
			$('.host').removeClass("has-error");
			$('#host-error').html( "");
		   }, 2000);
	}

	var latlng = $.trim($('#latlng').val());
	if(latlng == ""){

		$('#latlng').val("0, 0");

		// error++;
		// $('.latlng').removeClass( "has-error" ).addClass( "has-error" );
		// $('#latlng-error').html( "Plase insert a valid Lat/Lng");
		// setTimeout(function() {
		// 	$('.latlng').removeClass("has-error");
		// 	$('#latlng-error').html( "");
		// }, 2000);
	} else {

		var res = latlng.split(",");
		if(res.length != 2){
			error++;
			$('.latlng').removeClass( "has-error" ).addClass( "has-error" );
			$('#latlng-error').html( "Plase insert a valid Lat/Lng");
			setTimeout(function() {
				$('.latlng').removeClass("has-error");
				$('#latlng-error').html( "");
			}, 2000);
		}

	}

	return error;
}

$( document ).ready(function() {
	$('#host').focus();
	$('#host').on("change keyup paste",function() {

		// handle events here
		var input_init = $( this ).val();    
		var val_pices = input_init.split(":");
		if(val_pices.length == 1){
	
		input_init = input_init.replace(/ /g,'');
		input = input_init.replace(/:/g,'');
		if(input.length == 12){
		input = input.substring(0,12);
		const first = input.substring(0,4);
		const second = input.substring(4,8);
		const third = input.substring(8,12);
	
	
		if(input.length > 8){var output = `${first}:${second}:${third}`;}
		else if(input.length > 4){var output = `${first}:${second}`;}
		else if(input.length > 0){var output = `${first}`;}
	
			$( this ).val(output);
		}
	
		}

	});
	$( "#group" ).change(function() {
		  return false;
		});
	$('.sdg-form').submit(function (eventObj) {
		$('#save_bt').attr('disabled','disabled');
		
		if(validatecpform() == 0){

			var group = $.trim($('#group').val());
			var host = $.trim($('#host').val());
			var latlng = $.trim($('#latlng').val());
			
			$.ajax({
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
                url: APP_URL+'/set-device-group/save/',
                type: 'POST',
                data: { group_id: group, host: host, latlng: latlng },
                success: function(response)
                {
                	if(response != ''){
                		$('#host').val('');
						// $('#latlng').val('');
                		$('#save_bt').removeAttr('disabled');
                		$('#host').focus();
                		if(response == 1){
                			
                			toastr.success('Device successfully added!');
                			
//                			$('#success_mess').show();
//                    		setTimeout(function() {
//                    			$('#success_mess').hide();
//                    		   }, 2000);

                		} else {

                			toastr.success('Device was already added!');
                			
                		}
                		
                		
                		
//                		window.location.href = response;
                	} else {
                		$('#host').focus();
                		$('.host').removeClass( "has-error" ).addClass( "has-error" );
                		$('#host-error').html( "Plase insert a valid host");
                		setTimeout(function() {
                			$('.host').removeClass("has-error");
                			$('#host-error').html( "");
                		   }, 2000);
                		$('#save_bt').removeAttr('disabled');
                	}
                	
                }
            });
			
		} else {
			$('#save_bt').removeAttr('disabled');
		}

	    return true;
	});
});