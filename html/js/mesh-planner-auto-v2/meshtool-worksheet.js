let map
// let clonedMapDataLines = null
const topographyOptions = {
   token: 'pk.eyJ1IjoiYXJyYW5ldHdvcmtzIiwiYSI6ImNrcDV5MmxrYTJrcTYyeG1jN3llZzR5cDkifQ.Fj_YjEdGwSXcB6zWEpzvVA',
   priority: 'speed'
}

let darkMode = L.tileLayer.provider('MapBox', {
   id: 'mapbox/dark-v10',
   accessToken: 'pk.eyJ1IjoiYXJyYW5ldHdvcmtzIiwiYSI6ImNrcDV5MmxrYTJrcTYyeG1jN3llZzR5cDkifQ.Fj_YjEdGwSXcB6zWEpzvVA',
   attribution: '&copy; ArraNetworks',
   maxZoom: 20
   // maxZoom: 23
})


let streetsMode = L.tileLayer.provider('MapBox', {
   id: 'mapbox/satellite-streets-v11',
   accessToken: 'pk.eyJ1IjoiYXJyYW5ldHdvcmtzIiwiYSI6ImNrcDV5MmxrYTJrcTYyeG1jN3llZzR5cDkifQ.Fj_YjEdGwSXcB6zWEpzvVA',
   attribution: '&copy; ArraNetworks',
   maxZoom: 20
   // maxZoom: 23
})

let newLinesAdded = false;
let mapdotscolor
let customIcon = L.divIcon({className: 'node-icon-class mesh-tool-custom-marker'});
let floatingMarkerIcon = L.divIcon({className: 'floating-icon-class'});
let mapSettings = {
   settings: {
      center: [47.174765576409335, 27.59821951389313],
      zoom: 16,
      fullscreenControl: false,
      pegmanControl: false,
      zoomControl: true,
      locateControl: false,
      searchControl: false,
      resizerControl: false,
      layersControl: false,
      minimapControl: false,
      rotateControl: false,
      loadingControl: false,
      printControl: false,
      gestureHandling: false,
      attributionControl: false
   }
};


let mapBounds = {}
let mapToggler = false

function initMap() {
   map = L.map('meshMap', mapSettings.settings)
   mapdotscolor = '#3D3C3CFF'
   changeMapView()
   return map
}

let mapData = {
   points: [],
   lines: [],
   project: {
      zoom: 13,
      center: [53.42979759373895, -2.957167826018037],
      advancedMenuState: false,
      poiVisibilityStatus: false,
      cluster_status: false,
      mapType: window.mapType ? window.mapType : 1
   },
   poi : []
}

let clusterData = []

// window.mapDataAuto = {
//    points: [],
//    lines: [],
//    project: {
//       zoom: 13,
//       center: [53.42979759373895, -2.957167826018037],
//       advancedMenuState: false,
//       poiVisibilityStatus: false,
//       mapType: 1
//    },
//    poi : []
// }

let linesTOBeDeleted = [];
let localPoiArr = [];

let globalCirclesStatus = false


if ((window.points.length && window.lines.length) || window.poi.length || !window.poi.length || window.reload) {

   mapData.points = JSON.parse(JSON.stringify(window.points));
   mapData.lines =  JSON.parse(JSON.stringify(window.lines));
   mapData.poi =  JSON.parse(JSON.stringify(window.poi));
   localPoiArr = _.cloneDeep(mapData.poi);

   //aiciiiii
   // window.points = [];
   window.lines = [];
   //window.mapDataAuto.lines = mapData.lines;
   //console.log(window.mapDataAuto.lines, 'window.mapDataAuto.lines refresh page');


   // clonedMapDataLines = _.cloneDeep(mapData.lines)
   action = 'put';
   mapData._method = 'PUT'
   mapData.project.advancedMenuState = window.advancedMenuState;
   mapData.project.poiVisibilityStatus = window.poiVisibilityStatus;
   mapData.project.cluster_status = window.cluster_status;
   mapData.project.node_connection_type_saved = window.node_connection_type_saved;
   mapData.project.node_connection_type = window.node_connection_type;
   mapData.project.name = window.project_name;
   document.getElementById('nctChoice'+node_connection_type).checked = true;
   globalCirclesStatus =  mapData.points.length ? mapData.points[0].settings.hascircle : false;
   let editZoom = JSON.parse(JSON.stringify(window.zoom));
   if (editZoom) {
      mapSettings.settings.zoom = JSON.parse(JSON.stringify(window.zoom));
      mapData.project.zoom = JSON.parse(JSON.stringify(window.zoom));
   }
   mapData.project.center = JSON.parse(JSON.stringify(window.center));
}


if (window.points.length ||  window.lines.length || window.poi.length) {

}
else {
   $('#confirm-location').modal('show')
   // focus on input
   document.getElementById("location").focus();
}


// if (window.points.length || window.lines.length || window.poi.length) {
//    console.log(window.points, 'a')
//    console.log(window.lines, 'a')
//    console.log(window.poi, 'a')
// } else {
//    console.log(window.points)
//    console.log(window.lines)
//    console.log(window.poi)
//    $('#confirm-location').modal('show')
//    // focus on input
//    document.getElementById("location").focus();
// }

// autosave + main save

function configureMapData() {
   if ((window.noRefresh && window.points.length && window.lines.length) || (window.points.length && window.noRefresh) || (window.noRefresh && window.poi.length) || (window.noRefresh && !window.poi.length)) {
      action = 'put';
      mapData._method = 'PUT';

      if (window.autoSave) {
         if (window.points && window.points.length) {
            rebuildMapDataPointsClusterInfo()
         }

         if (window.lines && window.lines.length) {
            rebuildMapDataLinesClusterInfo()
         }

         // if (window.poi.length) {
         //    mapData.poi.push(window.poi[0]);
         // }
         //let clusterLength = Object.keys(window.clusterInfo.cluster).length;
         // if (clusterLength) {
         //    rebuildClusterInfo();
         // }
         window.autoSave = false;
      } else {
         if (autoSaveAndImportPoi) {
            mapData.points = JSON.parse(JSON.stringify(mapData.points));
            mapData.lines =  JSON.parse(JSON.stringify(mapData.lines));
            autoSaveAndImportPoi = false;
         } else {
            mapData.points = JSON.parse(JSON.stringify(window.points));
            mapData.lines =  JSON.parse(JSON.stringify(window.lines));
         }
      }
      // clonedMapDataLines = _.cloneDeep(mapData.lines)
      window.noRefresh = false;
      window.noRefreshBottom = true;
      mapData.poi = configurePOI()
      localPoiArr = _.cloneDeep(mapData.poi)
      mapData.project.advancedMenuState = window.advancedMenuState;
      mapData.project.poiVisibilityStatus = window.poiVisibilityStatus;
      mapData.project.node_connection_type_saved = window.node_connection_type_saved;
      mapData.project.node_connection_type = window.node_connection_type;
      globalCirclesStatus =  mapData.points.length ? mapData.points[0].settings.hascircle : false;
      let editZoom = JSON.parse(JSON.stringify(window.zoom));
      if (editZoom) {
         mapSettings.settings.zoom = JSON.parse(JSON.stringify(window.zoom));
         mapData.project.zoom = JSON.parse(JSON.stringify(window.zoom));
      }
      mapData.project.center = JSON.parse(JSON.stringify(window.center));
   }
}

setInterval(() => {
   configureMapData();
}, 600)

initMap()


function rebuildMapDataLinesClusterInfo()
{
   return new Promise((resolve, reject) => {
      // if(window.clusterInfo.lines && window.clusterInfo.lines.length) {
      //    window.clusterInfo.lines.map((line) => {
      //       let lineIndex = mapData.lines.findIndex(lineMD => line.slug == lineMD.slug);
      //       if (lineIndex !== -1) {
      //          mapData.lines[lineIndex] = line;
      //       } else if (lineIndex == -1) {
      //          mapData.lines.push(line)
      //       }
      //    })
      //    resolve()
      // }

      if (window.lines && window.lines.length) {
         window.lines.map((line) => {
            let lineIndex = mapData.lines.findIndex(lineMD => line.slug == lineMD.slug);
            if (lineIndex !== -1) {
               mapData.lines[lineIndex] = line;
            } else if (lineIndex == -1) {
               mapData.lines.push(line)
            }
         })
         window.lines = [];
         resolve()
      }
   })
}


function rebuildMapDataPointsClusterInfo()
{
   return new Promise((resolve, reject) => {
      // if (window.clusterInfo.points && window.clusterInfo.points.length) {
      //    window.clusterInfo.points.map((point) => {
      //        let pointIndex = mapData.points.findIndex(pointMD => point.settings.index == pointMD.settings.index);
      //        if (pointIndex !== -1) {
      //          mapData.points[pointIndex] = point;
      //        } else if (pointIndex == -1) {
      //           mapData.points.push(point)
      //        }
      //    })
      //    resolve()
      // }

      if (window.points && window.points.length) {
         window.points.map((point) => {
            let pointIndex = mapData.points.findIndex(pointMD => point.settings.index == pointMD.settings.index);
            if (pointIndex !== -1) {
               mapData.points[pointIndex] = point;
            } else if (pointIndex == -1) {
               mapData.points.push(point);
            }
         })
         resolve()
      }
   })
}

// function filterClusterInfoPoints() {
//    let output = [];
//    if(window.clusterInfo.points && window.clusterInfo.points.length) {
//       window.clusterInfo.points.map((point) => {
//         if (!point.rebuild) {
//            output.push(point)
//         }
//       })
//    }
//    return output;
// }

// function filterClusterInfoLines() {
//    let output = [];
//    if(window.clusterInfo.lines && window.clusterInfo.lines.length) {
//       window.clusterInfo.lines.map((line) => {
//          if (!line.rebuild) {
//             output.push(line)
//          }
//       })
//    }
//    return output;
// }

// function rebuildClusterInfo() {
//    console.log( window.clusterInfo, '; window.cluster')
//    window.clusterInfo.map(cluster => {
//       console.log(cluster, 'MAN')
//       let clusterIndex = clusterData.findIndex(clusterData => clusterData.cluster_id == cluster.cluster_id);
//       if (clusterIndex != -1) {
//          clusterData[clusterIndex] = cluster;
//       } else if (clusterIndex == -1) {
//          clusterData.push(cluster);
//       }
//    })
//    window.clusterInfo.cluster = [];
// }
//
// function checkIfClusterDataExists(cluster_id) {
//    let clusterIndex = clusterData.findIndex(clusterData => parseInt(clusterData.cluster_id) == parseInt(cluster_id));
//    return clusterIndex != -1;
// }

// function rebuildMapDataPoints() {
//    window.points.map(point => {
//       let pointIndex = mapData.points.findIndex(point => point.settings.lat == window.points[0].settings.lat && point.settings.lng == window.points[0].settings.lng)
//       if (pointIndex !== -1) {
//          mapData.points[pointIndex] = point;
//       } else if (pointIndex == -1) {
//          mapData.points.push(point);
//       }
//    })
// }
//
// function rebuildMapDataLines() {
//    window.lines.map(line => {
//       let findLineIndex = mapData.lines.findIndex(mapLine => mapLine.start_slug == line.start_slug && mapLine.end_slug == line.end_slug)
//       if (findLineIndex == -1) {
//          mapData.lines.push(line);
//       }else if(findLineIndex != -1) {
//          mapData.lines[findLineIndex] = line;
//       }
//    })
//    window.lines = [];
//    generateMapMesh();
// }

function changeMapView() {
   let mapTypeText = document.getElementById('mapTypeTitle')
   if (mapData.project.mapType == '1') {
      mapTypeText.innerHTML = 'Satelite';
      map.addLayer(darkMode);
      map.removeLayer(streetsMode);
   } else {
      mapTypeText.innerHTML = 'Map';
      map.removeLayer(darkMode);
      map.addLayer(streetsMode);
   }
}

// map.addLayer(darkMode);

// map.addLayer(streetsMode);

function configurePOI() {
   if (window.poi.length) {
      let returnWholeMapDataPoi = false;
      let newImport = false;
      for(let i = 0; i < window.poi.length; i++) {
         if (window.poi[i].modified == true) {
            returnWholeMapDataPoi = true;
            let poiChangedIndex = localPoiArr.findIndex(point => point.lat == window.poi[i].lat && point.lng == window.poi[i].lng);
            if (poiChangedIndex !== -1) {
               localPoiArr[poiChangedIndex] = window.poi[i]
            }
         } else {
            newImport = true
         }
      }
      console.log(returnWholeMapDataPoi, 'returnWholeMapDataPoi')
      console.log(newImport, 'newImport')

      if (returnWholeMapDataPoi) {
         return localPoiArr;
      } else if (newImport) {
        return localPoiArr.concat(window.poi)
      } else {
         console.log('if else')
         return window.poi;
      }
   } else {
      console.log('else')
      return localPoiArr;
   }
}

L.control.fullscreen({
   content: '<i class="fa fa-expand"></i>',
   position: 'bottomright',
   forceSeparateButton: false,
   forcePseudoFullscreen: false, // force use of pseudo full screen even if full screen API is available, default false
   fullscreenElement: false // Dom element to render in full screen, false by default, fallback to map._container
}).addTo(map);


map.boxZoom.disable()

let nodeRadiusStatus = document.getElementById('nodeRadiusStatus')

// const heatmapStep = 50
const lineStep = 1;
let markersIndex = []
let destination_point = []
let newMarker
let polyLineDistance
let floatingMarker
let selectedNode
let selectedMarker
let globalSelectedPointKey
let generatedHeatmapNode = null
let globalGeneratedHeatmapNode = null
let generatedGraphLine = null
let globalGeneratedGraphLine = null
let mapContainer = document.getElementById("meshMap")
let nodeContainer = document.getElementById("nodeSettingsContainer")
let lineMenu = document.getElementById("polyLineContextMenu")
let markerMenu = document.getElementById("markerContextMenu")
let loadingStatic = document.getElementById('loadingScreenPin')

let nodenameEl = document.getElementById('nodeIdentifier')
let nodeheightEl = document.getElementById('nodeHeight')
let nodeLatitudeEl = document.getElementById('nodeLatitude')
let nodeLongitudeEl = document.getElementById('nodeLongitude')
let nodedistanceEl = document.getElementById('nodeAreaDistance')
let heatMapAreaSector = document.getElementById('heatmapAreaSector')
let heatMapAreaAngle = document.getElementById('heatmapAreaAngle')
let customValueField = document.getElementById('customAngleValue')
let heatMapAreaBearing = document.getElementById('heatmapAreaBearing')
let heatMapAreaBearingPitch = document.getElementById('heatmapBearingPitch')
let heatmapLineStep = document.getElementById('sectorLineStep')
let heatmapState = document.getElementById('heatmapSlider')
let nodeTransmitPower = document.getElementById('nodeTransmitPower')
let nodeGain = document.getElementById('nodeGain')
let nodeCableLoss = document.getElementById('nodeCableLoss')
let nodeOtherLoss = document.getElementById('nodeOtherLoss')
let nodeFrequency = document.getElementById('nodeFrequency')
let secondAntennaHeight = document.getElementById('secondAntennaHeight')
let secondNodeGain = document.getElementById('secondNodeGain')
let secondNodeCableLoss = document.getElementById('secondNodeCableLoss')
let bandWidth = document.getElementById('bandWidth')
let backhaulStatusEl = document.getElementById('backhaulStatus')
let loadingFixed = document.getElementById('loadingScreenPinFixed')

let nodeSettingsBox = document.getElementById('nodeSettings')
let nodeBoxMessage = document.getElementById('emptyNodeBoxMessage')
let graphBoxMessage = document.getElementById('emptyGraphBoxMessage')
let elevationContainer = document.getElementById('graphContainer')
let graphBox = document.getElementsByClassName('graph-box')[0]
let deleteConfirmationPopup = document.getElementById('deleteConfirmationModal')
let meshNodesCount = document.getElementById('meshNodesCount')
let meshPoiCoverage = document.getElementById('meshPoiCoverage')
let meshClusterNodes = document.getElementById('meshClusterNodes')
let meshClusterCoverage = document.getElementById('meshClusterCoverage')
let clusterNodesEl = document.getElementById('meshClusterNodesWrapper')
let clusterCoverageEl = document.getElementById('meshClusterCoverageWrapper')
// points of interest utils start

let poiIcon
let globalSelectedPoi
let globalPOIKey


let poiMeshStatus = document.getElementById('poiMeshStatus')
let clusterMeshStatus = document.getElementById('clusterMeshStatus')
// let poiHandlerBtn = document.getElementById('poiHandlerBtn')
let coordsChangePopup = document.getElementById('poiCoordsChangeConfirmationModal')
let pointOfInterestSettings = document.getElementById('pointOfInterestSettings')
let poiAddressEl = document.getElementById('pointOfInterestAddress')
let poiLatEl = document.getElementById('pointOfInterestLat')
let poiLngEl = document.getElementById('pointOfInterestLng')
let poiIconEl = document.getElementById('pointOfInterestIcon')
let poiIconColorEl = document.getElementById('pointOfInterestIconColor')
let poiLabelEl = document.getElementById('pointOfInterestLabel')
let poiLabelType = document.getElementById('pointOfInterestType')
let poiPopupContent = document.getElementById('poiPopup')
let customPoiIconEl = document.getElementById('customPoiIconEl')
let customPoiIconInput = document.getElementById('customIconValue')
let rmbrNctType = document.getElementById('nctChoice')
let savedNodeConnTypeEl = document.getElementById('savedNodeConnTypeWrapper')

// let floatingPoiIcon = L.divIcon({
//    className: 'floating-poi-class',
//    html: '<i class="fa fa-street-view"></i>'
// });
let floatingPoiIcon = L.divIcon({className: 'floating-poi-class'});
let poiClusters = null

// points of interest utils end

let linesStateToggler = false
let sectorBearingsArr = []
let availableConnectionNodes = []
let sectorLinesWithPoints = []
let sectorHeatpoints = []
let pointsElevationRange = []
let range1 =[]
let range2 =[]
let range3 =[]
let bearingHandleIcon = L.divIcon({className: 'bearing-handle-class'});
let newBearingHandle
let markerHeatArea

// heatmap utils start

//const SPEED_OF_LIGHT = 0.299792458;
const SPEED_OF_LIGHT = 299792458;
let RFfrequency = 5.5;
let waveLength = null;
let lineOfSight = null;
let fresnelRadius = null;
let treshold_60 = null;
let treshold_80 = null;
let treshold_100 = null;
let receiverPower = null;

// end
let isAProblemCalcElevGraph = false;
function drawElevationProfile(lineElevation, rightElevation, leftElevation, tower1Hght = null, tower2Hght = null, leftKeyPoint, rightKeyPoint) {
   let a = '';


   //$('#x_tx_height)
   let tower1Height = tower1Hght || mapData.points[leftKeyPoint].settings.nodeheight;
   // $('#x_rx_height')
   let tower2Height = tower2Hght || mapData.points[rightKeyPoint].settings.nodeheight;

   window.firstTowerHeight = tower1Height
   window.secondTowerHeight = tower2Height

   let tower1AntennaGain = mapData.points[leftKeyPoint].settings.gain

   //$('#x_rx_gain').val()
   let tower2AntennaGain = mapData.points[rightKeyPoint].settings.gain

   // ANTENNA INFORMATIONS [END]


   // $('#x_frequency')
   let frequencyHz = mapData.points[leftKeyPoint].settings.node_frequency;

   let middleFresnel = (.5 * Math.sqrt(299.7 / parseFloat(frequencyHz) * 12742008 * Math.sin(polyLineDistance / 12742008))).toFixed(2);

   let cableLoss = mapData.points[leftKeyPoint].settings.cable_loss;


   let Lq = 32.4 + 20 * Math.log(polyLineDistance / 1E3) / Math.log(10) + 20 * Math.log(frequencyHz) / Math.log(10) + parseFloat(cableLoss);
   Lq = Lq.toFixed(2); // "62.28"

   //Transmit power (Tx Power) - $(#x_tx_power)
   let tPower = mapData.points[leftKeyPoint].settings.transmit_power;

   // select with two options: 1. "dBm" 2. "mW"
   let powerUnit = $("#x_tx_power_unit").val();

   if (powerUnit == 'mW') {
      tPower = (10 * Math.log(tPower) / Math.log(10)).toFixed(2)
   }



   let Sign =  parseFloat(tPower) + parseFloat(tower1AntennaGain) + parseFloat(tower2AntennaGain);
   Sign = (Sign - Lq).toFixed(2);

   let eirp = parseFloat(tPower) + parseFloat(tower1AntennaGain) - parseFloat(tower2AntennaGain) / 2;
   eirp = eirp.toFixed(2)
   let lastPointElevation = lineElevation[lineElevation.length - 1].elevation
   let firstPointElevation = lineElevation[0].elevation

   // line of sight calc
   let los = Math.sqrt(Math.pow(polyLineDistance, 2) + Math.pow(window.firstTowerHeight + firstPointElevation - window.secondTowerHeight - lastPointElevation, 2));

   // wavelength calc
   let wvl = SPEED_OF_LIGHT / frequencyHz

   // The signal strength that the receiving end RX can receive
   let rx_power = mapData.points[leftKeyPoint].settings.transmit_power + mapData.points[leftKeyPoint].settings.gain - mapData.points[leftKeyPoint].settings.cable_loss + mapData.points[rightKeyPoint].settings.gain - mapData.points[rightKeyPoint].settings.cable_loss - mapData.points[leftKeyPoint].settings.other_loss - 20 * Math.log( 4 * Math.PI * los * wvl )
   // Signal attenuation margin
   let signalAttenuationMargin = (parseFloat(Sign) - rx_power).toFixed(2)

   // let tower1AntennaGain = '16'
   //
   // //$('#x_rx_gain').val()
   // let tower2AntennaGain = '16'
   //
   // // ANTENNA INFORMATIONS [END]
   //
   //
   // // $('#x_frequency')
   // let frequency = '5500';
   //
   // let middleFresnel = (.5 * Math.sqrt(299.7 / parseFloat(frequency) * 12742008 * Math.sin(polyLineDistance / 12742008))).toFixed(2);
   //
   // let cableLoss = $("#cableloss").val();
   //
   //
   // let Lq = 32.4 + 20 * Math.log(polyLineDistance / 1E3) / Math.log(10) + 20 * Math.log(frequency) / Math.log(10) + parseFloat(cableLoss);
   // Lq = Lq.toFixed(2); // "62.28"
   //
   // //Transmit power (Tx Power) - $(#x_tx_power)
   // let tPower = '28';
   //
   // // select with two options: 1. "dBm" 2. "mW"
   // let powerUnit = $("#x_tx_power_unit").val();
   //
   // if (powerUnit == 'mW') {
   //    tPower = (10 * Math.log(tPower) / Math.log(10)).toFixed(2)
   // }
   //
   //
   //
   // let Sign =  parseFloat(tPower) + parseFloat(tower1AntennaGain) + parseFloat(tower2AntennaGain);
   // Sign = (Sign - Lq).toFixed(2);
   //
   // let eirp = parseFloat(tPower) + parseFloat(tower1AntennaGain) - parseFloat(tower2AntennaGain) / 2;
   // eirp = eirp.toFixed(2)
   //
   // // The signal strength that the receiving end RX can receive
   // let rx_power =  parseFloat($("#x_rx_power").val());
   //
   // // Signal attenuation margin
   // let signalAttenuationMargin = (parseFloat(Sign) - rx_power).toFixed(2)


   if (0 > tower1AntennaGain || 40 < tower1AntennaGain)
   a += "\u53d1\u5c04\u7ad9\u7684\u5929\u7ebf\u589e\u76ca\uff1b";
   if (0 > tower2AntennaGain || 40 < tower2AntennaGain)
   a += "\u63a5\u6536\u7ad9\u7684\u5929\u7ebf\u589e\u76ca\uff1b";
   if (0 > tower1Height || 200 < tower1Height)
   a += "\u53d1\u5c04\u7ad9\u7acb\u6746\u94c1\u5854\u9ad8\u5ea6(\u5929\u7ebf\u8ddd\u79bb\u5730\u8868\u76f8\u5bf9\u9ad8\u5ea6)\uff1b";
   if (0 > tower2Height || 200 < tower2Height)
   a += "\u63a5\u6536\u7ad9\u7acb\u6746\u94c1\u5854\u9ad8\u5ea6(\u5929\u7ebf\u8ddd\u79bb\u5730\u8868\u76f8\u5bf9\u9ad8\u5ea6)\uff1b";
   if (0 > tPower || 50 < tPower)
   a += "\u53d1\u5c04\u529f\u7387\uff1b";
   if (-150 > rx_power || -50 < rx_power)
   a += "\u63a5\u6536\u95e8\u9650\uff1b";
   if (300 > frequencyHz || 41E3 < frequencyHz)
   a += "\u9891\u7387\uff1b";
   if (0 > 0 || 30 < 0)
   a += "\u7ebf\u7f06\u635f\u8017\u3002";

   "" != a && (a = "  \u8bf7\u68c0\u67e5\u60a8\u8f93\u5165\u7684\u503c\u53ef\u80fd\u4f1a\u8d85\u51fa\u901a\u5e38\u6240\u4f7f\u7528\u8303\u56f4\uff1a" + a);
   600 > 2400 && (a += "\u7531\u4e8e\u60a8\u7684\u8bbe\u5907\u9891\u7387\u4e0d\u9ad8\uff0c\u901a\u5e38First Fresnel\u7684\u5c11\u91cf\u4fe1\u53f7\u7b49\u4ece\u53d1\u5c04\u62b5\u8fbe\u63a5\u6536\u4e5f\u53ef\u4ee5\u5b9e\u73b0\u4f20\u8f93\uff1b\u672c\u5de5\u5177\u672a\u8ba1\u7b97\u7535\u78c1\u6ce2\u6563\u5c04\u3001\u884d\u5c04\u5bf9\u4fe1\u53f7\u7684\u5f71\u54cd\uff1b");


   let plotSamples = lineElevation.length;
   let f = d = c = b = 0, k = 1;
   for (var z = [], h = parseFloat(lineElevation[0].elevation) + parseFloat(tower1Height), p = parseFloat(lineElevation[plotSamples - 1].elevation) + parseFloat(tower2Height), y = [], A = [], B = [], C = [], E = [], D = [], F = [], x = 0; x < plotSamples; x++) {
      var q = x * polyLineDistance / (plotSamples - 1)
      , t = parseFloat(frequencyHz);


      var v = 12742008 * Math.sin(polyLineDistance / 12742008)
      , r = 12742008 * Math.sin(q / 12742008);


      q = 12742008 * parseFloat(Math.sin((polyLineDistance - q) / 12742008))

      if (x == plotSamples - 1) {
         try {
            if (bigDecimal.compareTo("0.0000000001", bigDecimal.round(q, 16))) {
               q = 0.0000000001;
            }
         } catch (error) {
            q = 0.0000000001;
         }
      }

      var u = (v + r + q) / 2;
      q = 2 * Math.sqrt(u * (u - v) * (u - r) * (u - q)) / v;
      r = Math.sqrt(r * r - q * q);
      u = v - r;
      t = 299.7 / t * r * u;
      u = r + u;
      r = Math.sqrt(t / u);
      //t = Math.sqrt(.6 * t / u);
      t = .6 * r;



      z.push([x * (parseFloat(polyLineDistance) / (plotSamples - 1)), h - (h - p) * x / (plotSamples - 1)]);
      y.push([v / (plotSamples - 1) * x, h - (h - p) * x / (plotSamples - 1) + r]);
      A.push([v / (plotSamples - 1) * x, h - (h - p) * x / (plotSamples - 1) - r]);
      B.push([v / (plotSamples - 1) * x, h - (h - p) * x / (plotSamples - 1) + t]);
      C.push([v / (plotSamples - 1) * x, h - (h - p) * x / (plotSamples - 1) - t]);

      // parseFloat(m[x]) + q > h - (h - p) * x / (plotSamples - 1) - t && (parseFloat(m[x]) + q - (h - (h - p) * x / (plotSamples - 1) - t) > d && (d = parseFloat(m[x]) + q - (h - (h - p) * x / (plotSamples - 1) - t),
      if (
         parseFloat(lineElevation[x].elevation) + q > h - (h - p) * x / (plotSamples - 1) - t &&
         parseFloat(lineElevation[x].elevation) + q - (h - (h - p) * x / (plotSamples - 1) - t) > d) {
            d = parseFloat(lineElevation[x].elevation) + q - (h - (h - p) * x / (plotSamples - 1) - t);
         }

         b = v / (plotSamples - 1) * x;
         c = parseFloat(lineElevation[x].elevation);


         // parseFloat(lineElevation[g]) + q - (h - (h - p) * g / (plotSamples - 1) + r) > f && (f = parseFloat(lineElevation[g]) + q - (h - (h - p) * g / (plotSamples - 1) + r)));
         if ( parseFloat(lineElevation[x].elevation) + q - (h - (h - p) * x / (plotSamples - 1) + r) > f) {
            f = parseFloat(lineElevation[x].elevation) + q - (h - (h - p) * x / (plotSamples - 1) + r)
         }


         //   parseFloat(lineElevation[g]) + q >= h - (h - p) * g / (plotSamples - 1) - r && parseFloat(lineElevation[g]) + q <= h - (h - p) * g / (plotSamples - 1) + r && parseFloat(lineElevation[g]) + q != h - (h - p) * g / (plotSamples - 1) + r && k > (h - (h - p) * g / (plotSamples - 1) + r - (parseFloat(lineElevation[g]) + q)) / (2 * r) && (k = (h - (h - p) * g / plotSamples + r - (parseFloat(lineElevation[g]) + q)) / (2 * r));
         if (parseFloat(lineElevation[x].elevation) + q >= h - (h - p) * x / (plotSamples - 1) - r &&
         parseFloat(lineElevation[x].elevation) + q <= h - (h - p) * x / (plotSamples - 1) + r &&
         parseFloat(lineElevation[x].elevation) + q != h - (h - p) * x / (plotSamples - 1) + r &&
         k > (h - (h - p) * x / (plotSamples - 1) + r - (parseFloat(lineElevation[x].elevation) + q)) / (2 * r))
         {

            k = (h - (h - p) * x / plotSamples + r - (parseFloat(lineElevation[x].elevation) + q)) / (2 * r)

         }

         D.push([polyLineDistance / (plotSamples - 1) * x, parseFloat(lineElevation[x].elevation) + q]);
      }


      b = b.toFixed(0);
      c = c.toFixed(0);
      (100 * k).toFixed(1);
      d = d.toFixed(2);
      0 < f && (a += "\u8ba1\u7b97\u7ed3\u679c\u4e2d\u7684\u4fe1\u53f7\u5f3a\u5ea6\u4e3aFirst Fresnel\u533a\u57df\u5b8c\u5168\u65e0\u906e\u6321\u65f6\u7406\u8bba\u503c\uff0c\u76ee\u524d\u5168\u90e8First Fresnel\u533a\u57df\u90fd\u88ab\u906e\u6321\uff1b");
      0 == f && 0 < d && (a += "\u8ba1\u7b97\u7ed3\u679c\u4e2d\u7684\u4fe1\u53f7\u5f3a\u5ea6\u4e3aFirst Fresnel\u533a\u57df\u5b8c\u5168\u65e0\u906e\u6321\u65f6\u7406\u8bba\u503c\uff0c\u76ee\u524d\u6709\u90e8\u5206First Fresnel\u4fe1\u53f7\u53ef\u4ee5\u4ece\u53d1\u5c04\u62b5\u8fbe\u63a5\u6536\uff1b");



      let yforCanvas = [];
      for (let i = 0; i < y.length; i++) {
         let obj = {
            x: Math.round(y[i][0]),
            y: y[i][1],
         }

         yforCanvas.push(obj)
      }

      let bforCanvas = [];
      for (let i = 0; i < B.length; i++) {
         let obj = {
            x: Math.round(B[i][0]),
            y: B[i][1],
            fresnelHeightLabel: 'Fresnel height'
         }

         bforCanvas.push(obj)
      }

      let dforCanvas = [];
      let drawTowers = [];
      for (let i = 0; i < D.length; i++) {
         polylineElevPoints[i].elev_graph_x = Math.round(D[i][0]);
         let obj = {
            x: Math.round(D[i][0]),
            y: D[i][1],
            color: '#b7bab3',
            crudX: D[i][0]
         }

         if (i == 0) {
            let tower1Hght = parseFloat(tower1Height)
            drawTowers.push({
               x: 0,
               y: [obj.y, obj.y + tower1Hght],
               height: tower1Hght,
               pointKey: leftKeyPoint,
               index: mapData.points[leftKeyPoint].settings.index,
               color: '#6c7368',
               towerHeightLabel: 'Tower height',
               flotType: 'left'
            });
         }


         if (i == D.length - 1) {
            let objX = Math.round(polyLineDistance)
            let tower2Hght = parseFloat(tower2Height)
            drawTowers.push({
               x: objX,
               y: [obj.y, obj.y + tower2Hght],
               pointKey: rightKeyPoint,
               index: mapData.points[rightKeyPoint].settings.index,
               color: '#6c7368',
               height: tower2Hght,
               towerHeightLabel: 'Tower height',
               flotType: 'left'
            });
         }

         dforCanvas.push(obj);
      }

      let aforCanvas = [];
      for (let i = 0; i < A.length; i++) {
         let obj = {
            x: Math.round(A[i][0]),
            y: A[i][1],
            difference: (A[i][1] - dforCanvas[i].y).toFixed(1),
            color: '#add676'
         }

         aforCanvas.push(obj)
      }


      let cforCanvas = [];
      for (let i = 0; i < C.length; i++) {
         let obj = {
            x: Math.round(C[i][0]),
            y: C[i][1],
            difference: (C[i][1] - dforCanvas[i].y).toFixed(1),
            color: 'green',
         }

         cforCanvas.push(obj)
      }


      let zforCanvas = [];
      for (let i = 0; i < z.length; i++) {
         let obj = {
            x: Math.round(z[i][0]),
            y: z[i][1],
            difference: (z[i][1] - dforCanvas[i].y).toFixed(1),
            lineHeightLabel: 'Line of Sight Height',
            lineOfSightColor: '#848780',
            color: '#b7bab3',
         }

         zforCanvas.push(obj)
      }

      let m = 0;
      leftForCanvas = []
      for (let i = 0; i < leftElevation.length; i++) {
         let obj = {
            x: - (lineStep * m),
            y: leftElevation[i].elevation
         }
         m += 1;
         leftForCanvas.unshift(obj);
      }

      let j = 0;
      rightForCanvas = [];
      let objX = Math.round(polyLineDistance)
      for (let i = 0; i < rightElevation.length; i++) {
         let obj = {
            x: objX + (lineStep * j),
            y: rightElevation[i].elevation
         }
         j += 1;
         rightForCanvas.push(obj);
      }

      var chart = new CanvasJS.Chart("chartContainer", {
         //animationEnabled: true,
         theme: 'dark1',
         backgroundColor: "transparent",
         dataPointWidth: 7,
         axisX: {
            gridThickness: 0,
            tickLength: 1,
            lineThickness: 0,
            labelFormatter: function () {
               return " ";
            },
            labelAngle: 0,
            crosshair: {
               enabled: true,
               snapToDataPoint: true,
               //valueFormatString: "#.## m",
               labelFormatter: function ( e ) {
                  return showPointsCrosshair(e)
               }
            }
         },
         axisY: {
            gridThickness: 0,
            tickLength: 0,
            lineThickness: 0,
            labelFormatter: function () {
               return " ";
            },
            interval: 10
         },
         toolTip:{
            shared: true,
            // contentFormatter: function ( e ) {
            //    // console.log(e.entries, 'dataSeries')
            //    // console.log(e.entries[0].dataPoint.y, 'e.entries[0].dataPoint.y')
            //    return "Custom ToolTip";
            // }

         },
         data: [
            {
               type: "spline",
               showInLegend: true,
               axisYIndex: 1, //Defaults to Zero
               name: "Straight line between two points",
               toolTipContent: "<span style='\"'color: {color};'\"'><strong>Distance to Site:</strong> {x} m<br><span style='\"'color: {lineOfSightColor}'\"'><strong>{lineHeightLabel}</strong>: {difference} m",
               yValueFormatString: "#.## m",
               dataPoints: zforCanvas,
               color: '#848780',
            },
            {
               type: "spline",
               axisYIndex: 1, //When axisYType is secondary, axisYIndex indexes to secondary Y axis & not to primary Y axis
               toolTipContent: null,
               markerType: 'none',
               dataPoints: yforCanvas,
               color: '#add676',

            },
            {
               type: "spline",
               xValueFormatString: "Distance to Site: ###### m",
               dataPoints: bforCanvas,
               toolTipContent: null,
               lineDashType: "dot",
               markerType: 'none',
               color: 'green',
            },
            {
               type: "spline",
               name: "60% of the First Fresnel area (without obstacles)",
               toolTipContent: "<span style='\"'color:  {color}'\"'><strong>60% Fresnel Height:</strong> {difference} m",
               dataPoints: cforCanvas,
               yValueFormatString: "#.## m",
               lineDashType: "dot",
               markerSize: 1,
               showInLegend: true,
               color: 'green',
            },
            {
               type: "spline",
               xValueFormatString: "Distance to Site: ###### m",
               name: "First Fresnel area",
               toolTipContent: "<span style='\"'color: {color}'\"'><strong>Fresnel Height:</strong> {difference} m",
               yValueFormatString: "#.## m",
               dataPoints: aforCanvas,
               showInLegend: true,
               color: '#add676',
            },
            {
               type: "splineArea",
               showInLegend: true,
               toolTipContent: null,
               name: "Surface lines (including the curvature of the earth)",
               dataPoints: dforCanvas,
               color: '#b7bab3',
            },
            {
               type: "rangeColumn",
               toolTipContent: "<span style='\"'color: {color}', float: {flotType}'\"'><strong>{towerHeightLabel}:</strong> {height} m",
               //toolTipContent: null,
               dataPoints: drawTowers,
            },
            {
               type: "splineArea",
               showInLegend: false,
               toolTipContent: null,
               markerType:'none',
               dataPoints: leftForCanvas,
               color: '#b7bab3'
            },
            {
               type: "splineArea",
               showInLegend: false,
               toolTipContent: null,
               dataPoints: rightForCanvas,
               markerType: 'none',
               color: '#b7bab3'
            }
         ]

      });

      chart.canvas.setAttribute('id', 'elevation-fresnel')
      chart.render();

      var xSnapDistance = 2;
      var ySnapDistance = 3;

      var xValue, yValue;

      var mouseDown = false;
      var selected = null;
      var changeCursor = false;

      var timerId = null;

      function getPosition(e) {
         var parentOffset = $("#chartContainer > .canvasjs-chart-container").offset();
         var relX = e.pageX - parentOffset.left;
         var relY = e.pageY - parentOffset.top;
         xValue = Math.round(chart.axisX[0].convertPixelToValue(relX));
         yValue = Math.round(chart.axisY[0].convertPixelToValue(relY));
      }

      function searchDataPoint() {
         var dps = chart.data[6].dataPoints;
         for(var i = 0; i < dps.length; i++) {
           // (xValue >= dps[i].x - xSnapDistance && xValue <= dps[i].x + xSnapDistance)
          // console.log(xValue, dps[i].x, xSnapDistance, 'first debug')
          // console.log(xValue >= dps[i].x - xSnapDistance, 'first')
           //console.log(Number(xValue) == 0, 'trueeeeeeeeee')
           // console.log(xValue, dps[i].x, xSnapDistance, 'x debug')
           // console.log(yValue, dps[i].y[1], ySnapDistance, 'y debug')
           // console.log(yValue >= dps[i].y[1] - ySnapDistance, 'y first')
           // console.log(yValue <= dps[i].y[1] + ySnapDistance, 'y second')
           // //console.log(xValue <= dps[i].x + xSnapDistance, 'second')
           // console.log(Number(dps[i].x) == 0, 'x comp')
           // console.log(Number(dps[i].x), 'x value')
           // console.log(Math.round(polyLineDistance), 'polyLineDistance')
           // if((Number(xValue) == 0 || (Number(dps[i].x) == 0) || Number(dps[i].x) == Math.round(polyLineDistance) )&& (yValue >= dps[i].y[1] - ySnapDistance && yValue <= dps[i].y[1] + ySnapDistance)) {
            if(yValue >= dps[i].y[1] - ySnapDistance && yValue <= dps[i].y[1] + ySnapDistance) {
              // console.log('intraa')
               if(mouseDown) {
                  selected = i
                  break;
               }
               else {
                  changeCursor = true;
                  break;
               }
            } else {
               selected = null;
               changeCursor = false;
            }
         }

      }
      jQuery("#chartContainer > .canvasjs-chart-container").on({
         mousedown: function(e) {
            mouseDown = true;
            getPosition(e);
            searchDataPoint();
         },
         mousemove: function(e) {
            getPosition(e);
            if(mouseDown) {
               clearTimeout(timerId);
               timerId = setTimeout(function(){
                  if(selected != null) {
                     chart.data[6].dataPoints[selected].y[1] = yValue;

                     chart.data[6].dataPoints[selected].color = '#70db37';
                     //[OLD]
                     // let towerghtMM = parseFloat(chart.data[6].dataPoints[selected].y[1] - chart.data[6].dataPoints[selected].y[0]).toFixed(1);
                     let towerghtMM = Math.round(chart.data[6].dataPoints[selected].y[1] - chart.data[6].dataPoints[selected].y[0]).toFixed(1);
                     // console.log(chart.axisX[0], 'chat.axisX.crosshair')
                     // console.log(chart.data[6].dataSeries, 'dataSeries')
                     for (let i = 0; i < chart.data.length; i++) {
                        chart.data[i].set('toolTipContent', null)
                     }
                     chart.data[6].dataPoints[selected].height = towerghtMM;
                     // disable crosshair
                     chart.axisX[0].crosshair.set('enabled', false);
                     // set tooltip content
                     chart.toolTip.set('content', function() {
                        return towerghtMM + ' m'
                     })
                     // set canvas tower height
                     setTowersHeight(selected, towerghtMM)
                    //rebuildFresnelZone(leftKeyPoint, rightKeyPoint, chart.data[6].dataPoints[selected].pointKey)
                    //drawElevationProfile(window.polylineElevations, window.elevationPointsRight, window.elevationPointsLeft, window.firstTowerHeight, window.secondTowerHeight, leftKeyPoint, rightKeyPoint)
                     //chart.render();
                     addMarkerImages(chart);
                  }
               }, 0);
            }
            else {
               searchDataPoint();
               if(changeCursor) {
                  chart.data[6].set("cursor", "n-resize");
               } else {
                  chart.data[6].set("cursor", "default");
               }
            }
         },
         mouseup: function(e) {
            if(selected != null) {
               injectLoadingElement(graphBox)
               chart.data[6].dataPoints[selected].y[1] = yValue;
               chart.data[6].dataPoints[selected].color = '#70db37';
               let towerghtMU = parseFloat(chart.data[6].dataPoints[selected].y[1] - chart.data[6].dataPoints[selected].y[0]).toFixed(1);
               //setTowersHeight(selected, towerghtMU)
               rebuildFresnelZone(leftKeyPoint, rightKeyPoint, chart.data[6].dataPoints[selected].pointKey)
               mouseDown = false;
               setTimeout( () => {
                  removeLoadingElement(graphBox)
               }, 1000)
            }
         }
      });

      function setTowersHeight(towerIndex, yValue) {
         if (towerIndex == 0 || towerIndex % 2 == 0) {
            window.firstTowerHeight = yValue;
         }

         // set tower2 height
         if (towerIndex == 1 || towerIndex % 2 != 0) {
            window.secondTowerHeight = yValue;
         }
      }
      var customMarkers = [];

      addMarkerImages(chart);

      function addMarkerImages(chart) {
         for (var i = 0; i < chart.data[6].dataPoints.length; i++) {
            let graphNodeIndex = chart.data[6].dataPoints[i].index
            customMarkers.push($("<p>")
            .html(`${graphNodeIndex}`)
            .css("display", "none")
            .addClass('graph-node-index')
            .attr("data-tower-height", chart.data[6].dataPoints[i].y[1] - chart.data[6].dataPoints[i].y[0])
            .attr("data-tower-index", `${graphNodeIndex}`)
            .appendTo($("#chartContainer>.canvasjs-chart-container"))
         );
         positionMarkerImage(customMarkers[i], i);
      }
   }

   // new func [ START ]
   function rebuildFresnelZone(leftKeyPoint, rightKeyPoint, selectedPointKey) {
      // if( nodeheightEl )
      mapData.points[leftKeyPoint].settings.nodeheight = Number(window.firstTowerHeight) || mapData.points[leftKeyPoint].settings.nodeheight
      mapData.points[rightKeyPoint].settings.nodeheight = Number(window.secondTowerHeight) || mapData.points[rightKeyPoint].settings.nodeheight

      if( parseInt(selectedPointKey) === parseInt(globalSelectedPointKey) ) {
         nodeheightEl.value = mapData.points[globalSelectedPointKey].settings.nodeheight
      }

      globalSelectedPointKey = selectedPointKey
      for (let l = 0; l < mapData.points.length; l++) {
         mapData.points[l].settings.active = l === selectedPointKey;
      }


      selectedNode = mapData.points[selectedPointKey].settings
      heatmapState.checked = mapData.points[selectedPointKey].settings.heatmap_state
      renderNodeSettings(selectedPointKey)

      nodeSettingsBox.classList.remove('hidden')
      meshDataView.classList.add('mesh-data-active')
      generateMapMesh()
      drawElevationProfile(window.polylineElevations, window.elevationPointsRight, window.elevationPointsLeft, window.firstTowerHeight, window.secondTowerHeight, leftKeyPoint, rightKeyPoint)
   }

   function positionMarkerImage(customMarker, index) {

      if (chart.options.data[6].dataPoints[index] && tower1Height > 0 && tower2Height > 0) {
         var pixelX = chart.axisX[0].convertValueToPixel(chart.options.data[6].dataPoints[index].x);
         var pixelY = chart.axisY[0].convertValueToPixel(chart.options.data[6].dataPoints[index].y[1]);


         customMarker.css({
            "position": "absolute",
            "display": "flex",
            "top": pixelY - customMarker.height() / 2 - 12,
            "left": pixelX - customMarker.width() / 2
         });
      }
   }
}





let markersToBeDeleted = [];
function showPointsCrosshair(e) {

   if (markersToBeDeleted.length) {
      for (let i = 0; i < markersToBeDeleted.length; i++) {
         map.removeLayer(markersToBeDeleted[i])
      }
   }
   let xDistance = e.value;
   let latLng = polylineElevPoints.find(coord => coord.elev_graph_x == xDistance);
   if (latLng) {
      hoverPositionElement = L.circle([latLng.lat, latLng.lng], {radius: 3})
      hoverPositionElement.setStyle({className: 'lineHoverElement'})
      hoverPositionElement.addTo(map)
      markersToBeDeleted.push(hoverPositionElement);
   }
}
// [LFA] CODE REFACTORING START

$('.nav-tabs .view-mode-tab-controller a').click(function (e) {
   //get selected href
   var href = $(this).attr('href');

   //set all nav tabs to inactive
   $('.nav-tabs .view-mode-tab-controller').removeClass('active');

   //get all nav tabs matching the href and set to active
   $('.nav-tabs .view-mode-tab-controller a[href="'+href+'"]').closest('.view-mode-tab-controller').addClass('active');

   //active tab
   $('.tab-pane').removeClass('active');
   $('.tab-pane' + href).addClass('active');
   $('.tab-pane' + href).addClass('fade-in');
   if(href === '#settingsMode') {
      $('#viewModeTag').html('Settings')
   }
   if(href === '#dataMode') {
      $('#viewModeTag').html('Data')
   }
   if(href === '#financeMode') {
      $('#viewModeTag').html('Finance')
   }
})


let plansDropdownBtn = document.getElementById("meshPlansBtn");
let settingsDropdownBtn = document.getElementById("meshSettingsBtn");
let viewModeBtn = document.getElementById("viewModeBtn");
let plansDropdown = document.getElementById("meshPlansDropdown");
let settingsDropdown = document.getElementById("meshSettingsDropdown");
let viewModeDropdown = document.getElementById("viewModeDropdown");

viewModeBtn.addEventListener("click", function () {
   viewModeDropdown.classList.toggle("show");
});

plansDropdownBtn.addEventListener("click", function () {
   plansDropdown.classList.toggle("show");
});

settingsDropdownBtn.addEventListener("click", function () {
   settingsDropdown.classList.toggle("show");
});

window.addEventListener("click", function (event) {
   if (!event.target.matches('#meshPlansBtn')) {
      plansDropdown.classList.remove("show");
   }
});

window.addEventListener("click", function (event) {
   if (!event.target.matches('#meshSettingsBtn')) {
      settingsDropdown.classList.remove("show");
   }
});

window.addEventListener("click", function (event) {
   if (!event.target.matches('#viewModeBtn')) {
      viewModeDropdown.classList.remove("show");
   }
});

let advancedView = document.getElementById('toggleAdvanced');
let basicView = document.getElementById('toggleBasic');
let advancedList = document.getElementById('advancedSettings')
let advancedHeatElements = document.getElementById('heatAdvancedElements')
let meshTag = document.getElementById('meshPlannerTag')

   advancedView.addEventListener('click', function(){
      advancedList.classList.toggle('show')
      advancedHeatElements.classList.toggle('show')
      meshTag.innerHTML = 'Advanced'
      mapData.project.advancedMenuState = true
   })


   basicView.addEventListener('click', function(){
      advancedList.classList.remove('show')
      advancedHeatElements.classList.remove('show')
      meshTag.innerHTML = 'Basic'
      mapData.project.advancedMenuState = false
   })


if(mapData.project.advancedMenuState) {
   advancedList.classList.toggle('show')
   advancedHeatElements.classList.toggle('show')
   meshTag.innerHTML = 'Advanced'
} else {
   advancedList.classList.remove('show')
   advancedHeatElements.classList.remove('show')
   meshTag.innerHTML = 'Basic'
}


// touch event console error solution

(function () {
   if (typeof EventTarget !== "undefined") {
      let func = EventTarget.prototype.addEventListener;
      EventTarget.prototype.addEventListener = function (type, fn, capture) {
         this.func = func;
         if(typeof capture !== "boolean"){
            capture = capture || {};
            capture.passive = false;
         }
         this.func(type, fn, capture);
      };
   };
}());


const getRange = (start, stop, step = 1) => {
   range = [...Array(stop - start).keys()]
   .filter(i => !(i % Math.round(step)))
   .map(v => start + v)

   return range
}

function degToRad(degree) {
   return degree * Math.PI / 180;
}

function radToDeg(radians) {
   return radians * 180 / Math.PI;
}

function getLineLength(lat1, lng1, lat2, lng2) {
   var lat1rads, lat2rads, deltaLat, lat2rads, deltaLng,
   a, c, R;

   // Avoid to return NAN, if finding distance between same lat long.
   if (lat1 == lat2 && lng1 == lng2) {
      return 0;
   }

   //Earth Radius (in metre)
   R = 6371000

   lat1rads = degToRad(lat1)
   lat2rads = degToRad(lat2)
   deltaLat = degToRad((lat2 - lat1))
   deltaLng = degToRad((lng2 - lng1))

   a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
   Math.cos(lat1rads) * Math.cos(lat2rads) * Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2)
   c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

   distance_in_meters = R * c;

   if (isNaN(distance_in_meters)) {
      return 0;
   }
   return distance_in_meters
}


function getBearing(lat1, lng1, lat2, lng2) {
   var startLat, startLong, endLat, endLong, dLong, dPhi;

   startLat = degToRad(lat1)
   startLong = degToRad(lng1)
   endLat = degToRad(lat2)
   endLong = degToRad(lng2)

   dLong = endLong - startLong
   dPhi = Math.log(Math.tan(endLat / 2.0 + Math.PI / 4.0) / Math.tan(startLat / 2.0 + Math.PI / 4.0));

   if (Math.abs(dLong) > Math.PI) {
      if (dLong > 0) {
         dLong = -(2.0 * Math.PI - dLong)
      } else {
         dLong = (2.0 * Math.PI + dLong)
      }
   }

   bearing = (radToDeg(Math.atan2(dLong, dPhi)) + 360.0) % 360.0;

   return bearing;
}

// getBearing(pointC.lat, pointC.lng, pointD.lat, pointD.lng)

function getDestinationCoords(lat, lng, azimuth, distance_metre) {
   var lat2, lng2, R, brng, d_km, lat1, lng1;

   R = 6378.1 //Radius of the Earth in km

   //Bearing is degrees converted to radians.
   brng = degToRad(azimuth);
   d_km = distance_metre / 1000;
   lat1 = degToRad(lat)
   lng1 = degToRad(lng)

   lat2 = Math.asin(Math.sin(lat1) * Math.cos(d_km / R) +
   Math.cos(lat1) * Math.sin(d_km / R) * Math.cos(brng))
   lng2 = lng1 +
   Math.atan2(
      Math.sin(brng) * Math.sin(d_km / R) * Math.cos(lat1),
      Math.cos(d_km / R) - Math.sin(lat1) * Math.sin(lat2));

      //convert back to degrees
      lat2 = radToDeg(lat2)
      lng2 = radToDeg(lng2)
      destination_point = [parseFloat(lat2.toFixed(15)), parseFloat(lng2.toFixed(15))]
      return destination_point
   }

   // getDestinationCoords(pointC.lat, pointC.lng, bearing, distance_in_meters)

   function constructCoordinates(interval, azimuth, lat1, lng1, lat2, lng2) {
      var d, dist, counter, range_list, coord, interval_points_coords;

      d = getLineLength(lat1, lng1, lat2, lng2);

      dist = parseInt(d / interval);
      counter = parseFloat(interval)
      interval_points_coords = [];
      // interval_points_coords.push([lat1, lng1]);

      interval_points_coords.push({
         lat: lat1,
         lng: lng1,
      })
      range_list = getRange(0, dist);
      for (var key in range_list) {
         coord = getDestinationCoords(lat1, lng1, azimuth, counter)
         counter = counter + parseFloat(interval)
         // interval_points_coords.push(coord);
         interval_points_coords.push({
            lat: coord[0],
            lng: coord[1],
         })
      }

      // interval_points_coords.push([lat2, lng2])
      interval_points_coords.push({
         lat: lat2,
         lng: lng2,
      })
      return interval_points_coords;
   }

   // constructCoordinates(interval_step, bearing, pointC.lat, pointC.lng, pointD.lat, pointD.lng)

   function getPointsCoordinates(lat1, lng1, lat2, lng2, interval_meters) {
      var azimuth, coords;

      // point interval in meters
      if (!interval_meters) {
         interval_meters = 20.0
      }

      azimuth = getBearing(lat1, lng1, lat2, lng2)
      coords = constructCoordinates(interval_meters, azimuth, lat1, lng1, lat2, lng2)

      coordsList = coords
      return coordsList
   }

   // getPointsCoordinates(pointC.lat, pointC.lng, pointD.lat, pointD.lng, interval_step)

   // function removeElement() {
   //    loadingFixed.classList.add('pin-active')
   //    for(let rm = 0; rm < mapData.lines.length; rm++) {
   //       if
   //       (
   //          mapData.lines[rm].active
   //          &&
   //          (
   //             mapData.points[globalSelectedPointKey].settings.lat === mapData.lines[rm].start.lat && mapData.points[globalSelectedPointKey].settings.lng === mapData.lines[rm].start.lng
   //             ||
   //             mapData.points[globalSelectedPointKey].settings.lat === mapData.lines[rm].stop.lat && mapData.points[globalSelectedPointKey].settings.lng === mapData.lines[rm].stop.lng
   //          )
   //       )
   //       {
   //          globalGeneratedGraphLine = null
   //          generatedGraphLine = null
   //          $('#chartContainer').hide()
   //          $('#emptyGraphBoxMessage').removeClass('hidden')
   //       }
   //    }
   //    if(mapData.points[globalSelectedPointKey].settings.heatmap_state = true) {
   //       generatedHeatmapNode = null
   //       globalGeneratedHeatmapNode = null
   //       sectorBearingsArr = []
   //    }
   //    setTimeout( async () => {
   //       mapData.points[globalSelectedPointKey].settings.deleted = true
   //       mapData.points[globalSelectedPointKey].settings.heatmap_state = false
   //       meshNodesCount.innerHTML = mapData.points.length - 1
   //       nodeSettingsBox.classList.add('hidden')
   //       nodeBoxMessage.classList.remove('hidden')
   //       await clearMapDataLines(mapData.points[globalSelectedPointKey].settings.lat, mapData.points[globalSelectedPointKey].settings.lng)
   //       generateMapMesh()
   //    }, 100)
   // }


   function removePOI() {
      loadingFixed.classList.add('pin-active')
      setTimeout( async () => {
         localPoiArr[globalPOIKey].deleted = true
         // nodeSettingsBox.classList.add('hidden')
         pointOfInterestSettings.classList.add('hidden')
         nodeBoxMessage.classList.remove('hidden')
         generateMapMesh()
      }, 100)
   }

    let tempPoiArr = []

   function savePOI() {
      for (let i in localPoiArr) {
         if (mapData.poi[i] && localPoiArr[i]) {
            localPoiArr[i].modified = false;
            mapData.poi[i].modified = false;
         }

         if( (!mapData.poi[i] && JSON.stringify(mapData.poi[i]) !==  JSON.stringify(localPoiArr[i]) ) || ( JSON.stringify(mapData.poi[i]) !==  JSON.stringify(localPoiArr[i]) )  || ( localPoiArr[i].u_id == null && mapData.poi[i].u_id == null) ){
            tempPoiArr.push(localPoiArr[i])
         }
      }
      mapData.poi = tempPoiArr;
      tempPoiArr = [];
   }


   function clearMap() {

      // heatmapLines = []
      sectorHeatpoints = []
      pointsElevationRange = []
      range1 =[]
      range2 =[]
      range3 =[]
      for(i in map._layers) {
         if(map._layers[i]._path != undefined || map._layers[i]._url === undefined) {
            try {
               map.removeLayer(map._layers[i]);
            }
            catch(e) {
               console.log("problem with " + e + map._layers[i]);
            }
         }
      }
   }

   // heat area handler fn

   function createHeatHandle(node_settings) {
      let updatedBearingLineCoords
      let updatedBearingHandleCoords
      let newSectorBearing
      let bearingHandleCoords = getDestinationCoords(node_settings.defaultLat, node_settings.defaultLng, node_settings.heatmap_bearing ,node_settings.heatmap_area)
      let bearingHandle = L.marker(bearingHandleCoords, {icon: bearingHandleIcon, draggable: true}).addTo(map)
      let bearingLine = L.polyline([[node_settings.defaultLat, node_settings.defaultLng], bearingHandleCoords], {className: 'bearingLineStyle'}).addTo(map)
      let bearingLineLength = getLineLength(node_settings.defaultLat, node_settings.defaultLng, bearingHandle._latlng.lat, bearingHandle._latlng.lng)
      bearingHandle.options.marker = node_settings

      bearingHandle.addEventListener('dragstart', (e) => {
         if(e.target.options.marker.heatmap_state !== selectedMarker.options.heatmap_state){
            $('#heatmapNotice').modal('show')
         }
         sectorBearingsArr = []
         if(bearingLine){
            updatedBearingLineCoords = bearingLine.getLatLngs();
            updatedBearingHandleCoords = bearingHandle.getLatLng();

            for (let i = 0; i < updatedBearingLineCoords.length; i++) {
               if (updatedBearingHandleCoords.equals(updatedBearingLineCoords[i])) {
                  bearingHandle.storedBearingLineEnd = i;
               }
            }
         }
      })

      bearingHandle.addEventListener('drag', (e) => {
         if(bearingLine){
            updatedBearingLineCoords = bearingLine.getLatLngs(),
            updatedBearingHandleCoords = bearingHandle.getLatLng();
            updatedBearingLineCoords.splice(bearingHandle.storedBearingLineEnd, 1, updatedBearingHandleCoords);
            bearingLine.setLatLngs(updatedBearingLineCoords);


            newSectorBearing = getBearing(node_settings.defaultLat, node_settings.defaultLng, updatedBearingLineCoords[1].lat, updatedBearingLineCoords[1].lng)
            markerHeatArea.setSector(newSectorBearing, node_settings.heatmap_angle);
         }
      })

      bearingHandle.addEventListener('dragend', (e) => {
         loadingFixed.classList.add('pin-active')
         setTimeout( () => {
            bearingLine = L.polyline([[node_settings.defaultLat, node_settings.defaultLng], [bearingHandle._latlng.lat, bearingHandle._latlng.lng]])
            newSectorBearing = getBearing(node_settings.defaultLat, node_settings.defaultLng, bearingLine._latlngs[1].lat, bearingLine._latlngs[1].lng)
            mapData.points[globalSelectedPointKey].settings.heatmap_bearing = newSectorBearing
            generateMapMesh()
         }, 100)
      })
   }

   function setHeatmapSector(node_settings){
      markerHeatArea = L.circle([node_settings.defaultLat, node_settings.defaultLng], {
         radius: node_settings.heatmap_area
      }).setSector(node_settings.heatmap_bearing, node_settings.heatmap_angle);  //direction, angle

      markerHeatArea.setStyle({className: 'sectorHeatArea'})
      node_settings.heatmap_start_angle = markerHeatArea.options.startAngle;
      node_settings.heatmap_end_angle = markerHeatArea.options.endAngle;
      markerHeatArea.addTo(map)

      createHeatHandle(node_settings)
   }

   function pythagorean(sideA, sideB){
      return Math.sqrt(Math.pow(sideA, 2) + Math.pow(sideB, 2));
   }

   function calcWavelength(node_settings) {
      waveLength = SPEED_OF_LIGHT / ((node_settings.node_frequency / 1000) * 1000000000)
   }

   let halfWaveLength = waveLength / 2;


   function calcFresnelZoneRadius(lineOfSight) {
      fresnelRadius = 0.5 * Math.sqrt(lineOfSight * waveLength + Math.pow(waveLength, 2) / 4);
   }


   function treshold60(lineOfSight, fresnelRadius) {
      treshold_60 = Math.sqrt(1.44 * Math.pow(fresnelRadius, 2) + Math.pow(lineOfSight, 2)) - lineOfSight;
   }


   function treshold80(lineOfSight, fresnelRadius) {
      treshold_80 = Math.sqrt(2.56 * Math.pow(fresnelRadius, 2) + Math.pow(lineOfSight, 2)) - lineOfSight;
   }

   function treshold100(lineOfSight, fresnelRadius) {
      treshold_100 = waveLength / 2;
   }

   function calcLineOfSight(distance, tower1Height, tower1Elevation, tower2Height, tower2Elevation, node_settings, receiverPower) {
      lineOfSight = Math.sqrt(Math.pow(distance, 2) + Math.pow(tower1Height + tower1Elevation - tower2Height - tower2Elevation, 2));
    // receiverPower = node_settings.transmit_power + node_settings.gain - node_settings.cable_loss + node_settings.pair_antenna_gain - node_settings.pair_antenna_cable_loss - node_settings.other_loss - 20 * Math.log( 4 * Math.PI * lineOfSight * (RFfrequency / SPEED_OF_LIGHT) )
     //lineOfSight = (SPEED_OF_LIGHT * Math.pow(10, (node_settings.transmit_power + node_settings.gain - node_settings.cable_loss - (-64) - node_settings.other_loss +  node_settings.pair_antenna_gain - node_settings.pair_antenna_cable_loss)/ 20)) / (4 * Math.PI * RFfrequency * 1000000000)
      calcWavelength(node_settings)
      calcFresnelZoneRadius(lineOfSight)
      treshold60(lineOfSight, fresnelRadius)
      treshold80(lineOfSight, fresnelRadius)
      treshold100(lineOfSight, fresnelRadius)
   }

   function checkSignalStrength(node_settings, lineOfSight) {
     let cf = (((node_settings.node_frequency / 1000) * 1000000000) / SPEED_OF_LIGHT);
     let tmplogin = 4 * Math.PI * lineOfSight * cf;
     let tmplog = Math.log10(tmplogin);
      let lfs =  20 * tmplog;

      receiverPower = node_settings.transmit_power +
        node_settings.gain -
        node_settings.cable_loss +
        node_settings.pair_antenna_gain -
        node_settings.pair_antenna_cable_loss -
        node_settings.other_loss - lfs

      return receiverPower;
   }

   let heatmapLines = []
   let heatmapColors = {
      0: 'bad',
      1: 'magenta',
      2: 'blue',
      3: 'light-blue',
   }

   let RFSignalBounds = {
      80: {no_color: -82, magenta: -72, blue: -62},
      40: {no_color: -87, magenta: -77, blue: -67}
   }

   async function calcSectorElevationHeat(node_settings) {
      loadingFixed.classList.add('pin-active')
      heatmapLines = []
      sectorBearingsArr = []
      for( let nBrng = node_settings.heatmap_start_angle; nBrng <= node_settings.heatmap_end_angle; nBrng += parseFloat(node_settings.heatmap_bearing_pitch)) {
         let destinationCoords = getDestinationCoords(node_settings.defaultLat, node_settings.defaultLng, nBrng, node_settings.heatmap_area)
         let heatLine = L.polyline([[node_settings.defaultLat, node_settings.defaultLng], destinationCoords])
         heatmapLines.push(heatLine)
         sectorBearingsArr.push(getBearing(node_settings.defaultLat, node_settings.defaultLng, heatLine._latlngs[1].lat, heatLine._latlngs[1].lng))
      }
      let singleHeatlinePointsList  = [];
      for( let hln = 0; hln <  heatmapLines.length; hln++) {
         heatmapLines[hln].points = getPointsCoordinates(heatmapLines[hln]._latlngs[0].lat, heatmapLines[hln]._latlngs[0].lng, heatmapLines[hln]._latlngs[1].lat, heatmapLines[hln]._latlngs[1].lng, node_settings.heatmap_line_step)

         singleHeatlinePointsList = heatmapLines[hln].points
         for ( let heatpoint = 0; heatpoint < singleHeatlinePointsList.length; heatpoint++) {
            let reflectionsColorIndexes = []


            let elev_results = await Topography.getTopography(singleHeatlinePointsList[heatpoint], topographyOptions)
            if(elev_results && elev_results.elevation) {
               singleHeatlinePointsList[heatpoint].elevation = elev_results.elevation
            }

            if (heatpoint == 0) {
               continue;
            }

            let finalColor = 3

            if (heatpoint == 1  && node_settings.heatmap_line_step <= 5) {
               continue;
            }


            calcLineOfSight(node_settings.heatmap_line_step * heatpoint, node_settings.nodeheight, singleHeatlinePointsList[0].elevation, node_settings.pair_antenna_height, singleHeatlinePointsList[heatpoint].elevation, node_settings, receiverPower)

            let tempLineOfSight = lineOfSight;
            let tempWaveLength = waveLength;
            let tempFresnelRadius = fresnelRadius;
            let tempTreshold60 = treshold_60;
            let tempTreshold80 = treshold_80;
            let tempTreshold100 = treshold_100;



            for (let rangeIndex = 1; rangeIndex < heatpoint; rangeIndex++) {
               // this calculation is for fresenel zone
               let node1_to_point_distance = rangeIndex  * node_settings.heatmap_line_step
               let antenna1_to_point_distance = pythagorean(node_settings.nodeheight + singleHeatlinePointsList[0].elevation - singleHeatlinePointsList[rangeIndex].elevation, node1_to_point_distance)
               let node2_to_point_distance = (heatpoint - rangeIndex)  * node_settings.heatmap_line_step
               let antenna2_to_point_distance = pythagorean(node_settings.pair_antenna_height + singleHeatlinePointsList[heatpoint].elevation - singleHeatlinePointsList[rangeIndex].elevation, node2_to_point_distance)


               let reflected_RFPath =  antenna1_to_point_distance + antenna2_to_point_distance
               let tresholdResult = reflected_RFPath - tempLineOfSight;

               if (tresholdResult < tempTreshold60) {
                  finalColor = 0;
                  break;
               } else if ( tempTreshold60 <= tresholdResult && tresholdResult <=  tempTreshold80 ) {
                  finalColor = Math.min(finalColor, 1)
               } else if (tempTreshold80 <= tresholdResult && tresholdResult <= tempTreshold100) {
                  finalColor = Math.min(finalColor, 2)
               }
            }


            let tempReceiverPower = checkSignalStrength(node_settings, tempLineOfSight)

            let toCompare = RFSignalBounds[node_settings.band_width]
            if (tempReceiverPower < toCompare.no_color) {
               finalColor = 0;
            } else if ( toCompare.no_color <= tempReceiverPower && tempReceiverPower <=  toCompare.magenta) {
               finalColor = Math.min(finalColor, 1)
            } else if (toCompare.magenta <= tempReceiverPower && tempReceiverPower <= toCompare.blue) {
               finalColor = Math.min(finalColor, 2)
            } else {
               finalColor = Math.min(finalColor, 3)
            }

            heatmapLines[hln].points[heatpoint].final_color = finalColor;
         }
      }
   }


   function setHeatLayer() {
      for(let dot = 0; dot < heatmapLines.length; dot++){
         let pointHeatLayer = heatmapLines[dot].points

         for(point in pointHeatLayer){
            if(pointHeatLayer[point].final_color == 3){
               range1.push([pointHeatLayer[point]])
            }

            if(pointHeatLayer[point].final_color == 2){
               range2.push([pointHeatLayer[point]])
            }

            if(pointHeatLayer[point].final_color == 1){
               range3.push([pointHeatLayer[point]])
            }
         }
      }

      elevationRange1 = range1.map(function(h){
         return [h[0].lat, h[0].lng]

      })
      elevationRange2 = range2.map(function(h){
         return [h[0].lat, h[0].lng]
      })
      elevationRange3 = range3.map(function(h){
         return [h[0].lat, h[0].lng]
      })


      heatRange1 = L.heatLayer(elevationRange1)
      heatRange1.setOptions({
         radius: 13,
         // radius: getRadius(),
         max: 1,
         gradient: {
            1: '#60b5d7',
         },
         blur: 40,
         minOpacity: 1,
         maxZoom: 1,
      });

      //
      heatRange2 = L.heatLayer(elevationRange2)
      heatRange2.setOptions({
         radius: 13,
         // radius: getRadius(),
         max: 1,
         gradient: {
            1: 'rgba(0, 93, 255, .1)',
         },
         blur: 50,
         minOpacity: 1,
         maxZoom: 1,
      });


      heatRange3 = L.heatLayer(elevationRange3)
      heatRange3.setOptions({
         radius: 13,
         // radius: getRadius(),
         max: 1,
         gradient: {
            1: 'rgba(200, 0, 133, .1)',
         },
         blur: 50,
         minOpacity: 1,
         maxZoom: 1,
      });

      heatRange1.addTo(map)
      heatRange2.addTo(map)
      heatRange3.addTo(map)
      loadingFixed.classList.remove('pin-active')
   }

   function mouseX(evt) {
      if (evt.pageX) {
         return evt.pageX;
      } else if (evt.clientX) {
         return evt.clientX + (document.documentElement.scrollLeft ?
            document.documentElement.scrollLeft :
            document.body.scrollLeft);
         } else {
            return null;
         }
      }

      function mouseY(evt) {
         if (evt.pageY) {
            return evt.pageY;
         } else if (evt.clientY) {
            return evt.clientY + (document.documentElement.scrollTop ?
               document.documentElement.scrollTop :
               document.body.scrollTop);
            } else {
               return null;
            }
         }

         function injectLoadingElement(element) {
            // element.style.position = 'relative'
            // element.style.overflow = 'hidden'
            element.innerHTML += `
            <div class="loading-screen-pin" id="loadingScreenPin">
            <div class="pin"></div>
            <div class="pulse"></div>
            </div>
            `
         }

         function removeLoadingElement(element) {
            if(element.querySelector('.loading-screen-pin')){
               element.querySelector('.loading-screen-pin').remove()
            }
         }



         window.onload = initDivMouseOver;
         function initDivMouseOver()   {
            var md = document.getElementById("meshData");
            md.mouseIsOver = false;
            md.onmouseover = function()   {
               this.mouseIsOver = true;
               map.dragging.disable();
               map.touchZoom.disable();
               map.doubleClickZoom.disable();
               map.scrollWheelZoom.disable();
               map.keyboard.disable();
               md.style.cursor = "default"
            }
            md.onmouseout = function()   {
               this.mouseIsOver = false;
               map.dragging.enable();
               map.touchZoom.enable();
               map.doubleClickZoom.enable();
               map.scrollWheelZoom.enable();
               map.keyboard.enable();
               md.style.cursor = "grab"
            }
         }

         function toggleMapType() {
            let mapTypeText = document.getElementById('mapTypeTitle')
            if(map.hasLayer(darkMode)){
               mapTypeText.innerHTML = 'Map'
               mapData.project.mapType = 2
               map.addLayer(streetsMode);
               map.removeLayer(darkMode);
            } else {
               mapTypeText.innerHTML = 'Satelite'
               map.addLayer(darkMode);
               mapData.project.mapType = 1
               map.removeLayer(streetsMode);
            }
         }


         function updateNodeSettings(node) {
            let postObject = {
               points: []
            }
            postObject.points.push(node)
            updateNodeSettingsAjax(postObject);
         }

         function updateLineSettings(line) {
            let postObject = {
               lines: []
            }

            if(Array.isArray(line)) {
               line.map(line => postObject.lines.push(line))
            } else {
               postObject.lines.push(line)
            }

            sendReqAjax(true, postObject);
         }

         async function changeNodeHeight() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               globalGeneratedGraphLine = null
               generatedGraphLine = null
               mapData.points[globalSelectedPointKey].settings.nodeheight = parseInt(nodeheightEl.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeLatitude() {
            loadingFixed.classList.add('pin-active')
            clearMapDataLines(mapData.points[globalSelectedPointKey].settings.lat, mapData.points[globalSelectedPointKey].settings.lng)

            setTimeout( () => {
               globalGeneratedGraphLine = null
               generatedGraphLine = null
               mapData.points[globalSelectedPointKey].settings.lat = parseFloat(nodeLatitudeEl.value)
               // generateNodeLines(mapData.points[globalSelectedPointKey].settings)
               generateMapMesh();
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeLongitude() {
            loadingFixed.classList.add('pin-active')
            clearMapDataLines(mapData.points[globalSelectedPointKey].settings.lat, mapData.points[globalSelectedPointKey].settings.lng)
            setTimeout( () => {
               globalGeneratedGraphLine = null
               generatedGraphLine = null
               mapData.points[globalSelectedPointKey].settings.lng = parseFloat(nodeLongitudeEl.value)
               // generateNodeLines(mapData.points[globalSelectedPointKey].settings)
               generateMapMesh();
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeRadius() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.radius = parseInt(nodedistanceEl.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
               if (mapData.project.cluster_status) {
                  getNodeClusterInfo(mapData.points[globalSelectedPointKey].settings.slug).then(() => {
                     clusterPoints = window.clusterInfo.points
                     clusterLines = window.clusterInfo.lines

                     highlightClusterNodes(clusterPoints, null, mapMarkers)
                     highlightClusterLines(clusterLines, mapLines)

                     // rebuildMapDataLinesClusterInfo().then(() => {
                     //    rebuildMapDataPointsClusterInfo()
                     // })


                     meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                     meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                     // window.clusterInfo = []

                     loadingFixed.classList.remove('pin-active')
                  });
               }
            }, 500)
         }

         function changeHeatArea() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.heatmap_area = parseInt(heatMapAreaSector.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }
         function changeHeatAngle() {
            sectorBearingsArr = []
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(heatMapAreaAngle.value !== 'custom_value')  {
                  customValueField.value = ''
                  customValueField.classList.add('hidden')
                  mapData.points[globalSelectedPointKey].settings.heatmap_angle = parseInt(heatMapAreaAngle.value)
               } else {
                  customValueField.classList.remove('hidden')
               }
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeCustomHeatAngle() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(parseInt(customValueField.value) > 360) customValueField.value = 360
               if(parseInt(customValueField.value) < 1) customValueField.value = 1
               mapData.points[globalSelectedPointKey].settings.heatmap_angle = parseInt(customValueField.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeHeatBearing() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(heatMapAreaBearing.value > 360) heatMapAreaBearing.value = 360
               mapData.points[globalSelectedPointKey].settings.heatmap_bearing = parseInt(heatMapAreaBearing.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeHeatBearingPitch() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(heatMapAreaBearingPitch.value > 360) heatMapAreaBearingPitch.value = 360
               mapData.points[globalSelectedPointKey].settings.heatmap_bearing_pitch = heatMapAreaBearingPitch.value
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeHeatLineStep() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(heatmapLineStep.value <  2) heatmapLineStep.value = 2
               mapData.points[globalSelectedPointKey].settings.heatmap_line_step = parseInt(heatmapLineStep.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeHeatmapState() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               for(let i in mapData.points) {
                  if(i != globalSelectedPointKey) {
                     mapData.points[i].settings.heatmap_state = false
                  } else {
                     mapData.points[i].settings.heatmap_state = true
                  }
               }
               if(heatmapState.checked){
                  mapData.points[globalSelectedPointKey].settings.heatmap_state = true
               }  else {
                  mapData.points[globalSelectedPointKey].settings.heatmap_state = false
                  sectorBearingsArr = []
                  globalGeneratedHeatmapNode = null
                  generatedHeatmapNode = null
               }
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }



         function changeNodeTransmitPower() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.transmit_power = parseInt(nodeTransmitPower.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeGain() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.gain = parseInt(nodeGain.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeCableLoss() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(nodeCableLoss.value < 0 ) nodeCableLoss.value = 0
               mapData.points[globalSelectedPointKey].settings.cable_loss = parseInt(nodeCableLoss.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeOtherLoss() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.other_loss = parseInt(nodeOtherLoss.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }


         function changePairAntennaHeight() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(secondAntennaHeight.value < 1) secondAntennaHeight.value = 10
               mapData.points[globalSelectedPointKey].settings.pair_antenna_height = parseInt(secondAntennaHeight.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changePairAntennaGain() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(secondNodeGain.value < 1 ) secondNodeGain.value = 1
               mapData.points[globalSelectedPointKey].settings.pair_antenna_gain = parseInt(secondNodeGain.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changePairAntennaCableLoss() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(secondNodeCableLoss.value < 0 ) secondNodeCableLoss.value = 0
               mapData.points[globalSelectedPointKey].settings.pair_antenna_cable_loss = parseInt(secondNodeCableLoss.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeFrequency() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.node_frequency = parseInt(nodeFrequency.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeBandWidth() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               mapData.points[globalSelectedPointKey].settings.band_width = parseInt(bandWidth.value)
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function changeNodeBackhaulState() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               // for(let i in mapData.points) {
               //    if(i != globalSelectedPointKey) {
               //       mapData.points[i].settings.backhaul_status = false
               //    } else {
               //       mapData.points[i].settings.backhaul_status = true
               //    }
               // }
               if(backhaulStatusEl.checked){
                  mapData.points[globalSelectedPointKey].data.backhaul_status = true
               }  else {
                  mapData.points[globalSelectedPointKey].data.backhaul_status = false
                  globalGeneratedHeatmapNode = null
                  generatedHeatmapNode = null
               }
               generateMapMesh()
               updateNodeSettings(mapData.points[globalSelectedPointKey])
            }, 500)
         }

         function renderNodeSettings(point) {
            globalSelectedPointKey = point
            nodenameEl.innerHTML = 'Node ' + mapData.points[globalSelectedPointKey].settings.index
            nodeheightEl.value = mapData.points[globalSelectedPointKey].settings.nodeheight
            nodeLatitudeEl.value = mapData.points[globalSelectedPointKey].settings.lat
            nodeLongitudeEl.value = mapData.points[globalSelectedPointKey].settings.lng
            nodedistanceEl.value = mapData.points[globalSelectedPointKey].settings.radius
            heatMapAreaSector.value = mapData.points[globalSelectedPointKey].settings.heatmap_area
            heatMapAreaAngle.value = mapData.points[globalSelectedPointKey].settings.heatmap_angle
            customValueField.value = mapData.points[globalSelectedPointKey].settings.heatmap_angle
            heatMapAreaBearing.value = mapData.points[globalSelectedPointKey].settings.heatmap_bearing
            heatMapAreaBearingPitch.value = mapData.points[globalSelectedPointKey].settings.heatmap_bearing_pitch
            heatmapLineStep.value = mapData.points[globalSelectedPointKey].settings.heatmap_line_step
            heatmapState.value = mapData.points[globalSelectedPointKey].settings.heatmap_state
            nodeTransmitPower.value = mapData.points[globalSelectedPointKey].settings.transmit_power
            nodeGain.value = mapData.points[globalSelectedPointKey].settings.gain
            nodeCableLoss.value = mapData.points[globalSelectedPointKey].settings.cable_loss
            nodeOtherLoss.value = mapData.points[globalSelectedPointKey].settings.other_loss
            secondAntennaHeight.value = mapData.points[globalSelectedPointKey].settings.pair_antenna_height
            secondNodeGain.value = mapData.points[globalSelectedPointKey].settings.pair_antenna_gain
            secondNodeCableLoss.value = mapData.points[globalSelectedPointKey].settings.pair_antenna_cable_loss
            nodeFrequency.value = mapData.points[globalSelectedPointKey].settings.node_frequency
            bandWidth.value = mapData.points[globalSelectedPointKey].settings.band_width
            backhaulStatusEl.value = mapData.points[globalSelectedPointKey].data.backhaul_status

            nodeheightEl.removeEventListener('change', changeNodeHeight)
            nodeLatitudeEl.removeEventListener('change', changeNodeLatitude)
            nodeLongitudeEl.removeEventListener('change', changeNodeLongitude)
            nodedistanceEl.removeEventListener('change',changeNodeRadius)
            heatMapAreaSector.removeEventListener('change',changeHeatArea)
            heatMapAreaAngle.removeEventListener('change',changeHeatAngle)
            customValueField.removeEventListener('change',changeCustomHeatAngle)
            heatMapAreaBearing.removeEventListener('change',changeHeatBearing)
            heatMapAreaBearingPitch.removeEventListener('change',changeHeatBearingPitch)
            heatmapLineStep.removeEventListener('change',changeHeatLineStep)
            heatmapState.removeEventListener('change',changeHeatmapState)
            nodeTransmitPower.removeEventListener('change',changeNodeTransmitPower)
            nodeGain.removeEventListener('change',changeNodeGain)
            nodeCableLoss.removeEventListener('change',changeNodeCableLoss)
            nodeOtherLoss.removeEventListener('change',changeNodeOtherLoss)
            nodeFrequency.removeEventListener('change',changeNodeFrequency)
            secondAntennaHeight.removeEventListener('change',changePairAntennaHeight)
            secondNodeGain.removeEventListener('change',changePairAntennaGain)
            secondNodeCableLoss.removeEventListener('change',changePairAntennaCableLoss)
            bandWidth.removeEventListener('change',changeNodeBandWidth)
            backhaulStatusEl.removeEventListener('change',changeNodeBackhaulState)

            nodeheightEl.addEventListener('change', changeNodeHeight)
            nodeLatitudeEl.addEventListener('change',changeNodeLatitude)
            nodeLongitudeEl.addEventListener('change', changeNodeLongitude)
            nodedistanceEl.addEventListener('change',changeNodeRadius)
            heatMapAreaSector.addEventListener('change',changeHeatArea)
            heatMapAreaAngle.addEventListener('change',changeHeatAngle)
            customValueField.addEventListener('change',changeCustomHeatAngle)
            heatMapAreaBearing.addEventListener('change',changeHeatBearing)
            heatMapAreaBearingPitch.addEventListener('change',changeHeatBearingPitch)
            heatmapLineStep.addEventListener('change',changeHeatLineStep)
            heatmapState.addEventListener('change',changeHeatmapState)
            nodeTransmitPower.addEventListener('change',changeNodeTransmitPower)
            nodeGain.addEventListener('change',changeNodeGain)
            nodeCableLoss.addEventListener('change',changeNodeCableLoss)
            nodeOtherLoss.addEventListener('change',changeNodeOtherLoss)
            nodeFrequency.addEventListener('change',changeNodeFrequency)
            secondAntennaHeight.addEventListener('change',changePairAntennaHeight)
            secondNodeGain.addEventListener('change',changePairAntennaGain)
            secondNodeCableLoss.addEventListener('change',changePairAntennaCableLoss)
            bandWidth.addEventListener('change',changeNodeBandWidth)
            backhaulStatusEl.addEventListener('change',changeNodeBackhaulState)
         }

         function changePoiLabel() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].label = poiLabelEl.value
               generateMapMesh()
            }, 100)
         }

         function changePoiLabelType() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].type = poiLabelType.value
               generateMapMesh()
            }, 100)
         }

         function changePoiAddress() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].address = poiAddressEl.value
               generateMapMesh()
            }, 100)
         }

         function changePoiLatitude() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].lat = parseInt(poiLatEl.value)
               generateMapMesh()
            }, 100)
         }

         function changePoiLongitude() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].lng = parseInt(poiLngEl.value)
               generateMapMesh()
            }, 100)
         }

         function changePoiIcon() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               if(poiIconEl.value !== 'custom') {
                  customPoiIconEl.classList.add('hidden')
                  localPoiArr[globalPOIKey].icon_code = poiIconEl.value
               } else {
                  localPoiArr[globalPOIKey].icon_code = 'custom'
                  customPoiIconEl.classList.remove('hidden')
                  customPoiIconInput.value = ''
               }
               generateMapMesh()
            }, 100)
         }

         function changeCustomPoiIcon() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].icon_code = customPoiIconInput.value
               generateMapMesh()
            }, 100)
         }

         function changePoiIconColor() {
            loadingFixed.classList.add('pin-active')
            setTimeout( () => {
               localPoiArr[globalPOIKey].color = poiIconColorEl.value
               generateMapMesh()
            }, 100)
         }

         function renderPointOfInterest(point) {
            globalPOIKey = point

            poiLabelEl.value = globalSelectedPoi.label
            poiLabelType.value = globalSelectedPoi.type
            poiAddressEl.value = globalSelectedPoi.address
            poiLatEl.value = globalSelectedPoi.lat
            poiLngEl.value = globalSelectedPoi.lng
            poiIconColorEl.value = globalSelectedPoi.color
            let listOfIcons = [
               "fa-home",
               "fa-user",
               "fa-hospital-o",
               "fa-graduation-cap",
               "fa-shopping-basket",
               "fa-plane",
               "fa-tree",
               "fa-futbol-o",
               "fa-paw",
               "fa-industry",
               "fa-university",
               "fa-gavel",
               "fa-cutlery",
               "custom",
            ]

            if(listOfIcons.includes(globalSelectedPoi.icon_code)) {
               poiIconEl.value = globalSelectedPoi.icon_code
            } else {
               poiIconEl.value = 'custom'
               customPoiIconEl.classList.remove('hidden')
               customPoiIconInput.value = globalSelectedPoi.icon_code
            }

            if(poiIconEl.value === 'custom' ) {
               customPoiIconEl.classList.remove('hidden')
            }

            // customPoiIconInput.value = globalSelectedPoi.icon_code



            poiLabelEl.removeEventListener('change',  changePoiLabel)
            poiLabelType.removeEventListener('change',  changePoiLabelType)
            poiAddressEl.removeEventListener('change',  changePoiAddress)
            poiLatEl.removeEventListener('change',  changePoiLatitude)
            poiLngEl.removeEventListener('change',  changePoiLongitude)
            poiIconEl.removeEventListener('change',  changePoiIcon)
            customPoiIconInput.removeEventListener('change',  changeCustomPoiIcon)
            poiIconColorEl.removeEventListener('change',  changePoiIconColor)


            poiLabelEl.addEventListener('change',  changePoiLabel)
            poiLabelType.addEventListener('change',  changePoiLabelType)
            poiAddressEl.addEventListener('change', changePoiAddress)
            poiLatEl.addEventListener('change',  changePoiLatitude)
            poiLngEl.addEventListener('change',  changePoiLongitude)
            poiIconEl.addEventListener('change',  changePoiIcon)
            customPoiIconInput.addEventListener('change',  changeCustomPoiIcon)
            poiIconColorEl.addEventListener('change',  changePoiIconColor)


            poiIdentifier.innerHTML = 'POI: ' + globalSelectedPoi.label
         }

         if(mapData.project.poiVisibilityStatus){
            poiMeshStatus.innerHTML = 'On'
         } else {
            poiMeshStatus.innerHTML = 'Off'
         }

         if(mapData.project.cluster_status){
            clusterMeshStatus.innerHTML = 'On'
         } else {
            clusterMeshStatus.innerHTML = 'Off'
         }


         function togglePoiVisibility() {
            let poiDataDrawer = document.getElementById('poiDataDrawer')
            loadingFixed.classList.add('pin-active')
            setTimeout(() => {
               mapData.project.poiVisibilityStatus = !mapData.project.poiVisibilityStatus
               if(mapData.project.poiVisibilityStatus){
                  poiMeshStatus.innerHTML = 'On'
               } else {
                  poiMeshStatus.innerHTML = 'Off'
               }
               generateMapMesh()
            }, 100)
         }

         function toggleClusterVisibility() {
            loadingFixed.classList.add('pin-active')
            mapData.project.cluster_status = !mapData.project.cluster_status
            updateClusterVisibility(mapData.project)

            if(mapData.project.cluster_status){
               clusterMeshStatus.innerHTML = 'On'
               clusterNodesEl.classList.add('visible')
               clusterCoverageEl.classList.add('visible')

               if(selectedMarker){
                  getNodeClusterInfo(selectedMarker.options.slug).then(() => {
                     clusterPoints = window.clusterInfo.points
                     clusterLines = window.clusterInfo.lines

                     highlightClusterNodes(clusterPoints, null, mapMarkers)
                     highlightClusterLines(clusterLines, mapLines)

                     // rebuildMapDataLinesClusterInfo().then(() => {
                     //    rebuildMapDataPointsClusterInfo()
                     // })


                     meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                     meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                     // window.clusterInfo = []

                     loadingFixed.classList.remove('pin-active')
                  })
               }

               if(!selectedMarker) {
                  meshClusterNodes.innerHTML = '0'
                  meshClusterCoverage.innerHTML = '0'
               }

            } else {
               clusterMeshStatus.innerHTML = 'Off'
               removeClusterHighlight(mapMarkers, mapLines)

               if(!selectedMarker) {
                  meshClusterNodes.innerHTML = '0'
                  meshClusterCoverage.innerHTML = '0'
               }

               clusterNodesEl.classList.remove('visible')
               clusterCoverageEl.classList.remove('visible')

               loadingFixed.classList.remove('pin-active')
            }
         }

         function calcNodesCount() {
            meshNodesCount.innerHTML = mapData.points.filter(x => x.settings.deleted !== true).length
         }


         function calcRadiusArea() {
            let circleArea
            let sum = 0

            for(let radiusCheck = 0; radiusCheck < mapData.points.length; radiusCheck++) {
               if(!mapData.points[radiusCheck].settings.deleted) {
                  circleArea = Math.PI * Math.pow(mapData.points[radiusCheck].settings.radius, 2)
                  // sum += parseInt(circleArea / 1000000)
                  sum += parseFloat((circleArea / 1000000).toFixed(2))
                  sum = Math.round((sum + Number.EPSILON) * 100) / 100
               }
            }

            return sum
         }

         let xPx = 30;
         let yPx = 95;

         let poixPx = 30;
         let poiyPx = 165;

         // default floating marker pixels value end
         let triggerDataView = document.getElementById('switchDataView')
         let meshDataView = document.getElementById('meshData')
         triggerDataView.addEventListener('click', (e) => {
            meshDataView.classList.toggle('mesh-data-active')
         })

         let nodesCountNr = document.getElementById('nodesCount')
         function filterNodes () {
            var input, filter, ul, li, a, i, txtValue;
            ul = document.getElementById("connectionNodesList");
            input = document.getElementById("nodeFilterInput");
            filter = input.value.toUpperCase();
            li = ul.getElementsByTagName("li");
            if(input.value.length > 0) {
               ul.style.display = ''
               nodesCountNr.style.display = 'none'
               for (i = 0; i < li.length; i++) {
                   a = li[i].getElementsByTagName("label")[0];
                   txtValue = a.textContent || a.innerText;
                   if (txtValue.toUpperCase().indexOf(filter) > -1) {
                       li[i].style.display = "";
                   } else {
                       li[i].style.display = "none";
                   }

               }
            } else {
               ul.style.display = 'none'
               nodesCountNr.style.display = ''
            }
         }

         function generateAvailableNodesLines(nodeSlug) {

            var container = document.getElementById('connectionNodesList');
            container.innerHTML = ''
            nodesCountNr.style.display = 'none'

            // checking if we have elements inside the available nodes arr
            if(availableConnectionNodes.length) {
               nodesCountNr.style.display = ''
               nodesCountNr.innerHTML = availableConnectionNodes.length + ' nodes available'
               // looping through the array and constructing a checkbox for each available node
               for(let avbNode = 0; avbNode < availableConnectionNodes.length; avbNode++) {
                  var listEl = document.createElement('li')
                  listEl.style.display = 'none'
                  var checkbox = document.createElement('input');
                  checkbox.type = 'checkbox';
                  checkbox.id = 'node_' + availableConnectionNodes[avbNode].settings.slug;
                  checkbox.name = 'available_node';
                  checkbox.value = availableConnectionNodes[avbNode].settings.slug;
                  // attaching the lat lng of the node to his checkbox as attribute
                  checkbox.setAttribute('lat', availableConnectionNodes[avbNode].settings.lat);
                  checkbox.setAttribute('lng', availableConnectionNodes[avbNode].settings.lng);
                  //
                  var label = document.createElement('label')
                  label.htmlFor = checkbox.id;
                  label.appendChild(document.createTextNode('Node' + availableConnectionNodes[avbNode].settings.index));
                  var br = document.createElement('br');

                  listEl.appendChild(checkbox);
                  listEl.appendChild(label);
                  listEl.appendChild(br);
                  container.appendChild(listEl);
               }
            }  else {
               container.innerHTML = 'All available nodes are connected'
            }

            let checkedNodeSlug
            let avCbox = Array.from( document.getElementsByName( "available_node" ) );
            let postObject = {
               points: []
            }

            postObject.points.push(nodeSlug)
            loadingFixed.classList.remove('pin-active')

            for(let av = 0; av < avCbox.length; av++) {
               avCbox[av].addEventListener('change', () => {
                  loadingFixed.classList.add('pin-active')
                  checkedNodeSlug = avCbox[av].value
                  if(avCbox[av].checked){
                     postObject.points.push(avCbox[av].value)
                     connectWithSpecificNodeAjax(postObject)
                        .then(() => {
                           postObject.points.splice(postObject.points.length - 1, 1)

                           if(mapData.project.cluster_status && selectedMarker) {
                              getNodeClusterInfo(selectedMarker.options.slug)
                              .then(() => {
                                 clusterPoints = window.clusterInfo.points
                                 clusterLines = window.clusterInfo.lines

                                 highlightClusterNodes(clusterPoints, null, mapMarkers)
                                 highlightClusterLines(clusterLines, mapLines)

                                 // rebuildMapDataLinesClusterInfo().then(() => {
                                 //    rebuildMapDataPointsClusterInfo()
                                 // })

                                 meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                 meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                 // window.clusterInfo = []
                              })
                           }
                            generateMapMesh()
                        })
                  } else {
                     loadingFixed.classList.add('pin-active')
                     let existingLine = null
                     for(let line in mapData.lines) {
                        if(
                           (
                              mapData.lines[line].start_slug === checkedNodeSlug
                              &&
                              mapData.lines[line].end_slug === nodeSlug

                              ||

                              mapData.lines[line].start_slug === nodeSlug
                              &&
                              mapData.lines[line].end_slug === checkedNodeSlug
                           )
                        )
                        {
                           existingLine = line
                           break
                        }
                     }
                     if(existingLine) {
                        // postObject.points.push(avCbox[av].value)
                        // postObject.deleted = true
                        let lineSlug = mapData.lines[existingLine].slug
                        mapData.lines.splice(existingLine, 1)
                        deleteSpecificLine(lineSlug)
                           .then(() => {
                              // mapData.lines[existingLine].slug
                              if(mapData.project.cluster_status && selectedMarker) {
                                 removeClusterHighlight(mapMarkers, mapLines)
                                 getNodeClusterInfo(selectedMarker.options.slug)
                                 .then(() => {
                                    clusterPoints = window.clusterInfo.points
                                    clusterLines = window.clusterInfo.lines

                                    // rebuildMapDataLinesClusterInfo().then(() => {
                                    //    rebuildMapDataPointsClusterInfo()
                                    // })

                                    highlightClusterNodes(clusterPoints, null, mapMarkers)
                                    highlightClusterLines(clusterLines, mapLines)

                                    meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                    meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                    // window.clusterInfo = []
                                 })
                              }
                              generateMapMesh()
                           })
                     }
                  }
               })
            }
         }

         function generateAvailableNodes(nodeSlug) {
            new Promise((resolve, reject) => {
               availableConnectionNodes = []
               for(let node = 0; node < mapData.points.length; node++) {
                  if(mapData.points[node].settings.slug === nodeSlug) continue
                  let connected = false

                  for(let node_line = 0; node_line < mapData.lines.length; node_line++){
                     if(nodeSlug === mapData.points[node].settings.slug){
                        connected = true
                        continue
                     }
                     if(
                        mapData.lines[node_line].start_slug === nodeSlug
                       ||
                       mapData.lines[node_line].end_slug === nodeSlug
                     )
                     {
                        if(
                          mapData.lines[node_line].start_slug === mapData.points[node].settings.slug
                          ||
                          mapData.lines[node_line].end_slug === mapData.points[node].settings.slug
                        ){
                           connected = true
                        }
                     }
                  }
                  if(!connected && !mapData.points[node].settings.deleted) {
                     availableConnectionNodes.push(mapData.points[node])
                  }
                  generateAvailableNodesLines(nodeSlug)
               }
               resolve()
            })
         }


         // function that calculates the next index
         // if method is post- user is the first time on the page and has no data saved
         // then we will increment mapData points with 1
         // if method is edit - user has points and lines, we must take the last point from
         // the array and increment its index with 1
         function getNextIndex() {
            if (action == 'post') {
               return mapData.points.length + 1;
            } else if (action == 'put') {
               return mapData.points.length ? mapData.points[mapData.points.length - 1].settings.index + 1 : 1;
            }
         }

         function getNextID() {
            if (action == 'post') {
               return mapData.points.length + 1;
            } else if (action == 'put') {
               return mapData.points.length ? mapData.points[mapData.points.length - 1].settings.slug + 1 : 1;
            }
         }


         function customTipMarker() {
             this.unbindTooltip();
             if(!this.isPopupOpen()) this.bindTooltip('To add a node, drag or click this marker').openTooltip();
         }
         function customTipPOI() {
             this.unbindTooltip();
             if(!this.isPopupOpen()) this.bindTooltip('To add a point of interest, drag or click this icon').openTooltip();
         }

         function customPop() {
             this.unbindTooltip();
         }

         let mapCenter
         let dinLat
         let dinLng
         let dinLatLng

         function generateRandomNumber() {
             var min = 0.0011,
                 max = 0.0199,
                 highlightedNumber = Math.random() * (max - min) + min;

                 return highlightedNumber
         };

         function initFloatingMarker() {
            let pointID = getNextID()
            let pointIndex = getNextIndex()
            floatingMarker = L.marker(map.containerPointToLatLng([xPx, yPx]),{icon: floatingMarkerIcon, draggable: true}).addTo(map)

            map.on('move', (event) => {
               floatingMarker.setLatLng(map.containerPointToLatLng([xPx, yPx]))
            })

            floatingMarker.addEventListener('click', (e) => {
               mapCenter = map.getCenter()
               dinLat = mapCenter.lat + generateRandomNumber()
               dinLng = mapCenter.lng + generateRandomNumber()
               dinLatLng = new L.LatLng(dinLat, dinLng)

               loadingFixed.classList.add('pin-active')
               setTimeout( (e) => {
                  if(mapData.points.length === 0) {
                     floatingMarker.setLatLng(map.getCenter())
                  } else {
                     floatingMarker.setLatLng(dinLatLng)
                  }

                  // floatingMarker.setLatLng(map.getCenter())

                  mapData.points.push({
                     settings: {
                        slug: null,
                        id: null,
                        cluster_id: null,
                        index: pointIndex,
                        lat: floatingMarker._latlng.lat,
                        lng: floatingMarker._latlng.lng,
                        active: false,
                        deleted: false,
                        hascircle: globalCirclesStatus,
                        radius: 200,
                        nodeheight: 10,
                        heatmap_area: 1000,
                        heatmap_angle: 60,
                        heatmap_bearing: 90,
                        heatmap_state: false,
                        heatmap_bearing_pitch: 0.5,
                        heatmap_line_step: 3,
                        heatmap_start_angle: 0,
                        heatmap_end_angle: 0,
                        transmit_power: 28,
                        gain: 16,
                        cable_loss: 0,
                        other_loss: 6,
                        pair_antenna_height: 10,
                        pair_antenna_gain: 18.4,
                        pair_antenna_cable_loss: 0,
                        node_frequency: 5500,
                        band_width: 80,
                     },
                     data: {
                        backhaul_status: false
                     },
                     finance: {}

                  })

                  generateMapMesh()

                  if (mapData.points.length == 1 && !mapData.project.node_connection_type_saved) {
                     ajaxAutoMarker()
                       .then(() => {
                          loadingFixedBlade.classList.remove('pin-active')
                       });
                  }

                  if(mapData.points.length > 1 && !mapData.project.node_connection_type_saved) {
                     $('#diffNodeOptionModal').modal({backdrop: 'static', keyboard: false}, 'show')
                  }

                  if(!mapData.project.node_connection_type_saved) {
                     document.getElementById('nctChoice0').checked = true
                     mapData.project.node_connection_type === 0
                  }

                  if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 1) {
                     document.getElementById('nctChoice1').checked = true
                     loadingFixedBlade.classList.add('pin-active')
                     ajaxAutoMarker(1)
                        .then(() => {
                           if(mapData.project.cluster_status && selectedMarker) {
                              setTimeout(() => {
                                 getNodeClusterInfo(selectedMarker.options.slug)
                                   .then(() => {
                                      clusterPoints = window.clusterInfo.points
                                      clusterLines = window.clusterInfo.lines

                                      highlightClusterNodes(clusterPoints, null, mapMarkers)
                                      highlightClusterLines(clusterLines, mapLines)

                                      // rebuildMapDataLinesClusterInfo().then(() => {
                                      //    rebuildMapDataPointsClusterInfo()
                                      // })

                                      meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                      meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                      // window.clusterInfo = [];
                                 })
                              }, 1000)
                           }

                           loadingFixedBlade.classList.remove('pin-active')
                        })

                        if(sectorBearingsArr.length && globalGeneratedHeatmapNode) {
                           checkNodeInHeatArea(mapData.points.length - 1)
                        }
                  }

                  if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 2) {
                     document.getElementById('nctChoice2').checked = true
                     document.getElementById('nodeFilterInput').value = ''
                     loadingFixedBlade.classList.add('pin-active')
                     ajaxAutoMarker(2)
                       .then(() => {
                          setTimeout( () => {
                             generateAvailableNodes(mapData.points[mapData.points.length - 1].settings.slug)
                          }, 1000)

                          loadingFixedBlade.classList.remove('pin-active')
                      })
                       .then(() => {
                        $('#nodeConnectionsModal').modal('show')

                        setTimeout( () => {
                           document.getElementById('nodeFilterInput').focus()
                        }, 600)
                     })
                  }

                  if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 3) {
                     document.getElementById('nctChoice3').checked = true
                     loadingFixedBlade.classList.add('pin-active')
                     ajaxAutoMarker(3)
                       .then(() => {
                           generateMapMesh()
                     })
                  }

                  if(sectorBearingsArr.length && globalGeneratedHeatmapNode) {
                     checkNodeInHeatArea(mapData.points.length - 1)
                  }
               }, 100)
            })

            floatingMarker.addEventListener('dragstart', (e) => {
               newMarker = L.marker(map.containerPointToLatLng([xPx, yPx]),{icon: floatingMarkerIcon, draggable: true}).addTo(map)
            })

            floatingMarker.addEventListener('dragend', (e) => {
               loadingFixed.classList.add('pin-active')
               setTimeout( (e) => {
                  markerDragEnd(e)
               }, 100)
            })

            floatingMarker.on('mouseover', customTipMarker);
            // floatingMarker.bindTooltip("To start the project, drag or click this marker").openTooltip();
         }

         initFloatingMarker()

         function initFloatingPoi() {
            floatingPoi = L.marker(map.containerPointToLatLng([poixPx, poiyPx]),{icon: floatingPoiIcon, draggable: true}).addTo(map)
            map.on('move', (event) => {
               floatingPoi.setLatLng(map.containerPointToLatLng([poixPx, poiyPx]))
            })

            floatingPoi.addEventListener('click', (e) => {
               loadingFixed.classList.add('pin-active')
               setTimeout( (e) => {
                  floatingPoi.setLatLng(map.getCenter())

                  localPoiArr.push({
                     u_id: null,
                     active: false,
                     deleted: false,
                     lat: floatingPoi._latlng.lat,
                     lng: floatingPoi._latlng.lng,
                     label: 'Label example',
                     type: 'Category example',
                     address: 'Address example',
                     color: '#c3c0c0',
                     icon_code: 'fa-graduation-cap',
                  })
                  generateMapMesh()
               }, 100)
            })

            floatingPoi.addEventListener('dragstart', (e) => {
               newPoi = L.marker(map.containerPointToLatLng([poixPx, poiyPx]),{
                  icon: floatingPoiIcon,
                  draggable: true,
                  // renderer: myRenderer
               }).addTo(map)
            })

            floatingPoi.addEventListener('dragend', (e) => {
               loadingFixed.classList.add('pin-active')
               setTimeout( (e) => {
                  poiDragEnd(e)
               }, 100)
            })
            // floatingPoi.bindTooltip('To add a point of interest, drag or click this icon').openTooltip();
            floatingPoi.on('mouseover', customTipPOI);
         }

         $('#nodeConnectionsModal').on('hidden.bs.modal', function () {
            document.getElementById('nodeFilterInput').value = ''
            document.getElementById('connectionNodesList').innerHTML = ''
            document.getElementById('nodesCount').innerHTML = ''
         })

         function highlightClusterNodes(clusterNodes, marker = null, meshMarkers = null) {
            for(let cluster_point in clusterNodes) {
               if(meshMarkers){
                  for(let map_point in meshMarkers){
                     if(meshMarkers[map_point]._icon.classList.contains('m-' + clusterPoints[cluster_point].settings.slug)){
                        meshMarkers[map_point]._icon.classList.add('clusterActiveMarker')
                     }
                  }
               }
               if(marker && marker._icon.classList.contains('m-' + clusterPoints[cluster_point].settings.slug) && mapData.project.cluster_status){
                  marker._icon.classList.add('clusterActiveMarker')
               } else if(!mapData.project.cluster_status) {
                  marker._icon.classList.remove('clusterActiveMarker')
               }
            }
         }

         function highlightClusterLines(clusterLines, meshLines) {
            for(let cluster_line in clusterLines) {
               for(let map_line in meshLines){
                  if(meshLines[map_line]._path.classList.contains('l-' + clusterLines[cluster_line].slug)){
                     meshLines[map_line]._path.classList.add('clusterActiveLine')
                  }
               }
            }
         }

         function removeClusterHighlight(meshMarkers, meshLines) {
            clusterLines = null
            clusterPoints = null
            for(let map_point in meshMarkers){
               if(meshMarkers[map_point]._icon.classList.contains('clusterActiveMarker')) {
                  meshMarkers[map_point]._icon.classList.remove('clusterActiveMarker')
               }
            }

            for(let map_line in meshLines){
               if(meshLines[map_line]._path.classList.contains('clusterActiveLine')){
                  meshLines[map_line]._path.classList.remove('clusterActiveLine')
               }
            }
         }

         let mapMarkers = null
         let mapLines = null
         // let wcl = null
         let clusterPoints = window.clusterInfo.points
         let clusterLines = window.clusterInfo.lines

         // for(let initial_node in mapMarkers) {
         //    if(mapMarkers[initial_node].options.active){
         //       console.log(mapMarkers[initial_node])
         //       selectedMarker = mapMarkers[initial_node]
         //    }
         // }

         async function generateMapMesh() {

            mapMarkers = []
            mapLines = []
            mapCenter = null
            dinLat = null
            dinLng = null
            dinLatLng = null


            loadingFixed.classList.add('pin-active')
            clearMap()
            initFloatingMarker()
            calcNodesCount()
            meshPoiCoverage.innerHTML = calcRadiusArea() + ' km' + '<sup>2</sup>'

            if(mapData.project.cluster_status) {
               clusterNodesEl.classList.add('visible')
               clusterCoverageEl.classList.add('visible')
               meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
               meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'

               // if(!selectedMarker) {
               //    meshClusterNodes.innerHTML = '0'
               //    meshClusterCoverage.innerHTML = '0'
               // }

            } else {
               clusterNodesEl.classList.remove('visible')
               clusterCoverageEl.classList.remove('visible')
            }

            for(let point in mapData.points) {
               if(mapData.points[point].settings.deleted) continue

               mapLatlng = new L.LatLng(mapData.points[point].settings.lat, mapData.points[point].settings.lng)
               marker = L.marker(mapLatlng, {icon: customIcon, draggable: true,}).addTo(map)

               // apending the index of the node to the map marker
               var para = document.createElement('span')
               var node = document.createTextNode(mapData.points[point].settings.index)
               para.appendChild(node)
               para.className = 'marker-index'
               marker._icon.appendChild(para)

               //

               // transfering the point options from the main obj to the map marker
               marker.options.slug = mapData.points[point].settings.slug
               marker.options.active = mapData.points[point].settings.active
               marker.options.index = mapData.points[point].settings.index
               marker.options.deleted = mapData.points[point].settings.deleted
               marker.options.id = mapData.points[point].settings.id
               marker.options.defaultLat = mapData.points[point].settings.lat
               marker.options.defaultLng = mapData.points[point].settings.lng
               marker.options.radius = mapData.points[point].settings.radius
               marker.options.hascircle = mapData.points[point].settings.hascircle
               marker.options.nodeheight = mapData.points[point].settings.nodeheight
               marker.options.heatmap_state = mapData.points[point].settings.heatmap_state
               marker.options.heatmap_area = mapData.points[point].settings.heatmap_area
               marker.options.heatmap_angle = mapData.points[point].settings.heatmap_angle
               marker.options.heatmap_bearing = mapData.points[point].settings.heatmap_bearing
               marker.options.heatmap_bearing_pitch = mapData.points[point].settings.heatmap_bearing_pitch
               marker.options.heatmap_line_step = mapData.points[point].settings.heatmap_line_step
               marker.options.transmit_power = mapData.points[point].settings.transmit_power
               marker.options.gain = mapData.points[point].settings.gain
               marker.options.cable_loss = mapData.points[point].settings.cable_loss
               marker.options.other_loss = mapData.points[point].settings.other_loss
               marker.options.pair_antenna_height = mapData.points[point].settings.pair_antenna_height
               marker.options.pair_antenna_gain = mapData.points[point].settings.pair_antenna_gain
               marker.options.pair_antenna_cable_loss = mapData.points[point].settings.pair_antenna_cable_loss
               marker.options.node_frequency = mapData.points[point].settings.node_frequency
               marker.options.band_width = mapData.points[point].settings.band_width
               marker.options.backhaul_status = mapData.points[point].data.backhaul_status
               marker._icon.classList.add( 'm-' + marker.options.slug)

               if(selectedMarker === undefined || selectedMarker === null) {
                  if (mapData.points[point].settings.active) {
                     selectedMarker = marker
                  }
               }
               mapMarkers.push(marker)

               if(selectedMarker) {
                  highlightClusterNodes(clusterPoints, null, mapMarkers)
                  highlightClusterLines(clusterLines, mapLines)
               }
               // for(let cluster_point in clusterPoints) {
               //    if(marker._icon.classList.contains('m-' + clusterPoints[cluster_point].settings.slug) && mapData.project.cluster_status){
               //       marker._icon.classList.add('clusterActiveMarker')
               //    } else if(!mapData.project.cluster_status) {
               //       marker._icon.classList.remove('clusterActiveMarker')
               //    }
               // }



               if(mapData.points[point].settings.active) {
                  marker._icon.classList.add('activeMarker')

                  for (let am = 0; am < mapData.points.length; am++) {
                     if(mapData.points[point].settings.slug !== mapData.points[am].settings.slug) {
                        mapData.points[am].settings.active = false
                     } else {
                        if(mapData.project.poiVisibilityStatus && localPoiArr[globalPOIKey]) {
                           localPoiArr[globalPOIKey].active = false
                           globalSelectedPoi = null
                           globalPOIKey = null
                        }
                        mapData.points[am].settings.active = true
                     }
                  }

                  selectedNode = mapData.points[point]
                  heatmapState.checked = selectedNode.settings.heatmap_state
                  backhaulStatusEl.checked = selectedNode.data.backhaul_status
                  renderNodeSettings(point)

                  nodeSettingsBox.classList.remove('hidden')
                  nodeBoxMessage.classList.add('hidden')
                  pointOfInterestSettings.classList.add('hidden')
                  // meshDataView.classList.add('mesh-data-active')
               }

               // checking if the global variable is set and toggling the node radius if true when the fn is called
               if(globalCirclesStatus) {
                  let markerCircle = L.circle([marker.options.defaultLat, marker.options.defaultLng], {radius: marker.options.radius});
                  markerCircle.setStyle({className: 'circleStyle'})
                  markerCircle.addTo(map)
               }

               // checking heatmap state and creating the heatmap sector

               if(marker.options.heatmap_state){
                  generatedHeatmapNode = marker.options

                  let obj1 = _.cloneDeep(generatedHeatmapNode)
                  let obj2 = _.cloneDeep(globalGeneratedHeatmapNode)

                  if(obj1) {
                     obj1.heatmap_start_angle = null
                     obj1.heatmap_end_angle = null
                  }

                  if(obj2) {
                     obj2.heatmap_start_angle = null
                     obj2.heatmap_end_angle = null
                  }
                  let heatmapFlag = false
                  if(
                     (obj1 && obj2 && JSON.stringify(obj1) !== JSON.stringify(obj2))
                     ||
                     ( obj1 !== null && obj2 === null)
                  )
                  {
                     heatmapFlag = true
                  }
                  setHeatmapSector(generatedHeatmapNode)
                  if(heatmapFlag === true) {
                     heatmapLines = []
                     globalGeneratedHeatmapNode = generatedHeatmapNode
                     await calcSectorElevationHeat(globalGeneratedHeatmapNode)
                  }
                  setHeatLayer()
               }

               //

               if(marker.options.backhaul_status){
                  marker._icon.style.backgroundPosition = '-40px'
               }

               marker.addEventListener('click', (e) => {
                  removeClusterHighlight(mapMarkers, mapLines)

                  selectedNode = e.target.options
                  selectedMarker = e.target
                  heatmapState.checked = selectedNode.heatmap_state
                  backhaulStatusEl.checked = selectedNode.backhaul_status

                  // if(mapData.project.cluster_status){
                  //    loadingFixed.classList.add('pin-active')
                  //    getNodeClusterInfo(mapData.points[point].settings.slug)
                  //      .then(() => {
                  //          clusterPoints = window.clusterInfo.points
                  //          clusterLines = window.clusterInfo.lines
                  //
                  //          highlightClusterNodes(clusterPoints, null, mapMarkers)
                  //          highlightClusterLines(clusterLines, mapLines)
                  //
                  //         // rebuildMapDataLinesClusterInfo().then(() => {
                  //         //    rebuildMapDataPointsClusterInfo()
                  //         // })
                  //
                  //
                  //         clusterNodesEl.classList.add('visible')
                  //          clusterCoverageEl.classList.add('visible')
                  //          meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                  //          meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                  //          // window.clusterInfo = []
                  //
                  //          loadingFixed.classList.remove('pin-active')
                  //    })
                  // }

                  for (let am = 0; am < mapData.points.length; am++) {
                     if(e.target.options.slug !== mapData.points[am].settings.slug) {
                        mapData.points[am].settings.active = false
                     } else {
                        if(mapData.project.poiVisibilityStatus && localPoiArr[globalPOIKey]) {
                           localPoiArr[globalPOIKey].active = false
                           globalSelectedPoi = null
                           globalPOIKey = null
                        }
                        mapData.points[am].settings.active = true
                     }
                  }
                  setTimeout(() => {
                     updateDragPointAjax(mapData.points[point])
                     getNodeInfo(mapData.points[point].settings.slug)
                  }, 1000)

                  if(mapData.project.cluster_status){
                     loadingFixed.classList.add('pin-active')
                     getNodeClusterInfo(mapData.points[point].settings.slug)
                       .then(() => {
                          clusterPoints = window.clusterInfo.points
                          clusterLines = window.clusterInfo.lines

                          highlightClusterNodes(clusterPoints, null, mapMarkers)
                          highlightClusterLines(clusterLines, mapLines)


                          clusterNodesEl.classList.add('visible')
                          clusterCoverageEl.classList.add('visible')
                          meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                          meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'

                          loadingFixed.classList.remove('pin-active')
                       })
                  }

                  renderNodeSettings(point)

                  nodeSettingsBox.classList.remove('hidden')
                  meshDataView.classList.add('mesh-data-active')

                  nodeSettingsBox.classList.remove('hidden')
                  nodeBoxMessage.classList.add('hidden')
                  pointOfInterestSettings.classList.add('hidden')
                  meshDataView.classList.add('mesh-data-active')
               })

               marker.addEventListener('contextmenu', (e) => {
                  lineMenu.classList.add('hidden');
                  marker.contextmenuactive = true
                  markerMenu.innerHTML = `<ul class="context-menu marker-menu map-element-menu">
                                             <li id="showNodeLines">  <p class="icon-wrapper"> <i class="fa fa-sitemap" aria-hidden="true"></i> </p> Create all node lines</li>
                                             <li id="deleteLinesConfirm" data-toggle="modal" data-target="#deleteConfirmationModal"> <p class="icon-wrapper"> <i class="fa fa-trash"></i> </p> Delete node lines</li>
                                             <li id="connectSingleNode"> <p class="icon-wrapper"> <i class="fa fa-link" aria-hidden="true"></i> </p>  Connect to node </li>
                                             <li id="deleteSelectedNodeModalTrigger" data-toggle="modal" data-target="#deleteNodeModal"> <p class="icon-wrapper"> <i class="fa fa-trash"></i> </p>  Delete node </li>
                                          </ul>`
                  markerMenu.classList.remove('hidden');
                  markerMenu.style.top = (mouseY(event) - 80) +'px';
                  markerMenu.style.left = (mouseX(event) - 50) + 'px';
                  document.addEventListener('click', markerMenuHandler);


                  let showNodeLinesBtn = document.getElementById('showNodeLines')
                  let connectToSingleNode = document.getElementById('connectSingleNode')
                  let deleteLinesConfirm = document.getElementById('deleteLinesConfirm')
                  let deleteSelectedNodeModalTrigger = document.getElementById('deleteSelectedNodeModalTrigger')
                  let deleteNodeConfirmationModal = document.getElementById('deleteNodeModal')

                  deleteSelectedNodeModalTrigger.addEventListener('click', () => {
                     deleteNodeConfirmationModal.innerHTML = `
                     <div class="modal-dialog modal-sm">
                     <div class="modal-content">
                     <div class="modal-header">
                     <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                     <h5 class="modal-title">Confirm Delete</h5>
                     </div>
                     <div class="modal-body">
                     <p>If you are sure you want to remove the node, click Delete.</p>
                     </div>
                     <div class="modal-footer">
                     <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                     <button id="deleteNode" type="button" class="btn btn-danger danger" data-dismiss="modal">Delete Node</button>
                     </div>
                     </div>
                     </div>
                     `
                     let deleteNode = document.getElementById('deleteNode')

                     deleteNode.addEventListener('click', () => {
                        loadingFixed.classList.add('pin-active')

                        markerMenu.classList.add('hidden');
                        for(let rm = 0; rm < mapData.lines.length; rm++) {
                           if
                           (
                              mapData.lines[rm].active
                              &&
                              (
                                 mapData.points[point].settings.slug === mapData.lines[rm].start_slug
                                 ||
                                 mapData.points[point].settings.slug === mapData.lines[rm].end_slug
                              )
                           )
                           {
                              globalGeneratedGraphLine = null
                              generatedGraphLine = null
                              $('#chartContainer').hide()
                              $('#emptyGraphBoxMessage').removeClass('hidden')
                           }
                        }

                        if(e.target.options.heatmap_state = true) {
                           generatedHeatmapNode = null
                           globalGeneratedHeatmapNode = null
                           sectorBearingsArr = []
                        }

                        if(selectedMarker) {
                           if(e.target.options.slug === selectedMarker.options.slug) {
                              selectedMarker = null
                              removeClusterHighlight(mapMarkers, mapLines)
                              if(mapData.project.cluster_status) {
                                 toggleClusterVisibility()
                              }
                           }
                        }

                        mapData.points[point].settings.deleted = true
                        mapData.points[point].settings.heatmap_state = false
                        mapData.points[point].active = false
                        meshNodesCount.innerHTML = mapData.points.length - 1
                        nodeSettingsBox.classList.add('hidden')
                        nodeBoxMessage.classList.remove('hidden')
                        clearMapDataLines(mapData.points[point].settings)
                        let pointAJAX = mapData.points[point];
                        mapData.points.splice(point, 1);
                       // updateNodeSettings(mapData.points[point])
                        deleteNodeAJAX(pointAJAX).then(() => {
                           if(mapData.project.cluster_status && selectedMarker) {
                              getNodeClusterInfo(selectedMarker.options.slug)
                                .then(() => {
                                   clusterPoints = window.clusterInfo.points
                                   clusterLines = window.clusterInfo.lines

                                   // rebuildMapDataLinesClusterInfo().then(() => {
                                   //    rebuildMapDataPointsClusterInfo()
                                   // })

                                   meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                   meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                   // window.clusterInfo = []
                                })
                           }
                           generateMapMesh()
                        })
                     })
                  })

                  // event for the single node connection
                  connectToSingleNode.addEventListener('click', () => {
                     document.getElementById('nodeFilterInput').value = ''
                     loadingFixedBlade.classList.add('pin-active')
                     // ajaxAutoMarker(2)
                     //    .then(() => {
                           loadingFixedBlade.classList.remove('pin-active')
                           markerMenu.classList.add('hidden');
                           generateAvailableNodes(e.target.options.slug)
                           $('#nodeConnectionsModal').modal('show')
                        // })

                     setTimeout( () => {
                        document.getElementById('nodeFilterInput').focus()
                     }, 600)
                  })


                  // event for when the user wants to connect to all the available nodes
                  showNodeLinesBtn.addEventListener('click', () => {
                     loadingFixed.classList.add('pin-active')
                     markerMenu.classList.add('hidden');

                     connectToNodesLinesAjax(mapData.points[point])
                       .then(() => {
                          if(mapData.project.cluster_status && selectedMarker) {
                             getNodeClusterInfo(selectedMarker.options.slug)
                               .then(() => {
                                  clusterPoints = window.clusterInfo.points
                                  clusterLines = window.clusterInfo.lines

                                  highlightClusterNodes(clusterPoints, null, mapMarkers)
                                  highlightClusterLines(clusterLines, mapLines)

                                  // rebuildMapDataLinesClusterInfo().then(() => {
                                  //    rebuildMapDataPointsClusterInfo()
                                  // })


                                  meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                  meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                  // window.clusterInfo = []
                               })
                          }
                        generateMapMesh()
                       })
                  })

                  // showNodeLinesBtn.addEventListener('click', async () => {
                  //    markerMenu.classList.add('hidden');
                  //    await clearMapDataLines(e.target.options.defaultLat, e.target.options.defaultLng)
                  //    generateNodeLines(e.target.options.defaultLat, e.target.options.defaultLng)
                  // })

                  // event that triggers the popup in which the user chooses either to delete de node lines or cancel the action
                  deleteLinesConfirm.addEventListener('click', () => {
                     deleteConfirmationPopup.innerHTML = `
                     <div class="modal-dialog modal-sm">
                     <div class="modal-content">
                     <div class="modal-header">
                     <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                     <h5 class="modal-title">Confirm Delete</h5>
                     </div>
                     <div class="modal-body">
                     <p> If you are sure you want to delete the node lines click delete </p>
                     </div>
                     <div class="modal-footer">
                     <button type="button" class="btn btn-default" data-dismiss="modal"> Cancel </button>
                     <button type="button" class="btn btn-danger danger" id="deleteLinesBtn"> Delete lines </button>
                     </div>
                     </div>
                     </div>
                     `

                     let deleteLinesButton = document.getElementById('deleteLinesBtn')

                     // event that deletes all the node lines
                     deleteLinesButton.addEventListener('click', () => {
                        loadingFixed.classList.add('pin-active')
                        for(let u = 0; u < mapData.lines.length; u++) {
                           if
                           (
                              mapData.lines[u].active
                              &&
                              (
                                 e.target.options.slug === mapData.lines[u].start_slug
                                 ||
                                 e.target.options.slug === mapData.lines[u].end_slug
                              )
                           )
                           {
                              globalGeneratedGraphLine = null
                              generatedGraphLine = null
                              $('#chartContainer').hide()
                              $('#emptyGraphBoxMessage').removeClass('hidden')
                           }
                        }
                        setTimeout( () => {
                           markerMenu.classList.add('hidden');
                           // ajax delete lines

                           clearMapDataLines(e.target.options)
                           deleteNodeLinesAjax(mapData.points[point])
                             .then(() => {
                                if(mapData.project.cluster_status && selectedMarker) {
                                   removeClusterHighlight(mapMarkers, mapLines)
                                   getNodeClusterInfo(selectedMarker.options.slug)
                                     .then(() => {
                                        clusterPoints = window.clusterInfo.points
                                        clusterLines = window.clusterInfo.lines

                                        highlightClusterNodes(clusterPoints, null, mapMarkers)
                                        highlightClusterLines(clusterLines, mapLines)

                                        // rebuildMapDataLinesClusterInfo().then(() => {
                                        //    rebuildMapDataPointsClusterInfo()
                                        // })


                                        meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                        meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                        // window.clusterInfo = []
                                     })
                                }
                                generateMapMesh()
                                $('#deleteConfirmationModal').modal('hide')
                                deleteConfirmationPopup.innerHTML = ''
                             })
                        }, 100)
                     })
                  })
               });

               if(marker.contextmenuactive){
                  marker.fireEvent('oncontextmenu')
               }

               let markerMenuHandler = function(e) {
                  let isClickedOutside = !markerMenu.contains(e.target);
                  if (isClickedOutside) {
                     markerMenu.classList.add('hidden');
                     document.removeEventListener('click', markerMenuHandler);
                  }
               }


               marker.addEventListener('dragend', (e) => {
                  loadingFixed.classList.add('pin-active')

                  // checking if the dragged marker matches one of the line ends
                  for (let linestate = 0; linestate < mapData.lines.length; linestate++) {
                     if(
                        mapData.lines[linestate].active
                        &&
                        (
                           e.target.options.slug === mapData.lines[linestate].start_slug
                           ||
                           e.target.options.slug === mapData.lines[linestate].end_slug
                        )
                     )
                     {
                        injectLoadingElement(graphBox)
                     }
                  }

                  setTimeout( () => {
                     for(let dragPoint in mapData.points) {
                        if(mapData.points[dragPoint].settings.slug === e.target.options.slug) {
                           for(let dragLine in mapData.lines) {
                              if(
                                 mapData.lines[dragLine].start_slug === mapData.points[dragPoint].settings.slug
                              )
                              {
                                 mapData.lines[dragLine].start_slug = e.target.options.slug
                              }

                              if(
                                 mapData.lines[dragLine].end_slug === mapData.points[dragPoint].settings.slug
                              )
                              {
                                 mapData.lines[dragLine].end_slug = e.target.options.slug
                              }
                           }

                           mapData.points[dragPoint].settings.lat = e.target._latlng.lat
                           mapData.points[dragPoint].settings.lng = e.target._latlng.lng


                           generateMapMesh()
                           // map marker drag end ajax update point
                           updateDragPointAjax(mapData.points[dragPoint])
                           updateNodeSettings(mapData.points[dragPoint])
                        }
                     }
                  }, 100)

               })
            }


            if(mapData.project.poiVisibilityStatus) {
               poiClusters = L.markerClusterGroup();
               initFloatingPoi()

               for(let point = 0; point < localPoiArr.length; point++) {
                  if(localPoiArr[point].deleted) continue
                  pointCoords = new L.LatLng(localPoiArr[point].lat, localPoiArr[point].lng)

                  let poiMarker = L.divIcon({
                     html: ` <i class="fa ${localPoiArr[point].icon_code}" style="color: ${localPoiArr[point].color}"></i> `,
                     className: 'poi-icon-class mesh-tool-custom-marker',
                     iconSize: [30, 35]
                  });

                  pointOnMap = L.marker(pointCoords,{
                     icon: poiMarker,
                     draggable: true
                  })

                  pointOnMap.options.label = localPoiArr[point].label
                  pointOnMap.options.type = localPoiArr[point].type
                  pointOnMap.options.active = localPoiArr[point].active
                  pointOnMap.options.u_id = localPoiArr[point].u_id
                  pointOnMap.options.address = localPoiArr[point].address
                  pointOnMap.options.lat = localPoiArr[point].lat
                  pointOnMap.options.lng = localPoiArr[point].lng
                  pointOnMap.options.icon_code = localPoiArr[point].icon_code
                  pointOnMap.options.color = localPoiArr[point].color
                  pointOnMap.options.deleted = localPoiArr[point].deleted

                  poiClusters.addLayer(pointOnMap);
                  map.addLayer(poiClusters);
                  poiClusters.refreshClusters();

                  if(localPoiArr[point].active) {
                     // pointOnMap._icon.classList.add('activeMarker')

                     for (let poiAct = 0; poiAct < localPoiArr.length; poiAct++) {
                        if(localPoiArr[point].lat !== localPoiArr[poiAct].lat && localPoiArr[point].lng !== localPoiArr[poiAct].lng) {
                           localPoiArr[poiAct].active = false
                        } else {
                           localPoiArr[poiAct].active = true
                           if(mapData.points[globalSelectedPointKey]) {
                              mapData.points[globalSelectedPointKey].settings.active = false
                              selectedNode = null
                              globalSelectedPointKey = null
                           }
                        }
                     }


                     globalSelectedPoi = localPoiArr[point]
                     renderPointOfInterest(point)

                     if(!nodeSettingsBox.classList.contains('hidden')){
                        nodeSettingsBox.classList.add('hidden')
                     }
                     pointOfInterestSettings.classList.remove('hidden')
                     nodeBoxMessage.classList.add('hidden')
                     // meshDataView.classList.add('mesh-data-active')
                  }



                  pointOnMap.addEventListener('click', (e) => {
                     customPoiIconEl.classList.add('hidden')
                     globalSelectedPoi = e.target.options
                     if(globalSelectedPoi.icon_code === 'custom') {
                        customPoiIconInput.value = ''
                     }
                     renderPointOfInterest(point)

                     for (let sp = 0; sp < localPoiArr.length; sp++) {
                        if(e.target.options.lat !== localPoiArr[sp].lat && e.target.options.lng !== localPoiArr[sp].lng) {
                           localPoiArr[sp].active = false
                        } else {
                           if(mapData.points[globalSelectedPointKey]) {
                              mapData.points[globalSelectedPointKey].settings.active = false
                              selectedNode = null
                              globalSelectedPointKey = null
                           }
                           localPoiArr[sp].active = true
                        }
                     }

                     if(!nodeSettingsBox.classList.contains('hidden')){
                        nodeSettingsBox.classList.add('hidden')
                     }
                     pointOfInterestSettings.classList.remove('hidden')
                     nodeBoxMessage.classList.add('hidden')
                     nodeSettingsBox.classList.add('hidden')
                     meshDataView.classList.add('mesh-data-active')

                     // generateMapMesh()
                  })

                  pointOnMap.addEventListener('dragend', (e) => {
                     poiPopupContent.classList.add('hidden');
                     // pointOnMap._icon.classList.add('activeMarker')
                     // for (let sp = 0; sp < localPoiArr.length; sp++) {
                     //    if(e.target.options.lat !== localPoiArr[sp].lat && e.target.options.lng !== localPoiArr[sp].lng) {
                     //       localPoiArr[sp].active = false
                     //    } else {
                     //       localPoiArr[sp].active = true
                     //       if(selectedNode) {
                     //          selectedNode.active = false
                     //       }
                     //    }
                     // }

                     nodeSettingsBox.classList.add('hidden')
                     $('#poiCoordsChangeConfirmationModal').modal('show')
                     coordsChangePopup.innerHTML = `
                                                   <div class="modal-dialog modal-md">
                                                   <div class="modal-content">
                                                   <div class="modal-header">
                                                   <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                                                   <h5 class="modal-title">Confirm new POI location</h5>
                                                   </div>
                                                   <div class="modal-body">
                                                   <table class="table">
                                                      <thead>
                                                         <tr>
                                                            <th>Value names</th>
                                                            <th>Current POI coordinates</th>
                                                            <th>New POI coordinates</th>
                                                         </tr>
                                                      </thead>
                                                      <tbody>
                                                         <tr>
                                                            <th scope="row">Latitude</th>
                                                            <td><p id="currentLatCoord"> </p></td>
                                                            <td><p id="newLatCoord"> </p></td>
                                                         </tr>
                                                         <tr>
                                                            <th scope="row">Longitude</th>
                                                            <td><p id="currentLngCoord"> </p></td>
                                                            <td><p id="newLngCoord"> </p></td>
                                                         </tr>
                                                      </tbody>
                                                   </table>
                                                   </div>
                                                   <div class="modal-footer">
                                                   <button type="button" id="keepPOIPositionBtn" class="btn btn-default"> Keep current location </button>
                                                   <button type="button" id="changePOIPositionBtn" class="btn btn-primary"> Apply new location </button>
                                                   </div>
                                                   </div>
                                                   </div>
                                                   `
                     let changePOIPositionBtn = document.getElementById('changePOIPositionBtn')
                     let keepPOIPositionBtn = document.getElementById('keepPOIPositionBtn')
                     let currentLatCoord = document.getElementById('currentLatCoord')
                     let currentLngCoord = document.getElementById('currentLngCoord')
                     let newLatCoord = document.getElementById('newLatCoord')
                     let newLngCoord = document.getElementById('newLngCoord')

                     currentLatCoord.innerHTML = localPoiArr[point].lat
                     currentLngCoord.innerHTML = localPoiArr[point].lng
                     newLatCoord.innerHTML = e.target._latlng.lat
                     newLngCoord.innerHTML = e.target._latlng.lng

                     changePOIPositionBtn.addEventListener('click', () => {
                        loadingFixed.classList.add('pin-active')
                        setTimeout( () => {
                           if(localPoiArr[point].lat === e.target.options.lat && localPoiArr[point].lng === e.target.options.lng) {
                              localPoiArr[point].lat = e.target._latlng.lat
                              localPoiArr[point].lng = e.target._latlng.lng
                           }
                           generateMapMesh()
                           $('#poiCoordsChangeConfirmationModal').modal('hide')
                           coordsChangePopup.innerHTML = ''

                        }, 100)
                     })



                     keepPOIPositionBtn.addEventListener('click', () => {
                        $('#poiCoordsChangeConfirmationModal').modal('hide')
                        coordsChangePopup.innerHTML = ''
                     })

                     generateMapMesh()
                  })


                  pointOnMap.addEventListener('mouseover', (e) => {
                     poiPopupContent.innerHTML = `
                                                   <div class= "popup-poi-header">
                                                      <h6 id ="popupPoiLabel" class="popup-poi-label">  </h6>
                                                      <span id="popupPoiIcon" class="popup-poi-icon"> </span>
                                                   </div>
                                                   <p id="popupPoiType" class="popup-poi-type"></p>
                                                   <p id="popupPoiAddress" class="popup-poi-address"></p>
                                                `
                     popupPoiLabel = document.getElementById('popupPoiLabel')
                     popupPoiIcon = document.getElementById('popupPoiIcon')
                     popupPoiType = document.getElementById('popupPoiType')
                     popupPoiAddress = document.getElementById('popupPoiAddress')

                     popupPoiLabel.innerHTML = e.target.options.label
                     // popupPoiIcon.innerHTML = e.target.options.icon_code
                     // popupPoiIcon.innerHTML =  "<i class=" + e.target.options.icon_code + "></i> "
                     popupPoiIcon.innerHTML =  ` <i class="fa ${e.target.options.icon_code}"></i> `
                     popupPoiIcon.style.borderColor = e.target.options.color
                     popupPoiIcon.style.color = e.target.options.color
                     popupPoiType.innerHTML = e.target.options.type
                     popupPoiAddress.innerHTML = e.target.options.address

                     poiPopupContent.classList.remove('hidden');
                     poiPopupContent.style.top = (mouseY(event) - 55) +'px';
                     poiPopupContent.style.left = (mouseX(event) - 90) + 'px';

                  })


                  pointOnMap.addEventListener('mouseout', () => {
                     poiPopupContent.classList.add('hidden');
                     poiPopupContent.innerHTML = ``
                  })
               }

               // poi for end

               // map.addLayer(poiClusters);
               // poiClusters.refreshClusters();
            }

            let startLatlng = null
            let stopLatlng = null
            if(mapData.lines.length) {
               for(let line in mapData.lines) {
                  for(let node in mapData.points) {
                     if(mapData.points[node].settings.deleted)  continue
                     if(mapData.lines[line].start_slug === mapData.points[node].settings.slug){
                        startLatlng = new L.LatLng(mapData.points[node].settings.lat, mapData.points[node].settings.lng)
                     }
                     if(mapData.lines[line].end_slug === mapData.points[node].settings.slug){
                        stopLatlng = new L.LatLng(mapData.points[node].settings.lat, mapData.points[node].settings.lng);
                     }
                  }
                  lineColor = mapData.lines[line].color

                  mapLine = L.polyline([startLatlng, stopLatlng],{className: 'polylineStatic'});
                  mapLine.options.line = mapData.lines[line]
                  mapLine.setStyle({color: lineColor})
                  mapLine.addTo(map)
                  mapLine._path.classList.add('l-' + mapData.lines[line].slug)
                  mapLines.push(mapLine)

                  if(mapData.project.cluster_status && selectedMarker) {
                     highlightClusterLines(clusterLines, mapLines)
                     // for(let cluster_line in clusterLines) {
                     //    for(let map_line in mapLines){
                     //       if(mapLines[map_line]._path.classList.contains('l-' + clusterLines[cluster_line].slug)){
                     //          mapLines[map_line]._path.classList.add('clusterActiveLine')
                     //       }
                     //    }
                     // }
                  }

                  if(mapData.lines[line].active) {
                     mapLine._path.classList.add('polylineActive')
                     generatedGraphLine = mapLine
                     // meshDataView.classList.add('mesh-data-active')
                     graphBoxMessage.classList.add('hidden')
                  }

                  mapLine.addEventListener('click', (e) => {
                     graphBoxMessage.classList.add('hidden')
                     if(!mapData.lines[line].active) {
                        injectLoadingElement(graphBox)
                     }

                     updateLineSettingsAjax(mapData.lines[line].slug);

                     for (let al = 0; al < mapData.lines.length; al++) {
                        if(e.target.options.line !== mapData.lines[al]) {
                           mapData.lines[al].active = false
                        } else {
                           mapData.lines[al].active = true
                        }
                     }

                     generateMapMesh()
                     meshDataView.classList.add('mesh-data-active')
                  })

                  mapLine.addEventListener('contextmenu', (e) => {
                     markerMenu.classList.add('hidden');
                     mapData.lines[line].contextmenuactive = true
                     lineMenu.innerHTML = `<ul class="context-menu line-menu map-element-menu">
                                                <li color="#ff913a" class="line-context-menu-element"><p class="color-dot"></p> Apply color </li>
                                                <li color="#ff00ff" class="line-context-menu-element"><p class="color-dot"></p> Apply color </li>
                                                <li color="#94e4c9" class="line-context-menu-element"><p class="color-dot"></p> Apply color </li>
                                                <li color="#abe21b" class="line-context-menu-element"><p class="color-dot"></p> Apply color </li>
                                                <li id="clearLineColor"> <p class="icon-wrapper"> <i class="fa fa-eraser" aria-hidden="true"></i> </p> Clear color</li>
                                                <li id="deleteLineConfirm" data-toggle="modal" data-target="#deleteConfirmationModal"><p class="delete-icon icon-wrapper"><i class="fa fa-trash"></i></p> Delete </li>
                                          </ul>`
                     lineMenu.classList.remove('hidden');
                     lineMenu.style.top = (mouseY(event) - 80) +'px';
                     lineMenu.style.left = (mouseX(event) - 50) + 'px';
                     document.addEventListener('click', lineMenuHandler);

                     let contextMenuElements = document.getElementsByClassName('line-context-menu-element')
                     let clearColorBtn = document.getElementById('clearLineColor')

                     for (let cme = 0; cme < contextMenuElements.length; cme++) {
                        contextMenuElements[cme].addEventListener('click', function(){
                           // ajax line color

                           mapData.lines[line].color = contextMenuElements[cme].getAttribute('color')
                           changeLineColor(mapData.lines[line])
                           loadingFixed.classList.add('pin-active')
                           setTimeout( () => {
                              generateMapMesh()
                           }, 100)
                           lineMenu.classList.add('hidden');
                        })
                     }

                     clearColorBtn.addEventListener('click', () =>{
                        mapData.lines[line].color = '#c3c0c0'
                        // ajax line color delete
                        loadingFixed.classList.add('pin-active')
                        setTimeout( () => {
                           generateMapMesh()
                        }, 100)
                        lineMenu.classList.add('hidden');
                     })

                     let deleteLineConfirm = document.getElementById('deleteLineConfirm')

                     deleteLineConfirm.addEventListener('click', () => {
                        deleteConfirmationPopup.innerHTML = `
                                                            <div class="modal-dialog modal-sm">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                         <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                                                                         <h5 class="modal-title">Confirm Delete</h5>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                       <p> If you are sure you want to delete the line click delete </p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                    <button type="button" class="btn btn-default" data-dismiss="modal"> Cancel </button>
                                                                    <button type="button" class="btn btn-danger danger" id="deleteLineBtn"> Delete line </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            `

                        let deleteButton = document.getElementById('deleteLineBtn')
                        deleteButton.addEventListener('click', () => {
                           loadingFixed.classList.add('pin-active')

                           if(e.target.options.line.active)
                           {
                              globalGeneratedGraphLine = null
                              generatedGraphLine = null
                              $('#chartContainer').hide()
                              $('#emptyGraphBoxMessage').removeClass('hidden')
                           }
                           setTimeout( () => {
                              // ajax delete single line
                              let lineSlug =  mapData.lines[line].slug;
                              mapData.lines.splice(line, 1)

                              // let deleteWindLinesIndex = window.lines.findIndex(windLine => windLine.slug == mapData.lines[line].slug);
                              // if (deleteWindLinesIndex != -1) {
                              //    window.lines.splice(deleteWindLinesIndex, 1);
                              // }

                              console.log(mapData.lines, 'mapData.lines')
                              console.log(window.lines, 'window.lines')
                              deleteLine(lineSlug)
                                 .then(() => {
                                    if(mapData.project.cluster_status && selectedMarker) {
                                       removeClusterHighlight(mapMarkers, mapLines)

                                       getNodeClusterInfo(selectedMarker.options.slug)
                                          .then(() => {
                                             clusterPoints = window.clusterInfo.points
                                             clusterLines = window.clusterInfo.lines

                                             highlightClusterNodes(clusterPoints, null, mapMarkers)
                                             highlightClusterLines(clusterLines, mapLines)

                                             // rebuildMapDataLinesClusterInfo().then(() => {
                                             //    rebuildMapDataPointsClusterInfo()
                                             // })

                                             meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                             meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                             // window.clusterInfo = []
                                          })
                                    }

                                    // deleteLine
                                    generateMapMesh()
                                    $('#deleteConfirmationModal').modal('hide')
                                    deleteConfirmationPopup.innerHTML = ''
                              })
                           }, 100)
                        })
                     })
                  });

                  if(mapData.lines[line].contextmenuactive){
                     mapLine.fireEvent('oncontextmenu')
                  }

                  let lineMenuHandler = function(e) {
                     let isClickedOutside = !lineMenu.contains(e.target);
                     if (isClickedOutside) {
                        lineMenu.classList.add('hidden');
                        document.removeEventListener('click', lineMenuHandler);
                     }
                  }
               }
            }
            // var mapMarkers = mapContainer.getElementsByClassName("mesh-tool-custom-marker");
            // for (var n = 0; n < mapMarkers.length; n++) {
            //    mapMarkers[n].addEventListener("click", function(e){
            //       var current = document.getElementsByClassName("activeMarker");
            //       if (current.length > 0) {
            //          current[0].className = current[0].className.replace(" activeMarker", "");
            //       }
            //       this.className += " activeMarker";
            //    });
            // }

            $(".mesh-tool-custom-marker").click(function(e){
               $(".mesh-tool-custom-marker").removeClass("activeMarker");
               $(this).addClass("activeMarker");
            });

            loadingFixed.classList.remove('pin-active')
         }


         setInterval( () => {
            if(
               (generatedGraphLine && globalGeneratedGraphLine && JSON.stringify(generatedGraphLine._latlngs) !== JSON.stringify(globalGeneratedGraphLine._latlngs))
               ||
               ( generatedGraphLine !== null && globalGeneratedGraphLine === null)
            )
            {
               globalGeneratedGraphLine = generatedGraphLine
               generateElevationGraph(globalGeneratedGraphLine)
               $('#chartContainer').show()
               $('#emptyGraphBoxMessage').addClass('hidden')
            } else {
               generatedGraphLine = null
            }
         }, 1000)

         function clearMapDataLines(startNode) {
            let toDeleteLine = null
            for(let line in mapData.lines) {
               if(
                  (
                     mapData.lines[line].start_slug === startNode.slug
                     ||
                     mapData.lines[line].end_slug === startNode.slug
                  )
               )
               {
                  toDeleteLine = line
                  mapData.lines.splice(toDeleteLine, 1)
                  clearMapDataLines(startNode)
                 //break
               }
            }
         }


         let heightRow = document.getElementById('heightRow')
         let gainRow = document.getElementById('gainRow')
         let cableLossRow = document.getElementById('cableLossRow')
         let setHeatareaValuesPopup = document.getElementById('setHeatareaValuesModal')
         let setNewValBtn = document.getElementById('setNewValBtn')

         let defaultNodeHeight = document.getElementById('defaultNodeHeight')
         let defaultNodeGainEl = document.getElementById('defaultNodeGain')
         let defaultNodeCableLossEl = document.getElementById('defaultNodeCableLoss')

         let newNodeHeightEl = document.getElementById('parentNodePairHeight')
         let newNodeGainEl = document.getElementById('parentNodePairGain')
         let newNodeCableLossEl = document.getElementById('parentNodePairCableLoss')

         function checkNodeInHeatArea(pointKey) {
            new Promise((resolve, reject) => {
               let storedNodeBearing
               let storedNodeLineLength
               let smallest_bearing
               let largest_bearing

               if(globalGeneratedHeatmapNode){
                  storedNodeBearing = getBearing(globalGeneratedHeatmapNode.defaultLat, globalGeneratedHeatmapNode.defaultLng, mapData.points[pointKey].settings.lat, mapData.points[pointKey].settings.lng)
                  storedNodeLineLength = getLineLength(globalGeneratedHeatmapNode.defaultLat, globalGeneratedHeatmapNode.defaultLng, mapData.points[pointKey].settings.lat, mapData.points[pointKey].settings.lng)
                  smallest_bearing = Math.min(...sectorBearingsArr);
                  largest_bearing = Math.max(...sectorBearingsArr);
                  if((storedNodeBearing <= largest_bearing && storedNodeBearing >= smallest_bearing) && storedNodeLineLength <= globalGeneratedHeatmapNode.heatmap_area){
                     let diffFlag = false

                     if( globalGeneratedHeatmapNode.pair_antenna_height !== mapData.points[pointKey].settings.nodeheight){
                        diffFlag = true
                        heightRow.classList.remove('hidden')
                     }
                     if(globalGeneratedHeatmapNode.pair_antenna_gain !== mapData.points[pointKey].settings.gain ){
                        diffFlag = true
                        gainRow.classList.remove('hidden')
                     }
                     if( globalGeneratedHeatmapNode.pair_antenna_cable_loss !== mapData.points[pointKey].settings.cable_loss ){
                        diffFlag  = true
                        cableLossRow.classList.remove('hidden')
                     }
                     defaultNodeHeight.innerHTML = mapData.points[pointKey].settings.nodeheight + ' m'
                     defaultNodeGainEl.innerHTML = mapData.points[pointKey].settings.gain + ' dBi'
                     defaultNodeCableLossEl.innerHTML = mapData.points[pointKey].settings.cable_loss + ' dB'
                     newNodeHeightEl.innerHTML = globalGeneratedHeatmapNode.pair_antenna_height + ' m'
                     newNodeGainEl.innerHTML = globalGeneratedHeatmapNode.pair_antenna_gain + ' dBi'
                     newNodeCableLossEl.innerHTML = globalGeneratedHeatmapNode.pair_antenna_cable_loss + ' dB'


                     if(diffFlag === true) {
                        setTimeout( ( ) => {
                           $('#setHeatareaValuesModal').modal('show')
                        }, 200)
                     }


                     setNewValBtn.addEventListener('click', () => {
                        loadingFixed.classList.add('pin-active')
                        setTimeout( () => {
                           mapData.points[pointKey].settings.nodeheight = globalGeneratedHeatmapNode.pair_antenna_height
                           mapData.points[pointKey].settings.gain = globalGeneratedHeatmapNode.pair_antenna_gain
                           mapData.points[pointKey].settings.cable_loss = globalGeneratedHeatmapNode.pair_antenna_cable_loss
                           generateMapMesh()
                        }, 100)
                     })
                  }
               }
               resolve();
            })
         }

         $('#setHeatareaValuesModal').on('hidden.bs.modal', function () {
            defaultNodeHeight.innerHTML = ''
            defaultNodeGainEl.innerHTML = ''
            defaultNodeCableLossEl.innerHTML = ''
            newNodeHeightEl.innerHTML = ''
            newNodeGainEl.innerHTML = ''
            newNodeCableLossEl.innerHTML = ''
            heightRow.classList.add('hidden')
            gainRow.classList.add('hidden')
            cableLossRow.classList.add('hidden')
         });

         function markerDragEnd(e) {
            let pointIndex = getNextIndex()
            mapData.points.push({
               settings: {
                  slug: null,
                  id: null,
                  cluster_id: null,
                  index: pointIndex,
                  lat: floatingMarker._latlng.lat,
                  lng: floatingMarker._latlng.lng,
                  active: false,
                  deleted: false,
                  hascircle: globalCirclesStatus,
                  radius: 200,
                  nodeheight: 10,
                  heatmap_area: 1000,
                  heatmap_angle: 60,
                  heatmap_bearing: 90,
                  heatmap_state: false,
                  heatmap_bearing_pitch: 0.5,
                  heatmap_line_step: 3,
                  heatmap_start_angle: 0,
                  heatmap_end_angle: 0,
                  transmit_power: 28,
                  gain: 16,
                  cable_loss: 0,
                  other_loss: 6,
                  pair_antenna_height: 10,
                  pair_antenna_gain: 18.4,
                  pair_antenna_cable_loss: 0,
                  node_frequency: 5500,
                  band_width: 80,
               },
               data: {
                  backhaul_status: false
               },
               finance: {}
            })

            generateMapMesh()

            if (mapData.points.length == 1 && !mapData.project.node_connection_type_saved) {
               ajaxAutoMarker()
                 .then(() => {
                    loadingFixedBlade.classList.remove('pin-active')
                 });
            }

            if(mapData.points.length > 1 && !mapData.project.node_connection_type_saved) {
               $('#diffNodeOptionModal').modal({backdrop: 'static', keyboard: false}, 'show')
            }

            if(!mapData.project.node_connection_type_saved) {
               document.getElementById('nctChoice0').checked = true
               mapData.project.node_connection_type === 0
            }

            if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 1) {
               document.getElementById('nctChoice1').checked = true
               loadingFixedBlade.classList.add('pin-active')
               ajaxAutoMarker(1)
                  .then(() => {
                     if(mapData.project.cluster_status && selectedMarker) {
                        setTimeout(() => {
                           getNodeClusterInfo(selectedMarker.options.slug)
                             .then(() => {
                                clusterPoints = window.clusterInfo.points
                                clusterLines = window.clusterInfo.lines

                                highlightClusterNodes(clusterPoints, null, mapMarkers)
                                highlightClusterLines(clusterLines, mapLines)

                                // rebuildMapDataLinesClusterInfo().then(() => {
                                //    rebuildMapDataPointsClusterInfo()
                                // })

                                meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                                meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                                // window.clusterInfo = [];
                           })
                        }, 1000)
                     }

                     loadingFixedBlade.classList.remove('pin-active')
                  })

                  if(sectorBearingsArr.length && globalGeneratedHeatmapNode) {
                     checkNodeInHeatArea(mapData.points.length - 1)
                  }
            }

            if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 2) {
               document.getElementById('nctChoice2').checked = true
               document.getElementById('nodeFilterInput').value = ''
               loadingFixedBlade.classList.add('pin-active')
               ajaxAutoMarker(2)
                 .then(() => {
                    setTimeout( () => {
                       generateAvailableNodes(mapData.points[mapData.points.length - 1].settings.slug)
                    }, 1000)

                    loadingFixedBlade.classList.remove('pin-active')
                })
                 .then(() => {
                  $('#nodeConnectionsModal').modal('show')

                  setTimeout( () => {
                     document.getElementById('nodeFilterInput').focus()
                  }, 600)
               })
            }

            if(mapData.project.node_connection_type_saved && mapData.project.node_connection_type === 3) {
               document.getElementById('nctChoice3').checked = true
               loadingFixedBlade.classList.add('pin-active')
               ajaxAutoMarker(3)
                 .then(() => {
                     generateMapMesh()
                     loadingFixedBlade.classList.remove('pin-active')
               })
            }

            if(sectorBearingsArr.length && globalGeneratedHeatmapNode) {
               checkNodeInHeatArea(mapData.points.length - 1)
            }
         }



         rmbrNctType.addEventListener('change', () => {
            mapData.project.node_connection_type_saved = rmbrNctType.checked
         })


         nfConnectAllNodes.addEventListener('click', () => {
            loadingFixed.classList.add('pin-active')
            if(mapData.project.node_connection_type_saved){
               mapData.project.node_connection_type = 1
               document.getElementById('nctChoice1').checked = true
            }

            // generateNodeLines(mapData.points[mapData.points.length - 1].settings, true)

            if(sectorBearingsArr.length && globalGeneratedHeatmapNode) {
               checkNodeInHeatArea(mapData.points.length - 1)
            }

            $('#diffNodeOptionModal').modal('hide')

            mapData.project.node_connection_type = 1;
            let postObject = {
               points: [],
               lines: [],
               project: mapData.project
            };

            postObject.points.push(mapData.points[mapData.points.length - 1]);
            sendReqAjax(true, postObject)
            .then(() => {
               if(mapData.project.cluster_status && selectedMarker) {
                  getNodeClusterInfo(selectedMarker.options.slug)
                  .then(() => {
                     clusterPoints = window.clusterInfo.points
                     clusterLines = window.clusterInfo.lines

                     highlightClusterNodes(clusterPoints, null, mapMarkers)
                     highlightClusterLines(clusterLines, mapLines)

                     // rebuildMapDataLinesClusterInfo().then(() => {
                     //    rebuildMapDataPointsClusterInfo()
                     // })


                     meshClusterNodes.innerHTML = window.clusterInfo.nodes_count
                     meshClusterCoverage.innerHTML = Math.round((window.clusterInfo.coverage + Number.EPSILON) * 100) / 100  + ' km' + '<sup>2</sup>'
                     // window.clusterInfo = [];
                  })
               }

               loadingFixed.classList.remove('pin-active')
            })
         })

         nfConnectToSelected.addEventListener('click', () => {
            loadingFixed.classList.add('pin-active')
            if(mapData.project.node_connection_type_saved){
               mapData.project.node_connection_type = 2
               document.getElementById('nctChoice2').checked = true
            }
            ajaxAutoMarker(2)
             .then(() => {
                 setTimeout( () => {
                    generateAvailableNodes(mapData.points[mapData.points.length - 1].settings.slug)
                   loadingFixed.classList.remove('pin-active')
                 }, 1000)
             })

            $('#diffNodeOptionModal').modal('hide')
            $('#nodeConnectionsModal').modal('show')
            setTimeout( () => {
               document.getElementById('nodeFilterInput').focus()
            }, 600)
         })

         nfWithoutConnection.addEventListener('click', () => {
            if(mapData.project.node_connection_type_saved){
               mapData.project.node_connection_type = 3
               document.getElementById('nctChoice3').checked = true
            }
            $('#diffNodeOptionModal').modal('hide')

            generateMapMesh()

            mapData.project.node_connection_type = 3

            let postObject = {
               points: [],
               lines: [],
               project: mapData.project
            };

            postObject.points.push(mapData.points[mapData.points.length - 1]);
            sendReqAjax(true, postObject)
              .then(() => {
                 window.canMakeOtherRequest = true;
              })
         })

         document.getElementById('nctChoice0').addEventListener('change', () => {
            rmbrNctType.checked = false
            mapData.project.node_connection_type_saved = false
            mapData.project.node_connection_type = 0
         })
         document.getElementById('nctChoice1').addEventListener('change', () => {
            mapData.project.node_connection_type_saved = true
            mapData.project.node_connection_type = 1
         })
         document.getElementById('nctChoice2').addEventListener('change', () => {
            mapData.project.node_connection_type_saved = true
            mapData.project.node_connection_type = 2
         })
         document.getElementById('nctChoice3').addEventListener('change', () => {
            mapData.project.node_connection_type_saved = true
            mapData.project.node_connection_type = 3
         })

         function ajaxAutoMarker(connectionType = 0) {

            let finalMarker = mapData.points[mapData.points.length - 1];
            mapData.project.node_connection_type = connectionType;
            let postObject = {
               lines : [],
               points: [],
               project: mapData.project
            };

            if (finalMarker.settings.slug == null) {
               postObject.points.push(finalMarker)
            }

            return new Promise((resolve, reject) => {
               sendReqAjax(true, postObject);
               configureMapData();
               resolve(true);
            });

            // return new Promise((resolve, reject) => {
            //    sendReqAjax(true, postObject);
            //    resolve();
            // }).then(() => {
            //    configureMapData();
            // })
         }

         async function poiDragEnd(e) {
            await localPoiArr.push({
               u_id: null,
               active: false,
               deleted: false,
               lat: floatingPoi._latlng.lat,
               lng: floatingPoi._latlng.lng,
               label: 'Label example',
               type: 'Category example',
               address: 'Address example',
               color: '#c3c0c0',
               icon_code: 'fa-graduation-cap',
            })

            generateMapMesh()
         }

         function toggleNodeCircle() {
            loadingFixed.classList.add('pin-active')
            setTimeout(() => {
               // await changeHasCircleFlag()
               for (let node in mapData.points) {
                  mapData.points[node].settings.hascircle = !mapData.points[node].settings.hascircle
               }
               globalCirclesStatus = !globalCirclesStatus
               if(globalCirclesStatus){
                  nodeRadiusStatus.innerHTML = 'On'
               } else {
                  nodeRadiusStatus.innerHTML = 'Off'
               }
               generateMapMesh()
            }, 100)
         }
         //
         // async function changeHasCircleFlag() {
         //    return new Promise((resolve, reject) => {
         //       for (let node in mapData.points) {
         //          mapData.points[node].settings.hascircle = !mapData.points[node].settings.hascircle
         //       }
         //       resolve(true);
         //    })
         // }


         let polylineElevPoints = null;
         async function generateElevationGraph(line) {
            removeLoadingElement(graphBox)
            let totalDistance
            let polyline = line.getLatLngs()
            polyLineDistance = getLineLength(polyline[0].lat, polyline[0].lng, polyline[1].lat, polyline[1].lng)
            let polylineBearing = getBearing(polyline[0].lat, polyline[0].lng, polyline[1].lat, polyline[1].lng)
            polylineElevPoints = getPointsCoordinates(polyline[0].lat, polyline[0].lng, polyline[1].lat, polyline[1].lng,lineStep)

            for ( i = 0; i < polylineElevPoints.length; i++) {
               let elev_results =  await Topography.getTopography(polylineElevPoints[i], topographyOptions)
               polylineElevPoints[i].elevation = elev_results.elevation
            }

            // left 50
            let left50 = getDestinationCoords(polyline[0].lat, polyline[0].lng, polylineBearing, -50)
            let leftLatLng = new L.LatLng(left50[0], left50[1])
            let leftLine = L.polyline([polyline[0], leftLatLng]);
            let leftLineLength = getLineLength(leftLine._latlngs[0].lat, leftLine._latlngs[0].lng, leftLine._latlngs[1].lat, leftLine._latlngs[1].lng)
            let elevPointsLeft = getPointsCoordinates(leftLine._latlngs[0].lat, leftLine._latlngs[0].lng, leftLine._latlngs[1].lat, leftLine._latlngs[1].lng, lineStep)

            for ( i = 0; i < elevPointsLeft.length; i++) {
               let elev_results_left = await Topography.getTopography(elevPointsLeft[i], topographyOptions)
               elevPointsLeft[i].elevation = elev_results_left.elevation
            }

            // right 50
            let right50 = getDestinationCoords(polyline[1].lat, polyline[1].lng, polylineBearing, 50)
            let rightLatLng = new L.LatLng(right50[0], right50[1])
            let rightLine = L.polyline([polyline[1], rightLatLng]);
            let rightLineLength = getLineLength(rightLine._latlngs[0].lat, rightLine._latlngs[0].lng, rightLine._latlngs[1].lat, rightLine._latlngs[1].lng)
            let elevPointsRight = getPointsCoordinates(rightLine._latlngs[0].lat, rightLine._latlngs[0].lng, rightLine._latlngs[1].lat, rightLine._latlngs[1].lng, lineStep)

            for ( i = 0; i < elevPointsRight.length; i++) {
               let elev_results_right = await Topography.getTopography(elevPointsRight[i], topographyOptions)
               elevPointsRight[i].elevation = elev_results_right.elevation
            }

            let dataOBJ = {}
            dataOBJ['_token'] = window.csrf_
            dataOBJ['data'] = JSON.stringify(polylineElevPoints)

            window.polylineElevations = polylineElevPoints;
            window.elevationPointsRight = elevPointsRight;
            window.elevationPointsLeft = elevPointsLeft;


            let pointLeft = mapData.points.filter(p => p.settings.lat === polyline[0].lat && p.settings.lng === polyline[0].lng)[0]
            let leftKeyPoint = mapData.points.lastIndexOf(pointLeft)

            let pointRight = mapData.points.filter(p => p.settings.lat === polyline[1].lat && p.settings.lng === polyline[1].lng)[0]
            let rightKeyPoint = mapData.points.lastIndexOf(pointRight)

            drawElevationProfile(polylineElevPoints, elevPointsRight, elevPointsLeft, null, null, leftKeyPoint, rightKeyPoint)
            removeLoadingElement(graphBox)
            loadingFixed.classList.remove('pin-active')

         }



         function autocomplete(inp, arr, coordsArr) {
            console.log(inp, arr, coordsArr, 'autocomplete')
            /*the autocomplete function takes two arguments,
            the text field element and an array of possible autocompleted values:*/
            var currentFocus;
            /*execute a function when someone writes in the text field:*/
            inp.addEventListener("input", function(e) {
               var a, b, i, val = this.value;
               /*close any already open lists of autocompleted values*/
               closeAllLists();
               if (!val) { return false;}
               currentFocus = -1;
               /*create a DIV element that will contain the items (values):*/
               a = document.createElement("DIV");
               a.setAttribute("id", this.id + "autocomplete-list");
               a.setAttribute("class", "autocomplete-items");
               /*append the DIV element as a child of the autocomplete container:*/
               this.parentNode.appendChild(a);
               /*for each item in the array...*/
               for (i = 0; i < arr.length; i++) {
                  /*create a DIV element for each matching element:*/
                  b = document.createElement("DIV");
                  /*make the matching letters bold:*/
                  b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
                  b.innerHTML += arr[i].substr(val.length);
                  /*insert a input field that will hold the current array item's value:*/
                  b.innerHTML += "<input type='hidden' data-coords='"+ coordsArr[i] +"' value='" + arr[i] + "'>";
                  /*execute a function when someone clicks on the item value (DIV element):*/
                  b.addEventListener("click", function(e) {
                     /*insert the value for the autocomplete text field:*/
                     inp.value = this.getElementsByTagName("input")[0].value;
                     inp.setAttribute('data-coords', this.getElementsByTagName("input")[0].getAttribute('data-coords'))
                     /*close the list of autocompleted values,
                     (or any other open lists of autocompleted values:*/
                     closeAllLists();
                  });
                  a.appendChild(b);
               }
            });
            /*execute a function presses a key on the keyboard:*/
            inp.addEventListener("keydown", function(e) {
               var x = document.getElementById(this.id + "autocomplete-list");
               if (x) x = x.getElementsByTagName("div");
               if (e.keyCode == 40) {
                  /*If the arrow DOWN key is pressed,
                  increase the currentFocus variable:*/
                  currentFocus++;
                  /*and and make the current item more visible:*/
                  addActive(x);
               } else if (e.keyCode == 38) { //up
                  /*If the arrow UP key is pressed,
                  decrease the currentFocus variable:*/
                  currentFocus--;
                  /*and and make the current item more visible:*/
                  addActive(x);
               } else if (e.keyCode == 13) {
                  /*If the ENTER key is pressed, prevent the form from being submitted,*/
                  e.preventDefault();
                  if (currentFocus > -1) {
                     /*and simulate a click on the "active" item:*/
                     if (x) x[currentFocus].click();
                  }
               }
            });

            function addActive(x) {
               /*a function to classify an item as "active":*/
               if (!x) return false;
               /*start by removing the "active" class on all items:*/
               removeActive(x);
               if (currentFocus >= x.length) currentFocus = 0;
               if (currentFocus < 0) currentFocus = (x.length - 1);
               /*add class "autocomplete-active":*/
               x[currentFocus].classList.add("autocomplete-active");
            }

            function removeActive(x) {
               /*a function to remove the "active" class from all autocomplete items:*/
               for (var i = 0; i < x.length; i++) {
                  x[i].classList.remove("autocomplete-active");
               }
            }

            function closeAllLists(elmnt) {
               /*close all autocomplete lists in the document,
               except the one passed as an argument:*/
               var x = document.getElementsByClassName("autocomplete-items");
               for (var i = 0; i < x.length; i++) {
                  if (elmnt != x[i] && elmnt != inp) {
                     x[i].parentNode.removeChild(x[i]);
                  }
               }
            }
            /*execute a function when someone clicks in the document:*/
            document.addEventListener("click", function (e) {
               closeAllLists(e.target);
            });
         }

         let searchInput = document.getElementById('location')
         searchInput.addEventListener('keyup', async (e) => {
            // console.log(e.target.value, 'aaaaaa')
            if (e.target.value.length > 2) {
               console.log(e.target.value)
               loadingFixedBlade.classList.add('pin-active')
               let coords = await getSearchAddressCords(e.target.value)
               // console.log(coords, 'coordscoordscoordscoords')
              // setTimeout(async () => {
                  let places = coords.features.map((place) => place.place_name);
                  let centerCoordsPlaces = coords.features.map((place) => place.center)
                 // await autocomplete(searchInput, places, centerCoordsPlaces)
                  await autocomplete(searchInput, places, centerCoordsPlaces)
               loadingFixedBlade.classList.remove('pin-active')
               // }, 1000)
            }
         })

         let goToLocationBtn = document.getElementById('go-to-location');

         goToLocationBtn.addEventListener('click', (e) => {
            $('#confirm-location').modal('hide')
            if (searchInput.value && searchInput.getAttribute('data-coords')) {
               let goToPosition = searchInput.getAttribute('data-coords');
               let parts = goToPosition.split(',')
               setTimeout(() => {
                  map.panTo(new L.LatLng(parts[1], parts[0]))
               }, 700)
            }
         })

         // let skipLocationBtn = document.getElementById('skip-location')
         //
         // skipLocationBtn.addEventListener('click', (e) => {
         //    setTimeout(() => {
         //       map.panTo(new L.LatLng(mapSettings.settings.center[0], mapSettings.settings.center[1]))
         //    }, 500)
         // })

         let saveButton = document.getElementById('save-button');
         saveButton.addEventListener('click', saveData);


         // set the new center of the map
         const setNewMapCenter = () => {
            if(window.center){
               map.panTo(new L.LatLng(window.center[0], window.center[1]))
            } else {
               map.panTo(new L.LatLng(mapSettings.settings.center[0], mapSettings.settings.center[1]))
            }
         }
         //
         // setTimeout(() => {
         //    setNewMapCenter();
         // }, 1200)

         setInterval(() => {
            if (window.noRefreshBottom || window.reload) {
               setNewMapCenter();
               window.noRefreshBottom = false;
               window.reload = false;
               window.noRefreshGenerateMapMesh = true;
            }
         }, 400)

         setInterval(() => {
            mapData.project.zoom = map.getZoom();
            let mapCenterLatLng = map.getCenter();
            mapData.project.center = [mapCenterLatLng.lat, mapCenterLatLng.lng];
         },500)


         setInterval(() => {
            if(window.noRefreshGenerateMapMesh) {
               window.noRefreshGenerateMapMesh = false
               generateMapMesh()
            }
         }, 700)

         if ((window.points.length && window.lines.length) || (window.points.length && window.lines.length && window.poi.length)  || window.poi.length) {
            generateMapMesh()
         }
