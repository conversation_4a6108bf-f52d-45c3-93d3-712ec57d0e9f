<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/helpers.php';
require_once __DIR__ . '/db.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;
use function GuzzleHttp\json_decode;
use function GuzzleHttp\json_encode;

$connection = new AMQPStreamConnection(returnInfo('rmq_host'), 5672, returnInfo('rmq_username'), returnInfo('rmq_pass'));

$channel = $connection->channel();
$channel->queue_declare(returnInfo('rmq_device_actions_ch'), false, true, false, false);

echo ' [*] Waiting for messages. To exit press CTRL+C', "\n";

$callback = function ($msg) {
    //echo ' [x] Received ', $msg->body, "\n";
    $device_info = json_decode($msg->body);
    wh_device_action_log('Received new action - array: '.json_encode($device_info));
    
    $array_val = json_decode($msg->body);
    
    if(isset($array_val->action) && isset($array_val->devices)){
        
            if(is_array($array_val->devices) && sizeof($array_val->devices) >0){
                foreach ($array_val->devices as $device_ip){
                    
                    $exec_message = 'echo "{\'action\':\''.$array_val->action.'\'}" | nc -w6 '.$device_ip.' 1080';                    
                    wh_device_action_log('Command exec for: '.$exec_message);
                    exec($exec_message,$output);
                     
                    wh_device_action_log('Output command - array: '.json_encode($output));
                }
            }
    }

// 	echo " [x] Done", "\n";
    $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
};
$channel->basic_qos(null, 1, null);
$channel->basic_consume(returnInfo('rmq_device_actions_ch'), '', false, false, false, false, $callback);

while (count($channel->callbacks)) {
    $channel->wait();
}

$channel->close();
$connection->close();